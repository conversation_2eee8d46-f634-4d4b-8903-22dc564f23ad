{
  "files.associations": {
    ".env.example": "dotenv",
    ".env.sample": "dotenv",
    ".env.local.sample": "dotenv",
    ".env.local.example": "dotenv",
    ".env.feature_flags_development": "dotenv"
  },
  "debug.internalConsoleOptions": "neverOpen",
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.codeActionsOnSave": {
      "source.organizeImports": "explicit"
    }
  },
  "[markdown]": {
    // Format on save brakes key formatting in some of our markdown files - so disable here
    "editor.formatOnSave": false,
  },
  "typescript.format.semicolons": "insert",
  "editor.formatOnSave": true,
  "dotnet.automaticallyCreateSolutionInWorkspace": false,
  "remote.portsAttributes": {
    "2208": {
      "label": "sweagentd",
      "protocol": "http",
      "visibility": "public",
      "onAutoForward": "openBrowser"
    }
  },
  "cSpell.words": [
    "actionsv",
    "actx",
    "agentcallback",
    "agentcleanup",
    "APIURL",
    "authnd",
    "Brandyn",
    "brphelps",
    "capi",
    "CAPIHMAC",
    "codeowners",
    "colbylwilliams",
    "commenters",
    "ctxps",
    "ellismg",
    "Entitiesv",
    "errgroup",
    "evals",
    "eventstest",
    "featureflags",
    "freno",
    "ghclient",
    "githubtwirp",
    "githubv",
    "GOGENERATE",
    "gqlclient",
    "hydroevent",
    "hydroevents",
    "innerxml",
    "issuecomment",
    "iurl",
    "jacdavis",
    "jasonetco",
    "jobapi",
    "joberrors",
    "jobexecutor",
    "jobfactory",
    "jobmanager",
    "jobprocessor",
    "jobutils",
    "kvps",
    "Labelable",
    "maintapi",
    "mathrand",
    "Minimizable",
    "MONALISA",
    "newctx",
    "obsv",
    "omitzero",
    "oswe",
    "proberhandler",
    "problemstatement",
    "pullrequestreview",
    "rctx",
    "Reactable",
    "requestctx",
    "reviewcomment",
    "Statter",
    "stderrors",
    "stretchr",
    "sweagentd",
    "sweagentv",
    "tactx",
    "timestamppb",
    "tmpl",
    "unmarshals",
    "Unminimize",
    "Unminimized",
    "vexi",
    "webhookevent",
    "webhookevents",
    "Wrapf",
    "XMLPR"
  ]
}