{
	"folders": [
		{
			"name": "sweagentd",
			"path": "."
		},
		{
			"name": "runtime",
			"path": "runtime"
		}
	],
	"settings": {
		"files.associations": {
			".env.example": "dotenv",
			".env.sample": "dotenv",
			".env.local.sample": "dotenv",
			".env.local.example": "dotenv",
			".env.feature_flags_development": "dotenv"
		},
		"debug.internalConsoleOptions": "neverOpen",
		"[typescript]": {
			"editor.defaultFormatter": "esbenp.prettier-vscode",
			"editor.codeActionsOnSave": {
				"source.organizeImports": "explicit"
			},
		},
		"typescript.format.semicolons": "insert",
		"editor.formatOnSave": true,
		"dotnet.automaticallyCreateSolutionInWorkspace": false
	}
}