# Runtime Development Setup & Self-Hosting

The runtime is a Node.js-based system that provides core agent functionality. This guide will help you get started with development, testing, and using the CLI.

## Getting Started

### Prerequisites

1. Node.js 20.x or later (automatically installed when using DevContainer or CodeSpaces)
2. Access to the necessary AI provider endpoints (see Configuration section below)

This project uses [Vitest](https://vitest.dev/) for testing, so if you are using VS Code we recommend installing the [Vitest extension](https://marketplace.visualstudio.com/items?itemName=vitest.explorer) so you can see the tests in the Test Explorer.

#### macOS Specific Prerequisites

Some of the runtime tests use `bash` and `nano` for testing purposes. The inbox versions of these tools may not work as expected in all cases leading to failures when running the tests. To resolve this, you can install the latest versions using Homebrew:

```bash
brew install bash nano
```

And make sure your `PATH` is set up to use these versions instead of the system defaults. `which bash` and `which nano` should point to the Homebrew versions, typically located at `/opt/homebrew/bin/bash` and `/opt/homebrew/bin/nano`.

### Quick Start with CLI

The runtime includes a command-line interface for common operations. Navigate to the `/runtime` folder and:

1. Install dependencies and build the project:

   ```bash
   script/npm install

   script/npm run build
   ```

2. Use the CLI:

   ```bash
   # General help on available commands
   npx . --help

   # "Fix" path commands and options
   npx . fix --help

   # Load settings from a config file
   # (sample at `/cpd.local.settings.yaml.sample`)
   npx . fix --config <path to file>
   ```

## Configuration

1. Copy `/cpd.local.settings.yaml.sample` to a new file (e.g. `cpd.local.settings.yaml`)
2. Update the configuration values:
   - GitHub settings (token, repo, etc.)
   - AI provider settings
   - Service settings

We support multiple AI providers, with CAPI (GitHub Copilot) being the default and recommended option for development.

## Codebase Structure

Key locations to know:

- `/runtime/src/tools/` - Tool implementations used by the agent
- `/runtime/src/agents/prompts.ts` - Agent prompt definitions
- `/runtime/src/model/capi/` - CAPI (Copilot) model provider implementation
- `/runtime/src/index.ts` - Main entry point used in compute
- `/runtime/src/commands/` - CLI command implementations

## Development Guide

### Extended Prerequisites

Beyond the basic prerequisites mentioned above, for debugging specific agents you'll need:

1. Copy `.env.local.sample` to `repo_root/.env.local` and update the values as commented

   - These environment variables are used by Node.js for debugging and running
   - Specific variables control different AI model providers and features

2. For the Claude agent:

   - An Anthropic API key is required
   - Contact a team developer to obtain one

3. For the SweAgent:
   - Request endpoint access from a team developer
   - Once granted access, authenticate with:
     ```bash
     az login --use-device-code
     ```

### Running Tests

From the command line:

```bash
# Run all tests
npm test

# Run specific test file
npm test <path-to-test-file>
```

From VS Code:

1. Open the `repo_root/sweagentd.code-workspace` workspace in VS Code
1. Install the [Vitest extension](https://marketplace.visualstudio.com/items?itemName=vitest.explorer) if you have not already
1. Use the test explorer to run, debug or view tests

### Debugging

1. Open the `repo_root/sweagentd.code-workspace` workspace in VS Code
1. Update or create a `runtime/.env.local` file with variables for the scenario you want to test.

1. Open `index.ts` in VS Code and add a breakpoint in `main` if you want to start with the entry point.
1. Start debugging:
   - Select one of the `(runtime)` debug configurations.
   - Click the green play button or press F5

### Deploying into your own GitHub instance when using Actions

While using Actions as the runner, the runtime is acquired through an action that is deployed into your codespace's GitHub instance [here](https://http://github.localhost/github/copilot-developer-action). See the [developer setup guide](./service-dev-setup-with-github.md#deploying-the-runtime-when-using-the-actions-launcher) for information on updating it.

## Docker Container

The runtime can also be built and run as a container image.

### Building for use with sweagentd

If you want to build the image and use it with sweagentd, you'll want to make sure the image name you use matches what is in `/.env`. By default you would do this as follows:

```
cd runtime
docker buildx build --platform linux/amd64,linux/arm64 -t ghcr.io/github/sweagentd/runtime:latest .
```

You can also use this to run CLI commands. For example:

```
 docker run -it --rm ghcr.io/github/sweagentd/runtime:latest fix --help
```

You should not, however, push this image to the registry.

### Alternate build

If you want to use the image for another purpose like running it from the command line, you can pick another image name. e.g.

```bash
cd runtime
docker buildx build --platform linux/amd64,linux/arm64 -t sweagentddev.azurecr.io/runtime .
```

You can then push this to an image registry if you want to do so.

```bash
docker push sweagentddev.azurecr.io/runtime
```

### Debugging the runtime running in the container

1. Build the container image using `npm run docker:build-debug`.
1. Modify your .env file to set:
   1. `DOCKER_HOST_NETWORKING=false`
   1. `DOCKER_MAP_PORT=9229`
1. F5/start sweagentd.
1. Open the runtime folder in a separate VS Code instance.
1. Do something that causes the container to start, and verify that in the sweagentd Debug Console or in the container's logs you see "Debugger listening on ws://0.0.0.0:9229/...".
1. OPTIONAL: Using Docker desktop:
   1. Go to the container.
   1. Open the Files tab, and browse to `/usr/src/app/dist/`.
   1. Find `index.js.map`, right click, "Save".
   1. Save/ultimately place the `index.js.map` in the `sweagentd/runtime/dist`.
1. In the separate VS Code instance, F5 using the "Debug (local container)" launch config.
1. Wait for the debugger to attach. The debugger should initially break on the first line of execution of the runtime (likely some library file).
   - If you did the OPTIONAL step above, you should be able to set breakpoints/debug TypeScript files directly.
   - If you did not do the OPTIONAL step above, you will have to debug the `index.js` file that the debugger reads from the container.
1. Debug as desired.

> [!Note]  
> If you kill sweagentd while the container is still waiting on a debugger to attach, the container may not shutdown properly. In this case you will need to stop/delete it manually!

### Docker Troubleshooting

If you get an error like:

```
ERROR: Multi-platform build is not supported for the docker driver.
```

You need to configure docker for multi-platform builds. See the [docker documentation](https://docs.docker.com/go/build-multi-platform) for more info.

For M1 Mac users with Docker Desktop 4.37.2+, enabling `containerd` for pulling and storing images often resolves this issue. See [containerd documentation](https://docs.docker.com/desktop/features/containerd/#enable-the-containerd-image-store)
