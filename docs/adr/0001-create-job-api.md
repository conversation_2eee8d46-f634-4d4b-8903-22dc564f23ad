# Jobs API for Copilot Clients

Status: Partially implemented

## Context

As we're evolving Copilot coding agent (CCA), it is becoming clear that we want to be able to allow clients like VS Code to queue up jobs for processing that originate from things other than issues. Furthermore, individual developers may want to fire off arbitrary tasks that are not directly tied to planned work or may be picked up by the developer to continue on. While aspects of this can already be done with Agent mode in VS Code we want users to be able to queue up work from any number of services and delegate it to Copilot coding agent for processing while they do other processing.

With that in mind, this ADR describes a non-public Copilot API capability off of the existing `/agents/swe` endpoint to queue up jobs for processing tied to arbitrary tasks in addition to existing issues.

> **Note:** This ADR will assume that the pull request still ends up being the primary "entity" that is used to track progress and collaborate with the AI and loop in others into the conversation as appropriate. However, we do recognize that there's likely a need for a variation of a PR here that is more of general "task" tied to a specific user that would not clutter the main PR list. This ADR assumes that we will evolve the existing concept to support these kinds of interactions and improve the overall PR experience here. Even for scenarios where the PR UX may not be primary, the underlying PR data structure would be used.
>
> If for some reason we decide to not use the PR data structure, a separate ADR would be needed to understand how all of the existing capabilities PRs provide that Copilot coding agent uses would be replicated in a new data structure.

## Job API vs Session API

TLDR:

- **Job API** = queue up / stop async agentic work in CCA. For GH/MS use only.
- **[Session API](https://github.com/github/copilot-api/blob/main/docs/api/session.md)** = get a running view of executing agentic work.

The Job API endpoints are and **not** intended to replace or wrap the session API, but rather focus in on allowing clients to queue up asynchronous, cloud hosted, agentic workflows from multiple client surfaces. The session API is intended to be used as a general way to encapsulate information about an agentic workflow - whether cloud or not - in both a detailed and summary form. A session ID is returned by the job API.

While the API is technically publicly accessible given it is intended for client use (e.g. VS Code), it is considered a lower level "internal" API for GitHub / Microsoft use only. Over time we want to support multiple ways for queuing up work. At that point we'll have a different endpoint that proxies to the appropriate API based on the scenario - with the job API providing one of them. Since the session API **is** intended for public use, it is a strong candidate for that proxying endpoint.

### Example scenarios

This is a broad topic, so here are some example scenarios we'd like to enable over time.

#### New PR scenarios

1. Create a PR using a task description from the New PR experience in GitHub (w/o an issue)
1. Create a new PR using a task description from VS Code
   1. As an option in the GitHub PR extension
   1. Via chat surfaces (with the task containing summarized chat context)
   1. Transition from a VS Code Agent Mode session to an new PR (e.g. "Ok, this looks pretty good - can you create a PR and polish it up for me async?")
1. Support "+ New" button on the Sessions page that kicks off a new PR/session using a prompt (and repo/branch + in the future tools/model)
1. Enable dependabot (packages and language version auto-upgrades), and agentic Autofix features without having to couple to the issues or `sweagentd` Hydro handlers ([see here](https://github.com/github/code-scanning/blob/fd65986761b0dba4b4f1d1a6f1a3d361adea8d01/docs/adrs/0087-autofix-padawan-integration.md)).

#### Existing PR scenarios

1. Assign a task to Copilot coding agent tied to code for a locally checked out PR in VS Code - either created by a human, or even originally created by Copilot coding agent. e.g.
   1. Chat surfaces (with the task containing summarized chat context)
   1. Transition back from VS Code Agent Mode session on a locally opened PR (e.g. "Ok, this looks pretty good - can you polish it up for me async?")
1. Supporting a "fix merge conflict" button on an existing PR - whether created by a human and not assigned to Copilot coding agent.
   1. In GitHub
   1. In VS Code - e.g., when you try to pull from main and it fails
1. Support an agentic Copilot Code Review feature in the future (where it doesn't push changes but presents them as suggestions)
1. Kick off multiple tasks / jobs concurrently for the same PR.

#### Other scenarios

1. Display a list of jobs with different groupings and their macro-level state in UX other than GitHub.com (e.g. in VS Code).
   1. General status
   1. Provide quick links to relevant related data (e.g. PR, issue, etc.)
   1. Ability to click a link to get to the detailed session view
1. A CLI tool for spawning Copilot coding agent jobs and tracking them in the CLI (streaming back logs, allowing follow up comments)

## Approach

This new set of endpoints will be available at or under `/jobs` in the `sweagentd` service which currently translate to `https://api.githubcopilot.com/agents/swe/jobs` in prod. Authentication and authorization will be handled by `sweagentd` with help from existing monolith twirp endpoints for validating access. More information will be described in the following sections.

### Endpoints

#### **POST** `/jobs/{owner}/{repo}` - Create Job (Task)

**Status:** v0.0.1 implemented.

This endpoint focuses on queuing up a task for Copilot coding agent to work on. It is tied to a repository, repository owner, and an end user that queued up the work. This encapsulates the logic for creating an assignment, branch and pull request, and queuing up job execution in one API call.

A variation of the API that accepts an existing pull request can be found later in this document.

**Auth Token Scopes**

This endpoint requires a GitHub App / OAuth App user token (or classic PAT for testing) with equivalent of the following scopes to the requested repository:

- `metadata:read`
- `contents:write`
- `workflow:write`
- `pull_request:write`
- `actions:write`

Also be aware that the user tied to the token must have push / contents write access to the repository regardless of the token's scope.

While we recommend the token be repo scoped where possible, **Copilot coding agent will not inherit permissions** from this token so it will not be over privileged regardless.

Specific GitHub App and OAuth App IDs can be allow listed for reduced scope requirements. Trusted clients can then omit the `actions:write` scope (since it is more of an implementation detail in this case), and internal usage on the server side within GitHub can be further reduced when there are already other permissions checks in place (but no clients).

**Request Body**

Note that the repo owner and repo name are required, but come from the URL. Only problem statement is required in this body content.

```jsonc
{
  // [Required] The problem statement that the agent should work on.
  "problem_statement": "Create me a nice README", // required
  // [Optional] Any filtering should be applied to the problem statement: none, markdown, and hidden_characters
  "content_filter_mode": "hidden_characters", // Default
  // [Optional] Settings for the PR that will be created
  "pull_request": {
    "title": "Some PR title",
    "body_placeholder": "Text that should appear in the PR to start",
    "body_suffix": "A suffix that should appear at the bottom of all PR body updates",
    "base_ref": "refs/heads/some-branch", // If omitted, uses the default branch
    "head_ref": "refs/heads/copilot/some-branch", // If omitted, a new branch will be created. Branch must be prefixed with `copilot/`.
    "labels": ["some-label"] // An array of label strings
  },
  "run_name": "A name that should appear on the Actions run that is triggered",
  // [Optional] Triggering event properties
  "event_type": "", // An event type for the session that will be created
  "event_url": "", // An event URL for the session that will be created
  "event_identifiers": [""] // A list of string identifiers for the session that will be created
}
```

**Response**

`201 Created`

**Response Header**

- `X-GitHub-Api-Version: 2025-06-19` // Or similar value based on the version of the API you are using

**Response Body**

For consistency, the proposed output here is verbose like other GitHub REST APIs. However, we likely should pair this down from the raw `gg` objects over time.

```jsonc
{
  "job_id": "some-job-id",
  "session_id": "<ID for automatically created session>",
  "status": "<job status>",
  "actor": {
    // gg.User associated with the token used to create the job
  },
  "created_at": "<timestamp>",
  "updated_at": "<timestamp>",
  "pull_request": {
    // gg.PullRequest for the job
  }
}
```

#### **POST** `/jobs/{owner}/{repo}/pulls/{pr_number}` - Create Job for existing PR

**Status:** Not yet implemented.

This endpoint is similar to the previous one, but focuses on scenarios that should be applied to an existing PR rather then creating a new one.

> **Question:** Do we need to allow users to look up assignments /jobs (and by extension the PR) by identifiers other than the PR number?

**Request Body**

Note that the repo owner, repo name, and PR name are also required, but come from the URL. Only problem statement is required in this body content.

```jsonc
{
  "problem_statement": "Create me a nice README", // required
  // [Optional] Any filtering should be applied to the problem statement: none, markdown, and hidden_characters
  "content_filter_mode": "markdown",
  // [Optional] Whether to include other context from PR in addition to the problem statement - defaults to false
  "exclude_context": true,
  // [PROPOSED, Optional] Indicates whether this job should be run in parallel to any others for the PR rather than serial - defaults to false
  "parallel": true,

  "run_name": "A name that should appear on the Actions run that is triggered",
  "event_type": "", // An event type for the session that will be created
  "event_url": "", // An event URL for the session that will be created
  "event_identifiers": [""] // A list of string identifiers for the session that will be created
}
```

**Response**

`201 Created`

Errors:

- `400 Bad Request` - Incorrect information was passed into the endpoint
- `401 Unauthorized` - User is not authorized to call this endpoint or does not have push rights to the repo
- `402 Payment Required` - User has no remaining quota for premium requests
- `403 Forbidden` - User is authorized to call this endpoint, but the user or repository does not have the Coding Agent enabled.
- `404 Not Found` - Repository or other resource not found. Also used when an app or user is on the "disallow" list to obfuscate.
- `409 Conflict` - A PR already exists for the specified base and head refs
- `422 Unprocessable Entity` - A branch protection rule or other rule is preventing branch setup or PR creation
- `500 Internal Server Error` - Something went wrong on the server side

**Response Body**

Same as POST `/jobs/{owner}/{repo}`

#### **GET** `/jobs/{owner}/{repo}/{job_id}`, `/jobs/{owner}/{repo}/session/{session_id}` - Get Job Status

**Status:** v0.0.1

> **NOTE:** For user facing information, you should typically use the **[session API](https://github.com/github/copilot-api/blob/main/docs/api/session.md)** to get details about an executing job rather than this endpoint.

This endpoint retrieves the internal details of a job with a particular eye to getting visibility to its status and last update date. As a result, the initial version of this API takes returns only key information rather than the full details of the associate pull request or workflow run. We can add a version of this API that returns these details at a later point should the need arrise, but we would likely want to hydrate it through the monolith.

**Response**

`200 OK`

Errors:

- `400 Bad Request` - Incorrect information was passed into the endpoint
- `401 Unauthorized` - User is not authorized to call this endpoint or does not have push rights to the repo
- `403 Forbidden` - User is authorized to call this endpoint, but the user or repository does not have the Coding Agent enabled.
- `404 Not Found` - Repository or job not found. Also used when an app or user is on the "disallow" list to obfuscate.
- `500 Internal Server Error` - Something went wrong on the server side

**Response Body**

```jsonc
{
  "job_id": "some-job-id",
  "session_id": "<ID for automatically created session>",
  "problem_statement": "<problem statement passed in when creating the job (not the PR text)>",
  "content_filter_mode": "<content filter mode if set>",
  "status": "<job status>",
  "result": "<job result>",
  "actor": {
    "id": "<User ID>",
    "login": "<User login>"
  },
  "created_at": "<timestamp>",
  "updated_at": "<timestamp>",
  "pull_request": {
    "id": "<PR ID>",
    "number": "<PR number>"
  },
  //If a workflow run has been started
  "workflow_run": {
    "id": "<Workflow Run ID>"
  },
  // If the job has errors
  "error": {
    "message": "<Error message>"
  },
  "event_type": "", // If specified
  "event_url": "", // If specified
  "event_identifiers": [""] // If specified
}
```

#### **POST** `/jobs/{owner}/{repo}/{job_id}/cancel`, `/jobs/{owner}/{repo}/session/{session_id}/cancel` - Cancel Job

**Status:** v0.0.1 implemented.

These endpoints allow a user to cancel a job that they have queued up. Either a Job ID or a Session ID can be used to cancel the job. If both are provided, the Job ID will be used.

**Auth Token Scopes**

Same as **POST** `/jobs/{owner}/{repo}`

**Response**

`204 No Content`

Errors:

- `400 Bad Request` - Incorrect information was passed into the endpoint
- `401 Unauthorized` - User is not authorized to call this endpoint or does not have push rights to the repo
- `403 Forbidden` - User is authorized to call this endpoint, but the user or repository does not have the Coding Agent enabled.
- `404 Not Found` - Repository or job not found. Also used when an app or user is on the "disallow" list to obfuscate.
- `500 Internal Server Error` - Something went wrong on the server side

#### **PUT** `/jobs/{owner}/{repo}/{job_id}` - Update Job

**Status:** Not yet implemented.

> **NOTE:** This endpoint should **NOT** be implemented until we have a way to block the agent from calling it directly. The `/agent/job` endpoint requires a nonce that cannot be required here. So we either need a specific scope, or to block tokens generated Copilot coding agent from calling this endpoint - at least unless the token is encrypted, has a nonce, or a shared secret that can verify it is from an expected client.

This endpoint updates the job with information related to its current state and progress.

**Request Body**

```jsonc
// TODO
```

**Response**

`202 Accepted`

Same as **GET** `/jobs/{owner}/{repo}/{job_id}`
