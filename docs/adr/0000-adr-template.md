# [Short title describing the issue]

Status: [ accepted | rejected | deprecated | superseded by ADR-???? ]

Deciders: [list everyone involved in the decision]

Related issue: [link to the GitHub issue corresponding to the epic/feature that prompted the change, if relevant]

## Context

What is the issue that we're seeing that is motivating this decision or change?

### Current status

OPTIONAL: What is the current state of things and what shortcomings have we observed?

### Requirements

OPTIONAL: Are there any limitations and/or requirements we need to take into account?

## Alternatives considered

- Solution 1
- Solution 2

## Decision

What is the change that we're proposing and/or doing?
Why was it chosen out of all the options that were considered?

## Implications

What becomes easier or more difficult to do because of this change?
Are there any risks or unknowns?

### Additional details

OPTIONAL: This section can be used to provide implementation details, examples, future plans, etc.
