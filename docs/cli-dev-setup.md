# CLI Instructions

A command-line interface for the GitHub Copilot Developer Action.

## Prerequisites

- **Node.js**: Version 22 or higher (tested with v22.16.0)
- **npm**: Version 10 or higher (tested with v10.9.2)

- On windows, WSL is required. Native windows is still TBD.
   -  Recommend Ubuntu 24.04.1 LTS (GNU/Linux **********-microsoft-standard-WSL2 x86_64)

## Installation

Before building or running the CLI, install dependencies

From the sweagentd/runtime folder, run:

```bash
npm install
```

## Building the CLI

To build the CLI, run the following command from the sweagentd/runtime folder:

```bash
npm run build
npm run build:mcp-client
npm run build:cli
```

This will:
- Compile the TypeScript source code for the mcp-client and cli using Rollup

## Linting

To run linting on the entire runtime folder (including CLI):

```bash
npm run lint
```

## Running the CLI

### From the built version

After building, you can run the CLI using:

```bash
npm run start:cli
```

Or directly:

```bash
node --no-deprecation dist-cli/index.js
```

### Hot reloading

You can make the app auto-build and reload by running using this command to build the app:

```bash
npm run build:cli:watch
```

It will keep building the app when it detects changes in the code.

And then run the app in another terminal with this command:

```bash
npm run start:cli:watch
```

This will reload the app automatically as it gets rebuilt by the previous command.

### Command-line options

The CLI supports a prompt mode using the `-p` flag. This will not show UI or do REPL:

```bash
copilot -p "your prompt here"
```

## Published Global Installation

See internally published package: https://github.com/github/sweagentd/pkgs/npm/copilot

To install the CLI globally so you can use the `copilot` command anywhere:

1. First, [authenticate to GitHub Packages npm registry](https://docs.github.com/en/packages/working-with-a-github-packages-registry/working-with-the-npm-registry#authenticating-to-github-packages)
   - Create [a classic PAT](https://github.com/settings/tokens) with `packages:read` scope and SSO enabled for the `github` organization. 
   - Then run the following supplying your GitHub username as the username and the PAT as the password:
   ```bash
   npm login --scope=@github --auth-type=legacy --registry=https://npm.pkg.github.com
   ```

1. Install globally with npm:
   ```bash
   npm install -g @github/copilot
   ```

After installation, you can use the `copilot` command from anywhere:

```bash
copilot
or
copilot -p "your prompt here"
```

## Local Global Installation

To install a local build of the CLI globally so you can use the `copilot-dev` command anywhere:

1. First, build the CLI and mcp-client from the sweagentd/runtime folder:
   ```bash
   npm run build:mcp-client
   npm run build:cli
   ```

3. Install globally using npm from the sweagentd/runtime directory:
   ```bash
   npm install -g .
   ```

After installation, you can use the `copilot-dev` command from anywhere:

```bash
copilot-dev
or
copilot-dev -p "your prompt"
```

## Development

The CLI is built with:
- **TypeScript** for type safety
- **React** and **Ink** for the terminal UI

### Running CLI Tests

To run just the CLI tests:

```bash
npm run test:cli
```

For running the full runtime test suite, see [runtime-dev-setup.md](runtime-dev-setup.md).

## Configuration

### HMAC Key and Integration ID

Use the `/config` slash command to input your Copilot Key (use the HMAC key) and the GitHub Copilot Integration ID on first run. You can find the HMAC key value in 1Password (called `CAPI / Copilot Developer (Dev) API secret`). This requires [entitlements](https://github.com/github/copilot-api/blob/main/docs/dev/dev-onboarding.md#entitlements).

### Available Control Commands

Type `?` to display available control commands used to configure and control Copilot:

```
Available commands:
/clear - Clear chat history and start a fresh session
/cwd [directory] - Change working directory or show current directory
/config - Open interactive configuration settings
/mcp [show|add|edit|delete] [server-name] - Manage MCP server configuration
/history [show|clear] - View or clear conversation history
```

## Dependencies

The CLI requires Node.js and includes the following key dependencies:
- `ink` - React for interactive command-line apps
- `ink-text-input` - Text input component for Ink
- `react` - React library
