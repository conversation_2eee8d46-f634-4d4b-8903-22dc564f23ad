# sweagentd First Responder

[First responder training recording - 2025-05-16](https://microsoft-my.sharepoint.com/:v:/p/chrimartin/EXUcfHEzRwdFnSmce0T_tTwBtS_gCvhWJSHN_HdYtKggfQ?e=hwjJss&nav=eyJyZWZlcnJhbEluZm8iOnsicmVmZXJyYWxBcHAiOiJTdHJlYW1XZWJBcHAiLCJyZWZlcnJhbFZpZXciOiJTaGFyZURpYWxvZy1MaW5rIiwicmVmZXJyYWxBcHBQbGF0Zm9ybSI6IldlYiIsInJlZmVycmFsTW9kZSI6InZpZXcifX0%3D) 

## 1. Rotation

A shift is Wednesday morning through Wednesday morning, including off hours pages, organized through the [coding-agent-ep escalation policy](https://github.pagerduty.com/escalation_policies/P29A2BN).

## 2. Responsibilities

🚧: We are still building out our First Responder process, so expect this to become more concrete as our process does as well.

While you are the team's First Responder, you will take care of the following items in descending priority order:

1. **Respond to pages and active incidents:**
   - Your first mission is to make sure sweagentd is healthy, and when we have incidents staying engaged (even when it's a dependency issue) is priority number 1. Details on how to do this can be found in [our playbook](https://github.com/github/ops/blob/master/docs/playbooks/copilot/coding-agent.md).
1. **Monitor service health:**
   Check these out every so often throughout the day to make sure things look good.
   - [sweagentd's dashboard](https://app.datadoghq.com/dashboard/4wj-b2t-r9p/sweagentd) looks healthy
   - (We need to clean up Sentry a bit before we can expect FR to actively monitor this:) [sweagentd's sentry](https://github.sentry.io/issues/?project=4508892525559808&query=is%3Aunresolved%20issue.priority%3A%5Bhigh%2C%20medium%5D&referrer=issue-list&statsPeriod=24h) does not show any new or escalating issues
1. **Engage users seeing operational issues with the product:**
   Make sure to take some time of the day to check in on common areas folks may need help.
   - #coding-agent
   - #padawan-dogfood
   - [Microsoft's Teams dogfood channel](https://teams.microsoft.com/l/channel/19%3Ae54177e89c2a463db213a00c2bf33e95%40thread.tacv2/Padawan%20internal%20dogfood%20feedback?groupId=4c7d9ea8-db48-4929-889b-ba32c90d5274&tenantId=72f988bf-86f1-41af-91ab-2d7cd011db47)
1. **Improve our processes!**
   We have a lot of edge rounding to do. As you run into sharp corners, write an issue up, tag it as `first-responder`, and mention it in your handoff. This should include:
   - Gaps in monitors. Are we having "shadow outages" that our current monitoring isn't capturing?
   - Gaps in playbooks. If you are investigating something in the livesite and you're finding yourself having to write new queries for scratch for common questions, that means we need to add more information to our playbooks! Digging into our data might be happening on a page in the middle of the night, having known good queries ready to go will help our FRs.
   - Any bugs in how we are capturing or visualizing operational information. Does something look off in the dashboard? Is a log message missing or recording the wrong information? This is a great opportunity to address it, but at the very least create a `first-responder` issue to track it. 

At the beginning of your shift, make a point to:
  - Update the #padawan-dogfood channel to include the current first responder and drop the old one.
  - Let the next primary and secondary know that their shift starts in a week.

At the end of your shift, make a point to:
  - Add an entry to the [handoff discussion](https://github.com/github/sweagentd/discussions/2450) detailing your week.

## 3. Fallback

If you're unable to follow up on all these items at any point in your rotation, please work with the team to hand items off directly. It is not your responsibility to complete all the tasks, such as provide continuous reviews on all PRs, but to triage and delegate to experts where needed.

## 4. Useful Places and Things

Slack channels:

* [#coding-agent](https://github-grid.enterprise.slack.com/archives/C08BT2TUJNA)
* [#padawan-dogfood](https://github-grid.enterprise.slack.com/archives/C08DGM5ULJ3)
* [#coding-agent-team](https://github-grid.enterprise.slack.com/archives/C0891ESUXGW)
