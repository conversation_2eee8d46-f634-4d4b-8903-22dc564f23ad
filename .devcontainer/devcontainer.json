{
  "name": "sweagentd",
  "build": {
    "dockerfile": "Dockerfile",
    "context": ".."
  },
  "onCreateCommand": ".devcontainer/on-create-command.sh",
  "postCreateCommand": ".devcontainer/post-create-command.sh",
  "postStartCommand": ".devcontainer/post-start-command.sh",
  "hostRequirements": {
    "storage": "64gb",
    "memory": "64gb"
  },
  "remoteUser": "vscode",
  "containerEnv": {
    "GODEBUG": "multipathtcp=0"
  },
  "features": {
    "ghcr.io/devcontainers/features/docker-in-docker:2": {
      "azureDnsAutoDetection": false,
      "dockerDefaultAddressPool": "base=***********/16,size=24"
    },
    "ghcr.io/devcontainers/features/dotnet:2": {
      "version": "latest"
    },
    "ghcr.io/devcontainers/features/go:1": {
      "version": "1.24"
    },
    "ghcr.io/github/features/goproxy:1": {},
    "ghcr.io/github/features/go-linter:2": {},
    "ghcr.io/devcontainers/features/github-cli:1": {
      "version": "latest"
    },
    "ghcr.io/devcontainers/features/sshd:1": {
      "version": "latest"
    },
    "ghcr.io/devcontainers/features/azure-cli:1": {
      "installBicep": true
    },
    "ghcr.io/devcontainers/features/powershell:1": {}
  },
  "forwardPorts": [
    2208
  ],
  "portsAttributes": {
    "2208": {
      "label": "sweagentd",
      "onAutoForward": "silent"
    }
  },
  "customizations": {
    "codespaces": {
      "repositories": {
        "github/*": {
          "permissions": {
            "contents": "read",
            "packages": "read"
          }
        },
        "github/features": {
          "permissions": {
            "contents": "read",
            "packages": "read"
          }
        },
        "github/gh-base-image": {
          "permissions": {
            "contents": "read",
            "packages": "read"
          }
        },
        // Prebuilds need to explicitly request access and then authorize instead of using the /* syntax.
        // Ref: https://github.com/github/codespaces/issues/11743
        "github/go-linter": {
          "permissions": {
            "contents": "read",
            "packages": "read"
          }
        }
      }
    },
    "vscode": {
      "extensions": [
        "GitHub.copilot",
        "ms-azuretools.vscode-docker",
        "golang.go",
        "ms-vscode.vscode-node-azure-pack",
        "ms-azuretools.vscode-azure-github-copilot",
        "GraphQL.vscode-graphql",
        "redhat.vscode-yaml",
        "bierner.github-markdown-preview",
        "ms-azuretools.vscode-bicep",
        "mikestead.dotenv",
        "dbaeumer.vscode-eslint",
        "vitest.explorer"
      ],
      "settings": {
        "files.watcherExclude": {
          "**/vendor": true,
          "**/.git": true,
        },
        "editor.formatOnSave": true,
        "go.useLanguageServer": true,
        "[go]": {
          "editor.codeActionsOnSave": {
            "source.organizeImports": true
          }
        },
        "[go.mod]": {
          "editor.codeActionsOnSave": {
            "source.organizeImports": true
          }
        },
        "gopls": {
          "usePlaceholders": false,
          "staticcheck": true
        },
        "go.lintTool": "golangci-lint",
        "go.lintOnSave": "package",
        "go.toolsEnvVars": {},
        "go.testEnvVars": {},
        "go.testFlags": [
          "-v"
        ]
      }
    }
  }
}