# If you prefer the allow list template instead of the deny list, see community template:
# https://github.com/github/gitignore/blob/main/community/Golang/Go.AllowList.gitignore
#
# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Standalone binaries
http

script/actions-runner

# Test binary, built with `go test -c`
*.test
node_modules/

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
vendor/

# Go workspace file
go.work
go.work.sum

# env file
.env

bin/

.DS_Store
__debug_bin*
*.http
*.rest

*.pem

cpd.local.settings.yaml
cpd.local.settings.env

# Test generated files
test.txt
**/test.tmp

mcp-client/node_modules
mcp-client/dist
runtime/mcp-config.json