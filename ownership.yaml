---
version: 1
ownership:
  - name: copilot-coding-agent
    long_name: Copilot coding agent
    tier: 2
    description: Delegate tasks to <PERSON>pi<PERSON> by assigning it a GitHub Issue or asking it to open a PR from Copilot Chat
    kind: logical
    team_slack: coding-agent-team
    maintainer: jasonetco
    exec_sponsor: aprilla
    product_manager: timrogers
    team: github/copilot-extensibility-reviewers
    repo: https://github.com/github/sweagentd
    qos: experimental
    sev1:
      pagerduty: https://github.pagerduty.com/escalation_policies#P29A2BN
      issue: https://github.com/github/sweagentd/issues/
      tta: 10 minutes
    sev2:
      issue: https://github.com/github/sweagentd/issues/
      tta: 1 business day
    sev3:
      slack: coding-agent
      alert_slack: sweagentd-alerts
      tta: 2 business days
    links:
      - name: Incident playbook
        description: The playbook for the Coding agent product
        kind: playbook
        url: https://ops.githubapp.com/docs/playbooks/copilot/coding-agent.md
      - name: Team Datadog dashboard
        description: Datadog dashboard for health and performance monitoring
        kind: dashboard
        url: https://app.datadoghq.com/dashboard/4wj-b2t-r9p/sweagentd
      - name: Sentry exceptions
        description: Sentry exceptions for error monitoring
        kind: dashboard
        url: https://github.sentry.io/issues/?project=4508892525559808
      - name: Splunk logs
        description: Splunk logs for troubleshooting
        kind: dashboard
        url: https://splunk.githubapp.com/en-US/app/gh_reference_app/search?q=search%20index%3Dsweagentd
  - name: sweagentd
    component_of: copilot-coding-agent
    long_name: sweagentd Moda service
    description: Orchestration service for Copilot coding agent
    kind: moda
    repo: https://github.com/github/sweagentd
    tier: 2
    qos: experimental
    dependencies:
      - hydro
      - authnd
      - github
      - actions
      - freno-gh
      - copilot-api
  - name: coding-agent-runtime
    long_name: Coding agent runtime
    description: Runtime for the Copilot coding agent
    kind: artifact
    team_slack: coding-agent-team
    exec_sponsor: lukehoban
    product_manager: timrogers
    team: github/copilot-extensibility-reviewers
    tier: 2
    repo: https://github.com/github/sweagentd
    qos: experimental
    dependencies:
      - github
      - actions
      - copilot-api
      - sweagentd
      - codeload
    sev1:
      pagerduty: https://github.pagerduty.com/escalation_policies#PE75RHO
      issue: https://github.com/github/sweagentd/issues/
      tta: 10 minutes
    sev2:
      issue: https://github.com/github/sweagentd/issues/
      tta: 1 business day
    sev3:
      slack: coding-agent
      alert_slack: sweagentd-alerts
      tta: 2 business days
    links:
      - name: Incident playbook
        description: The playbook for the Coding agent product
        kind: playbook
        url: https://ops.githubapp.com/docs/playbooks/copilot/coding-agent.md
      - name: Team Datadog dashboard
        description: Datadog dashboard for health and performance monitoring
        kind: dashboard
        url: https://app.datadoghq.com/dashboard/4wj-b2t-r9p/sweagentd
      - name: Sentry exceptions
        description: Sentry exceptions for error monitoring
        kind: dashboard
        url: https://github.sentry.io/issues/?project=4508892525559808
      - name: Splunk logs
        description: Splunk logs for troubleshooting
        kind: dashboard
        url: https://splunk.githubapp.com/en-US/app/gh_reference_app/search?q=search%20index%3Dsweagentd
