# This needs to be a URL that GitHub can use to reach your instance -  e.g., github.com, localhost if the
# service is next to an GitHub instance, or use Codespaces or VS Code port forwarding and set it to public
AGENT_CALLBACK_URL="http://localhost:2208/agent"

# Set to true to disable Moda's hydro support - needed when debugging locally or using ACI w/o github/github
DISABLE_HYDRO_CONSUMER=true

# Change if you want to use another deployed instance of GitHub
# COPILOT_API_URL="https://api.githubcopilot.com"
# GITHUB_API_BASE_URL=https://api.github.com
# GITHUB_SERVER_URL=http://github.com/
# URL overrides - set if you want ignore the values already in the environment
# GITHUB_API_BASE_URL_OVERRIDE=http://api.github.localhost/
# GITHUB_SERVER_URL_OVERRIDE=http://github.localhost/

# Allows you to override the default model for testing purposes - e.g., sweagent, or sweagent-aip
# MODEL_OVERRIDE="sweagent"

# For sweagent-capi when developing in the Docker or Node launcher. For Actions, the default settings come from Github itself. For
# Actions dev, set Actions repo or org secrets for COPILOT_INTEGRATION_ID_OVERRIDE and CAPI_HMAC_KEY_OVERRIDE to override these defaults.
COPILOT_INTEGRATION_ID="copilot-developer-dev"
CAPI_HMAC_KEY=""

# Only needed for the Anthropic native sweagent.
# ANTHROPIC_API_KEY="your-key-here"

# Which launcher to use: docker, actions. Use "docker" for most development, "actions" is used in prod and is an option for development.
# AGENT_LAUNCHER="docker"
# Default setting for whether debug logging is enabled - Can be user set using the same name in an Actions variable.
# COPILOT_AGENT_DEBUG=true

# Enable and configure the firewall - the domain list is comma separated - user configurable via with non-_DEFAULT names in Actions variables.
# COPILOT_AGENT_FIREWALL_ENABLED_DEFAULT=false
# COPILOT_AGENT_FIREWALL_ALLOW_LIST_DEFAULT="api.github.com"

# Settings related to the docker launcher - DOCKER_AIP_PROXY is only required for sweagent-aip
DOCKER_AGENT_CONTAINER_IMAGE="ghcr.io/github/sweagentd/runtime:latest"
DOCKER_AIP_PROXY=false
# This determines if the container will share the host's network namespace directly, meaning
# it will use the host's network stack, interfaces, and IP addresses. Defaults to true. You may
# want to set it to false if you want to debug the runtime layer running in the container.
DOCKER_HOST_NETWORKING=true
# This enables mapping a port from the container to the host machine. Typically only used
# when you want to debug the runtime layer running in the container.
# DOCKER_MAP_PORT=9229

# Default "runs-on" labels and group for selecting an Actions runner. Can be changed by the
# user via the COPILOT_AGENT_RUNS_ON_LABELS Actions variable.
# ACTIONS_DEFAULT_RUNS_ON_LABELS="ubuntu-latest"

# 32-byte hex-encoded encryption key to encrypt/decrypt short-lived tokens temporarily stored in the database
# ENCRYPTION_KEY="your-key-here"

# Config for using your own GitHub App and bot login for testing
GITHUB_APP_PRIVATE_KEY="base64encodedPrivateKey"
GITHUB_APP_ID="123456"
GITHUB_APP_LOGIN="copilot-developer"
GITHUB_APP_CLIENT_ID="Iv1.some-number"
GITHUB_APP_CLIENT_SECRET="client-secret"
GITHUB_BOT_LOGIN="colbypilot"

# User and email for commits - the email below makes it look like the commit was done by Copilot
GITHUB_COMMIT_LOGIN="colbypilot"
GITHUB_COMMIT_EMAIL="<EMAIL>"

# MCP settings
# COPILOT_MCP_ENABLED="true" # use this to enable MCP tools
