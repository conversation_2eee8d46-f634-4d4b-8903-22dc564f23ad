syntax = "proto3";
package sweagentd.sweagentd_api.v1;

option go_package = "sweagentd/v1";
option ruby_package = "MonolithTwirp::Sweagentd::SweagentdAPI::V1";

// SweagentdAPI is used for all sweagentd -> dotcom communication.
service SweagentdAPI {
    //  ConfigurationForRepository returns the configuration that sweagentd cares about for a given repository.
    rpc ConfigurationForRepository(ConfigurationForRepositoryRequest) returns (ConfigurationForRepositoryResponse);

    //  MCPConfigurationForRepository returns the MCP settings that sweagentd cares about for a given repository.
    rpc MCPConfigurationForRepository(MCPConfigurationForRepositoryRequest) returns (MCPConfigurationForRepositoryResponse);

    // CreatePullRequest creates a pull request in the given repository.
    rpc CreatePullRequest(CreatePullRequestRequest) returns (CreatePullRequestResponse);

    // MintUserToServerTokenForRepo mints a user to server token for the given repository.
    rpc MintUserToServerTokenForRepo(MintUserToServerTokenForRepoRequest) returns (MintUserToServerTokenForRepoResponse);
}

message ConfigurationForRepositoryRequest {
    // The github repository id for the repository that we want to get configuration for.
    int64 repository_id = 1;
    // The user id for the user that we want to get configuration for.
    uint64 user_id = 2;
}

message ConfigurationForRepositoryResponse {
    // An all up "true or false" about whether sweagentd is enabled for this repository.
    // This will gate whether or not we do any additional checks or work for the repository.
    // Right now, this correlates to any padawan interactions being viable for the repository.
    bool is_sweagentd_enabled = 1;
}

message MCPConfigurationForRepositoryRequest {
    // The github repository id for the repository that we want to get configuration for.
    uint64 repository_id = 1;

    // The user id for the user that we want to get configuration for.
    uint64 user_id = 2;
}

message MCPConfigurationForRepositoryResponse {
    // mcp_configuration_json is the effective JSON configuration for the MCP settings for the repository.
    // It's OK for us to use a string here -- we will adhere to UTF8, and we will make sure that the file is not too large on the handler side.
    string mcp_configuration_json = 1;

    // is_mcp_enabled is a boolean that indicates whether or not the MCP settings are enabled for the repository and user.
    // it's expected that policies set on the monolith side can be used to populate this field.
    bool is_mcp_enabled = 2;
}

message CreatePullRequestRequest {
    uint64 repository_id = 1;
    string base_branch_name = 2;
    string head_branch_name = 3;
    string pr_name = 4;
    string pr_description = 5;
    bool draft = 6;
    uint64 attribution_user_id = 7;
}

message CreatePullRequestResponse {
    PullRequest pull_request = 1;
}

message PullRequest {
    uint64 id = 1;
    uint64 github_number = 2;
    uint64 repository_id = 3;
}

message MintUserToServerTokenForRepoRequest {
  // The user id for the user that we want to get a token for.
  uint64 user_id = 1;
  // The github repository id for the repository to scope the token to.
  uint64 repository_id = 2;
}

message MintUserToServerTokenForRepoResponse {
  string token = 1;
}