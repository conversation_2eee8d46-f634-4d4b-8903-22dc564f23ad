// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v5.27.2
// source: sweagentd/v1/sweagentd_api.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ConfigurationForRepositoryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The github repository id for the repository that we want to get configuration for.
	RepositoryId int64 `protobuf:"varint,1,opt,name=repository_id,json=repositoryId,proto3" json:"repository_id,omitempty"`
	// The user id for the user that we want to get configuration for.
	UserId uint64 `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *ConfigurationForRepositoryRequest) Reset() {
	*x = ConfigurationForRepositoryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sweagentd_v1_sweagentd_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfigurationForRepositoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigurationForRepositoryRequest) ProtoMessage() {}

func (x *ConfigurationForRepositoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_sweagentd_v1_sweagentd_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigurationForRepositoryRequest.ProtoReflect.Descriptor instead.
func (*ConfigurationForRepositoryRequest) Descriptor() ([]byte, []int) {
	return file_sweagentd_v1_sweagentd_api_proto_rawDescGZIP(), []int{0}
}

func (x *ConfigurationForRepositoryRequest) GetRepositoryId() int64 {
	if x != nil {
		return x.RepositoryId
	}
	return 0
}

func (x *ConfigurationForRepositoryRequest) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type ConfigurationForRepositoryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// An all up "true or false" about whether sweagentd is enabled for this repository.
	// This will gate whether or not we do any additional checks or work for the repository.
	// Right now, this correlates to any padawan interactions being viable for the repository.
	IsSweagentdEnabled bool `protobuf:"varint,1,opt,name=is_sweagentd_enabled,json=isSweagentdEnabled,proto3" json:"is_sweagentd_enabled,omitempty"`
}

func (x *ConfigurationForRepositoryResponse) Reset() {
	*x = ConfigurationForRepositoryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sweagentd_v1_sweagentd_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfigurationForRepositoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigurationForRepositoryResponse) ProtoMessage() {}

func (x *ConfigurationForRepositoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_sweagentd_v1_sweagentd_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigurationForRepositoryResponse.ProtoReflect.Descriptor instead.
func (*ConfigurationForRepositoryResponse) Descriptor() ([]byte, []int) {
	return file_sweagentd_v1_sweagentd_api_proto_rawDescGZIP(), []int{1}
}

func (x *ConfigurationForRepositoryResponse) GetIsSweagentdEnabled() bool {
	if x != nil {
		return x.IsSweagentdEnabled
	}
	return false
}

type MCPConfigurationForRepositoryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The github repository id for the repository that we want to get configuration for.
	RepositoryId uint64 `protobuf:"varint,1,opt,name=repository_id,json=repositoryId,proto3" json:"repository_id,omitempty"`
	// The user id for the user that we want to get configuration for.
	UserId uint64 `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *MCPConfigurationForRepositoryRequest) Reset() {
	*x = MCPConfigurationForRepositoryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sweagentd_v1_sweagentd_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MCPConfigurationForRepositoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MCPConfigurationForRepositoryRequest) ProtoMessage() {}

func (x *MCPConfigurationForRepositoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_sweagentd_v1_sweagentd_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MCPConfigurationForRepositoryRequest.ProtoReflect.Descriptor instead.
func (*MCPConfigurationForRepositoryRequest) Descriptor() ([]byte, []int) {
	return file_sweagentd_v1_sweagentd_api_proto_rawDescGZIP(), []int{2}
}

func (x *MCPConfigurationForRepositoryRequest) GetRepositoryId() uint64 {
	if x != nil {
		return x.RepositoryId
	}
	return 0
}

func (x *MCPConfigurationForRepositoryRequest) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type MCPConfigurationForRepositoryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// mcp_configuration_json is the effective JSON configuration for the MCP settings for the repository.
	// It's OK for us to use a string here -- we will adhere to UTF8, and we will make sure that the file is not too large on the handler side.
	McpConfigurationJson string `protobuf:"bytes,1,opt,name=mcp_configuration_json,json=mcpConfigurationJson,proto3" json:"mcp_configuration_json,omitempty"`
	// is_mcp_enabled is a boolean that indicates whether or not the MCP settings are enabled for the repository and user.
	// it's expected that policies set on the monolith side can be used to populate this field.
	IsMcpEnabled bool `protobuf:"varint,2,opt,name=is_mcp_enabled,json=isMcpEnabled,proto3" json:"is_mcp_enabled,omitempty"`
}

func (x *MCPConfigurationForRepositoryResponse) Reset() {
	*x = MCPConfigurationForRepositoryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sweagentd_v1_sweagentd_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MCPConfigurationForRepositoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MCPConfigurationForRepositoryResponse) ProtoMessage() {}

func (x *MCPConfigurationForRepositoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_sweagentd_v1_sweagentd_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MCPConfigurationForRepositoryResponse.ProtoReflect.Descriptor instead.
func (*MCPConfigurationForRepositoryResponse) Descriptor() ([]byte, []int) {
	return file_sweagentd_v1_sweagentd_api_proto_rawDescGZIP(), []int{3}
}

func (x *MCPConfigurationForRepositoryResponse) GetMcpConfigurationJson() string {
	if x != nil {
		return x.McpConfigurationJson
	}
	return ""
}

func (x *MCPConfigurationForRepositoryResponse) GetIsMcpEnabled() bool {
	if x != nil {
		return x.IsMcpEnabled
	}
	return false
}

type CreatePullRequestRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RepositoryId      uint64 `protobuf:"varint,1,opt,name=repository_id,json=repositoryId,proto3" json:"repository_id,omitempty"`
	BaseBranchName    string `protobuf:"bytes,2,opt,name=base_branch_name,json=baseBranchName,proto3" json:"base_branch_name,omitempty"`
	HeadBranchName    string `protobuf:"bytes,3,opt,name=head_branch_name,json=headBranchName,proto3" json:"head_branch_name,omitempty"`
	PrName            string `protobuf:"bytes,4,opt,name=pr_name,json=prName,proto3" json:"pr_name,omitempty"`
	PrDescription     string `protobuf:"bytes,5,opt,name=pr_description,json=prDescription,proto3" json:"pr_description,omitempty"`
	Draft             bool   `protobuf:"varint,6,opt,name=draft,proto3" json:"draft,omitempty"`
	AttributionUserId uint64 `protobuf:"varint,7,opt,name=attribution_user_id,json=attributionUserId,proto3" json:"attribution_user_id,omitempty"`
}

func (x *CreatePullRequestRequest) Reset() {
	*x = CreatePullRequestRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sweagentd_v1_sweagentd_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePullRequestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePullRequestRequest) ProtoMessage() {}

func (x *CreatePullRequestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_sweagentd_v1_sweagentd_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePullRequestRequest.ProtoReflect.Descriptor instead.
func (*CreatePullRequestRequest) Descriptor() ([]byte, []int) {
	return file_sweagentd_v1_sweagentd_api_proto_rawDescGZIP(), []int{4}
}

func (x *CreatePullRequestRequest) GetRepositoryId() uint64 {
	if x != nil {
		return x.RepositoryId
	}
	return 0
}

func (x *CreatePullRequestRequest) GetBaseBranchName() string {
	if x != nil {
		return x.BaseBranchName
	}
	return ""
}

func (x *CreatePullRequestRequest) GetHeadBranchName() string {
	if x != nil {
		return x.HeadBranchName
	}
	return ""
}

func (x *CreatePullRequestRequest) GetPrName() string {
	if x != nil {
		return x.PrName
	}
	return ""
}

func (x *CreatePullRequestRequest) GetPrDescription() string {
	if x != nil {
		return x.PrDescription
	}
	return ""
}

func (x *CreatePullRequestRequest) GetDraft() bool {
	if x != nil {
		return x.Draft
	}
	return false
}

func (x *CreatePullRequestRequest) GetAttributionUserId() uint64 {
	if x != nil {
		return x.AttributionUserId
	}
	return 0
}

type CreatePullRequestResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PullRequest *PullRequest `protobuf:"bytes,1,opt,name=pull_request,json=pullRequest,proto3" json:"pull_request,omitempty"`
}

func (x *CreatePullRequestResponse) Reset() {
	*x = CreatePullRequestResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sweagentd_v1_sweagentd_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePullRequestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePullRequestResponse) ProtoMessage() {}

func (x *CreatePullRequestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_sweagentd_v1_sweagentd_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePullRequestResponse.ProtoReflect.Descriptor instead.
func (*CreatePullRequestResponse) Descriptor() ([]byte, []int) {
	return file_sweagentd_v1_sweagentd_api_proto_rawDescGZIP(), []int{5}
}

func (x *CreatePullRequestResponse) GetPullRequest() *PullRequest {
	if x != nil {
		return x.PullRequest
	}
	return nil
}

type PullRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	GithubNumber uint64 `protobuf:"varint,2,opt,name=github_number,json=githubNumber,proto3" json:"github_number,omitempty"`
	RepositoryId uint64 `protobuf:"varint,3,opt,name=repository_id,json=repositoryId,proto3" json:"repository_id,omitempty"`
}

func (x *PullRequest) Reset() {
	*x = PullRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sweagentd_v1_sweagentd_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PullRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PullRequest) ProtoMessage() {}

func (x *PullRequest) ProtoReflect() protoreflect.Message {
	mi := &file_sweagentd_v1_sweagentd_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PullRequest.ProtoReflect.Descriptor instead.
func (*PullRequest) Descriptor() ([]byte, []int) {
	return file_sweagentd_v1_sweagentd_api_proto_rawDescGZIP(), []int{6}
}

func (x *PullRequest) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PullRequest) GetGithubNumber() uint64 {
	if x != nil {
		return x.GithubNumber
	}
	return 0
}

func (x *PullRequest) GetRepositoryId() uint64 {
	if x != nil {
		return x.RepositoryId
	}
	return 0
}

type MintUserToServerTokenForRepoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The user id for the user that we want to get a token for.
	UserId uint64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// The github repository id for the repository to scope the token to.
	RepositoryId uint64 `protobuf:"varint,2,opt,name=repository_id,json=repositoryId,proto3" json:"repository_id,omitempty"`
}

func (x *MintUserToServerTokenForRepoRequest) Reset() {
	*x = MintUserToServerTokenForRepoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sweagentd_v1_sweagentd_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MintUserToServerTokenForRepoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MintUserToServerTokenForRepoRequest) ProtoMessage() {}

func (x *MintUserToServerTokenForRepoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_sweagentd_v1_sweagentd_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MintUserToServerTokenForRepoRequest.ProtoReflect.Descriptor instead.
func (*MintUserToServerTokenForRepoRequest) Descriptor() ([]byte, []int) {
	return file_sweagentd_v1_sweagentd_api_proto_rawDescGZIP(), []int{7}
}

func (x *MintUserToServerTokenForRepoRequest) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *MintUserToServerTokenForRepoRequest) GetRepositoryId() uint64 {
	if x != nil {
		return x.RepositoryId
	}
	return 0
}

type MintUserToServerTokenForRepoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *MintUserToServerTokenForRepoResponse) Reset() {
	*x = MintUserToServerTokenForRepoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sweagentd_v1_sweagentd_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MintUserToServerTokenForRepoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MintUserToServerTokenForRepoResponse) ProtoMessage() {}

func (x *MintUserToServerTokenForRepoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_sweagentd_v1_sweagentd_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MintUserToServerTokenForRepoResponse.ProtoReflect.Descriptor instead.
func (*MintUserToServerTokenForRepoResponse) Descriptor() ([]byte, []int) {
	return file_sweagentd_v1_sweagentd_api_proto_rawDescGZIP(), []int{8}
}

func (x *MintUserToServerTokenForRepoResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

var File_sweagentd_v1_sweagentd_api_proto protoreflect.FileDescriptor

var file_sweagentd_v1_sweagentd_api_proto_rawDesc = []byte{
	0x0a, 0x20, 0x73, 0x77, 0x65, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x64, 0x2f, 0x76, 0x31, 0x2f, 0x73,
	0x77, 0x65, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x64, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x1a, 0x73, 0x77, 0x65, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x64, 0x2e, 0x73, 0x77,
	0x65, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x64, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x22, 0x61,
	0x0a, 0x21, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46,
	0x6f, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x72, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x22, 0x56, 0x0a, 0x22, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x69, 0x73, 0x5f, 0x73, 0x77,
	0x65, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x64, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x69, 0x73, 0x53, 0x77, 0x65, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x64, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x64, 0x0a, 0x24, 0x4d, 0x43, 0x50,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72,
	0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x72, 0x65, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22,
	0x83, 0x01, 0x0a, 0x25, 0x4d, 0x43, 0x50, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x6d, 0x63, 0x70,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6a,
	0x73, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x6d, 0x63, 0x70, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4a, 0x73, 0x6f, 0x6e, 0x12,
	0x24, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x6d, 0x63, 0x70, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x4d, 0x63, 0x70, 0x45, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x99, 0x02, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x50, 0x75, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x72, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x62, 0x61, 0x73, 0x65, 0x5f,
	0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x62, 0x61, 0x73, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x28, 0x0a, 0x10, 0x68, 0x65, 0x61, 0x64, 0x5f, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x68, 0x65, 0x61,
	0x64, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x70,
	0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x72, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x72,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x64,
	0x72, 0x61, 0x66, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x64, 0x72, 0x61, 0x66,
	0x74, 0x12, 0x2e, 0x0a, 0x13, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x11,
	0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x22, 0x67, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x75, 0x6c, 0x6c, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4a,
	0x0a, 0x0c, 0x70, 0x75, 0x6c, 0x6c, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x73, 0x77, 0x65, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x64,
	0x2e, 0x73, 0x77, 0x65, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x64, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x75, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0b, 0x70,
	0x75, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x67, 0x0a, 0x0b, 0x50, 0x75,
	0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x23,
	0x0a, 0x0d, 0x72, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x72, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72,
	0x79, 0x49, 0x64, 0x22, 0x63, 0x0a, 0x23, 0x4d, 0x69, 0x6e, 0x74, 0x55, 0x73, 0x65, 0x72, 0x54,
	0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x46, 0x6f, 0x72, 0x52,
	0x65, 0x70, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x72, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x22, 0x3c, 0x0a, 0x24, 0x4d, 0x69, 0x6e, 0x74,
	0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x32, 0xfa, 0x04, 0x0a, 0x0c, 0x53, 0x77, 0x65, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x64, 0x41, 0x50, 0x49, 0x12, 0x9b, 0x01, 0x0a, 0x1a, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x3d, 0x2e, 0x73, 0x77, 0x65, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x64, 0x2e, 0x73, 0x77, 0x65, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x64, 0x5f, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x73, 0x77, 0x65, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x64, 0x2e, 0x73, 0x77, 0x65, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x64, 0x5f, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x46, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa4, 0x01, 0x0a, 0x1d, 0x4d, 0x43, 0x50, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x40, 0x2e, 0x73, 0x77, 0x65, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x64, 0x2e, 0x73, 0x77, 0x65, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x64, 0x5f, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x43, 0x50, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f,
	0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x41, 0x2e, 0x73, 0x77, 0x65, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x64, 0x2e, 0x73, 0x77, 0x65, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x64, 0x5f,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x43, 0x50, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x80, 0x01, 0x0a,
	0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x75, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x34, 0x2e, 0x73, 0x77, 0x65, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x64, 0x2e, 0x73,
	0x77, 0x65, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x64, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x75, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x73, 0x77, 0x65, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x64, 0x2e, 0x73, 0x77, 0x65, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x64, 0x5f, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x75, 0x6c, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0xa1, 0x01, 0x0a, 0x1c, 0x4d, 0x69, 0x6e, 0x74, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6f,
	0x12, 0x3f, 0x2e, 0x73, 0x77, 0x65, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x64, 0x2e, 0x73, 0x77, 0x65,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x64, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x69,
	0x6e, 0x74, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x40, 0x2e, 0x73, 0x77, 0x65, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x64, 0x2e, 0x73, 0x77,
	0x65, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x64, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x4d,
	0x69, 0x6e, 0x74, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x42, 0x3b, 0x5a, 0x0c, 0x73, 0x77, 0x65, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x64,
	0x2f, 0x76, 0x31, 0xea, 0x02, 0x2a, 0x4d, 0x6f, 0x6e, 0x6f, 0x6c, 0x69, 0x74, 0x68, 0x54, 0x77,
	0x69, 0x72, 0x70, 0x3a, 0x3a, 0x53, 0x77, 0x65, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x64, 0x3a, 0x3a,
	0x53, 0x77, 0x65, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x64, 0x41, 0x50, 0x49, 0x3a, 0x3a, 0x56, 0x31,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_sweagentd_v1_sweagentd_api_proto_rawDescOnce sync.Once
	file_sweagentd_v1_sweagentd_api_proto_rawDescData = file_sweagentd_v1_sweagentd_api_proto_rawDesc
)

func file_sweagentd_v1_sweagentd_api_proto_rawDescGZIP() []byte {
	file_sweagentd_v1_sweagentd_api_proto_rawDescOnce.Do(func() {
		file_sweagentd_v1_sweagentd_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_sweagentd_v1_sweagentd_api_proto_rawDescData)
	})
	return file_sweagentd_v1_sweagentd_api_proto_rawDescData
}

var file_sweagentd_v1_sweagentd_api_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_sweagentd_v1_sweagentd_api_proto_goTypes = []interface{}{
	(*ConfigurationForRepositoryRequest)(nil),     // 0: sweagentd.sweagentd_api.v1.ConfigurationForRepositoryRequest
	(*ConfigurationForRepositoryResponse)(nil),    // 1: sweagentd.sweagentd_api.v1.ConfigurationForRepositoryResponse
	(*MCPConfigurationForRepositoryRequest)(nil),  // 2: sweagentd.sweagentd_api.v1.MCPConfigurationForRepositoryRequest
	(*MCPConfigurationForRepositoryResponse)(nil), // 3: sweagentd.sweagentd_api.v1.MCPConfigurationForRepositoryResponse
	(*CreatePullRequestRequest)(nil),              // 4: sweagentd.sweagentd_api.v1.CreatePullRequestRequest
	(*CreatePullRequestResponse)(nil),             // 5: sweagentd.sweagentd_api.v1.CreatePullRequestResponse
	(*PullRequest)(nil),                           // 6: sweagentd.sweagentd_api.v1.PullRequest
	(*MintUserToServerTokenForRepoRequest)(nil),   // 7: sweagentd.sweagentd_api.v1.MintUserToServerTokenForRepoRequest
	(*MintUserToServerTokenForRepoResponse)(nil),  // 8: sweagentd.sweagentd_api.v1.MintUserToServerTokenForRepoResponse
}
var file_sweagentd_v1_sweagentd_api_proto_depIdxs = []int32{
	6, // 0: sweagentd.sweagentd_api.v1.CreatePullRequestResponse.pull_request:type_name -> sweagentd.sweagentd_api.v1.PullRequest
	0, // 1: sweagentd.sweagentd_api.v1.SweagentdAPI.ConfigurationForRepository:input_type -> sweagentd.sweagentd_api.v1.ConfigurationForRepositoryRequest
	2, // 2: sweagentd.sweagentd_api.v1.SweagentdAPI.MCPConfigurationForRepository:input_type -> sweagentd.sweagentd_api.v1.MCPConfigurationForRepositoryRequest
	4, // 3: sweagentd.sweagentd_api.v1.SweagentdAPI.CreatePullRequest:input_type -> sweagentd.sweagentd_api.v1.CreatePullRequestRequest
	7, // 4: sweagentd.sweagentd_api.v1.SweagentdAPI.MintUserToServerTokenForRepo:input_type -> sweagentd.sweagentd_api.v1.MintUserToServerTokenForRepoRequest
	1, // 5: sweagentd.sweagentd_api.v1.SweagentdAPI.ConfigurationForRepository:output_type -> sweagentd.sweagentd_api.v1.ConfigurationForRepositoryResponse
	3, // 6: sweagentd.sweagentd_api.v1.SweagentdAPI.MCPConfigurationForRepository:output_type -> sweagentd.sweagentd_api.v1.MCPConfigurationForRepositoryResponse
	5, // 7: sweagentd.sweagentd_api.v1.SweagentdAPI.CreatePullRequest:output_type -> sweagentd.sweagentd_api.v1.CreatePullRequestResponse
	8, // 8: sweagentd.sweagentd_api.v1.SweagentdAPI.MintUserToServerTokenForRepo:output_type -> sweagentd.sweagentd_api.v1.MintUserToServerTokenForRepoResponse
	5, // [5:9] is the sub-list for method output_type
	1, // [1:5] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_sweagentd_v1_sweagentd_api_proto_init() }
func file_sweagentd_v1_sweagentd_api_proto_init() {
	if File_sweagentd_v1_sweagentd_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_sweagentd_v1_sweagentd_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfigurationForRepositoryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sweagentd_v1_sweagentd_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfigurationForRepositoryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sweagentd_v1_sweagentd_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MCPConfigurationForRepositoryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sweagentd_v1_sweagentd_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MCPConfigurationForRepositoryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sweagentd_v1_sweagentd_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePullRequestRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sweagentd_v1_sweagentd_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePullRequestResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sweagentd_v1_sweagentd_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PullRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sweagentd_v1_sweagentd_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MintUserToServerTokenForRepoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sweagentd_v1_sweagentd_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MintUserToServerTokenForRepoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_sweagentd_v1_sweagentd_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_sweagentd_v1_sweagentd_api_proto_goTypes,
		DependencyIndexes: file_sweagentd_v1_sweagentd_api_proto_depIdxs,
		MessageInfos:      file_sweagentd_v1_sweagentd_api_proto_msgTypes,
	}.Build()
	File_sweagentd_v1_sweagentd_api_proto = out.File
	file_sweagentd_v1_sweagentd_api_proto_rawDesc = nil
	file_sweagentd_v1_sweagentd_api_proto_goTypes = nil
	file_sweagentd_v1_sweagentd_api_proto_depIdxs = nil
}
