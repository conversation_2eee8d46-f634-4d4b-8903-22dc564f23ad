version: 2
registries:
  ghcr: # Define access for a private registry
    type: docker-registry
    url: ghcr.io
    username: PAT
    password: ${{secrets.CONTAINER_BUILDER_TOKEN}}
updates:
  - package-ecosystem: docker
    registries:
      - ghcr
    directory: "/"
    schedule:
      interval: weekly
    groups:
      baseImages:
        patterns:
          - "*"  
  - package-ecosystem: github-actions
    directory: "/"
    schedule:
      interval: weekly
    ignore:
      - dependency-name: "github/internal-actions"
    groups:
      actions:
        patterns:
          - "*"
  - package-ecosystem: npm
    directory: "/runtime"
    schedule:
      interval: weekly
    groups:
      npm:
        patterns:
          - "*"
  - package-ecosystem: gomod
    directory: "/"
    schedule:
      interval: weekly
    groups:
      godeps:
        patterns:
          - "*"
