## Overview
### What
_Describe the high level changes in this PR. What is the expected outcome?_

### Why
_Link to an issue or describe why this PR is necessary. What is the purpose?_

### Checklist
- [ ] The Overview section above is filled with a description of the PR
- [ ] PR link shared to [#coding-agent-team](https://github-grid.enterprise.slack.com/archives/C0891ESUXGW) Slack channel for review
- [ ] (Recommended) Take over pager when deploying outside of North America business hours `.pager me copilot-extensibility-primary <minutes>`
- [ ] (Recommended) Use feature flags to roll out changes progressively and reduce time to mitigation
- [ ] (Recommended) Deploy PR to staging for confidence following the [instruction](../blob/main/README.md#staging-environment) before deploying to production
- [ ] (Recommended) Familiarize yourself with the potential [rollback procedure](https://github.com/github/ops/blob/master/docs/playbooks/copilot/coding-agent.md#rolling-back-a-deploy)
