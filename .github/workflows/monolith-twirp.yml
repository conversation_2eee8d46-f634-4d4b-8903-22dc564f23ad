on:
  push: {}

permissions:
  id-token: write

jobs:
  monolith-twirp:
    # Note, if you see this start failing consider moving to a pinned build.
    uses: github/internal-actions/.github/workflows/monolith_twirp.yml@main
    with:
      ci-formatted-job-name: monolith-twirp
    # These are GitHub org Actions secrets which are automatically available
    secrets:
      dx-bot-token: ${{ secrets.INTERNAL_ACTIONS_DX_BOT_ACCOUNT_TOKEN }}
      datadog-api-key: ${{ secrets.DATADOG_API_KEY }}
