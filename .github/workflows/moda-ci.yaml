name: sweagentd Moda CI

# More info on CI actions setup can be found here:
# https://github.com/github/ops/blob/master/docs/playbooks/build-systems/moving-moda-apps-from-bp-to-actions.md

on:
  workflow_dispatch:
  push:
    branches-ignore:
      - "gh-readonly-queue/**"
  merge_group:
    types: [checks_requested]

jobs:
  moda-config-bundle:
    name: ${{ matrix.ci_job.job }}
    strategy:
      fail-fast: false
      matrix:
        ci_job: [{ "job": "sweagentd-moda-config-bundle" }]
    uses: github/internal-actions/.github/workflows/moda.yml@main
    with:
      ci-formatted-job-name: ${{ matrix.ci_job.job }}
      vault-keys: ${{ vars.VAULT_KEYS }}
    secrets:
      dx-bot-token: ${{ secrets.INTERNAL_ACTIONS_DX_BOT_ACCOUNT_TOKEN }}
      datadog-api-key: ${{ secrets.DATADOG_API_KEY }}

  docker-image:
    name: ${{ matrix.ci_job.job }}
    strategy:
      fail-fast: false
      matrix:
        ci_job: [{ "job": "sweagentd-docker-image" }]
    uses: github/internal-actions/.github/workflows/kube.yml@main
    with:
      attest: true
      ci-formatted-job-name: ${{ matrix.ci_job.job }}
      vault-keys: ${{ vars.VAULT_KEYS }}
      docker-build-env-secrets: "EBPF_DOWNLOAD_GITHUB_TOKEN"
    secrets:
      dx-bot-token: ${{ secrets.INTERNAL_ACTIONS_DX_BOT_ACCOUNT_TOKEN }}
      datadog-api-key: ${{ secrets.DATADOG_API_KEY }}

  docker-security:
    name: ${{ matrix.ci_job.job }}
    strategy:
      fail-fast: false
      matrix:
        ci_job: [{ "job": "sweagentd-docker-security" }]
    uses: github/internal-actions/.github/workflows/docker_security.yml@main
    with:
      ci-formatted-job-name: ${{ matrix.ci_job.job }}
      vault-keys: ${{ vars.VAULT_KEYS }}
      docker-build-env-secrets: "EBPF_DOWNLOAD_GITHUB_TOKEN"
    secrets:
      dx-bot-token: ${{ secrets.INTERNAL_ACTIONS_DX_BOT_ACCOUNT_TOKEN }}
      datadog-api-key: ${{ secrets.DATADOG_API_KEY }}

permissions:
  actions: read
  checks: read
  contents: read
  statuses: read
  id-token: write
  attestations: write
