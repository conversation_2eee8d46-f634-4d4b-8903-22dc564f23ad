name: proto-gen-go-check
on:
  pull_request: {}
  push:
    branches:
      - main
permissions:
  contents: read
jobs:
  pr:
    name: Validate that generated Go protobuf clients are up to date
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v1
      - uses: actions/setup-go@v2
        with:
          go-version: "1.23"
      - run: go version
      - name: Check for stale proto outputs
        run: |
          go generate ./proto && git diff --quiet proto || { echo "FAIL! Stale go protobuf clients detected (run: go generate ./proto)"; exit 1; }
