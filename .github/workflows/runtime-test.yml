name: "Runtime build and test"

on:
  push:
  workflow_dispatch:
  merge_group:

permissions:
  id-token: write # for setup-goproxy
  contents: read

jobs:
  changes:
    runs-on: ubuntu-latest
    outputs:
      runtime: ${{ steps.filter.outputs.runtime }}
    steps:
      - uses: actions/checkout@v4
      - uses: dorny/paths-filter@de90cc6fb38fc0963ad72b210f1f284cd68cea36
        id: filter
        with:
          filters: |
            runtime:
              - 'runtime/**'
  runtime:
    needs: changes
    runs-on: ubuntu-latest
    permissions:
      contents: read # push gets disabled if not set
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: "npm"
          cache-dependency-path: runtime/package-lock.json

      - name: Build runtime (via npm)
        working-directory: ./runtime
        run: npm ci && npm run build

      - name: Lint runtime (via npm)
        working-directory: ./runtime
        run: npm run lint

      - name: Check formatting (via npm)
        working-directory: ./runtime
        run: npm run format:check

      - name: Execute runtime tests
        working-directory: ./runtime
        run: |
          # Exit early unless runtime code has changed
          if [ "${{ needs.changes.outputs.runtime }}" == "false" ]; then
            echo "No changes in runtime code, skipping tests."
            exit 0
          fi
          git config --global user.name "$COPILOT_AGENT_COMMIT_LOGIN"
          git config --global user.email "$COPILOT_AGENT_COMMIT_EMAIL"
          npm run test
        env:
          TEST_CI: true
          COPILOT_AGENT_RUNNER_TYPE: STANDALONE
          GIT_HANDLER_TEST_REPO: ghcpd/runtime-git-test-repo
          GIT_HANDLER_TEST_GITHUB_TOKEN: ${{ secrets.COPILOT_DEVLOPER_AGENT_USER_PAT }}
          COPILOT_AGENT_COMMIT_LOGIN: "CloudColonel"
          COPILOT_AGENT_COMMIT_EMAIL: "<EMAIL>"
          CAPI_HMAC_KEY: ${{ secrets.CAPI_HMAC_KEY }}
          GITHUB_COPILOT_INTEGRATION_ID: ${{ vars.CAPI_INTEGRATION_ID }}
          DISABLE_CAPI_TESTS: ${{ vars.DISABLE_CAPI_TESTS }}
