name: Nightly Evals

on:
  workflow_dispatch:
    inputs:
      eval_model:
        description: "Model to evaluate"
        required: false
        type: string
        default: ""
      test_pattern:
        description: "Test pattern to run (e.g. ./test/evals/specific*.test.ts)"
        required: false
        type: string
        default: ./test/evals/*.test.ts
  schedule:
    # Every 8 hours for default model
    - cron: "0 4,12,20 * * *"
    # Once nightly for claude-3.7-sonnet at 01:00 UTC
    - cron: "0 1 * * *"
    # Once nightly for oswe at 02:00 UTC
    - cron: "0 2 * * *"

jobs:
  evals:
    env:
      EVAL_MODEL_NAME: ${{ github.event.inputs.eval_model != '' && github.event.inputs.eval_model || (github.event.schedule == '0 1 * * *' && 'claude-3.7-sonnet' || github.event.schedule == '0 2 * * *' && 'oswe' || '') }}
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        run_number: [1, 2, 3]
    permissions:
      issues: read # required for MCP test which fetches issues
      contents: read # push gets disabled if not set
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: "npm"
          cache-dependency-path: runtime/package-lock.json
      - uses: astral-sh/setup-uv@6b9c6063abd6010835644d4c2e1bef4cf5cd0fca #v6.0.1

      - name: Build runtime (via npm)
        working-directory: ./runtime
        run: npm install && npm run build

      - name: Execute runtime tests
        working-directory: ./runtime
        run: |
          # Set test pattern to default if not provided
          git config --global user.name "$COPILOT_AGENT_COMMIT_LOGIN"
          git config --global user.email "$COPILOT_AGENT_COMMIT_EMAIL"
          set -o pipefail # Ensure the pipeline fails if any command fails
          # Run tests; filter results to only the test output matching the test pattern
          npm run test ${{ github.event.inputs.test_pattern || './test/evals/*.test.ts' }} | grep -E "($(echo '${{ github.event.inputs.test_pattern || './test/evals/*.test.ts' }}' | sed 's|\./||g' | sed 's|\*|.*|g' | sed 's|\.ts||g'))"
        env:
          NIGHTLY_TEST_CI: true
          TEST_CI: true
          EVAL_MODEL: ${{ env.EVAL_MODEL_NAME }}
          EVAL_TEST_GITHUB_TOKEN: "${{ secrets.PROBE_TEST_GITHUB_TOKEN }}"
          EVAL_TEST_GITHUB_TOKEN_GITHUB: "${{ secrets.EVAL_TEST_GITHUB_TOKEN_GITHUB }}"
          EVAL_TEST_VISION: "${{ secrets.EVAL_TEST_VISION }}"
          EVAL_LOG_ROOT: ${{runner.temp}}/sweagent-evals-run${{ matrix.run_number }}
          COPILOT_AGENT_RUNNER_TYPE: STANDALONE
          COPILOT_AGENT_COMMIT_LOGIN: "CloudColonel"
          COPILOT_AGENT_COMMIT_EMAIL: "<EMAIL>"
          CAPI_HMAC_KEY: ${{ secrets.CAPI_HMAC_KEY }}
          GITHUB_COPILOT_INTEGRATION_ID: ${{ vars.CAPI_INTEGRATION_ID }}
          GITHUB_MCP_ACCESS_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Upload eval logs artifact
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: eval-results-run${{ matrix.run_number }}-${{ env.EVAL_MODEL_NAME != '' && env.EVAL_MODEL_NAME || 'default' }}
          path: ${{runner.temp}}/sweagent-evals-run${{ matrix.run_number }}

  generate-results-summary:
    name: Generate Results Summary
    runs-on: ubuntu-latest
    needs: evals
    if: always() # Run even if some matrix jobs fail
    permissions:
      contents: read
      actions: read
    env:
      EVAL_MODEL_NAME: ${{ github.event.inputs.eval_model != '' && github.event.inputs.eval_model || (github.event.schedule == '0 1 * * *' && 'claude-3.7-sonnet' || github.event.schedule == '0 2 * * *' && 'oswe' || 'default') }}
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: "npm"
          cache-dependency-path: runtime/script/log-visualizer/package.json

      - name: Install log-visualizer dependencies
        working-directory: ./runtime/script/log-visualizer
        run: npm install

      - name: Build log-visualizer
        working-directory: ./runtime/script/log-visualizer
        run: npm run build

      - name: Generate results summary report
        working-directory: ./runtime/script/log-visualizer
        run: npm start report.html ${{ env.EVAL_MODEL_NAME }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Upload results summary report
        uses: actions/upload-artifact@v4
        with:
          name: nightly-eval-results-summary-${{ env.EVAL_MODEL_NAME }}
          path: ./runtime/script/log-visualizer/report.html

      - name: Add report link to run summary
        run: |
          echo "## 📊 Nightly Eval Results Summary (Model: ${{ env.EVAL_MODEL_NAME }})" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "The detailed HTML report has been generated and is available for download." >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 🔗 Download Links" >> $GITHUB_STEP_SUMMARY
          echo "- Scroll to the bottom of the page and look for the 'nightly-eval-results-summary-${{ env.EVAL_MODEL_NAME }}' artifact." >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "_The report contains trend analysis, test status tables, and detailed check results from the last 10 nightly evaluation runs for model: ${{ env.EVAL_MODEL_NAME }}._" >> $GITHUB_STEP_SUMMARY
