name: Publish action

on:
  workflow_dispatch:
  push:
    branches: [main]
    paths:
      - "runtime/**"
      - ".github/workflows/publish-action.yml"

permissions:
  contents: read

jobs:
  build_and_push:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          path: sweagentd

      - run: npm ci
        working-directory: ./sweagentd/runtime

      - uses: actions/checkout@v4
        with:
          token: ${{ secrets.COPILOT_DEVELOPER_ACTION_PAT }}
          repository: github/copilot-developer-action
          path: copilot-developer-action
          ref: main

      - run: ./sweagentd/runtime/script/action-build ./copilot-developer-action
        working-directory: ./
        env:
          EBPF_DOWNLOAD_GITHUB_TOKEN: "${{ secrets.EBPF_DOWNLOAD_GITHUB_TOKEN }}"
          BLACKBIRD_DOWNLOAD_GITHUB_TOKEN: "${{ secrets.BLACKBIRD_DOWNLOAD_GITHUB_TOKEN }}"

      - name: Commit changes
        working-directory: ./copilot-developer-action
        run: |
          git config --global user.name "${{ github.actor }}"
          git config --global user.email "${{ github.actor }}@users.noreply.github.com"
          git add .
          git commit -m "Update action"
          git push
