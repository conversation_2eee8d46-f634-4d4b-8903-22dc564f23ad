name: "Copilot Setup Steps"

on: workflow_dispatch

jobs:
  copilot-setup-steps:
    runs-on: ubuntu-latest
    permissions:
      id-token: write # for github/setup-goproxy
      contents: read # for actions/checkout
    steps:
      - uses: actions/checkout@v4
      - uses: github/setup-goproxy@v1.1.0
      - uses: actions/setup-go@v5
        with:
          go-version-file: go.mod
      - name: Download Go dependencies
        run: go mod download
      - uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: "npm"
          cache-dependency-path: runtime/package-lock.json
      - name: Install runtime dependencies
        working-directory: ./runtime
        run: npm install
      - name: Azure Login
        uses: azure/login@a65d910e8af852a8061c627c456678983e180302 #v2.2.0
        with:
          client-id: "${{ secrets.AIP_AZURE_CLIENT_ID }}" # id-sweagent-aip-dogfood
          tenant-id: "${{ secrets.AIP_AZURE_TENANT_ID }}"
          subscription-id: "${{ secrets.AIP_AZURE_SUBSCRIPTION_ID }}"
