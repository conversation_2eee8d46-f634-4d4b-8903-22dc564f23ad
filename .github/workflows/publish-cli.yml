name: Publish CLI npm package

on:
  workflow_dispatch:

permissions:
  contents: read
  packages: write

jobs:
  build_and_push:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version-file: runtime/.nvmrc
          cache: "npm"
          cache-dependency-path: runtime/package-lock.json
          registry-url: https://npm.pkg.github.com

      - run: npm ci
        working-directory: ./runtime
      
      - run: npm run build:cli
        working-directory: ./runtime

      - name: Write package.json
        run: ./script/cli-package-json.js "0.0.${{ github.run_number }}"
        working-directory: ./runtime

      - name: Publish CLI npm package to GitHub Packages
        run: npm publish
        working-directory: ./runtime/dist-cli
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
