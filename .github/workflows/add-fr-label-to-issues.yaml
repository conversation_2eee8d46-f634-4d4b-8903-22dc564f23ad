name: Label First Responder Issues

permissions:
  issues: write

on:
  issues:
    types: [opened]

jobs:
  label-specific-author:
    if: github.event.issue.user.login == 'ninesappbot'
    runs-on: ubuntu-latest
    steps:
      - name: Add label
        uses: actions-ecosystem/action-add-labels@v1
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          labels: |
            first-responder
