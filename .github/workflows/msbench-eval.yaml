name: "MSBench Run"

on:
  workflow_dispatch:
    inputs:
      model:
        description: "Model"
        required: true
        default: "claude-sonnet-4"
        type: string
      benchmark:
        description: "Benchmark"
        required: true
        default: "swebench.matplotlib__matplotlib-13989"
        type: string
      runOnGithubActionsVM:
        description: "Run on GitHub Actions VM"
        required: false
        default: true
        type: boolean
  schedule:
    - cron: "0 0 * * 1-5" # Run Monday through Friday at midnight UTC
    - cron: "0 0 * * 6" # Run Saturday at midnight UTC
    - cron: "0 0 * * 0" # Run Sunday at midnight UTC

jobs:
  msbench:
    env:
      # Benchmark selection based on schedule:
      # - Monday-Friday: 'swebench'
      # - Saturday: 'swebenchcsharp,mfa,refactorbench,multiswebench.ts'
      # - Sunday: 'setupbench,swebench'
      # - Manual runs: use input or default to 'swebench.matplotlib__matplotlib-13989'
      BENCHMARK: ${{ github.event_name == 'schedule' && (github.event.schedule == '0 0 * * 6' && 'swebenchcsharp,mfa,refactorbench,multiswebench.ts' || github.event.schedule == '0 0 * * 0' && 'setupbench,swebench' || 'swebench') || (inputs.benchmark || 'swebench.matplotlib__matplotlib-13989') }}
      MODEL: ${{ matrix.model }}
      # Worker count: 30 for all scheduled runs, 20 for manual runs (default)
      WORKERS: ${{ github.event_name == 'schedule' && '30' || '20' }}
      TAGS: ${{ github.event_name == 'schedule' && format('run=workflow-scheduled,branch={0},workers={1}', github.ref_name, '30') || format('run=workflow-trigger,branch={0},workers={1},triggeredBy={2},endpoint={3}-workflow-{0}', github.ref_name, '20', github.actor, vars.CAPI_INTEGRATION_ID) }}
      # Runner image selection:
      # - Sunday scheduled runs: use msbench image (--runner-no-custom-image, setup requires msbench image)
      # - Other scheduled runs: use Github Actions VM (default behavior)
      # - Manual runs: based on runOnGithubActionsVM input (default Github Actions VM)
      RUNNER_IMAGE_FLAG: ${{ ((github.event_name == 'schedule' && github.event.schedule == '0 0 * * 0') || (github.event_name != 'schedule' && inputs.runOnGithubActionsVM == false)) && '--runner-no-custom-image' || '' }}
    runs-on: ubuntu-latest
    environment: msbench
    strategy:
      fail-fast: false
      matrix:
        model: ${{ github.event_name == 'schedule' && fromJson('["claude-sonnet-4", "oswe", "claude-3.7-sonnet"]') || fromJson(format('["{0}"]', inputs.model || 'claude-sonnet-4')) }}
    permissions:
      id-token: write # for azure/login
      contents: read # for actions/checkout
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: "npm"
          cache-dependency-path: |
            runtime/package-lock.json
            benchmarks/msbench/agent_pkg/cpd-msbench-runner/package-lock.json
      - name: Build runtime and cpd-msbench-runner
        working-directory: ./script
        run: ./build-all
      - name: Azure Login
        uses: azure/login@a65d910e8af852a8061c627c456678983e180302 #v2.2.0
        with:
          client-id: "${{ vars.CPD_ID_CLIENT_ID }}" # id-cpd-ci
          tenant-id: "${{ vars.CPD_ID_TENANT_ID }}"
          subscription-id: "${{ vars.CPD_ID_SUBSCRIPTION_ID }}"
      - name: Configure pip authentication for Azure Artifacts
        run: |
          # Get access token for Azure DevOps/Artifacts
          ACCESS_TOKEN=$(az account get-access-token --resource 499b84ac-1321-427f-aa17-267ca6975798 --query accessToken -o tsv)

          # Configure pip to use Azure Artifacts feed with authentication
          mkdir -p ~/.pip
          cat > ~/.pip/pip.conf << EOF
          [global]
          extra-index-url = https://build:${ACCESS_TOKEN}@pkgs.dev.azure.com/devdiv/_packaging/MicrosoftSweBench/pypi/simple/
          trusted-host = pkgs.dev.azure.com

          [install]
          trusted-host = pkgs.dev.azure.com
          EOF

          # Set environment variables for pip authentication
          echo "PIP_EXTRA_INDEX_URL=https://build:${ACCESS_TOKEN}@pkgs.dev.azure.com/devdiv/_packaging/MicrosoftSweBench/pypi/simple/" >> $GITHUB_ENV
          echo "PIP_TRUSTED_HOST=pkgs.dev.azure.com" >> $GITHUB_ENV

          # Alternative: Create .netrc file for authentication
          cat > ~/.netrc << EOF
          machine pkgs.dev.azure.com
          login build
          password ${ACCESS_TOKEN}
          EOF
          chmod 600 ~/.netrc

          # Verify configuration
          echo "Testing pip configuration..."
          pip config list

      - name: Setup msbench-cli
        working-directory: ./benchmarks/msbench
        run: ./setup.sh
      - name: Run ${{ env.BENCHMARK }} - ${{ env.MODEL }}
        working-directory: ./benchmarks/msbench
        run: |
          # - Scheduled runs: Tagged with 'run=workflow-scheduled' and branch name
          # - Manual runs: Tagged with 'run=workflow-trigger' and branch name
          #
          # The --endpoint parameter queues runs on a dedicated workflow-specific endpoint+model
          # to avoid throttling the LLM with too many concurrent runs for the same branch.
          #
          # To cancel a queued or running benchmark, use from the command line (run ./setup.sh under benchmarks/msbench first):
          # msbench-cli cancel --run_id <run_id>
          ./1-update-and-run.sh $BENCHMARK ${{ vars.CAPI_INTEGRATION_ID }} --copilot-key-vault ${{ vars.CAPI_CI_AZURE_KEY_VAULT_URI }} --agent-model sweagent-capi:$MODEL --tags $TAGS --workers $WORKERS $RUNNER_IMAGE_FLAG --no-wait
