name: 🛵 Epic template
description: Tracking issue for Epics
title: "[Epic] "
labels: ["epic"]
body:
  - type: textarea
    id: summary
    attributes:
      label: Summary
      description: "one or two human readable sentences describing the batch, short and 🍭"
    validations:
      required: true
  - type: textarea
    id: objectives
    attributes:
      label: Objectives
      description: "What is the problem you are trying to solve with this Initiative? What does this Initiative accomplish?"
  - type: textarea
    id: team
    attributes:
      label: Team
      description: Who is responsible?
      value: |
        <!-- issue-fields start -->
        | Role & Responsibility | Username(s) |
        |--|--|
        | DRI | @username |
        | Engineering | @username(s) |
        | Product | @username(s) |
        | Design | @username(s) |
        | TPM | @username(s) |
        | Others | @username(s) |
        <!-- issue-fields end -->
  - type: textarea
    id: measuring
    attributes:
      label: Measuring Success
      description: "Dashboards, KPIs, and other indicators of success for this epic"
      value: |
        - *For example, how might this feature be integrated with a dashboard like https://looker.githubapp.com/dashboards/2505*
  - type: textarea
    id: done
    attributes:
      label: Definition of Done
      description: "Checklist of goals that must be met to reach 'done'"
      value: |
        - [ ] *done criteria*
        - [ ] *done criteria*
        - [ ] ...
  - type: textarea
    id: corresponding
    attributes:
      label: Corresponding Work
      description: "Add Batches (or Tasks when no batches available) that ladder up to this epic"
      value: |
        ```[tasklist]
        - [ ] ... ?
        - [ ] ... ?
        ```
  - type: textarea
    id: risks
    attributes:
      label: Risks
      description: "List of known risks to complete this work"
  - type: textarea
    id: dependencies
    attributes:
      label: Dependencies
      description: "List of known dependencies to complete this work"
  - type: textarea
    id: schedule
    attributes:
      label: Release Schedule
      description: "High level timeline of planned releases"
  - type: textarea
    id: documentation
    attributes:
      label: Supporting Documentation
      description: "Links to documents, spreadsheets, issues, etc."
