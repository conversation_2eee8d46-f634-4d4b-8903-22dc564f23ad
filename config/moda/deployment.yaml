required_builds:
  - sweagentd-moda-config-bundle / sweagentd-moda-config-bundle
  - sweagentd-docker-image / sweagentd-docker-image
  - sweagentd-docker-security / sweagentd-docker-security

environments:
  - name: production
    require_pipeline: true
    auto_deploy: true
    extra_start_message: >
      [Sentry Dashboard](https://sentry.io/organizations/github/releases/{{RELEASE_SHA}}/?environment=production&project=4508892525559808&statsPeriod=1h)
    cluster_selector:
      profile: general
      region: iad
  - name: staging
    skip_auto_merge: true
    secret_environment: production
    auto_deploy: true
    cluster_selector:
      profile: general
      region: iad
    notify_still_locked: true
  - name: staff-wus2-01
    cluster_selector:
      stamp: staff-wus2-01
      profile: proxima
    service_mesh:
      enabled: true
    extra_start_message: "Deploying to staff-wus2-01."
    require_pipeline: true
    skip_auto_merge: true # okay for now and for our staff stamp, should not apply to other proxima stamps though.

auto_start_pipeline: production_rollout

notifications:
  slack_channels:
    - "#copilot-extensibility-ops"

pipelines:
  production_rollout:
    thread_notifications: true
    auto_start_wait: false
    notify_users_via_dm: true
    stages:
      # Canary not enabled for now while we're in PoC stage
      # Enable this at a later date once we're serving production traffic.
      # - name: production/canary
      #   kind: canary_deployment
      #   config:
      #     environment: production
      #     hosts: canary
      #   gates:
      #     - kind: timer
      #       duration: 300
      - name: production
        kind: deployment
        start_message: |-
          Deploying sweagentd to production now.
          If you are deploying something risky, make sure the current First Responder knows!
          If you are deploying outside of North America business hours, consider temporarily taking the pager for 30 min with `.pager me copilot-extensibility-primary 30`.
        config:
          environment: production
        gates:
          - kind: timer
            duration: 600 # 10 minutes
          - kind: datadog
            monitor_id: "178001263" # monitor URL: https://app.datadoghq.com/monitors/178001263
            execution_mode: continuous
            display_name: Production Availability Gate
            evaluation_period: 300
            auto_rollback:
              enabled: false
