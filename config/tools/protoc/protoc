#!/bin/bash
#
# Expected to execute inside the docker container built from /config/tools/protoc/Dockerfile
#
set -e

echo "Generating proto files..."

ruby_out=./ruby/gen
twirp_out=./twirp

function main() {
    cleanup
    generate
}

function cleanup() {
    echo "Cleaning older version..."
    rm -rf twirp/proto
    rm -rf ruby/gen/proto
}

function generate() {
    protoc \
        --twirp_out=${twirp_out} \
        --twirp_ruby_out=${ruby_out} \
        --ruby_out=${ruby_out} \
        --go_out=${twirp_out} \
        ./proto/**/*.proto

    # Print a lovely green checkmark if we succeeded
    printf " \033[32m✔︎\033[0m\n"
}

main
