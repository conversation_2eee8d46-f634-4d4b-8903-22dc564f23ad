FROM --platform=linux/amd64 golang:1.20

WORKDIR /go/src/github.com/github/sweagentd

# Squash a warning about world-writable dirs in PATH
RUN chmod o-w /go/bin /go/

RUN rm -rf /go/src/github.com/golang/protobuf
RUN go install github.com/golang/protobuf/protoc-gen-go@v1.5.3

RUN rm -rf /go/src/github.com/twitchtv/twirp
RUN go install github.com/twitchtv/twirp/protoc-gen-twirp@v8.1.3

RUN rm -rf /go/src/github.com/github/twirp-ruby
RUN go install github.com/github/twirp-ruby/protoc-gen-twirp_ruby@v1.10.0

RUN apt-get update && \
    apt-get -y install unzip
RUN curl -sL https://github.com/google/protobuf/releases/download/v3.20.0/protoc-3.20.0-linux-x86_64.zip -o /tmp/protoc.zip
RUN unzip /tmp/protoc.zip -d /tmp/protoc && \
    mv /tmp/protoc/bin/protoc /usr/local/bin/protoc && \
    mv /tmp/protoc/include/google /usr/local/include/

ADD config/tools/protoc/protoc /go/src/github.com/github/sweagentd/protoc

ENTRYPOINT ["/go/src/github.com/github/sweagentd/protoc"]
