apiVersion: v1
kind: Service
metadata:
  name: sweagentd
  labels:
    service: sweagentd
  annotations:
    # https://thehub.github.com/epd/engineering/products-and-services/internal/moda/reference/load-balancer-annotations#modagithubnetdomain-name
    # For example, this will render out to "sweagentd"-production.service.iad.github.net
    # if this is your production deployment in iad.
    moda.github.net/domain-name: "sweagentd-%environment%.service.%region%.github.net"
    opaque.github.net/checks: |
      - http:
          url: https://{{.Instance.Name}}/_ping
spec:
  ports:
    - name: http
      port: 8080
      protocol: TCP
      targetPort: http
  selector:
    app: sweagentd
  type: LoadBalancer
