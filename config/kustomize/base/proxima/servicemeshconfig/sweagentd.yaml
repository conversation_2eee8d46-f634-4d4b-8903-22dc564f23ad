apiVersion: octomesh.github.com/v1alpha1
kind: ServiceMeshConfig
metadata:
  name: config  # name *must* be `config`
spec:
  authorization:
    create: true
    strictTLS: false

    # List the namespaces that will be authorized to make network requests to this service.
    # This example will allow inbound access from the `myapp` app within with the same stamp.
    inboundNamespaces:
      - copilot-api-%stamp%
  loadBalancing:
    retryOnReset: true
    useLocalityLoadBalancing: true