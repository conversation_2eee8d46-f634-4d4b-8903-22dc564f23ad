apiVersion: apps/v1
kind: Deployment
metadata:
  name: sweagentd
spec:
  replicas: 6
  selector:
    matchLabels:
      app: sweagentd
  template:
    metadata:
      labels:
        app: sweagentd
      annotations:
        fluentbit.io/parser: logfmt
        observability.github.com/splunk_index: sweagentd
    spec:
      dnsPolicy: ClusterFirst
      containers:
        - name: sweagentd
          image: sweagentd
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          resources:
            requests:
              cpu: 200m
              memory: 200Mi
            limits:
              cpu: 500m
              memory: 500Mi
          envFrom:
            - configMapRef:
                name: kube-cluster-metadata
            - secretRef:
                name: vault-secrets
          env:
            # Moda metadata
            - name: KUBE_CONTAINER_NAME
              value: "sweagentd"
            - name: KUBE_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: KUBE_POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: APP_SHA
              valueFrom:
                fieldRef:
                  fieldPath: metadata.annotations['heaven.githubapp.com/sha']
            - name: APP_REF
              valueFrom:
                fieldRef:
                  fieldPath: metadata.annotations['heaven.githubapp.com/ref']
            - name: GOMAXPROCS
              valueFrom:
                resourceFieldRef:
                  containerName: sweagentd
                  resource: limits.cpu

            # OpenTelemetry
            - name: OTEL_EXPORTER_OTLP_TRACES_ENDPOINT
              value: "http://otelcol-mesh.otelcol-production.svc.cluster.local:14318/v1/traces"
            # Stats
            - name: STATS_PERIOD
              value: "5s"
            - name: STATS_ADDR
              value: "$(KUBE_NODE_HOSTNAME):28125"
            # Hydro
            - name: HYDRO_CLIENT_ID
              value: sweagentd
            - name: HYDRO_GROUP_NAME
              value: sweagentd
            - name: HYDRO_BROKERS
              # Should line up with what's in https://github.com/github/hydro-schemas/blob/main/topic-configuration/clusters.yaml#L165
              value: "hydro-kafka-%stamp%-%stamp%.service.%stamp%-az1.consul:9093 hydro-kafka-%stamp%-%stamp%.service.%stamp%-az2.consul:9093 hydro-kafka-%stamp%-%stamp%.service.%stamp%-az3.consul:9093"

            # Authnd
            - name: AUTHND_URL
              value: "http://authnd.authnd-%stamp%.svc.cluster.local:8080"

            # GitHub metadata
            - name: GITHUB_API_BASE_URL
              value: "https://internal-api.service.%stamp%.github.net"
            - name: GITHUB_INTERNAL_API_BASE_URL
              value: "https://internal-api.service.%stamp%.github.net"
            - name: COPILOT_INTEGRATION_ID
              value: "copilot-developer"

            # GitHub App
            # APP_ID Needs to change to match proxima stamp, this is currently hardcoded to match staff-wus2-01.
            # TODO: Outbound calls need to support tenant awareness #3815 https://github.com/github/sweagentd/issues/3759
            - name: GITHUB_APP_ID
              value: "1521"
            - name: GITHUB_APP_LOGIN
              value: "copilot-swe-agent"
            - name: GITHUB_BOT_LOGIN
              value: "c7f03f6b3fa4c9b88c292f068c344b14ef"
            - name: GITHUB_COMMIT_LOGIN
              value: "c7f03f6b3fa4c9b88c292f068c344b14ef"
            - name: GITHUB_COMMIT_EMAIL
              value: "<EMAIL>"

            # Runtime config
            - name: AGENT_LAUNCHER
              value: "actions"
            - name: COPILOT_API_URL
              value: "https://copilot-api.<tenant>.ghe.com"
            - name: AGENT_CALLBACK_URL
              value: "https://copilot-api.<tenant>.ghe.com/agents/swe/agent"
            - name: DISABLE_HYDRO_CONSUMER
              value: "false"

            # MCP
            - name: COPILOT_MCP_ENABLED
              value: "true"

            # Failbot
            - name: FAILBOT_HAYSTACK_URL
              value: "http://failbotg.failbotg-%stamp%.svc.cluster.local/api/needles"

            # Monolith TWIRP
            - name: MONOLITH_TWIRP_API_URL
              value: "https://internal-api.service.%stamp%.github.net/internal"
            # Limiter TWIRP
            - name: COPILOT_LIMITER_API_URL
              value: "http://copilot-limiter-http.copilot-limiter-%stamp%.svc.cluster.local:8080"
            # Aqueduct URL
            - name: AQUEDUCT_URL
              value: "https://aqueduct.service.%stamp%.github.net"
            # We don't use experimentation in proxima.
            # # Experimentation
            # - name: EXPERIMENTATION_API_URL
            #   value: https://default.exp-tas.com/vscode/ab
          lifecycle:
            preStop:
              exec:
                command: ["sleep", "5"]
          readinessProbe:
            exec:
              command:
                - cat
                - /tmp/consumer_healthy
            initialDelaySeconds: 5
            periodSeconds: 5
