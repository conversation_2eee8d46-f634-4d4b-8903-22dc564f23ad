apiVersion: v1
kind: Service
metadata:
  name: sweagentd
  labels:
    service: sweagentd
  annotations:
    moda.github.net/domain-name: "sweagentd.service.%stamp%.github.net"
    moda.github.net/istio-ingress-enabled: "true"
    opaque.github.net/checks: |
      - http:
          url: https://{{.Instance.Name}}/_ping
spec:
  ports:
    - name: http
      port: 8080
      protocol: TCP
      targetPort: http
  selector:
    app: sweagentd
  type: LoadBalancer
  