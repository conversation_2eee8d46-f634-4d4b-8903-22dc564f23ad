# This file was generated with <PERSON><PERSON>mize. Please do not edit manually.
apiVersion: apps/v1
kind: Deployment
metadata:
    name: sweagentd
spec:
    replicas: 2
    selector:
        matchLabels:
            app: sweagentd
    template:
        metadata:
            annotations:
                fluentbit.io/parser: logfmt
                observability.github.com/splunk_index: sweagentd
            labels:
                app: sweagentd
        spec:
            containers:
                - env:
                    - name: KUBE_CONTAINER_NAME
                      value: sweagentd
                    - name: KUBE_NAMESPACE
                      valueFrom:
                        fieldRef:
                            fieldPath: metadata.namespace
                    - name: KUBE_POD_NAME
                      valueFrom:
                        fieldRef:
                            fieldPath: metadata.name
                    - name: APP_SHA
                      valueFrom:
                        fieldRef:
                            fieldPath: metadata.annotations['heaven.githubapp.com/sha']
                    - name: APP_REF
                      valueFrom:
                        fieldRef:
                            fieldPath: metadata.annotations['heaven.githubapp.com/ref']
                    - name: GOMAXPROCS
                      valueFrom:
                        resourceFieldRef:
                            containerName: sweagentd
                            resource: limits.cpu
                    - name: OTEL_EXPORTER_OTLP_TRACES_ENDPOINT
                      value: https://otelcol-$(GH_OTEL_K8S_CLUSTER_NAME).service.$(GH_OTEL_GH_INFRA_SITE).github.net/v1/traces
                    - name: STATS_PERIOD
                      value: 5s
                    - name: STATS_ADDR
                      value: $(KUBE_NODE_HOSTNAME):28125
                    - name: HYDRO_CLIENT_ID
                      value: sweagentd
                    - name: HYDRO_GROUP_NAME
                      value: sweagentd-staging
                    - name: HYDRO_BROKERS
                      value: hydro-kafka-potomac.service.ac4-iad.consul:9093 hydro-kafka-potomac.service.ash1-iad.consul:9093 hydro-kafka-potomac.service.va3-iad.consul:9093
                    - name: HYDRO_ROOT_CA_FILEPATH
                      value: /etc/ssl/certs/cp1-iad-production-1487801205-root.pem
                    - name: AUTHND_URL
                      value: https://authnd-production.service.iad.github.net
                    - name: FRENO_ADDR
                      value: freno.service.github.net:8111
                    - name: GITHUB_API_BASE_URL
                      value: https://api.github.com/
                    - name: GITHUB_INTERNAL_API_BASE_URL
                      value: https://internal-api.service.iad.github.net/
                    - name: GITHUB_GIT_HOST
                      value: github.com
                    - name: GITHUB_GIT_HOST_PROTOCOL
                      value: https
                    - name: COPILOT_INTEGRATION_ID
                      value: copilot-developer
                    - name: GITHUB_APP_ID
                      value: "1143301"
                    - name: GITHUB_APP_LOGIN
                      value: copilot-swe-agent
                    - name: GITHUB_BOT_LOGIN
                      value: copilot-swe-agent[bot]
                    - name: GITHUB_COMMIT_LOGIN
                      value: copilot-swe-agent[bot]
                    - name: GITHUB_COMMIT_EMAIL
                      value: <EMAIL>
                    - name: AGENT_LAUNCHER
                      value: actions
                    - name: AGENT_CALLBACK_URL
                      value: https://api.githubcopilot.com/agents/swe-staging/agent
                    - name: DISABLE_HYDRO_CONSUMER
                      value: "false"
                    - name: COPILOT_MCP_ENABLED
                      value: "true"
                    - name: MONOLITH_TWIRP_API_URL
                      value: https://internal-api.service.iad.github.net/internal
                    - name: COPILOT_LIMITER_API_URL
                      value: https://copilot-limiter-production.service.iad.github.net
                    - name: AQUEDUCT_URL
                      value: https://aqueduct-gateway-production.service.iad.github.net
                    - name: EXPERIMENTATION_API_URL
                      value: https://default.exp-tas.com/vscode/ab
                  envFrom:
                    - configMapRef:
                        name: kube-cluster-metadata
                    - secretRef:
                        name: vault-secrets
                  image: sweagentd
                  lifecycle:
                    preStop:
                        exec:
                            command:
                                - sleep
                                - "5"
                  name: sweagentd
                  ports:
                    - containerPort: 8080
                      name: http
                      protocol: TCP
                  readinessProbe:
                    exec:
                        command:
                            - cat
                            - /tmp/consumer_healthy
                    initialDelaySeconds: 5
                    periodSeconds: 5
                  resources:
                    limits:
                        cpu: 500m
                        memory: 500Mi
                    requests:
                        cpu: 200m
                        memory: 200Mi
            dnsPolicy: Default
