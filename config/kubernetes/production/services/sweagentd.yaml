# This file was generated with Kustomize. Please do not edit manually.
apiVersion: v1
kind: Service
metadata:
    annotations:
        moda.github.net/domain-name: sweagentd-%environment%.service.%region%.github.net
        opaque.github.net/checks: |
            - http:
                url: https://{{.Instance.Name}}/_ping
    labels:
        service: sweagentd
    name: sweagentd
spec:
    ports:
        - name: http
          port: 8080
          protocol: TCP
          targetPort: http
    selector:
        app: sweagentd
    type: LoadBalancer
