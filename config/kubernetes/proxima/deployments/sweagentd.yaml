# This file was generated with <PERSON><PERSON>mize. Please do not edit manually.
apiVersion: apps/v1
kind: Deployment
metadata:
    name: sweagentd
spec:
    replicas: 6
    selector:
        matchLabels:
            app: sweagentd
    template:
        metadata:
            annotations:
                fluentbit.io/parser: logfmt
                observability.github.com/splunk_index: sweagentd
            labels:
                app: sweagentd
        spec:
            containers:
                - env:
                    - name: KUBE_CONTAINER_NAME
                      value: sweagentd
                    - name: KUBE_NAMESPACE
                      valueFrom:
                        fieldRef:
                            fieldPath: metadata.namespace
                    - name: KUBE_POD_NAME
                      valueFrom:
                        fieldRef:
                            fieldPath: metadata.name
                    - name: APP_SHA
                      valueFrom:
                        fieldRef:
                            fieldPath: metadata.annotations['heaven.githubapp.com/sha']
                    - name: APP_REF
                      valueFrom:
                        fieldRef:
                            fieldPath: metadata.annotations['heaven.githubapp.com/ref']
                    - name: GOMAXPROCS
                      valueFrom:
                        resourceFieldRef:
                            containerName: sweagentd
                            resource: limits.cpu
                    - name: OTEL_EXPORTER_OTLP_TRACES_ENDPOINT
                      value: http://otelcol-mesh.otelcol-production.svc.cluster.local:14318/v1/traces
                    - name: STATS_PERIOD
                      value: 5s
                    - name: STATS_ADDR
                      value: $(KUBE_NODE_HOSTNAME):28125
                    - name: HYDRO_CLIENT_ID
                      value: sweagentd
                    - name: HYDRO_GROUP_NAME
                      value: sweagentd
                    - name: HYDRO_BROKERS
                      value: hydro-kafka-%stamp%-%stamp%.service.%stamp%-az1.consul:9093 hydro-kafka-%stamp%-%stamp%.service.%stamp%-az2.consul:9093 hydro-kafka-%stamp%-%stamp%.service.%stamp%-az3.consul:9093
                    - name: AUTHND_URL
                      value: http://authnd.authnd-%stamp%.svc.cluster.local:8080
                    - name: GITHUB_API_BASE_URL
                      value: https://internal-api.service.%stamp%.github.net
                    - name: GITHUB_INTERNAL_API_BASE_URL
                      value: https://internal-api.service.%stamp%.github.net
                    - name: COPILOT_INTEGRATION_ID
                      value: copilot-developer
                    - name: GITHUB_APP_ID
                      value: "1521"
                    - name: GITHUB_APP_LOGIN
                      value: copilot-swe-agent
                    - name: GITHUB_BOT_LOGIN
                      value: c7f03f6b3fa4c9b88c292f068c344b14ef
                    - name: GITHUB_COMMIT_LOGIN
                      value: c7f03f6b3fa4c9b88c292f068c344b14ef
                    - name: GITHUB_COMMIT_EMAIL
                      value: <EMAIL>
                    - name: AGENT_LAUNCHER
                      value: actions
                    - name: COPILOT_API_URL
                      value: https://copilot-api.<tenant>.ghe.com
                    - name: AGENT_CALLBACK_URL
                      value: https://copilot-api.<tenant>.ghe.com/agents/swe/agent
                    - name: DISABLE_HYDRO_CONSUMER
                      value: "false"
                    - name: COPILOT_MCP_ENABLED
                      value: "true"
                    - name: FAILBOT_HAYSTACK_URL
                      value: http://failbotg.failbotg-%stamp%.svc.cluster.local/api/needles
                    - name: MONOLITH_TWIRP_API_URL
                      value: https://internal-api.service.%stamp%.github.net/internal
                    - name: COPILOT_LIMITER_API_URL
                      value: http://copilot-limiter-http.copilot-limiter-%stamp%.svc.cluster.local:8080
                    - name: AQUEDUCT_URL
                      value: https://aqueduct.service.%stamp%.github.net
                  envFrom:
                    - configMapRef:
                        name: kube-cluster-metadata
                    - secretRef:
                        name: vault-secrets
                  image: sweagentd
                  lifecycle:
                    preStop:
                        exec:
                            command:
                                - sleep
                                - "5"
                  name: sweagentd
                  ports:
                    - containerPort: 8080
                      name: http
                      protocol: TCP
                  readinessProbe:
                    exec:
                        command:
                            - cat
                            - /tmp/consumer_healthy
                    initialDelaySeconds: 5
                    periodSeconds: 5
                  resources:
                    limits:
                        cpu: 500m
                        memory: 500Mi
                    requests:
                        cpu: 200m
                        memory: 200Mi
            dnsPolicy: ClusterFirst
