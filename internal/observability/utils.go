package observability

import (
	"context"
	"errors"
	"net/http"
	"time"

	"github.com/github/github-telemetry-go/kvp"
	"github.com/github/github-telemetry-go/log"
	"github.com/github/go-stats"
)

type HydrateTelemetryFunc func(ctx context.Context, r *http.Request) (stats stats.Tags, exception map[string]string, log []kvp.Field)

// LoggerWithTelemetry returns a logger with telemetry fields populated from the
// context using the custom HydrateTelemetryFunc if it is set.
func (e *Exporters) LoggerWithTelemetry(ctx context.Context) log.Logger {
	if e.telemetryFunc == nil {
		return e.Logger.WithContext(ctx)
	}

	_, _, kvps := e.telemetryFunc(ctx, nil)
	return e.Logger.WithContext(ctx).WithFields(kvps...)
}

func (e *Exporters) LoggerWithRequestTelemetry(ctx context.Context, r *http.Request) log.Logger {
	if e.telemetryFunc == nil {
		return e.Logger.WithContext(ctx)
	}

	_, _, kvps := e.telemetryFunc(ctx, r)
	return e.Logger.WithContext(ctx).WithFields(kvps...)
}

// LogAndReportError is a convenience function that logs an error and reports
// it to sentry, using the custom HydrateTelemetryFunc if it is set.
func (e *Exporters) LogAndReportError(ctx context.Context, err error, message string) {
	if err == nil && message == "" {
		return
	}
	if err == nil {
		err = errors.New(message)
	}

	if e.telemetryFunc == nil {
		e.Logger.WithContext(ctx).WithError(err).Error(message)
		_ = e.Reporter.Report(ctx, err, map[string]string{})
		return
	}

	_, ex, kvps := e.telemetryFunc(ctx, nil)
	e.Logger.WithContext(ctx).WithFields(kvps...).WithError(err).Error(message)
	_ = e.Reporter.Report(ctx, err, ex)
}

func (e *Exporters) TelemetryTags(ctx context.Context) stats.Tags {
	if e.telemetryFunc == nil {
		return stats.Tags{}
	}

	tags, _, _ := e.telemetryFunc(ctx, nil)
	return tags
}

func (e *Exporters) Increment(ctx context.Context, key string, tags stats.Tags) {
	e.Statter.Counter(key, e.TelemetryTags(ctx).Merge(tags), 1)
}

func (e *Exporters) DistributionMs(ctx context.Context, key string, tags stats.Tags, time time.Duration) {
	e.Statter.DistributionMs(key, e.TelemetryTags(ctx).Merge(tags), time)
}
