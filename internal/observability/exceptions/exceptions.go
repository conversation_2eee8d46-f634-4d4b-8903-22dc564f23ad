package exceptions

import (
	"os"
	"regexp"
	"sync"

	"github.com/github/github-telemetry-go/log"
	"github.com/github/go-exceptions"
	httpexporter "github.com/github/go-exceptions/exporters/http"
	"github.com/github/go-exceptions/exporters/writer"
	"github.com/github/go-exceptions/stacktracers/pkgerrors"
)

var (
	rollupChecks      []func(exception error) (info string, ok bool) = []func(exception error) (info string, ok bool){}
	rollupChecksMutex sync.Mutex
)

// AddRollupCheck adds a rollup check function to the list of rollup checks.
// You can just return the raw string, the consumer of these registered checks will hash it for you.
func AddRollupCheck(rollupFunc func(exception error) (info string, ok bool)) {
	rollupChecksMutex.Lock()
	defer rollupChecksMutex.Unlock()
	rollupChecks = append(rollupChecks, rollupFunc)
}

// NewReporter configures a new exceptions reporter based on the config.
// It prints reports to stderr in development environment, but sends them to
// Failbotg (which is proxied to Sentry) in production.
func NewReporter(
	isDevelopment bool,
	serviceName,
	environment,
	sha,
	ref string,
	logger log.Logger,
) (*exceptions.Reporter, error) {
	var err error
	var exporter exceptions.Exporter = writer.NewExporter(os.Stderr)

	if !isDevelopment {
		exporter, err = httpexporter.NewExporter()
		if err != nil {
			return nil, err
		}
	}

	errorLogger := func(reportErr, exception error, payload map[string]string) {
		logger.WithError(reportErr).Error("reporter.Report has failed")
	}

	reporter, err := newReporter(exporter, serviceName, environment, sha, ref, errorLogger)
	return reporter, err
}

// broken out for testing
func newReporter(
	exporter exceptions.Exporter,
	serviceName,
	environment,
	sha,
	ref string,
	errorLogger exceptions.ErrorLoggerFunc,
) (*exceptions.Reporter, error) {
	reporter, err := exceptions.NewReporter(
		exceptions.WithExporter(exporter),
		exceptions.WithErrorLogger(errorLogger),
		exceptions.WithApplication(serviceName),
		exceptions.WithStacktraceFunc(NewRedactedStackTracer()),
		exceptions.WithCustomRedaction(redactSensitiveInfo),
		exceptions.WithRollupInfoFunc(func(exception error) (string, bool) {
			toRollup := ""

			// Enumerate rollupcheck, invoking each to see if we want to use it
			for _, check := range rollupChecks {
				if info, ok := check(exception); ok {
					toRollup = info
					break
				}
			}

			if toRollup == "" {
				toRollup = exception.Error()
			}

			// go-exceptions calls our custom redaction function _before_ calling
			// the rollup function, so we need to manually apply redaction here
			toRollup = redactSensitiveInfo(toRollup)

			return redactRollupInfo(toRollup), true
		}),
		exceptions.WithValues(map[string]string{
			"deployed_to": environment,
			"release":     sha,
			"ref":         ref,
		}),
	)
	if err != nil {
		return nil, err
	}

	return reporter, nil
}

func NewRedactedStackTracer() func(err error) ([]exceptions.StackTrace, string) {
	pe := pkgerrors.NewStackTracer()
	fn := func(err error) ([]exceptions.StackTrace, string) {
		stacktrace, rp := pe(err)
		// go-exceptions calls our custom redaction function _before_ calling
		// the stack tracer, so we need to manually apply redaction here
		for i, st := range stacktrace {
			if st.Type != "" {
				stacktrace[i].Type = redactSensitiveInfo(st.Type)
			}
			if st.Value != "" {
				stacktrace[i].Value = redactSensitiveInfo(st.Value)
			}
		}
		return stacktrace, rp
	}
	return fn
}

type replacer struct {
	placeholder string
	pattern     *regexp.Regexp
}

var sensitiveInfoReplacers = []replacer{
	{"https://api.github.com/repos/:owner/:repo/issues/:number/reactions/:id", regexp.MustCompile(`https://api\.github\.com/repos/[a-zA-Z0-9._-]+/[a-zA-Z0-9._-]+/issues/\d+/reactions/\d+`)},
	{"https://api.github.com/repos/:owner/:repo/actions/jobs/:id", regexp.MustCompile(`https://api\.github\.com/repos/[a-zA-Z0-9._-]+/[a-zA-Z0-9._-]+/actions/jobs/\d+`)},
	{"https://api.github.com/repos/:owner/:repo/collaborators/:user/permission", regexp.MustCompile(`https://api\.github\.com/repos/[a-zA-Z0-9._-]+/[a-zA-Z0-9._-]+/collaborators/[a-zA-Z0-9._-]+/permission`)},
	{"https://api.github.com/repos/:owner/:repo/issues/:number", regexp.MustCompile(`https://api\.github\.com/repos/[a-zA-Z0-9._-]+/[a-zA-Z0-9._-]+/issues/\d+`)},
	{"https://api.github.com/repos/:owner/:repo/issues/comments/:id", regexp.MustCompile(`https://api\.github\.com/repos/[a-zA-Z0-9._-]+/[a-zA-Z0-9._-]+/issues/comments/\d+`)},
	{"https://api.github.com/repos/:owner/:repo/pulls/:number/comments/:id", regexp.MustCompile(`https://api\.github\.com/repos/[a-zA-Z0-9._-]+/[a-zA-Z0-9._-]+/pulls/\d+/comments/\d+`)},
	{"https://api.github.com/repos/:owner/:repo/pulls/:number", regexp.MustCompile(`https://api\.github\.com/repos/[a-zA-Z0-9._-]+/[a-zA-Z0-9._-]+/pulls/\d+`)},
	{"https://api.github.com/repos/:owner/:repo/git/commits/:hash", regexp.MustCompile(`https://api\.github\.com/repos/[a-zA-Z0-9._-]+/[a-zA-Z0-9._-]+/git/commits/[a-zA-Z0-9]+`)},
	{"https://api.github.com/repos/:owner/:repo", regexp.MustCompile(`https://api\.github\.com/repos/[a-zA-Z0-9._-]+/[a-zA-Z0-9._-]+`)},

	// Alternate endpoint format using repository ID
	{"https://api.github.com/repositories/:repo/issues/:number/reactions/:id", regexp.MustCompile(`https://api\.github\.com/repositories/\d+/issues/\d+/reactions/\d+`)},
	{"https://api.github.com/repositories/:repo/actions/jobs/:id", regexp.MustCompile(`https://api\.github\.com/repositories/\d+/actions/jobs/\d+`)},
	{"https://api.github.com/repositories/:repo/collaborators/:user/permission", regexp.MustCompile(`https://api\.github\.com/repositories/\d+/collaborators/[a-zA-Z0-9._-]+/permission`)},
	{"https://api.github.com/repositories/:repo/issues/:number", regexp.MustCompile(`https://api\.github\.com/repositories/\d+/issues/\d+`)},
	{"https://api.github.com/repositories/:repo/issues/comments/:id", regexp.MustCompile(`https://api\.github\.com/repositories/\d+/issues/comments/\d+`)},
	{"https://api.github.com/repositories/:repo/pulls/:number/comments/:id", regexp.MustCompile(`https://api\.github\.com/repositories/\d+/pulls/\d+/comments/\d+`)},
	{"https://api.github.com/repositories/:repo/pulls/:number", regexp.MustCompile(`https://api\.github\.com/repositories/\d+/pulls/\d+`)},
	{"https://api.github.com/repositories/:repo/git/commits/:hash", regexp.MustCompile(`https://api\.github\.com/repositories/\d+/git/commits/[a-zA-Z0-9]+`)},
	{"https://api.github.com/repositories/:repo", regexp.MustCompile(`https://api\.github\.com/repositories/\d+`)},

	{"https://api.github.com/applications/:id/token", regexp.MustCompile(`https://api\.github\.com/applications/[a-zA-Z0-9._-]+/token`)},
	{"https://internal-api.service.iad.github.net/internal/repos/:owner/:repo", regexp.MustCompile(`https://internal-api\.service\.iad\.github\.net/internal/repos/[a-zA-Z0-9._-]+/[a-zA-Z0-9._-]+`)},
	{"https://github.com/:owner/:repo.git", regexp.MustCompile(`https://github\.com/[a-zA-Z0-9._-]+/[a-zA-Z0-9._-]+\.git`)},
	{"https://github.com/:owner/:repo", regexp.MustCompile(`https://github\.com/[a-zA-Z0-9._-]+/[a-zA-Z0-9._-]+`)},
	{"https://api.githubcopilot.com/agents/sessions/:session", regexp.MustCompile(`https://api\.githubcopilot\.com/agents/sessions/[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}`)},
	{"copilot/<branch>", regexp.MustCompile(`copilot/[\da-zA-Z0-9-]+`)},

	{"<ip-address>", regexp.MustCompile(`(?:\d{1,3}\.){3}\d{1,3}(?::\d{1,5})?`)},
}

// redactSensitiveInfo redacts sensitive information from the error message.
func redactSensitiveInfo(val string) string {
	for _, re := range sensitiveInfoReplacers {
		val = re.pattern.ReplaceAllString(val, re.placeholder) // replace sensitive info with placeholders
	}

	return val
}

var rollupReplacers = []replacer{
	{"<database-id>", regexp.MustCompile(`\d+-\d+-[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}`)},
	{"<request-id>", regexp.MustCompile(`[0-9A-Fa-f]{1,8}:[0-9A-Fa-f]{1,8}:[0-9A-Fa-f]{1,8}:[0-9A-Fa-f]{1,8}:[0-9A-Fa-f]{1,8}`)},
	{"<request-id>", regexp.MustCompile(`\b[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\b`)},
	{"<retry-info>", regexp.MustCompile(`retried \d+ times \(total retry wait time: \d+ seconds\)`)},
	{"<datetime>", regexp.MustCompile(`\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?Z`)},
	{"timeout after <timeout>", regexp.MustCompile(`timeout after \d+(?:\.\d+)?s`)},
}

// redactRollupInfo redacts request-specific information from the rollup info.
// This ensures that request-specific information is not considered when grouping
// exceptions in Sentry. Unlike [redactSensitiveInfo], this function is not not
// applied to the exception message itself, only the rollup used for grouping.
func redactRollupInfo(val string) string {
	for _, re := range rollupReplacers {
		val = re.pattern.ReplaceAllString(val, re.placeholder) // replace sensitive info with placeholders
	}

	return val
}
