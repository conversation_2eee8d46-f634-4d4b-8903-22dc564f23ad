package jobexecutor

import (
	"context"
	_ "embed"
	"fmt"
	"slices"
	"strconv"
	"strings"
	"time"

	aqueductApi "github.com/github/aqueduct-client-go/v3/proto"
	"github.com/github/github-telemetry-go/kvp"
	"github.com/github/go-mark"
	"github.com/github/sweagentd/internal/aqueduct"
	aqueductJobs "github.com/github/sweagentd/internal/aqueduct/jobs"
	"github.com/github/sweagentd/internal/capi"
	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/githubtwirp"
	"github.com/github/sweagentd/internal/joberrors"
	"github.com/github/sweagentd/internal/jobs"
	ju "github.com/github/sweagentd/internal/jobutils"
	"github.com/github/sweagentd/internal/launcher"
	"github.com/github/sweagentd/internal/observability"
	gg "github.com/google/go-github/v72/github"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type RequeueOptions struct {
	IntervalSeconds int
}

type JobExecutor interface {
	QueueJob(ctx context.Context, gh github.ClientInterface, job *jobs.Job) error
	RequeueJob(ctx context.Context, job *aqueductJobs.AgentJob, requeueOptions *RequeueOptions) error
	ProcessJob(ctx context.Context, queueJob *aqueductJobs.AgentJob) error
	ExecuteJob(ctx context.Context, gh github.ClientInterface, sessionClient capi.SessionClient, job *jobs.Job) error
	ShouldWake(ctx context.Context, userId int64, problemStatement, jobId string) (bool, error)
	CancelJob(ctx context.Context, opts *CancelJobOpts) error
	GetPremiumQuotaStatus(ctx context.Context, ownerID, repoID, userID int64) (bool, int32, error)
}

type AgentJobExecutor struct {
	launcher       launcher.AgentLauncher
	jobsStore      jobs.JobsStore
	ffClient       featureflags.Client
	obsv           observability.Exporters
	capiClient     capi.Client
	ghTwirp        githubtwirp.ClientInterface
	ghFactory      github.ClientFactoryInterface
	aqueductClient aqueduct.AqueductClient
}

var _ JobExecutor = (*AgentJobExecutor)(nil)

func NewAgentJobExecutor(obsv observability.Exporters, capiClient capi.Client, launcher launcher.AgentLauncher, jobsStore jobs.JobsStore, ffClient featureflags.Client, ghTwirp githubtwirp.ClientInterface, ghFactory github.ClientFactoryInterface, aqueductClient aqueduct.AqueductClient) *AgentJobExecutor {
	return &AgentJobExecutor{
		obsv:           obsv,
		launcher:       launcher,
		jobsStore:      jobsStore,
		ffClient:       ffClient,
		capiClient:     capiClient,
		ghTwirp:        ghTwirp,
		ghFactory:      ghFactory,
		aqueductClient: aqueductClient,
	}
}

// QueueJob queues a job for processing. We should be mindful of things to do
// in this method to keep it fast and efficient, as there are latency-sensitive
// consumers like the job APIs.
//
// TODO (colbylwilliams): The [gh] arg should be removed once we get closer to
// shipping the job queuing more broadly. It is here now to avoid minting yet
// another token for the user. If the feature flag isn't enabled, this function
// still executes the job immediately.
func (e *AgentJobExecutor) QueueJob(ctx context.Context, gh github.ClientInterface, job *jobs.Job) error {
	ctx, span := e.obsv.Tracer.Start(ctx, "jobexecutor.QueueJob")
	defer span.End()

	ju.UpdateRequestContextJob(ctx, e.obsv, job)
	jobQueuingEnabled := e.ffClient.IsEnabledForUserOrRepoOrOwner(ctx, featureflags.CopilotSWEAgentAqueductJobQueuing, job.ActorID, job.RepoID, job.OwnerID)

	sessionClient, err := e.createSessionClient(ctx, job.ActorID, job.RepoID)
	if err != nil {
		return fmt.Errorf("failed to create session client: %w", err)
	}

	sessionState := capi.SessionStateQueued
	if !jobQueuingEnabled {
		sessionState = capi.SessionStateInProgress
	}

	_, err = e.createSession(ctx, sessionClient, job, sessionState)
	if err != nil {
		return fmt.Errorf("failed to create session: %w", err)
	}

	// Premium quota checking is deferred to when the job is processed and agent launched

	if !jobQueuingEnabled {
		e.obsv.LoggerWithTelemetry(ctx).Info("job queuing is disabled, executing job immediately")

		if gh == nil {
			gh, err = e.ghFactory.NewClientFromRepo(ctx, job.RepoID, job.OwnerID)
			if err != nil {
				return fmt.Errorf("failed to create GitHub client: %w", err)
			}
		}

		if err := e.ExecuteJob(ctx, gh, sessionClient, job); err != nil {
			return fmt.Errorf("failed to execute job immediately: %w", err)
		}

		return nil
	}

	e.obsv.LoggerWithTelemetry(ctx).Info("queuing job")

	queueStatus := jobs.JobStatusQueued
	queueTime := time.Now().Unix()
	e.obsv.LoggerWithTelemetry(ctx).Info("patching job with queue info before queuing")
	if err := e.jobsStore.PatchJob(ctx, job, jobs.JobPatch{
		Status:   &queueStatus,
		QueuedAt: &queueTime,
	}); err != nil {
		return fmt.Errorf("failed to patch job with queue info before queuing: %w", err)
	}

	queueID, err := e.aqueductClient.QueueJob(ctx, job)
	if err != nil {
		e.obsv.LoggerWithTelemetry(ctx).WithError(err).Error("failed to queue job")
		if innerErr := e.finalizeUnexecutedJob(ctx, sessionClient, job, jobs.JobStatusFailed, "Failed to queue job"); innerErr != nil {
			e.obsv.LogAndReportError(ctx, innerErr, "failed to finalize unexecuted job after queue failure")
		}
		return fmt.Errorf("failed to queue job: %w", err)
	}

	// NOTE for future: try to minimize updates to jobs after this point, as it may incur race condition
	// with jobs being processed in other threads.

	job.Status = queueStatus
	ju.UpdateRequestContextJob(ctx, e.obsv, job)
	ju.UpdateRequestContextJobQueue(ctx, e.obsv, queueID)

	e.obsv.LoggerWithTelemetry(ctx).Info("finished queuing job")
	return nil
}

func (e *AgentJobExecutor) RequeueJob(ctx context.Context, jobToRequeue *aqueductJobs.AgentJob, requeueOptions *RequeueOptions) error {
	ctx, span := e.obsv.Tracer.Start(ctx, "jobexecutor.RequeueJob")
	defer span.End()

	repoID := jobToRequeue.GetRepositoryId()
	ownerID := jobToRequeue.GetOwnerId()
	jobID := jobToRequeue.GetJobId()
	job, err := e.jobsStore.GetJob(ctx, ownerID, repoID, jobID)
	if err != nil {
		return joberrors.MarkExistingError(err, "failed to get job before re-queuing")
	}
	ju.UpdateRequestContextJob(ctx, e.obsv, job)

	e.obsv.LoggerWithTelemetry(ctx).Info("re-queuing job")

	requeueIntervalSeconds := 0
	if requeueOptions != nil {
		requeueIntervalSeconds = requeueOptions.IntervalSeconds
	}
	queueID, err := e.aqueductClient.QueueJobRaw(ctx, jobToRequeue, func(sr *aqueductApi.SendRequest) {
		sr.DeliverAt = timestamppb.New(time.Now().Add(time.Duration(requeueIntervalSeconds) * time.Second))
	})

	if err != nil {
		e.obsv.LoggerWithTelemetry(ctx).WithError(err).Error("failed to queue job to aqueduct")
		sessionClient, err := e.createSessionClient(ctx, job.ActorID, job.RepoID)
		if err != nil {
			return fmt.Errorf("failed to create session client: %w", err)
		}
		if innerErr := e.finalizeUnexecutedJob(ctx, sessionClient, job, jobs.JobStatusFailed, "Failed to re-queue job"); innerErr != nil {
			e.obsv.LogAndReportError(ctx, innerErr, "failed to finalize unexecuted job after re-queue failure")
		}
		return fmt.Errorf("failed to re-queue job: %w", err)
	}

	ju.UpdateRequestContextJobQueue(ctx, e.obsv, queueID)
	ju.UpdateRequestContextJob(ctx, e.obsv, job)
	e.obsv.LoggerWithTelemetry(ctx).Info("finished re-queuing job")
	return nil
}

// ProcessJob processes a queued job from the aqueduct queue.
func (e *AgentJobExecutor) ProcessJob(ctx context.Context, queueJob *aqueductJobs.AgentJob) error {
	ctx, span := e.obsv.Tracer.Start(ctx, "jobexecutor.ProcessJob")
	defer span.End()

	e.obsv.LoggerWithTelemetry(ctx).Info("processing queued job")

	repoID := queueJob.GetRepositoryId()
	ownerID := queueJob.GetOwnerId()
	assignmentID := queueJob.GetAssignmentId()
	jobID := queueJob.GetJobId()

	if ownerID == 0 || repoID == 0 || assignmentID == "" || jobID == "" {
		return fmt.Errorf("invalid agent job: missing owner_id, repository_id, assignment_id, or job_id")
	}

	prID := queueJob.GetPullRequestId()
	prNumber := queueJob.GetPullRequestNumber()
	if prID == 0 || prNumber == 0 {
		return fmt.Errorf("invalid agent job: missing pull_request_id or pull_request_number")
	}

	job, err := e.jobsStore.GetJob(ctx, ownerID, repoID, jobID)
	if err != nil {
		return joberrors.MarkExistingError(err, "failed to get job")
	}
	ju.UpdateRequestContextJob(ctx, e.obsv, job)

	if !job.IsQueued() {
		// if the job is no longer queued, we can assume it was already processed,
		// cancelled, etc. and skip processing the job.
		e.obsv.LoggerWithTelemetry(ctx).Info("job is no longer queued, skipping processing")
		return nil
	}

	gh, err := e.ghFactory.NewClientFromRepo(ctx, repoID, ownerID)
	if err != nil {
		return fmt.Errorf("failed to create GitHub client: %w", err)
	}

	// Only process events where the actor has write access to the repo
	permission, err := gh.GetUserPermissionsForRepositoryByID(ctx, repoID, job.ActorLogin)
	if err != nil {
		return fmt.Errorf("getting user permissions for repository: %w", err)
	}
	if !permission.Push {
		e.obsv.LoggerWithTelemetry(ctx).Info("ignoring job because actor does not have write access to the repo")
		return nil
	}

	assignment, err := e.jobsStore.GetAssignment(ctx, ownerID, repoID, assignmentID)
	if err != nil {
		return joberrors.MarkExistingError(err, "failed to get assignment")
	}

	allJobs, _, err := ju.JobsForAssignment(ctx, e.obsv, e.ffClient, gh, e.capiClient, e.ghTwirp, e.jobsStore, assignment)
	if err != nil {
		return fmt.Errorf("failed to get jobs for assignment: %w", err)
	}

	if has, runningJobID := jobs.HasRunningJobs(allJobs); has {
		// We should not have more than one running job at a time for a given assignment
		e.obsv.LoggerWithTelemetry(ctx).Error("not launching agent because there is another running job",
			kvp.String("running_job_id", runningJobID))
		return nil
	}

	// Check if the PR is still open and assigned to the copilot
	pr, err := gh.GetPullRequest(ctx, repoID, int(prNumber))
	if err != nil {
		return fmt.Errorf("failed to get pull request: %w", err)
	}

	sc, err := e.createSessionClient(ctx, job.ActorID, job.RepoID)
	if err != nil {
		return fmt.Errorf("failed to create session client: %w", err)
	}

	if pr.GetState() != "open" {
		e.obsv.LoggerWithTelemetry(ctx).Info("pull request is not open, finalizing job")
		if err := e.finalizeUnexecutedJob(ctx, sc, job, jobs.JobStatusCancelled, "Pull request is not open"); err != nil {
			return fmt.Errorf("failed to finalize unexecuted job: %w", err)
		}
		return nil
	}

	if !slices.ContainsFunc(pr.Assignees, func(a *gg.User) bool { return gh.UserIsCopilot(a.GetLogin()) }) {
		e.obsv.LoggerWithTelemetry(ctx).Info("pull request is not assigned to Copilot, finalizing job")
		if err := e.finalizeUnexecutedJob(ctx, sc, job, jobs.JobStatusCancelled, "Pull request is not assigned to Copilot"); err != nil {
			return fmt.Errorf("failed to finalize unexecuted job: %w", err)
		}
		return nil
	}

	e.obsv.LoggerWithTelemetry(ctx).Info("executing job from queue")
	if err := e.ExecuteJob(ctx, gh, sc, job); err != nil {
		return fmt.Errorf("failed to execute job from queue: %w", err)
	}

	e.obsv.LoggerWithTelemetry(ctx).Info("finished processing queued job")
	return nil
}

func (e *AgentJobExecutor) finalizeUnexecutedJob(ctx context.Context, sessionClient capi.SessionClient, job *jobs.Job, status jobs.JobStatus, errorMessage string) error {
	e.obsv.LoggerWithTelemetry(ctx).Info("finalizing unexecuted job")
	completedAt := time.Now().Unix()

	if err := ju.FinalizeJob(ctx, e.obsv, e.ffClient, e.jobsStore, job, status, completedAt); err != nil {
		return fmt.Errorf("failed to finalize job: %w", err)
	}

	if job.SessionID != "" {
		e.obsv.LoggerWithTelemetry(ctx).Info("finalizing session for unexecuted job")
	}

	if err := e.finalizeUnexecutedSession(ctx, sessionClient, job, errorMessage); err != nil {
		e.obsv.LogAndReportError(ctx, err, "failed to finalize session")
	}

	return nil
}

func (e *AgentJobExecutor) ExecuteJob(ctx context.Context, gh github.ClientInterface, sessionClient capi.SessionClient, job *jobs.Job) error {
	ctx, span := e.obsv.Tracer.Start(ctx, "jobexecutor.ExecuteJob")
	defer span.End()

	ju.UpdateRequestContextJob(ctx, e.obsv, job)
	useSessions := e.ffClient.IsEnabledForUser(ctx, capi.CopilotAPIAgentSessions, job.ActorID)

	e.obsv.LoggerWithTelemetry(ctx).Info("executing job")

	launchOpts := launcher.LaunchAgentOpts{
		JobID:              job.ID,
		Action:             string(job.Action),
		ActorLogin:         job.ActorLogin,
		ActorID:            job.ActorID,
		BranchName:         job.BranchName,
		Model:              job.Model,
		GitHubBaseCommit:   job.BaseCommit,
		GitHubRepo:         job.RepoOwner + "/" + job.RepoName,
		GitHubRepoName:     job.RepoName,
		GitHubRepoID:       job.RepoID,
		GitHubRepoOwner:    job.RepoOwner,
		GitHubRepoOwnerID:  job.OwnerID,
		IssueNumber:        job.IssueNumber,
		PRNumber:           job.PullRequestNumber,
		ProblemStatement:   substituteOnboardingTipIfEnabled(ctx, e.ffClient, job, job.ProblemStatement.Content),
		ContentFilterMode:  string(job.ProblemStatement.ContentFilterMode),
		RunName:            job.RunOptions.RunName,
		ResponseBodySuffix: job.RunOptions.ResponseBodySuffix,
		Push:               true, // this is always true for agent jobs
		CommitCount:        job.ProblemStatement.CommitCount,
		SessionID:          job.SessionID,
		GitHubToken:        gh.Token(),
		UseSessions:        useSessions,
	}

	// If the session client is nil, we don't need to use sessions
	useSessions = useSessions && sessionClient != nil

	// Check if the user has sufficient premium quota before launching the agent
	hasSufficientQuota, totalPremiumQuota, err := e.GetPremiumQuotaStatus(ctx, job.OwnerID, job.RepoID, job.ActorID)
	if err != nil {
		return fmt.Errorf("failed to confirm quota availability: %w", err)
	}
	if !hasSufficientQuota {
		if useSessions {
			quotaMessage := e.insufficientQuotaMessage(totalPremiumQuota)

			e.obsv.LoggerWithTelemetry(ctx).Info("user does not have sufficient quota, updating session to failed")
			if err := e.failSession(ctx, sessionClient, job, joberrors.ErrorCAPIPaymentRequired, quotaMessage); err != nil {
				return fmt.Errorf("failed to update session: %w", err)
			}
		}

		if err := gh.RequestReviewers(ctx, job.RepoOwner, job.RepoName, job.PullRequestNumber, []string{job.ActorLogin}); err != nil {
			// Log and report the error but don't return it so we don't add
			// an additional error comment to the pull request.
			e.obsv.LogAndReportError(ctx, err, "failed to request reviewers")
		}

		// We've "handled" the job so we don't need to return an error here.
		// We'll also leave the job as pending (with no session ID) in the
		// database so that if a future attempt to launch the agent succeeds,
		// it will pick up any unhandled comments/reviews.
		return nil
	}

	e.obsv.LoggerWithTelemetry(ctx).Info("generating nonce for job")
	nonce, err := e.jobsStore.RegenerateJobNonce(ctx, job)
	if err != nil {
		return fmt.Errorf("failed to regenerate job nonce: %w", err)
	}
	launchOpts.Nonce = nonce

	e.obsv.LoggerWithTelemetry(ctx).Info("launching agent")

	launchTime := time.Now().Unix()
	computeID, err := e.launcher.LaunchAgent(ctx, launchOpts)
	if err != nil {
		return err
	}

	job.Args = launchOpts
	job.ComputeID = computeID
	job.Status = jobs.JobStatusRunning
	job.Launcher = e.launcher.LauncherType()
	job.LaunchedAt = launchTime

	// update the job in the database
	job, err = e.jobsStore.UpdateJob(ctx, job)
	if err != nil {
		return fmt.Errorf("failed to update job: %w", err)
	}
	ju.UpdateRequestContextJob(ctx, e.obsv, job)

	// publish job_started telemetry event to Hydro
	e.publishJobStartTelemetry(ctx, job)

	// update the session with the workflow run id
	if useSessions && sessionClient != nil {
		runID, err := strconv.ParseUint(computeID, 10, 64)
		if err != nil {
			return fmt.Errorf("failed to parse compute ID: %w", err)
		}
		if err := e.startSession(ctx, sessionClient, job, runID); err != nil {
			return fmt.Errorf("failed to update session: %w", err)
		}
	}

	e.obsv.LoggerWithTelemetry(ctx).Info("finished executing job")

	return nil
}

type CancelJobOpts struct {
	OwnerID   int64
	RepoID    int64
	JobID     string                 // Either JobID or SessionID are required, but not both.
	SessionID string                 // Either JobID or SessionID are required, but not both.
	Client    github.ClientInterface // Optional, used to cancel the job if it is running.
}

func (e *AgentJobExecutor) CancelJob(ctx context.Context, opts *CancelJobOpts) error {
	ctx, span := e.obsv.Tracer.Start(ctx, "jobexecutor.CancelJob")
	defer span.End()

	e.obsv.LoggerWithTelemetry(ctx).Info("cancelling job")

	var err error
	var job *jobs.Job

	e.obsv.LoggerWithTelemetry(ctx).Info("getting job to cancel")
	if opts.JobID != "" {
		job, err = e.jobsStore.GetJob(ctx, opts.OwnerID, opts.RepoID, opts.JobID)
	} else {
		job, err = e.jobsStore.GetJobForSessionID(ctx, opts.OwnerID, opts.RepoID, opts.SessionID)
	}
	if err != nil {
		return joberrors.MarkExistingError(err, "failed to get job")
	}

	if job == nil {
		return mark.With(mark.ErrNotFound, fmt.Errorf("job not found"))
	}

	ju.UpdateRequestContextJob(ctx, e.obsv, job)

	switch {
	case job.IsTerminal():
		// If the job is already terminal, we can't cancel it.
		e.obsv.LoggerWithTelemetry(ctx).Info("job is already terminal, cannot cancel")
		return mark.With(mark.ErrAlreadyExists, fmt.Errorf("cannot cancel a job that is %s", job.Status))

	case job.IsRunning():
		// If the job is running, we need to cancel the workflow run
		e.obsv.LoggerWithTelemetry(ctx).Info("job is running, cancelling job")
		if err := e.launcher.TerminateAgent(ctx, launcher.TerminateAgentOpts{
			OwnerID:   job.OwnerID,
			RepoID:    job.RepoID,
			RepoName:  job.RepoName,
			RepoOwner: job.RepoOwner,
			ComputeID: job.ComputeID,
			Client:    opts.Client,
		}); err != nil {
			return joberrors.MarkExistingError(err, "failed to cancel running job")
		}

		// NOTE: The actual job status will be updated based on the job event coming
		// 		 back from actions, which also ensures any pending jobs that remain
		// 		 in the database will pick up as expected (via FinalizeJob).

		// TODO (colbylwilliams):
		// There will be a delay between the job being cancelled and the workflow
		// job event coming back from actions where the job status is updated.
		//
		// We should consider adding transitional statuses like "cancelling" to
		// the job to indicate that the job is in the process of being cancelled.
		//
		// 1. This allows us to handle additional events (like another cancel request)
		//    gracefully while the job is in the process of being cancelled.
		// 2. It provides an opportunity for better visibility into the job's state
		//    for users (e.g. showing a "Cancelling..." status in the UI).

		return nil

	case job.IsPending() || job.IsQueued():
		// If the job is pending or queued, we haven't launched the agent yet, so we can
		// just cancel the job in the database and finalize the session if it exists.
		completedAt := time.Now().Unix()

		e.obsv.LoggerWithTelemetry(ctx).Info("job is pending or queued, cancelling job")
		if err := ju.FinalizeJob(ctx, e.obsv, e.ffClient, e.jobsStore, job, jobs.JobStatusCancelled, completedAt); err != nil {
			return fmt.Errorf("failed to finalize job: %w", err)
		}

		if job.SessionID != "" {
			e.obsv.LoggerWithTelemetry(ctx).Info("job has session ID, finalizing session")

			sessionClient, err := e.createSessionClient(ctx, job.ActorID, job.RepoID)
			if err != nil {
				return fmt.Errorf("failed to create session client: %w", err)
			}

			if err := e.finalizeUnexecutedSession(ctx, sessionClient, job, "Queued job is cancelled"); err != nil {
				return fmt.Errorf("failed to finalize session: %w", err)
			}
		}

		return nil

	default:
		// This should never happen, but just in case we have an unknown job status.
		e.obsv.LogAndReportError(ctx, fmt.Errorf("unknown job status: %s", job.Status), "failed to cancel job")
		return fmt.Errorf("unknown job status: %s", job.Status)
	}
}

// A magic string that is used to deep link users to an issue that onboards
// their repository with Copilot coding agent. This mechanism serves a few
// purposes:
// 1) it keeps our onboarding prompt private from competitors.
// 2) it allows us to change the onboarding prompt even for stale onboarding PRs that have been open a while.
// 3) it prevents users from interfering with our tuned onboarding prompt by adding their own comments.
const OnboardingTipMagicString string = "<Onboard this repo>"

func substituteOnboardingTipIfEnabled(ctx context.Context, ffClient featureflags.Client, job *jobs.Job, onboardingTip string) string {
	if !ffClient.IsEnabledForUserOrRepoOrOwner(ctx, "copilot_swe_agent_tips_onboarding", job.ActorID, job.RepoID, job.OwnerID) ||
		!strings.Contains(onboardingTip, OnboardingTipMagicString) {
		return onboardingTip
	}

	// Always substitute the entire magic string with the onboarding prompt.
	// This is intentional in case the user decided to add additional comments.
	// While good intentioned, we'll be running our prompt through evals.
	return github.OnboardingPrompt
}
