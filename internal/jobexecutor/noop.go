package jobexecutor

import (
	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/launcher"
	"github.com/github/sweagentd/internal/observability"
)

type NoopJobExecutor struct {
	AgentJobExecutor
	Launcher *launcher.NoopLauncher
}

var _ JobExecutor = (*NoopJobExecutor)(nil)

func NewNoopJobExecutor(obsv observability.Exporters, jobStore jobs.JobsStore) *NoopJobExecutor {
	launcher := launcher.NewNoopLauncher(obsv)
	return &NoopJobExecutor{
		Launcher: launcher,
		AgentJobExecutor: AgentJobExecutor{
			obsv:      obsv,
			jobsStore: jobStore,
			launcher:  launcher,
		},
	}
}

func (n *NoopJobExecutor) LaunchAgentCalls() []launcher.LaunchAgentOpts {
	return n.Launcher.LaunchAgentCalls
}

func (n *NoopJobExecutor) TerminateAgentCalls() []launcher.TerminateAgentOpts {
	return n.Launcher.TerminateAgentCalls
}

func (n *NoopJobExecutor) TerminateAgentError() error {
	return n.Launcher.TerminateAgentError
}
