package jobexecutor

import (
	"context"
	_ "embed"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	copilotLimitersTwirp "github.com/github/copilot-twirp/proto/limiter/v1"
	copilotUsersTwirp "github.com/github/copilot-twirp/proto/users/v1"
	"github.com/github/go-stats"
	aqueductJobs "github.com/github/sweagentd/internal/aqueduct/jobs"
	"github.com/github/sweagentd/internal/capi"
	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/githubtwirp"
	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/observability"
	gg "github.com/google/go-github/v72/github"
	"github.com/joho/godotenv"
	"github.com/openai/openai-go"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestJobExecutor_GenerateSessionName(t *testing.T) {
	t.Run("should generate session name for initial PR", func(t *testing.T) {
		job := &jobs.Job{
			ActorLogin: "timrogers",
			Action:     jobs.AgentActionFix,
		}
		sessionName := generateSessionName(job)
		assert.Equal(t, "Initial implementation", sessionName)
	})

	t.Run("should generate session name for PR follow-up sessions", func(t *testing.T) {
		job := &jobs.Job{
			ActorLogin: "timrogers",
			Action:     jobs.AgentActionFixPRComment,
		}
		sessionName := generateSessionName(job)
		assert.Equal(t, "Review from @timrogers", sessionName)
	})
}

func TestJobExecutor_ShouldWake(t *testing.T) {
	obsv := observability.NewNoopExporters()
	integrationId := "integration-id"
	cf := &github.NoopClientFactory{}
	ffClient := featureflags.NewNoopClient(map[string]bool{
		featureflags.CopilotSWEAgentOnlyWakeOnAtCopilot: false,
		featureflags.CopilotSWEAgentAqueductJobQueuing:  true,
	})

	t.Run("tool call with wake:true", func(t *testing.T) {
		b := &openai.ChatCompletion{
			ID: "noop",
			Choices: []openai.ChatCompletionChoice{
				{
					Message: openai.ChatCompletionMessage{
						ToolCalls: []openai.ChatCompletionMessageToolCall{
							{
								Type: "function",
								Function: openai.ChatCompletionMessageToolCallFunction{
									Name:      "should_wake",
									Arguments: `{"wake":true}`,
								},
							},
						},
					},
				},
			},
		}
		for _, onlyWakeOnAtCopilotFF := range []bool{true, false} {
			t.Run(fmt.Sprintf("With AI wake disabled: %v", onlyWakeOnAtCopilotFF), func(t *testing.T) {
				ffAlt := featureflags.NewNoopClient(map[string]bool{
					featureflags.CopilotSWEAgentOnlyWakeOnAtCopilot: onlyWakeOnAtCopilotFF,
				})
				svr := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
					w.Header().Add("Content-Type", "application/json")
					err := json.NewEncoder(w).Encode(b)
					require.NoError(t, err)
				}))
				defer svr.Close()

				capiClient := capi.NewClient(obsv, svr.URL, integrationId, "hmac-secret")

				je := NewTestJobExecutor(obsv, ffAlt, jobs.NewMemoryStore(), cf, githubtwirp.NewNoopClient(), capiClient)
				shouldWake, err := je.ShouldWake(context.Background(), 1, "test", "interaction-id")
				require.NoError(t, err)
				require.Equal(t, shouldWake, !onlyWakeOnAtCopilotFF)
			})
		}
	})

	t.Run("retries for rate-limits", func(t *testing.T) {
		reqs := 0
		svr := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			reqs++
			w.Header().Add("Content-Type", "application/json")

			if reqs > 1 {
				// return a valid response on the second request
				w.Header().Add("Content-Type", "application/json")
				b := &openai.ChatCompletion{
					ID: "noop",
					Choices: []openai.ChatCompletionChoice{
						{
							Message: openai.ChatCompletionMessage{
								ToolCalls: []openai.ChatCompletionMessageToolCall{
									{
										Type: "function",
										Function: openai.ChatCompletionMessageToolCallFunction{
											Name:      "should_wake",
											Arguments: `{"wake":true}`,
										},
									},
								},
							},
						},
					},
				}
				err := json.NewEncoder(w).Encode(b)
				require.NoError(t, err)
				return
			}

			// return a rate limit error on the first request
			w.Header().Add("Retry-After", "5")
			w.WriteHeader(http.StatusTooManyRequests)
		}))
		defer svr.Close()

		capiClient := capi.NewClient(obsv, svr.URL, integrationId, "hmac-secret")
		je := NewTestJobExecutor(obsv, ffClient, jobs.NewMemoryStore(), cf, githubtwirp.NewNoopClient(), capiClient)

		now := time.Now()
		shouldWake, err := je.ShouldWake(context.Background(), 1, "test", "interaction-id")
		require.NoError(t, err)
		require.Greater(t, time.Since(now), 3*time.Second, "should have waited for rate limit")
		require.Greater(t, reqs, 1, "should have retried at least once")
		require.True(t, shouldWake)
	})

	t.Run("returns an error for rate-limit errors with no Retry-After header", func(t *testing.T) {
		svr := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.Header().Add("Content-Type", "application/json")
			w.WriteHeader(http.StatusTooManyRequests)
		}))
		defer svr.Close()

		capiClient := capi.NewClient(obsv, svr.URL, integrationId, "hmac-secret")
		je := NewTestJobExecutor(obsv, ffClient, jobs.NewMemoryStore(), cf, githubtwirp.NewNoopClient(), capiClient)

		_, err := je.ShouldWake(context.Background(), 1, "test", "interaction-id")
		require.Error(t, err)
		require.Equal(t, "retry-after header not set", err.Error())
	})
}

func TestJobExecutor_ShouldWake_Eval(t *testing.T) {
	_ = godotenv.Load("../../.env")

	if os.Getenv("COPILOT_INTEGRATION_ID") == "" {
		t.Skip("Skipping test because COPILOT_INTEGRATION_ID is not set")
	}

	if os.Getenv("CAPI_HMAC_KEY") == "" {
		t.Skip("Skipping test because CAPI_HMAC_KEY is not set")
	}

	obsv := observability.NewNoopExporters()
	ffClient := featureflags.NewNoopClient(nil)
	cf := &github.NoopClientFactory{}
	var cloudColonelUserId int64 = 39814168

	capiClient := capi.NewClient(obsv, "https://api.githubcopilot.com", os.Getenv("COPILOT_INTEGRATION_ID"), os.Getenv("CAPI_HMAC_KEY"))

	je := NewTestJobExecutor(obsv, ffClient, jobs.NewMemoryStore(), cf, githubtwirp.NewNoopClient(), capiClient)

	t.Run("returns false", func(t *testing.T) {
		files, err := os.ReadDir("testdata/evals/should-wake-false")
		require.NoError(t, err)

		// run each test 3 times to get more coverage
		files = append(files, files...)
		files = append(files, files...)

		for _, file := range files {
			t.Run(file.Name(), func(t *testing.T) {
				problemStatement, err := os.ReadFile("testdata/evals/should-wake-false/" + file.Name())
				require.NoError(t, err)
				t.Logf("problem statement: %s", string(problemStatement))
				shouldWake, err := je.ShouldWake(context.Background(), cloudColonelUserId, string(problemStatement), "")
				require.NoError(t, err)
				require.False(t, shouldWake)
			})
		}
	})
	t.Run("returns true", func(t *testing.T) {
		files, err := os.ReadDir("testdata/evals/should-wake-true")
		require.NoError(t, err)

		// run each test 3 times to get more coverage
		files = append(files, files...)
		files = append(files, files...)

		for _, file := range files {
			t.Run(file.Name(), func(t *testing.T) {
				problemStatement, err := os.ReadFile("testdata/evals/should-wake-true/" + file.Name())
				require.NoError(t, err)
				t.Logf("problem statement: %s", string(problemStatement))
				shouldWake, err := je.ShouldWake(context.Background(), cloudColonelUserId, string(problemStatement), "")
				require.NoError(t, err)
				require.True(t, shouldWake)
			})
		}
	})
}

func TestGetPremiumQuotaStatus(t *testing.T) {
	tests := []struct {
		name               string
		featureFlagEnabled bool
		remainingQuota     int32
		totalQuota         int32
		overagePermitted   bool
		ghError            error
		expectedHasQuota   bool
		expectedQuota      int32
		expectError        bool
	}{
		{
			name:               "Feature flag disabled",
			featureFlagEnabled: false,
			expectedHasQuota:   true,
			expectedQuota:      0,
			expectError:        false,
		},
		{
			name:               "Sufficient quota",
			featureFlagEnabled: true,
			remainingQuota:     5,
			totalQuota:         10,
			overagePermitted:   false,
			ghError:            nil,
			expectedHasQuota:   true,
			expectedQuota:      10,
			expectError:        false,
		},
		{
			name:               "Zero quota but overage permitted",
			featureFlagEnabled: true,
			remainingQuota:     0,
			totalQuota:         10,
			overagePermitted:   true,
			ghError:            nil,
			expectedHasQuota:   true,
			expectedQuota:      10,
			expectError:        false,
		},
		{
			name:               "No quota and no overage",
			featureFlagEnabled: true,
			remainingQuota:     0,
			totalQuota:         10,
			overagePermitted:   false,
			ghError:            nil,
			expectedHasQuota:   false,
			expectedQuota:      10,
			expectError:        false,
		},
		{
			name:               "GitHub API error",
			featureFlagEnabled: true,
			remainingQuota:     0,
			totalQuota:         0,
			overagePermitted:   false,
			ghError:            errors.New("github API error"),
			expectedHasQuota:   false,
			expectedQuota:      0,
			expectError:        true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ffClient := featureflags.NewNoopClient(map[string]bool{
				featureflags.CopilotSweAgentPremQuotaCheckFlagName: tt.featureFlagEnabled,
			})

			ghTwirp := &githubtwirp.NoopClient{
				NoopUserDetailAPI: &githubtwirp.NoopUserDetailAPI{
					MockGetCopilotUser: func(ctx context.Context, req *copilotUsersTwirp.GetCopilotUserRequest) (*copilotUsersTwirp.GetCopilotUserResponse, error) {
						return &copilotUsersTwirp.GetCopilotUserResponse{
							UserDetails: &copilotUsersTwirp.CopilotUserDetails{
								AnalyticsTrackingId: "test-tracking-id",
							},
						}, tt.ghError
					},
				},
				NoopLimiterAPI: &githubtwirp.NoopLimiterAPI{
					MockGetQuotaRemaining: func(ctx context.Context, req *copilotLimitersTwirp.GetQuotaRemainingRequest) (*copilotLimitersTwirp.GetQuotaRemainingResponse, error) {
						return &copilotLimitersTwirp.GetQuotaRemainingResponse{
							QuotaDetails: []*copilotLimitersTwirp.QuotaDetails{
								{
									QuotaType:        copilotLimitersTwirp.QuotaType_QUOTA_TYPE_PREMIUM_INTERACTIONS,
									Remaining:        tt.remainingQuota,
									Entitlement:      tt.totalQuota,
									OveragePermitted: tt.overagePermitted,
								},
							},
						}, nil
					},
				},
			}

			executor := &AgentJobExecutor{
				ffClient: ffClient,
				ghTwirp:  ghTwirp,
				obsv:     observability.NewNoopExporters(),
			}
			hasQuota, quota, err := executor.GetPremiumQuotaStatus(context.Background(), int64(123), int64(456), int64(789))

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			assert.Equal(t, tt.expectedHasQuota, hasQuota)
			assert.Equal(t, tt.expectedQuota, quota)
		})
	}
}

func TestGetPremiumQuotaStatus_EmitsPremiumQuotaExceededMetric(t *testing.T) {
	// Setup feature flag enabled for quota check
	ffClient := featureflags.NewNoopClient(map[string]bool{
		featureflags.CopilotSweAgentPremQuotaCheckFlagName: true,
	})

	// Mock GitHub Twirp client to return zero remaining quota and no overage
	ghTwirp := &githubtwirp.NoopClient{
		NoopUserDetailAPI: &githubtwirp.NoopUserDetailAPI{
			MockGetCopilotUser: func(ctx context.Context, req *copilotUsersTwirp.GetCopilotUserRequest) (*copilotUsersTwirp.GetCopilotUserResponse, error) {
				return &copilotUsersTwirp.GetCopilotUserResponse{
					UserDetails: &copilotUsersTwirp.CopilotUserDetails{AnalyticsTrackingId: "test-tracking-id"},
				}, nil
			},
		},
		NoopLimiterAPI: &githubtwirp.NoopLimiterAPI{
			MockGetQuotaRemaining: func(ctx context.Context, req *copilotLimitersTwirp.GetQuotaRemainingRequest) (*copilotLimitersTwirp.GetQuotaRemainingResponse, error) {
				return &copilotLimitersTwirp.GetQuotaRemainingResponse{
					QuotaDetails: []*copilotLimitersTwirp.QuotaDetails{{
						QuotaType:        copilotLimitersTwirp.QuotaType_QUOTA_TYPE_PREMIUM_INTERACTIONS,
						Remaining:        0,
						Entitlement:      10,
						OveragePermitted: false,
					}},
				}, nil
			},
		},
	}

	// Capture metric via MockStatter
	var recordedKey string
	var recordedTags stats.Tags
	var recordedValue int64
	mockStatter := &observability.MockStatter{
		MockCounter: func(key string, tags stats.Tags, value int64) {
			recordedKey = key
			recordedTags = tags
			recordedValue = value
		},
	}
	obsv := observability.NewNoopExporters()
	obsv.Statter = mockStatter

	executor := &AgentJobExecutor{
		ffClient: ffClient,
		ghTwirp:  ghTwirp,
		obsv:     obsv,
	}

	hasQuota, quota, err := executor.GetPremiumQuotaStatus(context.Background(), 1, 2, 3)
	require.NoError(t, err)
	assert.False(t, hasQuota)
	assert.Equal(t, int32(10), quota)
	assert.Equal(t, "events.premium_quota_exceeded", recordedKey)
	assert.Equal(t, int64(1), recordedValue)
	assert.Len(t, recordedTags, 0)
}

func TestCancelJob(t *testing.T) {
	ctx := context.Background()
	obsv := observability.NewNoopExporters()
	jobStore := jobs.NewMemoryStore()

	testJob, _ := jobStore.CreateJob(ctx, &jobs.Job{
		ID:        "job-1234",
		SessionID: "session-1234",
		RepoOwner: "oregon",
		RepoName:  "trail",
		OwnerID:   1,
		RepoID:    2,
		ComputeID: "test-compute-id",
		Status:    jobs.JobStatusRunning,
	})

	t.Run("using Job ID", func(t *testing.T) {
		je := NewNoopJobExecutor(obsv, jobStore)
		err := je.CancelJob(ctx, &CancelJobOpts{
			OwnerID:   testJob.OwnerID,
			RepoID:    testJob.RepoID,
			SessionID: testJob.SessionID,
		})
		require.NoError(t, err)
		assert.Equal(t, 1, len(je.TerminateAgentCalls()))
		terminateCallOpts := je.TerminateAgentCalls()[0]
		assert.Equal(t, testJob.RepoID, terminateCallOpts.RepoID)
		assert.Equal(t, testJob.OwnerID, terminateCallOpts.OwnerID)
		assert.Equal(t, testJob.RepoName, terminateCallOpts.RepoName)
		assert.Equal(t, testJob.RepoOwner, terminateCallOpts.RepoOwner)
		assert.Equal(t, testJob.ComputeID, terminateCallOpts.ComputeID)
	})
	t.Run("using session ID", func(t *testing.T) {
		je := NewNoopJobExecutor(obsv, jobStore)
		err := je.CancelJob(ctx, &CancelJobOpts{
			OwnerID:   testJob.OwnerID,
			RepoID:    testJob.RepoID,
			SessionID: testJob.SessionID,
		})
		require.NoError(t, err)
		assert.Equal(t, 1, len(je.TerminateAgentCalls()))
		terminateCallOpts := je.TerminateAgentCalls()[0]
		assert.Equal(t, testJob.RepoID, terminateCallOpts.RepoID)
		assert.Equal(t, testJob.OwnerID, terminateCallOpts.OwnerID)
		assert.Equal(t, testJob.RepoName, terminateCallOpts.RepoName)
		assert.Equal(t, testJob.RepoOwner, terminateCallOpts.RepoOwner)
		assert.Equal(t, testJob.ComputeID, terminateCallOpts.ComputeID)
	})
	t.Run("errors on invalid errors", func(t *testing.T) {
		je := NewNoopJobExecutor(obsv, jobStore)
		err := je.CancelJob(ctx, &CancelJobOpts{
			OwnerID: testJob.OwnerID,
			RepoID:  testJob.RepoID,
			JobID:   "fail me yo",
		})
		require.Error(t, err)
	})
	t.Run("errors if terminate fails", func(t *testing.T) {
		je := NewNoopJobExecutor(obsv, jobStore)
		je.Launcher.TerminateAgentError = fmt.Errorf("you died of dysentery")
		err := je.CancelJob(ctx, &CancelJobOpts{
			OwnerID: testJob.OwnerID,
			RepoID:  testJob.RepoID,
			JobID:   testJob.ID,
		})
		require.Error(t, err)
	})
}

func TestAgentJobExecutor_ProcessJob(t *testing.T) {
	obsv := observability.NewNoopExporters()
	ffClient := featureflags.NewNoopClient(nil)
	jobStore := jobs.NewMemoryStore()
	ghFactory := &github.NoopClientFactory{}
	ghTwirp := githubtwirp.NewNoopClient()
	capiClient := capi.NewClient(obsv, "https://api.githubcopilot.com", "integration-id", "hmac-secret")

	je := NewTestJobExecutor(obsv, ffClient, jobStore, ghFactory, ghTwirp, capiClient)

	assignment := &jobs.Assignment{
		ID:      "assign-1",
		OwnerID: 1,
		RepoID:  2,
	}
	jobStore.CreateAssignment(context.Background(), assignment)

	baseJob := &jobs.Job{
		ID:                "job-1",
		OwnerID:           1,
		RepoID:            2,
		ActorID:           3,
		RepoOwner:         "owner",
		RepoName:          "repo",
		ActorLogin:        "actor",
		Status:            jobs.JobStatusQueued,
		AssignmentID:      "assign-1",
		PullRequestNumber: 123,
	}
	agentJob := &aqueductJobs.AgentJob{
		RepositoryId:      baseJob.RepoID,
		OwnerId:           baseJob.OwnerID,
		AssignmentId:      baseJob.AssignmentID,
		JobId:             baseJob.ID,
		PullRequestId:     1,
		PullRequestNumber: int32(baseJob.PullRequestNumber),
	}

	t.Run("does not launch agent if job is not queued", func(t *testing.T) {
		job := *baseJob
		job.Status = jobs.JobStatusRunning
		_, _ = jobStore.CreateJob(context.Background(), &job)
		err := je.ProcessJob(context.Background(), agentJob)
		require.NoError(t, err)
		require.Len(t, je.LaunchAgentCalls(), 0)
	})

	t.Run("does not launch agent if user does not have push access", func(t *testing.T) {
		ghFactory.MockGetUserPermissionsForRepositoryByID = func(ctx context.Context, repoID int64, login string) (*github.RepositoryPermissions, error) {
			return &github.RepositoryPermissions{Push: false}, nil
		}

		job := *baseJob
		job.Status = jobs.JobStatusQueued
		_, _ = jobStore.CreateJob(context.Background(), &job)
		err := je.ProcessJob(context.Background(), agentJob)
		require.NoError(t, err)
		require.Len(t, je.LaunchAgentCalls(), 0)
	})

	t.Run("does not launch agent if there are other running jobs for assignment", func(t *testing.T) {
		runningJob := *baseJob
		runningJob.ID = "job-2"
		runningJob.Status = jobs.JobStatusRunning
		runningJob.ComputeID = "123"
		_, _ = jobStore.CreateJob(context.Background(), &runningJob)

		job := *baseJob
		job.Status = jobs.JobStatusQueued
		_, _ = jobStore.CreateJob(context.Background(), &job)
		err := je.ProcessJob(context.Background(), agentJob)
		require.NoError(t, err)
		require.Len(t, je.LaunchAgentCalls(), 0)
	})

	t.Run("does not launch agent if pull request is not open", func(t *testing.T) {
		ghFactory.MockPullRequest = &gg.PullRequest{
			State:     gg.Ptr("closed"),
			Number:    gg.Ptr(123),
			Assignees: []*gg.User{{Login: gg.Ptr("copilot[bot]")}},
		}

		job := *baseJob
		job.Status = jobs.JobStatusQueued
		_, _ = jobStore.CreateJob(context.Background(), &job)
		err := je.ProcessJob(context.Background(), agentJob)
		require.NoError(t, err)
		require.Len(t, je.LaunchAgentCalls(), 0)
	})

	t.Run("does not launch agent if pull request is not assigned to Copilot", func(t *testing.T) {
		ghFactory.MockPullRequest = &gg.PullRequest{
			State:     gg.Ptr("closed"),
			Number:    gg.Ptr(123),
			Assignees: []*gg.User{{Login: gg.Ptr("someoneelse")}},
		}

		job := *baseJob
		job.Status = jobs.JobStatusQueued
		_, _ = jobStore.CreateJob(context.Background(), &job)
		err := je.ProcessJob(context.Background(), agentJob)
		require.NoError(t, err)
		require.Len(t, je.LaunchAgentCalls(), 0)
	})
}
