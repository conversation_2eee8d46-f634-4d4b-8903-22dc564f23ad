package jobexecutor

import (
	"context"
	_ "embed"
	"encoding/json"
	"fmt"
	"strconv"

	"github.com/github/go-stats"
	"github.com/github/sweagentd/internal/featureflags"
	"github.com/openai/openai-go"
)

// shouldWakeModel is the model used in the request to determine if the model should
// "wake up" and start processing the conversation.
const shouldWakeModel = "gpt-4o-mini"

// shouldWakeSystemMessage is the system message used in the request to determine if the agent
// "wakes up" and starts processing the conversation. It is embedded from a separate file because
// it is quite long and contains characters that would need to be escaped if it were a string
// literal (either standard or raw).
//
//go:embed should_wake_system_message.txt
var shouldWakeSystemMessage string

func (e *AgentJobExecutor) ShouldWake(ctx context.Context, userId int64, problemStatement, jobId string) (bool, error) {
	// If the feature flag is enabled for the user, we skip the AI-based wake check. Individual events
	// already have a direct check to see if "@copilot" is referenced, so we do not need to do that here
	if e.ffClient.IsEnabledForUser(ctx, featureflags.CopilotSWEAgentOnlyWakeOnAtCopilot, userId) {
		return false, nil
	}

	body := openai.ChatCompletionNewParams{
		Model: shouldWakeModel,
		Messages: []openai.ChatCompletionMessageParamUnion{
			openai.SystemMessage(shouldWakeSystemMessage),
			openai.UserMessage(problemStatement),
		},
		ToolChoice: openai.ChatCompletionToolChoiceOptionUnionParam{
			OfChatCompletionNamedToolChoice: &openai.ChatCompletionNamedToolChoiceParam{
				Function: openai.ChatCompletionNamedToolChoiceFunctionParam{
					Name: "wake_up",
				},
			},
		},
		Tools: []openai.ChatCompletionToolParam{
			{
				Function: openai.FunctionDefinitionParam{
					Name:        "wake_up",
					Description: openai.String("A function that determines if the model should \"wake up\" and start processing feedback from the conversation."),
					Parameters: openai.FunctionParameters{
						"type": "object",
						"properties": map[string]interface{}{
							"wake": map[string]string{
								"type": "boolean",
							},
						},
						"required": []string{"wake"},
					},
				},
			},
		},
	}

	logger := e.obsv.LoggerWithTelemetry(ctx)

	userIdStr := fmt.Sprintf("%d", userId)
	result, err := e.capiClient.ChatCompletions(ctx, userIdStr, jobId, body)
	if err != nil {
		logger.WithError(err).Error("failed to get chat completions for shouldWake")
		return false, err
	}

	if len(result.Choices) == 0 {
		logger.Error("no choices in chat completions for shouldWake")
		return false, err
	}

	if len(result.Choices[0].Message.ToolCalls) == 0 {
		logger.Error("no tool calls in chat completions for shouldWake")
		return false, err
	}

	var args map[string]interface{}
	err = json.Unmarshal([]byte(result.Choices[0].Message.ToolCalls[0].Function.Arguments), &args)
	if err != nil {
		logger.WithError(err).Error("failed to unmarshal function arguments for shouldWake")
		return false, err
	}

	// Parse the boolean value from the "wake" argument
	shouldWake := args["wake"].(bool)

	tags := e.obsv.TelemetryTags(ctx).Merge(stats.Tags{"should_wake": strconv.FormatBool(shouldWake)})
	e.obsv.Statter.Counter("events.should_wake", tags, 1)

	return shouldWake, nil
}
