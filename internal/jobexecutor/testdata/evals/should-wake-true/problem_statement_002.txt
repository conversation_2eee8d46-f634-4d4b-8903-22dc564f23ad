*This section includes details on the pull request with code changes you have already submitted to fix the problem above.*

<pr_title>
[WIP] Improve test coverage
</pr_title>

<pr_description>
- [x] Improve copilot-setup-steps.yml workflow by enabling bundler cache
- [x] Implement pending tests in ServiceCatalog::Service spec
  - [x] Add test for codeql enabled check
  - [x] Add tests for GitHub ownership handling
  - [x] Add tests for GitHub ownership assignment
- [ ] Create tests for API::VulnerabilityAggregator
  - [ ] Add fixtures for GraphQL responses
  - [ ] Add tests for report_findings method
  - [ ] Add tests for create_external_export method
  - [ ] Add tests for complete_external_export method
  - [ ] Add tests for exception_status method
  - [ ] Add tests for filter_github_issues method
  - [ ] Add tests for has_filterable_exception? method
  - [ ] Add tests for fetch_exception_status_with_retries method
- [ ] Create tests for GHASWorker
  - [ ] Add tests for run method
  - [ ] Add tests for process_services method
  - [ ] Add tests for retrieve_services method
  - [ ] Add tests for dedupe_services method
  - [ ] Add tests for filter_services method
- [ ] Create tests for worker subclasses
  - [ ] Add tests for CodeScanningWorker
  - [ ] Add tests for DependabotWorker
  - [ ] Add tests for SecretScanningWorker

Fixes #635.
</pr_description>

## Comments on the PR (you are @copilot in this section)

<comments>

<pr_reviews>

<comment_thread>
<comment_id>2047112728</comment_id>
<file>.github/workflows/copilot-setup-steps.yml:24</file>

<comment_new>
<author>@boveus</author>
I want you to install the `.gem` files using the files in `vendor/cache` as a source - I do not want to  have a different caching mechanism here that we will need to gitignore and deal with.  Can you explain your reasoning for this change?
</comment_new>

</comment_thread>

</pr_reviews>

<comments>

----
The last **2** git commits in this branch are the changes you have made so far. Use those as your change commit history.