----
*This section includes details on the pull request with code changes you have already submitted to fix the problem above.*

<pr_title>
Add multi-state garbage collection for runners older than 48 hours
</pr_title>

<pr_description>

Currently the garbage collection only cleans up runners in Deallocated state that are older than 6 hours. However, there is potential for runners to remain in other states indefinitely if they get stuck or abandoned, consuming resources in Redis.

This PR expands the garbage collection to:

1. Clean up runners in all possible states (Deallocated, ProvisioningRequested, ProvisioningStarted, ProvisioningCompleted, DeprovisioningRequested) that are older than 48 hours
2. For Deallocated state specifically, use whichever TTL is shorter between the existing 6 hour window and the new 48 hour threshold to maintain backward compatibility
3. Add proper error handling and logging to report how many entries were purged from each state

Key changes:
- Added oldRunnerThreshold constant (48 hours)
- Modified garbageCollect to loop through all known provisioning states
- Enhanced error handling to continue processing other states if one fails
- Improved logging to show per-state and total purge counts

Fixes #1731.
</pr_description>

## Comments on the PR (you are @copilot in this section)

<comments>

<pr_reviews>

<comment_thread>
<file>internal/hostedservice/hostedservice.go:452-458</file>
<original_commit>af86236</original_commit>

<comment_new>
<comment_id>2027042289</comment_id>
<author>@rentziass</author>
Can we simplify this by just defaulting to `s.deprovisionedDataTTL` is the state is `Deallocated`?
</comment_new>

</comment_thread>

<comment_thread>
<file>internal/hostedservice/hostedservice.go:463</file>
<original_commit>af86236</original_commit>

<comment_new>
<comment_id>2027046391</comment_id>
<author>@rentziass</author>
Can we test this by manually adding an entry for each state, calling Service.DeprovisionRunners and checking the set becomes empty? Please add this to `hostedservice_test.go`.
</comment_new>

</comment_thread>

</pr_reviews>

</comments>

----
The last **2** git commits in this branch are the changes you have made so far. Use those as your change commit history.