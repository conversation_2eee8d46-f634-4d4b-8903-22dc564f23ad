----
*This section includes details on the pull request with code changes you have already submitted to fix the problem above.*

<pr_title>
[WIP] `qsc_formatter` missing lint section in Cargo.toml
</pr_title>

<pr_description>
Thanks for assigning this issue to me. I'm starting to work on it and will keep this PR's description up to date as I form a plan and make progress.

Original issue description:

> The Cargo.toml for `qsc_formatter` is missing this section:
> ```
> [lints]
> workspace = true
> ```
> When that is added, there are bunch of warnings that need to be fixed in the crate.


Fixes #11
</pr_description>

## Comments on the PR (you are @copilot in this section)

<comments>

<pr_comments>

<comment_new>
<comment_id>2023648321</comment_id>
<author>@minestarks</author>
Can you try this one again? Looks like you hit some network issues the first time. @copilot
</comment_new>

<comment_new>
<comment_id>2023648331</comment_id>
<author>@minestarks</author>
Can you try this one again? Looks like you hit some network issues the first time.
</comment_new>

<comment_new>
<comment_id>2023648341</comment_id>
<author>@minestarks</author>
Can you try again? Looks like you hit some network issues the first time
</comment_new>

</pr_comments>

</comments>

----
The last **2** git commits in this branch are the changes you have made so far. Use those as your change commit history.