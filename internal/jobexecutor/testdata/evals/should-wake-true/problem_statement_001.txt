----
*This section includes details on the pull request with code changes you have already submitted to fix the problem above.*

<pr_title>
Fix case sensitivity in Slack mentions for overdue AR messages
</pr_title>

<pr_description>
This PR fixes an issue where certain usernames with uppercase characters (like "<PERSON><PERSON><PERSON>") weren't being properly converted to Slack mentions in overdue AR messages.

## Problem

When sending overdue AR notifications, the code was constructing Slack messages with user mentions by simply prefixing the username with an `@` symbol:

```javascript
"text": "Availability Review DRI: @${availabilityReviewDRIHandle}"
```

This approach doesn't properly create clickable Slack mentions, especially when the handle contains uppercase characters.

## Solution

Changed the implementation to:

1. Retrieve the actual Slack user ID for the DRI handle using `SlackClient.GetSlackIdByUsername()`
2. Use the proper Slack mention format `<@USER_ID>` in message text
3. Added error handling in case the Slack ID lookup fails

## Changes

- Updated `perIncidentChannelSlackMessage` function to accept a Slack user ID parameter
- Modified `sendIncidentChannelSlackMessage` to fetch the Slack ID using the lowercased handle
- Updated the message format to use `<@USER_ID>` syntax for proper mentions
- Updated tests to verify the new behavior

These changes ensure that all handles are properly converted to Slack mentions regardless of case sensitivity.

Fixes #966.
<pr_description>


## Comments on the PR (you are @copilot in this section)

<comments>

<pr_reviews>

<comment_thread>
<original_commit>af86236</original_commit>
<file>action/src/overdue-summary-notifications/per-incident-channel-slack-message.spec.ts:33</file>

<comment_new>
<comment_id>2047376912</comment_id>
<author>@alpacamybags118</author>
@copilot I would like to keep the explicit json checks in these tests.
</comment_new>

</comment_thread>

</pr_reviews>

<comments>

----
The last **2** git commits in this branch are the changes you have made so far. Use those as your change commit history.