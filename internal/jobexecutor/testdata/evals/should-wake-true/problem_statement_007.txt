----
*This section includes details on the pull request with code changes you have already submitted to fix the problem above.*

<pr_title>
Add Kusto function syntax validation workflow
</pr_title>

<pr_description>

This PR adds a GitHub Actions workflow that checks all Kusto query (.csl) files for correct syntax before they're merged. This helps catch syntax errors early instead of at deployment time.

Key components:

1. A new `check_functions.sh` script that:
   - Takes a directory parameter containing CSL files
   - Recursively processes all `.csl` files
   - Uses the Kusto CLI to validate syntax without actually deploying to a cluster

2. A GitHub Actions workflow that:
   - Runs on pull requests that modify files in the `functions/` directory
   - Downloads the Kusto CLI tool
   - Runs the syntax check on all function files
   - Fails the build if any syntax errors are found

The validation uses a dummy connection string with local execution to avoid any need for actual cluster connectivity during the validation process.

Fixes #991.

> [!WARNING]
>
> <details>
> <summary>Firewall rules blocked me from connecting to one or more addresses</summary>
>
> #### I tried to connect to the following addresses, but was blocked by firewall rules:
>
>  - ` https://www.nuget.org/api/v2/package/Microsoft.Azure.Kusto.Tools/6.0.1 `
>
>
> If you need me to access, download, or install something from one of these locations, you can either:
>
> - Configure [Actions setup steps](https://gh.io/copilot/actions-setup-steps) to set up my environment, which run before the firewall is enabled
> - Add the appropriate URLs or hosts to my [firewall allow list](https://gh.io/copilot/firewall-config)
>
> </details>
</pr_description>

## Comments on the PR (you are @copilot in this section)

<comments>

<pr_comments>

<comment_old>
<author>@gdryke</author>
Comment around the workflow permissions. Also, you should be able to access nuget.org and www.nuget.org now.
</comment_old>

</pr_comments>

<pr_reviews>

<comment_thread>
<file>.github/workflows/check_functions.yml:12</file>
<original_commit>c744d61</original_commit>

<comment_old>
<author>@gdryke</author>
This job was flagged by CodeQL for not having specific workflow permissions. Update this to use the least privilege necessary.

More details:
> If a GitHub Actions job or workflow has no explicit permissions set, then the repository permissions are used. Repositories created under organizations inherit the organization permissions. The organizations or repositories created before February 2023 have the default permissions set to read-write. Often these permissions do not adhere to the principle of least privilege and can be reduced to read-only, leaving the write permission only to a specific types as issues: write or pull-requests: write.
</comment_old>

<comment_old>
<author>@copilot</author>
I've updated the workflow to follow the principle of least privilege by adding explicit permissions with `contents: read` scope, which is the minimum required for this syntax-checking workflow. This limits permissions to only what's needed for checking out code and running the validation, without any write access. The change is in commit c446597.
</comment_old>

</comment_thread>

</pr_reviews>

<pr_comments>

<comment_old>
<author>@copilot</author>
Thank you for the feedback! I've addressed both points:

1. For the workflow permissions - I've updated the check_functions.yml workflow to follow the principle of least privilege by adding explicit permissions with `contents: read` scope. This is the minimum required for this syntax-checking workflow since it only needs to check out code and run validation without any write access. The change is in commit c446597.

2. Regarding the firewall issues - I understand that access to nuget.org should now be working properly. The workflow will be able to download the Kusto CLI tool as needed.
</comment_old>

<comment_new>
<author>@gdryke</author>
<comment_id>2045213928</comment_id>
Are you able to properly test the workflow with access to nuget.org?
</comment_new>

</pr_comments>

</comments>

----
The last **3** git commits in this branch are the changes you have made so far. Use those as your change commit history.