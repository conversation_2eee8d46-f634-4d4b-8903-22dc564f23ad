----
*This section includes details on the pull request with code changes you have already submitted to fix the problem above.*

<pr_title>
Add high-throughput API command support
</pr_title>

<pr_description>

Added new `high-throughput` command (aliased as `ht`) for managing app high-throughput settings. This implements the following operations:

* `high-throughput list` - Lists current high-throughput settings for an app
* `high-throughput list-tags` - Lists current high-throughput tags for an app
* `high-throughput enable` - Enables high-throughput for an app
* `high-throughput enable-tag` - Enables high-throughput for an app+tag combination
* `high-throughput disable` - Disables high-throughput for an app
* `high-throughput disable-tag` - Disables high-throughput for an app+tag combination

Implementation:
- Added `high-throughput` package with core logic and tests
- Added `cmd/high_throughput.go` with CLI command definition and form validation
- Added command to root config with all required flags
- Supports both interactive and command-line modes
- Follows patterns used by existing `queue-limit` command

Example usage:
```bash
# Basic enable/disable
aqueduct-tools high-throughput enable my-app
aqueduct-tools ht disable my-app

# Tag operations
aqueduct-tools ht enable-tag my-app my-tag
aqueduct-tools ht disable-tag my-app --tag=my-tag

# List settings
aqueduct-tools ht list my-app
aqueduct-tools ht list-tags my-app
```

Fixes #97.
</pr_description>

## Comments on the PR (you are @copilot in this section)

<comments>

<pr_comments>

<comment_old>
<author>@zerowidth</author>
Run the tests and fix the failures.
</comment_old>

</pr_comments>

<pr_reviews>

<comment_thread>
<file>cmd/high_throughput.go:20-30</file>
<original_commit>b794271</original_commit>

<comment_old>
<author>@zerowidth</author>
Remove this, it's generated by cobra help already
</comment_old>

<comment_old>
<author>@copilot</author>
Removed the manual help text since it's already generated by Cobra's help system. Changed in commit 50fa4ab.
</comment_old>

</comment_thread>

<comment_thread>
<file>cmd/high_throughput.go:83</file>
<original_commit>b794271</original_commit>

<comment_old>
<author>@zerowidth</author>
move this code to where the rest of the cmd initialization is, remove the init() here.
</comment_old>

</comment_thread>

<comment_thread>
<file>cmd/high_throughput.go:124</file>
<original_commit>b794271</original_commit>

<comment_old>
<author>@zerowidth</author>
Set up the subcommands as explicit cobra child commands, not a map of operations.
</comment_old>

</comment_thread>

</pr_reviews>

<pr_comments>

<comment_old>
<author>@copilot</author>
I analyzed the test failures and found this is an authentication issue rather than a test code problem. The tests themselves are well structured with proper mocking and full coverage of success/error cases for all high-throughput operations.

The failures are occurring because we cannot access the private dependency github.com/github/aqueduct-client-go/v3 which is required for the tests. This is an environment configuration issue rather than a test failure.

To run the tests, we need proper authentication setup similar to what the CI environment uses (github/setup-goproxy@v1.1.0). Could you please help provide the necessary authentication or access token details to run the tests? Once we have proper access configured, I can validate that all tests pass.
</comment_old>

<comment_new>
<comment_id>2040254146</comment_id>
<author>@zerowidth</author>
I've updated the runtime environment, run tests and linters again and fix the errors. Use `make test` and `make lint` respectively
</comment_new>

</pr_comments>

</comments>

----
The last **11** git commits in this branch are the changes you have made so far. Use those as your change commit history.