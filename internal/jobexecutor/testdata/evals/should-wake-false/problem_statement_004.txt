----
*This section includes details on the pull request with code changes you have already submitted to fix the problem above.*

<pr_title>
Upgrade ABC Trino to version 471
</pr_title>

<pr_description>
This PR updates the Trino clusters to version 471 as requested in the following files:
- config/values/airflow.yaml
- config/values/backfill.yaml
- config/values/canonical.yaml

Changes made:
- Updated image tag to "471-9d4bead" in all three files
- Removed "hive.config.resources=/etc/trino/catalog/abfs-site.xml" from all catalogs
- Added "fs.native-azure.enabled=true" to all catalogs
- Verified no "hive.s3.*" configurations existed that needed removal

The changes follow the example provided in PR #131 for adhoc.yaml.

Fixes #134.
</pr_description>

## Comments on the PR (you are @copilot in this section)

<comments>

<pr_comments>

<comment_new>
<comment_id>2023648321</comment_id>
<author>@jayzzh</author>
.noop
</comment_new>

<comment_new>
<comment_id>2023648777</comment_id>
<author>@jayzzh</author>
.deploy
</comment_new>

</pr_comments>

<comments>

----
The last **3** git commits in this branch are the changes you have made so far. Use those as your change commit history.