----
*This section includes details on the pull request with code changes you have already submitted to fix the problem above.*

<pr_title>
Add Repository Owners Caching to Improve Feature Flag Performance
</pr_title>

<pr_description>
This PR implements caching for the `getRepositoryOwners` method to reduce the number of Twirp API calls needed for feature flag checks when using `IsFeatureEnabledForRepoOrOwners`.

The changes include:
- Adding a new `GitHubRepositoryOwnersCache` interface to the `launchcache` package
- Implementing the cache using JSON serialization for the list of GlobalIDs
- Updating `getRepositoryOwners` to check the cache first before making API calls
- Adding appropriate metrics to track cache hits/misses
- Adding a unit test to verify the caching functionality

The cache expires after 1 hour, and the implementation gracefully falls back to making API calls if the cache lookup fails. This should significantly reduce the load on the `GetRepositoryOwners` Twirp API, which was identified as one of the most used Twirp calls from Launch.

Fixes #8958.
</pr_description>

## Comments on the PR (you are @copilot in this section)

<comments>

<pr_comments>

<comment_old>
<author>@Sr<PERSON>yan</author>
@copilot can you try again?
</comment_old>

<comment_old>
<author>@copilot</author>
I've addressed the implementation of the repository owners caching in commit ee16534:

1. Added a dedicated constant `repoOwnersCacheExpires` for the cache expiration time (1 hour) instead of hardcoding it
2. Enhanced error handling with better logging and detailed metrics
3. Added additional metrics to track cache failures (both read and write errors)
4. Improved the test coverage with specific cases for cache hits, misses, and error scenarios
5. Simplified the test structure to make it more robust and less prone to false failures

The tests are now passing and the implementation follows the same patterns used elsewhere in the codebase. Let me know if you'd like any additional changes!
</comment_old>

</pr_comments>

<pr_reviews>

<comment_thread>
<file>clients/ghtwirp/client.go:2164-2165</file>
<original_commit>af86236</original_commit>

<comment_new>
<comment_id>2043069856</comment_id>
<author>@joshmgross</author>
@SrRyan we might want to consider putting these changes behind a feature flag, what do you think?

To avoid an infinite recursive loop, we *can't* call `IsFeatureEnabledForRepoOrOwners`, but `IsFeatureEnabledForRepository` should be fine.

My one concern is that this is a very hot path.
</comment_new>

</comment_thread>

</pr_reviews>

<comments>

----
The last **3** git commits in this branch are the changes you have made so far. Use those as your change commit history.