----
*This section includes details on the pull request with code changes you have already submitted to fix the problem above.*

## Comments on the PR (you are @copilot in this section)

<comments>

<pr_comments>

<comment_new>
<comment_id>2027046391</comment_id>
<author>@Chuxel</author>
@JasonEtco @timrogers - We should run this through at least unicode content filtering.  How critical are HTML comments to this?
</comment_new>

</pr_comments>

<comments>

----
The last **4** git commits in this branch are the changes you have made so far. Use those as your change commit history.