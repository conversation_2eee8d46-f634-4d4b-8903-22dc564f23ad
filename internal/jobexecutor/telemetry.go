package jobexecutor

import (
	"context"
	"strconv"

	"github.com/github/sweagentd/internal/jobs"

	sweagentv0 "github.com/github/hydro-schemas-go/hydro/schemas/sweagentd/v0"
	sweagentEntitiesv0 "github.com/github/hydro-schemas-go/hydro/schemas/sweagentd/v0/entities"
)

func (e *AgentJobExecutor) publishJobStartTelemetry(ctx context.Context, job *jobs.Job) {
	if e.ffClient.IsEnabledForUserOrRepo(ctx, "sweagentd_publish_telemetry_events_to_hydro", job.ActorID, job.RepoID) {
		jobStartedEvent := toJobStarted(job)
		err := e.obsv.HydroPbl.Publish(jobStartedEvent)

		if err != nil {
			e.obsv.LogAndReportError(ctx, err, "failed to publish job_started event")
		}
	}
}

func toJobStarted(job *jobs.Job) *sweagentv0.JobStarted {
	hydroJob := jobs.ToHydro(job)
	event := &sweagentv0.JobStarted{
		Job: hydroJob,
		Trigger: &sweagentEntitiesv0.JobTrigger{
			Type:         toTriggerTypeProtobuf(job.EventType),
			IdentifierId: resolveTriggerID(job),
		},
		Action: toActionTypeProtobuf(job.Action),
	}
	if job.Experiments != nil && job.Experiments.AssignmentContext != "" {
		event.ExpAssignmentContext = job.Experiments.AssignmentContext
	}
	return event
}

func toActionTypeProtobuf(action jobs.AgentAction) sweagentEntitiesv0.ActionType {
	switch action {
	case jobs.AgentActionFix:
		return sweagentEntitiesv0.ActionType_FIX
	case jobs.AgentActionFixPRComment:
		return sweagentEntitiesv0.ActionType_FIX_PR_COMMENT
	default:
		return sweagentEntitiesv0.ActionType_UNKNOWN_ACTION
	}
}

func toTriggerTypeProtobuf(eventType jobs.EventType) sweagentEntitiesv0.JobTrigger_TriggerType {
	if eventType == "" {
		return sweagentEntitiesv0.JobTrigger_UNKNOWN_TRIGGER
	}

	switch eventType {
	case jobs.EventTypeIssueAssignment:
		return sweagentEntitiesv0.JobTrigger_ISSUE_ASSIGNED
	case jobs.EventTypePullRequestComment:
		return sweagentEntitiesv0.JobTrigger_PULL_REQUEST_COMMENT_CREATED
	case jobs.EventTypePullRequestReview:
		return sweagentEntitiesv0.JobTrigger_PULL_REQUEST_REVIEW_SUBMIT
	case jobs.EventTypeCodeScanningAlertsAssignment:
		return sweagentEntitiesv0.JobTrigger_CODE_SCANNING_ALERTS_ASSIGNED
	case jobs.EventTypeApiCallReceived:
		return sweagentEntitiesv0.JobTrigger_API_CALL_RECEIVED
	default:
		// The Jobs API allows for custom event types, so if we encounter an
		// unknown event type we can assume it was an API call.
		return sweagentEntitiesv0.JobTrigger_API_CALL_RECEIVED
	}
}

func resolveTriggerID(job *jobs.Job) int64 {
	switch job.EventType {
	case jobs.EventTypeIssueAssignment, jobs.EventTypePullRequestComment, jobs.EventTypePullRequestReview:
		return triggerIDFromEventIdentifiers(job.EventIdentifiers)
	case jobs.EventTypeCodeScanningAlertsAssignment:
		// Alerts is currently using the PR id as the trigger ID
		return job.PullRequestID
	default:
		return triggerIDFromEventIdentifiers(job.EventIdentifiers)
	}
}

func triggerIDFromEventIdentifiers(eventIdentifiers []string) int64 {
	if len(eventIdentifiers) == 0 || eventIdentifiers[0] == "" {
		return 0
	}

	triggerID, err := strconv.ParseInt(eventIdentifiers[0], 10, 64)
	if err != nil {
		return 0
	}

	return triggerID
}
