package jobexecutor

import (
	"context"
	_ "embed"
	"fmt"

	"github.com/github/sweagentd/internal/capi"
	"github.com/github/sweagentd/internal/joberrors"
	"github.com/github/sweagentd/internal/jobs"
	ju "github.com/github/sweagentd/internal/jobutils"
	sweagentdTwirp "github.com/github/sweagentd/proto/sweagentd/v1"
)

func (e *AgentJobExecutor) createSessionClient(ctx context.Context, userID, repoID int64) (capi.SessionClient, error) {
	if !e.ffClient.IsEnabledForUser(ctx, capi.CopilotAPIAgentSessions, userID) {
		return &capi.NoopSessionClient{}, nil
	}

	response, err := e.ghTwirp.SweagentdAPI().MintUserToServerTokenForRepo(ctx, &sweagentdTwirp.MintUserToServerTokenForRepoRequest{
		UserId:       uint64(userID),
		RepositoryId: uint64(repoID),
	})

	if err != nil {
		return nil, fmt.Errorf("failed to mint user to server token for repo: %w", err)
	}

	if response == nil || response.Token == "" {
		return nil, fmt.Errorf("failed to mint user to server token for repo. token is invalid or empty")
	}

	client := e.capiClient.NewSessionClient(userID, response.Token)

	return client, nil
}

// createSession creates a session for the job and patches the job with the session ID.
func (e *AgentJobExecutor) createSession(ctx context.Context, sessionClient capi.SessionClient, job *jobs.Job, sessionState capi.SessionState) (string, error) {
	// set the session early so we have the event type in logging and errors
	ju.AddRequestCtxSessionFromJob(ctx, e.obsv, job)
	if job.SessionID != "" {
		e.obsv.LoggerWithTelemetry(ctx).Info("session already exists for job, skipping session creation")
		return job.SessionID, nil
	}

	sessionName := generateSessionName(job)

	e.obsv.LoggerWithTelemetry(ctx).Info("creating session for job")
	session, err := sessionClient.CreateSession(ctx, job.OwnerID, job.RepoID, job.PullRequestID, job.EventType, job.EventURL, job.EventIdentifiers, sessionState, sessionName)
	if err != nil {
		return "", fmt.Errorf("failed to create session: %w", err)
	}

	e.obsv.LoggerWithTelemetry(ctx).Info("patching job with session ID")

	if err := e.jobsStore.PatchJob(ctx, job, jobs.JobPatch{SessionID: &session.ID}); err != nil {
		return "", fmt.Errorf("failed to patch job with session ID: %w", err)
	}

	ju.AddRequestCtxSessionFromJob(ctx, e.obsv, job)
	ju.UpdateRequestContextJob(ctx, e.obsv, job)

	return session.ID, nil
}

// startSession starts a session for the job and updates the session state to in progress.
func (e *AgentJobExecutor) startSession(ctx context.Context, sessionClient capi.SessionClient, job *jobs.Job, workflowID uint64) error {
	update := capi.SessionUpdate{WorkflowRunID: workflowID, State: capi.SessionStateInProgress}
	return e.updateSession(ctx, sessionClient, job, update)
}

// failSession fails the session for the job and updates the session state to failed with the provided message.
func (e *AgentJobExecutor) failSession(ctx context.Context, sessionClient capi.SessionClient, job *jobs.Job, jobError *joberrors.JobError, errorMessage string) error {
	update := capi.SessionUpdate{
		State: capi.SessionStateFailed,
		Error: &capi.SessionError{Code: jobError.String(), Message: errorMessage},
	}
	return e.updateSession(ctx, sessionClient, job, update)
}

func (e *AgentJobExecutor) updateSession(ctx context.Context, sessionClient capi.SessionClient, job *jobs.Job, update capi.SessionUpdate) error {
	if job.SessionID == "" {
		e.obsv.LoggerWithTelemetry(ctx).Info("session ID is empty, skipping session update")
		return nil
	}

	e.obsv.LoggerWithTelemetry(ctx).Info("updating session for job")

	if err := sessionClient.UpdateSession(ctx, job.SessionID, update); err != nil {
		return fmt.Errorf("failed to update session: %w", err)
	}
	ju.UpdateRequestContextSession(ctx, e.obsv, update)

	return nil
}

// generateSessionName creates a session name based on the job action.
func generateSessionName(job *jobs.Job) string {
	if job.Action == jobs.AgentActionFix {
		return "Initial implementation"
	} else {
		return fmt.Sprintf("Review from @%s", job.ActorLogin)
	}
}

// finalizeUnexecutedSession finalizes the session for unexecuted job by updating its state based on the job status.
func (e *AgentJobExecutor) finalizeUnexecutedSession(ctx context.Context, sessionClient capi.SessionClient, job *jobs.Job, errorMessage string) error {
	logger := e.obsv.LoggerWithTelemetry(ctx)
	logger.Info("finalizing session")

	if !job.IsTerminal() {
		logger.Info("job is not in a terminal state, nothing to do")
		return nil
	}

	if job.SessionID == "" {
		logger.Info("no session ID found for job")
		return nil
	}

	logger.Info("getting session for job")

	session, err := sessionClient.GetSession(ctx, job.SessionID)
	if err != nil {
		return fmt.Errorf("failed to get session: %w", err)
	}
	if session == nil {
		return fmt.Errorf("session not found")
	}
	ju.AddRequestContextSession(ctx, e.obsv, session)

	if capi.IsFinalState(session.State) {
		// Session is already in a final state, nothing to do
		logger.Info("session is already in a final state")
		return nil
	}

	newState := ju.MapJobStatusToSessionState(job.Status)
	if newState == "" {
		logger.Error("Failed to map job status to session state")
		return nil
	}

	if newState == session.State {
		logger.Info("session is already in the correct state, nothing to do")
		return nil
	}

	session.State = newState
	sessionUpdate := capi.SessionUpdate{State: session.State}
	if errorMessage != "" {
		sessionUpdate.Error = &capi.SessionError{Message: errorMessage}
	}

	if err := sessionClient.UpdateSession(ctx, session.ID, sessionUpdate); err != nil {
		return fmt.Errorf("failed to update session: %w", err)
	}
	ju.AddRequestContextSession(ctx, e.obsv, session)

	logger.Info("finalized job session")

	return nil
}
