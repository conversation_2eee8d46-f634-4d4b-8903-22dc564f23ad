package jobexecutor

import (
	"github.com/github/sweagentd/internal/aqueduct"
	aqueductJobs "github.com/github/sweagentd/internal/aqueduct/jobs"
	"github.com/github/sweagentd/internal/capi"
	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/githubtwirp"
	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/launcher"
	"github.com/github/sweagentd/internal/observability"
)

type TestJobExecutor struct {
	AgentJobExecutor
	Launcher       *launcher.NoopLauncher
	aqueductClient *aqueduct.AqueductMemoryClient
}

var _ JobExecutor = (*TestJobExecutor)(nil)

func NewTestJobExecutor(obsv observability.Exporters, ffClient featureflags.Client, jobsStore jobs.JobsStore, ghFactory github.ClientFactoryInterface, ghTwirp githubtwirp.ClientInterface, capiClient capi.Client) *TestJobExecutor {
	launcher := launcher.NewNoopLauncher(obsv)
	memoryClient := aqueduct.NewMemoryClient(obsv)
	return &TestJobExecutor{
		AgentJobExecutor: AgentJobExecutor{
			obsv:           obsv,
			ffClient:       ffClient,
			jobsStore:      jobsStore,
			ghFactory:      ghFactory,
			ghTwirp:        ghTwirp,
			capiClient:     capiClient,
			launcher:       launcher,
			aqueductClient: memoryClient,
		},
		Launcher:       launcher,
		aqueductClient: memoryClient,
	}
}

func (n *TestJobExecutor) QueuedJobs() []*aqueductJobs.AgentJob {
	var agentJobs []*aqueductJobs.AgentJob

	jobs := n.aqueductClient.QueuedJobs()

	for _, job := range jobs {
		var agentJob aqueductJobs.AgentJob
		if err := agentJob.Unmarshal(job.Payload); err != nil {
			continue
		}
		agentJobs = append(agentJobs, &agentJob)
	}

	return agentJobs
}

func (n *TestJobExecutor) LaunchAgentCalls() []launcher.LaunchAgentOpts {
	return n.Launcher.LaunchAgentCalls
}

func (n *TestJobExecutor) TerminateAgentCalls() []launcher.TerminateAgentOpts {
	return n.Launcher.TerminateAgentCalls
}

func (n *TestJobExecutor) TerminateAgentError() error {
	return n.Launcher.TerminateAgentError
}
