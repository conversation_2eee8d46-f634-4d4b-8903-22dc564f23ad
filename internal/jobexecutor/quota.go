package jobexecutor

import (
	"context"
	_ "embed"
	"fmt"

	"github.com/github/go-stats"
	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/githubtwirp"
)

func (e *AgentJobExecutor) insufficientQuotaMessage(totalPremiumQuota int32) string {
	message := "Your session could not start because you've used up the "
	if totalPremiumQuota > 0 {
		message += fmt.Sprintf("%d ", totalPremiumQuota)
	}
	message += "premium requests allowance included in your Copilot subscription this month, "
	message += "and your subscription does not allow additional paid usage. "
	message += "To retry, just leave a comment on this pull request asking me to try again. "
	message += "Learn more about managing premium request limits https://gh.io/manage-premrequests."
	return message
}

func (e *AgentJobExecutor) GetPremiumQuotaStatus(ctx context.Context, ownerID, repoID, userID int64) (bool, int32, error) {
	if !e.ffClient.IsEnabledForUserOrRepoOrOwner(ctx, featureflags.CopilotSweAgentPremQuotaCheckFlagName, userID, repoID, ownerID) {
		e.obsv.LoggerWithTelemetry(ctx).Info("skipping premium quota check for user")
		return true, 0, nil
	}

	remainingQuota, totalQuota, overagePermitted, err := githubtwirp.GetQuotaRemaining(ctx, e.ghTwirp, userID, githubtwirp.PREMIUM_REQUESTS)
	if err != nil {
		return false, 0, fmt.Errorf("failed to check quota remaining: %w", err)
	}
	e.obsv.LoggerWithTelemetry(ctx).Info(fmt.Sprintf("premium quota status: remaining=%d, total=%d, overagePermitted=%t", remainingQuota, totalQuota, overagePermitted))
	if remainingQuota > 0 || overagePermitted {
		e.obsv.LoggerWithTelemetry(ctx).Info("user has sufficient premium quota")
		return true, totalQuota, nil
	}
	e.obsv.LoggerWithTelemetry(ctx).Info("user has no remaining premium quota and overage is not permitted")
	e.obsv.Increment(ctx, "events.premium_quota_exceeded", stats.Tags{})
	return false, totalQuota, nil
}
