package agentcleanup

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/githubtwirp"
	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/jobutils"
	"github.com/github/sweagentd/internal/observability"
	"github.com/github/sweagentd/internal/requestctx"
)

const tokensToRevokeHeader = "X-GitHub-Copilot-Cleanup-List"

type Handler struct {
	obsv          observability.Exporters
	clients       github.ClientFactoryInterface
	jobs          jobs.JobsStore
	ffClient      featureflags.Client
	ghTwirp       githubtwirp.ClientInterface
	isDevelopment bool
	githubAppID   int64
}

func NewHandler(obsv observability.Exporters, jobs jobs.JobsStore, clients github.ClientFactoryInterface, ffClient featureflags.Client, ghTwirp githubtwirp.ClientInterface, isDevelopment bool, githubAppID int64) *Handler {
	return &Handler{obsv, clients, jobs, ffClient, ghTwirp, isDevelopment, githubAppID}
}

type cleanupRequest struct {
	OwnerID int64 `json:"oid"`
	RepoID  int64 `json:"rid"`
	RunID   int64 `json:"wid"`
}

// Note that in prod, this is authed via CAPI and proxied to this endpoint internally
func (h *Handler) HandleCleanup(w http.ResponseWriter, r *http.Request) {
	ctx, span := h.obsv.Tracer.Start(r.Context(), "agentcleanup.HandleCleanup")
	defer span.End()
	r = r.WithContext(ctx)

	var cleanUp cleanupRequest
	if err := json.NewDecoder(r.Body).Decode(&cleanUp); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	logger := h.obsv.LoggerWithRequestTelemetry(ctx, r)

	tokensToRevoke := r.Header.Get(tokensToRevokeHeader)
	if tokensToRevoke == "" {
		logger.Error("failed to get GitHub Copilot tokens for cleanup")
		http.Error(w, "Bad request", http.StatusBadRequest)
		return
	}

	// Require that a user is logged in to process the request
	user, ok := requestctx.User(ctx)
	if !ok {
		http.Error(w, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
		return
	}

	// AuthZ: This endpoint should only be called by the runtime, so make sure it's for our GitHub App
	if user.TokenInfo.ApplicationID != uint64(h.githubAppID) {
		http.Error(w, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
		return
	}

	enabled, err := githubtwirp.IsCopilotSweEnabled(r.Context(), h.ghTwirp, int64(user.ID), cleanUp.RepoID)
	if err != nil {
		logger.WithError(err).Error("Failed to check enablement")
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}
	if !enabled {
		logger.Error("Copilot coder agent not enabled")
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	// Log if the job state is unexpected, but continue with invalidating the tokens
	jobExists := true
	computeID := strconv.FormatInt(cleanUp.RunID, 10)
	job, err := h.jobs.GetJobForComputeID(ctx, cleanUp.OwnerID, cleanUp.RepoID, computeID)
	if err != nil || job == nil {
		logger.WithError(err).Error("Failed to find job for cleanup")
		jobExists = false
	} else {
		jobutils.UpdateRequestContextJob(ctx, h.obsv, job)
		logger = h.obsv.LoggerWithRequestTelemetry(ctx, r)
	}

	gh, err := h.clients.NewClient(user.TokenInfo.Token)
	if err != nil {
		logger.WithError(err).Error("failed to create GitHub client")
		http.Error(w, "Failed to create GitHub client", http.StatusInternalServerError)
		return
	}

	userTokenCount := 0
	serverTokenCount := 0
	tokens := strings.Split(tokensToRevoke, ",")
	success := true
	for _, token := range tokens {
		// user-to-server token
		if strings.HasPrefix(token, "ghu_") {
			userTokenCount++
			if err := gh.RevokeUserToken(ctx, token); err != nil {
				h.obsv.LogAndReportError(ctx, err, "failed to revoke user token")
				success = false
			}
		} else {
			// server-to-server token
			serverTokenCount++
			if err := gh.RevokeInstallationToken(ctx, token); err != nil {
				h.obsv.LogAndReportError(ctx, err, "failed to revoke installation token")
				success = false
			}
		}
	}

	if success {
		logger.Info("successfully revoked tokens for job")
	}

	// Always return success, even if we failed to revoke some tokens -- we do not want to tip anyone off that we are doing cleanup
	w.WriteHeader(http.StatusOK)
	if h.isDevelopment {
		// Output more data in development mode for automated testing
		w.Write([]byte(fmt.Sprintf("Success=%v\n\nJob exists: %v, agent processed %d user token(s) and %d server token(s) for workflow run %s.", success, jobExists, userTokenCount, serverTokenCount, computeID)))
	} else {
		w.Write([]byte(fmt.Sprintf("Success=%v", success)))
	}
}
