package freno

import (
	"context"
)

// ProximaClient is a client for the freno throttler. Proxima doesn't support Freno, so it does nothing.
type ProximaClient struct {
}

// NewProximaClient creates a new freno client.
func NewProximaClient() FrenoClient {
	return &ProximaClient{}
}

// WaitOnThrottler waits on the throttler.
func (c *ProximaClient) WaitOnThrottler(ctx context.Context) error {
	return nil
}
