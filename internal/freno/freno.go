package freno

import (
	"context"
	"time"

	freno "github.com/github/go-freno-client"
	"github.com/github/sweagentd/internal/observability"
)

const (
	frenoThrottlerTimeout = time.Second * 10
	frenoCluster          = "blanket"
)

// FrenoClient is a client for the freno throttler.
type FrenoClient interface {
	WaitOnThrottler(ctx context.Context) error
}

// ProductionFrenoClient is a client for the freno throttler.
type ProductionFrenoClient struct {
	throttler freno.Throttler
	obsv      observability.Exporters
}

// NewFrenoClient creates a new freno client.
func NewFrenoClient(obsv observability.Exporters, addr, app string) FrenoClient {
	t := freno.DefaultThrottler
	if addr != "" {
		t = freno.NewFrenoThrottler(addr, app, frenoCluster)
	}
	return &ProductionFrenoClient{throttler: t, obsv: obsv}
}

// WaitOnThrottler waits on the throttler.
func (c *ProductionFrenoClient) WaitOnThrottler(ctx context.Context) error {
	timeoutCtx, cancel := context.WithTimeout(ctx, frenoThrottlerTimeout)
	defer cancel()

	start := time.Now()
	err := freno.WaitOnThrottler(timeoutCtx, c.throttler)
	c.obsv.Statter.DistributionMs("freno.throttle.time", nil, time.Since(start))
	c.obsv.Statter.Counter("freno.throttle.count", nil, 1)

	return err
}
