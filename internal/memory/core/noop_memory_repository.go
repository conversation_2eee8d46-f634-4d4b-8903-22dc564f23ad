package core

type noopMemoryRepository struct{}

// NewNoopMemoryRepository creates a new noopMemoryRepository instance.
func NewNoopMemoryRepository() MemoryRepository {
	return &noopMemoryRepository{}
}

// Save saves a memory to the repository.
func (r *noopMemoryRepository) Save(memory *Memory) error {
	// No operation for noop repository
	return nil
}

// FindByID retrieves a memory by its ID from the repository.
func (r *noopMemoryRepository) FindByID(repoNWO RepoNWO, id MemoryID) (*Memory, error) {
	// No operation for noop repository
	return nil, nil
}

// FindByRepository retrieves all memories from the repository.
func (r *noopMemoryRepository) FindByRepository(repoNWO RepoNWO) ([]*Memory, error) {
	// No operation for noop repository
	return make([]*Memory, 0), nil
}

// Delete removes a memory from the repository.
func (r *noopMemoryRepository) Delete(id MemoryID) error {
	// No operation for noop repository
	return nil
}
