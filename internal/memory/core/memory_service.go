package core

import "errors"

type MemoryService interface {
	CreateMemory(memory *Memory) error
	GetMemories(repoNWO RepoNWO) ([]*Memory, error)
}

type memoryService struct {
	repo MemoryRepository
}

// NewMemoryService creates a new instance of MemoryService.
func NewMemoryService(repo MemoryRepository) MemoryService {
	return &memoryService{
		repo: repo,
	}
}

// CreateMemory saves a new memory to the repository.
func (s *memoryService) CreateMemory(memory *Memory) error {
	return s.repo.Save(memory)
}

// GetMemories retrieves all memories for a given repository NWO.
func (s *memoryService) GetMemories(repoNWO RepoNWO) ([]*Memory, error) {
	memories, err := s.repo.FindByRepository(repoNWO)
	if err != nil {
		return nil, err
	}
	if len(memories) == 0 {
		return nil, ErrNoMemoriesNotFound
	}
	return memories, nil
}

var (
	ErrNoMemoriesNotFound = errors.New("no memories found for the given repository")
)
