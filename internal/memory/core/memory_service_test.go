package core_test

import (
	"errors"
	"testing"

	"github.com/github/sweagentd/internal/memory/core"
	"github.com/stretchr/testify/assert"
)

type MockMemoryRepository struct {
	SaveFunc             func(memory *core.Memory) error
	FindByIDFunc         func(repoNWO core.RepoNWO, id core.MemoryID) (*core.Memory, error)
	FindByRepositoryFunc func(repoNWO core.RepoNWO) ([]*core.Memory, error)
}

func (m *MockMemoryRepository) Save(memory *core.Memory) error {
	return m.SaveFunc(memory)
}

func (m *MockMemoryRepository) FindByID(repoNWO core.RepoNWO, id core.MemoryID) (*core.Memory, error) {
	return m.FindByIDFunc(repoNWO, id)
}

func (m *MockMemoryRepository) FindByRepository(repoNWO core.RepoNWO) ([]*core.Memory, error) {
	return m.FindByRepositoryFunc(repoNWO)
}

func (m *MockMemoryRepository) Delete(id core.MemoryID) error {
	return nil // No-op for this mock
}

func TestCreateMemory(t *testing.T) {
	memoryRepository := &MockMemoryRepository{
		SaveFunc: func(memory *core.Memory) error {
			return nil // Simulate successful save
		},
	}
	memoryService := core.NewMemoryService(memoryRepository)
	memory := &core.Memory{
		ID:        "1",
		Subject:   "Test Subject",
		Fact:      "Test Fact",
		Reason:    "Test Reason",
		Citations: []string{"source1"},
		Source: core.MemorySource{
			IntegrationID: "integration1",
			InteractionID: "interaction1",
			NWO:           core.RepoNWO{Owner: "test", Repo: "repo"},
			User:          core.User{ID: 1, Login: "testuser"},
		},
	}

	err := memoryService.CreateMemory(memory)
	assert.NoError(t, err, "expected no error when creating memory")
}

func TestGetMemoriesNotFound(t *testing.T) {
	memoryRepository := &MockMemoryRepository{
		FindByRepositoryFunc: func(repoNWO core.RepoNWO) ([]*core.Memory, error) {
			return make([]*core.Memory, 0), nil // Simulate no memories found
		},
	}
	memoryService := core.NewMemoryService(memoryRepository)
	repoNwo, err := core.NewRepoNWO("test", "random-repo")
	assert.NoError(t, err, "expected no error when creating RepoNWO")
	_, err = memoryService.GetMemories(*repoNwo)
	assert.ErrorIs(t, err, core.ErrNoMemoriesNotFound, "expected no memories found error")
}

func TestGetMemories(t *testing.T) {
	repoNwo, err := core.NewRepoNWO("test", "random-repo")
	assert.NoError(t, err, "expected no error when creating RepoNWO")
	memory := &core.Memory{
		ID:        "1",
		Subject:   "Test Subject",
		Fact:      "Test Fact",
		Reason:    "Test Reason",
		Citations: []string{"source1"},
		Source: core.MemorySource{
			IntegrationID: "integration1",
			InteractionID: "interaction1",
			NWO:           *repoNwo,
			User:          core.User{ID: 1, Login: "testuser"},
		},
	}
	memoryRepository := &MockMemoryRepository{
		FindByRepositoryFunc: func(repoNWO core.RepoNWO) ([]*core.Memory, error) {
			return []*core.Memory{
				memory,
			}, nil // Simulate one memory found
		},
	}
	memoryService := core.NewMemoryService(memoryRepository)

	memories, err := memoryService.GetMemories(*repoNwo)
	assert.NoError(t, err, "expected no error when getting memories")
	assert.Len(t, memories, 1, "expected one memory to be returned")
	assert.Equal(t, memory, memories[0], "expected returned memory to match the created memory")
}

func TestGetMemoriesRepositoryError(t *testing.T) {
	expectedError := errors.New("some random error")
	memoryRepository := &MockMemoryRepository{
		FindByRepositoryFunc: func(repoNWO core.RepoNWO) ([]*core.Memory, error) {
			return nil, expectedError // Simulate an error
		},
	}
	memoryService := core.NewMemoryService(memoryRepository)
	repoNwo, err := core.NewRepoNWO("test", "error-repo")
	assert.NoError(t, err, "expected no error when creating RepoNWO")
	_, err = memoryService.GetMemories(*repoNwo)
	assert.Error(t, err, "expected an error when getting memories")
	assert.ErrorIs(t, err, expectedError, "expected the returned error to match the simulated error")
}
