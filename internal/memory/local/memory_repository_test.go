package memorylocal

import (
	"testing"

	"github.com/github/sweagentd/internal/memory/core"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
)

var testRepo = NewLocalMemoryRepository()

func createTestMemory(nwo string) *core.Memory {
	memoryID := core.MemoryID(uuid.New().String())
	repoNwo, _ := core.NewRepoNWOFromString(nwo)
	return core.NewMemory(memoryID, "Test Subject", "Test Fact", "Test Reason", []string{"source1"}, core.MemorySource{
		IntegrationID: "integration1",
		InteractionID: "interaction1",
		NWO:           *repoNwo,
		User:          core.User{ID: 1, Login: "testuser"},
	})
}

func TestLocalMemoryRepository_Save(t *testing.T) {
	memory := createTestMemory("test/repo")

	// Test successful save
	err := testRepo.Save(memory)
	assert.NoError(t, err)

	// Test Save with nil memory should return error
	err = testRepo.Save(nil)
	assert.Error(t, err)
}

func TestLocalMemoryRepository_FindByID(t *testing.T) {
	memory := createTestMemory("test/repo")
	repoNwo, err := core.NewRepoNWO("test", "repo")
	assert.NoError(t, err, "expected no error when creating RepoNWO")

	// Save memory first
	err = testRepo.Save(memory)
	assert.NoError(t, err)

	// Test FindByID with existing memory
	foundMemory, err := testRepo.FindByID(*repoNwo, memory.ID)
	assert.NoError(t, err)
	assert.Equal(t, memory, foundMemory)

	// Test FindByID with non-existent memory
	nonExistentID := core.MemoryID(uuid.New().String())
	_, err = testRepo.FindByID(*repoNwo, nonExistentID)
	assert.Error(t, err)
}

func TestLocalMemoryRepository_FindByRepository(t *testing.T) {
	memory := createTestMemory("test/repo")
	repoNwo, err := core.NewRepoNWO("test", "repo")
	assert.NoError(t, err, "expected no error when creating RepoNWO")

	// Save memory first
	err = testRepo.Save(memory)
	assert.NoError(t, err)

	// Test FindByRepository with existing repository
	memories, err := testRepo.FindByRepository(*repoNwo)
	assert.NoError(t, err)
	assert.Greater(t, len(memories), 0) // Should have at least the memory we just saved

	// Find our specific memory in the results
	found := false
	for _, m := range memories {
		if m.ID == memory.ID {
			assert.Equal(t, memory, m)
			found = true
			break
		}
	}
	assert.True(t, found, "Should find the saved memory in repository results")

	// Test FindByRepository with non-existent repository
	nonExistentRepo, _ := core.NewRepoNWO("test", "nonexistent")
	memories, err = testRepo.FindByRepository(*nonExistentRepo)
	assert.NoError(t, err)
	assert.Len(t, memories, 0)
}

func TestLocalMemoryRepository_Delete(t *testing.T) {
	memory := createTestMemory("test/repo")
	repoNwo, err := core.NewRepoNWO("test", "repo")
	assert.NoError(t, err, "expected no error when creating RepoNWO")

	// Save memory first
	err = testRepo.Save(memory)
	assert.NoError(t, err)

	// Test Delete existing memory
	err = testRepo.Delete(memory.ID)
	assert.NoError(t, err)

	// Test that memory is actually deleted
	_, err = testRepo.FindByID(*repoNwo, memory.ID)
	assert.Error(t, err)

	// Test Delete non-existent memory (should not error)
	nonExistentID := core.MemoryID(uuid.New().String())
	err = testRepo.Delete(nonExistentID)
	assert.ErrorIs(t, err, core.ErrMemoryNotFound) // Expect specific error for not found
}
