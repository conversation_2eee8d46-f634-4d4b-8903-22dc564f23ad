// Package memorylocal provides an in-memory implementation of a repository for managing local data.
package memorylocal

import (
	"errors"
	"sync"

	"github.com/github/sweagentd/internal/memory/core"
)

// LocalMemoryRepository is an in-memory implementation of the MemoryRepository interface.
type LocalMemoryRepository struct {
	mu       sync.RWMutex
	memories map[core.MemoryID]*core.Memory // key is memory ID
}

var _ core.MemoryRepository = (*LocalMemoryRepository)(nil)

// NewLocalMemoryRepository creates a new instance of LocalMemoryRepository.
func NewLocalMemoryRepository() core.MemoryRepository {
	return &LocalMemoryRepository{
		memories: make(map[core.MemoryID]*core.Memory),
	}
}

// Save stores a memory in the repository.
func (r *LocalMemoryRepository) Save(memory *core.Memory) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if memory == nil {
		return errors.New("memory cannot be nil")
	}

	r.memories[memory.ID] = memory
	return nil
}

// FindByID retrieves a memory by its ID and repository NWO.
func (r *LocalMemoryRepository) FindByID(repoNWO core.RepoNWO, id core.MemoryID) (*core.Memory, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	memory, exists := r.memories[id]
	if !exists || memory.Source.NWO != repoNWO {
		return nil, core.ErrMemoryNotFound
	}
	return memory, nil
}

// FindByRepository retrieves all memories for a given repository NWO.
func (r *LocalMemoryRepository) FindByRepository(repoNWO core.RepoNWO) ([]*core.Memory, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	var results []*core.Memory
	for _, memory := range r.memories {
		if memory.Source.NWO == repoNWO {
			results = append(results, memory)
		}
	}
	return results, nil
}

// Delete removes a memory by its ID.
func (r *LocalMemoryRepository) Delete(id core.MemoryID) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if _, exists := r.memories[id]; !exists {
		return core.ErrMemoryNotFound
	}
	delete(r.memories, id)
	return nil
}
