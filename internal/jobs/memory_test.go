package jobs

import (
	"context"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestJobETag(t *testing.T) {
	ctx := context.Background()

	t.Run("MemoryStore creates job ETag", func(t *testing.T) {
		store := NewMemoryStore()
		job := &Job{
			ID:        "job-id",
			OwnerID:   1,
			RepoID:    2,
			RepoOwner: "owner",
			RepoName:  "repo",
		}

		job, err := store.CreateJob(ctx, job)
		require.NoError(t, err)
		require.NotEmpty(t, job.ETag, "ETag should be automatically generated")

		// Verify the hash was stored
		retrievedJob, err := store.GetJob(ctx, job.OwnerID, job.RepoID, job.ID)
		require.NoError(t, err)
		require.NotEmpty(t, retrievedJob.ETag, "ETag should be stored")
	})

	t.Run("MemoryStore creates assignment ETag", func(t *testing.T) {
		store := NewMemoryStore()

		assignment := &Assignment{
			ID:        "assignment-id-3",
			OwnerID:   1,
			RepoID:    2,
			RepoOwner: "owner",
			RepoName:  "repo",
		}

		assignment, err := store.CreateAssignment(ctx, assignment)
		require.NoError(t, err)
		require.NotEmpty(t, assignment.ETag, "ETag should be automatically generated")
	})

	t.Run("MemoryStore creates job ETag", func(t *testing.T) {
		store := NewMemoryStore()

		job := &Job{
			ID:        "job-id-2",
			OwnerID:   1,
			RepoID:    2,
			RepoOwner: "owner",
			RepoName:  "repo",
		}

		job, err := store.CreateJob(ctx, job)
		require.NoError(t, err)
		require.NotEmpty(t, job.ETag, "ETag should be automatically generated")
	})

	t.Run("MemoryStore updates assignment ETag on patch", func(t *testing.T) {
		store := NewMemoryStore()

		assignment := &Assignment{
			ID:        "assignment-id-3",
			OwnerID:   1,
			RepoID:    2,
			RepoOwner: "owner",
			RepoName:  "repo",
		}

		assignment, err := store.CreateAssignment(ctx, assignment)
		require.NoError(t, err)
		require.NotEmpty(t, assignment.ETag, "ETag should be automatically generated on creation")

		etag := assignment.ETag
		require.NotEmpty(t, etag, "ETag should be generated on creation")

		prNumber := 8
		err = store.PatchAssignment(ctx, assignment, AssignmentPatch{PullRequestNumber: &prNumber})
		require.NoError(t, err)

		require.False(t, assignment.ETag.Equals(*etag), "ETag should change after patching assignment")
	})

	t.Run("MemoryStore updates job ETag on patch", func(t *testing.T) {
		store := NewMemoryStore()

		job := &Job{
			ID:        "job-id-3",
			OwnerID:   1,
			RepoID:    2,
			RepoOwner: "owner",
			RepoName:  "repo",
		}

		job, err := store.CreateJob(ctx, job)
		require.NoError(t, err)
		require.NotEmpty(t, job.ETag, "ETag should be automatically generated on creation")

		etag := job.ETag
		require.NotEmpty(t, etag, "ETag should be generated on creation")

		newStatus := JobStatusCompleted
		err = store.PatchJob(ctx, job, JobPatch{Status: &newStatus})
		require.NoError(t, err)

		require.False(t, job.ETag.Equals(*etag), "ETag should change after patching job")
	})

	t.Run("MemoryStore updates assignment ETag on update", func(t *testing.T) {
		store := NewMemoryStore()

		assignment := &Assignment{
			ID:        "assignment-id-4",
			OwnerID:   1,
			RepoID:    2,
			RepoOwner: "owner",
			RepoName:  "repo",
		}

		assignment, err := store.CreateAssignment(ctx, assignment)
		require.NoError(t, err)
		require.NotEmpty(t, assignment.ETag, "ETag should be automatically generated on creation")

		etag := assignment.ETag
		require.NotEmpty(t, etag, "ETag should be generated on creation")

		assignment.PullRequestNumber = 8
		assignment, err = store.UpdateAssignment(ctx, assignment)
		require.NoError(t, err)

		require.False(t, assignment.ETag.Equals(*etag), "ETag should change after patching assignment")
	})

	t.Run("MemoryStore updates job ETag on update", func(t *testing.T) {
		store := NewMemoryStore()

		job := &Job{
			ID:        "job-id-4",
			OwnerID:   1,
			RepoID:    2,
			RepoOwner: "owner",
			RepoName:  "repo",
		}

		job, err := store.CreateJob(ctx, job)
		require.NoError(t, err)
		require.NotEmpty(t, job.ETag, "ETag should be automatically generated on creation")

		etag := job.ETag
		require.NotEmpty(t, etag, "ETag should be generated on creation")

		job.Status = JobStatusCompleted
		job, err = store.UpdateJob(ctx, job)
		require.NoError(t, err)

		require.False(t, job.ETag.Equals(*etag), "ETag should change after patching job")
	})
}

func TestJobNonce(t *testing.T) {
	ctx := context.Background()

	t.Run("MemoryStore generates nonce", func(t *testing.T) {
		store := NewMemoryStore()
		job := &Job{
			ID:        "job-id",
			OwnerID:   1,
			RepoID:    2,
			RepoOwner: "owner",
			RepoName:  "repo",
			Status:    JobStatusPending,
		}

		_, err := store.CreateJob(ctx, job)
		require.NoError(t, err)
		require.Empty(t, job.NonceHash, "NonceHash should be empty initially")

		nonce, err := store.RegenerateJobNonce(ctx, job)
		require.NoError(t, err)
		require.NotEmpty(t, nonce, "Nonce should be generated")
		require.Len(t, nonce, 64, "Nonce should be 64 characters (32 bytes hex-encoded)")
		require.NotEmpty(t, job.NonceHash, "NonceHash should be populated after nonce generation")

		// Verify the hash was stored
		retrievedJob, err := store.GetJob(ctx, job.OwnerID, job.RepoID, job.ID)
		require.NoError(t, err)
		require.NotEmpty(t, retrievedJob.NonceHash, "NonceHash should be stored")
		require.Equal(t, HashNonce(nonce), retrievedJob.NonceHash, "NonceHash should match the hash of the nonce")
	})

	t.Run("MemoryStore fails to regenerate nonce for running job", func(t *testing.T) {
		store := NewMemoryStore()
		job := &Job{
			ID:        "job-id-running",
			OwnerID:   1,
			RepoID:    2,
			RepoOwner: "owner",
			RepoName:  "repo",
			Status:    JobStatusRunning,
		}

		_, err := store.CreateJob(ctx, job)
		require.NoError(t, err)

		// Attempt to regenerate nonce for a running job
		_, err = store.RegenerateJobNonce(ctx, job)
		require.Error(t, err, "Should not be able to regenerate nonce for running job")
	})
}
