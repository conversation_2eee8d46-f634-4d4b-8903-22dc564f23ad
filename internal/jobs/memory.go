package jobs

import (
	"context"
	"errors"
	"fmt"
	"slices"
	"sync"
	"time"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore"
	"github.com/google/uuid"
)

type MemoryStore struct {
	mu          sync.RWMutex
	assignments map[int64]map[int64]map[string]*memoryAssignment
	jobs        map[int64]map[int64]map[string]*memoryJob
}

// Ensure that MemoryStore implements JobStore
var _ JobsStore = &MemoryStore{}

type memoryAssignment struct {
	*Assignment
}

type memoryJob struct {
	*Job
	Result string
}

func NewMemoryStore() *MemoryStore {
	return &MemoryStore{
		assignments: make(map[int64]map[int64]map[string]*memoryAssignment),
		jobs:        make(map[int64]map[int64]map[string]*memoryJob),
	}
}

func newETag() *azcore.ETag {
	uid := uuid.New()
	str := fmt.Sprintf("\"%s\"", uid.String())
	etag := azcore.ETag(str)
	return &etag
}

func (m *MemoryStore) CreateAssignment(ctx context.Context, assignment *Assignment) (*Assignment, error) {
	m.mu.Lock()
	defer m.mu.Unlock()

	if assignment.ETag == nil {
		assignment.ETag = newETag()
	}

	if assignment.CreatedAt == 0 {
		assignment.CreatedAt = time.Now().Unix()
	}

	if _, ok := m.assignments[assignment.OwnerID]; !ok {
		m.assignments[assignment.OwnerID] = make(map[int64]map[string]*memoryAssignment)
	}

	if _, ok := m.assignments[assignment.OwnerID][assignment.RepoID]; !ok {
		m.assignments[assignment.OwnerID][assignment.RepoID] = make(map[string]*memoryAssignment)
	}

	m.assignments[assignment.OwnerID][assignment.RepoID][assignment.ID] = &memoryAssignment{Assignment: assignment}
	return assignment, nil
}

func (m *MemoryStore) GetAssignment(ctx context.Context, owner, repo int64, id string) (*Assignment, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	assignment, ok := m.assignments[owner][repo][id]
	if !ok {
		return nil, errors.New("assignment not found")
	}
	return assignment.Assignment, nil
}

func (m *MemoryStore) GetAssignments(ctx context.Context, owner, repo int64) ([]*Assignment, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	assignments := make([]*Assignment, 0, len(m.assignments[owner][repo]))
	for _, assignment := range m.assignments[owner][repo] {
		assignments = append(assignments, assignment.Assignment)
	}
	return assignments, nil
}

func (m *MemoryStore) UpdateAssignment(ctx context.Context, assignment *Assignment) (*Assignment, error) {
	m.mu.Lock()
	defer m.mu.Unlock()

	ma, ok := m.assignments[assignment.OwnerID][assignment.RepoID][assignment.ID]
	if !ok {
		return nil, errors.New("assignment not found")
	}

	if assignment.ETag != nil && !ma.Assignment.ETag.Equals(*assignment.ETag) {
		return nil, ErrPreconditionFailedOnUpdate
	}
	assignment.ETag = newETag()

	ma.Assignment = assignment

	return assignment, nil
}

func (m *MemoryStore) PatchAssignment(ctx context.Context, assignment *Assignment, patch AssignmentPatch) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	ma, ok := m.assignments[assignment.OwnerID][assignment.RepoID][assignment.ID]
	if !ok {
		return errors.New("assignment not found")
	}

	if patch.PullRequestID != nil {
		assignment.PullRequestID = *patch.PullRequestID
	}
	if patch.PullRequestNumber != nil {
		assignment.PullRequestNumber = *patch.PullRequestNumber
	}
	if patch.Experiments != nil && patch.Experiments.AssignmentContext != "" {
		assignment.Experiments = patch.Experiments
	}

	if assignment.ETag != nil && !ma.Assignment.ETag.Equals(*assignment.ETag) {
		return ErrPreconditionFailedOnUpdate
	}
	assignment.ETag = newETag()

	ma.Assignment = assignment

	return nil
}

func (m *MemoryStore) DeleteAssignment(ctx context.Context, owner, repo int64, id string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	_, ok := m.assignments[owner][repo][id]
	if !ok {
		return errors.New("assignment not found")
	}
	delete(m.assignments[owner][repo], id)
	return nil
}

func (m *MemoryStore) GetAssignmentForPR(ctx context.Context, owner, repo int64, id *int64, number *int) (*Assignment, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	for _, assignment := range m.assignments[owner][repo] {
		if id != nil && assignment.PullRequestID == *id {
			return assignment.Assignment, nil
		}
		if number != nil && assignment.PullRequestNumber == *number {
			return assignment.Assignment, nil
		}
	}

	return nil, nil
}

func (m *MemoryStore) GetAssignmentForResource(ctx context.Context, owner, repo int64, resource AssignmentResource) (*Assignment, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	assignments := []*Assignment{}
	for _, assignment := range m.assignments[owner][repo] {
		for _, r := range assignment.Resources {
			if r.ID == resource.ID && r.Type == resource.Type {
				assignments = append(assignments, assignment.Assignment)
			}
		}
	}
	if len(assignments) == 0 {
		return nil, nil
	}
	if len(assignments) > 1 {
		return nil, errors.New("multiple assignments found for resource")
	}

	return assignments[0], nil
}

func (m *MemoryStore) DetachAssignmentResource(ctx context.Context, assignment *Assignment, resource AssignmentResource) error {
	assignment, err := m.GetAssignment(ctx, assignment.OwnerID, assignment.RepoID, assignment.ID)
	if err != nil {
		return err
	}

	remove := -1
	for i, r := range assignment.Resources {
		if r.ID == resource.ID && r.Type == resource.Type {
			remove = i
			assignment.DetachedResources = append(assignment.DetachedResources, r)
			break
		}
	}

	if remove == -1 {
		for _, r := range assignment.DetachedResources {
			if r.ID == resource.ID && r.Type == resource.Type {
				return nil // already detached
			}
		}
		return errors.New("resource not found in assignment")
	}

	assignment.Resources = slices.Delete(assignment.Resources, remove, remove+1)
	_, err = m.UpdateAssignment(ctx, assignment)

	if err != nil {
		return err
	}

	return nil
}

func (m *MemoryStore) DetachAssignmentResources(ctx context.Context, assignment *Assignment) error {
	assignment, err := m.GetAssignment(ctx, assignment.OwnerID, assignment.RepoID, assignment.ID)
	if err != nil {
		return err
	}

	assignment.DetachedResources = append(assignment.DetachedResources, assignment.Resources...)
	assignment.Resources = []AssignmentResource{}

	_, err = m.UpdateAssignment(ctx, assignment)
	if err != nil {
		return err
	}

	return nil
}

func (m *MemoryStore) GetJobsForAssignment(ctx context.Context, assignment *Assignment) ([]*Job, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	jobs := []*Job{}
	for _, job := range m.jobs[assignment.OwnerID][assignment.RepoID] {
		if job.AssignmentID == assignment.ID {
			jobs = append(jobs, job.Job)
		}
	}

	return jobs, nil
}

func (m *MemoryStore) CreateJob(ctx context.Context, job *Job) (*Job, error) {
	m.mu.Lock()
	defer m.mu.Unlock()

	if job.ETag == nil {
		job.ETag = newETag()
	}

	if job.CreatedAt == 0 {
		job.CreatedAt = time.Now().Unix()
	}

	if _, ok := m.jobs[job.OwnerID]; !ok {
		m.jobs[job.OwnerID] = make(map[int64]map[string]*memoryJob)
	}

	if _, ok := m.jobs[job.OwnerID][job.RepoID]; !ok {
		m.jobs[job.OwnerID][job.RepoID] = make(map[string]*memoryJob)
	}

	m.jobs[job.OwnerID][job.RepoID][job.ID] = &memoryJob{Job: job}

	return job, nil
}

func (m *MemoryStore) GetJob(ctx context.Context, owner, repo int64, id string) (*Job, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	job, ok := m.jobs[owner][repo][id]
	if !ok {
		return nil, errors.New("job not found")
	}
	return job.Job, nil
}

func (m *MemoryStore) GetJobForComputeID(ctx context.Context, owner, repo int64, computeID string) (*Job, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	for _, job := range m.jobs[owner][repo] {
		if job.ComputeID == computeID {
			return job.Job, nil
		}
	}

	return nil, nil
}

func (m *MemoryStore) GetJobForSessionID(ctx context.Context, owner, repo int64, sessionID string) (*Job, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	for _, job := range m.jobs[owner][repo] {
		if job.SessionID == sessionID {
			return job.Job, nil
		}
	}

	return nil, nil
}

func (m *MemoryStore) GetJobs(ctx context.Context, owner, repo int64) ([]*Job, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	jobs := make([]*Job, 0, len(m.jobs[owner][repo]))
	for _, job := range m.jobs[owner][repo] {
		jobs = append(jobs, job.Job)
	}
	return jobs, nil
}

func (m *MemoryStore) UpdateJob(ctx context.Context, job *Job) (*Job, error) {
	m.mu.Lock()
	defer m.mu.Unlock()

	mj, ok := m.jobs[job.OwnerID][job.RepoID][job.ID]
	if !ok {
		return nil, errors.New("job not found")
	}

	if job.ETag != nil && !mj.Job.ETag.Equals(*job.ETag) {
		return nil, ErrPreconditionFailedOnUpdate
	}
	job.ETag = newETag()

	mj.Job = job
	return job, nil
}

func (m *MemoryStore) UpsertJob(ctx context.Context, job *Job) (*Job, error) {
	j, err := m.GetJob(ctx, job.OwnerID, job.RepoID, job.ID)
	if err != nil && err.Error() != "job not found" {
		return nil, err
	}
	if j == nil {
		return m.CreateJob(ctx, job)
	}

	if job.CreatedAt == 0 {
		job.CreatedAt = time.Now().Unix()
	}

	job, err = m.UpdateJob(ctx, job)
	return job, err
}

func (m *MemoryStore) DeleteJob(ctx context.Context, owner, repo int64, id string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	_, ok := m.jobs[owner][repo][id]
	if !ok {
		return errors.New("job not found")
	}
	delete(m.jobs[owner][repo], id)
	return nil
}

func (m *MemoryStore) PatchJob(ctx context.Context, job *Job, patch JobPatch) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	mj, ok := m.jobs[job.OwnerID][job.RepoID][job.ID]
	if !ok {
		return errors.New("job not found")
	}

	if patch.Status != nil {
		job.Status = *patch.Status
	}
	if patch.ComputeID != nil {
		job.ComputeID = *patch.ComputeID
	}
	if patch.CompletedAt != nil {
		job.CompletedAt = *patch.CompletedAt
	}
	if patch.BranchName != nil {
		job.BranchName = *patch.BranchName
	}
	if patch.PullRequestID != nil {
		job.PullRequestID = *patch.PullRequestID
	}
	if patch.PullRequestNumber != nil {
		job.PullRequestNumber = *patch.PullRequestNumber
	}
	if patch.ReplyCommentID != nil {
		job.ReplyCommentID = *patch.ReplyCommentID
	}
	if patch.SessionID != nil {
		job.SessionID = *patch.SessionID
	}
	if patch.QueuedAt != nil {
		job.QueuedAt = *patch.QueuedAt
	}
	if patch.Error != nil && patch.Error.Message != "" {
		job.Error = patch.Error
	}
	if patch.Experiments != nil && patch.Experiments.AssignmentContext != "" {
		job.Experiments = patch.Experiments
	}

	if job.ETag != nil && !mj.Job.ETag.Equals(*job.ETag) {
		return ErrPreconditionFailedOnUpdate
	}
	job.ETag = newETag()

	mj.Job = job

	return nil
}

func (m *MemoryStore) RegenerateJobNonce(ctx context.Context, job *Job) (string, error) {
	m.mu.Lock()
	defer m.mu.Unlock()

	mj, ok := m.jobs[job.OwnerID][job.RepoID][job.ID]
	if !ok {
		return "", errors.New("job not found")
	}

	if !job.IsQueued() && !job.IsPending() {
		return "", errors.New("job has already launched, nonce cannot be regenerated")
	}

	// Generate a nonce and set its hash in the job only if no hash exists
	nonce := GenerateNonce()
	job.NonceHash = HashNonce(nonce)

	if job.ETag != nil && !mj.Job.ETag.Equals(*job.ETag) {
		return "", ErrPreconditionFailedOnUpdate
	}
	job.ETag = newETag()

	mj.Job = job

	return nonce, nil
}
