package jobs

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestCosmosDB_CreateJob(t *testing.T) {
	didPostJob := false
	svr := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Method == http.MethodPost {
			didPostJob = true
			require.Equal(t, "/dbs/db-id/colls/jobs/docs", r.URL.Path)

			body, err := io.ReadAll(r.Body)
			require.NoError(t, err)
			defer r.Body.Close()

			// Check that the request body contains the expected JSON
			// structure for the job
			var jobBody map[string]any
			err = json.Unmarshal(body, &jobBody)
			require.NoError(t, err)

			// Check that the job ID is present
			require.Equal(t, "test-job", jobBody["id"].(string))
		}

		w.<PERSON>riteHeader(http.StatusCreated)
		fmt.Fprint(w, `{"_rid":"test","_self":"test","_etag":"test","_ts":**********, "id":"test-job"}`)
	}))
	t.Cleanup(svr.Close)

	raw := "fakeBase64String"
	base64Str := base64.StdEncoding.EncodeToString([]byte(raw))
	connectionStr := fmt.Sprintf("AccountEndpoint=%s;AccountKey=%s", svr.URL+"/", base64Str)

	store, err := NewCosmosJobStore(&CosmosJobStoreOpts{
		ConnectionString:       connectionStr,
		DatabaseId:             "db-id",
		JobsContainerId:        "jobs",
		AssignmentsContainerId: "assignments",
	})
	require.NoError(t, err)
	require.NotNil(t, store)

	ctx := context.Background()
	j, err := store.CreateJob(ctx, &Job{
		ID: "test-job",
	})
	require.True(t, didPostJob)
	require.NoError(t, err)
	require.Equal(t, "test-job", j.ID)
}
