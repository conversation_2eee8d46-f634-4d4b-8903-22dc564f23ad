package jobs

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"slices"
	"time"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/runtime"
	"github.com/Azure/azure-sdk-for-go/sdk/azidentity"
	"github.com/Azure/azure-sdk-for-go/sdk/data/azcosmos"
)

var errMultipleItemsFoundForQuery = errors.New("more than one item found for query")

type CosmosJobsStore struct {
	assignments *azcosmos.ContainerClient
	jobs        *azcosmos.ContainerClient
}

// Ensure that CosmosJobStore implements JobStore
var _ JobsStore = &CosmosJobsStore{}

func repoPk(owner, repo int64) azcosmos.PartitionKey {
	return azcosmos.NewPartitionKey().AppendNumber(float64(owner)).AppendNumber(float64(repo))
}

func pk(owner, repo int64, id string) azcosmos.PartitionKey {
	return repoPk(owner, repo).AppendString(id)
}

type CosmosJobStoreOpts struct {
	Endpoint               string
	ConnectionString       string
	DatabaseId             string
	JobsContainerId        string
	AssignmentsContainerId string
}

func NewCosmosJobStore(opts *CosmosJobStoreOpts) (*CosmosJobsStore, error) {
	opt := &azcosmos.ClientOptions{
		EnableContentResponseOnWrite: true, // TODO(colbylwilliams): default is false, reevaluate for production
	}

	var err error
	var cosmos *azcosmos.Client

	switch {
	case opts.ConnectionString != "":
		cosmos, err = azcosmos.NewClientFromConnectionString(opts.ConnectionString, opt)
		if err != nil {
			return nil, err
		}
	case opts.Endpoint != "":
		cred, err := azidentity.NewDefaultAzureCredential(nil)
		if err != nil {
			return nil, err
		}
		cosmos, err = azcosmos.NewClient(opts.Endpoint, cred, opt)
		if err != nil {
			return nil, err
		}
	default:
		return nil, errors.New("either endpoint or connectionString must be provided")
	}

	assignments, err := cosmos.NewContainer(opts.DatabaseId, opts.AssignmentsContainerId)
	if err != nil {
		return nil, err
	}

	jobs, err := cosmos.NewContainer(opts.DatabaseId, opts.JobsContainerId)
	if err != nil {
		return nil, err
	}

	return &CosmosJobsStore{
		assignments: assignments,
		jobs:        jobs,
	}, nil
}

func (s *CosmosJobsStore) CreateAssignment(ctx context.Context, assignment *Assignment) (*Assignment, error) {
	pk := pk(assignment.OwnerID, assignment.RepoID, assignment.ID)

	if assignment.CreatedAt == 0 {
		assignment.CreatedAt = time.Now().Unix()
	}

	marshalled, err := json.Marshal(assignment)
	if err != nil {
		return nil, err
	}

	response, err := s.assignments.CreateItem(ctx, pk, marshalled, nil)
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal(response.Value, &assignment)
	if err != nil {
		return nil, err
	}

	return assignment, nil
}

func (s *CosmosJobsStore) GetAssignment(ctx context.Context, owner, repo int64, id string) (*Assignment, error) {
	pk := pk(owner, repo, id)

	response, err := s.assignments.ReadItem(ctx, pk, id, nil)
	if err != nil {
		return nil, err
	}

	var item *Assignment
	err = json.Unmarshal(response.Value, &item)
	if err != nil {
		return nil, err
	}

	return item, nil
}

func (s *CosmosJobsStore) GetAssignments(ctx context.Context, owner, repo int64) ([]*Assignment, error) {
	opt := azcosmos.QueryOptions{
		QueryParameters: []azcosmos.QueryParameter{
			{Name: "@owner_id", Value: owner},
			{Name: "@repo_id", Value: repo},
		},
	}

	query := "SELECT * FROM c WHERE c.owner_id = @owner_id AND c.repo_id = @repo_id"
	pager := s.assignments.NewQueryItemsPager(query, azcosmos.NewPartitionKey(), &opt)

	items, err := getAllItemsFromPager[Assignment](ctx, pager)
	if err != nil {
		return nil, fmt.Errorf("failed to get assignments: %w", err)
	}
	return items, nil
}

func (s *CosmosJobsStore) UpdateAssignment(ctx context.Context, assignment *Assignment) (*Assignment, error) {
	pk := pk(assignment.OwnerID, assignment.RepoID, assignment.ID)

	marshalled, err := json.Marshal(assignment)
	if err != nil {
		return nil, err
	}

	response, err := s.assignments.UpsertItem(ctx, pk, marshalled, nil)
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal(response.Value, &assignment)
	if err != nil {
		return nil, err
	}

	return assignment, nil
}

func (s *CosmosJobsStore) PatchAssignment(ctx context.Context, assignment *Assignment, patch AssignmentPatch) error {
	pk := pk(assignment.OwnerID, assignment.RepoID, assignment.ID)
	patchOps := azcosmos.PatchOperations{}

	if patch.PullRequestID != nil {
		patchOps.AppendAdd("/pull_request_id", *patch.PullRequestID)
	}
	if patch.PullRequestNumber != nil {
		patchOps.AppendAdd("/pull_request_number", *patch.PullRequestNumber)
	}
	if patch.Experiments != nil && patch.Experiments.AssignmentContext != "" {
		patchOps.AppendAdd("/experiments", *patch.Experiments)
	}

	response, err := s.assignments.PatchItem(ctx, pk, assignment.ID, patchOps, &azcosmos.ItemOptions{
		EnableContentResponseOnWrite: true,
		IfMatchEtag:                  assignment.ETag,
	})
	if err != nil {
		var responseErr *azcore.ResponseError
		if errors.As(err, &responseErr) && responseErr.StatusCode == http.StatusPreconditionFailed {
			return ErrPreconditionFailedOnUpdate
		}
		return err
	}

	err = json.Unmarshal(response.Value, &assignment)
	if err != nil {
		return err
	}

	return nil
}

func (s *CosmosJobsStore) DeleteAssignment(ctx context.Context, owner, repo int64, id string) error {
	pk := pk(owner, repo, id)

	_, err := s.assignments.DeleteItem(ctx, pk, id, nil)
	if err != nil {
		return err
	}

	return nil
}

func getIssueQuery(base string, owner, repo int64, id *int64, number *int) (string, azcosmos.QueryOptions, error) {
	opt := azcosmos.QueryOptions{
		QueryParameters: []azcosmos.QueryParameter{
			{Name: "@owner_id", Value: owner},
			{Name: "@repo_id", Value: repo},
		},
	}
	if id == nil && number == nil {
		return "", opt, errors.New("either id or number must be provided")
	}

	// construct a query that checks the org/repo specific partition
	query := "SELECT * FROM c WHERE c.owner_id = @owner_id AND c.repo_id = @repo_id AND ("
	if id != nil {
		opt.QueryParameters = append(opt.QueryParameters, azcosmos.QueryParameter{Name: "@issue_id", Value: id})
		query += "c." + base + "_id = @issue_id"
	}
	if number != nil {
		opt.QueryParameters = append(opt.QueryParameters, azcosmos.QueryParameter{Name: "@issue_number", Value: number})
		if id != nil {
			query += " OR "
		}
		query += "c." + base + "_number = @issue_number"
	}
	query += ")"

	return query, opt, nil
}

func (s *CosmosJobsStore) GetAssignmentForPR(ctx context.Context, owner, repo int64, id *int64, number *int) (*Assignment, error) {
	query, opt, err := getIssueQuery("pull_request", owner, repo, id, number)
	if err != nil {
		return nil, err
	}

	pager := s.assignments.NewQueryItemsPager(query, azcosmos.NewPartitionKey(), &opt)

	item, err := getSingleItemFromPager[Assignment](ctx, pager)
	if err != nil {
		if errors.Is(err, errMultipleItemsFoundForQuery) {
			return nil, errors.New("multiple assignments found for PR")
		}
		return nil, fmt.Errorf("failed to get assignment for PR: %w", err)
	}

	return item, nil
}

func getResourceQuery(owner, repo int64, resource AssignmentResource) (string, azcosmos.QueryOptions, error) {
	opt := azcosmos.QueryOptions{
		QueryParameters: []azcosmos.QueryParameter{
			{Name: "@owner_id", Value: owner},
			{Name: "@repo_id", Value: repo},
			{Name: "@resource_id", Value: resource.ID},
		},
	}

	if resource.ID == "" {
		return "", opt, errors.New("resource ID must be provided")
	}

	// construct a query that checks the org/repo specific partition and the resource ID
	query := "SELECT VALUE c FROM c WHERE c.owner_id = @owner_id AND c.repo_id = @repo_id AND EXISTS(SELECT VALUE r FROM r IN c.resources WHERE r.id = @resource_id"

	if resource.Type != "" {
		opt.QueryParameters = append(opt.QueryParameters, azcosmos.QueryParameter{Name: "@resource_type", Value: resource.Type})
		query += " AND r.type = @resource_type"
	}
	query += ")"
	return query, opt, nil
}

func (s *CosmosJobsStore) GetAssignmentForResource(ctx context.Context, owner, repo int64, resource AssignmentResource) (*Assignment, error) {
	query, opt, err := getResourceQuery(owner, repo, resource)
	if err != nil {
		return nil, err
	}

	pager := s.assignments.NewQueryItemsPager(query, azcosmos.NewPartitionKey(), &opt)

	item, err := getSingleItemFromPager[Assignment](ctx, pager)
	if err != nil {
		if errors.Is(err, errMultipleItemsFoundForQuery) {
			return nil, ErrMultipleAssignmentsForResource
		}
		return nil, fmt.Errorf("failed to get assignment for resource: %w", err)
	}

	return item, nil
}

func (s *CosmosJobsStore) DetachAssignmentResource(ctx context.Context, assignment *Assignment, resource AssignmentResource) error {
	assignment, err := s.GetAssignment(ctx, assignment.OwnerID, assignment.RepoID, assignment.ID)
	if err != nil {
		return err
	}

	remove := -1
	for i, r := range assignment.Resources {
		if r.ID == resource.ID && r.Type == resource.Type {
			remove = i
			assignment.DetachedResources = append(assignment.DetachedResources, r)
			break
		}
	}

	if remove == -1 {
		for _, r := range assignment.DetachedResources {
			if r.ID == resource.ID && r.Type == resource.Type {
				return nil // already detached
			}
		}
		return fmt.Errorf("resource %s not found in assignment %s", resource.ID, assignment.ID)
	}

	assignment.Resources = slices.Delete(assignment.Resources, remove, remove+1)
	_, err = s.UpdateAssignment(ctx, assignment)

	if err != nil {
		return err
	}

	return nil
}

func (s *CosmosJobsStore) DetachAssignmentResources(ctx context.Context, assignment *Assignment) error {
	assignment, err := s.GetAssignment(ctx, assignment.OwnerID, assignment.RepoID, assignment.ID)
	if err != nil {
		return err
	}

	assignment.DetachedResources = append(assignment.DetachedResources, assignment.Resources...)
	assignment.Resources = []AssignmentResource{}

	_, err = s.UpdateAssignment(ctx, assignment)
	if err != nil {
		return err
	}

	return nil
}

func (s *CosmosJobsStore) GetJobsForAssignment(ctx context.Context, assignment *Assignment) ([]*Job, error) {
	opt := azcosmos.QueryOptions{
		QueryParameters: []azcosmos.QueryParameter{
			{Name: "@owner_id", Value: assignment.OwnerID},
			{Name: "@repo_id", Value: assignment.RepoID},
			{Name: "@assignment_id", Value: assignment.ID},
		},
	}
	query := "SELECT * FROM c WHERE c.owner_id = @owner_id AND c.repo_id = @repo_id AND c.assignment_id = @assignment_id"
	pager := s.jobs.NewQueryItemsPager(query, azcosmos.NewPartitionKey(), &opt)

	items, err := getAllItemsFromPager[Job](ctx, pager)
	if err != nil {
		return nil, fmt.Errorf("failed to get jobs for assignment: %w", err)
	}

	return items, nil
}

func (s *CosmosJobsStore) CreateJob(ctx context.Context, job *Job) (*Job, error) {
	pk := pk(job.OwnerID, job.RepoID, job.ID)

	if job.CreatedAt == 0 {
		job.CreatedAt = time.Now().Unix()
	}

	marshalled, err := json.Marshal(job)
	if err != nil {
		return nil, err
	}

	response, err := s.jobs.CreateItem(ctx, pk, marshalled, nil)
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal(response.Value, &job)
	if err != nil {
		return nil, err
	}

	return job, nil
}

func (s *CosmosJobsStore) GetJob(ctx context.Context, owner, repo int64, id string) (*Job, error) {
	pk := pk(owner, repo, id)

	response, err := s.jobs.ReadItem(ctx, pk, id, nil)
	if err != nil {
		return nil, err
	}

	var item *Job
	err = json.Unmarshal(response.Value, &item)
	if err != nil {
		return nil, err
	}

	return item, nil
}

func (s *CosmosJobsStore) GetJobForComputeID(ctx context.Context, owner, repo int64, computeID string) (*Job, error) {
	opt := azcosmos.QueryOptions{
		QueryParameters: []azcosmos.QueryParameter{
			{Name: "@owner_id", Value: owner},
			{Name: "@repo_id", Value: repo},
			{Name: "@compute_id", Value: computeID},
		},
	}

	query := "SELECT * FROM c WHERE c.owner_id = @owner_id AND c.repo_id = @repo_id AND c.compute_id = @compute_id"
	pager := s.jobs.NewQueryItemsPager(query, azcosmos.NewPartitionKey(), &opt)

	item, err := getSingleItemFromPager[Job](ctx, pager)
	if err != nil {
		if errors.Is(err, errMultipleItemsFoundForQuery) {
			return nil, errors.New("multiple jobs found for compute ID")
		}
		return nil, fmt.Errorf("failed to get job for compute ID: %w", err)
	}

	return item, nil
}

func (s *CosmosJobsStore) GetJobForSessionID(ctx context.Context, owner, repo int64, sessionID string) (*Job, error) {
	opt := azcosmos.QueryOptions{
		QueryParameters: []azcosmos.QueryParameter{
			{Name: "@owner_id", Value: owner},
			{Name: "@repo_id", Value: repo},
			{Name: "@session_id", Value: sessionID},
		},
	}

	query := "SELECT * FROM c WHERE c.owner_id = @owner_id AND c.repo_id = @repo_id AND c.session_id = @session_id"
	pager := s.jobs.NewQueryItemsPager(query, azcosmos.NewPartitionKey(), &opt)

	item, err := getSingleItemFromPager[Job](ctx, pager)
	if err != nil {
		if errors.Is(err, errMultipleItemsFoundForQuery) {
			return nil, errors.New("multiple jobs found for session ID")
		}
		return nil, fmt.Errorf("failed to get job for session ID: %w", err)
	}

	return item, nil
}

func (s *CosmosJobsStore) GetJobs(ctx context.Context, owner, repo int64) ([]*Job, error) {
	opt := azcosmos.QueryOptions{
		QueryParameters: []azcosmos.QueryParameter{
			{Name: "@owner_id", Value: owner},
			{Name: "@repo_id", Value: repo},
		},
	}

	query := "SELECT * FROM c WHERE c.owner_id = @owner_id AND c.repo_id = @repo_id"
	pager := s.jobs.NewQueryItemsPager(query, azcosmos.NewPartitionKey(), &opt)

	items, err := getAllItemsFromPager[Job](ctx, pager)
	if err != nil {
		return nil, fmt.Errorf("failed to get jobs: %w", err)
	}

	return items, nil
}

func (s *CosmosJobsStore) UpdateJob(ctx context.Context, job *Job) (*Job, error) {
	pk := pk(job.OwnerID, job.RepoID, job.ID)

	marshalled, err := json.Marshal(job)
	if err != nil {
		return nil, err
	}

	opts := &azcosmos.ItemOptions{EnableContentResponseOnWrite: true}
	if job.ETag != nil {
		opts.IfMatchEtag = job.ETag
	}

	response, err := s.jobs.UpsertItem(ctx, pk, marshalled, opts)
	if err != nil {
		var responseErr *azcore.ResponseError
		if errors.As(err, &responseErr) && responseErr.StatusCode == http.StatusPreconditionFailed {
			return nil, ErrPreconditionFailedOnUpdate
		}
		return nil, err
	}

	err = json.Unmarshal(response.Value, &job)
	if err != nil {
		return nil, err
	}

	return job, nil
}

func (s *CosmosJobsStore) UpsertJob(ctx context.Context, job *Job) (*Job, error) {
	pk := pk(job.OwnerID, job.RepoID, job.ID)

	if job.CreatedAt == 0 {
		job.CreatedAt = time.Now().Unix()
	}

	marshalled, err := json.Marshal(job)
	if err != nil {
		return nil, err
	}

	opts := &azcosmos.ItemOptions{EnableContentResponseOnWrite: true}
	if job.ETag != nil {
		opts.IfMatchEtag = job.ETag
	}

	response, err := s.jobs.UpsertItem(ctx, pk, marshalled, opts)
	if err != nil {
		var responseErr *azcore.ResponseError
		if errors.As(err, &responseErr) && responseErr.StatusCode == http.StatusPreconditionFailed {
			return nil, ErrPreconditionFailedOnUpdate
		}
		return nil, err
	}

	err = json.Unmarshal(response.Value, &job)
	if err != nil {
		return nil, err
	}

	return job, nil
}

func (s *CosmosJobsStore) DeleteJob(ctx context.Context, owner, repo int64, id string) error {
	pk := pk(owner, repo, id)

	_, err := s.jobs.DeleteItem(ctx, pk, id, nil)
	if err != nil {
		return err
	}

	return nil
}

func (s *CosmosJobsStore) PatchJob(ctx context.Context, job *Job, patch JobPatch) error {
	pk := pk(job.OwnerID, job.RepoID, job.ID)
	patchOps := azcosmos.PatchOperations{}

	if patch.Status != nil {
		patchOps.AppendAdd("/status", *patch.Status)
	}
	if patch.ComputeID != nil {
		patchOps.AppendAdd("/compute_id", *patch.ComputeID)
	}
	if patch.CompletedAt != nil {
		patchOps.AppendAdd("/completed_at", *patch.CompletedAt)
	}
	if patch.BranchName != nil {
		patchOps.AppendAdd("/branch_name", *patch.BranchName)
	}
	if patch.PullRequestID != nil {
		patchOps.AppendAdd("/pull_request_id", *patch.PullRequestID)
	}
	if patch.PullRequestNumber != nil {
		patchOps.AppendAdd("/pull_request_number", *patch.PullRequestNumber)
	}
	if patch.ReplyCommentID != nil {
		patchOps.AppendAdd("/reply_comment_id", *patch.ReplyCommentID)
	}
	if patch.SessionID != nil {
		patchOps.AppendAdd("/session_id", *patch.SessionID)
	}
	if patch.QueuedAt != nil {
		patchOps.AppendAdd("/queued_at", *patch.QueuedAt)
	}
	if patch.Error != nil && patch.Error.Message != "" {
		patchOps.AppendAdd("/error", *patch.Error)
	}
	if patch.Experiments != nil && patch.Experiments.AssignmentContext != "" {
		patchOps.AppendAdd("/experiments", *patch.Experiments)
	}
	if patch.Model != nil {
		patchOps.AppendAdd("/model", *patch.Model)
	}

	response, err := s.jobs.PatchItem(ctx, pk, job.ID, patchOps, &azcosmos.ItemOptions{
		EnableContentResponseOnWrite: true,
		IfMatchEtag:                  job.ETag,
	})
	if err != nil {
		var responseErr *azcore.ResponseError
		if errors.As(err, &responseErr) && responseErr.StatusCode == http.StatusPreconditionFailed {
			return ErrPreconditionFailedOnUpdate
		}
		return err
	}

	err = json.Unmarshal(response.Value, &job)
	if err != nil {
		return err
	}

	return nil
}

func (s *CosmosJobsStore) RegenerateJobNonce(ctx context.Context, job *Job) (string, error) {
	pk := pk(job.OwnerID, job.RepoID, job.ID)

	if !job.IsQueued() && !job.IsPending() {
		return "", errors.New("job has already launched, nonce cannot be regenerated")
	}

	nonce := GenerateNonce()
	nonceHash := HashNonce(nonce)

	patchOps := azcosmos.PatchOperations{}
	patchOps.AppendReplace("/nonce_hash", nonceHash)

	response, err := s.jobs.PatchItem(ctx, pk, job.ID, patchOps, &azcosmos.ItemOptions{
		EnableContentResponseOnWrite: true,
		IfMatchEtag:                  job.ETag,
	})
	if err != nil {
		var responseErr *azcore.ResponseError
		if errors.As(err, &responseErr) && responseErr.StatusCode == http.StatusPreconditionFailed {
			return "", ErrPreconditionFailedOnUpdate
		}
		return "", err
	}

	err = json.Unmarshal(response.Value, &job)
	if err != nil {
		return "", err
	}

	return nonce, nil
}

// getAllItemsFromPager retrieves all items from the pager and unmarshals them into a slice of type T.
func getAllItemsFromPager[T any](ctx context.Context, pager *runtime.Pager[azcosmos.QueryItemsResponse]) ([]*T, error) {
	var items []*T
	for pager.More() {
		response, err := pager.NextPage(ctx)
		if err != nil {
			return nil, err
		}

		for _, item := range response.Items {
			var i *T
			err = json.Unmarshal(item, &i)
			if err != nil {
				return nil, err
			}
			items = append(items, i)
		}
	}

	return items, nil
}

// getSingleItemFromPager retrieves a single item from the pager and returns it as type T.
// If multiple items are found, it returns [errMultipleItemsFoundForQuery] error.
// If no items are found, it returns nil.
func getSingleItemFromPager[T any](ctx context.Context, pager *runtime.Pager[azcosmos.QueryItemsResponse]) (*T, error) {
	items, err := getAllItemsFromPager[T](ctx, pager)
	if err != nil {
		return nil, err
	}

	switch len(items) {
	case 0:
		return nil, nil // no item found
	case 1:
		return items[0], nil // single item found
	default:
		return nil, errMultipleItemsFoundForQuery // multiple items found
	}
}
