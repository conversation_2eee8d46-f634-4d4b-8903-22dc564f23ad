package jobs

import (
	"context"
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"errors"
	"fmt"
	"time"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore"
	"github.com/github/sweagentd/internal/launcher"
	"github.com/github/sweagentd/internal/requestctx"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/google/uuid"

	sweagentEntitiesv0 "github.com/github/hydro-schemas-go/hydro/schemas/sweagentd/v0/entities"
)

var ErrMultipleAssignmentsForResource = errors.New("more than one active assignment found for resource")
var ErrPreconditionFailedOnUpdate = errors.New("failed to update, attempted to update a stale version")

type AssignmentExperiments struct {
	Features          []string                      `json:"features"`
	Flights           map[string]string             `json:"flights"`
	Configs           []AssignmentExperimentsConfig `json:"configs"`
	FlightingVersion  int                           `json:"flighting_version"`
	ImpressionID      string                        `json:"impression_id"`
	AssignmentContext string                        `json:"assignment_context"`
	// TODO (jacdavis)
	// ParameterGroups any                          `json:"parameter_groups"`
}

type AssignmentExperimentsConfig struct {
	ID         string         `json:"id"`
	Parameters map[string]any `json:"parameters"`
}

type AssignmentStatus string

const (
	AssignmentStatusActive   AssignmentStatus = "active"
	AssignmentStatusInactive AssignmentStatus = "inactive"
)

// JobNonceHeader is the header used to pass the nonce to the agent
// when the job is created. The agent will use this nonce to validate
// that the callback request comes from the right workflow run.
const JobNonceHeader = "X-GitHub-Job-Nonce"

type AssignmentResource struct {
	ID   string `json:"id"`
	Type string `json:"type"`
}

type Assignment struct {
	ID                string               `json:"id"`
	RepoID            int64                `json:"repo_id"`
	OwnerID           int64                `json:"owner_id"`
	RepoNodeID        string               `json:"repo_node_id"`
	OwnerNodeID       string               `json:"owner_node_id"`
	RepoName          string               `json:"repo_name"`
	RepoOwner         string               `json:"repo_owner"`
	PullRequestID     int64                `json:"pull_request_id"`
	PullRequestNumber int                  `json:"pull_request_number"`
	CreatedAt         int64                `json:"created_at"`
	UpdatedAt         int64                `json:"_ts,omitempty"`
	Resources         []AssignmentResource `json:"resources"`
	// DetachedResources are the resources that have been detached from the assignment.
	// This is used to track resources that originally initiated the assignment, but
	// are no longer directly associated with it. For example, unassigned issues.
	DetachedResources []AssignmentResource   `json:"detached_resources"`
	Experiments       *AssignmentExperiments `json:"experiments,omitempty"`
	ETag              *azcore.ETag           `json:"_etag,omitempty"`
}

type JobStatus string

const (
	JobStatusPending   JobStatus = "pending"
	JobStatusQueued    JobStatus = "queued"
	JobStatusRunning   JobStatus = "running"
	JobStatusCompleted JobStatus = "completed"
	JobStatusFailed    JobStatus = "failed"
	JobStatusCancelled JobStatus = "cancelled"
)

// JobResponseType is the type of response that the job will produce.
// Right now there's only one option here, but there will be more.
// Other examples includes chat or just hydro
type JobResponseType string

const (
	JobResponseTypeDefault     JobResponseType = ""
	JobResponseTypePullRequest JobResponseType = "pull_request"
)

func (s JobStatus) IsTerminal() bool {
	return s == JobStatusCompleted || s == JobStatusFailed || s == JobStatusCancelled
}

type Job struct {
	ID          string `json:"id"`
	RepoID      int64  `json:"repo_id"`
	OwnerID     int64  `json:"owner_id"`
	RepoNodeID  string `json:"repo_node_id"`
	OwnerNodeID string `json:"owner_node_id"`
	RepoName    string `json:"repo_name"`
	RepoOwner   string `json:"repo_owner"`
	// AssignmentID is the ID of the assignment that the job is part of
	AssignmentID      string `json:"assignment_id"`
	PullRequestID     int64  `json:"pull_request_id"`
	PullRequestNumber int    `json:"pull_request_number"`
	// IssueNumber is the number of the issue that the job is associated with.
	// It is needed to manage the issue state and to create comments.
	IssueNumber int `json:"issue_number,omitzero"`
	// ComputeID identifies the compute instance that the job runs/ran on.
	// For GitHub Actions, this is the ID of the WorkflowRun.
	ComputeID        string                   `json:"compute_id"`
	SessionID        string                   `json:"session_id"`
	EventType        EventType                `json:"event_type"`
	EventURL         string                   `json:"event_url"`
	EventIdentifiers []string                 `json:"event_identifiers"`
	Launcher         launcher.LauncherType    `json:"launcher"`
	Status           JobStatus                `json:"status"`
	CreatedAt        int64                    `json:"created_at"`
	LaunchedAt       int64                    `json:"launched_at"`
	QueuedAt         int64                    `json:"queued_at"`
	CompletedAt      int64                    `json:"completed_at"`
	UpdatedAt        int64                    `json:"_ts,omitempty"`
	Action           AgentAction              `json:"action"`
	Model            string                   `json:"model"`
	BranchName       string                   `json:"branch_name"`
	BaseCommit       string                   `json:"base_commit"`
	ProblemStatement ProblemStatement         `json:"problem_statement"`
	RunOptions       RunOptions               `json:"run_options"`
	Args             launcher.LaunchAgentOpts `json:"args"`
	// ReplyCommentID is the ID of the of the PR comment where the agent replied
	ReplyCommentID int64 `json:"reply_comment_id"`
	// ActorID is the ID of the GitHub user that triggered the copilot agent job.
	// This is the actor that will be attributed to API calls made by the agent
	// as well as git commits made by the agent. Thus, the actor must have the
	// necessary permissions to perform the required actions.
	ActorID int64 `json:"actor_id"`
	// ActorLogin is the login of the GitHub user that triggered the copilot agent
	// job. This is the actor that will be attributed to API calls made by the
	// agent as well as git commits made by the agent. Thus, the actor must have
	// the necessary permissions to perform the required actions.
	ActorLogin string `json:"actor_login"`
	// ActorAnalyticsTrackingID is the tracking ID of the actor that triggered the copilot agent job.
	ActorAnalyticsTrackingID string `json:"actor_analytics_tracking_id,omitempty"`
	// ReviewIDs is a list of review IDs that can be considered handled/to
	// be handled) by the agent as part of this job.
	// Individual review comments are tracked as part of their parent review.
	ReviewIDs []int64 `json:"review_ids"`
	// CommentIDs is a list of comment IDs that can be considered handled/to
	// be handled) by the agent as part of this job.
	CommentIDs []int64 `json:"comment_ids"`
	// NonceHash is a SHA256 hash of a random value generated when the job is created and used to
	// validate callback requests come from the right workflow run
	NonceHash   string                 `json:"nonce_hash"`
	Error       *JobError              `json:"error,omitempty"`
	Experiments *AssignmentExperiments `json:"experiments,omitempty"`
	ETag        *azcore.ETag           `json:"_etag,omitempty"`
}

type JobError struct {
	Message string `json:"message"`
	// Optional HTTP status code that is representative of the class of error that was encountered
	ResponseStatusCode int `json:"response_status_code,omitempty"`
}

type RunOptions struct {
	RunName                  string          `json:"run_name,omitempty"`                   // Optional title to use for runs
	ResponseType             JobResponseType `json:"response_type,omitempty"`              // Optional type of response to use for the job: E.g. Pull request
	ResponseTitlePlaceholder string          `json:"response_title_placeholder,omitempty"` // Optional title to use to describe the job before AI updates it
	ResponseBodyPlaceholder  string          `json:"response_body_placeholder,omitempty"`  // Optional placeholder to use for the response body before AI updates it
	ResponseBodySuffix       string          `json:"response_body_suffix,omitempty"`       // Optional suffix to use for any AI response
	ResponseLabels           []string        `json:"response_labels,omitempty"`            // Optional labels that should in the response - typically PR labels
}

type ProblemStatement struct {
	Content           string                            `json:"content"`
	CommitCount       int                               `json:"commit_count"`
	ContentFilterMode ProblemStatementContentFilterMode `json:"content_filter_mode"`
}

type ProblemStatementContentFilterMode string

const (
	ProblemStatementContentFilterModeNone             ProblemStatementContentFilterMode = "none"
	ProblemStatementContentFilterModeMarkdown         ProblemStatementContentFilterMode = "markdown"
	ProblemStatementContentFilterModeHiddenCharacters ProblemStatementContentFilterMode = "hidden_characters"
)

// TODO we probably just need an foreign key/identifier to Job.ID, we probably have duplicated values inside JobUpdate
type JobUpdate struct {
	JobID       string          `json:"job_id"`
	Kind        AgentUpdateKind `json:"kind"`
	Action      AgentAction     `json:"action"`
	CreatedAt   time.Time       `json:"created_at"`
	RepoID      int64           `json:"repo_id"`
	RepoName    string          `json:"repo_name"`
	Repo        string          `json:"repo"` // the is in the form of owner/repo
	RepoOwnerID int64           `json:"repo_owner_id"`
	RepoOwner   string          `json:"repo_owner"`
	Content     string          `json:"content"` // see runtime/src/types.ts
}

type AgentUpdateKind string

const (
	AgentUpdateKindProgress      AgentUpdateKind = "progress"
	AgentUpdateKindPartialResult AgentUpdateKind = "partialResult"
	AgentUpdateKindCommentReply  AgentUpdateKind = "commentReply"
	AgentUpdateKindResult        AgentUpdateKind = "result"
	AgentUpdateKindError         AgentUpdateKind = "error"
)

type EventType string

const (
	EventTypeIssueAssignment              EventType = "issue_assignment"
	EventTypePullRequestComment           EventType = "pull_request_comment"
	EventTypePullRequestReview            EventType = "pull_request_review"
	EventTypeCodeScanningAlertsAssignment EventType = "code_scanning_alerts_assignment"
	EventTypeApiCallReceived              EventType = "api_call_received"
)

type AgentAction string

const (
	// AgentActionFix is the action that the agent takes when it is triggered by
	// by initially being assigned to an issue. The agent will then create a pull
	// request, post the plan/summary as the PR body, and make the fix in the PR.
	AgentActionFix AgentAction = "fix"
	// AgentActionFixPRComment is the action that the agent takes when it is
	// triggered by an actionable comment or a review on a pull request. The agent
	// replies directly to review comments and posts the plan/summary as a comment
	// on the pull request.
	AgentActionFixPRComment AgentAction = "fix-pr-comment"
)

// AssignmentPatch is a struct that can be used to patch specific fields on an
// assignment in the database. This is useful for updating one or two fields efficiently.
type AssignmentPatch struct {
	PullRequestID     *int64
	PullRequestNumber *int
	Experiments       *AssignmentExperiments
}

// JobPatch is a struct that can be used to patch specific fields on a job in the database.
// This is useful for updating one or two fields efficiently.
type JobPatch struct {
	Status            *JobStatus
	ComputeID         *string
	CompletedAt       *int64
	BranchName        *string
	PullRequestID     *int64
	PullRequestNumber *int
	ReplyCommentID    *int64
	SessionID         *string
	QueuedAt          *int64
	Error             *JobError
	Experiments       *AssignmentExperiments
	Model             *string
}

type JobsStore interface {
	CreateAssignment(ctx context.Context, assignment *Assignment) (*Assignment, error)
	GetAssignment(ctx context.Context, owner, repo int64, id string) (*Assignment, error)
	GetAssignments(ctx context.Context, owner, repo int64) ([]*Assignment, error)
	UpdateAssignment(ctx context.Context, assignment *Assignment) (*Assignment, error)

	// PatchAssignment patches specific fields on the assignment in the database.
	// This is useful for updating one or two fields efficiently.
	PatchAssignment(ctx context.Context, assignment *Assignment, patch AssignmentPatch) error
	DeleteAssignment(ctx context.Context, owner, repo int64, id string) error

	GetAssignmentForResource(ctx context.Context, owner, repo int64, resource AssignmentResource) (*Assignment, error)
	DetachAssignmentResource(ctx context.Context, assignment *Assignment, resource AssignmentResource) error
	DetachAssignmentResources(ctx context.Context, assignment *Assignment) error

	// GetAssignmentForPR returns the assignment for the pull request id and/or number.
	//
	// Note: As far as the GitHub API is concerned, every pull request is an issue, but not every issue
	// is a pull request. Some PR related webhooks (e.g. issue_comment) will use an issue field to
	// represent the PR. The issue id however does NOT match the id of the PR, but the numbers do.
	//
	// The database is partitioned hierarchically by owner -> repo -> assignments, so we can guarantee
	// both the pr id and number will be unique within the partition. There is no difference in cost/speed
	// of querying id vs. querying id and number.
	GetAssignmentForPR(ctx context.Context, owner, repo int64, id *int64, number *int) (*Assignment, error)

	GetJobsForAssignment(ctx context.Context, assignment *Assignment) ([]*Job, error)
	GetJobForComputeID(ctx context.Context, owner, repo int64, computeID string) (*Job, error)
	GetJobForSessionID(ctx context.Context, owner, repo int64, sessionID string) (*Job, error)

	CreateJob(ctx context.Context, job *Job) (*Job, error)
	GetJob(ctx context.Context, owner, repo int64, id string) (*Job, error)
	GetJobs(ctx context.Context, owner, repo int64) ([]*Job, error)
	UpdateJob(ctx context.Context, job *Job) (*Job, error)
	UpsertJob(ctx context.Context, job *Job) (*Job, error)

	// PatchJob patches specific fields on the job in the database.
	// This is useful for updating one or two fields efficiently.
	PatchJob(ctx context.Context, job *Job, patch JobPatch) error
	DeleteJob(ctx context.Context, owner, repo int64, id string) error

	// RegenerateJobNonce (re)generates the job nonce, saves the nonce_hash to
	// job database, and returns the new nonce.
	//
	// Note: The nonce must not change once the agent has been launched, so this
	// should only be after the job is created and before the agent is launched.
	//
	// If the job is not in a pending or queued state, this will return an error.
	RegenerateJobNonce(ctx context.Context, job *Job) (string, error)
}

func NewAssignment(ownerID, repoID int64, ownerNodeID, repoNodeID, ownerName, repoName string, resources ...AssignmentResource) *Assignment {
	return &Assignment{
		ID:          GenerateAssignmentID(ownerID, repoID),
		RepoID:      repoID,
		OwnerID:     ownerID,
		RepoNodeID:  repoNodeID,
		OwnerNodeID: ownerNodeID,
		RepoName:    repoName,
		RepoOwner:   ownerName,
		Resources:   resources,
	}
}

func (a *Assignment) AddDetachedResources(resources ...AssignmentResource) {
	a.DetachedResources = append(a.DetachedResources, resources...)
}

// NewJob creates a new pending job for the assignment.
func (a *Assignment) NewJob(action AgentAction, user *requestctx.UserInfo, baseCommit string) *Job {
	return &Job{
		ID:                       GenerateJobID(a.OwnerID, a.RepoID),
		AssignmentID:             a.ID,
		RepoID:                   a.RepoID,
		OwnerID:                  a.OwnerID,
		RepoNodeID:               a.RepoNodeID,
		OwnerNodeID:              a.OwnerNodeID,
		RepoName:                 a.RepoName,
		RepoOwner:                a.RepoOwner,
		PullRequestID:            a.PullRequestID,
		PullRequestNumber:        a.PullRequestNumber,
		Status:                   JobStatusPending,
		Action:                   action,
		Args:                     launcher.LaunchAgentOpts{},
		ActorID:                  int64(user.ID),
		ActorLogin:               user.Login,
		ActorAnalyticsTrackingID: user.TrackingID,
		BaseCommit:               baseCommit,
		Experiments:              a.Experiments,
	}
}

func generateRandomHex(n int) string {
	bytes := make([]byte, n)
	if _, err := rand.Read(bytes); err != nil {
		return "12345"
	}
	return hex.EncodeToString(bytes)
}

func GenerateAssignmentID(ownerID, repoID int64) string {
	uid := uuid.New()
	return fmt.Sprintf("%d-%d-%s", ownerID, repoID, uid)
}

func GenerateJobID(ownerID, repoID int64) string {
	uid := uuid.New()
	return fmt.Sprintf("%d-%d-%s", ownerID, repoID, uid)
}

func GenerateNonce() string {
	return generateRandomHex(32) // 64 characters as hex string
}

func HashNonce(nonce string) string {
	hasher := sha256.New()
	hasher.Write([]byte(nonce))
	return hex.EncodeToString(hasher.Sum(nil))
}

func (j *Job) IsQueued() bool {
	return j.Status == JobStatusQueued
}

func (j *Job) IsPending() bool {
	return j.Status == JobStatusPending
}

func (j *Job) IsRunning() bool {
	return j.Status == JobStatusRunning
}

// IsTerminal returns true if the job is terminal (completed, failed, or cancelled).
func (j *Job) IsTerminal() bool {
	return j.Status.IsTerminal()
}

func HasNonTerminalJobs(jobs []*Job) bool {
	for _, j := range jobs {
		if !j.IsTerminal() {
			return true
		}
	}
	return false
}

func HasRunningJobs(jobs []*Job) (bool, string) {
	for _, j := range jobs {
		if j.Status == JobStatusRunning {
			return true, j.ID
		}
	}
	return false, ""
}

func ToHydro(job *Job) *sweagentEntitiesv0.Job {
	return &sweagentEntitiesv0.Job{
		CreatedAt:                timestamppb.New(time.Unix(job.CreatedAt, 0)),
		JobId:                    job.ID,
		RepositoryId:             job.RepoID,
		OwnerId:                  job.OwnerID,
		ActorId:                  job.ActorID,
		PullRequestId:            job.PullRequestID,
		WorkflowRunId:            job.ComputeID,
		SessionId:                job.SessionID,
		ActorAnalyticsTrackingId: job.ActorAnalyticsTrackingID,
	}
}
