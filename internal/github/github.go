package github

import (
	"archive/zip"
	"bytes"
	"context"
	_ "embed"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"slices"
	"strings"
	"time"

	"github.com/bradleyfalzon/ghinstallation/v2"
	"github.com/github/github-telemetry-go/kvp"
	"github.com/github/go-auth/hmac"
	"github.com/github/go-http/v2/middleware/headers"
	"github.com/github/go-http/v2/middleware/requestid"
	mark "github.com/github/go-mark"
	"github.com/github/sweagentd/internal/config"
	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/githubtwirp"
	"github.com/github/sweagentd/internal/observability"
	"github.com/github/sweagentd/internal/requestctx"
	gg "github.com/google/go-github/v72/github"
	"github.com/google/uuid"
	"github.com/shurcooL/githubv4"
	"golang.org/x/oauth2"
)

// This is the prefix used by Copilot coding agent when creating branches, e.g. copilot/fix-123
// If a branch with the same name as this prefix exists, Copilot coding agent can't create a new branch with the prefix.
// There are security implications like only allowing Copilot to push to branches with this prefix.
// If this constant needs to be changed, ensure the security guardrails are updated too.
// See https://github.com/github/sweagentd/issues/124 for details.
const CopilotBranchPrefix = "copilot"

// List of branch protection rules that can prevent Copilot from pushing code
const (
	RuleRequireGitHubActions = "GitHub Actions is disabled for this repository. To learn how to enable GitHub Actions, head to the [GitHub Docs](https://gh.io/copilot-coding-agent-enable-actions)."
	RuleCopilotBranchExists  = "A branch named `copilot` already exists. To learn how to delete a branch, head to the [GitHub Docs](https://gh.io/copilot-coding-agent-delete-copilot-branch)."
	RuleBlockCreation        = "Branch creation is blocked"
	RuleRequirePullRequest   = "Changes must be made through a pull request"
	RuleRequireSignatures    = "Commits must have verified signatures"
	RuleBranchNamePattern    = "Branch name restrictions apply"
)

// Limits on field sizes
const (
	MaxPullRequestTitleLength = 255
	MaxRunNameLength          = 255
	MaxPullRequestBodyLength  = 65535 // 64 KB
)

func NewGitHubAPIError(resp *gg.Response, msg string, err error) *GitHubAPIError {
	return &GitHubAPIError{resp, msg, err}
}

type GitHubAPIError struct {
	Response *gg.Response
	Message  string
	Err      error
}

func (e *GitHubAPIError) Error() string {
	if e.Err == nil {
		if e.Message != "" {
			return e.Message
		}
		return "unknown github api error"
	}

	var err error
	if ue, ok := e.Err.(*url.Error); ok {
		// remove http method and url from the error message
		err = ue.Unwrap()
	} else {
		err = e.Err
	}

	if e.Message != "" {
		return fmt.Sprintf("%s: %v", e.Message, err)
	}

	return err.Error()
}

func (e *GitHubAPIError) RequestID() string {
	if e.Response != nil {
		return e.Response.Header.Get(headers.GitHubRequestID)
	}
	return ""
}

func (e *GitHubAPIError) StatusCode() int {
	if e.Response != nil {
		return e.Response.StatusCode
	}
	return 0
}

type ClientFactoryInterface interface {
	NewClient(token string) (ClientInterface, error)
	NewClientFromRepo(ctx context.Context, repoId, repoOwnerId int64) (ClientInterface, error)
	NewClientFromRepoName(ctx context.Context, repoName, ownerName string) (ClientInterface, error)
}

// ClientFactory is a factory for creating GitHub clients, using
// the given base URL (ex: http://api.github.localhost, https://api.github.com).
type ClientFactory struct {
	baseUrl                    *url.URL
	baseInternalURL            *url.URL
	dynamicWorkflowsHmacSecret string
	ffClient                   featureflags.Client
	githubApp                  *GitHubApp
	githubCommitLogin          string
	githubCommitEmail          string
	obsv                       observability.Exporters
	environment                config.Environment
}

const CopilotEnvironmentName string = "copilot"

//go:embed feedback_tip.md
var FeedbackTip string

//go:embed best_practices_tip.md
var BestPracticesTip string

//go:embed oswe_experiment_note.md
var OsweExperimentNote string

//go:embed onboarding_tip.md
var OnboardingTip string

//go:embed onboarding_prompt.md
var OnboardingPrompt string

type ClientInterface interface {
	Token() string
	GenerateBranchName(ctx context.Context, ownerLogin string, repoName string, branchName string) (string, error)
	CheckIfBranchExists(ctx context.Context, ownerLogin string, repoName string, branchName string) (bool, error)
	CreateNewEmptyBranch(ctx context.Context, ownerLogin, repoName, baseBranchName, branchName string) (*gg.Reference, error)
	CreateEmptyCommit(ctx context.Context, ownerLogin, repoName, branchName string) (*gg.Commit, error)
	CreatePullRequest(ctx context.Context, ownerLogin, repoName string, opts *gg.NewPullRequest) (*gg.PullRequest, error)
	GetRepository(ctx context.Context, ownerLogin, repoName string) (*gg.Repository, error)
	GetRepositoryById(ctx context.Context, id int64) (*gg.Repository, error)
	GetIssue(ctx context.Context, ownerLogin, repoName string, number int) (*gg.Issue, error)
	AssignToIssue(ctx context.Context, ownerLogin, repoName string, issueNumber int, assignee string) error
	GetPullRequest(ctx context.Context, repoID int64, number int) (*gg.PullRequest, error)
	UpdatePullRequest(ctx context.Context, repoID int64, pr *gg.PullRequest) (*gg.PullRequest, error)
	RequestReviewers(ctx context.Context, ownerLogin, repoName string, number int, reviewers []string) error
	GetComment(ctx context.Context, owner, repo string, commentID int64) (*gg.IssueComment, error)
	CreateComment(ctx context.Context, repoID int64, issueOrPRNumber int, body string) (*gg.IssueComment, error)
	CreateCommentInReplyTo(ctx context.Context, owner, repo string, prNumber int, body string, commentID int64) (*gg.PullRequestComment, error)
	UpdateComment(ctx context.Context, owner, repo string, commentID int64, body string) (*gg.IssueComment, error)
	MinimizeComment(ctx context.Context, nodeID string, classifier GQLReportedContentClassifier) error
	UnminimizeComment(ctx context.Context, nodeID string) error
	MarkPullRequestReadyForReview(ctx context.Context, prNodeID string) error
	ConvertPullRequestToDraft(ctx context.Context, prNodeID string) error
	AddReaction(ctx context.Context, nodeID string, content GQLReactionContent) error
	RemoveReaction(ctx context.Context, nodeID string, content GQLReactionContent) error
	RemoveReactionFromIssue(ctx context.Context, owner, repo string, number int) error
	GetWorkflowRunByID(ctx context.Context, ownerLogin, repoName string, runID int64) (*gg.WorkflowRun, error)
	CancelWorkflowRunByID(ctx context.Context, ownerLogin, repoName string, runID int64) error
	GetWorkflowRunLogs(ctx context.Context, logsURL string) (string, error)
	CreateDynamicWorkflowRun(ctx context.Context, owner, repo string, repoID int64, opts *NewDynamicWorkflowRun) (*DynamicWorkflowRun, error)
	ListWorkflowJobs(ctx context.Context, owner, repo string, runID int64) ([]*gg.WorkflowJob, error)
	GetWorkflowJobByID(ctx context.Context, owner, repo string, jobID int64) (*gg.WorkflowJob, error)
	ListCheckRunAnnotations(ctx context.Context, owner, repo string, jobID int64) ([]*gg.CheckRunAnnotation, error)
	AddLabelsToIssueOrPR(ctx context.Context, ownerLogin string, repoName string, issueOrPRNumber int, labels []string) error
	GetUserById(ctx context.Context, id uint64) (*gg.User, error)
	DeleteRepo(ctx context.Context, owner, repo string) error
	CreateRepo(ctx context.Context, org, name string, autoinit bool) (*gg.Repository, error)
	CreateIssue(ctx context.Context, owner, repo, title, body string) (*gg.Issue, error)
	GetPullRequests(ctx context.Context, owner, repo string, opts *gg.PullRequestListOptions) ([]*gg.PullRequest, error)
	GetContents(ctx context.Context, owner, repo, path string, opts *gg.RepositoryContentGetOptions) (fileContent *gg.RepositoryContent, dirContent []*gg.RepositoryContent, err error)
	UpdateFile(ctx context.Context, owner, repo, path string, opts *gg.RepositoryContentFileOptions) (*gg.RepositoryContentResponse, error)
	GetAuthenticatedUser(ctx context.Context) (*gg.User, error)
	UserIsCopilot(login string) bool
	GetCopilotEnvironmentSecretsAndVariables(ctx context.Context, owner, repo string, repoID int64) (secrets []string, variables []string, err error)
	GetCopilotBotUser(ctx context.Context) (*gg.User, error)
	GetUserPermissionsForRepository(ctx context.Context, owner, repo, username string) (*RepositoryPermissions, error)
	GetUserPermissionsForRepositoryByID(ctx context.Context, repoID int64, username string) (*RepositoryPermissions, error)
	GetRepositoryContext(ctx context.Context, repoNodeID string) (*RepositoryContext, error)
	GetIssueContext(ctx context.Context, repoNodeID string, number int) (*IssueContext, error)
	GetIssueUserContentEdits(ctx context.Context, issueNodeID string) ([]UserContentEdit, error)
	GetPullRequestContext(ctx context.Context, repoNodeID string, number int) (*PullRequestContext, error)
	ReplaceActorsForAssignable(ctx context.Context, assignableID string, ActorsIDs []string) error
	RevokeUserToken(ctx context.Context, token string) error
	RevokeInstallationToken(ctx context.Context, token string) error
	ListBranchProtectionRules(ctx context.Context, repoNodeID string) (*[]GQLBranchProtectionRule, error)
	ListRepositoryRulesForBranch(ctx context.Context, ownerLogin, repoName, branchName string) (*gg.BranchRules, error)
	GetActionsPermissions(ctx context.Context, ownerLogin, repoName string) (*gg.ActionsPermissionsRepository, error)
}

// NewClientFactory returns a new ClientFactory.
func NewClientFactory(
	obsv observability.Exporters,
	ffClient featureflags.Client,
	baseUrl, baseInternalURL string,
	githubApp *GitHubApp,
	githubCommitLogin, githubCommitEmail string,
	dynamicWorkflowsHmacSecret string,
	environment config.Environment,
) (ClientFactoryInterface, error) {
	parsedUrl, err := url.Parse(baseUrl)
	if err != nil {
		return nil, err
	}
	if !strings.HasSuffix(parsedUrl.Path, "/") {
		parsedUrl.Path = parsedUrl.Path + "/"
	}

	var parsedInternalUrl *url.URL
	if baseInternalURL == "" {
		parsedInternalUrl = parsedUrl
	} else {
		parsedInternalUrl, err = url.Parse(baseInternalURL)
		if err != nil {
			return nil, err
		}
		if !strings.HasSuffix(parsedInternalUrl.Path, "/") {
			parsedInternalUrl.Path = parsedInternalUrl.Path + "/"
		}
	}

	return &ClientFactory{
		baseUrl:                    parsedUrl,
		baseInternalURL:            parsedInternalUrl,
		dynamicWorkflowsHmacSecret: dynamicWorkflowsHmacSecret,
		ffClient:                   ffClient,
		githubApp:                  githubApp,
		githubCommitLogin:          githubCommitLogin,
		githubCommitEmail:          githubCommitEmail,
		obsv:                       obsv,
		environment:                environment,
	}, nil
}

func NewClientFactoryFromConfig(obsv observability.Exporters, cfg *config.Config, ffClient featureflags.Client) (ClientFactoryInterface, error) {
	app, err := NewAppFromConfig(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create GitHub app: %w", err)
	}

	return NewClientFactory(obsv, ffClient, cfg.GitHubAPIURL, cfg.GitHubInternalAPIURL,
		app,
		cfg.GitHubCommitLogin, cfg.GitHubCommitEmail,
		cfg.DynamicWorkflowsHMACKey, cfg.Environment)
}

// NewAppClient returns a new GitHub client authenticated with a JWT for the app
func (cf *ClientFactory) NewAppClient() *gg.Client {
	atr := ghinstallation.NewAppsTransportFromPrivateKey(http.DefaultTransport, cf.githubApp.ID, cf.githubApp.PrivateKey)
	atr.BaseURL = cf.baseUrl.String()
	telemetryRT := observability.NewTelemetryRoundTripper(atr, cf.obsv, "github-app")
	client := gg.NewClient(&http.Client{Transport: &githubClientRoundTripper{rt: telemetryRT, obsv: cf.obsv}})
	client.BaseURL = cf.baseUrl
	return client
}

func (cf *ClientFactory) NewClientFromRepo(ctx context.Context, repoId, repoOwnerId int64) (ClientInterface, error) {
	appClient := cf.NewAppClient()
	token, err := cf.getGlobalToken(ctx, appClient, repoId, repoOwnerId)
	if err != nil {
		return nil, err
	}
	return cf.NewClient(token)
}

func (cf *ClientFactory) NewClientFromRepoName(ctx context.Context, repoName, ownerName string) (ClientInterface, error) {
	appClient := cf.NewAppClient()

	token, err := cf.getGlobalTokenByRepoName(ctx, appClient, repoName, ownerName)
	if err != nil {
		return nil, err
	}
	return cf.NewClient(token)
}

type globalTokenRequest struct {
	Target        string   `json:"target,omitempty"`
	TargetID      int64    `json:"target_id,omitempty"`
	RepositoryIDs []int64  `json:"repository_ids,omitempty"`
	Repositories  []string `json:"repositories,omitempty"`
	// RepoVisibility string   `json:"repo_visibility"`
}
type globalTokenResponse struct {
	Token      string `json:"token"`
	ExpiresAt  string `json:"expires_at"`
	ValidAfter string `json:"valid_after"`
}

func (cf *ClientFactory) getGlobalToken(ctx context.Context, appClient *gg.Client, repoId, repoOwnerId int64) (string, error) {
	tokenRes := &globalTokenResponse{}
	req, err := appClient.NewRequest("POST", "/app/global/access_tokens", globalTokenRequest{
		TargetID:      repoOwnerId,
		RepositoryIDs: []int64{repoId},
	})
	if err != nil {
		return "", err
	}
	resp, err := appClient.Do(ctx, req, tokenRes)
	if err != nil {
		if isSecondaryRateLimitError(resp, err) {
			return "", mark.With(mark.ErrTooManyRequests, err)
		}
		return "", NewGitHubAPIError(resp, "failed to create global app token", err)
	}
	if tokenRes.Token == "" {
		return "", NewGitHubAPIError(resp, "failed to create global app token", errors.New("empty token"))
	}
	return tokenRes.Token, nil
}

func (cf *ClientFactory) getGlobalTokenByRepoName(ctx context.Context, appClient *gg.Client, repoName, ownerName string) (string, error) {
	tokenRes := &globalTokenResponse{}
	req, err := appClient.NewRequest("POST", "/app/global/access_tokens", globalTokenRequest{
		Target:       ownerName,
		Repositories: []string{repoName},
	})
	if err != nil {
		return "", err
	}
	resp, err := appClient.Do(ctx, req, tokenRes)
	if err != nil {
		if isSecondaryRateLimitError(resp, err) {
			return "", mark.With(mark.ErrTooManyRequests, err)
		}
		return "", NewGitHubAPIError(resp, "failed to create global app token", err)
	}
	if tokenRes.Token == "" {
		return "", NewGitHubAPIError(resp, "failed to create global app token", errors.New("empty token"))
	}
	return tokenRes.Token, nil
}

func (cf *ClientFactory) NewClient(token string) (ClientInterface, error) {
	telemetryRT := observability.NewTelemetryRoundTripper(http.DefaultTransport, cf.obsv, "github")
	withHeaders := &githubClientRoundTripper{rt: telemetryRT, obsv: cf.obsv}
	client := gg.NewClient(&http.Client{Transport: withHeaders})
	client.BaseURL = cf.baseUrl
	client = client.WithAuthToken(token)

	internalClient := gg.NewClient(&http.Client{Transport: withHeaders})
	internalClient.BaseURL = cf.baseInternalURL
	internalClient = internalClient.WithAuthToken(token)

	src := oauth2.StaticTokenSource(
		&oauth2.Token{AccessToken: token},
	)
	httpClient := oauth2.NewClient(context.Background(), src)
	// Apply the same round tripper to the GraphQL client
	telemetryRT2 := observability.NewTelemetryRoundTripper(httpClient.Transport, cf.obsv, "github")
	httpClient.Transport = &githubClientRoundTripper{rt: telemetryRT2, obsv: cf.obsv}
	gqlclient := githubv4.NewEnterpriseClient(cf.graphqlUrl(), httpClient)

	return &Client{
		client:                     client,
		internalClient:             internalClient,
		ffClient:                   cf.ffClient,
		gqlclient:                  gqlclient,
		token:                      token,
		githubApp:                  cf.githubApp,
		githubCommitLogin:          cf.githubCommitLogin,
		githubCommitEmail:          cf.githubCommitEmail,
		obsv:                       cf.obsv,
		dynamicWorkflowsHmacSecret: cf.dynamicWorkflowsHmacSecret,
		environment:                cf.environment,
	}, nil
}

func (cf *ClientFactory) graphqlUrl() string {
	return cf.baseUrl.JoinPath("graphql").String()
}

type Client struct {
	client                     *gg.Client
	internalClient             *gg.Client
	gqlclient                  *githubv4.Client
	dynamicWorkflowsHmacSecret string
	ffClient                   featureflags.Client
	token                      string
	githubCommitLogin          string
	githubCommitEmail          string
	githubApp                  *GitHubApp
	obsv                       observability.Exporters
	environment                config.Environment
}

// Ensure Client implements ClientInterface
var _ ClientInterface = &Client{}

// Returns the token used in the GitHub client.
func (ac *Client) Token() string {
	return ac.token
}

func (ac *Client) GenerateBranchName(ctx context.Context, ownerLogin, repoName, branchName string) (string, error) {
	ownerLogin = ac.proximaHackStripPossibleTenant(ownerLogin)
	ctx, span := ac.obsv.Tracer.Start(ctx, "github.Client.GenerateBranchName")
	defer span.End()

	baseName := fmt.Sprintf("%s/%s", CopilotBranchPrefix, branchName)
	candidateName := baseName
	counter := 2

	for {
		exists, err := ac.CheckIfBranchExists(ctx, ownerLogin, repoName, candidateName)
		if err != nil {
			return "", err
		}

		if !exists {
			return candidateName, nil
		}

		candidateName = fmt.Sprintf("%s-%d", baseName, counter)
		counter++

		// Give up after 5 attempts and fall back to using the random job ID as a suffix
		if counter > 5 {
			uid := uuid.New()
			return fmt.Sprintf("%s-%s", baseName, uid), nil
		}
	}
}

func (ac *Client) CheckIfBranchExists(ctx context.Context, ownerLogin, repoName, branchName string) (bool, error) {
	ownerLogin = ac.proximaHackStripPossibleTenant(ownerLogin)
	_, resp, err := ac.client.Repositories.GetBranch(ctx, ownerLogin, repoName, branchName, 0)
	if err != nil {
		if resp.StatusCode == http.StatusNotFound {
			return false, nil
		} else {
			return false, NewGitHubAPIError(resp, "failed to check if branch exists", err)
		}
	}
	return true, nil
}

func (ac *Client) CreateNewEmptyBranch(ctx context.Context, ownerLogin, repoName, baseBranchName, branchName string) (*gg.Reference, error) {
	ownerLogin = ac.proximaHackStripPossibleTenant(ownerLogin)
	// Get the SHA of the default branch
	baseBranchRes, resp, err := ac.client.Repositories.GetBranch(ctx, ownerLogin, repoName, baseBranchName, 0)
	if err != nil {
		return nil, NewGitHubAPIError(resp, "failed to get base branch", err)
	}

	// Create a new branch from the default branch
	newRef, resp, err := ac.client.Git.CreateRef(ctx, ownerLogin, repoName, &gg.Reference{
		Ref: gg.Ptr(fmt.Sprintf("refs/heads/%s", branchName)),
		Object: &gg.GitObject{
			Type: gg.Ptr("commit"),
			SHA:  baseBranchRes.Commit.SHA,
		},
	})
	if err != nil {
		return nil, NewGitHubAPIError(resp, "failed to create new branch", err)
	}

	// Clean up the reference if Copilot can't move forward to avoid littering unused branches
	defer func() {
		if err != nil {
			_, err := ac.client.Git.DeleteRef(ctx, ownerLogin, repoName, *newRef.Ref)
			if err != nil {
				ac.obsv.Logger.WithError(err).Warn(fmt.Sprintf("Error deleting branch %s", *newRef.Ref))
			}
		}
	}()

	commit, resp, err := ac.client.Git.GetCommit(ctx, ownerLogin, repoName, *newRef.Object.SHA)
	if err != nil {
		return nil, NewGitHubAPIError(resp, "failed to get commit for new branch", err)
	}

	_, err = ac.createEmptyCommit(ctx, ownerLogin, repoName, branchName, newRef, commit)
	if err != nil {
		return nil, err
	}

	return newRef, nil
}

// Note: This function creates an empty commit but takes in an existing ref. We cannot reuse the CreateEmptyCommit
// function directly since this needs to look up the branch, and replication delays can cause 404s based on what we've seen in prod.
func (ac *Client) createEmptyCommit(ctx context.Context, ownerLogin, repoName, branchName string, ref *gg.Reference, commit *gg.Commit) (*gg.Commit, error) {
	ownerLogin = ac.proximaHackStripPossibleTenant(ownerLogin)
	emptyCommit, resp, err := ac.client.Git.CreateCommit(ctx, ownerLogin, repoName, &gg.Commit{
		Parents: []*gg.Commit{commit},
		Message: gg.Ptr("Initial plan"),
		Tree:    commit.Tree,
		Author: &gg.CommitAuthor{
			Name:  &ac.githubCommitLogin,
			Email: &ac.githubCommitEmail,
		},
	}, &gg.CreateCommitOptions{
		// TODO: Use a signer so commits are signed by Copilot
		Signer: nil,
	})
	if err != nil {
		return nil, NewGitHubAPIError(resp, "failed to create initial empty commit for branch", err)
	}

	logger := ac.obsv.LoggerWithTelemetry(ctx)
	var isRetrying bool
	user, ok := requestctx.User(ctx)
	if !ok {
		logger.Error("failed to get user from context, will not check feature state")
	} else {
		isRetrying = ac.ffClient.IsEnabledForUser(ctx, "sweagentd_retry_update_ref", int64(user.ID))
	}
	if isRetrying {
		// Update the branch to point to the empty commit with retries and backoff delays
		for i := 0; i < 3; i++ {
			_, resp, err = ac.client.Git.UpdateRef(ctx, ownerLogin, repoName, &gg.Reference{
				Ref:    gg.Ptr(fmt.Sprintf("refs/heads/%s", branchName)),
				URL:    ref.URL,
				NodeID: ref.NodeID,
				Object: &gg.GitObject{
					Type: gg.Ptr("commit"),
					SHA:  emptyCommit.SHA,
				},
			}, true)
			if err == nil {
				logger.Info("successfully updated ref to point to empty commit", kvp.Int("attempts", i))
				break
			}
			if resp != nil && resp.StatusCode == 422 && strings.Contains(err.Error(), "Reference does not exist") {
				// Retry on 422 Reference does not exist
				time.Sleep(time.Duration(1<<i) * 100 * time.Millisecond) // Exponential backoff
				continue
			}
			return nil, NewGitHubAPIError(resp, "failed to update branch to point to empty commit", err)
		}
		if err != nil {
			return nil, NewGitHubAPIError(resp, "failed to update branch to point to empty commit after retries", err)
		}
	} else {
		// Update the branch to point to the empty commit
		_, resp, err = ac.client.Git.UpdateRef(ctx, ownerLogin, repoName, &gg.Reference{
			Ref:    gg.Ptr(fmt.Sprintf("refs/heads/%s", branchName)),
			URL:    ref.URL,
			NodeID: ref.NodeID,
			Object: &gg.GitObject{
				Type: gg.Ptr("commit"),
				SHA:  emptyCommit.SHA,
			},
		}, true)
		if err != nil {
			return nil, NewGitHubAPIError(resp, "failed to update branch to point to empty commit", err)
		}
	}

	return emptyCommit, nil
}

func (ac *Client) CreateEmptyCommit(ctx context.Context, ownerLogin, repoName, branchName string) (*gg.Commit, error) {
	ownerLogin = ac.proximaHackStripPossibleTenant(ownerLogin)
	ref, resp, err := ac.client.Git.GetRef(ctx, ownerLogin, repoName, fmt.Sprintf("refs/heads/%s", branchName))
	if err != nil {
		return nil, NewGitHubAPIError(resp, "failed to get branch", err)
	}
	commit, resp, err := ac.client.Git.GetCommit(ctx, ownerLogin, repoName, *ref.Object.SHA)
	if err != nil {
		return nil, NewGitHubAPIError(resp, "failed to get commit for branch", err)
	}

	return ac.createEmptyCommit(ctx, ownerLogin, repoName, branchName, ref, commit)
}

// CreatePullRequest creates a new pull request with the given options.
func (ac *Client) CreatePullRequest(ctx context.Context, ownerLogin, repoName string, opts *gg.NewPullRequest) (*gg.PullRequest, error) {
	ownerLogin = ac.proximaHackStripPossibleTenant(ownerLogin)
	ctx, span := ac.obsv.Tracer.Start(ctx, "github.Client.CreatePullRequest")
	defer span.End()

	pullRequest, res, err := ac.client.PullRequests.Create(ctx, ownerLogin, repoName, opts)
	if err != nil {
		return nil, NewGitHubAPIError(res, "failed to create pull request", err)
	}

	if pullRequest != nil {
		copilot, err := ac.GetCopilotBotUser(ctx)
		if err != nil {
			return nil, err
		}

		// Use the GraphQL mutation to assign the PR to copilot
		assignees := []string{copilot.GetNodeID()}
		err = ac.ReplaceActorsForAssignable(ctx, pullRequest.GetNodeID(), assignees)
		if err != nil {
			return nil, err
		}
	}

	return pullRequest, nil
}

type pullRequestUpdate struct {
	Title               *string `json:"title,omitempty"`
	Body                *string `json:"body,omitempty"`
	State               *string `json:"state,omitempty"`
	Base                *string `json:"base,omitempty"`
	MaintainerCanModify *bool   `json:"maintainer_can_modify,omitempty"`
}

// The following fields are editable: Title, Body, State, Base.Ref and MaintainerCanModify.
// Base.Ref updates the base branch of the pull request.
func (ac *Client) UpdatePullRequest(ctx context.Context, repoID int64, pr *gg.PullRequest) (*gg.PullRequest, error) {
	ctx, span := ac.obsv.Tracer.Start(ctx, "github.Client.UpdatePullRequest")
	defer span.End()

	u := fmt.Sprintf("repositories/%d/pulls/%d", repoID, pr.GetNumber())
	update := &pullRequestUpdate{
		Title:               pr.Title,
		Body:                pr.Body,
		State:               pr.State,
		MaintainerCanModify: pr.MaintainerCanModify,
	}
	// Cannot change base branch of closed pull request
	if pr.Base != nil && pr.GetState() != "closed" {
		update.Base = pr.Base.Ref
	}

	req, err := ac.client.NewRequest(http.MethodPatch, u, update)
	if err != nil {
		return nil, err
	}

	p := new(gg.PullRequest)
	resp, err := ac.client.Do(ctx, req, p)
	if err != nil {
		return nil, NewGitHubAPIError(resp, "failed to update pull request", err)
	}

	return p, nil
}

func (ac *Client) RequestReviewers(ctx context.Context, owner, repo string, number int, reviewers []string) error {
	ctx, span := ac.obsv.Tracer.Start(ctx, "github.Client.RequestReviewers")
	defer span.End()
	reviewersreq := gg.ReviewersRequest{
		Reviewers: reviewers,
	}
	_, _, err := ac.client.PullRequests.RequestReviewers(ctx, owner, repo, number, reviewersreq)
	return err
}

func (ac *Client) GetComment(ctx context.Context, owner, repo string, commentID int64) (*gg.IssueComment, error) {
	ctx, span := ac.obsv.Tracer.Start(ctx, "github.Client.GetComment")
	defer span.End()

	comment, resp, err := ac.client.Issues.GetComment(ctx, owner, repo, commentID)
	if err != nil {
		if resp != nil && resp.StatusCode == http.StatusNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get pull request comment: %w", err)
	}

	return comment, err
}

func (ac *Client) CreateComment(ctx context.Context, repoID int64, issueOrPRNumber int, body string) (*gg.IssueComment, error) {
	ctx, span := ac.obsv.Tracer.Start(ctx, "github.Client.CreateComment")
	defer span.End()

	u := fmt.Sprintf("repositories/%d/issues/%d/comments", repoID, issueOrPRNumber)
	req, err := ac.client.NewRequest(http.MethodPost, u, &gg.IssueComment{
		Body: &body,
	})
	if err != nil {
		return nil, err
	}

	c := new(gg.IssueComment)
	resp, err := ac.client.Do(ctx, req, c)
	if err != nil {
		return nil, NewGitHubAPIError(resp, "failed to create comment", err)
	}

	return c, nil
}

func (ac *Client) CreateCommentInReplyTo(ctx context.Context, owner, repo string, prNumber int, body string, commentID int64) (*gg.PullRequestComment, error) {
	// This function in go-github has a bug that uses the comments endpoint instead of reply. So we'll do a post ourselves.
	// comment, _, err := ac.client.PullRequests.CreateCommentInReplyTo(ctx, owner, repo, prNumber, body, commentID)

	// See https://docs.github.com/en/rest/pulls/comments?apiVersion=2022-11-28#create-a-reply-for-a-review-comment

	comment := &struct {
		Body string `json:"body"`
	}{
		Body: body,
	}
	u := fmt.Sprintf("repos/%v/%v/pulls/%d/comments/%d/replies", owner, repo, prNumber, commentID)
	req, err := ac.client.NewRequest("POST", u, comment)
	if err != nil {
		return nil, err
	}

	c := new(gg.PullRequestComment)
	_, err = ac.client.Do(ctx, req, c)
	if err != nil {
		return nil, err
	}
	return c, nil
}

func (ac *Client) UpdateComment(ctx context.Context, owner, repo string, commentID int64, body string) (*gg.IssueComment, error) {
	comment, _, err := ac.client.Issues.EditComment(ctx, owner, repo, commentID, &gg.IssueComment{
		Body: &body,
	})
	return comment, err
}

func (ac *Client) GetRepository(ctx context.Context, ownerName, repoName string) (*gg.Repository, error) {
	ctx, span := ac.obsv.Tracer.Start(ctx, "github.Client.GetRepository")
	defer span.End()
	repo, resp, err := ac.client.Repositories.Get(ctx, ownerName, repoName)
	if err != nil {
		return nil, NewGitHubAPIError(resp, "failed to get repository", err)
	}
	return repo, err
}

func (ac *Client) GetRepositoryById(ctx context.Context, id int64) (*gg.Repository, error) {
	ctx, span := ac.obsv.Tracer.Start(ctx, "github.Client.GetRepositoryById")
	defer span.End()
	repo, _, err := ac.client.Repositories.GetByID(ctx, id)
	return repo, err
}

func (ac *Client) GetWorkflowRunLogs(ctx context.Context, logsURL string) (string, error) {
	ctx, span := ac.obsv.Tracer.Start(ctx, "github.Client.GetWorkflowRunLogs")
	defer span.End()

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, logsURL, http.NoBody)
	if err != nil {
		return "", err
	}

	// Download the zip file as binary
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// Unzip the archive
	buff := bytes.NewBuffer([]byte{})
	size, err := io.Copy(buff, resp.Body)
	if err != nil {
		return "", err
	}
	zipReader, err := zip.NewReader(bytes.NewReader(buff.Bytes()), size)
	if err != nil {
		return "", err
	}

	// Find the logs.txt file (should be at the root level)
	for _, f := range zipReader.File {
		if f.Name == "logs.txt" {
			rc, err := f.Open()
			if err != nil {
				return "", err
			}
			defer rc.Close()

			buf := new(strings.Builder)
			_, err = io.Copy(buf, rc)
			return buf.String(), err
		}
	}
	return "", errors.New("could not find logs.txt in workflow run logs zip")
}

func (ac *Client) GetPullRequest(ctx context.Context, repoID int64, number int) (*gg.PullRequest, error) {
	ctx, span := ac.obsv.Tracer.Start(ctx, "github.Client.GetPullRequest")
	defer span.End()

	u := fmt.Sprintf("repositories/%d/pulls/%d", repoID, number)
	req, err := ac.client.NewRequest(http.MethodGet, u, nil)
	if err != nil {
		return nil, err
	}

	pr := new(gg.PullRequest)
	resp, err := ac.client.Do(ctx, req, pr)
	if err != nil {
		return nil, NewGitHubAPIError(resp, "failed to get pull request", err)
	}

	return pr, nil
}

func (ac *Client) GetIssue(ctx context.Context, ownerLogin, repoName string, number int) (*gg.Issue, error) {
	ownerLogin = ac.proximaHackStripPossibleTenant(ownerLogin)
	ctx, span := ac.obsv.Tracer.Start(ctx, "github.Client.GetIssue")
	defer span.End()
	issue, _, err := ac.client.Issues.Get(ctx, ownerLogin, repoName, number)
	return issue, err
}

func (ac *Client) AssignToIssue(ctx context.Context, ownerLogin, repoName string, issueNumber int, assignee string) error {
	ownerLogin = ac.proximaHackStripPossibleTenant(ownerLogin)
	_, _, err := ac.client.Issues.AddAssignees(ctx, ownerLogin, repoName, issueNumber, []string{assignee})
	return err
}

func (ac *Client) RemoveReactionFromIssue(ctx context.Context, owner, repo string, number int) error {
	ctx, span := ac.obsv.Tracer.Start(ctx, "github.Client.RemoveReactionFromIssue")
	defer span.End()

	opts := &gg.ListReactionOptions{Content: "eyes"}
	reactions, resp, err := ac.client.Reactions.ListIssueReactions(ctx, owner, repo, number, opts)
	if err != nil {
		return NewGitHubAPIError(resp, "failed to list reactions", err)
	}

	var reaction *gg.Reaction
	for _, r := range reactions {
		if r.User.GetLogin() != "" && ac.UserIsCopilot(r.User.GetLogin()) {
			reaction = r
			break
		}
	}
	if reaction != nil {
		resp, err = ac.client.Reactions.DeleteIssueReaction(ctx, owner, repo, number, reaction.GetID())
		if err != nil {
			return NewGitHubAPIError(resp, "failed to delete reaction", err)
		}
	}

	return nil
}

func (ac *Client) GetWorkflowRunByID(ctx context.Context, ownerLogin, repoName string, runID int64) (*gg.WorkflowRun, error) {
	ownerLogin = ac.proximaHackStripPossibleTenant(ownerLogin)
	ctx, span := ac.obsv.Tracer.Start(ctx, "github.Client.GetWorkflowRunByID")
	defer span.End()
	run, _, err := ac.client.Actions.GetWorkflowRunByID(ctx, ownerLogin, repoName, runID)
	return run, err
}

func (ac *Client) CancelWorkflowRunByID(ctx context.Context, ownerLogin, repoName string, runID int64) error {
	ownerLogin = ac.proximaHackStripPossibleTenant(ownerLogin)
	ctx, span := ac.obsv.Tracer.Start(ctx, "github.Client.CancelWorkflowRunByID")
	defer span.End()
	resp, err := ac.client.Actions.CancelWorkflowRunByID(ctx, ownerLogin, repoName, runID)
	if err != nil {
		// This call can result in an "accepted error" that says that the cancellation has been queued,
		// but it comes back as an error saying its fully cancelled. Since we do not need to wait for
		// the cancellation to actually be complete, treat this as a success condition instead.
		if _, ok := err.(*gg.AcceptedError); ok && resp.StatusCode == http.StatusAccepted {
			return nil
		}
		return NewGitHubAPIError(resp, "failed to cancel workflow run", err)
	}
	return nil
}

func (ac *Client) AddLabelsToIssueOrPR(ctx context.Context, ownerLogin, repoName string, issueOrPRNumber int, labels []string) error {
	ownerLogin = ac.proximaHackStripPossibleTenant(ownerLogin)
	_, _, err := ac.client.Issues.AddLabelsToIssue(ctx, ownerLogin, repoName, issueOrPRNumber, labels)
	return err
}

type DynamicWorkflowRun struct {
	ExecutionID   *string        `json:"execution_id"`
	WorkflowRunID *int64         `json:"workflow_run_id"`
	Repository    *gg.Repository `json:"repository"`
	Ref           *string        `json:"ref"`
	Slug          *string        `json:"slug"`
	WorkflowName  *string        `json:"workflow_name"`
	Workflow      *string        `json:"workflow"`
}

type NewDynamicWorkflowRun struct {
	Ref           *string            `json:"ref"`
	Slug          *string            `json:"slug"`
	WorkflowName  *string            `json:"workflow_name"`
	Workflow      *string            `json:"workflow"`
	Inputs        *map[string]string `json:"inputs"`
	PreventReruns bool               `json:"prevent_reruns,omitempty"`
}

func (ac *Client) CreateDynamicWorkflowRun(ctx context.Context, owner, repo string, repoID int64, opts *NewDynamicWorkflowRun) (*DynamicWorkflowRun, error) {
	url := fmt.Sprintf("internal/repos/%s/%s/actions/dynamic", owner, repo)
	client := ac.internalClient

	requestOpts := []gg.RequestOption{
		func(req *http.Request) {
			req.Header.Add(hmac.RequestHeader, hmac.NewRequestHMAC(ac.dynamicWorkflowsHmacSecret).String())
		},
	}

	req, err := client.NewRequest(http.MethodPost, url, opts, requestOpts...)
	if err != nil {
		return nil, err
	}

	run := new(DynamicWorkflowRun)
	_, err = client.Do(ctx, req, run)
	if err != nil {
		return nil, err
	}

	return run, nil
}

func (ac *Client) ListWorkflowJobs(ctx context.Context, owner, repo string, runID int64) ([]*gg.WorkflowJob, error) {
	ctx, span := ac.obsv.Tracer.Start(ctx, "github.Client.ListWorkflowJobs")
	defer span.End()
	jobs, _, err := ac.client.Actions.ListWorkflowJobs(ctx, owner, repo, runID, nil)
	return jobs.Jobs, err
}

func (ac *Client) GetWorkflowJobByID(ctx context.Context, owner, repo string, jobID int64) (*gg.WorkflowJob, error) {
	ctx, span := ac.obsv.Tracer.Start(ctx, "github.Client.GetWorkflowJobByID")
	defer span.End()
	job, _, err := ac.client.Actions.GetWorkflowJobByID(ctx, owner, repo, jobID)
	return job, err
}

func (ac *Client) ListCheckRunAnnotations(ctx context.Context, owner, repo string, jobID int64) ([]*gg.CheckRunAnnotation, error) {
	ctx, span := ac.obsv.Tracer.Start(ctx, "github.Client.ListCheckRunAnnotations")
	defer span.End()
	annotations, _, err := ac.client.Checks.ListCheckRunAnnotations(ctx, owner, repo, jobID, &gg.ListOptions{PerPage: 100})
	return annotations, err
}

func (ac *Client) GetUserById(ctx context.Context, id uint64) (*gg.User, error) {
	ctx, span := ac.obsv.Tracer.Start(ctx, "github.Client.GetUserById")
	defer span.End()
	user, _, err := ac.client.Users.GetByID(ctx, int64(id))
	return user, err
}

func (ac *Client) DeleteRepo(ctx context.Context, owner, repo string) error {
	ctx, span := ac.obsv.Tracer.Start(ctx, "github.Client.DeleteRepo")
	defer span.End()
	_, err := ac.client.Repositories.Delete(ctx, owner, repo)
	return err
}

func (ac *Client) CreateRepo(ctx context.Context, org, name string, autoinit bool) (*gg.Repository, error) {
	ctx, span := ac.obsv.Tracer.Start(ctx, "github.Client.CreateRepo")
	defer span.End()
	repo := &gg.Repository{
		Name:     gg.Ptr(name),
		AutoInit: gg.Ptr(autoinit),
	}
	repo, _, err := ac.client.Repositories.Create(ctx, org, repo)
	return repo, err
}

func (ac *Client) CreateIssue(ctx context.Context, owner, repo, title, body string) (*gg.Issue, error) {
	req := &gg.IssueRequest{
		Title: gg.Ptr(title),
		Body:  gg.Ptr(body),
	}
	issue, _, err := ac.client.Issues.Create(ctx, owner, repo, req)
	return issue, err
}

func (ac *Client) GetPullRequests(ctx context.Context, owner, repo string, opts *gg.PullRequestListOptions) ([]*gg.PullRequest, error) {
	ctx, span := ac.obsv.Tracer.Start(ctx, "github.Client.GetPullRequests")
	defer span.End()
	prs, resp, err := ac.client.PullRequests.List(ctx, owner, repo, opts)
	if err != nil {
		return nil, NewGitHubAPIError(resp, "failed to list pull requests", err)
	}
	return prs, nil
}

func (ac *Client) GetContents(ctx context.Context, owner, repo, path string, opts *gg.RepositoryContentGetOptions) (fileContent *gg.RepositoryContent, dirContent []*gg.RepositoryContent, err error) {
	ctx, span := ac.obsv.Tracer.Start(ctx, "github.Client.GetContents")
	defer span.End()
	fileContent, dirContent, _, err = ac.client.Repositories.GetContents(ctx, owner, repo, path, opts)
	return fileContent, dirContent, err
}

func (ac *Client) UpdateFile(ctx context.Context, owner, repo, path string, opts *gg.RepositoryContentFileOptions) (*gg.RepositoryContentResponse, error) {
	ctx, span := ac.obsv.Tracer.Start(ctx, "github.Client.UpdateFile")
	defer span.End()
	resp, _, err := ac.client.Repositories.UpdateFile(ctx, owner, repo, path, opts)
	return resp, err
}

func (ac *Client) GetAuthenticatedUser(ctx context.Context) (*gg.User, error) {
	ctx, span := ac.obsv.Tracer.Start(ctx, "github.Client.GetAuthenticatedUser")
	defer span.End()
	user, _, err := ac.client.Users.Get(ctx, "")
	return user, err
}

func (ac *Client) UserIsCopilot(login string) bool {
	// Our privileged app also reports the display login as "Copilot",
	// but not consistently. For now, just handle every case.
	copilotLogins := []string{
		ac.githubApp.AppLogin,
		ac.githubApp.AppLogin + "[bot]",
		ac.githubApp.BotLogin,
		"Copilot",
	}

	// Ignore review comments on PRs not opened by the GitHub App
	// which looks like a login followed by [bot]
	return slices.Contains(copilotLogins, login)
}

func (ac *Client) GetCopilotEnvironmentSecretsAndVariables(ctx context.Context, owner, repo string, repoID int64) (secrets []string, variables []string, err error) {
	ctx, span := ac.obsv.Tracer.Start(ctx, "github.Client.GetCopilotEnvironmentSecretsAndVariables")
	defer span.End()

	_, resp, err := ac.client.Repositories.GetEnvironment(ctx, owner, repo, CopilotEnvironmentName)
	if err != nil {
		if resp != nil && resp.StatusCode == http.StatusNotFound {
			ac.obsv.Logger.Warn("Copilot environment not found", kvp.String("owner", owner), kvp.String("repo", repo))
			return []string{}, []string{}, nil
		}
		ac.obsv.Logger.Error(fmt.Sprintf("Failed to get Copilot environment: %s", err), kvp.String("owner", owner), kvp.String("repo", repo))
		return nil, nil, err
	}

	listOpts := &gg.ListOptions{PerPage: 100}

	secs, _, err := ac.client.Actions.ListEnvSecrets(ctx, int(repoID), CopilotEnvironmentName, listOpts)
	if err != nil {
		return nil, nil, err
	}
	for _, s := range secs.Secrets {
		secrets = append(secrets, s.Name)
	}

	vars, _, err := ac.client.Actions.ListEnvVariables(ctx, owner, repo, CopilotEnvironmentName, listOpts)
	if err != nil {
		return nil, nil, err
	}
	for _, v := range vars.Variables {
		variables = append(variables, v.Name)
	}

	return secrets, variables, nil
}

func (ac *Client) GetCopilotBotUser(ctx context.Context) (*gg.User, error) {
	ctx, span := ac.obsv.Tracer.Start(ctx, "github.Client.GetCopilotBotUser")
	defer span.End()

	user, _, err := ac.client.Users.Get(ctx, ac.githubApp.BotLogin)
	return user, err
}

func (ac *Client) RevokeUserToken(ctx context.Context, token string) error {
	ctx, span := ac.obsv.Tracer.Start(ctx, "github.Client.RevokeUserToken")
	defer span.End()

	// Requires the use of basic auth, with client ID and a client secret. So we need to create a one off client for exclusively this purpose.
	// See https://docs.github.com/rest/apps/oauth-applications#delete-an-app-token
	utr := &gg.UnauthenticatedRateLimitedTransport{
		ClientID:     ac.githubApp.ClientID,
		ClientSecret: ac.githubApp.ClientSecret,
	}
	client := gg.NewClient(utr.Client())
	client.BaseURL = ac.client.BaseURL
	resp, err := client.Authorizations.Revoke(ctx, ac.githubApp.ClientID, token)
	if err != nil {
		return NewGitHubAPIError(resp, "failed to revoke GitHub App user token", err)
	}
	return nil
}

func (ac *Client) RevokeInstallationToken(ctx context.Context, token string) error {
	ctx, span := ac.obsv.Tracer.Start(ctx, "github.Client.RevokeInstallationToken")
	defer span.End()

	// Need to create a one off client for this token since we will immediately make the client unusable
	client := gg.NewClient(nil).WithAuthToken(token)
	client.BaseURL = ac.client.BaseURL
	resp, err := client.Apps.RevokeInstallationToken(ctx)
	if err != nil {
		return NewGitHubAPIError(resp, "failed to revoke installation token", err)
	}
	return err
}

type githubClientRoundTripper struct {
	rt   http.RoundTripper
	obsv observability.Exporters
}

func (h *githubClientRoundTripper) RoundTrip(req *http.Request) (*http.Response, error) {
	slug := requestctx.TenantSlug(req.Context())
	if slug != "" {
		req.Header.Set(requestctx.GitHubTenantHeader, slug)
		req.Header.Set(requestctx.GitHubTenantIDHeader, requestctx.TenantID(req.Context()))
	}

	requestid.Forward(req)

	// Replace <tenant> placeholder in URL with actual tenant ID from context
	if req.URL != nil {
		originalURL := req.URL.String()
		modifiedURL, err := githubtwirp.ReplaceTenantPlaceholder(req.Context(), originalURL)
		if err != nil {
			return nil, fmt.Errorf("failed to replace tenant placeholder in URL: %w", err)
		}

		if modifiedURL != originalURL {
			// Parse the modified URL and update the request
			if newURL, err := url.Parse(modifiedURL); err == nil {
				req.URL = newURL
				// Update the Host header to match the new URL host to avoid HTTP/2 validation errors
				req.Host = newURL.Host
			}
		}
	}

	return h.rt.RoundTrip(req)
}

func (ac *Client) GetRepositoryPermissionLevel(ctx context.Context, repoID int64, username string) (*gg.RepositoryPermissionLevel, *gg.Response, error) {
	username = ac.proximaHackStripPossibleTenant(username)
	ctx, span := ac.obsv.Tracer.Start(ctx, "github.Client.GetRepositoryPermissionLevel")
	defer span.End()

	u := fmt.Sprintf("repositories/%d/collaborators/%s/permission", repoID, username)
	req, err := ac.client.NewRequest(http.MethodGet, u, nil)
	if err != nil {
		return nil, nil, err
	}

	rpl := new(gg.RepositoryPermissionLevel)
	resp, err := ac.client.Do(ctx, req, rpl)
	if err != nil {
		return nil, resp, err
	}

	return rpl, resp, nil
}

// RepositoryPermissions represents the permissions of an actor on a repository.
// [Client.GetUserPermissionsForRepository] returns some of these as a map[string]bool,
// this struct flattens them out for easier access.
type RepositoryPermissions struct {
	// Permission provides the legacy base roles of admin, write, read, and none,
	// where the maintain role is mapped to write and the triage role is mapped to read.
	Permission string `json:"permission,omitempty"`
	// Role provides the full role name, including custom roles.
	Role     string `json:"role_name,omitempty"`
	Pull     bool   `json:"pull"`
	Triage   bool   `json:"triage"`
	Push     bool   `json:"push"`
	Maintain bool   `json:"maintain"`
	Admin    bool   `json:"admin"`
}

func (ac *Client) GetUserPermissionsForRepository(ctx context.Context, owner, repo, username string) (*RepositoryPermissions, error) {
	username = ac.proximaHackStripPossibleTenant(username)
	ctx, span := ac.obsv.Tracer.Start(ctx, "github.Client.GetUserPermissionsForRepository")
	defer span.End()

	perm, resp, err := ac.client.Repositories.GetPermissionLevel(ctx, owner, repo, username)
	if err != nil {
		// Need response codes to handle rate limiting from API and other spots
		return nil, NewGitHubAPIError(resp, "", err)
	}

	permissions := getRepositoryPermissions(perm)

	return permissions, nil
}

func (ac *Client) GetUserPermissionsForRepositoryByID(ctx context.Context, repoID int64, username string) (*RepositoryPermissions, error) {
	username = ac.proximaHackStripPossibleTenant(username)
	ctx, span := ac.obsv.Tracer.Start(ctx, "github.Client.GetUserPermissionsForRepositoryByID")
	defer span.End()

	perm, _, err := ac.GetRepositoryPermissionLevel(ctx, repoID, username)
	if err != nil {
		return nil, err
	}

	permissions := getRepositoryPermissions(perm)

	return permissions, nil
}

func getRepositoryPermissions(perm *gg.RepositoryPermissionLevel) *RepositoryPermissions {
	permissions := perm.User.GetPermissions()

	return &RepositoryPermissions{
		Permission: perm.GetPermission(),
		Role:       perm.GetRoleName(),
		Admin:      permissions["admin"],
		Maintain:   permissions["maintain"],
		Push:       permissions["push"],
		Triage:     permissions["triage"],
		Pull:       permissions["pull"],
	}
}

func (ac *Client) ListRepositoryRulesForBranch(ctx context.Context, ownerLogin, repoName, branchName string) (*gg.BranchRules, error) {
	ownerLogin = ac.proximaHackStripPossibleTenant(ownerLogin)
	ctx, span := ac.obsv.Tracer.Start(ctx, "github.Client.ListRepositoryRulesForBranch")
	defer span.End()

	var allRules *gg.BranchRules
	// Using max for page size https://docs.github.com/en/rest/repos/rules?apiVersion=2022-11-28#get-all-repository-rulesets
	opts := &gg.ListOptions{PerPage: 100}
	for {
		rules, resp, err := ac.client.Repositories.GetRulesForBranch(ctx, ownerLogin, repoName, branchName, opts)
		if err != nil {
			return nil, err
		}
		if rules != nil {
			allRules, err = mergeBranchRules(allRules, rules)
			if err != nil {
				return nil, err
			}
		}
		if resp == nil || resp.NextPage == 0 {
			break
		}
		opts.Page = resp.NextPage
	}

	return allRules, nil
}

func mergeBranchRules(rules1, rules2 *gg.BranchRules) (*gg.BranchRules, error) {
	if rules1 == nil && rules2 == nil {
		return nil, errors.New("cannot merge when both rules are nil")
	}
	if rules2 == nil {
		return rules1, nil
	}
	if rules1 == nil {
		return rules2, nil
	}
	mergedRules := &gg.BranchRules{
		Creation:                 append(rules1.Creation, rules2.Creation...),
		Update:                   append(rules1.Update, rules2.Update...),
		Deletion:                 append(rules1.Deletion, rules2.Deletion...),
		RequiredLinearHistory:    append(rules1.RequiredLinearHistory, rules2.RequiredLinearHistory...),
		MergeQueue:               append(rules1.MergeQueue, rules2.MergeQueue...),
		RequiredDeployments:      append(rules1.RequiredDeployments, rules2.RequiredDeployments...),
		RequiredSignatures:       append(rules1.RequiredSignatures, rules2.RequiredSignatures...),
		PullRequest:              append(rules1.PullRequest, rules2.PullRequest...),
		RequiredStatusChecks:     append(rules1.RequiredStatusChecks, rules2.RequiredStatusChecks...),
		NonFastForward:           append(rules1.NonFastForward, rules2.NonFastForward...),
		CommitMessagePattern:     append(rules1.CommitMessagePattern, rules2.CommitMessagePattern...),
		CommitAuthorEmailPattern: append(rules1.CommitAuthorEmailPattern, rules2.CommitAuthorEmailPattern...),
		CommitterEmailPattern:    append(rules1.CommitterEmailPattern, rules2.CommitterEmailPattern...),
		BranchNamePattern:        append(rules1.BranchNamePattern, rules2.BranchNamePattern...),
		TagNamePattern:           append(rules1.TagNamePattern, rules2.TagNamePattern...),
		FilePathRestriction:      append(rules1.FilePathRestriction, rules2.FilePathRestriction...),
		MaxFilePathLength:        append(rules1.MaxFilePathLength, rules2.MaxFilePathLength...),
		FileExtensionRestriction: append(rules1.FileExtensionRestriction, rules2.FileExtensionRestriction...),
		MaxFileSize:              append(rules1.MaxFileSize, rules2.MaxFileSize...),
		Workflows:                append(rules1.Workflows, rules2.Workflows...),
		CodeScanning:             append(rules1.CodeScanning, rules2.CodeScanning...),
	}
	return mergedRules, nil
}

func (ac *Client) GetActionsPermissions(ctx context.Context, ownerLogin, repoName string) (*gg.ActionsPermissionsRepository, error) {
	ownerLogin = ac.proximaHackStripPossibleTenant(ownerLogin)
	ctx, span := ac.obsv.Tracer.Start(ctx, "github.Client.GetActionsPermissions")
	defer span.End()

	permissions, _, err := ac.client.Repositories.GetActionsPermissions(ctx, ownerLogin, repoName)

	return permissions, err
}

// What does this hack do?
// If we're in proxima, we know that sometimes the usernames we get from different sources (like hydro events) are tenant suffixed.
// Our current client interaction pipes logins through to the APIs, and it seems like some of our logins have suffix still attached.
// Example, a login for brphelps would look like brphelps_staff01 .
// This hack SHOULD NOT be taken to production for proxima. It will cause problems with users that have underscores in their names.
// The issue to address this is here: https://github.com/github/sweagentd/issues/3939
func (ac *Client) proximaHackStripPossibleTenant(username string) string {

	if ac.environment == config.EnvironmentProxima {
		// Strip the tenant information from the username
		index := strings.LastIndex(username, "_")
		if index > 0 {
			// If the username has a tenant, it will be in the format "username_tenant"
			// We want to return just the username part
			username = username[:index]
		}
	}

	return username
}

func isSecondaryRateLimitError(resp *gg.Response, err error) bool {
	if resp == nil || resp.StatusCode != http.StatusForbidden {
		return false
	}

	// Check if the error message contains "secondary rate limit"
	if err != nil && strings.Contains(err.Error(), "secondary rate limit") {
		return true
	}

	return false
}
