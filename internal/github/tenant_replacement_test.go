package github

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/github/sweagentd/internal/config"
	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/observability"
	"github.com/github/sweagentd/internal/requestctx"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestGraphQLClientTenantReplacement(t *testing.T) {
	tests := []struct {
		name          string
		baseURLSuffix string
		tenantSlug    string
		shouldReplace bool
	}{
		{
			name:          "URL with tenant placeholder",
			baseURLSuffix: "/<tenant>",
			tenantSlug:    "test-tenant-123",
			shouldReplace: true,
		},
		{
			name:          "URL with URL-encoded tenant placeholder",
			baseURLSuffix: "/%3Ctenant%3E",
			tenantSlug:    "test-tenant-456",
			shouldReplace: true,
		},
		{
			name:          "URL without tenant placeholder",
			baseURLSuffix: "/graphql",
			tenantSlug:    "test-tenant-789",
			shouldReplace: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			requestReceived := false

			// Mock GraphQL server that captures the request URL
			server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				requestReceived = true

				if tt.shouldReplace {
					// Verify the URL contains the replaced tenant slug
					assert.Contains(t, r.URL.Path, tt.tenantSlug, "GraphQL request should contain replaced tenant slug")
				} else {
					// Verify the URL doesn't contain any tenant placeholder
					assert.NotContains(t, r.URL.Path, "<tenant>", "GraphQL request should not contain tenant placeholder")
					assert.NotContains(t, r.URL.Path, "%3Ctenant%3E", "GraphQL request should not contain URL-encoded tenant placeholder")
				}

				// Mock a simple GraphQL response
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{"data": {}}`))
			}))
			defer server.Close()

			// Create a client factory with the mock server URL
			baseURL := server.URL + tt.baseURLSuffix

			obsv := observability.NewNoopExporters()
			ffClient := featureflags.NewNoopClient(nil)

			app := &GitHubApp{
				ID:           123456,
				ClientID:     "client-id",
				ClientSecret: "",
				AppLogin:     "test-app",
				BotLogin:     "test-bot",
				PrivateKey:   randomPrivateKey(t),
			}

			cf, err := NewClientFactory(obsv, ffClient, baseURL, "", app, "", "", "", config.EnvironmentDevelopment)
			require.NoError(t, err)

			client, err := cf.NewClient("test-token")
			require.NoError(t, err)

			// Create context with tenant slug
			ctx := context.Background()
			ctx = requestctx.AddData(ctx, &requestctx.CtxData{
				TenantSlug: tt.tenantSlug,
			})

			// Make a GraphQL request
			ghClient := client.(*Client)
			_ = ghClient.gqlclient.Query(ctx, struct{}{}, nil) // We expect this to fail, but we want to verify the request was made

			// We expect this to fail since it's not a real GraphQL endpoint,
			// but the important thing is that the request was made
			require.True(t, requestReceived, "GraphQL request should have been made to the server")
		})
	}
}
