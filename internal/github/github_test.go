package github

import (
	"context"
	"crypto/rand"
	"crypto/rsa"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"strconv"
	"strings"
	"testing"

	"github.com/github/go-http/v2/middleware/headers"
	"github.com/github/go-http/v2/middleware/requestid"
	"github.com/github/go-http/v2/middleware/tenant"
	"github.com/github/sweagentd/internal/config"
	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/observability"
	"github.com/golang-jwt/jwt/v4"
	"github.com/stretchr/testify/require"
)

func TestGitHubClient(t *testing.T) {
	obsv := observability.NewNoopExporters()
	ffClient := featureflags.NewNoopClient(nil)

	appLogin := "github-app-login"
	botLogin := "github-bot-login"

	app := &GitHubApp{
		ID:           123456,
		ClientID:     "client-id",
		ClientSecret: "",
		AppLogin:     appLogin,
		BotLogin:     botLogin,
		PrivateKey:   randomPrivateKey(t),
	}

	t.Run("urls", func(t *testing.T) {
		cf, err := NewClientFactory(obsv, ffClient, "https://api.github.com/", "", app, "", "", "", config.EnvironmentDevelopment)
		require.NoError(t, err)

		ghcf := cf.(*ClientFactory)

		require.Equal(t, "https://api.github.com/", ghcf.baseUrl.String())
		require.Equal(t, "https://api.github.com/", ghcf.baseInternalURL.String())
		require.Equal(t, "https://api.github.com/graphql", ghcf.graphqlUrl())

		// Test explicitly setting the internal base URL
		cf, err = NewClientFactory(obsv, ffClient, "https://api.github.com/", "https://internal-api.service.iad.github.net/", app, "", "", "", config.EnvironmentDevelopment)
		require.NoError(t, err)

		ghcf = cf.(*ClientFactory)

		require.Equal(t, "https://api.github.com/", ghcf.baseUrl.String())
		require.Equal(t, "https://internal-api.service.iad.github.net/", ghcf.baseInternalURL.String())
		require.Equal(t, "https://api.github.com/graphql", ghcf.graphqlUrl())
	})

	t.Run("url without ending slash", func(t *testing.T) {
		// Test an input URL that does not end with a slash
		cf, err := NewClientFactory(obsv, ffClient, "http://api.github.localhost", "", app, "", "", "", config.EnvironmentDevelopment)
		require.NoError(t, err)

		ghcf := cf.(*ClientFactory)
		require.Equal(t, "http://api.github.localhost/", ghcf.baseUrl.String())
		require.Equal(t, "http://api.github.localhost/", ghcf.baseInternalURL.String())
		require.Equal(t, "http://api.github.localhost/graphql", ghcf.graphqlUrl())
	})

	t.Run("user is copilot", func(t *testing.T) {
		cf, err := NewClientFactory(obsv, ffClient, "https://api.github.com/", "", app, "", "", "", config.EnvironmentDevelopment)
		require.NoError(t, err)

		gh, err := cf.NewClient("token")
		require.NoError(t, err)

		// Test a user that is not a copilot
		require.False(t, gh.UserIsCopilot("not-copilot"))

		// Test a user that is copilot
		require.True(t, gh.UserIsCopilot(appLogin))
		require.True(t, gh.UserIsCopilot(appLogin+"[bot]"))
		require.True(t, gh.UserIsCopilot(botLogin))
		require.True(t, gh.UserIsCopilot("Copilot"))
	})

	t.Run("key headers are set", func(t *testing.T) {
		tenantIdHeader := "tenant-id"
		tenantSlugHeader := "tenant-slug"
		requestIdHeader := "request-id"
		defaultToken := "token"

		svr := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			require.Equal(t, tenantIdHeader, r.Header.Get(headers.TenantID))
			require.Equal(t, tenantSlugHeader, r.Header.Get(headers.Tenant))
			require.Equal(t, requestIdHeader, r.Header.Get(headers.GitHubRequestID))

			authHeader := r.Header.Get("Authorization")
			if len(authHeader) == 0 {
				require.Fail(t, "Authorization header is empty")
			}
			tokenString := strings.Replace(authHeader, "Bearer ", "", 1)
			token, _, err := new(jwt.Parser).ParseUnverified(tokenString, jwt.MapClaims{})
			if err != nil {
				require.Equal(t, defaultToken, tokenString, "token validation failed for github client")
			} else if claims, ok := token.Claims.(jwt.MapClaims); ok {
				verified := claims.VerifyIssuer(strconv.FormatInt(app.ID, 10), true)
				require.True(t, verified, "token validation failed for github app client")
			} else {
				require.Fail(t, "failed to parse token claims")
			}

			// Make sure the GLBVia header is set too, see: https://github.com/github/go-http/blob/5760243997dbe5ff7e943def5dc8239cc6e3ac26/middleware/requestid/requestid.go#L95-L98
			host, err := os.Hostname()
			require.NoError(t, err)
			require.Contains(t, r.Header.Get(headers.GLBVia), fmt.Sprintf("hostname=%s t=", host))

			w.Header().Add("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			// Necessary mock for the app client
			resp := map[string]any{"Token": defaultToken}
			err = json.NewEncoder(w).Encode(resp)
			require.NoError(t, err)
		}))
		defer svr.Close()

		cf, err := NewClientFactory(obsv, ffClient, svr.URL, "", app, "", "", "", config.EnvironmentDevelopment)
		require.NoError(t, err)

		// Verify client sends the headers
		gh, err := cf.NewClient(defaultToken)
		require.NoError(t, err)

		ctx := context.Background()
		ctx = tenant.TenantIDContext(ctx, tenantIdHeader)
		ctx = tenant.TenantContext(ctx, tenantSlugHeader)
		ctx = requestid.WithGitHubRequestID(ctx, requestIdHeader)
		_, err = gh.CheckIfBranchExists(ctx, "owner", "repo", "branch")
		require.NoError(t, err)

		// Verify app client sends the headers
		ghApp, err := cf.NewClientFromRepo(ctx, 1, 1)
		require.NoError(t, err)

		_, err = ghApp.CheckIfBranchExists(ctx, "owner", "repo", "branch")
		require.NoError(t, err)
	})

	t.Run("proxima hack strip possible tenant", func(t *testing.T) {
		// Test with Proxima environment
		cf, err := NewClientFactory(obsv, ffClient, "https://api.github.com/", "", app, "", "", "", config.EnvironmentProxima)
		require.NoError(t, err)

		gh, err := cf.NewClient("token")
		require.NoError(t, err)

		client := gh.(*Client)

		// Test username with tenant suffix
		require.Equal(t, "brphelps", client.proximaHackStripPossibleTenant("brphelps_staff01"))
		require.Equal(t, "user", client.proximaHackStripPossibleTenant("user_tenant"))
		require.Equal(t, "complex_user", client.proximaHackStripPossibleTenant("complex_user_tenant"))

		// Test username without tenant suffix
		require.Equal(t, "brphelps", client.proximaHackStripPossibleTenant("brphelps"))
		require.Equal(t, "user", client.proximaHackStripPossibleTenant("user"))

		// Test with non-Proxima environment (should not strip)
		cfDev, err := NewClientFactory(obsv, ffClient, "https://api.github.com/", "", app, "", "", "", config.EnvironmentDevelopment)
		require.NoError(t, err)

		ghDev, err := cfDev.NewClient("token")
		require.NoError(t, err)

		clientDev := ghDev.(*Client)

		// In non-Proxima environment, username should remain unchanged
		require.Equal(t, "brphelps_staff01", clientDev.proximaHackStripPossibleTenant("brphelps_staff01"))
		require.Equal(t, "user_tenant", clientDev.proximaHackStripPossibleTenant("user_tenant"))
		require.Equal(t, "brphelps", clientDev.proximaHackStripPossibleTenant("brphelps"))
	})
}

func randomPrivateKey(t *testing.T) *rsa.PrivateKey {
	// create a new random RSA key pair
	key, err := rsa.GenerateKey(rand.Reader, 2048)
	require.NoError(t, err)
	return key
}
