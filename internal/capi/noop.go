package capi

import (
	"context"
	"fmt"
	"slices"
	"strconv"
	"time"

	"github.com/github/sweagentd/internal/jobs"
	"github.com/openai/openai-go"
)

type NoopClient struct {
	MockChatCompletions func(ctx context.Context, userId, interactionId string, body openai.ChatCompletionNewParams) (*openai.ChatCompletion, error)
	MockCreateSession   func(ctx context.Context, ownerID int64, repoID int64, pullID int64, eventType jobs.EventType, eventURL string, eventIdentifiers []string, sessionState SessionState, sessionName string) (*CreateSessionResponse, error)
	MockGetSession      func(ctx context.Context, sessionID string) (*Session, error)
	MockUpdateSession   func(ctx context.Context, sessionID string, update SessionUpdate) error
}

type NoopSessionClient struct {
	Sessions []Session

	MockCreateSession func(ctx context.Context, ownerID int64, repoID int64, pullID int64, eventType jobs.EventType, eventURL string, eventIdentifiers []string, sessionState SessionState, sessionName string) (*CreateSessionResponse, error)
	MockGetSession    func(ctx context.Context, sessionID string) (*Session, error)
	MockUpdateSession func(ctx context.Context, sessionID string, update SessionUpdate) error
}

var _ Client = &NoopClient{}
var _ SessionClient = &NoopSessionClient{}

// NewNoopClientWithFixedShouldWakeResponse creates a NoopClient that always returns a single completion: a tool call to "should_wake"
// with the specified shouldWakeResponse value.
func NewNoopClientWithFixedShouldWakeResponse(shouldWakeResponse bool) *NoopClient {
	return &NoopClient{
		MockChatCompletions: func(ctx context.Context, userId, interactionId string, body openai.ChatCompletionNewParams) (*openai.ChatCompletion, error) {
			return &openai.ChatCompletion{
				ID: "noop",
				Choices: []openai.ChatCompletionChoice{
					{
						Message: openai.ChatCompletionMessage{
							ToolCalls: []openai.ChatCompletionMessageToolCall{
								{
									Type: "function",
									Function: openai.ChatCompletionMessageToolCallFunction{
										Name:      "should_wake",
										Arguments: `{"wake":` + strconv.FormatBool(shouldWakeResponse) + `}`,
									},
								},
							},
						},
					},
				},
			}, nil
		},
	}
}

func (c *NoopClient) NewSessionClient(userID int64, token string) SessionClient {
	sessionClient := &NoopSessionClient{}
	if c.MockCreateSession != nil {
		sessionClient.MockCreateSession = c.MockCreateSession
	}
	if c.MockGetSession != nil {
		sessionClient.MockGetSession = c.MockGetSession
	}
	if c.MockUpdateSession != nil {
		sessionClient.MockUpdateSession = c.MockUpdateSession
	}
	return sessionClient
}

func (c *NoopClient) ChatCompletions(ctx context.Context, userId, interactionId string, body openai.ChatCompletionNewParams) (*openai.ChatCompletion, error) {
	if c.MockChatCompletions != nil {
		return c.MockChatCompletions(ctx, userId, interactionId, body)
	}

	return &openai.ChatCompletion{
		ID: "noop",
		Choices: []openai.ChatCompletionChoice{
			{
				Message: openai.ChatCompletionMessage{
					Role:    "assistant",
					Content: "I'm sorry, I cannot do that, Dave.",
				},
			},
		},
	}, nil
}

func (c *NoopSessionClient) CreateSession(ctx context.Context, ownerID int64, repoID int64, pullID int64, eventType jobs.EventType, eventURL string, eventIdentifiers []string, sessionState SessionState, sessionName string) (*CreateSessionResponse, error) {
	if c.MockCreateSession != nil {
		return c.MockCreateSession(ctx, ownerID, repoID, pullID, eventType, eventURL, eventIdentifiers, sessionState, sessionName)
	}

	session := &Session{
		ID:               fmt.Sprintf("session-%d", ownerID),
		Name:             sessionName,
		OwnerID:          uint64(ownerID),
		RepoID:           uint64(repoID),
		ResourceID:       pullID,
		ResourceType:     "pull",
		EventType:        string(eventType),
		EventURL:         eventURL,
		EventIdentifiers: eventIdentifiers,
		State:            sessionState,
		CreatedAt:        time.Now(),
		LastUpdatedAt:    time.Now(),
	}

	if slices.ContainsFunc(c.Sessions, func(s Session) bool { return s.ID == session.ID }) {
		return nil, fmt.Errorf("session already exists: %s", session.ID)
	}

	c.Sessions = append(c.Sessions, *session)
	return &CreateSessionResponse{ID: session.ID}, nil

}

func (c *NoopSessionClient) GetSession(ctx context.Context, sessionID string) (*Session, error) {
	if c.MockGetSession != nil {
		return c.MockGetSession(ctx, sessionID)
	}

	for _, session := range c.Sessions {
		if session.ID == sessionID {
			return &session, nil
		}
	}
	return nil, fmt.Errorf("session not found: %s", sessionID)
}

func (c *NoopSessionClient) ListSessions(ctx context.Context, ownerID int64, repoID int64) (*ListSessionResponse, error) {
	return nil, nil
}

func (c *NoopSessionClient) ListSessionsByRepoID(ctx context.Context, repoID int64) (*ListSessionResponse, error) {
	return nil, nil
}

func (c *NoopSessionClient) ListSessionsByRepoNWO(ctx context.Context, owner string, repo string) (*ListSessionResponse, error) {
	return nil, nil
}

func (c *NoopSessionClient) ListSessionsByResourceID(ctx context.Context, resourceType string, resourceID int64) (*ListSessionResponse, error) {
	return nil, nil
}

func (c *NoopSessionClient) UpdateSession(ctx context.Context, sessionID string, update SessionUpdate) error {
	if c.MockUpdateSession != nil {
		return c.MockUpdateSession(ctx, sessionID, update)
	}

	for i, session := range c.Sessions {
		if session.ID == sessionID {
			if update.Name != "" {
				c.Sessions[i].Name = update.Name
			}
			if update.State != "" {
				c.Sessions[i].State = update.State
			}
			if update.WorkflowRunID != 0 {
				c.Sessions[i].WorkflowRunID = update.WorkflowRunID
			}
			if update.Error != nil {
				c.Sessions[i].Error = update.Error
			}
			c.Sessions[i].LastUpdatedAt = time.Now()
			return nil
		}
	}
	return fmt.Errorf("session not found: %s", sessionID)
}

func (c *NoopSessionClient) UpdateSessionLogs(ctx context.Context, sessionID string, logs string) error {
	return nil
}
