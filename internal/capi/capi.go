package capi

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/github/go-auth/hmac"
	"github.com/github/go-http/middleware/headers"
	"github.com/github/go-http/middleware/requestid"
	"github.com/github/sweagentd/internal/githubtwirp"
	"github.com/github/sweagentd/internal/observability"
	"github.com/github/sweagentd/internal/requestctx"
	"github.com/hashicorp/go-retryablehttp"
	"github.com/openai/openai-go"
	"github.com/openai/openai-go/option"
	"go.opentelemetry.io/otel/attribute"
)

const (
	GitHubUserIDHeader     = "X-GitHub-User"
	IntegrationIDHeader    = "Copilot-Integration-Id"
	InteractionIDHeader    = "X-Interaction-Id"
	GitHubApiVersionHeader = "X-GitHub-Api-Version"
	GitHubApiVersionValue  = "2025-05-01"
	InteractionTypeHeader  = "X-Interaction-Type"
	InteractionTypeValue   = "conversation-agent"
	InitiatorHeader        = "X-Initiator"

	// Maximum number of retries for rate-limited ChatCompletions requests
	maxRetries = 3
)

type baseTransport struct {
	RoundTripper  http.RoundTripper
	integrationId string
}

func (ct *baseTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	slug := requestctx.TenantSlug(req.Context())
	if slug != "" {
		req.Header.Set(requestctx.GitHubTenantHeader, slug)
		req.Header.Set(requestctx.GitHubTenantIDHeader, requestctx.TenantID(req.Context()))
	}

	// Replace <tenant> placeholder in URL with actual tenant ID from context
	if req.URL != nil {
		originalURL := req.URL.String()
		modifiedURL, err := githubtwirp.ReplaceTenantPlaceholder(req.Context(), originalURL)
		if err != nil {
			return nil, fmt.Errorf("failed to replace tenant placeholder in URL: %w", err)
		}

		if modifiedURL != originalURL {
			// Parse the modified URL and update the request
			if newURL, err := url.Parse(modifiedURL); err == nil {
				req.URL = newURL
				// Update the Host header to match the new URL host to avoid HTTP/2 validation errors
				req.Host = newURL.Host
			}
		}
	}

	req.Header.Add(IntegrationIDHeader, ct.integrationId)
	req.Header.Add(GitHubApiVersionHeader, GitHubApiVersionValue)
	githubRequestID := requestid.GetGitHubRequestID(req.Context())
	if githubRequestID != "" {
		req.Header.Set(headers.GitHubRequestID, githubRequestID)
	}

	return ct.RoundTripper.RoundTrip(req)
}

type HmacTransport struct {
	base       baseTransport
	hmacSecret string
}

func (ct *HmacTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	req.Header.Add(hmac.RequestHeader, hmac.NewRequestHMAC(ct.hmacSecret).String())
	return ct.base.RoundTrip(req)
}

type UserTransport struct {
	base  baseTransport
	token string
}

func (ct *UserTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", ct.token))
	return ct.base.RoundTrip(req)
}

type Client interface {
	NewSessionClient(userID int64, token string) SessionClient
	ChatCompletions(ctx context.Context, userId, interactionId string, body openai.ChatCompletionNewParams) (*openai.ChatCompletion, error)
}

var _ Client = &HmacClient{}

type HmacClient struct {
	obsv          observability.Exporters
	oai           openai.Client
	baseURL       url.URL
	baseTransport baseTransport
	integrationId string
}

type UserClient struct {
	obsv    observability.Exporters
	http    *http.Client
	baseURL url.URL
	userID  int64
}

func NewClient(obsv observability.Exporters, baseUrl, integrationId, hmacSecret string) *HmacClient {
	bt := &baseTransport{
		RoundTripper:  observability.NewTelemetryRoundTripper(http.DefaultTransport, obsv, "capi"),
		integrationId: integrationId,
	}
	tr := &HmacTransport{
		base:       *bt,
		hmacSecret: hmacSecret,
	}

	u, err := url.Parse(baseUrl)
	if err != nil {
		log.Fatalf("failed to parse baseURL: %s\n", err)
	}
	if u.Path != "" && !strings.HasSuffix(u.Path, "/") {
		u.Path += "/"
	}

	httpClient := &http.Client{
		Transport: tr,
	}

	oai := openai.NewClient(
		option.WithBaseURL(baseUrl),
		option.WithHTTPClient(httpClient),
		option.WithHeader(GitHubApiVersionHeader, GitHubApiVersionValue),
		// OpenAI's retry logic doesn't seem to work with CAPI's retry-after header
		option.WithMaxRetries(0),
		// These headers tell CAPI that this is a conversation agent interaction
		// and to rate-limit it according to agent behaviors.
		option.WithHeader(InteractionTypeHeader, InteractionTypeValue),
		option.WithHeader("Openai-Intent", InteractionTypeValue),
		// This header is used to bill the request according to the right multiplier.
		// We should mostly bill requests originated from the sweagentd runtime.
		// Use agent here to not bill the request originated from the sweagentd service by default.
		option.WithHeader(InitiatorHeader, "agent"),
	)

	return &HmacClient{
		obsv:          obsv,
		oai:           oai,
		baseURL:       *u,
		baseTransport: *bt,
		integrationId: integrationId,
	}
}

func (c *HmacClient) NewSessionClient(userID int64, token string) SessionClient {
	ut := &UserTransport{
		base:  c.baseTransport,
		token: token,
	}
	httpClient := &http.Client{
		Transport: ut,
	}

	retryableClient := retryablehttp.NewClient()
	retryableClient.HTTPClient = httpClient
	retryableClient.CheckRetry = func(ctx context.Context, resp *http.Response, err error) (bool, error) {
		// There's some intermittent 401 errors that are returning.  It's possible that this is actually a race
		// condition with valid tokens not being propagated (the error is that the provided token doesn't exist).
		// Therefore, we retry on 401 errors too.
		if err == nil && resp.StatusCode == http.StatusUnauthorized {
			return true, nil
		}

		// There are few scenarios resulting in 404 responses that are worth retrying:
		// 1. Due to consistency configuration for sessions (in Cosmos DB), we can get 404 responses when the session
		//	was newly created. Retry on client side to cope with latency https://github.com/github/sweagentd/issues/3024
		// 2. Session is immediately created after pull request creation, the PUT session API checks pull request existence
		//	and returns 404 if the pull request is not found.
		if err == nil && resp.StatusCode == http.StatusNotFound {
			return true, nil
		}

		return retryablehttp.DefaultRetryPolicy(ctx, resp, err)
	}

	return &UserClient{
		obsv:    c.obsv,
		http:    retryableClient.StandardClient(),
		baseURL: c.baseURL,
		userID:  userID,
	}
}

func (c *HmacClient) ChatCompletions(ctx context.Context, userId, interactionId string, body openai.ChatCompletionNewParams) (*openai.ChatCompletion, error) {
	return c.chatCompletionsWithRetry(ctx, userId, interactionId, body, 0)
}

func (c *HmacClient) chatCompletionsWithRetry(ctx context.Context, userId, interactionId string, body openai.ChatCompletionNewParams, retryCount int) (*openai.ChatCompletion, error) {
	// Check if we have exceeded the maximum number of retries
	if retryCount >= maxRetries {
		return nil, fmt.Errorf("exceeded maximum number of retries (%d) for rate-limited requests", maxRetries)
	}

	ctx, span := c.obsv.Tracer.Start(ctx, "capi.ChatCompletions")
	span.SetAttributes(attribute.String("gh.capi.model", body.Model))
	defer span.End()

	var httpResp *http.Response
	options := []option.RequestOption{
		option.WithHeader(GitHubUserIDHeader, userId),
		option.WithHeader(InteractionIDHeader, interactionId),
		option.WithResponseInto(&httpResp),
	}

	res, err := c.oai.Chat.Completions.New(ctx, body, options...)
	if err != nil {
		if httpResp != nil && httpResp.StatusCode == http.StatusTooManyRequests {
			// Retry-After header is set, so we can use that to retry
			ra := httpResp.Header.Get("Retry-After") // in seconds as a string
			if ra == "" {
				return nil, fmt.Errorf("retry-after header not set")
			}

			raInt, err := strconv.Atoi(ra)
			if err != nil {
				return nil, fmt.Errorf("failed to parse retry-after header: %w", err)
			}
			t := time.Now().Add(time.Duration(raInt) * time.Second)
			sleepTill := time.Until(t)
			if sleepTill > 0 {
				time.Sleep(sleepTill)
				return c.chatCompletionsWithRetry(ctx, userId, interactionId, body, retryCount+1)
			}
			return nil, err
		}

		c.obsv.Logger.WithContext(ctx).WithError(err).Error("failed to get chat completions")
		return nil, err
	}
	return res, nil
}
