package capi

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"slices"
	"time"

	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/requestctx"
)

const (
	CopilotAPIAgentSessions = "copilot_api_agent_sessions"
)

type Session struct {
	ID               string       `json:"id"`
	Name             string       `json:"name"`
	UserID           uint64       `json:"user_id"`
	AgentID          int64        `json:"agent_id"`
	State            SessionState `json:"state"`
	OwnerID          uint64       `json:"owner_id"`
	RepoID           uint64       `json:"repo_id"`
	ResourceType     string       `json:"resource_type"`
	ResourceID       int64        `json:"resource_id"`
	LastUpdatedAt    time.Time    `json:"last_updated_at,omitzero"`
	CreatedAt        time.Time    `json:"created_at,omitzero"`
	CompletedAt      time.Time    `json:"completed_at,omitzero"`
	EventURL         string       `json:"event_url"`
	EventType        string       `json:"event_type"`
	EventIdentifiers []string     `json:"event_identifiers"`
	// The ID portion in https://github/github/actions/runs/14799761754 for Actions runs.
	// Globally unique and can be used to look up the owner and repo.
	WorkflowRunID uint64        `json:"workflow_run_id"`
	Error         *SessionError `json:"error"`
}

// SessionState represents the state of a session.
type SessionState string

// NewSessionState returns a new session state type.
func NewSessionState(name string) (*SessionState, error) {
	s := SessionState(name)
	if !slices.Contains(allSessionStates, s) {
		return &s, fmt.Errorf("invalid session state: %s", name)
	}

	return &s, nil
}

// IsFinalState checks if a given state is a final state
func IsFinalState(state SessionState) bool {
	return slices.Contains(FinalSessionState, state)
}

var (
	// SessionStateQueued represents the session state when it is queued for processing.
	SessionStateQueued = SessionState("queued")
	// SessionStateInProgress represents the session state when it is in progress.
	SessionStateInProgress = SessionState("in_progress")
	// SessionStateCompleted represents the session state when it has completed.
	SessionStateCompleted = SessionState("completed")
	// SessionStateFailed represents the session state when it has failed.
	SessionStateFailed = SessionState("failed")
	// SessionStateIdle represents the session state when it is idle.
	SessionStateIdle = SessionState("idle")
	// SessionStateWaitingForUser represents the session state when it is waiting for user input.
	SessionStateWaitingForUser = SessionState("waiting_for_user")
	// SessionStateTimedOut represents the session state when it has timed out due to inactivity.
	SessionStateTimedOut = SessionState("timed_out")
	// SessionStateCancelled represents the session state when it has been cancelled.
	SessionStateCancelled = SessionState("cancelled")

	allSessionStates = []SessionState{
		SessionStateQueued,
		SessionStateInProgress,
		SessionStateCompleted,
		SessionStateFailed,
		SessionStateIdle,
		SessionStateWaitingForUser,
		SessionStateTimedOut,
		SessionStateCancelled,
	}

	// FinalSessionState represents the final states of a session.
	FinalSessionState = []SessionState{
		SessionStateCompleted,
		SessionStateFailed,
		SessionStateTimedOut,
		SessionStateCancelled,
	}
)

type SessionResourceType string

const (
	ResourceTypePullRequest SessionResourceType = "pull"
	ResourceTypeIssue       SessionResourceType = "issue"
)

type SessionError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

type SessionCreate struct {
	Name             string       `json:"name,omitempty"`
	OwnerID          uint64       `json:"owner_id,omitzero"`
	RepoID           uint64       `json:"repo_id,omitzero"`
	ResourceID       int64        `json:"resource_id,omitzero"`
	ResourceType     string       `json:"resource_type,omitempty"` // "pull" or "issue"
	EventURL         string       `json:"event_url,omitempty"`
	EventType        string       `json:"event_type,omitempty"`
	EventIdentifiers []string     `json:"event_identifiers,omitempty"`
	State            SessionState `json:"state,omitempty"`
}

type SessionUpdate struct {
	Name          string        `json:"name,omitempty"`
	OwnerID       uint64        `json:"owner_id,omitzero"`
	RepoID        uint64        `json:"repo_id,omitzero"`
	ResourceID    int64         `json:"resource_id,omitzero"`
	ResourceType  string        `json:"resource_type,omitempty"` // "pull" or "issue"
	State         SessionState  `json:"state,omitempty"`
	WorkflowRunID uint64        `json:"workflow_run_id,omitzero"`
	Error         *SessionError `json:"error,omitempty"`
}

type CreateSessionResponse struct {
	ID string `json:"id"`
}

type ListSessionResponse struct {
	Sessions []Session `json:"sessions"`
}

type SessionClient interface {
	CreateSession(ctx context.Context, ownerID, repoID, pullID int64, eventType jobs.EventType, eventURL string, eventIdentifiers []string, sessionState SessionState, sessionName string) (*CreateSessionResponse, error)
	UpdateSession(ctx context.Context, sessionID string, update SessionUpdate) error
	GetSession(ctx context.Context, sessionID string) (*Session, error)
	ListSessions(ctx context.Context, ownerID, repoID int64) (*ListSessionResponse, error)
	ListSessionsByRepoID(ctx context.Context, repoID int64) (*ListSessionResponse, error)
	ListSessionsByRepoNWO(ctx context.Context, owner, repo string) (*ListSessionResponse, error)
	ListSessionsByResourceID(ctx context.Context, resourceType string, resourceID int64) (*ListSessionResponse, error)
	UpdateSessionLogs(ctx context.Context, sessionID string, logs string) error
}

var _ SessionClient = &UserClient{}

// CreateSession creates a new session for the authenticated user.
func (c *UserClient) CreateSession(ctx context.Context, ownerID, repoID, pullID int64, eventType jobs.EventType, eventURL string, eventIdentifiers []string, sessionState SessionState, sessionName string) (*CreateSessionResponse, error) {
	ctx, span := c.obsv.Tracer.Start(ctx, "capi.CreateSession")
	defer span.End()

	u := "agents/sessions"

	body := SessionCreate{
		OwnerID:          uint64(ownerID),
		RepoID:           uint64(repoID),
		ResourceID:       pullID,
		ResourceType:     string(ResourceTypePullRequest),
		EventURL:         eventURL,
		EventType:        string(eventType),
		EventIdentifiers: eventIdentifiers,
		State:            sessionState,
	}

	if sessionName != "" {
		body.Name = sessionName
	}

	req, err := c.newRequest(ctx, http.MethodPost, u, body)
	if err != nil {
		return nil, err
	}

	req = req.WithContext(ctx)
	resp, err := c.http.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		return nil, fmt.Errorf("unexpected status code: %s", resp.Status)
	}

	var createSessionResponse CreateSessionResponse
	if err := json.NewDecoder(resp.Body).Decode(&createSessionResponse); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &createSessionResponse, nil
}

// UpdateSession updates a session
func (c *UserClient) UpdateSession(ctx context.Context, sessionID string, update SessionUpdate) error {
	ctx, span := c.obsv.Tracer.Start(ctx, "capi.UpdateSession")
	defer span.End()

	u := fmt.Sprintf("agents/sessions/%s", sessionID)
	req, err := c.newRequest(ctx, http.MethodPut, u, update)
	if err != nil {
		return err
	}

	req = req.WithContext(ctx)
	resp, err := c.http.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("failed to update session: %s", resp.Status)
	}

	return nil
}

// GetSession retrieves a session by its ID.
func (c *UserClient) GetSession(ctx context.Context, sessionID string) (*Session, error) {
	ctx, span := c.obsv.Tracer.Start(ctx, "capi.GetSession")
	defer span.End()

	u := fmt.Sprintf("agents/sessions/%s", sessionID)
	req, err := c.newRequest(ctx, http.MethodGet, u, nil)
	if err != nil {
		return nil, err
	}

	req = req.WithContext(ctx)
	resp, err := c.http.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to get session: %s", resp.Status)
	}

	var session Session
	if err := json.NewDecoder(resp.Body).Decode(&session); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &session, nil
}

// ListSessions retrieves all sessions for the authenticated user.
func (c *UserClient) ListSessions(ctx context.Context, ownerID, repoID int64) (*ListSessionResponse, error) {
	ctx, span := c.obsv.Tracer.Start(ctx, "capi.ListSessions")
	defer span.End()

	u := "agents/sessions"
	return c.listSessions(ctx, u)
}

// ListSessionsByRepoID returns a session by its Repo ID
func (c *UserClient) ListSessionsByRepoID(ctx context.Context, repoID int64) (*ListSessionResponse, error) {
	ctx, span := c.obsv.Tracer.Start(ctx, "capi.ListSessionsByRepoID")
	defer span.End()

	u := fmt.Sprintf("agents/sessions/repo/%d", repoID)
	return c.listSessions(ctx, u)
}

// ListSessionsByRepoNWO returns a session by Namespace With Owner
func (c *UserClient) ListSessionsByRepoNWO(ctx context.Context, owner, repo string) (*ListSessionResponse, error) {
	ctx, span := c.obsv.Tracer.Start(ctx, "capi.ListSessionsByRepoNWO")
	defer span.End()

	u := fmt.Sprintf("agents/sessions/nwo/%s/%s", owner, repo)
	return c.listSessions(ctx, u)
}

// ListSessionsByResourceID returns all session of a specific resource id in a repo
func (c *UserClient) ListSessionsByResourceID(ctx context.Context, resourceType string, resourceID int64) (*ListSessionResponse, error) {
	ctx, span := c.obsv.Tracer.Start(ctx, "capi.ListSessionsByResourceID")
	defer span.End()

	u := fmt.Sprintf("agents/sessions/resource/%s/%d", resourceType, resourceID)
	return c.listSessions(ctx, u)
}

// UpdateSessionLogs updates a session logs by its ID
func (c *UserClient) UpdateSessionLogs(ctx context.Context, sessionID string, logs string) error {
	ctx, span := c.obsv.Tracer.Start(ctx, "capi.UpdateSessionLogs")
	defer span.End()

	u := fmt.Sprintf("agents/sessions/%s/logs", sessionID)
	req, err := c.newRequest(ctx, http.MethodPut, u, logs)
	if err != nil {
		return err
	}

	req = req.WithContext(ctx)
	resp, err := c.http.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("failed to update session logs: %s", resp.Status)
	}

	return nil
}

func (c *UserClient) listSessions(ctx context.Context, url string) (*ListSessionResponse, error) {
	req, err := c.newRequest(ctx, http.MethodGet, url, nil)
	if err != nil {
		return nil, err
	}

	req = req.WithContext(ctx)
	resp, err := c.http.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to list sessions: %s", resp.Status)
	}

	var listSessionResponse ListSessionResponse
	if err := json.NewDecoder(resp.Body).Decode(&listSessionResponse); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &listSessionResponse, nil
}

func (c *UserClient) newRequest(ctx context.Context, method, urlStr string, body any) (*http.Request, error) {
	u, err := c.baseURL.Parse(urlStr)
	if err != nil {
		return nil, err
	}

	var buf io.ReadWriter
	if body != nil {
		buf = &bytes.Buffer{}
		enc := json.NewEncoder(buf)
		enc.SetEscapeHTML(false)
		err := enc.Encode(body)
		if err != nil {
			return nil, err
		}
	}

	req, err := http.NewRequest(method, u.String(), buf)
	if err != nil {
		return nil, err
	}

	job, ok := requestctx.Job(ctx)
	if ok {
		req.Header.Set(InteractionIDHeader, job.ID)
	}

	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	req.Header.Set(GitHubUserIDHeader, fmt.Sprintf("%d", c.userID))

	return req, nil
}
