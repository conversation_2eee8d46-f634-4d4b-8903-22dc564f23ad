package agentcallback

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"sync"
	"time"

	"github.com/github/go-stats"
	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/jobutils"
	"github.com/github/sweagentd/internal/observability/exceptions"
)

var registerOnce sync.Once

type AgentRuntimeError struct {
	Content          string
	rollupIdentifier string
}

func (e *AgentRuntimeError) Error() string {
	return fmt.Sprintf("agent runtime error: %s", e.Content)
}

func RollupCheck(err error) (string, bool) {
	if runtimeError, ok := err.(*AgentRuntimeError); ok {
		if runtimeError.rollupIdentifier != "" {
			return runtimeError.rollupIdentifier, true
		}
		return "AgentRuntimeError", true
	}
	return "", false
}

const MaxErrorMessageSize = 1000
const MaxErrorContentSize = 5000

func (m *jobManager) handleError(ctx context.Context, r *http.Request, job *jobs.Job, update jobs.JobUpdate) (*JobResult, error) {
	logger := m.obsv.LoggerWithRequestTelemetry(ctx, r)
	// This is a contract with the runtime. See https://github.com/github/sweagentd/blob/463009a5df2a9297ef617b33504209001cbe42f9/runtime/src/types.ts#L5
	var errorMap map[string]any
	if err := json.Unmarshal([]byte(update.Content), &errorMap); err != nil {
		return nil, fmt.Errorf("failed to unmarshal error: %w", err)
	}

	// The error map may contain stderr and stdout properties, which we don't want
	// to persist, or include in the reported exception, so we remove them here.
	delete(errorMap, "stderr")
	delete(errorMap, "stdout")
	delete(errorMap, "blockedRequests")

	// Then marshal the error map back to JSON, so we can use it in the job error.
	b, err := json.Marshal(errorMap)
	if err != nil {
		return nil, fmt.Errorf("failed to re marshal error: %w", err)
	}
	content := truncate(string(b), MaxErrorContentSize)

	// Make sure we register the rollup check, only doing it once per startup.
	registerOnce.Do(func() { exceptions.AddRollupCheck(RollupCheck) })

	jobErr := &jobs.JobError{Message: content}
	runtimeErr := &AgentRuntimeError{Content: content}

	if text, ok := errorMap["text"].(string); ok {
		truncated := truncate(text, MaxErrorMessageSize)
		jobErr.Message = truncated
		runtimeErr.rollupIdentifier = truncated
	} else {
		runtimeErr.rollupIdentifier = "AgentRuntimeError"
	}

	// Default to reporting, but allow the runtime to skip reporting when a known error occurs
	// The *only* thing skipped when skipping reporting is the Sentry exception report.
	// The error will still be logged, and the job will still be marked as failed.
	// This is useful for known errors that are expected to occur.
	skipReport := false
	if skipReportMapVal, ok := errorMap["skipReport"]; ok {
		if skipReportBool, ok := skipReportMapVal.(bool); ok {
			skipReport = skipReportBool
		}
	}
	if !skipReport {
		m.obsv.LogAndReportError(ctx, runtimeErr, "")
	} else {
		logger.WithError(runtimeErr).Error("a runtime error occurred, but will not be reported to Sentry")
	}

	isVisionFlow := false
	if isVisionFlowMapVal, ok := errorMap["isVisionFlow"]; ok {
		if isVisionFlowBool, ok := isVisionFlowMapVal.(bool); ok {
			isVisionFlow = isVisionFlowBool
		}
	}

	tags := m.obsv.TelemetryTags(ctx).Merge(stats.Tags{
		"is_vision_flow": strconv.FormatBool(isVisionFlow),
		"skip_report":    strconv.FormatBool(skipReport),
	})

	m.obsv.Statter.Counter("agent.job_error_callback", tags, 1)

	if err := m.jobs.PatchJob(ctx, job, jobs.JobPatch{Error: jobErr}); err != nil {
		return nil, fmt.Errorf("failed to patch job: %w", err)
	}

	jobResult := &JobResult{
		Status:      jobs.JobStatusFailed,
		CompletedAt: time.Now().UTC(),
		Result:      errorMap,
	}

	jobutils.UpdateRequestContextJob(ctx, m.obsv, job)
	logger = m.obsv.LoggerWithRequestTelemetry(ctx, r)
	logger.Error("Job error. Processing handleError callback")

	return jobResult, nil
}

// truncate truncates a string to the given length, appending "..." to the end.
// If the provided length is 0, or the string is already shorter than the given
// length, the string is returned as-is.
func truncate(s string, n int) string {
	if n <= 0 {
		return s
	}
	runes := []rune(s)
	if len(runes) <= n {
		return s
	}
	return string(runes[:n]) + "..."
}
