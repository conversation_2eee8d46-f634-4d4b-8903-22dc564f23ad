package agentcallback

import (
	"encoding/json"
	"testing"

	"github.com/github/sweagentd/internal/testutils"
	"github.com/stretchr/testify/require"
)

func TestJobEventUnmarshall(t *testing.T) {
	t.Run("unmarshall job event - telemetry", func(t *testing.T) {
		content := testutils.ReadFixture("../testutils/fixtures/job-event-telemetry.json")
		jobEvent := ProgressJobEvent{}
		err := json.Unmarshal([]byte(content), &jobEvent)

		require.NoError(t, err)
		require.Equal(t, jobEvent.Kind, "telemetry")
		require.Equal(t, jobEvent.Telemetry.Event, "get_completion_with_tools_turn")

		require.Equal(t, 4, len(jobEvent.Telemetry.Properties))
		propertyValue, exists := jobEvent.Telemetry.Properties["model"]
		require.True(t, exists, "expected model to exist in properties")
		require.NotNil(t, propertyValue, "Claude 3.7 Sonnet (Preview)")

		require.Equal(t, 12, len(jobEvent.Telemetry.Metrics))
		metricValue, exists := jobEvent.Telemetry.Metrics["turnDurationMs"]
		require.True(t, exists, "expected turn_duration_ms to exist in metrics")
		require.NotNil(t, metricValue, 12400)
	})

	t.Run("unmarshal job event - log", func(t *testing.T) {
		content := testutils.ReadFixture("../testutils/fixtures/job-event-log.json")
		jobEvent := ProgressJobEvent{}
		err := json.Unmarshal([]byte(content), &jobEvent)

		require.NoError(t, err)
		require.Equal(t, "log", jobEvent.Kind)
		require.Contains(t, jobEvent.Message.StringValue, "undefined")

		require.Equal(t, "", jobEvent.Message.Role)
		require.Equal(t, "", jobEvent.Message.ToolCallID)
		require.Nil(t, jobEvent.Message.ToolCalls)
	})

	t.Run("unmarshal job event - message user", func(t *testing.T) {
		content := testutils.ReadFixture("../testutils/fixtures/job-event-message-user.json")
		jobEvent := ProgressJobEvent{}
		err := json.Unmarshal([]byte(content), &jobEvent)

		require.NoError(t, err)
		require.Equal(t, "message", jobEvent.Kind)
		require.Equal(t, "user", jobEvent.Message.Role)
	})

	t.Run("unmarshall job event - message tool", func(t *testing.T) {
		content := testutils.ReadFixture("../testutils/fixtures/job-event-message-tool.json")
		jobEvent := ProgressJobEvent{}
		err := json.Unmarshal([]byte(content), &jobEvent)

		require.NoError(t, err)
		require.Equal(t, "message", jobEvent.Kind)
		require.Equal(t, "tool", jobEvent.Message.Role)
		require.Equal(t, "tooluse_AtGRSdiVQAScJE_17B7oPg", jobEvent.Message.ToolCallID)

		require.Equal(t, "Claude 3.7 Sonnet", jobEvent.ModelCall.Model)
		require.Equal(t, "c5973912-25d8-4e9d-b604-21a66f91ce3b", jobEvent.ModelCall.APIID)

		require.Equal(t, 8, len(jobEvent.Telemetry.Properties))
		propertyValue, exists := jobEvent.Telemetry.Properties["toolName"]
		require.True(t, exists, "expected toolName to exist in properties")
		require.NotNil(t, propertyValue, "reply_to_comment")

		require.Equal(t, 4, len(jobEvent.Telemetry.Metrics))
		metricValue, exists := jobEvent.Telemetry.Metrics["durationMs"]
		require.True(t, exists, "expected durationMs to exist in metrics")
		require.NotNil(t, metricValue, 2967)
		require.Contains(t, jobEvent.Message.Content, "My Content")
	})

	t.Run("unmarshal job event - message assistant", func(t *testing.T) {
		content := testutils.ReadFixture("../testutils/fixtures/job-event-message-assistant.json")
		jobEvent := ProgressJobEvent{}
		err := json.Unmarshal([]byte(content), &jobEvent)

		require.NoError(t, err)
		require.Equal(t, "message", jobEvent.Kind)
		require.Equal(t, "assistant", jobEvent.Message.Role)
		require.Equal(t, 1, len(jobEvent.Message.ToolCalls))
		require.Equal(t, "function", jobEvent.Message.ToolCalls[0].Type)
	})

	t.Run("unmarshal job event - response", func(t *testing.T) {
		content := testutils.ReadFixture("../testutils/fixtures/job-event-response.json")
		jobEvent := ProgressJobEvent{}
		err := json.Unmarshal([]byte(content), &jobEvent)

		require.NoError(t, err)
		require.NotNil(t, jobEvent.Kind)
		require.Equal(t, "response", jobEvent.Kind)
		require.Equal(t, "assistant", jobEvent.Response.Role)
	})

	t.Run("unmarshal job event - session log", func(t *testing.T) {
		content := testutils.ReadFixture("../testutils/fixtures/job-event-session-log.json")
		jobEvent := ProgressJobEvent{}
		err := json.Unmarshal([]byte(content), &jobEvent)

		require.NoError(t, err)
		require.NotNil(t, jobEvent.Kind)
		require.Equal(t, "session_log", jobEvent.Kind)

		require.Equal(t, "72f7b1a0-1fe7-454c-a6b8-338cd17970e3", jobEvent.SessionLog.Id)

		require.Equal(t, "Claude 3.7 Sonnet", jobEvent.SessionLog.Model)

		require.Equal(t, int64(69), jobEvent.SessionLog.Usage.CompletionTokens)
		require.Equal(t, int64(6963), jobEvent.SessionLog.Usage.PromptTokens)
		require.Equal(t, int64(7032), jobEvent.SessionLog.Usage.TotalTokens)

		require.Equal(t, "chat.completion.chunk", jobEvent.SessionLog.Object)

		require.NotNil(t, jobEvent.SessionLog)

		require.Equal(t, "tool_calls", jobEvent.SessionLog.Choices[0].FinishReason)
		require.Equal(t, "assistant", jobEvent.SessionLog.Choices[0].Delta.Role)

		require.Equal(t, "tool_calls", jobEvent.SessionLog.Choices[1].FinishReason)
		require.Equal(t, "assistant", jobEvent.SessionLog.Choices[1].Delta.Role)
		require.Equal(t, "bash", jobEvent.SessionLog.Choices[1].Delta.Toolcalls[0].Function.Name)
		require.Equal(t, "function", jobEvent.SessionLog.Choices[1].Delta.Toolcalls[0].Type)
		require.Equal(t, "tooluse_cqYOO6bbQvCuUYW5GhgISw", jobEvent.SessionLog.Choices[1].Delta.Toolcalls[0].ID)

		require.Equal(t, int64(5212), jobEvent.ModelCallDurationMs)
	})

	t.Run("unmarshal job event - model call failure", func(t *testing.T) {
		content := testutils.ReadFixture("../testutils/fixtures/job-event-model-call-failure.json")
		jobEvent := ProgressJobEvent{}
		err := json.Unmarshal([]byte(content), &jobEvent)

		require.NoError(t, err)
		require.NotNil(t, jobEvent.Kind)
		require.Equal(t, "model_call_failure", jobEvent.Kind)

		require.Empty(t, jobEvent.SessionLog.Id)

		require.NotNil(t, jobEvent.ModelCall)
		require.Equal(t, "claude-sonnet-4", jobEvent.ModelCall.Model)
		require.Equal(t, int64(400), jobEvent.ModelCall.Status)
		require.Equal(t, "0602:29277D:245ABBC:2CAE3B9:685970E1", jobEvent.ModelCall.RequestId)
		require.Contains(t, jobEvent.ModelCall.Error, "one or more attachments was not accessible")
		require.Equal(t, int64(339), jobEvent.ModelCallDurationMs)
	})
}
