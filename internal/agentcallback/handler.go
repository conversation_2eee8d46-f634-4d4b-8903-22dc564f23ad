package agentcallback

import (
	"encoding/json"
	"errors"
	"net/http"

	"github.com/github/go-mark"
	"github.com/github/sweagentd/internal/capi"
	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/githubtwirp"
	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/jobutils"
	"github.com/github/sweagentd/internal/observability"
	"github.com/github/sweagentd/internal/requestctx"
)

type Handler struct {
	obsv        observability.Exporters
	manager     *jobManager
	ffClient    featureflags.Client
	ghTwirp     githubtwirp.ClientInterface
	githubAppID int64
}

func NewHandler(obsv observability.Exporters, capiClient capi.Client, jobs jobs.JobsStore, clients github.ClientFactoryInterface, ffClient featureflags.Client, ghTwirp githubtwirp.ClientInterface, githubAppID int64) *Handler {
	return &Handler{obsv, newManager(obsv, jobs, clients, ffClient), ffClient, ghTwirp, githubAppID}
}

func (h *Handler) HandleCallback(w http.ResponseWriter, r *http.Request) {
	ctx, span := h.obsv.Tracer.Start(r.Context(), "agentcallback.HandleJobs")
	defer span.End()
	r = r.WithContext(ctx)

	var update jobs.JobUpdate
	if err := json.NewDecoder(r.Body).Decode(&update); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	// Require that a user is logged in to process the request
	user, ok := requestctx.User(ctx)
	if !ok {
		http.Error(w, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
		return
	}

	// AuthZ: This endpoint should only be called by the runtime, so make sure it's for our GitHub App
	if user.TokenInfo.ApplicationID != uint64(h.githubAppID) {
		http.Error(w, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
		return
	}

	enabled, err := githubtwirp.IsCopilotSweEnabled(r.Context(), h.ghTwirp, int64(user.ID), update.RepoID)
	if err != nil {
		h.obsv.Logger.WithError(err).Error("Failed to check enablement")
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}
	if !enabled {
		h.obsv.Logger.Info("Copilot coder agent not enabled")
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	// Get the job to validate nonce
	job, err := h.manager.jobs.GetJob(ctx, update.RepoOwnerID, update.RepoID, update.JobID)
	if err != nil {
		// Don't reveal too much information in the error message
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	jobutils.UpdateRequestContextJob(ctx, h.obsv, job)
	if err := requestctx.AddJobUpdate(ctx, &requestctx.CtxJobUpdate{
		Action:    string(update.Action),
		Kind:      string(update.Kind),
		CreatedAt: update.CreatedAt.Unix(),
	}); err != nil {
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	logger := h.obsv.LoggerWithTelemetry(ctx)

	// Validate nonce header if job has a nonce hash
	if h.ffClient.IsEnabledForUser(ctx, "sweagentd_enforce_job_nonce", int64(user.ID)) {
		nonce := r.Header.Get(jobs.JobNonceHeader)
		if nonce == "" || jobs.HashNonce(nonce) != job.NonceHash {
			logger.Info("Invalid or missing nonce header")
			http.Error(w, "Invalid or missing job nonce", http.StatusBadRequest)
			return
		}
	}

	if err := h.manager.HandleJobUpdate(ctx, r, job, update, int64(user.ID)); err != nil {
		var badRequestErr *badRequestError
		if errors.As(err, &badRequestErr) {
			http.Error(w, err.Error(), http.StatusBadRequest)
			return
		}

		if errors.Is(err, mark.ErrNotFound) {
			http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
			return
		}

		h.obsv.LogAndReportError(ctx, err, "Failed to process agent callback")
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	logger.Info("processed agent callback")
	w.WriteHeader(http.StatusOK)
}

type badRequestError struct {
	message string
}

func (e *badRequestError) Error() string {
	return e.message
}
