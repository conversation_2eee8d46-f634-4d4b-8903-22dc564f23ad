package agentcallback

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/github/go-exceptions"
	"github.com/github/go-exceptions/exporters/writer"
	"github.com/github/go-exceptions/stacktracers/pkgerrors"
	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/observability"
	"github.com/stretchr/testify/require"
)

func TestRollupCheck(t *testing.T) {
	t.Run("error is not AgentRuntimeError", func(t *testing.T) {
		regularError := errors.New("regular error")

		identifier, ok := RollupCheck(regularError)

		require.Empty(t, identifier)
		require.False(t, ok)
	})

	t.Run("error is AgentRuntimeError with rollupIdentifier", func(t *testing.T) {
		runtimeError := &AgentRuntimeError{
			Content:          "Some error content",
			rollupIdentifier: "CustomIdentifier",
		}

		identifier, ok := RollupCheck(runtimeError)

		require.Equal(t, "CustomIdentifier", identifier)
		require.True(t, ok)
	})

	t.Run("error is AgentRuntimeError with no specific identifier", func(t *testing.T) {
		runtimeError := &AgentRuntimeError{
			Content: "Some error content",
		}

		identifier, ok := RollupCheck(runtimeError)

		require.Equal(t, "AgentRuntimeError", identifier)
		require.True(t, ok)
	})

	t.Run("nil error should not panic", func(t *testing.T) {
		identifier, ok := RollupCheck(nil)

		require.Empty(t, identifier)
		require.False(t, ok)
	})
}

// TestTruncate tests the truncate helper function used by RollupCheck
func TestTruncate(t *testing.T) {
	t.Run("string shorter than limit", func(t *testing.T) {
		result := truncate("short", 10)
		require.Equal(t, "short", result)
	})

	t.Run("string equal to limit", func(t *testing.T) {
		result := truncate("exactly5", 8)
		require.Equal(t, "exactly5", result)
	})

	t.Run("string longer than limit", func(t *testing.T) {
		result := truncate("this is a long string", 7)
		require.Equal(t, "this is...", result)
	})

	t.Run("utf8 characters", func(t *testing.T) {
		// This string has 5 runes: "こんにちは"
		result := truncate("こんにちは", 3)
		require.Equal(t, "こんに...", result)
	})

	t.Run("empty string", func(t *testing.T) {
		result := truncate("", 5)
		require.Equal(t, "", result)
	})

	t.Run("negative length", func(t *testing.T) {
		result := truncate("test", -1)
		require.Equal(t, "test", result)
	})

	t.Run("zero length", func(t *testing.T) {
		result := truncate("test", 0)
		require.Equal(t, "test", result)
	})
}

func TestHandleErrorSkipReport(t *testing.T) {
	t.Run("skipReport true - error logged but not reported", func(t *testing.T) {
		exceptionStream := new(bytes.Buffer)
		reporter, err := exceptions.NewReporter(
			exceptions.WithApplication("sweagentd"),
			exceptions.WithExporter(writer.NewExporter(exceptionStream)),
			exceptions.WithStacktraceFunc(pkgerrors.NewStackTracer()),
		)
		require.NoError(t, err)

		obsv := observability.NewNoopExporters()
		obsv.Reporter = reporter

		jobStore := jobs.NewMemoryStore()
		jobMgr := &jobManager{
			obsv: obsv,
			jobs: jobStore,
		}

		ctx := addUserToContext(t, context.Background())
		req := httptest.NewRequest(http.MethodPost, "/test", nil)

		// Create test job
		job := &jobs.Job{
			ID:        "test-job-skip-true",
			OwnerID:   1,
			RepoID:    2,
			RepoOwner: "owner",
			RepoName:  "repo",
			ActorID:   1,
		}

		_, err = jobMgr.jobs.CreateJob(ctx, job)
		require.NoError(t, err)

		// Create error content with skipReport: true
		errorContent := map[string]any{
			"text":       "Test error message",
			"skipReport": true,
		}
		contentBytes, err := json.Marshal(errorContent)
		require.NoError(t, err)

		update := jobs.JobUpdate{
			Content: string(contentBytes),
		}

		// Call handleError
		result, err := jobMgr.handleError(ctx, req, job, update)

		// Verify the result
		require.NoError(t, err)
		require.NotNil(t, result)
		require.Equal(t, jobs.JobStatusFailed, result.Status)
		require.NotNil(t, result.CompletedAt)

		// Verify error was NOT reported to Sentry (mock reporter)
		require.Empty(t, exceptionStream, "Error should not be reported when skipReport is true")

		// Verify job was updated with error
		updatedJob, err := jobStore.GetJob(ctx, job.OwnerID, job.RepoID, job.ID)
		require.NoError(t, err)
		require.NotNil(t, updatedJob.Error)
		require.Equal(t, "Test error message", updatedJob.Error.Message)
	})

	t.Run("skipReport false - error logged and reported", func(t *testing.T) {
		exceptionStream := new(bytes.Buffer)
		reporter, err := exceptions.NewReporter(
			exceptions.WithApplication("sweagentd"),
			exceptions.WithExporter(writer.NewExporter(exceptionStream)),
			exceptions.WithStacktraceFunc(pkgerrors.NewStackTracer()),
		)
		require.NoError(t, err)

		obsv := observability.NewNoopExporters()
		obsv.Reporter = reporter

		jobStore := jobs.NewMemoryStore()
		jobMgr := &jobManager{
			obsv: obsv,
			jobs: jobStore,
		}

		ctx := addUserToContext(t, context.Background())
		req := httptest.NewRequest(http.MethodPost, "/test", nil)

		// Create test job
		job := &jobs.Job{
			ID:        "test-job-skip-false",
			OwnerID:   1,
			RepoID:    2,
			RepoOwner: "owner",
			RepoName:  "repo",
			ActorID:   1,
		}

		_, err = jobMgr.jobs.CreateJob(ctx, job)
		require.NoError(t, err)

		// Create error content with skipReport: false
		errorContent := map[string]any{
			"text":       "Test error message with reporting",
			"skipReport": false,
		}
		contentBytes, err := json.Marshal(errorContent)
		require.NoError(t, err)

		update := jobs.JobUpdate{
			Content: string(contentBytes),
		}

		// Call handleError
		result, err := jobMgr.handleError(ctx, req, job, update)

		// Verify the result
		require.NoError(t, err)
		require.NotNil(t, result)
		require.Equal(t, jobs.JobStatusFailed, result.Status)

		// Verify error WAS reported to Sentry (mock reporter)
		require.NotEmpty(t, exceptionStream)

		// Verify the reported error is of correct type
		require.Contains(t, exceptionStream.String(), "AgentRuntimeError")
		require.Contains(t, exceptionStream.String(), "Test error message with reporting")

		// Verify job was updated with error
		updatedJob, err := jobStore.GetJob(ctx, job.OwnerID, job.RepoID, job.ID)
		require.NoError(t, err)
		require.NotNil(t, updatedJob.Error)
		require.Equal(t, "Test error message with reporting", updatedJob.Error.Message)
	})
}
