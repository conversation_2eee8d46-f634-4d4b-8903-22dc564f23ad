package agentcallback

import (
	"encoding/json"
	"fmt"
	"maps"
	"strings"
	"time"

	"github.com/github/github-telemetry-go/kvp"
	sweagentv0 "github.com/github/hydro-schemas-go/hydro/schemas/sweagentd/v0"
	sweagentEntitiesv0 "github.com/github/hydro-schemas-go/hydro/schemas/sweagentd/v0/entities"
	"github.com/github/sweagentd/internal/jobs"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func GenerateProgressJobEventTelemetry(event ProgressJobEvent) []kvp.Field {
	kvps := make([]kvp.Field, 0)

	kvps = append(kvps, kvp.String("job.update.event.kind", event.Kind))

	if event.Message.Role != "" {
		kvps = append(kvps, kvp.String("job.update.event.message.role", event.Message.Role))
	}
	if event.Message.ToolCallID != "" {
		kvps = append(kvps, kvp.String("job.update.event.message.tool_call_id", event.Message.ToolCallID))
	}

	// Populate Response tags
	if event.Response.Role != "" {
		kvps = append(kvps, kvp.String("job.update.event.response.role", event.Response.Role))
	}

	// Populate SessionLog tags
	if event.SessionLog.Id != "" {
		kvps = append(kvps, kvp.String("job.update.event.session_log.id", event.SessionLog.Id))
	}
	if event.SessionLog.Model != "" {
		kvps = append(kvps, kvp.String("job.update.event.session_log.model", event.SessionLog.Model))
	}
	if event.SessionLog.Object != "" {
		kvps = append(kvps, kvp.String("job.update.event.session_log.object", event.SessionLog.Object))
	}
	if event.SessionLog.Usage.CompletionTokens != 0 {
		kvps = append(kvps, kvp.Float64("job.update.event.session_log.usage.completion_tokens", float64(event.SessionLog.Usage.CompletionTokens)))
	}
	if event.SessionLog.Usage.PromptTokens != 0 {
		kvps = append(kvps, kvp.Float64("job.update.event.session_log.usage.prompt_tokens", float64(event.SessionLog.Usage.PromptTokens)))
	}
	if event.SessionLog.Usage.TotalTokens != 0 {
		kvps = append(kvps, kvp.Float64("job.update.event.session_log.usage.total_tokens", float64(event.SessionLog.Usage.TotalTokens)))
	}
	if len(event.SessionLog.Choices) > 0 {
		// Local struct to extract relevant choice data for telemetry
		type choiceTelemetry struct {
			FinishReason  string `json:"finish_reason,omitempty"`
			Role          string `json:"role,omitempty"`
			ToolCallCount int    `json:"tool_calls_count,omitempty"`
		}

		// Extract information from each choice without PII data
		choiceTelemetryData := make([]choiceTelemetry, len(event.SessionLog.Choices))
		for i, choice := range event.SessionLog.Choices {
			choiceTelemetryData[i] = choiceTelemetry{
				FinishReason:  choice.FinishReason,
				Role:          choice.Delta.Role,
				ToolCallCount: len(choice.Delta.Toolcalls),
			}
		}
		choicesJSON, err := json.Marshal(choiceTelemetryData)
		if err == nil {
			kvps = append(kvps, kvp.String("job.update.event.session_log.choices", string(choicesJSON)))
		}
	}

	// Populate Telemetry tags
	if event.Telemetry.Event != "" {
		kvps = append(kvps, kvp.String("job.update.event.telemetry.event", event.Telemetry.Event))
	}
	// we cant just log every value, we need to filter privacy related out from here TODO
	for key, value := range event.Telemetry.Properties {
		kvps = append(kvps, kvp.String(fmt.Sprintf("job.update.event.telemetry.properties.%s", strings.ToLower(key)), value))
	}
	for key, value := range event.Telemetry.Metrics {
		kvps = append(kvps, kvp.Float64(fmt.Sprintf("job.update.event.telemetry.metrics.%s", strings.ToLower(key)), value))
	}

	return kvps
}

func ToJobProgress(job *jobs.Job, update jobs.JobUpdate, progressEvent ProgressJobEvent) *sweagentv0.JobProgress {
	event := &sweagentv0.JobProgress{
		Job:          jobs.ToHydro(job),
		CreatedAt:    timestamppb.New(update.CreatedAt),
		Kind:         sweagentEntitiesv0.JobUpdateKind_PROGRESS,
		ProgressKind: &progressEvent.Kind,
	}
	if job.Experiments != nil && job.Experiments.AssignmentContext != "" {
		event.ExpAssignmentContext = job.Experiments.AssignmentContext
	}
	role, ok := progressEvent.GetRole()
	if ok {
		event.Role = &role
	}
	if progressEvent.ModelCall.APIID != "" {
		event.ModelCallId = progressEvent.ModelCall.APIID
	} else if progressEvent.SessionLog.Id != "" {
		event.ModelCallId = progressEvent.SessionLog.Id
	} else {
		for k, v := range progressEvent.Telemetry.Properties {
			if k == "api_call_id" {
				event.ModelCallId = v
			}
		}
	}

	return event
}

func ToModelCall(job *jobs.Job, update jobs.JobUpdate, progressEvent ProgressJobEvent) *sweagentv0.ModelCall {
	e := &sweagentv0.ModelCall{
		JobId:        job.ID,
		CreatedAt:    timestamppb.New(update.CreatedAt),
		ApiCreatedAt: timestamppb.New(time.Unix(progressEvent.SessionLog.Created, 0)),
		DurationMs:   progressEvent.ModelCallDurationMs,
	}
	if progressEvent.SessionLog.Id != "" {
		e.ApiId = progressEvent.SessionLog.Id
		e.Model = progressEvent.SessionLog.Model
		e.CompletionTokensCount = progressEvent.SessionLog.Usage.CompletionTokens
		e.PromptTokensCount = progressEvent.SessionLog.Usage.PromptTokens
		e.TotalTokensCount = progressEvent.SessionLog.Usage.TotalTokens
		e.DurationMs = progressEvent.ModelCallDurationMs
	} else {
		e.Model = progressEvent.ModelCall.Model
		e.DurationMs = progressEvent.ModelCallDurationMs
		e.RequestId = progressEvent.ModelCall.RequestId
		e.Status = progressEvent.ModelCall.Status
	}
	if job.Experiments != nil && job.Experiments.AssignmentContext != "" {
		e.ExpAssignmentContext = job.Experiments.AssignmentContext
	}

	for _, choice := range progressEvent.SessionLog.Choices {
		if len(choice.Delta.Toolcalls) > 0 {
			e.ToolCallsCount = int64(len(choice.Delta.Toolcalls))
			break // there is only ever one choice with tool calls
		}
	}

	return e
}

func ToRestrictedModelCall(job *jobs.Job, update jobs.JobUpdate, progressEvent ProgressJobEvent) *sweagentv0.RestrictedModelCall {
	e := &sweagentv0.RestrictedModelCall{
		JobId:        job.ID,
		CreatedAt:    timestamppb.New(update.CreatedAt),
		ApiCreatedAt: timestamppb.New(time.Unix(progressEvent.SessionLog.Created, 0)),
		DurationMs:   progressEvent.ModelCallDurationMs,
		Messages:     progressEvent.RequestMessages,
	}

	if progressEvent.SessionLog.Id != "" {
		e.ApiId = progressEvent.SessionLog.Id
		e.Model = progressEvent.SessionLog.Model
		e.CompletionTokensCount = progressEvent.SessionLog.Usage.CompletionTokens
		e.PromptTokensCount = progressEvent.SessionLog.Usage.PromptTokens
		e.TotalTokensCount = progressEvent.SessionLog.Usage.TotalTokens
	} else {
		e.Model = progressEvent.ModelCall.Model
		e.RequestId = progressEvent.ModelCall.RequestId
		e.Status = progressEvent.ModelCall.Status
		e.Error = progressEvent.ModelCall.Error
	}
	if job.Experiments != nil && job.Experiments.AssignmentContext != "" {
		e.ExpAssignmentContext = job.Experiments.AssignmentContext
	}

	choices := []*sweagentEntitiesv0.ModelChoice{}
	for _, choice := range progressEvent.SessionLog.Choices {
		c := sweagentEntitiesv0.ModelChoice{FinishReason: choice.FinishReason, Content: choice.Delta.Content, Refusal: choice.Delta.Refusal}
		toolCallIds := []string{}
		for _, toolcall := range choice.Delta.Toolcalls {
			if toolcall.ID != "" {
				toolCallIds = append(toolCallIds, toolcall.ID)
			}
		}
		c.ToolCallIds = toolCallIds
		choices = append(choices, &c)
		if len(choice.Delta.Toolcalls) > 0 {
			e.ToolCallsCount = int64(len(choice.Delta.Toolcalls))
		}
	}

	e.Choice = choices

	return e
}

func ToTelemetry(job *jobs.Job, update jobs.JobUpdate, progressEvent ProgressJobEvent) *sweagentv0.Telemetry {
	event := &sweagentv0.Telemetry{
		JobId:      job.ID,
		Kind:       progressEvent.Telemetry.Event,
		CreatedAt:  timestamppb.New(update.CreatedAt),
		Properties: progressEvent.Telemetry.Properties,
		Metrics:    progressEvent.Telemetry.Metrics,
	}
	if job.Experiments != nil && job.Experiments.AssignmentContext != "" {
		event.ExpAssignmentContext = job.Experiments.AssignmentContext
	}
	for k, v := range progressEvent.Telemetry.Properties {
		if k == "api_call_id" {
			event.ModelCallId = v
		}
	}
	return event
}

func ToRestrictedTelemetry(job *jobs.Job, update jobs.JobUpdate, progressEvent ProgressJobEvent) *sweagentv0.RestrictedTelemetry {
	event := &sweagentv0.RestrictedTelemetry{
		JobId:     job.ID,
		Kind:      progressEvent.Telemetry.Event,
		CreatedAt: timestamppb.New(update.CreatedAt),
		Metrics:   progressEvent.Telemetry.Metrics,
	}
	if job.Experiments != nil && job.Experiments.AssignmentContext != "" {
		event.ExpAssignmentContext = job.Experiments.AssignmentContext
	}
	for k, v := range progressEvent.Telemetry.Properties {
		if k == "api_call_id" {
			event.ModelCallId = v
		}
	}
	// Create a new map to hold combined properties
	completeProperties := make(map[string]string)

	maps.Copy(completeProperties, progressEvent.Telemetry.Properties)
	maps.Copy(completeProperties, progressEvent.Telemetry.RestrictedProperties)

	event.Properties = completeProperties
	return event
}

func ToToolCall(job *jobs.Job, update jobs.JobUpdate, progressEvent ProgressJobEvent) *sweagentv0.ToolCall {
	event := &sweagentv0.ToolCall{
		JobId:       job.ID,
		ModelCallId: "",
		CreatedAt:   timestamppb.New(update.CreatedAt),
	}
	if job.Experiments != nil && job.Experiments.AssignmentContext != "" {
		event.ExpAssignmentContext = job.Experiments.AssignmentContext
	}
	for k, v := range progressEvent.Telemetry.Properties {
		if k == "resultType" && v == "success" {
			event.ResultType = sweagentEntitiesv0.ToolResultType_SUCCESS
		} else if k == "resultType" && v == "failure" {
			event.ResultType = sweagentEntitiesv0.ToolResultType_FAILURE
		}
		if k == "toolName" {
			event.ToolName = v
		}
		if k == "toolCallId" {
			event.ToolCallId = v
		}
		if k == "api_call_id" {
			event.ModelCallId = v
		}
	}

	for k, v := range progressEvent.Telemetry.Metrics {
		if k == "durationMs" {
			event.DurationMs = int64(v)
		}
	}
	return event
}

func ToRestrictedToolCall(job *jobs.Job, update jobs.JobUpdate, progressEvent ProgressJobEvent) *sweagentv0.RestrictedToolCall {
	event := &sweagentv0.RestrictedToolCall{
		JobId:       job.ID,
		ModelCallId: "",
		CreatedAt:   timestamppb.New(update.CreatedAt),
		Content:     progressEvent.Message.Content,
	}
	if job.Experiments != nil && job.Experiments.AssignmentContext != "" {
		event.ExpAssignmentContext = job.Experiments.AssignmentContext
	}
	for k, v := range progressEvent.Telemetry.Properties {
		if k == "resultType" && v == "success" {
			event.ResultType = sweagentEntitiesv0.ToolResultType_SUCCESS
		} else if k == "resultType" && v == "failure" {
			event.ResultType = sweagentEntitiesv0.ToolResultType_FAILURE
		}
		if k == "toolName" {
			event.ToolName = v
		}
		if k == "toolCallId" {
			event.ToolCallId = v
		}
		if k == "api_call_id" {
			event.ModelCallId = v
		}
	}

	for k, v := range progressEvent.Telemetry.RestrictedProperties {
		if k == "arguments" {
			event.Arguments = v
		}
		// If the tool is not a 1P integration, the unrestricted toolname is obfuscated,
		if k == "toolName" {
			event.ToolName = v
		}
		if k == "error" {
			event.Error = v
		}
	}

	for k, v := range progressEvent.Telemetry.Metrics {
		if k == "durationMs" {
			event.DurationMs = int64(v)
		}
	}

	return event
}
