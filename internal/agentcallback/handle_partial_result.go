package agentcallback

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/github/sweagentd/internal/jobs"
)

type partialResultUpdate struct {
	BranchName string `json:"branchName"`
	Message    string `json:"message"`
}

type partialResult struct {
	PRNumber int `json:"prNumber"`
}

// partialResult is called when the runtime calls the progress_update tool
func (m *jobManager) handlePartialResult(ctx context.Context, r *http.Request, job *jobs.Job, update jobs.JobUpdate) (*JobResult, error) {
	var prp partialResultUpdate
	if err := json.Unmarshal([]byte(update.Content), &prp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal partial result: %w", err)
	}

	// We now only want to continually update the PR if it's the initial
	// job and we're updating the body of the PR itself. This is indicated
	// by the job's AgentAction being "fix".
	if job.Action == jobs.AgentActionFix {
		ghclient, err := m.clients.NewClientFromRepo(ctx, job.RepoID, job.OwnerID)
		if err != nil {
			return nil, fmt.Errorf("failed to create GitHub client: %w", err)
		}

		if err := m.updatePullRequest(ctx, ghclient, job, nil, &prp.Message); err != nil {
			return nil, fmt.Errorf("failed to update pull request: %w", err)
		}
	}

	jobResult := &JobResult{
		Status: jobs.JobStatusRunning,
		Result: &partialResult{
			PRNumber: job.PullRequestNumber,
		},
	}

	logger := m.obsv.LoggerWithRequestTelemetry(ctx, r)
	logger.Info("Job in progress. Processing handlePartialResult callback")
	if m.ffClient.IsEnabledForUserOrRepo(ctx, "sweagentd_publish_telemetry_events_to_hydro", job.ActorID, job.RepoID) {
		jobProgressEvent := ToJobProgress(job, update, ProgressJobEvent{})
		err := m.obsv.HydroPbl.Publish(jobProgressEvent)

		if err != nil {
			logger.WithContext(ctx).WithError(err).Error("failed to publish job_progress - partialResult event")
		}
	}

	return jobResult, nil

}
