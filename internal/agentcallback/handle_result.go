package agentcallback

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/github/sweagentd/internal/firewall"
	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/jobutils"
)

func (m *jobManager) handleResult(ctx context.Context, r *http.Request, job *jobs.Job, update jobs.JobUpdate) (*JobResult, error) {
	ghclient, err := m.clients.NewClientFromRepo(ctx, job.RepoID, job.OwnerID)
	if err != nil {
		return nil, fmt.Errorf("failed to create GitHub client: %w", err)
	}

	switch update.Action {
	case jobs.AgentActionFix:
		return m.handleFixResult(ctx, r, ghclient, job, update)
	case jobs.AgentActionFixPRComment:
		return m.handleFixPRCommentResult(ctx, r, ghclient, job, update)
	}

	return nil, &badRequestError{"unknown action: " + string(update.Action)}
}

type fixResultUpdate struct {
	Diff            string                    `json:"diff"`
	BranchName      string                    `json:"branchName"`
	PRTitle         string                    `json:"prTitle"`
	PRDescription   string                    `json:"prDescription"`
	BlockedRequests []firewall.BlockedRequest `json:"blockedRequests"`
}

type fixResult struct {
	PRNumber        int                       `json:"prNumber"`
	BlockedRequests []firewall.BlockedRequest `json:"blockedRequests"`
}

func (m *jobManager) handleFixResult(ctx context.Context, r *http.Request, ghclient github.ClientInterface, job *jobs.Job, update jobs.JobUpdate) (*JobResult, error) {
	var frp fixResultUpdate
	if err := json.Unmarshal([]byte(update.Content), &frp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal fix result: %w", err)
	}

	if frp.PRTitle == "" || frp.PRDescription == "" {
		return nil, errors.New("PRTitle and PRDescription must both be non-empty")
	}

	title := frp.PRTitle
	body := frp.PRDescription
	if len(frp.BlockedRequests) > 0 {
		firewallComment, err := firewall.GenerateFirewallComment(frp.BlockedRequests, job)
		if err != nil {
			return nil, fmt.Errorf("failed to generate firewall comment: %w", err)
		}
		if firewallComment != "" {
			body += "\n\n" + firewallComment
		}
	}

	if err := m.updatePullRequest(ctx, ghclient, job, &title, &body); err != nil {
		return nil, fmt.Errorf("failed to update pull request: %w", err)
	}

	jobResult := &JobResult{
		Status:      jobs.JobStatusCompleted, // TS code shows "succeeded" we've codified it as "completed"
		CompletedAt: time.Now().UTC(),
		Result: &fixResult{
			PRNumber:        job.PullRequestNumber,
			BlockedRequests: frp.BlockedRequests,
		},
	}

	jobutils.UpdateRequestContextJob(ctx, m.obsv, job)
	m.obsv.LoggerWithRequestTelemetry(ctx, r).WithFields(firewall.GenerateFirewallTelemetry(frp.BlockedRequests)...).
		Info("Job finished. Processing handleFixResult callback")

	return jobResult, nil
}

func (m *jobManager) handleFixPRCommentResult(ctx context.Context, r *http.Request, ghclient github.ClientInterface, job *jobs.Job, update jobs.JobUpdate) (*JobResult, error) {
	var fp fixResultUpdate
	if err := json.Unmarshal([]byte(update.Content), &fp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal fix pr comment result: %w", err)
	}

	if job.ReplyCommentID != 0 {
		if len(fp.BlockedRequests) > 0 {
			firewallComment, err := firewall.GenerateFirewallComment(fp.BlockedRequests, job)
			if err != nil {
				return nil, fmt.Errorf("failed to generate firewall comment: %w", err)
			}
			if firewallComment != "" {
				comment, err := ghclient.GetComment(ctx, job.RepoOwner, job.RepoName, job.ReplyCommentID)
				if err != nil || comment == nil {
					return nil, fmt.Errorf("failed to get comment: %w", err)
				}

				body := fmt.Sprintf("%s\n\n%s", comment.GetBody(), firewallComment)
				if _, err := ghclient.UpdateComment(ctx, job.RepoOwner, job.RepoName, job.ReplyCommentID, body); err != nil {
					return nil, fmt.Errorf("failed to update comment: %w", err)
				}
			}
		}
	} else if len(fp.BlockedRequests) > 0 {
		// If there's no comment to update, we need to create one if there's any blocked network traffic
		// TODO: Also update session logs in this case - but we need the details so just a timeline event isn't enough
		firewallComment, err := firewall.GenerateFirewallComment(fp.BlockedRequests, job)
		if err != nil {
			return nil, fmt.Errorf("failed to generate firewall comment: %w", err)
		}
		body := fmt.Sprintf("%s\n\n%s", firewall.FirewallStandAloneCommentPrefix, firewallComment)
		if _, err := ghclient.CreateComment(ctx, job.RepoID, job.PullRequestNumber, body); err != nil {
			return nil, fmt.Errorf("failed to create comment: %w", err)
		}
	}

	if m.ffClient.IsEnabledForUserOrRepo(ctx, "copilot_swe_agent_sync_pr_title_description", job.ActorID, job.RepoID) {
		if fp.PRTitle != "" || fp.PRDescription != "" {
			if err := m.updatePullRequest(ctx, ghclient, job, &fp.PRTitle, &fp.PRDescription); err != nil {
				return nil, fmt.Errorf("failed to update pull request: %w", err)
			}
		}
	}

	jobResult := &JobResult{
		Status:      jobs.JobStatusCompleted, // TS code shows "succeeded" we've codified it as "completed"
		CompletedAt: time.Now().UTC(),
		Result: &fixResult{
			PRNumber:        job.PullRequestNumber,
			BlockedRequests: fp.BlockedRequests,
		},
	}

	jobutils.UpdateRequestContextJob(ctx, m.obsv, job)
	logger := m.obsv.LoggerWithRequestTelemetry(ctx, r).WithFields(firewall.GenerateFirewallTelemetry(fp.BlockedRequests)...)
	logger.Info("Job finished. Processing handleFixPRCommentResult callback")

	return jobResult, nil
}
