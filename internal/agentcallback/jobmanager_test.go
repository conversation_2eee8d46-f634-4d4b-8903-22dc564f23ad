package agentcallback

import (
	"testing"

	"github.com/github/sweagentd/internal/jobutils"
	"github.com/stretchr/testify/assert"
)

func TestExtractCoreBodyAndSuffixFromPR(t *testing.T) {
	m := &jobManager{}

	tests := []struct {
		name           string
		prBody         string
		expectedCore   string
		expectedSuffix string
		expectedTip    string
	}{
		{
			name:           "empty body",
			prBody:         "",
			expectedCore:   "",
			expectedSuffix: "",
			expectedTip:    "",
		},
		{
			name:           "core body only",
			prBody:         "This is the main PR description",
			expectedCore:   "This is the main PR description",
			expectedSuffix: "",
			expectedTip:    "",
		},
		{
			name: "body with suffix separator only",
			prBody: "This is the main PR description" +
				suffixSeparator +
				"This is the suffix content",
			expectedCore:   "This is the main PR description",
			expectedSuffix: suffixSeparator + "This is the suffix content",
			expectedTip:    "",
		},
		{
			name: "body with tip separator only",
			prBody: "This is the main PR description" +
				tipSeparator +
				"This is a helpful tip",
			expectedCore:   "This is the main PR description",
			expectedSuffix: "",
			expectedTip:    tipSeparator + "This is a helpful tip",
		},
		{
			name: "body with both suffix and tip separators",
			prBody: "This is the main PR description" +
				suffixSeparator +
				"This is the suffix content" +
				tipSeparator +
				"This is a helpful tip",
			expectedCore:   "This is the main PR description",
			expectedSuffix: suffixSeparator + "This is the suffix content",
			expectedTip:    tipSeparator + "This is a helpful tip",
		},
		{
			name: "multiline core body with suffix",
			prBody: "# Main Title\n\n" +
				"This is a detailed description\n" +
				"with multiple lines\n\n" +
				"- Item 1\n" +
				"- Item 2" +
				suffixSeparator +
				"Additional context from agent",
			expectedCore: "# Main Title\n\n" +
				"This is a detailed description\n" +
				"with multiple lines\n\n" +
				"- Item 1\n" +
				"- Item 2",
			expectedSuffix: suffixSeparator + "Additional context from agent",
			expectedTip:    "",
		},
		{
			name: "suffix with newlines",
			prBody: "Core content" +
				suffixSeparator +
				"Suffix line 1\n" +
				"Suffix line 2\n\n" +
				"Suffix line 3",
			expectedCore: "Core content",
			expectedSuffix: suffixSeparator + "Suffix line 1\n" +
				"Suffix line 2\n\n" +
				"Suffix line 3",
			expectedTip: "",
		},
		{
			name: "suffix followed by tip",
			prBody: "Core content" +
				suffixSeparator +
				"Suffix content" +
				tipSeparator +
				"💡 **Tip**: This is helpful advice",
			expectedCore:   "Core content",
			expectedSuffix: suffixSeparator + "Suffix content",
			expectedTip:    tipSeparator + "💡 **Tip**: This is helpful advice",
		},
		{
			name: "empty suffix section followed by tip",
			prBody: "Core content" +
				suffixSeparator +
				tipSeparator +
				"💡 **Tip**: This is helpful advice",
			expectedCore:   "Core content",
			expectedSuffix: suffixSeparator,
			expectedTip:    tipSeparator + "💡 **Tip**: This is helpful advice",
		},
		{
			name: "tip separator without proper format",
			prBody: "Core content\n\n" +
				jobutils.PullRequestBodyTipSeparator + "\n" +
				"Not properly formatted tip",
			expectedCore: "Core content\n\n" +
				jobutils.PullRequestBodyTipSeparator + "\n" +
				"Not properly formatted tip",
			expectedSuffix: "",
			expectedTip:    "",
		},
		{
			name: "tip appears before suffix (unusual order)",
			prBody: "Core content" +
				tipSeparator +
				"This is a tip" +
				suffixSeparator +
				"This is a suffix",
			expectedCore:   "Core content",
			expectedSuffix: suffixSeparator + "This is a suffix",
			expectedTip:    tipSeparator + "This is a tip",
		},
		{
			name: "multiple tip sections",
			prBody: "Core content" +
				tipSeparator +
				"First tip\n\n💡 Second tip\n\nThird tip",
			expectedCore:   "Core content",
			expectedSuffix: "",
			expectedTip:    tipSeparator + "First tip\n\n💡 Second tip\n\nThird tip",
		},
		{
			name: "complex real-world example",
			prBody: "## Summary\n\nFixed the login bug\n\n" +
				"### Changes\n- Updated auth logic\n- Fixed tests" +
				suffixSeparator +
				"Generated by Copilot Coding Agent" +
				tipSeparator +
				"💡 **Tip**: Consider adding more error handling\n\n" +
				"🔬 **Experiment**: This PR was created with enhanced AI assistance",
			expectedCore: "## Summary\n\nFixed the login bug\n\n" +
				"### Changes\n- Updated auth logic\n- Fixed tests",
			expectedSuffix: suffixSeparator + "Generated by Copilot Coding Agent",
			expectedTip: tipSeparator + "💡 **Tip**: Consider adding more error handling\n\n" +
				"🔬 **Experiment**: This PR was created with enhanced AI assistance",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			core, suffix, tip := m.extractCoreBodyAndSuffixFromPR(tt.prBody)
			assert.Equal(t, tt.expectedCore, core, "Core body should match expected")
			assert.Equal(t, tt.expectedSuffix, suffix, "Suffix should match expected")
			assert.Equal(t, tt.expectedTip, tip, "Tip should match expected")
		})
	}
}
