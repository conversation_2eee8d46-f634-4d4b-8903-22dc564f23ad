package agentcallback

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/github/sweagentd/internal/jobs"
)

type ProgressJobEvent struct {
	Kind                string     `json:"kind"`
	ModelCall           ModelCall  `json:"modelCall"`
	Message             Message    `json:"message"`
	Response            Response   `json:"response"`
	SessionLog          SessionLog `json:"sessionLog"`
	Telemetry           Telemetry  `json:"telemetry"`
	ModelCallDurationMs int64      `json:"modelCallDurationMs"`
	RequestMessages     string     `json:"requestMessages"`
}

type ModelCall struct {
	APIID     string `json:"api_id"`
	Model     string `json:"model"`
	RequestId string `json:"request_id"`
	Status    int64  `json:"status"`
	Error     string `json:"error"`
}

// return role if available, there should only be one role
func (e *ProgressJobEvent) GetRole() (string, bool) {
	if e.Message.Role != "" {
		return e.Message.Role, true
	}

	if e.Response.Role != "" {
		return e.Response.Role, true
	}

	if len(e.SessionLog.Choices) > 0 {
		for _, choice := range e.SessionLog.Choices {
			if choice.Delta.Role != "" {
				return choice.Delta.Role, true
			}
		}
	}

	return "", false
}

// currently we are getting a second sessionlog event in the same turn, we ignore it for now and investigate later if it can be removed on the runtime side
func (e *ProgressJobEvent) HasUsage() bool {
	return e.SessionLog.Usage.CompletionTokens > 0 || e.SessionLog.Usage.PromptTokens > 0 || e.SessionLog.Usage.TotalTokens > 0
}

// MESSAGE struct is set for MessageEvent and its subtypes ToolMessageEvent/AssistantMessageEvent/UserMessageEvent
// Message represents a message in a job event which can be either a complex object or a simple string
type Message struct {
	// Fields for when message is an object
	Role string `json:"role,omitempty"`
	// uncommenting might leak info into the logs, so uncommenting requires checking generateProgressJobEventTelemetry
	Content string `json:"content"`
	//Refusal    string     `json:"refusal"`
	ToolCallID string     `json:"tool_call_id,omitempty"`
	ToolCalls  []ToolCall `json:"tool_calls,omitempty"`
	// Raw message when message is a string
	StringValue string `json:"-"`
}

// UnmarshalJSON custom implementation for handling both object and string message formats
func (m *Message) UnmarshalJSON(data []byte) error {
	// First try as string
	var s string
	if err := json.Unmarshal(data, &s); err == nil {
		m.StringValue = s
		return nil
	}

	// If not a string, try as object
	type MessageAlias Message
	alias := &struct {
		*MessageAlias
	}{
		MessageAlias: (*MessageAlias)(m),
	}

	if err := json.Unmarshal(data, alias); err != nil {
		return err
	}

	return nil
}

// RESPONSE struct is set for ResponseEvent
type Response struct {
	Role string `json:"role"`
	// uncommenting might leak info into the logs, so uncommenting requires checking generateProgressJobEventTelemetry
	//Content string `json:"content"`
}

// SESSIONLOG is set for SessionLogEvent
type SessionLog struct {
	Id      string   `json:"id"`
	Choices []Choice `json:"choices"`
	Created int64    `json:"created"`
	Model   string   `json:"model"`
	Object  string   `json:"object"`
	Usage   Usage    `json:"usage"`
}

type Choice struct {
	Delta        Delta  `json:"delta"`
	FinishReason string `json:"finish_reason"`
}

type Delta struct {
	Toolcalls []ToolCall `json:"tool_calls"`
	// uncommenting might leak info into the logs, so uncommenting requires checking generateProgressJobEventTelemetry
	Content string `json:"content"`
	Refusal string `json:"refusal"`
	Role    string `json:"role"`
}

type Usage struct {
	CompletionTokens int64 `json:"completion_tokens"`
	PromptTokens     int64 `json:"prompt_tokens"`
	TotalTokens      int64 `json:"total_tokens"`
}

type Telemetry struct {
	Event                string             `json:"event"`
	Properties           map[string]string  `json:"properties"`
	RestrictedProperties map[string]string  `json:"restrictedProperties"`
	Metrics              map[string]float64 `json:"metrics"`
}

type ToolCall struct {
	// Fields for ToolCalls in Message and SessionLog events
	ID       string           `json:"id,omitempty"`
	Type     string           `json:"type,omitempty"`
	Function FunctionToolCall `json:"function,omitempty"`
	// Fields for ToolCalls in GetCompletionWithToolsTurnProperties Telemetry event
	ToolCallId string `json:"toolCallId,omitempty"`
	ToolName   string `json:"toolName,omitempty"`
}

type FunctionToolCall struct {
	Name string `json:"name"`
	// uncommenting might leak info into the logs, so uncommenting requires checking generateProgressJobEventTelemetry
	Arguments string `json:"arguments"`
}

func (m *jobManager) handleProgress(ctx context.Context, r *http.Request, job *jobs.Job, update jobs.JobUpdate) (*JobResult, error) {
	jobResult := &JobResult{
		Status: jobs.JobStatusRunning,
	}

	progressJobEvent := ProgressJobEvent{}
	// do not fail on an unsuccessful unmarshal, as all the properties are used for telemetry only
	json.Unmarshal([]byte(update.Content), &progressJobEvent)
	logger := m.obsv.LoggerWithRequestTelemetry(ctx, r).WithFields(GenerateProgressJobEventTelemetry(progressJobEvent)...)

	logger.Info("Job in progress. Processing handleProgress callback")

	//publish hydro events
	// this code needs some love/refactoring after we have the events and see the output, and definitely make it more maintainable
	if m.ffClient.IsEnabledForUserOrRepo(ctx, "sweagentd_publish_telemetry_events_to_hydro", job.ActorID, job.RepoID) {
		jobProgressEvent := ToJobProgress(job, update, progressJobEvent)
		err := m.obsv.HydroPbl.Publish(jobProgressEvent)

		if err != nil {
			logger.WithError(err).Error("failed to publish job_progress event")
		}

		// we are receiving two session log events in the same model completion turn, the first one contains all the info we need, for now we ignore the second one which contains less data. TODO investigate the second sessionlog emission if its necessary.
		if progressJobEvent.Kind == "session_log" && progressJobEvent.HasUsage() {
			modelCallEvent := ToModelCall(job, update, progressJobEvent)
			err := m.obsv.HydroPbl.Publish(modelCallEvent)

			if err != nil {
				logger.WithError(err).Error("failed to publish model call event")
			}

			restrictedModelCallEvent := ToRestrictedModelCall(job, update, progressJobEvent)
			restrictedErr := m.obsv.HydroPbl.Publish(restrictedModelCallEvent)

			if restrictedErr != nil {
				logger.WithError(restrictedErr).Error("failed to publish restricted model call event")
			}
		}

		if progressJobEvent.Kind == "telemetry" && progressJobEvent.Telemetry.Event == "tool_call_executed" {
			toolCallEvent := ToToolCall(job, update, progressJobEvent)

			err := m.obsv.HydroPbl.Publish(toolCallEvent)

			if err != nil {
				logger.WithError(err).Error("failed to publish tool call event")
			}

			restrictedToolCallEvent := ToRestrictedToolCall(job, update, progressJobEvent)
			restrictedErr := m.obsv.HydroPbl.Publish(restrictedToolCallEvent)

			if restrictedErr != nil {
				logger.WithError(restrictedErr).Error("failed to publish restricted tool call event")
			}
		}

		if progressJobEvent.Kind == "model_call_failure" {
			modelCallEvent := ToModelCall(job, update, progressJobEvent)
			err := m.obsv.HydroPbl.Publish(modelCallEvent)

			if err != nil {
				logger.WithError(err).Error("failed to publish model call event")
			}

			restrictedModelCallEvent := ToRestrictedModelCall(job, update, progressJobEvent)
			restrictedErr := m.obsv.HydroPbl.Publish(restrictedModelCallEvent)

			if restrictedErr != nil {
				logger.WithError(restrictedErr).Error("failed to publish restricted model call event")
			}
		}

		if progressJobEvent.Kind == "telemetry" && progressJobEvent.Telemetry.Event == "get_completion_with_tools_turn" {
			tags := m.obsv.TelemetryTags(ctx)

			// Publish stats to Datadog
			if base64ImagesCount, ok := progressJobEvent.Telemetry.Metrics["base64ImagesCount"]; ok {
				m.obsv.Statter.Counter("agent.vision.images_as_base64_count", tags, int64(base64ImagesCount))

				for i := range int(base64ImagesCount) {
					imageName := fmt.Sprintf("base64ImageSize_%d", i)
					if imageSize, ok := progressJobEvent.Telemetry.Metrics[imageName]; ok {
						m.obsv.Statter.Distribution("agent.vision.base64_image_size", tags, imageSize)
					}
				}
			}
			if extractedImagesCount, ok := progressJobEvent.Telemetry.Metrics["imagesExtractedCount"]; ok {
				m.obsv.Statter.Counter("agent.vision.images_extracted_count", tags, int64(extractedImagesCount))

				for i := range int(extractedImagesCount) {
					imageName := fmt.Sprintf("extractedImageSize_%d", i)
					if imageSize, ok := progressJobEvent.Telemetry.Metrics[imageName]; ok {
						m.obsv.Statter.Distribution("agent.vision.extracted_image_size", tags, imageSize)
					}
				}
			}
			if imagesResolvedFromGitHubMCPCount, ok := progressJobEvent.Telemetry.Metrics["imagesResolvedFromGitHubMCPCount"]; ok {
				m.obsv.Statter.Counter("agent.vision.images_resolved_from_github_mcp_count", tags, int64(imagesResolvedFromGitHubMCPCount))

				for i := range int(imagesResolvedFromGitHubMCPCount) {
					imageName := fmt.Sprintf("imagesResolvedFromGitHubMCPSize_%d", i)
					if imageSize, ok := progressJobEvent.Telemetry.Metrics[imageName]; ok {
						m.obsv.Statter.Distribution("agent.vision.images_resolved_from_github_mcp_size", tags, imageSize)
					}
				}
			}

			if imagesSendToLlm, ok := progressJobEvent.Telemetry.Metrics["allImagesSendToLlm"]; ok {
				m.obsv.Statter.Counter("agent.vision.images_passed_to_llm", tags, int64(imagesSendToLlm))
			}
			if imagesRemovedDueToSize, ok := progressJobEvent.Telemetry.Metrics["imagesRemovedDueToSize"]; ok {
				m.obsv.Statter.Counter("agent.vision.images_removed_due_to_size", tags, int64(imagesRemovedDueToSize))
			}
			if imagesRemovedDueToDimensions, ok := progressJobEvent.Telemetry.Metrics["imagesRemovedDueToDimensions"]; ok {
				m.obsv.Statter.Counter("agent.vision.images_removed_due_to_dimensions", tags, int64(imagesRemovedDueToDimensions))
			}
			if imagesResized, ok := progressJobEvent.Telemetry.Metrics["imagesResized"]; ok {
				m.obsv.Statter.Counter("agent.vision.images_resized", tags, int64(imagesResized))
			}
		}

		if progressJobEvent.Telemetry.Event != "" {
			telemetryEvent := ToTelemetry(job, update, progressJobEvent)
			err := m.obsv.HydroPbl.Publish(telemetryEvent)

			if err != nil {
				logger.WithError(err).Error("failed to publish telemetry event")
			}

			restrictedTelemetryEvent := ToRestrictedTelemetry(job, update, progressJobEvent)
			restrictedErr := m.obsv.HydroPbl.Publish(restrictedTelemetryEvent)

			if restrictedErr != nil {
				logger.WithError(restrictedErr).Error("failed to publish restricted telemetry event")
			}
		}
	}
	return jobResult, nil
}
