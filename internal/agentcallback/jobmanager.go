package agentcallback

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/github/go-mark"
	"github.com/github/go-stats"
	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/jobutils"
	"github.com/github/sweagentd/internal/observability"
)

var (
	suffixSeparator = fmt.Sprintf("\n\n%s\n\n", jobutils.PullRequestBodySuffixSeparator)
	tipSeparator    = fmt.Sprintf("\n\n%s\n---", jobutils.PullRequestBodyTipSeparator)
)

type jobManager struct {
	obsv     observability.Exporters
	jobs     jobs.JobsStore
	clients  github.ClientFactoryInterface
	ffClient featureflags.Client
}

func newManager(obsv observability.Exporters, jobs jobs.JobsStore, clients github.ClientFactoryInterface, ffClient featureflags.Client) *jobManager {
	return &jobManager{
		obsv,
		jobs,
		clients,
		ffClient,
	}
}

func (m *jobManager) HandleJobUpdate(ctx context.Context, r *http.Request, job *jobs.Job, update jobs.JobUpdate, actorID int64) (err error) {
	// Ensure that the job is for the correct actor
	if job.ActorID != actorID {
		return mark.ErrNotFound
	}

	if job.Status.IsTerminal() {
		return &badRequestError{"job is in a terminal state: " + string(job.Status)}
	}

	var result *JobResult
	// If we error, we want to clean up the container and ensure we set the job status to a terminal state
	defer func() {
		var badRequestErr *badRequestError
		if err != nil && !errors.As(err, &badRequestErr) {
			m.obsv.LoggerWithRequestTelemetry(ctx, r).Error(fmt.Sprintf("Failed to handle result: %s", err))

			if job != nil {
				if result == nil {
					update.Kind = jobs.AgentUpdateKindError
					jobutils.UpdateRequestContextJobUpdate(ctx, m.obsv, &update)
					m.obsv.LoggerWithRequestTelemetry(ctx, r).Error("Job failed without a result")
					err = fmt.Errorf("failed to process agent update, check logs for detail: %w", err)
					return
				}
			}
		}
	}()

	tags := m.obsv.TelemetryTags(ctx).Merge(stats.Tags{"kind": string(update.Kind)})
	m.obsv.Statter.Counter("agent.runtime_update", tags, 1)

	switch update.Kind {
	case jobs.AgentUpdateKindResult:
		result, err = m.handleResult(ctx, r, job, update)
	case jobs.AgentUpdateKindProgress:
		result, err = m.handleProgress(ctx, r, job, update)
	case jobs.AgentUpdateKindPartialResult:
		result, err = m.handlePartialResult(ctx, r, job, update)
	case jobs.AgentUpdateKindCommentReply:
		result, err = m.handleCommentReply(ctx, r, job, update)
	case jobs.AgentUpdateKindError:
		result, err = m.handleError(ctx, r, job, update)
	default:
		return &badRequestError{"unknown update kind: " + string(update.Kind)}
	}
	if err != nil {
		return err
	}

	// Update the job status if it has changed, but only if the new status
	// is not terminal. We set the status to terminal states upon workflow
	// run completion where we also finalize the session.
	if job.Status != result.Status && !result.Status.IsTerminal() {
		if err = m.jobs.PatchJob(ctx, job, jobs.JobPatch{Status: &result.Status}); err != nil {
			return fmt.Errorf("failed to patch job: %w", err)
		}
	}

	return nil
}

type JobResult struct {
	Status      jobs.JobStatus `json:"status"`
	CompletedAt time.Time      `json:"completedAt"`
	Result      any            `json:"result"`
}

// updatePullRequest creates or updates the title and body of a pull request.
// If the title or body has changed, the pull request is updated
func (m *jobManager) updatePullRequest(ctx context.Context, gh github.ClientInterface, job *jobs.Job, title, body *string) error {
	pr, err := gh.GetPullRequest(ctx, job.RepoID, job.PullRequestNumber)
	if err != nil {
		return fmt.Errorf("failed to get pull request: %w", err)
	}

	needsUpdate := false

	if title != nil && *title != pr.GetTitle() {
		pr.Title = title
		needsUpdate = true
	}

	if body != nil {
		newBody := *body
		currentCoreBody, currentSuffix, currentTip := m.extractCoreBodyAndSuffixFromPR(pr.GetBody())

		// preserve the existing suffix if it exists or use new one
		var suffixToAdd string
		if job.RunOptions.ResponseBodySuffix != "" {
			suffixToAdd = suffixSeparator + job.RunOptions.ResponseBodySuffix
		} else if currentSuffix != "" {
			suffixToAdd = currentSuffix
		}

		if suffixToAdd != "" {
			newBody = newBody + suffixToAdd
		}

		// preserve the existing tip if it exists
		var tipToAdd string
		if currentTip != "" {
			tipToAdd = currentTip
		} else {
			tip := jobutils.GetRandomTipForJob(ctx, m.ffClient, job.ActorID, job.RepoID, job.OwnerID, job.RepoOwner, job.RepoName)
			// TODO: osweExperimentEnabled is only set for the create flow
			osweExperimentEnabled := jobutils.IsOsweExperimentEnabled(ctx, m.ffClient, job)

			if tip != "" || osweExperimentEnabled {
				tipToAdd = tipSeparator

				if tip != "" {
					tipToAdd = fmt.Sprintf("%s\n\n%s", tipToAdd, tip)
				}

				if osweExperimentEnabled {
					tipToAdd = fmt.Sprintf("%s\n\n%s", tipToAdd, github.OsweExperimentNote)
				}
			}
		}

		if tipToAdd != "" {
			newBody = newBody + tipToAdd
		}

		if currentCoreBody != *body {
			pr.Body = &newBody
			needsUpdate = true
		}
	}

	if needsUpdate {
		_, err = gh.UpdatePullRequest(ctx, job.RepoID, pr)
		return err
	}
	return nil
}

// extractCoreBodyAndSuffixFromPR extracts the core body, suffix, and tip from a pull request body
func (m *jobManager) extractCoreBodyAndSuffixFromPR(prBody string) (core string, suffix string, tip string) {
	suffixIndex := strings.Index(prBody, suffixSeparator)
	tipIndex := strings.Index(prBody, tipSeparator)

	if suffixIndex != -1 && tipIndex != -1 {
		if suffixIndex < tipIndex {
			// expected order: core -> suffix -> tip
			core = prBody[:suffixIndex]
			suffix = prBody[suffixIndex:tipIndex]
			tip = prBody[tipIndex:]
		} else {
			// unexpected order: core -> tip -> suffix (shouldn't happen)
			core = prBody[:tipIndex]
			suffix = prBody[suffixIndex:]
			tip = prBody[tipIndex:suffixIndex]
		}
		return core, suffix, tip
	}

	// only suffix exists
	if suffixIndex != -1 {
		return prBody[:suffixIndex], prBody[suffixIndex:], ""
	}

	// only tip exists
	if tipIndex != -1 {
		return prBody[:tipIndex], "", prBody[tipIndex:]
	}

	// no separators found
	return prBody, "", ""
}
