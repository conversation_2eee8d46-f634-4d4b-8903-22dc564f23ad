package agentcallback

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore/to"
	authd "github.com/github/authnd/client"
	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/githubtwirp"
	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/launcher"
	"github.com/github/sweagentd/internal/observability"
	"github.com/github/sweagentd/internal/requestctx"
	"github.com/github/sweagentd/internal/testutils"
	sweagentdTwirp "github.com/github/sweagentd/proto/sweagentd/v1"
	"github.com/stretchr/testify/require"
)

func TestAgentCallbackBadRequests(t *testing.T) {
	ffClient := featureflags.NewNoopClient(nil)
	obsv := observability.NewNoopExporters()
	jobStore := jobs.NewMemoryStore()
	cf := &github.NoopClientFactory{}
	noopClient := githubtwirp.NewNoopClient()
	noopClient.NoopSweagentdAPI.ConfigurationForRepositoryResponse = to.Ptr(sweagentdTwirp.ConfigurationForRepositoryResponse{
		IsSweagentdEnabled: true,
	})
	h := NewHandler(obsv, nil, jobStore, cf, ffClient, noopClient, 1)

	t.Run("bad request", func(t *testing.T) {
		req := httptest.NewRequest(http.MethodPost, "/agent/jobs", nil)
		w := httptest.NewRecorder()
		h.HandleCallback(w, req)
		require.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("job already complete", func(t *testing.T) {
		ctx := addUserToContext(t, context.Background())
		nonce, nonceHash := jobNonce()
		// setup job
		job := &jobs.Job{
			ID:           "job-id",
			AssignmentID: "assignment-id",
			OwnerID:      1,
			RepoID:       2,
			RepoOwner:    "owner",
			RepoName:     "repo",
			Status:       jobs.JobStatusCompleted,
			ActorID:      1,
			NonceHash:    nonceHash,
		}
		jobStore.CreateJob(ctx, job)

		update := jobs.JobUpdate{
			Repo:        job.RepoOwner + "/" + job.RepoName,
			RepoID:      job.RepoID,
			RepoOwner:   job.RepoOwner,
			RepoOwnerID: job.OwnerID,
			RepoName:    job.RepoName,
			JobID:       "job-id",
			Kind:        jobs.AgentUpdateKindProgress,
		}
		b, _ := json.Marshal(update)
		req := httptest.NewRequestWithContext(ctx, http.MethodPost, "/agent/jobs", bytes.NewReader(b))
		req.Header.Set(jobs.JobNonceHeader, nonce)
		w := httptest.NewRecorder()
		h.HandleCallback(w, req)
		require.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("invalid kind", func(t *testing.T) {
		ctx := addUserToContext(t, context.Background())
		nonce, nonceHash := jobNonce()

		// setup job
		job := &jobs.Job{
			ID:           "job-id",
			AssignmentID: "assignment-id",
			OwnerID:      1,
			RepoID:       2,
			RepoOwner:    "owner",
			RepoName:     "repo",
			ActorID:      1,
			NonceHash:    nonceHash,
		}
		jobStore.CreateJob(ctx, job)

		update := jobs.JobUpdate{
			Repo:        job.RepoOwner + "/" + job.RepoName,
			RepoID:      job.RepoID,
			RepoOwner:   job.RepoOwner,
			RepoOwnerID: job.OwnerID,
			RepoName:    job.RepoName,
			JobID:       "job-id",
			Kind:        "invalid",
		}
		b, _ := json.Marshal(update)

		req := httptest.NewRequestWithContext(ctx, http.MethodPost, "/agent/jobs", bytes.NewReader(b))
		req.Header.Set(jobs.JobNonceHeader, nonce)
		w := httptest.NewRecorder()
		h.HandleCallback(w, req)
		require.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("invalid action", func(t *testing.T) {
		ctx := addUserToContext(t, context.Background())
		nonce, nonceHash := jobNonce()

		// setup job
		job := &jobs.Job{
			ID:           "job-id",
			AssignmentID: "assignment-id",
			OwnerID:      1,
			RepoID:       2,
			RepoOwner:    "owner",
			RepoName:     "repo",
			ActorID:      1,
			NonceHash:    nonceHash,
		}
		jobStore.CreateJob(ctx, job)

		update := jobs.JobUpdate{
			Repo:        job.RepoOwner + "/" + job.RepoName,
			RepoID:      job.RepoID,
			RepoOwner:   job.RepoOwner,
			RepoOwnerID: job.OwnerID,
			RepoName:    job.RepoName,
			JobID:       "job-id",
			Kind:        jobs.AgentUpdateKindResult,
			Action:      "invalid",
		}
		b, _ := json.Marshal(update)

		req := httptest.NewRequestWithContext(ctx, http.MethodPost, "/agent/jobs", bytes.NewReader(b))
		req.Header.Set(jobs.JobNonceHeader, nonce)
		w := httptest.NewRecorder()
		h.HandleCallback(w, req)
		require.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("wrong actor is authed", func(t *testing.T) {
		ctx := addUserToContext(t, context.Background())
		nonce, nonceHash := jobNonce()

		// setup job
		job := &jobs.Job{
			ID:           "job-id",
			AssignmentID: "assignment-id",
			OwnerID:      1,
			RepoID:       2,
			RepoOwner:    "owner",
			RepoName:     "repo",
			ActorID:      2,
			NonceHash:    nonceHash,
		}
		jobStore.CreateJob(ctx, job)

		update := jobs.JobUpdate{
			Repo:        job.RepoOwner + "/" + job.RepoName,
			RepoID:      job.RepoID,
			RepoOwner:   job.RepoOwner,
			RepoOwnerID: job.OwnerID,
			RepoName:    job.RepoName,
			JobID:       "job-id",
			Kind:        jobs.AgentUpdateKindResult,
			Action:      jobs.AgentActionFix,
		}
		b, _ := json.Marshal(update)

		req := httptest.NewRequestWithContext(ctx, http.MethodPost, "/agent/jobs", bytes.NewReader(b))
		req.Header.Set(jobs.JobNonceHeader, nonce)
		w := httptest.NewRecorder()
		h.HandleCallback(w, req)
		require.Equal(t, http.StatusNotFound, w.Code)
	})
}

func TestAgentCallbackHandleFixResult(t *testing.T) {
	ffClient := featureflags.NewNoopClient(nil)
	obsv := observability.NewNoopExporters()
	jobStore := jobs.NewMemoryStore()
	cf := &github.NoopClientFactory{}
	noopClient := githubtwirp.NewNoopClient()
	noopClient.NoopSweagentdAPI.ConfigurationForRepositoryResponse = to.Ptr(sweagentdTwirp.ConfigurationForRepositoryResponse{
		IsSweagentdEnabled: true,
	})
	h := NewHandler(obsv, nil, jobStore, cf, ffClient, noopClient, 1)

	t.Run("invalid content", func(t *testing.T) {
		ctx := addUserToContext(t, context.Background())
		nonce, nonceHash := jobNonce()

		assignment := &jobs.Assignment{
			ID:                "assignment-id",
			RepoID:            2,
			OwnerID:           1,
			RepoName:          "repo",
			RepoOwner:         "owner",
			PullRequestID:     1,
			PullRequestNumber: 1,
		}
		jobStore.CreateAssignment(ctx, assignment)

		// setup job
		job := &jobs.Job{
			ID:           "job-id",
			AssignmentID: "assignment-id",
			OwnerID:      1,
			RepoID:       2,
			RepoOwner:    "owner",
			RepoName:     "repo",
			ActorID:      1,
			NonceHash:    nonceHash,
		}
		jobStore.CreateJob(ctx, job)

		update := jobs.JobUpdate{
			Repo:        job.RepoOwner + "/" + job.RepoName,
			RepoID:      job.RepoID,
			RepoOwner:   job.RepoOwner,
			RepoOwnerID: job.OwnerID,
			RepoName:    job.RepoName,
			JobID:       "job-id",
			Kind:        jobs.AgentUpdateKindResult,
			Action:      jobs.AgentActionFix,
		}
		b, _ := json.Marshal(update)

		req := httptest.NewRequestWithContext(ctx, http.MethodPost, "/agent/jobs", bytes.NewReader(b))
		req.Header.Set(jobs.JobNonceHeader, nonce)
		w := httptest.NewRecorder()
		h.HandleCallback(w, req)
		require.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("invalid PR description", func(t *testing.T) {
		ctx := addUserToContext(t, context.Background())
		nonce, nonceHash := jobNonce()

		// setup job
		job := &jobs.Job{
			ID:           "job-id",
			AssignmentID: "assignment-id",
			OwnerID:      1,
			RepoID:       2,
			RepoOwner:    "owner",
			RepoName:     "repo",
			ActorID:      1,
			NonceHash:    nonceHash,
		}
		jobStore.CreateJob(ctx, job)

		u := fixResultUpdate{}
		b, _ := json.Marshal(u)

		update := jobs.JobUpdate{
			Repo:        job.RepoOwner + "/" + job.RepoName,
			RepoID:      job.RepoID,
			RepoOwner:   job.RepoOwner,
			RepoOwnerID: job.OwnerID,
			RepoName:    job.RepoName,
			JobID:       "job-id",
			Kind:        jobs.AgentUpdateKindResult,
			Action:      jobs.AgentActionFix,
			Content:     string(b),
		}
		b, _ = json.Marshal(update)

		req := httptest.NewRequestWithContext(ctx, http.MethodPost, "/agent/jobs", bytes.NewReader(b))
		req.Header.Set(jobs.JobNonceHeader, nonce)
		w := httptest.NewRecorder()
		h.HandleCallback(w, req)
		require.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("successful run", func(t *testing.T) {
		ctx := addUserToContext(t, context.Background())
		nonce, nonceHash := jobNonce()

		// setup job
		job := &jobs.Job{
			ID:           "job-id",
			AssignmentID: "assignment-id",
			OwnerID:      1,
			RepoID:       2,
			RepoOwner:    "owner",
			RepoName:     "repo",
			ActorID:      1,
			NonceHash:    nonceHash,
		}
		jobStore.CreateJob(ctx, job)

		u := fixResultUpdate{
			PRTitle:       "hello",
			PRDescription: "world",
		}
		b, _ := json.Marshal(u)

		update := jobs.JobUpdate{
			Repo:        job.RepoOwner + "/" + job.RepoName,
			RepoID:      job.RepoID,
			RepoOwner:   job.RepoOwner,
			RepoOwnerID: job.OwnerID,
			RepoName:    job.RepoName,
			JobID:       "job-id",
			Kind:        jobs.AgentUpdateKindResult,
			Action:      jobs.AgentActionFix,
			Content:     string(b),
		}
		b, _ = json.Marshal(update)

		req := httptest.NewRequestWithContext(ctx, http.MethodPost, "/agent/jobs", bytes.NewReader(b))
		req.Header.Set(jobs.JobNonceHeader, nonce)
		w := httptest.NewRecorder()
		h.HandleCallback(w, req)
		require.Equal(t, http.StatusOK, w.Code)
	})
}

func TestAgentCallbackHandleFixResultPRComment(t *testing.T) {
	ffClient := featureflags.NewNoopClient(nil)
	obsv := observability.NewNoopExporters()
	jobStore := jobs.NewMemoryStore()
	cf := &github.NoopClientFactory{}
	noopClient := githubtwirp.NewNoopClient()
	noopClient.NoopSweagentdAPI.ConfigurationForRepositoryResponse = to.Ptr(sweagentdTwirp.ConfigurationForRepositoryResponse{
		IsSweagentdEnabled: true,
	})
	h := NewHandler(obsv, nil, jobStore, cf, ffClient, noopClient, 1)

	t.Run("invalid content", func(t *testing.T) {
		ctx := addUserToContext(t, context.Background())
		nonce, nonceHash := jobNonce()

		assignment := &jobs.Assignment{
			ID:                "assignment-id",
			OwnerID:           1,
			RepoID:            2,
			RepoOwner:         "owner",
			RepoName:          "repo",
			PullRequestID:     1,
			PullRequestNumber: 1,
		}
		jobStore.CreateAssignment(ctx, assignment)

		// setup job
		job := &jobs.Job{
			ID:           "job-id",
			AssignmentID: "assignment-id",
			OwnerID:      1,
			RepoID:       2,
			RepoOwner:    "owner",
			RepoName:     "repo",
			ActorID:      1,
			NonceHash:    nonceHash,
		}
		jobStore.CreateJob(ctx, job)

		update := jobs.JobUpdate{
			Repo:        job.RepoOwner + "/" + job.RepoName,
			RepoID:      job.RepoID,
			RepoOwner:   job.RepoOwner,
			RepoOwnerID: job.OwnerID,
			RepoName:    job.RepoName,
			JobID:       "job-id",
			Kind:        jobs.AgentUpdateKindResult,
			Action:      jobs.AgentActionFixPRComment,
		}
		b, _ := json.Marshal(update)

		req := httptest.NewRequestWithContext(ctx, http.MethodPost, "/agent/jobs", bytes.NewReader(b))
		req.Header.Set(jobs.JobNonceHeader, nonce)
		w := httptest.NewRecorder()
		h.HandleCallback(w, req)
		require.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("successful run", func(t *testing.T) {
		ctx := addUserToContext(t, context.Background())
		nonce, nonceHash := jobNonce()

		// setup job
		job := &jobs.Job{
			ActorID:      1,
			ID:           "job-id",
			AssignmentID: "assignment-id",
			OwnerID:      1,
			RepoID:       2,
			RepoOwner:    "owner",
			RepoName:     "repo",
			Args: launcher.LaunchAgentOpts{
				ActorLogin:  "actor",
				IssueNumber: 1,
				PRNumber:    3,
			},
			NonceHash: nonceHash,
		}
		jobStore.CreateJob(ctx, job)

		u := fixResultUpdate{}
		b, _ := json.Marshal(u)

		update := jobs.JobUpdate{
			Repo:        job.RepoOwner + "/" + job.RepoName,
			RepoID:      job.RepoID,
			RepoOwner:   job.RepoOwner,
			RepoOwnerID: job.OwnerID,
			RepoName:    job.RepoName,
			JobID:       "job-id",
			Kind:        jobs.AgentUpdateKindResult,
			Action:      jobs.AgentActionFixPRComment,
			Content:     string(b),
		}
		b, _ = json.Marshal(update)

		req := httptest.NewRequestWithContext(ctx, http.MethodPost, "/agent/jobs", bytes.NewReader(b))
		req.Header.Set(jobs.JobNonceHeader, nonce)
		w := httptest.NewRecorder()
		h.HandleCallback(w, req)
		require.Equal(t, http.StatusOK, w.Code)
	})
}

func TestAgentCallbackHandleProgress(t *testing.T) {
	ffClient := featureflags.NewNoopClient(nil)
	obsv := observability.NewNoopExporters()
	jobStore := jobs.NewMemoryStore()
	cf := &github.NoopClientFactory{}
	noopClient := githubtwirp.NewNoopClient()
	noopClient.NoopSweagentdAPI.ConfigurationForRepositoryResponse = to.Ptr(sweagentdTwirp.ConfigurationForRepositoryResponse{
		IsSweagentdEnabled: true,
	})
	h := NewHandler(obsv, nil, jobStore, cf, ffClient, noopClient, 1)

	t.Run("successful run", func(t *testing.T) {
		ctx := addUserToContext(t, context.Background())
		nonce, nonceHash := jobNonce()

		// setup job
		job := &jobs.Job{
			ID:           "job-id",
			AssignmentID: "assignment-id",
			OwnerID:      1,
			RepoID:       2,
			RepoOwner:    "owner",
			RepoName:     "repo",
			ActorID:      1,
			NonceHash:    nonceHash,
		}
		jobStore.CreateJob(ctx, job)

		update := jobs.JobUpdate{
			Repo:        job.RepoOwner + "/" + job.RepoName,
			RepoID:      job.RepoID,
			RepoOwner:   job.RepoOwner,
			RepoOwnerID: job.OwnerID,
			RepoName:    job.RepoName,
			JobID:       "job-id",
			Kind:        jobs.AgentUpdateKindProgress,
		}
		b, _ := json.Marshal(update)

		req := httptest.NewRequestWithContext(ctx, http.MethodPost, "/agent/jobs", bytes.NewReader(b))
		req.Header.Set(jobs.JobNonceHeader, nonce)
		w := httptest.NewRecorder()
		h.HandleCallback(w, req)
		require.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("successful run with telemetry", func(t *testing.T) {
		ctx := addUserToContext(t, context.Background())
		nonce, nonceHash := jobNonce()

		// setup job
		job := &jobs.Job{
			ID:           "job-id",
			AssignmentID: "assignment-id",
			OwnerID:      1,
			RepoID:       2,
			RepoOwner:    "owner",
			RepoName:     "repo",
			ActorID:      1,
			NonceHash:    nonceHash,
		}
		jobStore.CreateJob(ctx, job)

		update := jobs.JobUpdate{
			Repo:        job.RepoOwner + "/" + job.RepoName,
			RepoID:      job.RepoID,
			RepoOwner:   job.RepoOwner,
			RepoOwnerID: job.OwnerID,
			RepoName:    job.RepoName,
			JobID:       "job-id",
			Kind:        jobs.AgentUpdateKindProgress,
			Content:     testutils.ReadFixture("../testutils/fixtures/job-event-message-user.json"),
		}
		b, _ := json.Marshal(update)

		req := httptest.NewRequestWithContext(ctx, http.MethodPost, "/agent/jobs", bytes.NewReader(b))
		req.Header.Set(jobs.JobNonceHeader, nonce)
		w := httptest.NewRecorder()
		h.HandleCallback(w, req)
		require.Equal(t, http.StatusOK, w.Code)
	})
}

func TestAgentCallbackHandlePartialResult(t *testing.T) {
	ffClient := featureflags.NewNoopClient(nil)
	obsv := observability.NewNoopExporters()
	jobStore := jobs.NewMemoryStore()
	cf := &github.NoopClientFactory{}
	noopClient := githubtwirp.NewNoopClient()
	noopClient.NoopSweagentdAPI.ConfigurationForRepositoryResponse = to.Ptr(sweagentdTwirp.ConfigurationForRepositoryResponse{
		IsSweagentdEnabled: true,
	})
	h := NewHandler(obsv, nil, jobStore, cf, ffClient, noopClient, 1)

	t.Run("invalid content", func(t *testing.T) {
		ctx := addUserToContext(t, context.Background())
		nonce, nonceHash := jobNonce()

		// setup job
		job := &jobs.Job{
			ID:           "job-id",
			AssignmentID: "assignment-id",
			OwnerID:      1,
			RepoID:       2,
			RepoOwner:    "owner",
			RepoName:     "repo",
			ActorID:      1,
			NonceHash:    nonceHash,
		}
		jobStore.CreateJob(ctx, job)

		update := jobs.JobUpdate{
			Repo:        job.RepoOwner + "/" + job.RepoName,
			RepoID:      job.RepoID,
			RepoOwner:   job.RepoOwner,
			RepoOwnerID: job.OwnerID,
			RepoName:    job.RepoName,
			JobID:       "job-id",
			Kind:        jobs.AgentUpdateKindPartialResult,
		}
		b, _ := json.Marshal(update)

		req := httptest.NewRequestWithContext(ctx, http.MethodPost, "/agent/jobs", bytes.NewReader(b))
		req.Header.Set(jobs.JobNonceHeader, nonce)
		w := httptest.NewRecorder()
		h.HandleCallback(w, req)
		require.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("successful run", func(t *testing.T) {
		ctx := addUserToContext(t, context.Background())
		nonce, nonceHash := jobNonce()

		// setup job
		job := &jobs.Job{
			ActorID:      1,
			ID:           "job-id",
			AssignmentID: "assignment-id",
			OwnerID:      1,
			RepoID:       2,
			RepoOwner:    "owner",
			RepoName:     "repo",
			Args: launcher.LaunchAgentOpts{
				PRNumber: 3,
			},
			NonceHash: nonceHash,
		}
		jobStore.CreateJob(ctx, job)

		u := partialResultUpdate{
			Message: "hello world",
		}
		b, _ := json.Marshal(u)

		update := jobs.JobUpdate{
			Repo:        job.RepoOwner + "/" + job.RepoName,
			RepoID:      job.RepoID,
			RepoOwner:   job.RepoOwner,
			RepoOwnerID: job.OwnerID,
			RepoName:    job.RepoName,
			JobID:       "job-id",
			Kind:        jobs.AgentUpdateKindPartialResult,
			Content:     string(b),
		}
		b, _ = json.Marshal(update)

		req := httptest.NewRequestWithContext(ctx, http.MethodPost, "/agent/jobs", bytes.NewReader(b))
		req.Header.Set(jobs.JobNonceHeader, nonce)
		w := httptest.NewRecorder()
		h.HandleCallback(w, req)
		require.Equal(t, http.StatusOK, w.Code)
	})
}

func TestAgentCallbackHandleError(t *testing.T) {
	ffClient := featureflags.NewNoopClient(nil)
	obsv := observability.NewNoopExporters()
	jobStore := jobs.NewMemoryStore()
	cf := &github.NoopClientFactory{}
	noopClient := githubtwirp.NewNoopClient()
	noopClient.NoopSweagentdAPI.ConfigurationForRepositoryResponse = to.Ptr(sweagentdTwirp.ConfigurationForRepositoryResponse{
		IsSweagentdEnabled: true,
	})
	h := NewHandler(obsv, nil, jobStore, cf, ffClient, noopClient, 1)

	t.Run("successful run", func(t *testing.T) {
		ctx := addUserToContext(t, context.Background())
		nonce, nonceHash := jobNonce()

		// setup assignment
		assignment := &jobs.Assignment{
			ID:        "assignment-id",
			OwnerID:   1,
			RepoID:    2,
			RepoOwner: "owner",
			RepoName:  "repo",
		}
		jobStore.CreateAssignment(ctx, assignment)

		// setup job
		job := &jobs.Job{
			ID:           "job-id",
			AssignmentID: "assignment-id",
			OwnerID:      1,
			RepoID:       2,
			RepoOwner:    "owner",
			RepoName:     "repo",
			ActorID:      1,
			NonceHash:    nonceHash,
		}
		jobStore.CreateJob(ctx, job)

		u := map[string]any{"hello": "world"}
		b, _ := json.Marshal(u)

		update := jobs.JobUpdate{
			Repo:        job.RepoOwner + "/" + job.RepoName,
			RepoID:      job.RepoID,
			RepoOwner:   job.RepoOwner,
			RepoOwnerID: job.OwnerID,
			RepoName:    job.RepoName,
			JobID:       "job-id",
			Kind:        jobs.AgentUpdateKindError,
			Content:     string(b),
		}
		b, _ = json.Marshal(update)

		req := httptest.NewRequestWithContext(ctx, http.MethodPost, "/agent/jobs", bytes.NewReader(b))
		req.Header.Set(jobs.JobNonceHeader, nonce)
		w := httptest.NewRecorder()
		h.HandleCallback(w, req)
		require.Equal(t, http.StatusOK, w.Code)
	})
}

func TestAgentCallbackWithNonce(t *testing.T) {
	ffClient := featureflags.NewNoopClient(map[string]bool{
		"sweagentd_enforce_job_nonce": true,
	})
	obsv := observability.NewNoopExporters()
	jobStore := jobs.NewMemoryStore()
	cf := &github.NoopClientFactory{}
	noopClient := githubtwirp.NewNoopClient()
	noopClient.NoopSweagentdAPI.ConfigurationForRepositoryResponse = to.Ptr(sweagentdTwirp.ConfigurationForRepositoryResponse{
		IsSweagentdEnabled: true,
	})
	h := NewHandler(obsv, nil, jobStore, cf, ffClient, noopClient, 1)

	t.Run("missing nonce header", func(t *testing.T) {
		ctx := addUserToContext(t, context.Background())

		// setup job with nonce hash
		job := &jobs.Job{
			ID:           "job-id-nonce",
			AssignmentID: "assignment-id",
			OwnerID:      1,
			RepoID:       2,
			RepoOwner:    "owner",
			RepoName:     "repo",
			ActorID:      1,
		}
		jobStore.CreateJob(ctx, job)

		update := jobs.JobUpdate{
			Repo:        job.RepoOwner + "/" + job.RepoName,
			RepoID:      job.RepoID,
			RepoOwner:   job.RepoOwner,
			RepoOwnerID: job.OwnerID,
			RepoName:    job.RepoName,
			JobID:       "job-id-nonce",
			Kind:        jobs.AgentUpdateKindProgress,
		}
		b, _ := json.Marshal(update)

		req := httptest.NewRequestWithContext(ctx, http.MethodPost, "/agent/jobs", bytes.NewReader(b))
		w := httptest.NewRecorder()
		h.HandleCallback(w, req)
		require.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("invalid nonce header", func(t *testing.T) {
		ctx := addUserToContext(t, context.Background())

		// setup job with nonce hash
		job := &jobs.Job{
			ID:           "job-id-nonce-2",
			AssignmentID: "assignment-id",
			OwnerID:      1,
			RepoID:       2,
			RepoOwner:    "owner",
			RepoName:     "repo",
			ActorID:      1,
		}
		jobStore.CreateJob(ctx, job)

		update := jobs.JobUpdate{
			Repo:        job.RepoOwner + "/" + job.RepoName,
			RepoID:      job.RepoID,
			RepoOwner:   job.RepoOwner,
			RepoOwnerID: job.OwnerID,
			RepoName:    job.RepoName,
			JobID:       "job-id-nonce-2",
			Kind:        jobs.AgentUpdateKindProgress,
		}
		b, _ := json.Marshal(update)

		req := httptest.NewRequestWithContext(ctx, http.MethodPost, "/agent/jobs", bytes.NewReader(b))
		req.Header.Set(jobs.JobNonceHeader, "wrong-nonce-value")
		w := httptest.NewRecorder()
		h.HandleCallback(w, req)
		require.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("valid nonce header", func(t *testing.T) {
		ctx := addUserToContext(t, context.Background())

		// For a test where we're manually setting up the nonce/hash relationship,
		// we need to create the job record directly rather than going through CreateJob
		// because CreateJob would generate its own nonce/hash
		job := &jobs.Job{
			ID:           "job-id-nonce-3",
			AssignmentID: "assignment-id",
			OwnerID:      1,
			RepoID:       2,
			RepoOwner:    "owner",
			RepoName:     "repo",
			ActorID:      1,
			Status:       jobs.JobStatusPending,
		}

		_, err := jobStore.CreateJob(ctx, job)
		require.NoError(t, err)

		nonce, err := jobStore.RegenerateJobNonce(ctx, job)
		require.NoError(t, err)

		update := jobs.JobUpdate{
			Repo:        job.RepoOwner + "/" + job.RepoName,
			RepoID:      job.RepoID,
			RepoOwner:   job.RepoOwner,
			RepoOwnerID: job.OwnerID,
			RepoName:    job.RepoName,
			JobID:       "job-id-nonce-3",
			Kind:        jobs.AgentUpdateKindProgress,
		}
		b, _ := json.Marshal(update)

		req := httptest.NewRequestWithContext(ctx, http.MethodPost, "/agent/jobs", bytes.NewReader(b))
		req.Header.Set(jobs.JobNonceHeader, nonce)
		w := httptest.NewRecorder()
		h.HandleCallback(w, req)
		require.Equal(t, http.StatusOK, w.Code)
	})
}

func addUserToContext(t *testing.T, ctx context.Context) context.Context {
	t.Helper()
	return requestctx.AddData(ctx, &requestctx.CtxData{
		UserInfo: &requestctx.UserInfo{
			ID: 1,
			TokenInfo: &requestctx.TokenInfo{
				Type:          string(authd.CredentialTypeUserToServerToken),
				ApplicationID: 1,
			},
			Login:      "testuser",
			TrackingID: "tracking-id",
		},
	})
}

// Generate a nonce and its hash
func jobNonce() (string, string) {
	nonce := "test-nonce-value"
	nonceHash := jobs.HashNonce(nonce)
	return nonce, nonceHash
}
