package agentcallback

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/jobs"
)

type commentReplyUpdate struct {
	CommentID int64  `json:"comment_id"`
	Message   string `json:"message"`
}

type commentReply struct {
	PRNumber int `json:"prNumber"`
}

func (m *jobManager) handleCommentReply(ctx context.Context, r *http.Request, job *jobs.Job, update jobs.JobUpdate) (*JobResult, error) {
	var cr commentReplyUpdate
	if err := json.Unmarshal([]byte(update.Content), &cr); err != nil {
		return nil, fmt.Errorf("failed to unmarshal comment reply: %w", err)
	}

	jobResult := &JobResult{
		Status: jobs.JobStatusRunning,
		Result: &commentReply{
			PRNumber: job.PullRequestNumber,
		},
	}

	logger := m.obsv.LoggerWithRequestTelemetry(ctx, r)
	logger.Info("Job in progress. Processing handleCommentReply callback")

	if m.ffClient.IsEnabledForUserOrRepo(ctx, "sweagentd_publish_telemetry_events_to_hydro", job.ActorID, job.RepoID) {
		jobProgressEvent := ToJobProgress(job, update, ProgressJobEvent{})
		err := m.obsv.HydroPbl.Publish(jobProgressEvent)

		if err != nil {
			logger.WithContext(ctx).WithError(err).Error("failed to publish job_progress - comment-reply event")
		}
	}

	if cr.CommentID == 0 || cr.Message == "" {
		return jobResult, nil
	}

	ghclient, err := m.clients.NewClientFromRepo(ctx, job.RepoID, job.OwnerID)
	if err != nil {
		return nil, fmt.Errorf("failed to create GitHub client: %w", err)
	}

	actx, err := ghclient.GetPullRequestContext(ctx, job.RepoNodeID, job.PullRequestNumber)
	if err != nil {
		m.obsv.LogAndReportError(ctx, err, "failed to get pull request context")
	}

	comment := actx.PullRequest.CommentByID(cr.CommentID)
	if comment != nil {
		if err := m.appendComment(ctx, ghclient, job, comment.GetBody(), cr.Message); err != nil {
			m.obsv.LogAndReportError(ctx, err, "failed to append comment")
		}
		if err := comment.RemoveReaction(ctx, ghclient, github.GQLReactionContentEyes); err != nil {
			m.obsv.LogAndReportError(ctx, err, "failed to remove reaction")
		}
		return jobResult, nil
	}

	reviewComment := actx.PullRequest.ReviewCommentByID(cr.CommentID)
	if reviewComment != nil {
		if _, err := ghclient.CreateCommentInReplyTo(ctx, job.RepoOwner, job.RepoName, job.PullRequestNumber, cr.Message, cr.CommentID); err != nil {
			m.obsv.LogAndReportError(ctx, err, "failed to append comment in reply")
		}
		if err := reviewComment.RemoveReaction(ctx, ghclient, github.GQLReactionContentEyes); err != nil {
			m.obsv.LogAndReportError(ctx, err, "failed to remove reaction")
		}
		return jobResult, nil
	}

	review := actx.PullRequest.ReviewByID(cr.CommentID)
	if review != nil {
		if err := m.appendComment(ctx, ghclient, job, review.GetBody(), cr.Message); err != nil {
			m.obsv.LogAndReportError(ctx, err, "failed to append comment")
		}
		if err := review.RemoveReaction(ctx, ghclient, github.GQLReactionContentEyes); err != nil {
			m.obsv.LogAndReportError(ctx, err, "failed to remove reaction")
		}
		return jobResult, nil
	}

	// We didn't find the comment. Log the error but don't fail the job
	logger.Error(fmt.Sprintf("Failed to find comment with ID %d", cr.CommentID))

	return jobResult, nil
}

const MaxQuoteLines = 3
const MaxQuoteLength = 200

func (m *jobManager) appendComment(ctx context.Context, ghclient github.ClientInterface, job *jobs.Job, original, message string) error {
	quote := original
	if quote == "" || message == "" {
		return nil
	}

	truncated := false
	if len(quote) > MaxQuoteLength {
		quote = quote[:MaxQuoteLength]
		truncated = true
	}

	// limit the number of lines in the quote
	lines := strings.Split(quote, "\n")
	if len(lines) > MaxQuoteLines {
		lines = lines[:MaxQuoteLines]
		quote = strings.Join(lines, "\n")
		truncated = true
	}

	// add markdown ">" formatting to the quote
	quote = fmt.Sprintf("\n> %s", strings.ReplaceAll(quote, "\n", "\n> "))
	if truncated {
		quote += "..."
	}

	body := fmt.Sprintf("%s\n\n%s", quote, message)

	if job.Args.ResponseBodySuffix != "" {
		body = fmt.Sprintf("%s\n\n%s", body, job.Args.ResponseBodySuffix)
	}

	if job.ReplyCommentID == 0 {
		reply, err := ghclient.CreateComment(ctx, job.RepoID, job.PullRequestNumber, body)
		if err != nil {
			return fmt.Errorf("failed to create job reply comment: %w", err)
		}

		if err := m.jobs.PatchJob(ctx, job, jobs.JobPatch{ReplyCommentID: reply.ID}); err != nil {
			return fmt.Errorf("failed to update job reply_comment_id: %w", err)
		}
	} else {
		reply, err := ghclient.GetComment(ctx, job.RepoOwner, job.RepoName, job.ReplyCommentID)
		if err != nil {
			return fmt.Errorf("failed to get job reply comment: %w", err)
		}
		if reply == nil {
			return fmt.Errorf("job reply comment not found")
		}

		body = fmt.Sprintf("%s\n\n%s", reply.GetBody(), body)

		_, err = ghclient.UpdateComment(ctx, job.RepoOwner, job.RepoName, job.ReplyCommentID, body)
		if err != nil {
			return fmt.Errorf("failed to update comment: %w", err)
		}
	}

	return nil
}
