package featureflags

import (
	"context"

	"github.com/github/feature-management-client-go/vexi"
)

type NoopClient struct {
	flags map[string]bool
	appID int64
}

func NewNoopClient(flags map[string]bool) Client {
	flagsToReturn := map[string]bool{
		CopilotSWEAgentIntegrationCodeScanningAlerts: true,
	}
	for k, v := range flags {
		flagsToReturn[k] = v
	}
	return &NoopClient{
		flags: flagsToReturn,
	}
}

func NewNoopClientWithAppID(flags map[string]bool, appID int64) Client {
	flagsToReturn := map[string]bool{
		CopilotSWEAgentIntegrationCodeScanningAlerts: true,
	}
	for k, v := range flags {
		flagsToReturn[k] = v
	}
	return &NoopClient{
		flags: flagsToReturn,
		appID: appID,
	}
}

func (c *NoopClient) IsEnabled(ctx context.Context, featureFlagName string, actors ...vexi.Actor) bool {
	f, ok := c.flags[featureFlagName]
	if !ok {
		return false
	}
	return f
}

func (c *NoopClient) IsEnabledForUser(ctx context.Context, featureFlagName string, userId int64) bool {
	return c.IsEnabled(ctx, featureFlagName)
}

func (c *NoopClient) IsEnabledForUserOrRepo(ctx context.Context, featureFlagName string, userId, repoId int64) bool {
	return c.IsEnabled(ctx, featureFlagName)
}

func (c *NoopClient) IsEnabledForUserOrRepoOrOwner(ctx context.Context, featureFlagName string, userId, repoId, repoOwnerId int64) bool {
	return c.IsEnabled(ctx, featureFlagName)
}

func (c *NoopClient) IsEnabledForGitHubApp(ctx context.Context, featureFlagName string, appId int64) bool {
	return c.IsEnabled(ctx, featureFlagName) && c.appID == appId
}

func (c *NoopClient) IsEnabledForOAuthApp(ctx context.Context, featureFlagName string, appId int64) bool {
	return c.IsEnabled(ctx, featureFlagName) && c.appID == appId
}
