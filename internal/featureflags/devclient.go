package featureflags

import (
	"context"
	"fmt"

	"github.com/github/feature-management-client-go/vexi"
	ffd_adapter "github.com/github/feature-management-client-go/vexi/adapter/feature_flag_data"
	"github.com/github/sweagentd/internal/observability"
)

type DevClient struct {
	client *vexi.Client
}

func NewDevClient(ctx context.Context, serviceName string, obsv observability.Exporters) (*DevClient, error) {
	featureFlagDataAdapter, err := ffd_adapter.New(ffd_adapter.FeatureManagementLiteURL)
	if err != nil {
		return nil, fmt.Errorf("creating feature flag data adapter: %w", err)
	}

	vexiClient, err := vexi.NewVexiClient(ctx, featureFlagDataAdapter, serviceName, vexi.WithLogger(obsv.Logger))
	if err != nil {
		return nil, fmt.Errorf("creating vexi client: %w", err)
	}

	return &DevClient{client: vexiClient}, nil
}

func (f *DevClient) IsEnabled(ctx context.Context, featureFlagName string, actors ...vexi.Actor) bool {
	return f.client.IsEnabledWithDefaultValue(ctx, featureFlagName, false, actors...)
}

func (f *DevClient) IsEnabledForUser(ctx context.Context, featureFlagName string, userId int64) bool {
	return f.IsEnabled(ctx, featureFlagName, vexi.NewActor(fmt.Sprintf("User:%d", userId)))
}

func (f *DevClient) IsEnabledForUserOrRepo(ctx context.Context, featureFlagName string, userId, repoId int64) bool {
	return f.IsEnabled(ctx, featureFlagName, vexi.NewActor(fmt.Sprintf("User:%d", userId)), vexi.NewActor(fmt.Sprintf("Repository:%d", repoId)))
}

func (f *DevClient) IsEnabledForUserOrRepoOrOwner(ctx context.Context, featureFlagName string, userId, repoId, repoOwnerId int64) bool {
	userActor := vexi.NewActor(fmt.Sprintf("User:%d", userId))
	repoActor := vexi.NewActor(fmt.Sprintf("Repository:%d", repoId))
	ownerActor := vexi.NewActor(fmt.Sprintf("User:%d", repoOwnerId))
	orgOwnerActor := vexi.NewActor(fmt.Sprintf("Organization:%d", repoOwnerId))
	return f.IsEnabled(ctx, featureFlagName, userActor, repoActor, ownerActor, orgOwnerActor)
}

func (f *DevClient) IsEnabledForGitHubApp(ctx context.Context, featureFlagName string, appId int64) bool {
	// https://github.com/github/feature-management-client-go/blob/0b84383f18b3cb11a97eb3e0119a311b7786dc57/vexi/extensions/actor.go#L56
	return f.IsEnabled(ctx, featureFlagName, vexi.NewActor(fmt.Sprintf("Integration:%d", appId)))
}

func (f *DevClient) IsEnabledForOAuthApp(ctx context.Context, featureFlagName string, appId int64) bool {
	// https://github.com/github/feature-management-client-go/blob/0b84383f18b3cb11a97eb3e0119a311b7786dc57/vexi/extensions/actor.go#L51
	return f.IsEnabled(ctx, featureFlagName, vexi.NewActor(fmt.Sprintf("OauthApplication:%d", appId)))
}
