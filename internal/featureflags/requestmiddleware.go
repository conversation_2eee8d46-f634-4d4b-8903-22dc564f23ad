package featureflags

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"github.com/github/feature-management-client-go/vexi"
	"github.com/github/feature-management-client-go/vexi/extensions"
	"github.com/github/sweagentd/internal/observability"
	"github.com/github/sweagentd/internal/requestctx"
)

type CachingClient struct {
	client Client
	obsv   observability.Exporters
}

func NewCachingClient(client Client, obsv observability.Exporters) *CachingClient {
	return &CachingClient{
		client: client,
		obsv:   obsv,
	}
}

type contextCacheKey int

var cacheKey contextCacheKey

func (c *CachingClient) Middleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		ctx = context.WithValue(ctx, cacheKey, make(map[string]bool))
		next.ServeHTTP(w, r.<PERSON>ontext(ctx))
	})
}

func (c *CachingClient) IsEnabled(ctx context.Context, featureFlagName string, actors ...vexi.Actor) bool {
	key := c.cacheKey(featureFlagName, actors...)

	var cache map[string]bool

	if c := ctx.Value(cacheKey); c != nil {
		if m, ok := c.(map[string]bool); ok {
			cache = m
		}
	}

	if cache != nil {
		if v, ok := cache[key]; ok {
			return v
		}
	} else {
		cache = make(map[string]bool)
	}

	isEnabled := c.client.IsEnabled(ctx, featureFlagName, actors...)
	cache[key] = isEnabled

	return isEnabled
}

func (c *CachingClient) IsEnabledForUser(ctx context.Context, featureFlagName string, userId int64) bool {
	return c.IsEnabled(ctx, featureFlagName, extensions.NewUser(strconv.FormatInt(userId, 10)))
}

func (c *CachingClient) IsEnabledForUserOrRepoOrOwner(ctx context.Context, featureFlagName string, userId, repoId, repoOwnerId int64) bool {
	return c.IsEnabled(ctx, featureFlagName,
		extensions.NewUser(strconv.FormatInt(userId, 10)),
		extensions.NewRepository(strconv.FormatInt(repoId, 10)),
		extensions.NewUser(strconv.FormatInt(repoOwnerId, 10)), // Owner as a user
		extensions.NewOrganization(strconv.FormatInt(repoOwnerId, 10)))
}

func (c *CachingClient) IsEnabledForGitHubApp(ctx context.Context, featureFlagName string, appId int64) bool {
	return c.IsEnabled(ctx, featureFlagName, extensions.NewGitHubApp(strconv.FormatInt(appId, 10)))
}

func (c *CachingClient) IsEnabledForOAuthApp(ctx context.Context, featureFlagName string, appId int64) bool {
	return c.IsEnabled(ctx, featureFlagName, extensions.NewOAuthApplication(strconv.FormatInt(appId, 10)))
}

func (c *CachingClient) cacheKey(featureFlagName string, actors ...vexi.Actor) string {
	sb := strings.Builder{}
	sb.WriteString(featureFlagName)

	for _, actor := range actors {
		sb.WriteString(":")
		sb.WriteString(actor.VexiID())
	}

	return sb.String()
}

// IsEnabledMiddleware checks if the user has the feature flag enabled.
// If there is no user, or the user does not have the feature flag enabled,
// it returns a 404.
func (c *CachingClient) IsEnabledMiddleware(feature string) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		fn := func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()
			user, hasUser := requestctx.User(ctx)
			if !hasUser {
				c.obsv.Logger.Info("no user in context - returning 404")
				http.NotFound(w, r)
				return
			}

			enabled := c.IsEnabledForUser(ctx, feature, int64(user.ID))
			if !enabled {
				c.obsv.Logger.Info(fmt.Sprintf("feature flag '%s' not enabled - returning 404", feature))
				http.NotFound(w, r)
				return
			}

			next.ServeHTTP(w, r)
		}

		return http.HandlerFunc(fn)
	}
}
