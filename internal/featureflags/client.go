package featureflags

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"sync/atomic"
	"time"

	"github.com/github/feature-management-client-go/vexi"
	hydro_adapter "github.com/github/feature-management-client-go/vexi/adapter/hydro"
	"github.com/github/feature-management-client-go/vexi/extensions"
	"github.com/github/github-telemetry-go/kvp"
	"github.com/github/sweagentd/internal/config"
	"github.com/github/sweagentd/internal/observability"
)

const (
	CopilotSWEAgentIntegrationCodeScanningAlerts          = "copilot_swe_agent_integration_code_scanning_alerts"
	CopilotSWEAgentIntegrationCodeScanningAlertsLinksInPR = "copilot_swe_agent_integration_code_scanning_alerts_links_in_pr"
	CopilotSWEAgentAPIPATFlagName                         = "copilot_swe_agent_api_pat"                // TODO: Remove once new authz logic is verified working
	CopilotSWEAgentAPIAppFlagName                         = "copilot_swe_agent_api_app"                // TODO: Remove once new authz logic is verified working
	CopilotSWEAgentAPIRevisedAuthZ                        = "copilot_swe_agent_api_revised_authz"      // TODO: Remove once new authz logic is verified working
	CopilotSWEAgentAPIInternalApp                         = "copilot_swe_agent_api_gh_internal_app"    // Only for apps that are used to authenticate to the API in a service-to-service form
	CopilotSWEAgentAPIFirstPartyApp                       = "copilot_swe_agent_api_gh_first_party_app" // For apps like VS Code that we can trust
	CopilotSWEAgentAPIDisallow                            = "copilot_swe_agent_api_disallow"           // Disallow specific users or apps from using the API for abuse
	CopilotSweAgentPremQuotaCheckFlagName                 = "copilot_swe_agent_prem_quota_checks"
	CopilotSweAgentTrackingIDLookupFlagName               = "copilot_swe_agent_tracking_id_lookup"
	CopilotSWEAgentExperimentation                        = "copilot_swe_agent_experimentation"
	CopilotSWEAgentIssueBodyEditCheck                     = "sweagentd_issue_body_edit_check"
	CopilotSWEAgentOnlyWakeOnAtCopilot                    = "copilot_swe_agent_only_wake_on_at_copilot"
	CopilotSWEAgentCanSelectModel                         = "sweagentd_can_select_model"
	CopilotSWEAgentGZipProblemStatement                   = "copilot_swe_agent_gzip_problem_statement"
	SweagentdSkipEmptyBranchCreation                      = "sweagentd_skip_empty_branch_creation" // Skip creating an empty branch in sweagentd, in favor of doing it in the monolith
	CopilotSWEAgentEnableExperimentsForAPI                = "copilot_swe_agent_enable_experiments_for_api"
	CopilotSWEAgentAqueductJobQueuing                     = "copilot_swe_agent_aqueduct_job_queuing"
	CopilotSWEAgentJobProcessorDisabled                   = "sweagentd_job_processor_disabled"
)

type Client interface {
	IsEnabled(ctx context.Context, featureFlagName string, actors ...vexi.Actor) bool
	IsEnabledForUser(ctx context.Context, featureFlagName string, userId int64) bool
	IsEnabledForUserOrRepo(ctx context.Context, featureFlagName string, userId, repoId int64) bool
	IsEnabledForUserOrRepoOrOwner(ctx context.Context, featureFlagName string, userId, repoId, repoOwnerId int64) bool
	IsEnabledForGitHubApp(ctx context.Context, featureFlagName string, appId int64) bool
	IsEnabledForOAuthApp(ctx context.Context, featureFlagName string, appId int64) bool
}

type FeatureFlagClient struct {
	client *vexi.Client
	obsv   observability.Exporters
	ready  atomic.Bool
}

// IsEnabled implements Client.
func (f *FeatureFlagClient) IsEnabled(ctx context.Context, featureFlagName string, actors ...vexi.Actor) bool {
	if !f.ready.Load() {
		f.obsv.Logger.Warn("feature flag client not ready, returning default value", kvp.String("feature_flag", featureFlagName))
		return false
	}

	return f.client.IsEnabledWithDefaultValue(ctx, featureFlagName, false, actors...)
}

func (f *FeatureFlagClient) IsEnabledForUser(ctx context.Context, featureFlagName string, userId int64) bool {
	return f.IsEnabled(ctx, featureFlagName, extensions.NewUser(strconv.FormatInt(userId, 10)))
}

func (f *FeatureFlagClient) IsEnabledForUserOrRepo(ctx context.Context, featureFlagName string, userId, repoId int64) bool {
	actors := []vexi.Actor{
		extensions.NewUser(strconv.FormatInt(userId, 10)),
		extensions.NewRepository(strconv.FormatInt(repoId, 10)),
	}
	return f.IsEnabled(ctx, featureFlagName, actors...)
}

func (f *FeatureFlagClient) IsEnabledForUserOrRepoOrOwner(ctx context.Context, featureFlagName string, userId, repoId, repoOwnerId int64) bool {
	return f.IsEnabled(ctx, featureFlagName,
		extensions.NewUser(strconv.FormatInt(userId, 10)),
		extensions.NewRepository(strconv.FormatInt(repoId, 10)),
		extensions.NewUser(strconv.FormatInt(repoOwnerId, 10)), // Owner as a user
		extensions.NewOrganization(strconv.FormatInt(repoOwnerId, 10)))
}

func (f *FeatureFlagClient) IsEnabledForGitHubApp(ctx context.Context, featureFlagName string, appId int64) bool {
	return f.IsEnabled(ctx, featureFlagName, extensions.NewGitHubApp(strconv.FormatInt(appId, 10)))
}

func (f *FeatureFlagClient) IsEnabledForOAuthApp(ctx context.Context, featureFlagName string, appId int64) bool {
	return f.IsEnabled(ctx, featureFlagName, extensions.NewOAuthApplication(strconv.FormatInt(appId, 10)))
}

var _ Client = &FeatureFlagClient{}

func NewClient(ctx context.Context, cfg *config.Config, obsv observability.Exporters) (Client, error) {
	owningServices := []string{"copilot-coding-agent", "copilot-extensibility", "sweagentd"}
	hydroAdapter, err := hydro_adapter.NewAdapterWithOwningServices(ctx, cfg.ServiceName, owningServices,
		hydro_adapter.WithLogger(obsv.Logger.Named("vexi-hydro")),
		hydro_adapter.WithStatsClient(obsv.Statter),
		hydro_adapter.WithHydroBrokers(strings.Join(cfg.HydroBrokers, ",")),
		hydro_adapter.WithCuratedSegments(),
	)
	if err != nil {
		return nil, fmt.Errorf("creating hydro adapter: %w", err)
	}

	cacheReady, err := hydroAdapter.Start(ctx)
	if err != nil {
		return nil, fmt.Errorf("starting hydro adapter: %w", err)
	}

	// Wait for cache to become ready
	ready := false
	select {
	case <-cacheReady:
		// Cache is ready
		ready = true
	case <-time.After(5 * time.Second):
		// Timeout after 5 seconds
		obsv.Logger.Warn("feature flag cache not ready after 5 seconds")
	}

	vexiClient, err := vexi.NewClient(ctx,
		hydroAdapter,
		vexi.WithLogger(obsv.Logger.Named("vexi")),
		vexi.WithStatsClient(obsv.Statter),
	)
	if err != nil {
		return nil, fmt.Errorf("creating vexi client: %w", err)
	}

	ffc := &FeatureFlagClient{
		client: vexiClient,
		obsv:   obsv.WithNamedLogger("feature-flags"),
	}

	if !ready {
		go func() {
			<-cacheReady
			ffc.obsv.Logger.Info("feature flag client is now ready")
			ffc.ready.Store(true)
		}()
	} else {
		ffc.ready.Store(true)
	}

	return ffc, nil
}
