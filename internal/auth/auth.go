// Package auth contains middleware and helpers for authentication
package auth

import (
	"net/http"
	"strings"

	v1 "github.com/github/copilot-twirp/proto/users/v1"
	"github.com/github/go-stats"
	"github.com/github/sweagentd/internal/authnd"
	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/githubtwirp"
	"github.com/github/sweagentd/internal/observability"
	"github.com/github/sweagentd/internal/requestctx"
)

const (
	AuthorizationHeader  = "Authorization"
	EncryptedTokenPrefix = "GitHub-Bearer "
)

// Authenticator contains a middleware and helpers for authenticating requests.
type Authenticator struct {
	obsv         observability.Exporters
	authndClient authnd.Authenticator
	ffClient     featureflags.Client
	ghTwirp      githubtwirp.ClientInterface
}

// NewAuthenticator returns an Authenticator.
func NewAuthenticator(obsv observability.Exporters, authndClient authnd.Authenticator, ghTwirp githubtwirp.ClientInterface, ffClient featureflags.Client) *Authenticator {
	return &Authenticator{
		obsv:         obsv,
		authndClient: authndClient,
		ghTwirp:      ghTwirp,
		ffClient:     ffClient,
	}
}

// Middleware authenticates requests and adds the user info to the request context.
func (a *Authenticator) Middleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx, span := a.obsv.Tracer.Start(r.Context(), "auth.Authenticator.Middleware")
		defer span.End()

		tags, _, kvps := requestctx.ToTelemetry(ctx, r)
		logger := a.obsv.Logger.WithContext(ctx).WithFields(kvps...)

		token := a.parseAuthorizationHeader(r)
		if token == "" {
			logger.Error("no token in request")
			http.Error(w, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
			return
		}

		if !a.authndClient.IsValidTokenFormat(token) {
			logger.Error("unsupported token format")
			http.Error(w, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
			return
		}

		tokenInfo, err := a.authndClient.AuthenticateAPIToken(ctx, token)
		if err != nil {
			logger.WithError(err).Error("failed to authenticate token")
			a.obsv.Statter.Counter("authnd.attempt", tags.Merge(stats.Tags{"success": "false"}), 1)
			http.Error(w, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
			return
		}

		// This should be the actual request IP rather than just the IP used to connect to the service.
		ipAddress := r.Header.Get("X-Real-IP")

		a.obsv.Statter.Counter("authnd.attempt", tags.Merge(stats.Tags{"success": "true", "credential_type": string(tokenInfo.Type)}), 1)

		// let's see if we can get the tracking ID from dotcom
		var trackingID string

		// check if CopilotSweAgentTrackingIDLookupFlagName is enabled
		// please only call this if you have a ffclient set (like you passed it in NewAuthenticator)
		if a.ffClient.IsEnabledForUser(ctx, featureflags.CopilotSweAgentTrackingIDLookupFlagName, int64(tokenInfo.ActorID)) {
			// use the GitHub Twirp client to get the tracking ID
			resp, err := a.ghTwirp.UserDetailAPI().GetCopilotUser(
				ctx,
				&v1.GetCopilotUserRequest{
					Id: tokenInfo.ActorID,
				},
			)
			if err != nil {
				logger.WithError(err).Error("failed to get copilot user details")
				a.obsv.Statter.Counter("authnd.copilot_user_lookup", tags.Merge(stats.Tags{"success": "false"}), 1)
			} else {
				a.obsv.Statter.Counter("authnd.copilot_user_lookup", tags.Merge(stats.Tags{"success": "true"}), 1)
				trackingID = resp.UserDetails.AnalyticsTrackingId
			}
		}

		r = r.WithContext(requestctx.AddData(ctx, &requestctx.CtxData{
			UserInfo: &requestctx.UserInfo{
				ID:         tokenInfo.ActorID,
				Login:      tokenInfo.UserLogin,
				RequestIP:  ipAddress,
				TrackingID: trackingID,
				TokenInfo: &requestctx.TokenInfo{
					SessionID:                    tokenInfo.SessionID,
					ApplicationID:                tokenInfo.ApplicationID,
					Type:                         string(tokenInfo.Type),
					ApplicationType:              tokenInfo.ApplicationType,
					Token:                        token,
					OrganizationSSOAuthorizedIDs: tokenInfo.OrganizationSSOAuthorizedIDs,
				},
			},
		}))

		next.ServeHTTP(w, r)
	})
}

// parseAuthorizationHeader parses the `Authorization` header and returns the token.
func (a *Authenticator) parseAuthorizationHeader(r *http.Request) string {
	authHeader := r.Header.Get(AuthorizationHeader)
	if authHeader == "" {
		return ""
	}

	// support "Bearer", "bearer" and "token" to conform to api.github.com
	token := authHeader
	token = strings.TrimPrefix(token, "Bearer ")
	token = strings.TrimPrefix(token, "bearer ")
	token = strings.TrimPrefix(token, "token ")
	return token
}
