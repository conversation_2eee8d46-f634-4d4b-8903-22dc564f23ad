package config

import (
	"bytes"
	"compress/gzip"
	"embed"
	"encoding/base64"
	"fmt"
	"net/url"
	"path"
	"strings"
)

//go:embed default_network_rules
var defaultNetworkRules embed.FS

const defaultNetworkRulesEmbeddedFolderName = "default_network_rules"

// These are domains that we do not want to allow by default, but should not show up in the firewall alert.
// This should mainly be things that happen independently by the runner OS or common telemetry endpoints.
var DomainIgnoreList = []string{
	// Also ignore _* - e.g. _https._tcp.apt.releases.hashicorp.com
	"cdn.fwupd.org",
	"fwupd.org",
	"esm.unbuntu.com",
	"motd.ubuntu.com",
	"telemetry.nextjs.org",
}

// This is a list of URIs that are needed for the agent to function in production
// See https://docs.github.com/en/actions/using-github-hosted-runners/using-github-hosted-runners/about-github-hosted-runners#communication-requirements-for-github-hosted-runners
// For Proxima/GHE.com, see https://docs.github.com/en/enterprise-cloud@latest/admin/data-residency/network-details-for-ghecom
var githubBaseRequiredAllowList = []string{
	// "https://github.{{suffix}}/", <- Covered by GITHUB_SERVER_URL, so can be dynamic
	"githubusercontent.{{suffix}}", // <- Need wildcard for this domain, so don't prefix with https://
	"https://raw.githubusercontent.{{suffix}}/",
	"https://objects.githubusercontent.{{suffix}}/",
	"https://codeload.github.{{suffix}}/",
	"https://uploads.github.{{suffix}}/user-attachments/assets/",      // <- Used to upload images to GitHub
	"https://api.github.{{suffix}}/internal/user-attachments/assets/", // <- Used to download user attachments from GitHub
	"https://github.githubassets.{{suffix}}/assets",                   // <- Used to load assets for GitHub pages, e.g.'"
	"https://*.githubusercontent.{{suffix}}",                          // <- Used to download assets from S3
	"https://uploads.github.{{suffix}}",
	// Special IPs / domains
	"**********",           // <- Docker default bridge network IP. We will remove this w/a firewall binary update to auto-handle
	"*************",        // <- Azure MetaData endpoint used by WAAgent https://learn.microsoft.com/en-us/azure/virtual-machines/extensions/agent-linux
	"host.docker.internal", // <- Docker internal host IP if set
	// Git LFS
	"https://lfs.github.com/",
	"https://github-cloud.githubusercontent.{{suffix}}/",
	"https://github-cloud.s3.amazonaws.com/",
	// GitHub URLs that need to be added if we lock down the host instead of the process
	// "https://api.github.{{suffix}}/", <- Covered by GITHUB_API_URL_BASE, so can be dynamic
	// "https://*.actions.githubusercontent.{{suffix}}/",
	// "https://pkg.actions.githubusercontent.{{suffix}}/",
	// "https://results-receiver.actions.githubusercontent.{{suffix}}/",
	// "https://objects-origin.githubusercontent.{{suffix}}/"
	// "https://github-releases.githubusercontent.{{suffix}}/"
	// "https://github-registry-files.githubusercontent.{{suffix}}/"
	// "https://dependabot-actions.githubapp.{{suffix}}/" <- Should not be needed
}

// WARNING: This list is being replaced by the rules files in internal/config/default_network_rules/
//
//	⚠ While this change is tested changes must be duplicated. This will will then be removed once
//	⚠ we've confirmed new approach works as expected.
//
// This is the default allow list for the agent firewall. It can be overridden by the user.
// It contains trusted package repositories, certificate authorities, and top safe URLs from
// https://splunk.githubapp.com/en-US/app/gh_reference_app/report?s=%2FservicesNS%2Fchuxel%2540github.com%2Fgh_reference_app%2Fsaved%2Fsearches%2FCopilot%2520code%2520agent%2520top%2520blocked%2520domains
var defaultAllowList = []string{
	// Node / npm / JS ecosystem
	"https://npmjs.org/",
	"https://npmjs.com/",
	"https://registry.npmjs.com/",
	"https://registry.npmjs.org/",
	"https://skimdb.npmjs.com/",
	"https://npm.pkg.github.com/",
	"https://api.npms.io/",
	"https://npm.jsr.io/",
	"https://nodejs.org/",
	"https://yarnpkg.com/",
	"https://registry.yarnpkg.com/",
	"https://repo.yarnpkg.com/",
	"deb.nodesource.com",
	"https://get.pnpm.io/",
	"https://bun.sh",
	"https://deno.land/",
	// Bower
	"https://registry.bower.io/",
	// Python
	"https://pypi.python.org/",
	"https://pypi.org/",
	"https://pip.pypa.io/",
	"https://pythonhosted.org/",
	"https://files.pythonhosted.org/",
	"https://bootstrap.pypa.io/",
	"https://conda.binstar.org/",
	"https://conda.anaconda.org/",
	"https://binstar.org/",
	"https://anaconda.org/",
	"https://download.pytorch.org/",
	"https://repo.continuum.io/",
	"https://repo.anaconda.com/",
	// C#
	"https://nuget.org/",
	"https://dist.nuget.org/",
	"https://api.nuget.org/",
	"https://nuget.pkg.github.com/",
	"https://dotnet.microsoft.com/",
	"https://pkgs.dev.azure.com/",
	"https://builds.dotnet.microsoft.com/",
	"https://dotnetcli.blob.core.windows.net/",
	"https://nugetregistryv2prod.blob.core.windows.net/",
	"https://azuresearch-usnc.nuget.org/",
	"https://azuresearch-ussc.nuget.org/",
	"https://dc.services.visualstudio.com/", // <- NuGet seems to not like it if this is not allowed
	"https://dot.net",
	"https://download.visualstudio.microsoft.com", // <- Agent frequently tries to download .NET from here and gives up when blocked. Allow to help it recover gracefully.
	"https://dotnetcli.azureedge.net/",            // <- Azure CDN for dotnet downloads
	"https://ci.dot.net/",                         // <- Used by https://builds.dotnet.microsoft.com/dotnet/scripts/v1/dotnet-install.sh
	"https://www.microsoft.com/",                  // <- Appears in some .NET related output and is harmless to let the agent access
	"http://www.microsoft.com/pkiops/crl/",        // <- Appears in some .NET related output and is harmless to let the agent access
	// Ruby
	"https://rubygems.org/",
	"https://api.rubygems.org/",
	"https://rubygems.pkg.github.com/",
	"https://bundler.rubygems.org/",
	"https://gems.rubyforge.org/",
	"https://gems.rubyonrails.org/",
	"https://index.rubygems.org/",
	"https://cache.ruby-lang.org/",
	"rvm.io", // <- Need wildcard for this domain, so don't prefix with https://
	// Json
	"https://json-schema.org/",
	"https://json.schemastore.org/",
	// Java
	"https://www.java.com/",
	"https://jdk.java.net/",
	"https://api.adoptium.net/",
	"https://adoptium.net/",
	"https://search.maven.org/",
	"https://maven.apache.org/",
	"https://repo.maven.apache.org/",
	"https://repo1.maven.org/",
	"https://repo.maven.apache.org/",
	"https://maven.pkg.github.com/",
	"https://maven-central.storage-download.googleapis.com/", // <- Google Mirror
	"https://maven.google.com",                               // <- Android
	"https://maven.oracle.com/",
	"https://jcenter.bintray.com/", // <- Old URL that redirects to "https://repo1.maven.org/"
	"https://oss.sonatype.org/",    // <- Yet another older maven URL
	"https://repo.spring.io/",
	"https://gradle.org/",
	"https://services.gradle.org/",
	"https://plugins.gradle.org/",
	"https://plugins-artifacts.gradle.org",
	"https://repo.grails.org/",
	"https://download.eclipse.org/",
	"https://download.oracle.com/",
	// Rust / Cargo
	"https://crates.io/",
	"https://index.crates.io/",
	"https://static.crates.io/",
	"https://sh.rustup.rs/",
	"https://static.rust-lang.org/",
	// Go
	"https://go.dev/",
	"https://golang.org/",
	"https://proxy.golang.org/",
	"https://sum.golang.org/",
	"https://pkg.go.dev/",
	"https://goproxy.io/",
	"https://storage.googleapis.com/proxy-golang-org-prod/", // Go toolchain downloads
	// Swift
	"https://download.swift.org/",
	"https://swift.org/",
	"https://cocoapods.org/",
	"https://cdn.cocoapods.org/",
	// Perl
	"https://cpan.org/",
	"https://www.cpan.org/",
	"https://metacpan.org/",
	"https://cpan.metacpan.org/",
	// PHP
	"https://repo.packagist.org/",
	"https://packagist.org/",
	"https://getcomposer.org/",
	// Haskell
	"https://haskell.org/",
	"hackage.haskell.org", // <- Appears to need more than just https
	"https://get-ghcup.haskell.org/",
	"https://downloads.haskell.org/",
	// Dart
	"https://pub.dev/",
	"https://pub.dartlang.org/",
	"https://storage.googleapis.com/pub-packages/", // Package downloads
	"https://storage.googleapis.com/dart-archive/", // Toolchain downloads
	// Containers
	"https://ghcr.io/",
	"https://registry.hub.docker.com/",
	"docker.io",  // <- Need wildcard for this domain, so don't prefix with https://
	"docker.com", // <- Need wildcard for this domain, so don't prefix with https://
	"https://production.cloudflare.docker.com/",
	"https://dl.k8s.io/",
	"https://pkgs.k8s.io/",
	"https://quay.io/",
	"https://mcr.microsoft.com/",
	"https://gcr.io/",
	"https://public.ecr.aws/",
	"https://auth.docker.io/",
	// "azurecr.io/", // <- We'd have to wildcard allow here which is pretty wide open. Omit for now in favor of MCR.
	// Linux Package Managers
	// Ubuntu - Note: apt uses SRV requests and HTTP so we need to allow all subdomains and protocols for debian + ubuntu
	// See: https://github.com/github/sweagentd/issues/1759
	"archive.ubuntu.com",
	"security.ubuntu.com",
	"ppa.launchpad.net",
	"keyserver.ubuntu.com",
	// Debian
	"deb.debian.org",
	"security.debian.org",
	"packages.debian.org",
	"keyring.debian.org",
	"debian.map.fastlydns.net", // <- CDN for deb.debian.org and security.debian.org
	"apt.llvm.org",             // <- Specific to how C++/clang is managed on Debian/Ubuntu
	// Common Package Mirrors (common)
	"packagecloud.io",
	"packages.cloud.google.com",
	// Fedora
	"https://dl.fedoraproject.org/",
	"https://mirrors.fedoraproject.org/",
	"https://download.fedoraproject.org/",
	// CentOS
	"https://mirror.centos.org/",
	"https://vault.centos.org/",
	// Alpine
	"https://dl-cdn.alpinelinux.org/",
	"https://pkg.alpinelinux.org/",
	// Arch
	"https://mirror.archlinux.org/",
	"https://archlinux.org/",
	// SUSE
	"https://download.opensuse.org/",
	// Red Hat
	"https://cdn.redhat.com/",
	// MS Sources
	"packages.microsoft.com",
	"azure.archive.ubuntu.com",
	// Hashicorp Tools
	"https://releases.hashicorp.com/",
	"https://apt.releases.hashicorp.com/",
	"https://yum.releases.hashicorp.com/",
	// Terraform
	"https://registry.terraform.io/",
	// Playwright
	"https://playwright.download.prss.microsoft.com/",
	"https://cdn.playwright.dev/",
	"https://playwright.azureedge.net/",
	"https://playwright-akamai.azureedge.net/",
	"https://playwright-verizon.azureedge.net/",
	// Certificate Authorities
	"https://oneocsp.microsoft.com/", // Check the status of digital certs
	"crl3.digicert.com",
	"crl4.digicert.com",
	"ocsp.digicert.com",
	"ts-crl.ws.symantec.com",  // Digicert
	"ts-ocsp.ws.symantec.com", // Digicert
	"crl.geotrust.com",        // Digicert
	"ocsp.geotrust.com",       // Digicert
	"crl.thawte.com",          // Digicert
	"ocsp.thawte.com",         // Digicert
	"crl.verisign.com",        // Digicert
	"ocsp.verisign.com",       // Digicert
	"crl.globalsign.com",
	"ocsp.globalsign.com",
	"crls.ssl.com",
	"ocsp.ssl.com",
	"crl.identrust.com",
	"ocsp.identrust.com",
	"crl.sectigo.com",
	"ocsp.sectigo.com",
	"crl.usertrust.com",
	"ocsp.usertrust.com",
	"s.symcb.com", // Digicert
	"s.symcd.com", // Digicert
}

func GenerateEncodedDefaultRulesSetEnv() (string, error) {
	output := strings.Builder{}
	files, err := defaultNetworkRules.ReadDir(defaultNetworkRulesEmbeddedFolderName)
	if err != nil {
		return "", fmt.Errorf("failed to read default network rules directory: %w", err)
	}
	for _, file := range files {
		bytes, err := defaultNetworkRules.ReadFile(path.Join("default_network_rules", file.Name()))
		if err != nil {
			return "", fmt.Errorf("failed to read file %s: %w", file.Name(), err)
		}

		output.WriteString(fmt.Sprintf("\n---\n%s", bytes))
	}

	// Gzip and base64 encode the output
	var buf bytes.Buffer
	gzipWriter := gzip.NewWriter(&buf)
	if _, err := gzipWriter.Write([]byte(output.String())); err != nil {
		return "", fmt.Errorf("failed to gzip default network rules: %w", err)
	}
	if err := gzipWriter.Close(); err != nil {
		return "", fmt.Errorf("failed to close gzip writer: %w", err)
	}

	// Base64 encode the gzipped content
	encoded := base64.StdEncoding.EncodeToString(buf.Bytes())
	return encoded, nil
}

// Converts the defaultAllowList into a comma-separated string
func GenerateDefaultFirewallAllowList() string {
	return strings.Join(defaultAllowList, ",")
}

func GenerateRequiredFirewallAllowList(cfg *Config) string {
	sb := strings.Builder{}
	// Allow calls to locally running services on all ports and we will also not add
	// variations like http://localhost:<port> since a URL with a port is not supported yet.
	// But we should only see these in development, so its not a user-facing issue.
	// Note: 127.0.0.1 is on by default for firewall otherwise lots of stuff breaks
	sb.WriteString("localhost,")

	// Automatically add the GitHub server URL, API URL, and callback URLs w/o the path
	// to the allow list. This is to ensure that the agent can communicate with the server.
	baseList := strings.Join(githubBaseRequiredAllowList, ",")
	serverUrl, err := url.Parse(cfg.GitHubServerURL)
	if err != nil {
		sb.WriteString(strings.ReplaceAll(baseList, "{{suffix}}", "com"))
	} else {
		if serverUrl.Hostname() != "localhost" {
			serverUrl.Path = "/"
			sb.WriteString(serverUrl.String())
			sb.WriteString(",")
		}
		// Adapt URLs when the server is localhost or github.localhost
		if serverUrl.Hostname() == "github.localhost" || serverUrl.Hostname() == "localhost" {
			sb.WriteString(strings.ReplaceAll(baseList, "{{suffix}}", "localhost"))
		} else {
			// TODO: Adapt to GHE.com URLs beyond "github.com" which should be automatically handled. Most others appear to be normal .com addresses.
			sb.WriteString(strings.ReplaceAll(baseList, "{{suffix}}", "com"))
		}
	}
	sb.WriteString(",")

	// Add unique capiUrl unless it is localhost
	capiUrl, err := url.Parse(cfg.CopilotAPIURL)
	if err == nil && capiUrl.Hostname() != "localhost" {
		capiUrl.Path = "/"
		sb.WriteString(capiUrl.String())
		sb.WriteString(",")
	}
	// Add unique callbackUrl unless it is localhost
	callbackUrl, err := url.Parse(cfg.AgentCallbackURL)
	if err == nil && callbackUrl.Hostname() != "localhost" {
		callbackUrl.Path = "/"
		sb.WriteString(callbackUrl.String())
		sb.WriteString(",")
	}
	if sb.Len() > 0 {
		return sb.String()[:sb.Len()-1]
	}
	return sb.String()
}
