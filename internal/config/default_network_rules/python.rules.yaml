version: 0.0.1
rules:
  - kind: http-rule
    url: { scheme: ["https"], domain: pypi.python.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: pypi.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: pip.pypa.io }
  - kind: http-rule
    url:
      { scheme: ["https"], domain: pythonhosted.org, allow-any-subdomain: true }
  - kind: http-rule
    url: { scheme: ["https"], domain: files.pythonhosted.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: bootstrap.pypa.io }
  - kind: http-rule
    url: { scheme: ["https"], domain: conda.binstar.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: conda.anaconda.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: binstar.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: anaconda.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: download.pytorch.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: repo.continuum.io }
  - kind: http-rule
    url: { scheme: ["https"], domain: repo.anaconda.com }
