version: 0.0.1
rules:
  - kind: http-rule
    url: { scheme: ["https"], domain: npmjs.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: npmjs.com }
  - kind: http-rule
    url: { scheme: ["https"], domain: registry.npmjs.com }
  - kind: http-rule
    url: { scheme: ["https"], domain: registry.npmjs.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: skimdb.npmjs.com }
  - kind: http-rule
    url: { scheme: ["https"], domain: npm.pkg.github.com }
  - kind: http-rule
    url: { scheme: ["https"], domain: api.npms.io }
  - kind: http-rule
    url: { scheme: ["https"], domain: nodejs.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: yarnpkg.com }
  - kind: http-rule
    url: { scheme: ["https"], domain: registry.yarnpkg.com }
  - kind: http-rule
    url: { scheme: ["https"], domain: repo.yarnpkg.com }
  - kind: http-rule
    url: { domain: deb.nodesource.com }
  - kind: http-rule
    url: { scheme: ["https"], domain: get.pnpm.io }
  - kind: http-rule
    url: { scheme: ["https"], domain: bun.sh }
  - kind: http-rule
    url: { scheme: ["https"], domain: deno.land }
  - kind: http-rule
    url: { scheme: ["https"], domain: registry.bower.io }
