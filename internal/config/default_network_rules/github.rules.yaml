version: 0.0.1
rules:
  - kind: http-rule
    url: { domain: githubusercontent.com, allow-any-subdomain: true }
  - kind: http-rule
    url: { scheme: ["https"], domain: raw.githubusercontent.com }
  - kind: http-rule
    url: { scheme: ["https"], domain: objects.githubusercontent.com }
  - kind: http-rule
    url: { scheme: ["https"], domain: lfs.github.com }
  - kind: http-rule
    url: { scheme: ["https"], domain: github-cloud.githubusercontent.com }
  - kind: http-rule
    url: { scheme: ["https"], domain: github-cloud.s3.amazonaws.com }
  - kind: http-rule
    url: { scheme: ["https"], domain: codeload.github.com }
  - kind: http-rule
    url:
      scheme: ["https"]
      domain: uploads.github.com
      path: "/copilot/chat/attachments/"
