version: 0.0.1
rules:
  - kind: http-rule
    url: { scheme: ["https"], domain: go.dev }
  - kind: http-rule
    url: { scheme: ["https"], domain: golang.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: proxy.golang.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: sum.golang.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: pkg.go.dev }
  - kind: http-rule
    url: { scheme: ["https"], domain: goproxy.io }
  - kind: http-rule
    url:
      scheme: ["https"]
      domain: storage.googleapis.com
      path: "/proxy-golang-org-prod/"
