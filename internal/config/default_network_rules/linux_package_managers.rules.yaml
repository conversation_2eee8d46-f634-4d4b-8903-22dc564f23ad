version: 0.0.1
rules:
  # Reminder: Lots of `apt` repositories don't use HTTP see: https://superuser.com/questions/1356786/ubuntu-apt-why-are-the-respositories-accessed-over-http

  # Ubuntu
  - kind: http-rule
    url: { scheme: ["http", "https"], domain: archive.ubuntu.com }
  - kind: http-rule
    url: { scheme: ["http", "https"], domain: security.ubuntu.com }
  - kind: http-rule
    url: { scheme: ["http", "https"], domain: ppa.launchpad.net }
  - kind: http-rule
    url: { scheme: ["http", "https"], domain: keyserver.ubuntu.com }
  - kind: http-rule
    url: { scheme: ["http", "https"], domain: azure.archive.ubuntu.com }
  - kind: http-rule
    url: { scheme: ["http", "https"], domain: api.snapcraft.io }

  # Debian
  - kind: http-rule
    url: { scheme: ["http", "https"], domain: deb.debian.org }
  - kind: http-rule
    url: { scheme: ["http", "https"], domain: security.debian.org }
  - kind: http-rule
    url: { scheme: ["http", "https"], domain: keyring.debian.org }
  - kind: http-rule
    url: { scheme: ["http", "https"], domain: packages.debian.org }
  - kind: http-rule
    url: { scheme: ["http", "https"], domain: debian.map.fastlydns.net }
  - kind: http-rule
    url: { scheme: ["http", "https"], domain: apt.llvm.org }

  # Fedora
  - kind: http-rule
    url: { scheme: ["https"], domain: dl.fedoraproject.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: mirrors.fedoraproject.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: download.fedoraproject.org }

  # CentOS
  - kind: http-rule
    url: { scheme: ["https"], domain: mirror.centos.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: vault.centos.org }

  # Alpine
  - kind: http-rule
    url: { scheme: ["https"], domain: dl-cdn.alpinelinux.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: pkg.alpinelinux.org }

  # Arch
  - kind: http-rule
    url: { scheme: ["https"], domain: mirror.archlinux.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: archlinux.org }

  # SUSE
  - kind: http-rule
    url: { scheme: ["https"], domain: download.opensuse.org }

  # Red Hat
  - kind: http-rule
    url: { scheme: ["https"], domain: cdn.redhat.com }

  # Common Package Mirrors
  - kind: http-rule
    url: { scheme: ["https"], domain: packagecloud.io }
  - kind: http-rule
    url: { scheme: ["https"], domain: packages.cloud.google.com }

  # Microsoft Sources
  - kind: http-rule
    url: { scheme: ["https"], domain: packages.microsoft.com }
