version: 0.0.1
rules:
  - kind: http-rule
    url: { scheme: ["https"], domain: www.java.com }
  - kind: http-rule
    url: { scheme: ["https"], domain: jdk.java.net }
  - kind: http-rule
    url: { scheme: ["https"], domain: api.adoptium.net }
  - kind: http-rule
    url: { scheme: ["https"], domain: adoptium.net }
  - kind: http-rule
    url: { scheme: ["https"], domain: search.maven.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: maven.apache.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: repo.maven.apache.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: repo1.maven.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: maven.pkg.github.com }
  - kind: http-rule
    url:
      {
        scheme: ["https"],
        domain: maven-central.storage-download.googleapis.com,
      }
  - kind: http-rule
    url: { scheme: ["https"], domain: maven.google.com }
  - kind: http-rule
    url: { scheme: ["https"], domain: maven.oracle.com }
  - kind: http-rule
    url: { scheme: ["https"], domain: jcenter.bintray.com }
  - kind: http-rule
    url: { scheme: ["https"], domain: oss.sonatype.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: repo.spring.io }
  - kind: http-rule
    url: { scheme: ["https"], domain: gradle.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: services.gradle.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: plugins.gradle.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: plugins-artifacts.gradle.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: repo.grails.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: download.eclipse.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: download.oracle.com }
