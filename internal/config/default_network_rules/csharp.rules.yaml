version: 0.0.1
rules:
  - kind: http-rule
    url: { scheme: ["https"], domain: nuget.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: dist.nuget.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: api.nuget.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: nuget.pkg.github.com }
  - kind: http-rule
    url: { scheme: ["https"], domain: dotnet.microsoft.com }
  - kind: http-rule
    url: { scheme: ["https"], domain: pkgs.dev.azure.com }
  - kind: http-rule
    url: { scheme: ["https"], domain: builds.dotnet.microsoft.com }
  - kind: http-rule
    url: { scheme: ["https"], domain: dotnetcli.blob.core.windows.net }
  - kind: http-rule
    url:
      { scheme: ["https"], domain: nugetregistryv2prod.blob.core.windows.net }
  - kind: http-rule
    url: { scheme: ["https"], domain: azuresearch-usnc.nuget.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: azuresearch-ussc.nuget.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: dc.services.visualstudio.com }
  - kind: http-rule
    url: { scheme: ["https"], domain: dot.net }
  - kind: http-rule
    url: { scheme: ["https"], domain: download.visualstudio.microsoft.com }
  - kind: http-rule
    url: { scheme: ["https"], domain: dotnetcli.azureedge.net }
  - kind: http-rule
    url: { scheme: ["https"], domain: ci.dot.net }
  - kind: http-rule
    url: { scheme: ["https"], domain: www.microsoft.com }
  - kind: http-rule
    url: { scheme: ["https"], domain: oneocsp.microsoft.com }
  - kind: http-rule
    name: "Allow certificate revocation list over http"
    url:
      scheme: ["http"]
      domain: "www.microsoft.com"
      path: "/pkiops/crl/"
