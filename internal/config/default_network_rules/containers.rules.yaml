version: 0.0.1
rules:
  - kind: ip-rule
    name: docker-compose-bridge-ip
    ip: "**********"
  - kind: http-rule
    url: { scheme: ["https"], domain: ghcr.io }
  - kind: http-rule
    url: { scheme: ["https"], domain: registry.hub.docker.com }
  - kind: http-rule
    url: { domain: docker.io, allow-any-subdomain: true }
  - kind: http-rule
    url: { domain: docker.com, allow-any-subdomain: true }
  - kind: http-rule
    url: { scheme: ["https"], domain: production.cloudflare.docker.com }
  - kind: http-rule
    url: { scheme: ["https"], domain: dl.k8s.io }
  - kind: http-rule
    url: { scheme: ["https"], domain: pkgs.k8s.io }
  - kind: http-rule
    url: { scheme: ["https"], domain: quay.io }
  - kind: http-rule
    url: { scheme: ["https"], domain: mcr.microsoft.com }
  - kind: http-rule
    url: { scheme: ["https"], domain: gcr.io }
  - kind: http-rule
    url: { scheme: ["https"], domain: public.ecr.aws }
  - kind: http-rule
    url: { scheme: ["https"], domain: auth.docker.io }
