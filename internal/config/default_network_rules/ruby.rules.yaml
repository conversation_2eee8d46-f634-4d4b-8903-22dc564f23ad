version: 0.0.1
rules:
  - kind: http-rule
    url: { scheme: ["https"], domain: rubygems.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: api.rubygems.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: rubygems.pkg.github.com }
  - kind: http-rule
    url: { scheme: ["https"], domain: bundler.rubygems.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: gems.rubyforge.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: gems.rubyonrails.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: index.rubygems.org }
  - kind: http-rule
    url: { scheme: ["https"], domain: cache.ruby-lang.org }
  - kind: http-rule
    url: { domain: rvm.io, allow-any-subdomain: true }
