package config

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestDefaultAllowList(t *testing.T) {
	list := GenerateDefaultFirewallAllowList()

	// Test that essential domains are present
	essentialDomains := []string{
		// Package Managers
		"npmjs.org",
		"pypi.org",
		"rubygems.org",
		"gradle.org",
		"crates.io",
		"proxy.golang.org",
		// Build Essentials
		"nodejs.org",
		"yarnpkg.com",
		"registry.yarnpkg.com",
		"adoptium.net",
		"sh.rustup.rs",
		"quay.io",
		"mcr.microsoft.com",
		"gcr.io",
		"releases.hashicorp.com",

		// Linux Package Managers
		"archive.ubuntu.com",
		"deb.debian.org",
		"dl.fedoraproject.org",
		"mirror.centos.org",
		"dl-cdn.alpinelinux.org",
		"mirror.archlinux.org",
		"download.opensuse.org",
		"cdn.redhat.com",
	}

	for _, domain := range essentialDomains {
		assert.True(t, strings.Contains(list, domain), "Default allow list should contain %s", domain)
	}
}

func TestGenerateRequiredFirewallAllowList(t *testing.T) {
	cfg := &Config{
		GitHubServerURL:  "https://github.com",
		CopilotAPIURL:    "https://api.githubcopilot.com",
		AgentCallbackURL: "https://test-callback.com",
	}

	list := GenerateRequiredFirewallAllowList(cfg)

	assert.Contains(t, list, "localhost")
	assert.Contains(t, list, "github.com")
	assert.Contains(t, list, "githubusercontent.com")
	assert.Contains(t, list, "test-callback.com")
}
