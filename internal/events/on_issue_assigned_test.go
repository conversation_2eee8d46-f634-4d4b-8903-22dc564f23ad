package events

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"testing"
	"time"

	copilotUsersTwirp "github.com/github/copilot-twirp/proto/users/v1"
	sweagentv0 "github.com/github/hydro-schemas-go/hydro/schemas/sweagentd/v0"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/github/hydro-client-go/v7/pkg/hydro"
	sweagentEntitiesv0 "github.com/github/hydro-schemas-go/hydro/schemas/sweagentd/v0/entities"
	et "github.com/github/sweagentd/internal/events/eventstest"
	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/githubtwirp"
	"github.com/github/sweagentd/internal/observability"
	sweagentdTwirp "github.com/github/sweagentd/proto/sweagentd/v1"
	gg "github.com/google/go-github/v72/github"
	"github.com/stretchr/testify/require"
)

func TestOnIssueAssigned(t *testing.T) {
	obsv := observability.NewNoopExporters()
	ffClient := featureflags.NewNoopClient(map[string]bool{
		featureflags.CopilotSWEAgentOnlyWakeOnAtCopilot: true,
		featureflags.CopilotSWEAgentAqueductJobQueuing:  true,
	})
	ghFactory := &github.NoopClientFactory{
		GitHubAppLogin: et.GitHubAppLogin,
		GitHubBotLogin: et.GitHubBotLogin,
	}

	t.Run("queues the agent job", func(t *testing.T) {
		tests := []struct {
			name           string
			queuingEnabled bool
		}{
			{
				name:           "queues job when queuing is enabled",
				queuingEnabled: true,
			},
			{
				name:           "executes job when queuing is disabled",
				queuingEnabled: false,
			},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				ffc := featureflags.NewNoopClient(map[string]bool{
					featureflags.CopilotSWEAgentAqueductJobQueuing: tt.queuingEnabled,
				})
				capiClient, jobStore, ghTwirp := commonHandlerDependencies()
				ghTwirp.NoopUserDetailAPI.MockGetCopilotUser = func(ctx context.Context, req *copilotUsersTwirp.GetCopilotUserRequest) (*copilotUsersTwirp.GetCopilotUserResponse, error) {
					return &copilotUsersTwirp.GetCopilotUserResponse{
						UserDetails: &copilotUsersTwirp.CopilotUserDetails{
							CopilotPlan: copilotUsersTwirp.CopilotPlan_COPILOT_PLAN_ENTERPRISE,
						},
					}, nil
				}
				h, executor := NewTestHandler(obsv, capiClient, ffc, jobStore, ghTwirp, ghFactory)

				assignee := et.UserCopilot()
				issue := et.Issue(*assignee)
				ghFactory.MockIssueContext = &github.IssueContext{Repository: *et.Repo(), Issue: *issue}

				err := h.OnIssueAssigned(context.Background(), IssueAssignmentEvent{
					Actor:     EventActorWriter(),
					Repo:      EventRepository(),
					Issue:     EventIssue(),
					Action:    IssueAssignmentEventActionAssigned,
					Assignees: []string{assignee.Login},
				})

				require.NoError(t, err)

				if tt.queuingEnabled {
					require.Len(t, executor.QueuedJobs(), 1)
					require.Len(t, executor.LaunchAgentCalls(), 0)

					require.Equal(t, et.ActorWriter().GetID(), executor.QueuedJobs()[0].ActorId)
				} else {
					require.Len(t, executor.QueuedJobs(), 0)
					require.Len(t, executor.LaunchAgentCalls(), 1)

					require.Equal(t, et.ActorWriter().GetID(), executor.LaunchAgentCalls()[0].ActorID)
				}
			})
		}
	})

	t.Run("ignores events where Copilot was not assigned/unassigned", func(t *testing.T) {
		tests := []struct {
			name             string
			featureFlagState bool
		}{
			{
				name:             "use Assignee when feature flag is enabled",
				featureFlagState: true,
			},
			{
				name:             "use Assignees when feature flag is disabled",
				featureFlagState: false,
			},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				ffClient := featureflags.NewNoopClient(map[string]bool{
					"sweagentd_cleanup_dup_assignment_hack":         tt.featureFlagState,
					featureflags.CopilotSWEAgentOnlyWakeOnAtCopilot: true,
				})
				h, executor := NewDefaultTestHandler(obsv, ffClient, ghFactory)

				assignee := et.UserReader()
				issue := et.Issue(*assignee)
				ghFactory.MockIssueContext = &github.IssueContext{Repository: *et.Repo(), Issue: *issue}

				var err error
				if tt.featureFlagState {
					err = h.OnIssueAssigned(context.Background(), IssueAssignmentEvent{
						Actor:    EventActorWriter(),
						Repo:     EventRepository(),
						Issue:    EventIssue(),
						Action:   IssueAssignmentEventActionAssigned,
						Assignee: assignee.Login,
					})
				} else {
					err = h.OnIssueAssigned(context.Background(), IssueAssignmentEvent{
						Actor:     EventActorWriter(),
						Repo:      EventRepository(),
						Issue:     EventIssue(),
						Action:    IssueAssignmentEventActionAssigned,
						Assignees: []string{assignee.Login},
					})
				}

				require.NoError(t, err)
				require.Len(t, executor.QueuedJobs(), 0)
			})
		}
	})

	t.Run("uses twirp to create the PR", func(t *testing.T) {
		capiClient, jobStore, ghTwirp := commonHandlerDependencies()
		didCallTwirp := false
		ghTwirp.NoopSweagentdAPI = &githubtwirp.NoopSweagentdAPI{
			MockCreatePullRequest: func(ctx context.Context, req *sweagentdTwirp.CreatePullRequestRequest) (*sweagentdTwirp.CreatePullRequestResponse, error) {
				didCallTwirp = true
				return &sweagentdTwirp.CreatePullRequestResponse{
					PullRequest: &sweagentdTwirp.PullRequest{
						Id:           123,
						GithubNumber: 789,
						RepositoryId: 456,
					},
				}, nil
			},
		}
		ghTwirp.NoopUserDetailAPI = &githubtwirp.NoopUserDetailAPI{
			MockGetCopilotUser: func(ctx context.Context, req *copilotUsersTwirp.GetCopilotUserRequest) (*copilotUsersTwirp.GetCopilotUserResponse, error) {
				return &copilotUsersTwirp.GetCopilotUserResponse{
					UserDetails: &copilotUsersTwirp.CopilotUserDetails{
						CopilotPlan: copilotUsersTwirp.CopilotPlan_COPILOT_PLAN_ENTERPRISE,
					},
				}, nil
			},
		}
		ffClient := featureflags.NewNoopClient(map[string]bool{
			"sweagentd_create_pr_twirp":                     true,
			featureflags.CopilotSWEAgentOnlyWakeOnAtCopilot: true,
			featureflags.CopilotSWEAgentAqueductJobQueuing:  true,
		})
		h, executor := NewTestHandler(obsv, capiClient, ffClient, jobStore, ghTwirp, ghFactory)

		assignee := et.UserCopilot()
		issue := et.Issue(*assignee)
		ghFactory.MockIssueContext = &github.IssueContext{Repository: *et.Repo(), Issue: *issue}

		err := h.OnIssueAssigned(context.Background(), IssueAssignmentEvent{
			Actor:     EventActorWriter(),
			Repo:      EventRepository(),
			Issue:     EventIssue(),
			Action:    IssueAssignmentEventActionAssigned,
			Assignees: []string{assignee.Login},
		})

		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 1)
		require.True(t, didCallTwirp)
	})

	t.Run("emits job_created hydro event", func(t *testing.T) {
		messageChan := make(chan hydro.Message, 1)
		sink, err := hydro.NewMemorySink(messageChan)
		require.NoError(t, err)
		publisher, _ := hydro.NewPublisher(sink, hydro.WithEncoder(hydro.NewJSONEncoder()))
		obsvWithPublisher := obsv.WithPublisher(publisher)
		capiClient, jobStore, ghTwirp := commonHandlerDependencies()
		didCallTwirp := false

		ghTwirp.NoopSweagentdAPI = &githubtwirp.NoopSweagentdAPI{
			MockCreatePullRequest: func(ctx context.Context, req *sweagentdTwirp.CreatePullRequestRequest) (*sweagentdTwirp.CreatePullRequestResponse, error) {
				didCallTwirp = true
				return &sweagentdTwirp.CreatePullRequestResponse{
					PullRequest: &sweagentdTwirp.PullRequest{
						Id:           123,
						GithubNumber: 789,
						RepositoryId: 456,
					},
				}, nil
			},
		}
		ghTwirp.NoopUserDetailAPI = &githubtwirp.NoopUserDetailAPI{
			MockGetCopilotUser: func(ctx context.Context, req *copilotUsersTwirp.GetCopilotUserRequest) (*copilotUsersTwirp.GetCopilotUserResponse, error) {
				return &copilotUsersTwirp.GetCopilotUserResponse{
					UserDetails: &copilotUsersTwirp.CopilotUserDetails{
						CopilotPlan: copilotUsersTwirp.CopilotPlan_COPILOT_PLAN_ENTERPRISE,
					},
				}, nil
			},
		}
		ffClient := featureflags.NewNoopClient(map[string]bool{
			"sweagentd_create_pr_twirp":                     true,
			"sweagentd_publish_telemetry_events_to_hydro":   true,
			featureflags.CopilotSWEAgentOnlyWakeOnAtCopilot: true,
			featureflags.CopilotSWEAgentAqueductJobQueuing:  false, // Disable queuing for this test
		})
		h, executor := NewTestHandler(obsvWithPublisher, capiClient, ffClient, jobStore, ghTwirp, ghFactory)

		assignee := et.UserCopilot()
		issue := et.Issue(*assignee)
		ghFactory.MockIssueContext = &github.IssueContext{Repository: *et.Repo(), Issue: *issue}

		eventRepo := EventRepository()
		eventIssue := EventIssue()

		err = h.OnIssueAssigned(context.Background(), IssueAssignmentEvent{
			Actor:     EventActorWriter(),
			Repo:      eventRepo,
			Issue:     eventIssue,
			Action:    IssueAssignmentEventActionAssigned,
			Assignees: []string{assignee.Login},
		})

		require.NoError(t, err)
		require.True(t, didCallTwirp)
		require.Len(t, executor.LaunchAgentCalls(), 1)

		// Check that the job_started event was published to Hydro
		select {
		case eventReceived := <-messageChan:
			var jobStartedEvent = &sweagentv0.JobStarted{}
			protojson.Unmarshal(eventReceived.Value, jobStartedEvent)

			require.NotNil(t, jobStartedEvent)
			require.NotNil(t, jobStartedEvent.Trigger)
			require.Equal(t, sweagentEntitiesv0.JobTrigger_ISSUE_ASSIGNED, jobStartedEvent.Trigger.Type)
			require.Equal(t, sweagentEntitiesv0.ActionType_FIX, jobStartedEvent.Action)

			split := strings.Split(jobStartedEvent.Job.JobId, "-")
			repoOwnerIDFromEvent, err := strconv.ParseInt(split[0], 10, 64)
			require.NoError(t, err)
			require.Equal(t, eventRepo.OwnerID, repoOwnerIDFromEvent)
			repoIDFromEvent, err := strconv.ParseInt(split[1], 10, 64)
			require.NoError(t, err)
			require.Equal(t, eventRepo.ID, repoIDFromEvent)
			require.Equal(t, int64(123), jobStartedEvent.Job.PullRequestId)
			require.Equal(t, "", jobStartedEvent.Job.WorkflowRunId)
			require.Equal(t, eventRepo.ID, jobStartedEvent.Job.RepositoryId)
		default:
			t.Fail()
		}
	})

	t.Run("launches the agent", func(t *testing.T) {
		tests := []struct {
			name             string
			featureFlagState bool
		}{
			{
				name:             "user Assignee when feature flag is enabled",
				featureFlagState: true,
			},
			{
				name:             "use Assignees when feature flag is disabled",
				featureFlagState: false,
			},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				ffClient := featureflags.NewNoopClient(map[string]bool{
					"sweagentd_cleanup_dup_assignment_hack":         tt.featureFlagState,
					featureflags.CopilotSWEAgentOnlyWakeOnAtCopilot: true,
					featureflags.CopilotSWEAgentAqueductJobQueuing:  true,
				})
				capiClient, jobStore, ghTwirp := commonHandlerDependencies()
				ghTwirp.NoopUserDetailAPI.MockGetCopilotUser = func(ctx context.Context, req *copilotUsersTwirp.GetCopilotUserRequest) (*copilotUsersTwirp.GetCopilotUserResponse, error) {
					return &copilotUsersTwirp.GetCopilotUserResponse{
						UserDetails: &copilotUsersTwirp.CopilotUserDetails{
							CopilotPlan: copilotUsersTwirp.CopilotPlan_COPILOT_PLAN_ENTERPRISE,
						},
					}, nil
				}

				h, executor := NewTestHandler(obsv, capiClient, ffClient, jobStore, ghTwirp, ghFactory)

				assignee := et.UserCopilot()
				issue := et.Issue(*assignee)
				ghFactory.MockIssueContext = &github.IssueContext{Repository: *et.Repo(), Issue: *issue}

				var err error
				if tt.featureFlagState {
					err = h.OnIssueAssigned(context.Background(), IssueAssignmentEvent{
						Actor:    EventActorWriter(),
						Repo:     EventRepository(),
						Issue:    EventIssue(),
						Action:   IssueAssignmentEventActionAssigned,
						Assignee: assignee.Login,
					})
				} else {
					err = h.OnIssueAssigned(context.Background(), IssueAssignmentEvent{
						Actor:     EventActorWriter(),
						Repo:      EventRepository(),
						Issue:     EventIssue(),
						Action:    IssueAssignmentEventActionAssigned,
						Assignees: []string{assignee.Login},
					})
				}

				require.NoError(t, err)
				require.Len(t, executor.QueuedJobs(), 1)

				// Ensure we've passed the correct parameters to the launcher
				require.Equal(t, et.ActorWriter().GetID(), executor.QueuedJobs()[0].ActorId)
			})
		}
	})

	t.Run("uses previous body content if updated after hydro event (race condition)", func(t *testing.T) {
		ffAlt := featureflags.NewNoopClient(map[string]bool{
			featureflags.CopilotSWEAgentIssueBodyEditCheck:  true,
			featureflags.CopilotSWEAgentOnlyWakeOnAtCopilot: true,
			featureflags.CopilotSWEAgentAqueductJobQueuing:  true,
		})

		capiClient, jobStore, ghTwirp := commonHandlerDependencies()
		ghTwirp.NoopUserDetailAPI.MockGetCopilotUser = func(ctx context.Context, req *copilotUsersTwirp.GetCopilotUserRequest) (*copilotUsersTwirp.GetCopilotUserResponse, error) {
			return &copilotUsersTwirp.GetCopilotUserResponse{
				UserDetails: &copilotUsersTwirp.CopilotUserDetails{
					CopilotPlan: copilotUsersTwirp.CopilotPlan_COPILOT_PLAN_ENTERPRISE,
				},
			}, nil
		}
		h, executor := NewTestHandler(obsv, capiClient, ffAlt, jobStore, ghTwirp, ghFactory)

		assignee := et.UserCopilot()
		issue := et.Issue(*assignee)
		lastEditAt := time.Now()
		issue.LastEditedAt = &lastEditAt
		ghFactory.MockIssueContext = &github.IssueContext{Repository: *et.Repo(), Issue: *issue}
		ghFactory.MockIssueUserContentEdits = []github.UserContentEdit{
			{
				EditedAt: time.Now().Add(-time.Minute * 2), // Simulate an edit before the event timestamp
				Diff:     "~~Initial body content~~",
			},
			{
				EditedAt: *issue.LastEditedAt,
				Diff:     "~~Updated body content~~",
			},
		}

		err := h.OnIssueAssigned(context.Background(), IssueAssignmentEvent{
			Actor:     EventActorWriter(),
			Repo:      EventRepository(),
			Issue:     EventIssue(),
			Action:    IssueAssignmentEventActionAssigned,
			Assignees: []string{assignee.Login},
			Timestamp: time.Now().Add(-time.Minute), // Simulate a race condition
		})

		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 1)

		// Ensure we've passed the correct parameters to the launcher
		require.Equal(t, et.ActorWriter().GetID(), executor.QueuedJobs()[0].ActorId)
	})

	t.Run("handles issues with no body", func(t *testing.T) {
		capiClient, jobStore, ghTwirp := commonHandlerDependencies()
		ghTwirp.NoopUserDetailAPI.MockGetCopilotUser = func(ctx context.Context, req *copilotUsersTwirp.GetCopilotUserRequest) (*copilotUsersTwirp.GetCopilotUserResponse, error) {
			return &copilotUsersTwirp.GetCopilotUserResponse{
				UserDetails: &copilotUsersTwirp.CopilotUserDetails{
					CopilotPlan: copilotUsersTwirp.CopilotPlan_COPILOT_PLAN_ENTERPRISE,
				},
			}, nil
		}
		h, executor := NewTestHandler(obsv, capiClient, ffClient, jobStore, ghTwirp, ghFactory)

		assignee := et.UserCopilot()
		issue := et.Issue(*assignee)
		// set the issue body to empty
		issue.Body = ""
		ghFactory.MockIssueContext = &github.IssueContext{Repository: *et.Repo(), Issue: *issue}

		err := h.OnIssueAssigned(context.Background(), IssueAssignmentEvent{
			Actor:     EventActorWriter(),
			Repo:      EventRepository(),
			Issue:     EventIssue(),
			Action:    IssueAssignmentEventActionAssigned,
			Assignees: []string{assignee.Login},
		})

		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 1)
	})

	t.Run("handles issues in uninitialized repos", func(t *testing.T) {
		assignee := et.UserCopilot()
		repo := et.Repo()
		repo.DefaultBranchRef = nil
		gh := &github.NoopClient{
			MockRepositoryContext: &github.RepositoryContext{
				Repository: *repo,
			},
			GithubAppLogin: et.GitHubAppLogin,
			GithubBotLogin: et.GitHubBotLogin,
		}
		ghFactory := &github.NoopClientFactory{
			GitHubAppLogin:   et.GitHubAppLogin,
			GitHubBotLogin:   et.GitHubBotLogin,
			MockIssueContext: &github.IssueContext{Repository: *repo, Issue: *et.Issue(*assignee)},
			MockNewClientFromRepo: func(ctx context.Context, repoId, repoOwnerId int64) (github.ClientInterface, error) {
				return gh, nil
			},
		}

		h, executor := NewDefaultTestHandler(obsv, ffClient, ghFactory)

		err := h.OnIssueAssigned(context.Background(), IssueAssignmentEvent{
			Actor:     EventActorWriter(),
			Repo:      EventRepository(),
			Issue:     EventIssue(),
			Action:    IssueAssignmentEventActionAssigned,
			Assignees: []string{assignee.Login},
		})
		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 0)

		require.Len(t, gh.MethodCalls, 1)
		var createCommentCall github.MethodCall
		for _, call := range gh.MethodCalls {
			if call.Method == "CreateComment" {
				createCommentCall = call
				break
			}
		}
		require.NotEmpty(t, createCommentCall)
		require.Len(t, createCommentCall.Args, 3)
		commentBody := createCommentCall.Args[2].(string)
		require.Contains(t, commentBody, "This repository doesn't have any commits yet")
	})

	t.Run("ignores events where the assigner doesn't have repo write access", func(t *testing.T) {
		h, executor := NewDefaultTestHandler(obsv, ffClient, ghFactory)

		assignee := et.UserCopilot()
		issue := et.Issue(*assignee)
		ghFactory.MockIssueContext = &github.IssueContext{Repository: *et.Repo(), Issue: *issue}

		err := h.OnIssueAssigned(context.Background(), IssueAssignmentEvent{
			Actor:     EventActorReader(), // Reader doesn't have write access
			Repo:      EventRepository(),
			Issue:     EventIssue(),
			Action:    IssueAssignmentEventActionAssigned,
			Assignees: []string{assignee.Login},
		})

		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 0)
	})

	t.Run("handles rules violation", func(t *testing.T) {
		tests := []struct {
			name               string
			mockedClient       *github.NoopClient
			expectedComment    string
			launchAgentCalls   int
			createCommentCalls int
		}{
			{
				name: "blocks branch creation",
				mockedClient: &github.NoopClient{
					MockListBranchProtectionRules: func(ctx context.Context, repoNodeID string) (*[]github.GQLBranchProtectionRule, error) {
						return &[]github.GQLBranchProtectionRule{{
							Pattern:         fmt.Sprintf("%s/*", github.CopilotBranchPrefix),
							BlocksCreations: true,
						}}, nil
					},
				},
				expectedComment:    fmt.Sprintf("%s due to branch protection rules", github.RuleBlockCreation),
				createCommentCalls: 1,
			},
			{
				name: "requires pull requests",
				mockedClient: &github.NoopClient{
					MockListBranchProtectionRules: func(ctx context.Context, repoNodeID string) (*[]github.GQLBranchProtectionRule, error) {
						return &[]github.GQLBranchProtectionRule{{
							Pattern:                  fmt.Sprintf("%s/*", github.CopilotBranchPrefix),
							RequiresApprovingReviews: true,
						}}, nil
					},
				},
				expectedComment:    fmt.Sprintf("%s due to branch protection rules", github.RuleRequirePullRequest),
				createCommentCalls: 1,
			},
			{
				name: "requires signing commits",
				mockedClient: &github.NoopClient{
					MockListBranchProtectionRules: func(ctx context.Context, repoNodeID string) (*[]github.GQLBranchProtectionRule, error) {
						return &[]github.GQLBranchProtectionRule{{
							Pattern:                  fmt.Sprintf("%s/*", github.CopilotBranchPrefix),
							RequiresCommitSignatures: true,
						}}, nil
					},
				},
				expectedComment:    fmt.Sprintf("%s due to branch protection rules", github.RuleRequireSignatures),
				createCommentCalls: 1,
			},
			{
				name: "blocks branch creation",
				mockedClient: &github.NoopClient{
					MockListRepositoryRulesForBranch: func(ctx context.Context, ownerLogin string, repoName string, branchName string) (*gg.BranchRules, error) {
						return &gg.BranchRules{
							Creation: []*gg.BranchRuleMetadata{{RulesetID: 1}},
						}, nil
					},
				},
				expectedComment:    fmt.Sprintf("%s due to repository rules", github.RuleBlockCreation),
				createCommentCalls: 1,
			},
			{
				name: "requires pull requests",
				mockedClient: &github.NoopClient{
					MockListRepositoryRulesForBranch: func(ctx context.Context, ownerLogin string, repoName string, branchName string) (*gg.BranchRules, error) {
						return &gg.BranchRules{
							PullRequest: []*gg.PullRequestBranchRule{{BranchRuleMetadata: gg.BranchRuleMetadata{RulesetID: 2}}},
						}, nil
					},
				},
				expectedComment:    fmt.Sprintf("%s due to repository rules", github.RuleRequirePullRequest),
				createCommentCalls: 1,
			},
			{
				name: "requires signing commits",
				mockedClient: &github.NoopClient{
					MockListRepositoryRulesForBranch: func(ctx context.Context, ownerLogin string, repoName string, branchName string) (*gg.BranchRules, error) {
						return &gg.BranchRules{
							RequiredSignatures: []*gg.BranchRuleMetadata{{RulesetID: 3}},
						}, nil
					},
				},
				expectedComment:    fmt.Sprintf("%s due to repository rules", github.RuleRequireSignatures),
				createCommentCalls: 1,
			},
			{
				name: "copilot branch name collision",
				mockedClient: &github.NoopClient{
					MockCheckIfBranchExists: func(ctx context.Context, ownerLogin string, repoName string, branchName string) (bool, error) {
						return true, nil // Simulate that the branch already exists
					},
				},
				expectedComment:    github.RuleCopilotBranchExists,
				createCommentCalls: 1,
			},
			{
				name: "github actions disabled",
				mockedClient: &github.NoopClient{
					MockGetActionsPermissions: func(ctx context.Context, ownerLogin, repoName string) (*gg.ActionsPermissionsRepository, error) {
						enabled := false
						return &gg.ActionsPermissionsRepository{Enabled: &enabled}, nil
					},
				},
				expectedComment:    github.RuleRequireGitHubActions,
				createCommentCalls: 1,
			},
			{
				name: "requires signing commits (noop with error)",
				mockedClient: &github.NoopClient{
					MockListBranchProtectionRules: func(ctx context.Context, repoNodeID string) (*[]github.GQLBranchProtectionRule, error) {
						return nil, fmt.Errorf("failed to get branch protection rules")
					},
				},
				expectedComment:  fmt.Sprintf("%s due to branch protection rules", github.RuleRequireSignatures),
				launchAgentCalls: 1,
			},
			{
				name: "blocks branch creation (noop with error)",
				mockedClient: &github.NoopClient{
					MockListRepositoryRulesForBranch: func(ctx context.Context, ownerLogin string, repoName string, branchName string) (*gg.BranchRules, error) {
						return nil, fmt.Errorf("failed to get repository rules for branch")
					},
				},
				expectedComment:  fmt.Sprintf("%s due to repository rules", github.RuleBlockCreation),
				launchAgentCalls: 1,
			},
			{
				name: "branch name rule (start with)",
				mockedClient: &github.NoopClient{
					MockListRepositoryRulesForBranch: func(ctx context.Context, ownerLogin string, repoName string, branchName string) (*gg.BranchRules, error) {
						return &gg.BranchRules{
							BranchNamePattern: []*gg.PatternBranchRule{
								{
									Parameters: gg.PatternRuleParameters{
										Pattern:  "users/",
										Operator: gg.PatternRuleOperatorStartsWith,
									},
								},
							},
						}, nil
					},
				},
				expectedComment:    fmt.Sprintf("%s (must start with ***) due to repository rules", github.RuleBranchNamePattern),
				createCommentCalls: 1,
			},
			{
				name: "branch name rule (contain negated)",
				mockedClient: &github.NoopClient{
					MockListRepositoryRulesForBranch: func(ctx context.Context, ownerLogin string, repoName string, branchName string) (*gg.BranchRules, error) {
						return &gg.BranchRules{
							BranchNamePattern: []*gg.PatternBranchRule{
								{
									Parameters: gg.PatternRuleParameters{
										Pattern:  "copilot",
										Operator: gg.PatternRuleOperatorContains,
										Negate:   gg.Ptr(true),
									},
								},
							},
						}, nil
					},
				},
				expectedComment:    fmt.Sprintf("%s (must not contain ***) due to repository rules", github.RuleBranchNamePattern),
				createCommentCalls: 1,
			},
			{
				name: "branch name rule (match regex)",
				mockedClient: &github.NoopClient{
					MockListRepositoryRulesForBranch: func(ctx context.Context, ownerLogin string, repoName string, branchName string) (*gg.BranchRules, error) {
						return &gg.BranchRules{
							BranchNamePattern: []*gg.PatternBranchRule{
								{
									Parameters: gg.PatternRuleParameters{
										Pattern:  "^(feature|fix)\\/(\\S+-\\d+)\\_(.+)$",
										Operator: gg.PatternRuleOperatorRegex,
									},
								},
							},
						}, nil
					},
				},
				expectedComment:    fmt.Sprintf("%s (must match regex ***) due to repository rules", github.RuleBranchNamePattern),
				createCommentCalls: 1,
			},
			{
				name: "copilot branch name collision (noop with error)",
				mockedClient: &github.NoopClient{
					MockCheckIfBranchExists: func(ctx context.Context, ownerLogin string, repoName string, branchName string) (bool, error) {
						return false, fmt.Errorf("branch collision check failed")
					},
				},
				launchAgentCalls: 1,
			},
			{
				name: "github actions disabled (noop with error)",
				mockedClient: &github.NoopClient{
					MockGetActionsPermissions: func(ctx context.Context, ownerLogin, repoName string) (*gg.ActionsPermissionsRepository, error) {
						return nil, fmt.Errorf("actions permissions not found")
					},
				},
				launchAgentCalls: 1,
			},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				capiClient, jobStore, ghTwirp := commonHandlerDependencies()
				assignee := et.UserCopilot()
				issue := et.Issue(*assignee)
				gh := tt.mockedClient
				gh.MockIssueContext = &github.IssueContext{Repository: *et.Repo(), Issue: *issue}
				cf := &github.NoopClientFactory{
					MockNewClient: func(token string) (github.ClientInterface, error) {
						return gh, nil
					},
				}
				ghTwirp.NoopUserDetailAPI.MockGetCopilotUser = func(ctx context.Context, req *copilotUsersTwirp.GetCopilotUserRequest) (*copilotUsersTwirp.GetCopilotUserResponse, error) {
					return &copilotUsersTwirp.GetCopilotUserResponse{
						UserDetails: &copilotUsersTwirp.CopilotUserDetails{
							CopilotPlan: copilotUsersTwirp.CopilotPlan_COPILOT_PLAN_ENTERPRISE,
						},
					}, nil
				}
				h, executor := NewTestHandler(obsv, capiClient, ffClient, jobStore, ghTwirp, cf)

				err := h.OnIssueAssigned(context.Background(), IssueAssignmentEvent{
					Actor:     EventActorWriter(),
					Repo:      EventRepository(),
					Issue:     EventIssue(),
					Action:    IssueAssignmentEventActionAssigned,
					Assignees: []string{assignee.Login},
				})

				require.NoError(t, err)
				require.Len(t, executor.QueuedJobs(), tt.launchAgentCalls)

				if tt.createCommentCalls == 0 {
					require.Len(t, gh.MethodCalls, 0)
					return
				}

				require.Len(t, gh.MethodCalls, tt.createCommentCalls)
				var createCommentCall github.MethodCall
				for _, call := range gh.MethodCalls {
					if call.Method == "CreateComment" {
						createCommentCall = call
						break
					}
				}
				require.NotEmpty(t, createCommentCall)
				require.Len(t, createCommentCall.Args, 3)
				commentBody := createCommentCall.Args[2].(string)
				require.Contains(t, commentBody, tt.expectedComment)
			})
		}
	})
}
