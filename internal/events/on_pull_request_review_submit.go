package events

import (
	"context"
	"errors"
	"fmt"

	"github.com/github/github-telemetry-go/kvp"
	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/jobutils"
	"github.com/github/sweagentd/internal/problemstatement"
	"github.com/github/sweagentd/internal/requestctx"
)

func (h *Handler) OnPullRequestReviewSubmit(ctx context.Context, event PullRequestReviewSubmitEvent) error {
	assignment, err := h.assignmentForPR(ctx, event.Repo.OwnerID, event.Repo.ID, event.PullRequest.ID, &event.PullRequest.Number)
	if err != nil {
		return fmt.Errorf("failed to get assignment for pull request: %w", err)
	}
	if assignment == nil {
		return nil
	}

	gh, err := h.ghFactory.NewClientFromRepo(ctx, event.Repo.ID, event.Repo.OwnerID)
	if err != nil {
		return fmt.Errorf("failed to create GitHub client: %w", err)
	}

	ignore, err := h.ShouldIgnorePRCommentOrReview(ctx, gh, event.Repo, event.PullRequest, event.Actor)
	if err != nil {
		return fmt.Errorf("failed to check if comment should be ignored: %w", err)
	}
	if ignore {
		return nil
	}

	actx, err := gh.GetPullRequestContext(ctx, event.Repo.NodeID, event.PullRequest.Number)
	if err != nil {
		return fmt.Errorf("failed to get pull request context: %w", err)
	}

	err = h.onPullRequestReviewSubmit(ctx, gh, assignment, actx, event)
	if err != nil {
		return jobutils.NewUnhandledErrorWithPRComment(ctx, err, assignment, false, event.Actor.Login)
	}
	return nil
}

func (h *Handler) onPullRequestReviewSubmit(ctx context.Context, gh github.ClientInterface, assignment *jobs.Assignment, actx *github.PullRequestContext, event PullRequestReviewSubmitEvent) error {
	ctx, span := h.obsv.Tracer.Start(ctx, "events.Handler.onPullRequestReviewSubmit")
	defer span.End()

	pr := actx.PullRequest

	logger := h.obsv.LoggerWithTelemetry(ctx)

	logger.Info("processing pull request review")

	review := pr.ReviewByID(event.PullRequestReview.ID)
	if review == nil {
		return fmt.Errorf("failed to get pull request review")
	}

	// Double check that the review author matches the event actor
	if review.Author.GetID() != event.Actor.ID {
		return fmt.Errorf("review author does not match event actor")
	}

	// Ignore events for closed PRs or PRs not authored by Copilot
	if pr.IsClosed() || !pr.Author.IsCopilot(gh) {
		logger.Info("Ignoring review because pull request is closed or not authored by Copilot")
		return nil
	}

	// Ignore reviews for PRs not assigned to Copilot
	if !pr.IsCopilotAssigned(gh) {
		logger.Info("Ignoring review because pull request is not assigned to Copilot")
		return nil
	}

	// Ignore reviews authored by Copilot or other bots
	if review.Author.IsCopilot(gh) || review.Author.IsBot() {
		logger.Info("Ignoring review because it was authored by Copilot or a bot")
		return nil
	}

	// Only process events where the actor has write access to the repo
	if !actx.PermissionsFor(review.Author).Push {
		logger.Info("Ignoring review because actor does not have write access to the repo")
		return nil
	}

	// Skip if the review is an approval with no comments.
	if review.Approved() && review.Comments.Len() == 0 {
		logger.Info("Ignoring review because it is an approval with no comments")
		return nil
	}

	// ✅ All validation/permission checks passed, now we start doing things

	allJobs, pendingJob, err := jobutils.JobsForAssignment(ctx, h.obsv, h.ffClient, gh, h.capiClient, h.ghTwirp, h.jobsStore, assignment)
	if err != nil {
		return fmt.Errorf("failed to get jobs for assignment: %w", err)
	}

	if pendingJob != nil {
		// Make sure we set the actor to the user who left the latest review.
		pendingJob.ActorID = review.Author.GetID()
		pendingJob.ActorLogin = review.Author.GetLogin()
		jobutils.UpdateRequestContextJob(ctx, h.obsv, pendingJob)
		h.obsv.LoggerWithTelemetry(ctx).Info("Found existing pending job for pull request")
	} else {
		authorID := review.Author.GetID()
		trackingID := ""
		// check if CopilotSweAgentTrackingIDLookupFlagName is enabled
		// please only call this if you have a ffclient set (like you passed it in NewAuthenticator)
		if h.ffClient.IsEnabledForUser(ctx, featureflags.CopilotSweAgentTrackingIDLookupFlagName, authorID) {
			trackingID = review.Author.GetTrackingID(h.ghTwirp)
		}
		pendingJob = assignment.NewJob(jobs.AgentActionFixPRComment, &requestctx.UserInfo{
			ID:         uint64(review.Author.GetID()),
			Login:      review.Author.GetLogin(),
			TrackingID: trackingID,
		}, pr.HeadRef.Name)
		pendingJob.BranchName = pr.HeadRef.Name
		jobutils.UpdateRequestContextJob(ctx, h.obsv, pendingJob)
		h.obsv.LoggerWithTelemetry(ctx).Info("Created new pending job for pull request")
		pendingJob.Model = jobutils.DetermineModel(ctx, h.obsv, h.ffClient, h.ghTwirp, pendingJob, pr.Labels.Nodes)
		jobutils.UpdateRequestContextJob(ctx, h.obsv, pendingJob) // again for model
	}

	logger = h.obsv.LoggerWithTelemetry(ctx)

	pendingJob.ReviewIDs = append(pendingJob.ReviewIDs, review.GetID())

	// Add all other (qualifying) comments and reviews that haven't been included on a job yet.
	jobutils.AddUnprocessedCommentsAndReviewsToJob(gh, actx, allJobs, pendingJob)

	problemStatement, err := problemstatement.NewPullRequestBuilder(h.obsv, gh, actx, pendingJob).Build(ctx)
	if err != nil {
		return fmt.Errorf("failed to create problem statement: %w", err)
	}
	pendingJob.ProblemStatement = problemStatement

	// Always wake if there are changes requested or if the review mentions Copilot, but if "only wake on @copilot" is disabled, apply different rules.
	onlyWakeOnAtCopilot := h.ffClient.IsEnabledForUser(ctx, featureflags.CopilotSWEAgentOnlyWakeOnAtCopilot, event.Actor.ID)
	mentionsCopilot := reviewDoesMentionCopilot(review)
	if onlyWakeOnAtCopilot {
		if !mentionsCopilot {
			logger.Info("Ignoring review because it does not mention Copilot and \"only wake on @copilot\" is enabled")
			return nil
		}
	} else {
		if !review.ChangesRequested() || !mentionsCopilot {
			// Validate that Copilot thinks there is work being asked of it here
			shouldWake, err := h.jobExecutor.ShouldWake(ctx, event.Actor.ID, problemStatement.Content, pendingJob.ID)
			if err != nil {
				return fmt.Errorf("failed to check if Copilot should wake: %w", err)
			}
			if !shouldWake {
				logger.Info("Ignoring because Copilot does not think new feedback is actionable",
					kvp.String("wake_up_problem_statement", problemStatement.Content))
				return nil
			}
		}
	}

	logger.Info("Waking up Copilot because new feedback is actionable")

	// Add a 👀 reaction to acknowledge the review
	jobutils.AddReactionsForReview(ctx, h.obsv, gh, review)

	pendingJob.EventURL = event.EventUrl
	pendingJob.EventType = jobs.EventTypePullRequestReview
	pendingJob.EventIdentifiers = []string{fmt.Sprintf("%d", review.GetID())}

	pendingJob, err = h.jobsStore.UpsertJob(ctx, pendingJob)
	if err != nil {
		if errors.Is(err, jobs.ErrPreconditionFailedOnUpdate) {
			// This is almost certainly caused by a race condition where more than
			// one event handler grabbed the same pending job. This should be very
			// rare, but we have seen it in the wild, so we handle it gracefully.
			logger.WithError(err).Error("Failed to upsert job due to another process updating during processing")
			return nil
		}
		return fmt.Errorf("failed to upsert job: %w", err)
	}

	// Reload the logger with any job fields that may have changed
	jobutils.UpdateRequestContextJob(ctx, h.obsv, pendingJob)
	logger = h.obsv.LoggerWithTelemetry(ctx)

	if has, runningJobID := jobs.HasRunningJobs(allJobs); has {
		logger.Info("Job queued but not launching agent because there is already a running job",
			kvp.String("running_job_id", runningJobID))
		return nil
	} else {
		logger.Info("Job upserted for pull request")
	}

	logger.Info("Queuing new job for pull request")

	if err := h.jobExecutor.QueueJob(ctx, gh, pendingJob); err != nil {
		return fmt.Errorf("failed to queue job: %w", err)
	}

	return nil
}

func reviewDoesMentionCopilot(review *github.GQLPullRequestReview) bool {
	if doesMentionCopilot(review.GetBody()) {
		return true
	}
	for _, comment := range review.Comments.Nodes {
		if doesMentionCopilot(comment.GetBody()) {
			return true
		}
	}
	return false
}
