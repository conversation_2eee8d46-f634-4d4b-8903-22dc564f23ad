package events

import (
	"context"
	"fmt"
	"testing"

	copilotUsersTwirp "github.com/github/copilot-twirp/proto/users/v1"
	"github.com/github/hydro-client-go/v7/pkg/hydro"
	sweagentv0 "github.com/github/hydro-schemas-go/hydro/schemas/sweagentd/v0"
	sweagentEntitiesv0 "github.com/github/hydro-schemas-go/hydro/schemas/sweagentd/v0/entities"
	"github.com/github/sweagentd/internal/capi"
	et "github.com/github/sweagentd/internal/events/eventstest"
	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/githubtwirp"
	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/observability"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/encoding/protojson"
)

func TestOnPullRequestReviewSubmit(t *testing.T) {
	obsv := observability.NewNoopExporters()

	ghFactory := &github.NoopClientFactory{
		GitHubAppLogin: et.GitHubAppLogin,
		GitHubBotLogin: et.GitHubBotLogin,
	}
	ffClient := featureflags.NewNoopClient(map[string]bool{
		featureflags.CopilotSWEAgentOnlyWakeOnAtCopilot: true,
		featureflags.CopilotSWEAgentAqueductJobQueuing:  true,
	})

	t.Run("queues the agent job", func(t *testing.T) {
		tests := []struct {
			name           string
			queuingEnabled bool
		}{
			{
				name:           "queues job when queuing is enabled",
				queuingEnabled: true,
			},
			{
				name:           "executes job when queuing is disabled",
				queuingEnabled: false,
			},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				ffc := featureflags.NewNoopClient(map[string]bool{
					featureflags.CopilotSWEAgentAqueductJobQueuing: tt.queuingEnabled,
				})
				capiClient, jobStore, ghTwirp := commonHandlerDependencies()
				ghTwirp.NoopUserDetailAPI.MockGetCopilotUser = func(ctx context.Context, req *copilotUsersTwirp.GetCopilotUserRequest) (*copilotUsersTwirp.GetCopilotUserResponse, error) {
					return &copilotUsersTwirp.GetCopilotUserResponse{
						UserDetails: &copilotUsersTwirp.CopilotUserDetails{
							CopilotPlan: copilotUsersTwirp.CopilotPlan_COPILOT_PLAN_ENTERPRISE,
						},
					}, nil
				}
				h, executor := NewTestHandler(obsv, capiClient, ffc, jobStore, ghTwirp, ghFactory)
				h.jobsStore.CreateAssignment(context.Background(), et.Assignment())

				actor := et.ActorWriter()
				pr := et.PullRequest(*et.UserCopilot())
				pr = et.AddReviewToPullRequest(pr, true, *actor, *actor)

				ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

				ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

				err := h.OnPullRequestReviewSubmit(context.Background(), PullRequestReviewSubmitEvent{
					Actor:             EventActor(actor),
					Repo:              EventRepository(),
					PullRequest:       EventPullRequest(),
					PullRequestReview: EventPullRequestReview(actor),
				})
				require.NoError(t, err)

				if tt.queuingEnabled {
					require.Len(t, executor.QueuedJobs(), 1)
					require.Len(t, executor.LaunchAgentCalls(), 0)

					require.Equal(t, et.ActorWriter().GetID(), executor.QueuedJobs()[0].ActorId)
				} else {
					require.Len(t, executor.QueuedJobs(), 0)
					require.Len(t, executor.LaunchAgentCalls(), 1)

					require.Equal(t, et.ActorWriter().GetID(), executor.LaunchAgentCalls()[0].ActorID)
				}
			})
		}
	})

	t.Run("ignores events on PRs that are closed", func(t *testing.T) {
		h, executor := NewDefaultTestHandler(obsv, ffClient, ghFactory)
		h.jobsStore.CreateAssignment(context.Background(), et.Assignment())
		h.jobsStore.CreateJob(context.Background(), et.Job(jobs.JobStatusCompleted))

		author := et.ActorWriter()
		pr := et.PullRequest(*et.UserCopilot())
		pr = et.AddReviewToPullRequest(pr, true, *author, *author)
		pr.Closed = true

		ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

		err := h.OnPullRequestReviewSubmit(context.Background(), PullRequestReviewSubmitEvent{
			Actor:             EventActor(author),
			Repo:              EventRepository(),
			PullRequest:       EventPullRequest(),
			PullRequestReview: EventPullRequestReview(author),
		})

		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 0)
	})

	t.Run("ignores events on PRs not authored by Copilot", func(t *testing.T) {
		h, executor := NewDefaultTestHandler(obsv, ffClient, ghFactory)
		h.jobsStore.CreateAssignment(context.Background(), et.Assignment())
		h.jobsStore.CreateJob(context.Background(), et.Job(jobs.JobStatusCompleted))

		author := et.ActorWriter()
		pr := et.PullRequestWithAuthor(author, *et.UserCopilot())
		pr = et.AddReviewToPullRequest(pr, true, *author, *author)

		ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

		err := h.OnPullRequestReviewSubmit(context.Background(), PullRequestReviewSubmitEvent{
			Actor:             EventActor(author),
			Repo:              EventRepository(),
			PullRequest:       EventPullRequest(),
			PullRequestReview: EventPullRequestReview(author),
		})

		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 0)
	})

	t.Run("ignores events on PRs assigned to other users and not Copilot", func(t *testing.T) {
		h, executor := NewDefaultTestHandler(obsv, ffClient, ghFactory)
		h.jobsStore.CreateAssignment(context.Background(), et.Assignment())
		h.jobsStore.CreateJob(context.Background(), et.Job(jobs.JobStatusCompleted))

		actor := et.ActorWriter()
		pr := et.PullRequest(*et.UserReader())
		pr = et.AddReviewToPullRequest(pr, true, *actor, *actor)
		et.AddAssigneesToPullRequest(pr, *et.UserWriter())

		ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

		err := h.OnPullRequestReviewSubmit(context.Background(), PullRequestReviewSubmitEvent{
			Actor:             EventActor(actor),
			Repo:              EventRepository(),
			PullRequest:       EventPullRequest(),
			PullRequestReview: EventPullRequestReview(actor),
		})

		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 0)
	})

	t.Run("handles wake logic properly", func(t *testing.T) {
		ghTwirpAlt := githubtwirp.NewNoopClient()
		ghTwirpAlt.NoopUserDetailAPI.MockGetCopilotUser = func(ctx context.Context, req *copilotUsersTwirp.GetCopilotUserRequest) (*copilotUsersTwirp.GetCopilotUserResponse, error) {
			return &copilotUsersTwirp.GetCopilotUserResponse{
				UserDetails: &copilotUsersTwirp.CopilotUserDetails{
					CopilotPlan: copilotUsersTwirp.CopilotPlan_COPILOT_PLAN_ENTERPRISE,
				},
			}, nil
		}
		type scenario struct {
			bodyHasAtCopilot      bool
			changesRequested      bool
			onlyWakeOnAtCopilotFF bool
			shouldWakeResult      bool
			launched              bool
		}
		scenarios := []scenario{
			// If the onlyWakeOnAtCopilot feature flag is enabled, we should always launch if @copilot is mentioned
			{bodyHasAtCopilot: true, changesRequested: true, onlyWakeOnAtCopilotFF: true, shouldWakeResult: true, launched: true},
			{bodyHasAtCopilot: true, changesRequested: true, onlyWakeOnAtCopilotFF: true, shouldWakeResult: false, launched: true},
			{bodyHasAtCopilot: true, changesRequested: false, onlyWakeOnAtCopilotFF: true, shouldWakeResult: true, launched: true},
			{bodyHasAtCopilot: true, changesRequested: false, onlyWakeOnAtCopilotFF: true, shouldWakeResult: false, launched: true},
			// If changes are requested and onlyWakeOnCopilotFF flag is disabled, we should launch
			{bodyHasAtCopilot: true, changesRequested: true, onlyWakeOnAtCopilotFF: false, shouldWakeResult: true, launched: true},
			{bodyHasAtCopilot: true, changesRequested: true, onlyWakeOnAtCopilotFF: false, shouldWakeResult: false, launched: true},
			// If changes are NOT requested and onlyWakeOnCopilotFF flag is disabled, we should launch on should wake
			{bodyHasAtCopilot: true, changesRequested: false, onlyWakeOnAtCopilotFF: false, shouldWakeResult: true, launched: true},
			{bodyHasAtCopilot: true, changesRequested: false, onlyWakeOnAtCopilotFF: false, shouldWakeResult: false, launched: false},
			// If no @copilot, and onlyWakeOnCopilotFF flag is enabled, we should not launch
			{bodyHasAtCopilot: false, changesRequested: true, onlyWakeOnAtCopilotFF: true, shouldWakeResult: true, launched: false},
			{bodyHasAtCopilot: false, changesRequested: true, onlyWakeOnAtCopilotFF: true, shouldWakeResult: false, launched: false},
			{bodyHasAtCopilot: false, changesRequested: false, onlyWakeOnAtCopilotFF: true, shouldWakeResult: true, launched: false},
			{bodyHasAtCopilot: false, changesRequested: false, onlyWakeOnAtCopilotFF: true, shouldWakeResult: false, launched: false},
			// If no @copilot, and onlyWakeOnCopilotFF flag is disabled, we should only launch on shouldWake
			{bodyHasAtCopilot: false, changesRequested: true, onlyWakeOnAtCopilotFF: false, shouldWakeResult: true, launched: true},
			{bodyHasAtCopilot: false, changesRequested: true, onlyWakeOnAtCopilotFF: false, shouldWakeResult: false, launched: false},
			{bodyHasAtCopilot: false, changesRequested: false, onlyWakeOnAtCopilotFF: false, shouldWakeResult: true, launched: true},
			{bodyHasAtCopilot: false, changesRequested: false, onlyWakeOnAtCopilotFF: false, shouldWakeResult: false, launched: false},
		}
		for _, s := range scenarios {
			skipsText := "does not launch"
			if s.launched {
				skipsText = "launches"
			}
			atCopilotText := "with @copilot"
			if !s.bodyHasAtCopilot {
				atCopilotText = "without @copilot"
			}
			t.Run(fmt.Sprintf("%s %s, changesRequested: %v, onlyWakeOnAtCopilot: %v, and shouldWake: %v ", skipsText, atCopilotText, s.changesRequested, s.onlyWakeOnAtCopilotFF, s.shouldWakeResult), func(t *testing.T) {
				ffAlt := featureflags.NewNoopClient(map[string]bool{
					featureflags.CopilotSWEAgentOnlyWakeOnAtCopilot: s.onlyWakeOnAtCopilotFF,
					featureflags.CopilotSWEAgentAqueductJobQueuing:  true,
				})
				jobStore := jobs.NewMemoryStore()
				capiClient := capi.NewNoopClientWithFixedShouldWakeResponse(s.shouldWakeResult)
				h, executor := NewTestHandler(obsv, capiClient, ffAlt, jobStore, ghTwirpAlt, ghFactory)
				ctx := context.Background()
				_, err := h.jobsStore.CreateAssignment(ctx, et.Assignment())
				require.NoError(t, err)
				actor := et.ActorWriter()
				pr := et.PullRequest(*et.UserCopilot())
				pr = et.AddReviewToPullRequest(pr, s.bodyHasAtCopilot, *actor, *actor)
				if s.changesRequested {
					pr.Reviews.Nodes[0].State = github.GQLPullRequestReviewStateChangesRequested
				} else {
					pr.Reviews.Nodes[0].State = github.GQLPullRequestReviewStateApproved
				}

				ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

				pullRequestReview := EventPullRequestReview(actor)
				repo := EventRepository()
				evt := PullRequestReviewSubmitEvent{
					Actor:             EventActor(actor),
					Repo:              repo,
					PullRequest:       EventPullRequest(),
					PullRequestReview: pullRequestReview,
				}
				err = h.OnPullRequestReviewSubmit(ctx, evt)

				require.NoError(t, err)
				if s.launched {
					require.Len(t, executor.QueuedJobs(), 1)
				} else {
					require.Len(t, executor.QueuedJobs(), 0)
				}
			})
		}
	})

	t.Run("ignores reviews where the commenter doesn't have repo write access", func(t *testing.T) {
		h, executor := NewDefaultTestHandler(obsv, ffClient, ghFactory)
		h.jobsStore.CreateAssignment(context.Background(), et.Assignment())
		h.jobsStore.CreateJob(context.Background(), et.Job(jobs.JobStatusCompleted))

		reader := et.ActorReader()
		pr := et.PullRequest(*et.UserCopilot())
		pr = et.AddReviewToPullRequest(pr, true, *reader, *reader)

		ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

		err := h.OnPullRequestReviewSubmit(context.Background(), PullRequestReviewSubmitEvent{
			Actor:             EventActor(reader),
			Repo:              EventRepository(),
			PullRequest:       EventPullRequest(),
			PullRequestReview: EventPullRequestReview(reader),
		})

		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 0)
	})

	t.Run("ignores reviews created by bots", func(t *testing.T) {
		h, executor := NewDefaultTestHandler(obsv, ffClient, ghFactory)
		h.jobsStore.CreateAssignment(context.Background(), et.Assignment())
		h.jobsStore.CreateJob(context.Background(), et.Job(jobs.JobStatusCompleted))

		bot := et.ActorBot()
		pr := et.PullRequest(*et.UserCopilot())
		pr = et.AddReviewToPullRequest(pr, true, *bot, *bot)

		ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

		err := h.OnPullRequestReviewSubmit(context.Background(), PullRequestReviewSubmitEvent{
			Actor:             EventActor(bot),
			Repo:              EventRepository(),
			PullRequest:       EventPullRequest(),
			PullRequestReview: EventPullRequestReview(bot),
		})

		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 0)
	})

	t.Run("skips launching the agent with pending and running jobs", func(t *testing.T) {
		h, executor := NewDefaultTestHandler(obsv, ffClient, ghFactory)
		h.jobsStore.CreateAssignment(context.Background(), et.Assignment())
		h.jobsStore.CreateJob(context.Background(), et.Job(jobs.JobStatusRunning))
		h.jobsStore.CreateJob(context.Background(), et.Job(jobs.JobStatusPending))

		actor := et.ActorWriter()
		pr := et.PullRequest(*et.UserCopilot())
		pr = et.AddReviewToPullRequest(pr, true, *actor, *actor)

		ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

		err := h.OnPullRequestReviewSubmit(context.Background(), PullRequestReviewSubmitEvent{
			Actor:             EventActor(actor),
			Repo:              EventRepository(),
			PullRequest:       EventPullRequest(),
			PullRequestReview: EventPullRequestReview(actor),
		})

		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 0)
	})

	t.Run("launches the agent with pending and completed jobs", func(t *testing.T) {
		h, executor := NewDefaultTestHandler(obsv, ffClient, ghFactory)
		h.jobsStore.CreateAssignment(context.Background(), et.Assignment())
		h.jobsStore.CreateJob(context.Background(), et.Job(jobs.JobStatusCompleted))
		h.jobsStore.CreateJob(context.Background(), et.Job(jobs.JobStatusPending))

		actor := et.ActorWriter()
		pr := et.PullRequest(*et.UserCopilot())
		pr = et.AddReviewToPullRequest(pr, true, *actor, *actor)

		ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

		err := h.OnPullRequestReviewSubmit(context.Background(), PullRequestReviewSubmitEvent{
			Actor:             EventActor(actor),
			Repo:              EventRepository(),
			PullRequest:       EventPullRequest(),
			PullRequestReview: EventPullRequestReview(actor),
		})

		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 1)
	})

	t.Run("published job_started telemetry event", func(t *testing.T) {
		messageChan := make(chan hydro.Message, 1)
		sink, err := hydro.NewMemorySink(messageChan)
		require.NoError(t, err)
		publisher, _ := hydro.NewPublisher(sink, hydro.WithEncoder(hydro.NewJSONEncoder()))
		obsvWithPublisher := obsv.WithPublisher(publisher)
		capiClient, jobStore, ghTwirp := commonHandlerDependencies()

		ffClient := featureflags.NewNoopClient(map[string]bool{
			"sweagentd_publish_telemetry_events_to_hydro":   true,
			featureflags.CopilotSWEAgentOnlyWakeOnAtCopilot: true,
			featureflags.CopilotSWEAgentAqueductJobQueuing:  false, // Disable queuing for this test
		})
		ghTwirp.NoopUserDetailAPI.MockGetCopilotUser = func(ctx context.Context, req *copilotUsersTwirp.GetCopilotUserRequest) (*copilotUsersTwirp.GetCopilotUserResponse, error) {
			return &copilotUsersTwirp.GetCopilotUserResponse{
				UserDetails: &copilotUsersTwirp.CopilotUserDetails{
					CopilotPlan: copilotUsersTwirp.CopilotPlan_COPILOT_PLAN_ENTERPRISE,
				},
			}, nil
		}
		h, executor := NewTestHandler(obsvWithPublisher, capiClient, ffClient, jobStore, ghTwirp, ghFactory)
		assignment, err := h.jobsStore.CreateAssignment(context.Background(), et.Assignment())
		require.NoError(t, err)
		actor := et.ActorWriter()
		pr := et.PullRequest(*et.UserCopilot())
		pr = et.AddReviewToPullRequest(pr, true, *actor, *actor)

		ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

		pullRequestReview := EventPullRequestReview(actor)
		repo := EventRepository()
		err = h.OnPullRequestReviewSubmit(context.Background(), PullRequestReviewSubmitEvent{
			Actor:             EventActor(actor),
			Repo:              repo,
			PullRequest:       EventPullRequest(),
			PullRequestReview: pullRequestReview,
		})

		require.NoError(t, err)
		require.Len(t, executor.LaunchAgentCalls(), 1)

		select {
		case eventReceived := <-messageChan:
			var jobStartedEvent sweagentv0.JobStarted
			protojson.Unmarshal(eventReceived.Value, &jobStartedEvent)
			require.Equal(t, sweagentEntitiesv0.JobTrigger_PULL_REQUEST_REVIEW_SUBMIT, jobStartedEvent.Trigger.Type)
			require.Equal(t, pullRequestReview.ID, jobStartedEvent.Trigger.IdentifierId)
			require.Equal(t, sweagentEntitiesv0.ActionType_FIX_PR_COMMENT, jobStartedEvent.Action)
			require.Equal(t, repo.ID, jobStartedEvent.Job.RepositoryId)
			require.Equal(t, assignment.OwnerID, jobStartedEvent.Job.OwnerId)
			require.Equal(t, *pr.ID.Ptr(), jobStartedEvent.Job.PullRequestId)
		default:
			t.Fail()
		}
	})

	t.Run("launches the agent with no existing jobs", func(t *testing.T) {
		capiClient, jobStore, ghTwirp := commonHandlerDependencies()
		ghTwirp.NoopUserDetailAPI.MockGetCopilotUser = func(ctx context.Context, req *copilotUsersTwirp.GetCopilotUserRequest) (*copilotUsersTwirp.GetCopilotUserResponse, error) {
			return &copilotUsersTwirp.GetCopilotUserResponse{
				UserDetails: &copilotUsersTwirp.CopilotUserDetails{
					CopilotPlan: copilotUsersTwirp.CopilotPlan_COPILOT_PLAN_ENTERPRISE,
				},
			}, nil
		}
		h, executor := NewTestHandler(obsv, capiClient, ffClient, jobStore, ghTwirp, ghFactory)
		h.jobsStore.CreateAssignment(context.Background(), et.Assignment())

		actor := et.ActorWriter()
		pr := et.PullRequest(*et.UserCopilot())
		pr = et.AddReviewToPullRequest(pr, true, *actor, *actor)

		ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

		err := h.OnPullRequestReviewSubmit(context.Background(), PullRequestReviewSubmitEvent{
			Actor:             EventActor(actor),
			Repo:              EventRepository(),
			PullRequest:       EventPullRequest(),
			PullRequestReview: EventPullRequestReview(actor),
		})

		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 1)
	})

	t.Run("skips launching the agent with existing running job", func(t *testing.T) {
		capiClient, jobStore, ghTwirp := commonHandlerDependencies()
		ghTwirp.NoopUserDetailAPI.MockGetCopilotUser = func(ctx context.Context, req *copilotUsersTwirp.GetCopilotUserRequest) (*copilotUsersTwirp.GetCopilotUserResponse, error) {
			return &copilotUsersTwirp.GetCopilotUserResponse{
				UserDetails: &copilotUsersTwirp.CopilotUserDetails{
					CopilotPlan: copilotUsersTwirp.CopilotPlan_COPILOT_PLAN_ENTERPRISE,
				},
			}, nil
		}
		h, executor := NewTestHandler(obsv, capiClient, ffClient, jobStore, ghTwirp, ghFactory)
		h.jobsStore.CreateAssignment(context.Background(), et.Assignment())
		h.jobsStore.CreateJob(context.Background(), et.Job(jobs.JobStatusRunning))

		actor := et.ActorWriter()
		pr := et.PullRequest(*et.UserCopilot())
		pr = et.AddReviewToPullRequest(pr, true, *actor, *actor)

		ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

		err := h.OnPullRequestReviewSubmit(context.Background(), PullRequestReviewSubmitEvent{
			Actor:             EventActor(actor),
			Repo:              EventRepository(),
			PullRequest:       EventPullRequest(),
			PullRequestReview: EventPullRequestReview(actor),
		})

		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 0)
	})

	t.Run("launches the agent with existing terminal jobs", func(t *testing.T) {
		capiClient, jobStore, ghTwirp := commonHandlerDependencies()
		ghTwirp.NoopUserDetailAPI.MockGetCopilotUser = func(ctx context.Context, req *copilotUsersTwirp.GetCopilotUserRequest) (*copilotUsersTwirp.GetCopilotUserResponse, error) {
			return &copilotUsersTwirp.GetCopilotUserResponse{
				UserDetails: &copilotUsersTwirp.CopilotUserDetails{
					CopilotPlan: copilotUsersTwirp.CopilotPlan_COPILOT_PLAN_ENTERPRISE,
				},
			}, nil
		}
		h, executor := NewTestHandler(obsv, capiClient, ffClient, jobStore, ghTwirp, ghFactory)
		h.jobsStore.CreateAssignment(context.Background(), et.Assignment())
		h.jobsStore.CreateJob(context.Background(), et.Job(jobs.JobStatusCompleted))

		actor := et.ActorWriter()
		pr := et.PullRequest(*et.UserCopilot())
		pr = et.AddReviewToPullRequest(pr, true, *actor, *actor)

		ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

		err := h.OnPullRequestReviewSubmit(context.Background(), PullRequestReviewSubmitEvent{
			Actor:             EventActor(actor),
			Repo:              EventRepository(),
			PullRequest:       EventPullRequest(),
			PullRequestReview: EventPullRequestReview(actor),
		})

		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 1)
	})

	t.Run("skips launching if the review was an approval with no comments", func(t *testing.T) {
		h, executor := NewDefaultTestHandler(obsv, ffClient, ghFactory)
		h.jobsStore.CreateAssignment(context.Background(), et.Assignment())

		reviewer := et.ActorWriter()
		pr := et.PullRequest(*et.UserCopilot())
		pr = et.AddReviewToPullRequest(pr, true, *reviewer)

		ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

		err := h.OnPullRequestReviewSubmit(context.Background(), PullRequestReviewSubmitEvent{
			Actor:             EventActor(reviewer),
			Repo:              EventRepository(),
			PullRequest:       EventPullRequest(),
			PullRequestReview: EventPullRequestReview(reviewer),
		})

		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 0)
	})
}
