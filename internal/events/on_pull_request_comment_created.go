package events

import (
	"context"
	"errors"
	"fmt"

	"github.com/github/github-telemetry-go/kvp"
	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/jobutils"
	"github.com/github/sweagentd/internal/problemstatement"
	"github.com/github/sweagentd/internal/requestctx"
)

func (h *Handler) OnPullRequestCommentCreated(ctx context.Context, event PullRequestCommentCreatedEvent) error {
	assignment, err := h.assignmentForPR(ctx, event.Repo.OwnerID, event.Repo.ID, event.PullRequest.ID, &event.PullRequest.Number)
	if err != nil {
		return fmt.Errorf("failed to get assignment for pull request: %w", err)
	}
	if assignment == nil {
		return nil
	}

	gh, err := h.ghFactory.NewClientFromRepo(ctx, event.Repo.ID, event.Repo.OwnerID)
	if err != nil {
		return fmt.Errorf("failed to create GitHub client: %w", err)
	}

	ignore, err := h.ShouldIgnorePRCommentOrReview(ctx, gh, event.Repo, event.PullRequest, event.Actor)
	if err != nil {
		return fmt.Errorf("failed to check if comment should be ignored: %w", err)
	}
	if ignore {
		return nil
	}

	actx, err := gh.GetPullRequestContext(ctx, event.Repo.NodeID, event.PullRequest.Number)
	if err != nil {
		return fmt.Errorf("failed to get pull request context: %w", err)
	}

	h.setCtxPullRequestID(ctx, actx.PullRequest.GetID(), actx.PullRequest.Number)

	err = h.onPullRequestCommentCreated(ctx, gh, assignment, actx, event)
	if err != nil {
		return jobutils.NewUnhandledErrorWithPRComment(ctx, err, assignment, false, event.Actor.Login)
	}
	return nil
}

func (h *Handler) onPullRequestCommentCreated(ctx context.Context, gh github.ClientInterface, assignment *jobs.Assignment, actx *github.PullRequestContext, event PullRequestCommentCreatedEvent) error {
	ctx, span := h.obsv.Tracer.Start(ctx, "events.Handler.onPullRequestCommentCreated")
	defer span.End()

	pr := actx.PullRequest

	logger := h.obsv.LoggerWithTelemetry(ctx)

	logger.Info("processing pull request comment")

	comment := pr.CommentByID(event.Comment.ID)
	if comment == nil {
		return fmt.Errorf("failed to get pull request comment")
	}

	// Double check that the comment author matches the event actor
	if comment.Author.GetID() != event.Actor.ID {
		return fmt.Errorf("comment author does not match event actor")
	}

	// Ignore events for closed PRs or PRs not authored by Copilot
	if pr.IsClosed() || !pr.Author.IsCopilot(gh) {
		logger.Info("Ignoring comment because pull request is closed or not authored by Copilot")
		return nil
	}

	// Ignore comments for PRs not assigned to Copilot
	if !pr.IsCopilotAssigned(gh) {
		logger.Info("Ignoring comment because pull request is not assigned to Copilot")
		return nil
	}

	// Ignore comments authored by Copilot or other bots
	if comment.Author.IsCopilot(gh) || comment.Author.IsBot() {
		logger.Info("Ignoring comment because it was authored by Copilot or a bot")
		return nil
	}

	// Only process events where the actor has write access to the repo
	if !actx.PermissionsFor(comment.Author).Push {
		logger.Info("Ignoring comment because actor does not have write access to the repo")
		return nil
	}

	// ✅ All validation/permission checks passed, now we start doing things

	allJobs, pendingJob, err := jobutils.JobsForAssignment(ctx, h.obsv, h.ffClient, gh, h.capiClient, h.ghTwirp, h.jobsStore, assignment)
	if err != nil {
		return fmt.Errorf("failed to get jobs for assignment: %w", err)
	}

	if pendingJob != nil {
		// Make sure we set the actor to the user who left the latest actionable comment.
		// If we determine the comment is not actionable, this update will be discarded
		pendingJob.ActorID = comment.Author.GetID()
		pendingJob.ActorLogin = comment.Author.GetLogin()
		jobutils.UpdateRequestContextJob(ctx, h.obsv, pendingJob)
		h.obsv.LoggerWithTelemetry(ctx).Info("Found existing pending job for pull request")
	} else {
		authorID := comment.Author.GetID()
		trackingID := ""
		// check if CopilotSweAgentTrackingIDLookupFlagName is enabled
		// please only call this if you have a ffclient set (like you passed it in NewAuthenticator)
		if h.ffClient.IsEnabledForUser(ctx, featureflags.CopilotSweAgentTrackingIDLookupFlagName, authorID) {
			trackingID = comment.Author.GetTrackingID(h.ghTwirp)
		}
		// Prevent model customization for users who don't have the feature flag enabled
		pendingJob = assignment.NewJob(jobs.AgentActionFixPRComment, &requestctx.UserInfo{
			ID:         uint64(authorID),
			Login:      comment.Author.GetLogin(),
			TrackingID: trackingID,
		}, pr.HeadRef.Name)
		pendingJob.BranchName = pr.HeadRef.Name
		jobutils.UpdateRequestContextJob(ctx, h.obsv, pendingJob)
		h.obsv.LoggerWithTelemetry(ctx).Info("Created new pending job for pull request")
		pendingJob.Model = jobutils.DetermineModel(ctx, h.obsv, h.ffClient, h.ghTwirp, pendingJob, pr.Labels.Nodes)
		jobutils.UpdateRequestContextJob(ctx, h.obsv, pendingJob) // again for model
	}

	logger = h.obsv.LoggerWithTelemetry(ctx)

	// Make sure this comment is added to the pending job so the LLM considers it
	pendingJob.CommentIDs = append(pendingJob.CommentIDs, comment.GetID())

	// Add all other (qualifying) comments and reviews that haven't been included on a job yet.
	jobutils.AddUnprocessedCommentsAndReviewsToJob(gh, actx, allJobs, pendingJob)

	problemStatement, err := problemstatement.NewPullRequestBuilder(h.obsv, gh, actx, pendingJob).Build(ctx)
	if err != nil {
		return fmt.Errorf("failed to create problem statement: %w", err)
	}
	pendingJob.ProblemStatement = problemStatement

	// Always wake if the comment mentions Copilot
	if !doesMentionCopilot(comment.Body) {
		shouldWake, err := h.jobExecutor.ShouldWake(ctx, event.Actor.ID, problemStatement.Content, pendingJob.ID)
		if err != nil {
			return fmt.Errorf("failed to check if Copilot should wake: %w", err)
		}
		if !shouldWake {
			logger.Info("Ignoring because Copilot does not think new feedback is actionable",
				kvp.String("wake_up_problem_statement", problemStatement.Content))
			return nil
		}
	}

	logger.Info("Waking up Copilot because new feedback is actionable")

	// Add a 👀 reaction to acknowledge the (actionable) comment
	if err := comment.AddReaction(ctx, gh, github.GQLReactionContentEyes); err != nil {
		// Bummer, but we don't want to fail the job
		h.obsv.LogAndReportError(ctx, err, "failed to add reaction")
	}

	pendingJob.EventURL = event.EventUrl
	pendingJob.EventType = jobs.EventTypePullRequestComment
	pendingJob.EventIdentifiers = []string{fmt.Sprintf("%d", comment.GetID())}

	pendingJob, err = h.jobsStore.UpsertJob(ctx, pendingJob)
	if err != nil {
		if errors.Is(err, jobs.ErrPreconditionFailedOnUpdate) {
			// This is almost certainly caused by a race condition where more than
			// one event handler grabbed the same pending job. This should be very
			// rare, but we have seen it in the wild, so we handle it gracefully.
			logger.WithError(err).Error("Failed to upsert job due to another process updating during processing")
			return nil
		}
		return fmt.Errorf("failed to upsert job: %w", err)
	}

	// Reload the logger with any job fields that may have changed
	jobutils.UpdateRequestContextJob(ctx, h.obsv, pendingJob)
	logger = h.obsv.LoggerWithTelemetry(ctx)

	// There's already a job running, don't launch the agent
	if has, runningJobID := jobs.HasRunningJobs(allJobs); has {
		logger.Info("Job queued but not launching agent because there is already a running job",
			kvp.String("running_job_id", runningJobID))
		return nil
	} else {
		logger.Info("Job upserted for pull request")
	}

	logger.Info("Queuing new job for pull request")

	if err := h.jobExecutor.QueueJob(ctx, gh, pendingJob); err != nil {
		return fmt.Errorf("failed to queue job: %w", err)
	}

	return nil
}

// setCtxPullRequestID updates the pull request in the request context with an ID.
// The pull request is added to the context during the initial hydro event processing,
// but the event that triggers the OnPullRequestCommentCreated handler doesn't include
// the pull request ID (because the PR is represented as an issue). This function
// is used to backfill the ID value once we have it.
func (h *Handler) setCtxPullRequestID(ctx context.Context, id int64, number int) {
	pr, ok := requestctx.PullRequest(ctx)
	if ok && pr.Number == number && pr.ID == nil {
		pr.ID = &id
		if err := requestctx.AddPullRequest(ctx, *pr); err != nil {
			h.obsv.LoggerWithTelemetry(ctx).WithError(err).Error("failed to add pull request to context")
		}
	}
}
