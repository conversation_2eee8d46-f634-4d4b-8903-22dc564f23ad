package events

import (
	"bytes"
	"context"
	"errors"
	"testing"
	"time"

	"github.com/github/go-exceptions"
	"github.com/github/go-exceptions/exporters/writer"
	"github.com/github/go-exceptions/stacktracers/pkgerrors"
	"github.com/github/go-http/v2/middleware/requestid"
	"github.com/github/go-mark"
	"github.com/github/go-stats"
	"github.com/github/sweagentd/internal/observability"
	"github.com/github/sweagentd/internal/requestctx"
	"github.com/stretchr/testify/require"
)

// mockEvent implements the processor interface.
// It waits for m.delay before marking itself as processed via m.processedCh.
// If the context expires before m.delay elapses, Process returns the context error.
type mockEvent struct {
	id          string
	delay       time.Duration
	processedCh chan<- string                   // used to record successful processing
	onProcess   func(ctx context.Context) error // optional callback to run on processing
	// err can be used to force Process to return an error after delay.
	err error
}

func newNoopHandler() *NoopHandler {
	return &NoopHandler{
		MockProcess: func(ctx context.Context, e any) error {
			m := e.(*mockEvent)
			if m.onProcess != nil {
				if err := m.onProcess(ctx); err != nil {
					return err
				}
			}
			select {
			case <-time.After(m.delay):
				if m.err != nil {
					return m.err
				}
				m.processedCh <- m.id
				return nil
			case <-ctx.Done():
				return ctx.Err()
			}
		},
	}
}

// --- TestQueue ---------------------------------------------------------------

func TestQueue(t *testing.T) {
	// Create a channel to record the IDs of processed events.
	processedCh := make(chan string, 10)

	ctx := requestid.WithNewGitHubRequestID(context.Background())
	originalRequestId := requestid.GetGitHubRequestID(ctx)
	require.NotZero(t, originalRequestId, "request ID should be set")

	// We'll create two events:
	// 1. A "quick" event that should complete before the timeout.
	// 2. A "slow" event that will exceed the eventTimeout and cause a DeadlineExceeded error.
	quickEvent := &mockEvent{
		id:          "quickEvent",
		delay:       50 * time.Millisecond,
		processedCh: processedCh,
		onProcess: func(ctx context.Context) error {
			if requestid.GetGitHubRequestID(ctx) != originalRequestId {
				return errors.New("request ID mismatch") // This should be the same as the original request ID, if it's not, it's not following the queue
			}
			return nil
		},
	}

	// This event will sleep longer than the event timeout.
	slowEvent := &mockEvent{
		id:          "slowEvent",
		delay:       200 * time.Millisecond,
		processedCh: processedCh,
	}

	// Create a Queue with 2 workers and an event timeout of 100ms.
	obsv := observability.NewNoopExporters()
	q := NewAsyncQueue(obsv, 2, 100*time.Millisecond, newNoopHandler(), false)

	ctx = requestctx.AddData(ctx, &requestctx.CtxData{})
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	// Start the queue in a separate goroutine.
	startErrCh := make(chan error, 1)
	go func() {
		startErrCh <- q.Start(ctx)
	}()

	// Enqueue the quick event.
	if err := q.Enqueue(ctx, quickEvent, "quick"); err != nil {
		t.Fatalf("failed to enqueue quickEvent: %v", err)
	}

	// Wait for the quick event to be processed.
	select {
	case id := <-processedCh:
		if id != "quickEvent" {
			t.Errorf("expected processed event 'quickEvent', got '%s'", id)
		}
	case <-time.After(200 * time.Millisecond):
		t.Error("quickEvent was not processed in time")
	}

	// Enqueue the slow event.
	if err := q.Enqueue(ctx, slowEvent, "slow"); err != nil {
		t.Fatalf("failed to enqueue slowEvent: %v", err)
	}

	var gotErr bool
	select {
	case err := <-startErrCh:
		if err != nil {
			gotErr = true
		}
	case <-time.After(500 * time.Millisecond):
		if gotErr {
			t.Error("no error should be returned, they should be logged")
		}
	}
}

func TestQueueContextCancellation(t *testing.T) {
	processedCh := make(chan string, 10)

	// Create two events:
	// - preCancelEvent: enqueued before cancellation and should be processed.
	// - postCancelEvent: enqueued after cancellation and should NOT be processed.
	preCancelEvent := &mockEvent{
		id:          "preCancel",
		delay:       50 * time.Millisecond,
		processedCh: processedCh,
	}
	postCancelEvent := &mockEvent{
		id:          "postCancel",
		delay:       50 * time.Millisecond,
		processedCh: processedCh,
	}

	// Create a cancellable context for Start.
	ctx, cancel := context.WithCancel(context.Background())
	obsv := observability.NewNoopExporters()
	q := NewAsyncQueue(obsv, 1, 200*time.Millisecond, newNoopHandler(), false)

	// Start the queue.
	startErrCh := make(chan error, 1)
	go func() {
		startErrCh <- q.Start(ctx)
	}()

	// Enqueue the preCancelEvent (using a non-cancelled context).
	evCtx := requestctx.AddData(context.Background(), &requestctx.CtxData{})
	if err := q.Enqueue(evCtx, preCancelEvent, "pre-cancel"); err != nil {
		t.Fatalf("failed to enqueue preCancelEvent: %v", err)
	}

	// Wait for preCancelEvent to be processed.
	select {
	case id := <-processedCh:
		if id != "preCancel" {
			t.Errorf("expected preCancel event to be processed, got %s", id)
		}
	case <-time.After(300 * time.Millisecond):
		t.Error("preCancel event was not processed in time")
	}

	// Cancel the context passed to Start.
	cancel()

	var returned bool
	// Wait for Start to return.
	select {
	case err := <-startErrCh:
		returned = true

		// Expect nil error because cancellation is a normal exit.
		if err != nil {
			t.Errorf("expected Start to return nil after cancellation, got %v", err)
		}
	case <-time.After(100 * time.Millisecond):
	}
	if !returned {
		t.Error("timed out waiting for Start to return after cancellation")
	}

	// Attempt to enqueue postCancelEvent with a context that has a short timeout.
	evCtx = requestctx.AddData(context.Background(), &requestctx.CtxData{})
	enqueueCtx, enqueueCancel := context.WithTimeout(evCtx, 50*time.Millisecond)
	defer enqueueCancel()

	err := q.Enqueue(enqueueCtx, postCancelEvent, "post-cancel")
	if err == nil {
		t.Error("expected Enqueue to fail after queue is cancelled, but it succeeded")
	}

	// Ensure that postCancelEvent is not processed.
	select {
	case id := <-processedCh:
		t.Errorf("expected no event processing after cancellation, but got %s", id)
	case <-time.After(100 * time.Millisecond):
		// Expected: no event processed.
	}
}

// --- TestQueueNoDropOnShutdown ---------------------------------------------
//
// This test ensures that if an event is enqueued (accepted) by the queue,
// it is processed even if the context (used by Start) is canceled.
// If an accepted event were dropped during shutdown, this test would fail.
func TestQueueNoDropOnShutdown(t *testing.T) {
	// Channel to record processed event IDs.
	processedCh := make(chan string, 1)

	// Create a mock event that takes 100ms to process.
	event := &mockEvent{
		id:          "event1",
		delay:       100 * time.Millisecond,
		processedCh: processedCh,
	}

	// Create a cancellable context that will be passed to Start.
	ctx := requestctx.AddData(context.Background(), &requestctx.CtxData{})
	ctx, cancel := context.WithCancel(ctx)

	// Create a Queue with 1 worker and a generous per-event timeout.
	obsv := observability.NewNoopExporters()
	q := NewAsyncQueue(obsv, 1, 500*time.Millisecond, newNoopHandler(), false)

	// Capture any error from Start.
	startErrCh := make(chan error, 1)
	go func() {
		startErrCh <- q.Start(ctx)
	}()

	// Enqueue the event. Use a background context for Enqueue so it isn’t canceled.
	evCtx := requestctx.AddData(context.Background(), &requestctx.CtxData{})
	if err := q.Enqueue(evCtx, event, "event"); err != nil {
		t.Fatalf("failed to enqueue event: %v", err)
	}

	// Wait briefly to give the worker a chance to pick up the event.
	// (Since the channel is unbuffered, a worker should have accepted the event.)
	time.Sleep(50 * time.Millisecond)

	// Cancel the context to simulate shutdown.
	cancel()

	var processed bool
	select {
	case <-startErrCh:
		if !processed {
			t.Error("expected event to be processed before Start returned")
		}
	case <-processedCh:
		processed = true
	case <-time.After(200 * time.Millisecond):
		t.Error("timed out waiting for event to be processed")
	}
}

func TestProcessEvent_Panic(t *testing.T) {
	exceptionStream := new(bytes.Buffer)
	reporter, err := exceptions.NewReporter(
		exceptions.WithApplication("sweagentd"),
		exceptions.WithExporter(writer.NewExporter(exceptionStream)),
		exceptions.WithStacktraceFunc(pkgerrors.NewStackTracer()),
	)
	require.NoError(t, err)

	h := &NoopHandler{
		MockProcess: explode,
	}
	q := &AsyncQueue{
		handler: h,
		obsv:    observability.NewNoopExporters(),
	}

	q.obsv.Reporter = reporter

	// this call should not crash
	q.processEvent(context.Background(), queuedEvent{id: "test"})

	// the panic should be reported
	exceptionStr := exceptionStream.String()
	require.Contains(t, exceptionStr, "boom")
	require.Contains(t, exceptionStr, "PANIC")
	require.Contains(t, exceptionStr, `"function":"explode"`) // check stack trace
}

func TestAsyncQueueLongHeldLock(t *testing.T) {
	t.Run("locked on long processing if FF is disabled", func(t *testing.T) {
		h := &NoopHandler{
			MockProcess: func(ctx context.Context, e any) error {
				<-time.After(100 * time.Millisecond) // simulate some processing delay
				return nil                           // no error
			},
		}
		q := &AsyncQueue{
			handler:      h,
			obsv:         observability.NewNoopExporters(),
			eventTimeout: 10 * time.Millisecond,
			workers:      1,
		}

		var ran bool
		go func() {
			// This will block because the event processing takes longer than the timeout.
			q.processEvent(context.Background(), queuedEvent{id: "test"})
			ran = true
		}()

		// We expect this to not deadlock, but rather return an error after the timeout.
		<-time.After(20 * time.Millisecond)
		if ran {
			t.Error("expected processEvent to not have returned after timeout")
		}
	})

	t.Run("does not lock on long processing if long-running processes are allowed", func(t *testing.T) {
		h := &NoopHandler{
			MockProcess: func(ctx context.Context, e any) error {
				<-time.After(100 * time.Millisecond) // simulate some processing delay
				return nil                           // no error
			},
		}
		q := &AsyncQueue{
			handler:               h,
			obsv:                  observability.NewNoopExporters(),
			eventTimeout:          10 * time.Millisecond,
			workers:               1,
			allowRunAwayProcesses: true, // enable run-away processes
		}

		var ran bool
		go func() {
			// This will block because the event processing takes longer than the timeout.
			// However, since allowRunAwayProcesses is true, it should not deadlock.
			q.processEvent(context.Background(), queuedEvent{id: "test"})
			ran = true
		}()

		// We expect this to not deadlock, but rather return an error after the timeout.
		<-time.After(20 * time.Millisecond)
		if !ran {
			t.Error("expected processEvent to return after timeout, but it blocked past the timeout")
		}
	})
}

func explode(ctx context.Context, e any) error {
	panic("boom")
}

func TestProcessEvent_ErrTooManyRequestsSuccess(t *testing.T) {
	// Track counter calls to verify success=true is sent
	var counterCalls []struct {
		key   string
		tags  stats.Tags
		value int64
	}

	// Use a buffer to track if exceptions are reported
	exceptionStream := new(bytes.Buffer)
	reporter, err := exceptions.NewReporter(
		exceptions.WithApplication("sweagentd"),
		exceptions.WithExporter(writer.NewExporter(exceptionStream)),
		exceptions.WithStacktraceFunc(pkgerrors.NewStackTracer()),
	)
	require.NoError(t, err)

	// Mock statter to capture counter calls
	mockStatter := &observability.MockStatter{
		MockCounter: func(key string, tags stats.Tags, value int64) {
			counterCalls = append(counterCalls, struct {
				key   string
				tags  stats.Tags
				value int64
			}{key: key, tags: tags, value: value})
		},
	}

	// Handler that returns mark.ErrTooManyRequests
	h := &NoopHandler{
		MockProcess: func(ctx context.Context, e any) error {
			return mark.ErrTooManyRequests
		},
	}

	obsv := observability.NewNoopExporters()
	obsv.Statter = mockStatter
	obsv.Reporter = reporter

	q := &AsyncQueue{
		handler: h,
		obsv:    obsv,
	}

	ctx := requestctx.AddData(context.Background(), &requestctx.CtxData{})

	// Process an event that will return mark.ErrTooManyRequests
	q.processEvent(ctx, queuedEvent{id: "test-too-many-requests"})

	// Verify that the counter was called with success=true
	require.Len(t, counterCalls, 1, "Expected exactly one counter call")
	counterCall := counterCalls[0]
	require.Equal(t, "events.queue.processed", counterCall.key)
	require.Equal(t, int64(1), counterCall.value)

	// Check that success tag is set to true
	successTag, exists := counterCall.tags["success"]
	require.True(t, exists, "Expected success tag to be present")
	require.Equal(t, "true", successTag, "Expected success tag to be 'true'")

	// Verify that no exception was reported (buffer should be empty)
	require.Empty(t, exceptionStream.String(), "Expected no exception to be reported for ErrTooManyRequests")
}

func TestProcessEvent_RegularErrorFailure(t *testing.T) {
	// Track counter calls to verify success=false is sent
	var counterCalls []struct {
		key   string
		tags  stats.Tags
		value int64
	}

	// Use a buffer to track if exceptions are reported
	exceptionStream := new(bytes.Buffer)
	reporter, err := exceptions.NewReporter(
		exceptions.WithApplication("sweagentd"),
		exceptions.WithExporter(writer.NewExporter(exceptionStream)),
		exceptions.WithStacktraceFunc(pkgerrors.NewStackTracer()),
	)
	require.NoError(t, err)

	// Mock statter to capture counter calls
	mockStatter := &observability.MockStatter{
		MockCounter: func(key string, tags stats.Tags, value int64) {
			counterCalls = append(counterCalls, struct {
				key   string
				tags  stats.Tags
				value int64
			}{key: key, tags: tags, value: value})
		},
	}

	// Handler that returns a regular error
	h := &NoopHandler{
		MockProcess: func(ctx context.Context, e any) error {
			return errors.New("some regular error")
		},
	}

	obsv := observability.NewNoopExporters()
	obsv.Statter = mockStatter
	obsv.Reporter = reporter

	q := &AsyncQueue{
		handler: h,
		obsv:    obsv,
	}

	ctx := requestctx.AddData(context.Background(), &requestctx.CtxData{})

	// Process an event that will return a regular error
	q.processEvent(ctx, queuedEvent{id: "test-regular-error"})

	// Verify that the counter was called with success=false
	require.Len(t, counterCalls, 1, "Expected exactly one counter call")
	counterCall := counterCalls[0]
	require.Equal(t, "events.queue.processed", counterCall.key)
	require.Equal(t, int64(1), counterCall.value)

	// Check that success tag is set to false
	successTag, exists := counterCall.tags["success"]
	require.True(t, exists, "Expected success tag to be present")
	require.Equal(t, "false", successTag, "Expected success tag to be 'false'")

	// Verify that an exception was reported (buffer should contain the error)
	require.NotEmpty(t, exceptionStream.String(), "Expected exception to be reported for regular errors")
	require.Contains(t, exceptionStream.String(), "some regular error", "Expected reported exception to contain the error message")
}
