package events

import (
	"context"
	"fmt"
	"regexp"
	"time"

	"github.com/github/sweagentd/internal/capi"
	"github.com/github/sweagentd/internal/experiments"
	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/githubtwirp"
	"github.com/github/sweagentd/internal/jobexecutor"
	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/jobutils"
	"github.com/github/sweagentd/internal/observability"
)

const (
	IssueAssignmentEventActionAssigned   = "assigned"
	IssueAssignmentEventActionUnassigned = "unassigned"
)

const (
	WorkflowCompletedEventStatusCompleted     = "completed"
	WorkflowCompletedEventConclusionSuccess   = "success"
	WorkflowCompletedEventConclusionFailure   = "failure"
	WorkflowCompletedEventConclusionCancelled = "cancelled"
)

// Actor represents a user or bot that is the subject of an event.
type Actor struct {
	ID         int64
	NodeID     string
	Login      string
	TrackingID string // New field for tracking ID
	Type       string
}

// Repository represents a GitHub repository associated with an event.
type Repository struct {
	ID        int64
	NodeID    string
	Name      string
	OwnerID   int64
	IsPrivate bool
}

// Issue represents a GitHub issue or pull request associated with an event.
type Issue struct {
	ID     int64
	NodeID string
	Number int
	IsOpen bool
}

// PullRequest represents a GitHub pull request associated with an event.
type PullRequest struct {
	// ID is the pull request ID. May be nil in events that don't include a
	// pull request object (e.g. PullRequestTimelineCommentCreate).
	ID      *int64
	NodeID  string
	Number  int
	IsOpen  bool
	IsDraft *bool
}
type PullRequestReview struct {
	ID int64
}
type PullRequestComment struct {
	ID     int64
	NodeID string
}

// WorkflowRun represents a GitHub Actions workflow run associated with an event.
type WorkflowRun struct {
	ID           int64
	Path         string
	UserID       int64
	CheckSuiteID int64
	Status       string
	Conclusion   string
	Attempt      int
	StartedAt    int64
	CompletedAt  int64
	UsageMs      int64
	JobID        *int64
}

type WorkflowRepository struct {
	ID      int64
	OwnerID int64
}

type IssueAssignmentEvent struct {
	// The actor that set/cleared the assignee in the issue
	Actor *Actor
	Repo  *Repository
	Issue *Issue
	// The full set of assignees after update
	Assignees []string
	// The specific user that was assigned or unassigned by this action
	Assignee  string
	Action    string
	EventUrl  string
	Timestamp time.Time
}

type PullRequestReviewSubmitEvent struct {
	// The user that submitted the PR review
	Actor             *Actor
	Repo              *Repository
	PullRequest       *PullRequest
	PullRequestReview *PullRequestReview
	EventUrl          string
	Timestamp         time.Time
}

type PullRequestCommentCreatedEvent struct {
	// The actor that created the comment.
	Actor       *Actor
	Repo        *Repository
	PullRequest *PullRequest
	Comment     *PullRequestComment
	EventUrl    string
	Timestamp   time.Time
}

type WorkflowRunCompletedEvent struct {
	Repo        *WorkflowRepository
	WorkflowRun *WorkflowRun
	Timestamp   time.Time
}

type EventsHandler interface {
	OnIssueAssigned(ctx context.Context, event IssueAssignmentEvent) error
	OnIssueUnassigned(ctx context.Context, event IssueAssignmentEvent) error
	OnPullRequestCommentCreated(ctx context.Context, event PullRequestCommentCreatedEvent) error
	OnPullRequestReviewSubmit(ctx context.Context, event PullRequestReviewSubmitEvent) error
	OnWorkflowRunCompleted(ctx context.Context, event WorkflowRunCompletedEvent) error
	OnCodeScanningAlertsAssigned(ctx context.Context, event CodeScanningAlertsAssignmentEvent) error
	Process(ctx context.Context, event any) error
}

var _ EventsHandler = &Handler{}
var _ EventsHandler = &NoopHandler{}

type Handler struct {
	obsv        observability.Exporters
	ffClient    featureflags.Client
	jobsStore   jobs.JobsStore
	jobExecutor jobexecutor.JobExecutor
	ghTwirp     githubtwirp.ClientInterface
	capiClient  capi.Client
	ghFactory   github.ClientFactoryInterface
	experiments experiments.Client
}

func NewHandler(obsv observability.Exporters, capiClient capi.Client, ffClient featureflags.Client, jobExecutor jobexecutor.JobExecutor, jobsStore jobs.JobsStore, ghTwirp githubtwirp.ClientInterface, ghFactory github.ClientFactoryInterface, experiments experiments.Client) *Handler {
	return &Handler{
		obsv:        obsv,
		ffClient:    ffClient,
		jobsStore:   jobsStore,
		jobExecutor: jobExecutor,
		ghTwirp:     ghTwirp,
		capiClient:  capiClient,
		ghFactory:   ghFactory,
		experiments: experiments,
	}
}

func (h *Handler) Process(ctx context.Context, event any) error {
	err := processEvent(ctx, h, event)
	if err != nil {
		return jobutils.HandleErrorByType(ctx, h.obsv, h.ghFactory, err)
	}
	return nil
}

func processEvent(ctx context.Context, h EventsHandler, event any) error {
	switch v := event.(type) {
	case IssueAssignmentEvent:
		if v.Action == IssueAssignmentEventActionUnassigned {
			return h.OnIssueUnassigned(ctx, v)
		}

		return h.OnIssueAssigned(ctx, v)

	case PullRequestCommentCreatedEvent:
		return h.OnPullRequestCommentCreated(ctx, v)

	case PullRequestReviewSubmitEvent:
		return h.OnPullRequestReviewSubmit(ctx, v)

	case WorkflowRunCompletedEvent:
		return h.OnWorkflowRunCompleted(ctx, v)

	case CodeScanningAlertsAssignmentEvent:
		return h.OnCodeScanningAlertsAssigned(ctx, v)

	default:
		return fmt.Errorf("unknown event type %T", v)
	}
}

func (h *Handler) assignmentForPR(ctx context.Context, ownerID, repoID int64, prID *int64, prNumber *int) (*jobs.Assignment, error) {
	assignment, err := h.jobsStore.GetAssignmentForPR(ctx, ownerID, repoID, prID, prNumber)
	if err != nil {
		return nil, fmt.Errorf("failed to get assignment: %w", err)
	}

	return assignment, nil
}

var copilotAtMention = regexp.MustCompile(`(?i)(?:^|\s|\W)@copilot\b`)

func doesMentionCopilot(body string) bool {
	return copilotAtMention.MatchString(body)
}
