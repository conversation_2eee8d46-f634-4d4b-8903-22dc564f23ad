package events

import (
	"context"
	"strconv"
	"strings"
	"testing"

	"github.com/github/hydro-client-go/v7/pkg/hydro"
	sweagentv0 "github.com/github/hydro-schemas-go/hydro/schemas/sweagentd/v0"
	sweagentEntitiesv0 "github.com/github/hydro-schemas-go/hydro/schemas/sweagentd/v0/entities"
	et "github.com/github/sweagentd/internal/events/eventstest"
	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/observability"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/encoding/protojson"
)

func TestOnCodeScanningAlertsAssignment(t *testing.T) {
	obsv := observability.NewNoopExporters()
	ffClient := featureflags.NewNoopClient(map[string]bool{
		featureflags.CopilotSWEAgentAqueductJobQueuing: true,
	})
	ghFactory := &github.NoopClientFactory{
		GitHubAppLogin: et.GitHubAppLogin,
		GitHubBotLogin: et.GitHubBotLogin,
	}

	t.Run("queues the agent job", func(t *testing.T) {
		tests := []struct {
			name           string
			queuingEnabled bool
		}{
			{
				name:           "queues job when queuing is enabled",
				queuingEnabled: true,
			},
			{
				name:           "executes job when queuing is disabled",
				queuingEnabled: false,
			},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				ffc := featureflags.NewNoopClient(map[string]bool{
					featureflags.CopilotSWEAgentAqueductJobQueuing: tt.queuingEnabled,
				})
				h, executor := NewDefaultTestHandler(obsv, ffc, ghFactory)

				assignee := et.UserCopilot()
				ghFactory.MockRepositoryContext = &github.RepositoryContext{
					Repository: *et.Repo(),
				}

				alert := EventCodeScanningAlert()

				err := h.OnCodeScanningAlertsAssigned(context.Background(), CodeScanningAlertsAssignmentEvent{
					Actor:     EventActorWriter(),
					Repo:      EventRepository(),
					Alerts:    []*CodeScanningAlert{alert},
					Assignees: []string{assignee.Login},
				})

				require.NoError(t, err)

				if tt.queuingEnabled {
					require.Len(t, executor.QueuedJobs(), 1)
					require.Len(t, executor.LaunchAgentCalls(), 0)

					require.Equal(t, et.ActorWriter().GetID(), executor.QueuedJobs()[0].ActorId)
				} else {
					require.Len(t, executor.QueuedJobs(), 0)
					require.Len(t, executor.LaunchAgentCalls(), 1)

					require.Equal(t, et.ActorWriter().GetID(), executor.LaunchAgentCalls()[0].ActorID)
				}
			})
		}
	})

	t.Run("ignores events where Copilot was not assigned", func(t *testing.T) {
		h, executor := NewDefaultTestHandler(obsv, ffClient, ghFactory)

		assignee := et.UserReader()
		ghFactory.MockRepositoryContext = &github.RepositoryContext{Repository: *et.Repo()}

		err := h.OnCodeScanningAlertsAssigned(context.Background(), CodeScanningAlertsAssignmentEvent{
			Actor:     EventActorWriter(),
			Repo:      EventRepository(),
			Alerts:    []*CodeScanningAlert{EventCodeScanningAlert()},
			Assignees: []string{assignee.Login},
		})

		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 0)
	})

	t.Run("ignores events where the assigner doesn't have repo write access", func(t *testing.T) {
		h, executor := NewDefaultTestHandler(obsv, ffClient, ghFactory)

		assignee := et.UserCopilot()
		ghFactory.MockRepositoryContext = &github.RepositoryContext{Repository: *et.Repo()}

		err := h.OnCodeScanningAlertsAssigned(context.Background(), CodeScanningAlertsAssignmentEvent{
			Actor:     EventActorReader(),
			Repo:      EventRepository(),
			Alerts:    []*CodeScanningAlert{EventCodeScanningAlert()},
			Assignees: []string{assignee.Login},
		})

		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 0)
	})

	t.Run("emits job_created hydro event", func(t *testing.T) {
		messageChan := make(chan hydro.Message, 1)
		sink, err := hydro.NewMemorySink(messageChan)
		require.NoError(t, err)
		publisher, _ := hydro.NewPublisher(sink, hydro.WithEncoder(hydro.NewJSONEncoder()))
		obsvWithPublisher := obsv.WithPublisher(publisher)
		ffClient := featureflags.NewNoopClient(map[string]bool{
			"sweagentd_publish_telemetry_events_to_hydro":  true,
			featureflags.CopilotSWEAgentAqueductJobQueuing: false, // Disable queuing for this test
		})
		h, executor := NewDefaultTestHandler(obsvWithPublisher, ffClient, ghFactory)

		assignee := et.UserCopilot()
		ghFactory.MockRepositoryContext = &github.RepositoryContext{
			Repository: *et.Repo(),
		}

		alert := EventCodeScanningAlert()
		eventRepo := EventRepository()

		err = h.OnCodeScanningAlertsAssigned(context.Background(), CodeScanningAlertsAssignmentEvent{
			Actor:     EventActorWriter(),
			Repo:      eventRepo,
			Alerts:    []*CodeScanningAlert{alert},
			Assignees: []string{assignee.Login},
		})

		require.NoError(t, err)
		require.Len(t, executor.LaunchAgentCalls(), 1)

		// Ensure we've passed the correct parameters to the launcher
		require.Len(t, executor.LaunchAgentCalls()[0].Nonce, 64)
		require.Equal(t, et.ActorWriter().GetLogin(), executor.LaunchAgentCalls()[0].ActorLogin)

		select {
		case eventReceived := <-messageChan:
			var jobStartedEvent = &sweagentv0.JobStarted{}
			protojson.Unmarshal(eventReceived.Value, jobStartedEvent)

			require.NotNil(t, jobStartedEvent)
			require.NotNil(t, jobStartedEvent.Trigger)
			require.Equal(t, sweagentEntitiesv0.JobTrigger_CODE_SCANNING_ALERTS_ASSIGNED, jobStartedEvent.Trigger.Type)
			require.Equal(t, sweagentEntitiesv0.ActionType_FIX, jobStartedEvent.Action)

			split := strings.Split(jobStartedEvent.Job.JobId, "-")
			repoOwnerIDFromEvent, err := strconv.ParseInt(split[0], 10, 64)
			require.NoError(t, err)
			require.Equal(t, eventRepo.OwnerID, repoOwnerIDFromEvent)
			repoIDFromEvent, err := strconv.ParseInt(split[1], 10, 64)
			require.NoError(t, err)
			require.Equal(t, eventRepo.ID, repoIDFromEvent)
			require.Equal(t, int64(123), jobStartedEvent.Job.PullRequestId)
			require.Equal(t, "", jobStartedEvent.Job.WorkflowRunId)
			require.Equal(t, eventRepo.ID, jobStartedEvent.Job.RepositoryId)
		default:
			t.Fail()
		}
	})
}
