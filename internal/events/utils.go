package events

import (
	"context"
	"fmt"
	"path/filepath"
	"regexp"
	"strings"

	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/jobutils"
	"github.com/github/sweagentd/internal/requestctx"
	sweagentdTwirp "github.com/github/sweagentd/proto/sweagentd/v1"
	gg "github.com/google/go-github/v72/github"
)

type CreatePullRequestRes struct {
	ID     int64
	Number int
}

type CreatePullRequestOpts struct {
	Title          string
	Body           string
	RepoID         uint64
	RepoName       string
	RepoOwner      string
	HeadBranchName string
	BaseBranchName string
	Draft          bool
}

func (h *Handler) OpenPullRequest(ctx context.Context, gh github.ClientInterface, job *jobs.Job, assignment *jobs.Assignment, repo github.GQLRepository, branchName, title, body string) (*CreatePullRequestRes, error) {
	pr, err := h.ghTwirp.SweagentdAPI().CreatePullRequest(ctx, &sweagentdTwirp.CreatePullRequestRequest{
		RepositoryId:      uint64(repo.ID),
		PrName:            title,
		PrDescription:     body,
		HeadBranchName:    branchName,
		BaseBranchName:    repo.DefaultBranchRef.Name,
		Draft:             true,
		AttributionUserId: uint64(job.ActorID),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create pull request: %w", err)
	}

	if err := h.patchAssignmentPullRequest(ctx, assignment, job, int64(pr.PullRequest.Id), int(pr.PullRequest.GithubNumber)); err != nil {
		return nil, fmt.Errorf("failed to patch pull request: %w", err)
	}

	prID := int64(pr.PullRequest.Id)
	prNumber := int(pr.PullRequest.GithubNumber)

	requestctx.AddPullRequest(ctx, requestctx.CtxPullRequest{
		ID:     &prID,
		Number: prNumber,
	})

	return &CreatePullRequestRes{
		ID:     prID,
		Number: prNumber,
	}, nil
}

func (h *Handler) CheckRules(ctx context.Context, gh github.ClientInterface, repo github.GQLRepository, branchName string) *jobutils.CheckRulesResult {
	logger := h.obsv.LoggerWithTelemetry(ctx)
	rules := []string{}
	result := &jobutils.CheckRulesResult{}

	copilotBranchExists, err := gh.CheckIfBranchExists(ctx, repo.Owner.Login, repo.Name, github.CopilotBranchPrefix)
	if err != nil {
		logger.WithError(err).Error(fmt.Sprintf("failed to check if branch '%s' exists", github.CopilotBranchPrefix))
	}
	if copilotBranchExists {
		rules = append(rules, github.RuleCopilotBranchExists)
	}

	permissions, err := gh.GetActionsPermissions(ctx, repo.Owner.Login, repo.Name)
	if err != nil {
		logger.WithError(err).Error("failed to get actions permissions")
	}
	if permissions != nil && !*permissions.Enabled {
		rules = append(rules, github.RuleRequireGitHubActions)
	}

	repositoryRules := h.getRepositoryRules(ctx, gh, repo, branchName)
	if len(repositoryRules) > 0 {
		rules = append(rules, repositoryRules...)
		result.HasRepositoryRules = true
	}

	branchProtectionRules := h.getBranchProtectionRules(ctx, gh, repo, branchName)
	if len(branchProtectionRules) > 0 {
		rules = append(rules, branchProtectionRules...)
		result.HasBranchProtectionRules = true
	}

	result.Rules = rules
	return result
}

// Use REST API for repository rulesets
func (h *Handler) getRepositoryRules(ctx context.Context, gh github.ClientInterface, repo github.GQLRepository, branchName string) []string {
	logger := h.obsv.LoggerWithTelemetry(ctx)
	rules := []string{}
	repoRules, err := gh.ListRepositoryRulesForBranch(ctx, repo.Owner.Login, repo.Name, branchName)
	if err != nil {
		logger.WithError(err).Error("failed to list repository rules for branch")
	}
	if repoRules != nil {
		repoRulesSuffix := "due to repository rules"
		// Basic repository rules: block creation, require signatures, or require pull request
		if len(repoRules.Creation) > 0 {
			rules = append(rules, fmt.Sprintf("%s %s", github.RuleBlockCreation, repoRulesSuffix))
		}
		if len(repoRules.RequiredSignatures) > 0 {
			rules = append(rules, fmt.Sprintf("%s %s", github.RuleRequireSignatures, repoRulesSuffix))
		}
		if len(repoRules.PullRequest) > 0 {
			rules = append(rules, fmt.Sprintf("%s %s", github.RuleRequirePullRequest, repoRulesSuffix))
		}

		// Branch name pattern repository rules
		for _, rule := range repoRules.BranchNamePattern {
			pattern := rule.Parameters.Pattern
			op := rule.Parameters.Operator
			var matched bool
			var operator string
			switch op {
			case gg.PatternRuleOperatorStartsWith:
				matched = strings.HasPrefix(branchName, pattern)
				operator = "start with"
			case gg.PatternRuleOperatorEndsWith:
				matched = strings.HasSuffix(branchName, pattern)
				operator = "end with"
			case gg.PatternRuleOperatorContains:
				matched = strings.Contains(branchName, pattern)
				operator = "contain"
			case gg.PatternRuleOperatorRegex:
				if re, err := regexp.Compile(pattern); err != nil {
					logger.WithError(err).Error(fmt.Sprintf("invalid branch name pattern: %s", pattern))
					continue
				} else {
					matched = re.MatchString(branchName)
					operator = "match regex"
				}
			default:
				logger.Error(fmt.Sprintf("unknown branch name pattern operator: %s", op))
				continue
			}
			mustOrNegated := "must"
			if rule.Parameters.Negate != nil && *rule.Parameters.Negate {
				matched = !matched
				mustOrNegated = "must not"
			}
			if !matched {
				rules = append(rules, fmt.Sprintf("%s (%s %s ***) %s", github.RuleBranchNamePattern, mustOrNegated, operator, repoRulesSuffix))
				break
			}
		}
	}

	return rules
}

// Use GQL API for classic branch protection rules
func (h *Handler) getBranchProtectionRules(ctx context.Context, gh github.ClientInterface, repo github.GQLRepository, branchName string) []string {
	logger := h.obsv.LoggerWithTelemetry(ctx)
	rules := []string{}
	branchRules, err := gh.ListBranchProtectionRules(ctx, repo.NodeID)
	if err != nil {
		logger.WithError(err).Error("failed to list classic branch protection rules")
	}
	if branchRules != nil {
		branchRulesSuffix := "due to branch protection rules"
		for _, rule := range *branchRules {
			matched, _ := filepath.Match(rule.Pattern, branchName)
			if !matched {
				continue
			}
			if rule.BlocksCreations {
				rules = append(rules, fmt.Sprintf("%s %s", github.RuleBlockCreation, branchRulesSuffix))
			}
			if rule.RequiresCommitSignatures {
				rules = append(rules, fmt.Sprintf("%s %s", github.RuleRequireSignatures, branchRulesSuffix))
			}
			if rule.RequiresApprovingReviews {
				rules = append(rules, fmt.Sprintf("%s %s", github.RuleRequirePullRequest, branchRulesSuffix))
			}
		}
	}

	return rules
}

func (h *Handler) createBranch(ctx context.Context, gh github.ClientInterface, job *jobs.Job, repo github.GQLRepository, branchName string) error {
	return jobutils.CreateBranch(ctx, h.obsv, h.ffClient, h.jobsStore, gh, jobutils.CreateBranchOpts{
		Job:            job,
		RepoOwner:      repo.Owner.Login,
		RepoName:       repo.Name,
		BaseBranchName: repo.DefaultBranchRef.Name,
		NewBranchName:  branchName,
	})
}

func (h *Handler) patchAssignmentPullRequest(ctx context.Context, assignment *jobs.Assignment, job *jobs.Job, prID int64, prNumber int) error {
	// Save the PR details to the assignment
	if err := h.jobsStore.PatchAssignment(ctx, assignment, jobs.AssignmentPatch{
		PullRequestID:     &prID,
		PullRequestNumber: &prNumber,
	}); err != nil {
		return fmt.Errorf("failed to patch assignment: %w", err)
	}

	// Save the PR details to the job
	if err := h.jobsStore.PatchJob(ctx, job, jobs.JobPatch{
		PullRequestID:     &prID,
		PullRequestNumber: &prNumber,
	}); err != nil {
		return fmt.Errorf("failed to patch job: %w", err)
	}

	jobutils.UpdateRequestContextJob(ctx, h.obsv, job)
	return nil
}

func (h *Handler) ShouldIgnorePRCommentOrReview(ctx context.Context, gh github.ClientInterface, repo *Repository, pr *PullRequest, author *Actor) (bool, error) {
	logger := h.obsv.LoggerWithTelemetry(ctx)

	// Ignore events for closed PRs
	if !pr.IsOpen {
		logger.Info("Ignoring comment or review because the PR is closed")
		return true, nil
	}

	// Ignore events if the author is Copilot
	if gh.UserIsCopilot(author.Login) {
		logger.Info("Ignoring comment or review because the author is Copilot")
		return true, nil
	}

	// Ignore events if the author is a bot
	if strings.EqualFold(author.Type, "bot") {
		logger.Info("Ignoring comment or review because the author is a bot")
		return true, nil
	}

	// Only process events where the actor has write access to the repo
	permission, err := gh.GetUserPermissionsForRepositoryByID(ctx, repo.ID, author.Login)
	if err != nil {
		return true, fmt.Errorf("getting user permissions for repository: %w", err)
	}
	if !permission.Push {
		logger.Info("Ignoring comment or review because the author does not have write access to the repo")
		return true, nil
	}

	return false, nil
}
