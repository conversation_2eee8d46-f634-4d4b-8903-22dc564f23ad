package events

import (
	"context"
	"errors"
	"fmt"
	"sync/atomic"
	"time"

	"github.com/github/github-telemetry-go/kvp"
	"github.com/github/github-telemetry-go/log"
	"github.com/github/go-http/v2/middleware/requestid"
	"github.com/github/go-mark"
	"github.com/github/go-stats"
	"github.com/github/sweagentd/internal/observability"
	"github.com/github/sweagentd/internal/requestctx"
	"golang.org/x/sync/errgroup"
)

type Queue interface {
	Enqueue(ctx context.Context, event any, eventID string) error
	Start(ctx context.Context) error
}

type queuedEvent struct {
	id           string
	event        any
	ctxData      *requestctx.CtxData
	oldRequestID string
}

type AsyncQueue struct {
	obsv   observability.Exporters
	events chan queuedEvent

	handler      EventsHandler
	workers      int
	eventTimeout time.Duration

	activeEventProcesses int64
	queuedEvents         int64

	// allowRunAwayProcesses is a temporary workaround to allow long-running processes
	// if you wish to change this behavior, update the feature flag and re-deploy the service.
	allowRunAwayProcesses bool
}

func NewAsyncQueue(obsv observability.Exporters, workers int, eventTimeout time.Duration, handler EventsHandler, allowRunAwayProcesses bool) *AsyncQueue {
	return &AsyncQueue{
		obsv:         obsv,
		events:       make(chan queuedEvent),
		handler:      handler,
		workers:      workers,
		eventTimeout: eventTimeout,

		allowRunAwayProcesses: allowRunAwayProcesses,
	}
}

func (q *AsyncQueue) Enqueue(ctx context.Context, event any, eventID string) error {
	defer q.measureEventQueuing(ctx, eventID)()

	ev := queuedEvent{id: eventID, event: event, ctxData: requestctx.Data(ctx), oldRequestID: requestid.GetGitHubRequestID(ctx)}
	select {
	case <-ctx.Done():
		return ctx.Err()
	case q.events <- ev:
	}

	return nil
}

func (q *AsyncQueue) Start(ctx context.Context) error {
	g, ctx := errgroup.WithContext(ctx)

	for i := 0; i < q.workers; i++ {
		g.Go(func() error {
			return q.worker(ctx)
		})
	}

	// Wait for all workers to finish
	return g.Wait()
}

func (q *AsyncQueue) worker(ctx context.Context) error {
	for {
		select {
		case <-ctx.Done():
			return nil

		case qe := <-q.events:
			q.processEvent(ctx, qe)
		}
	}
}

func (q *AsyncQueue) processEvent(ctx context.Context, qe queuedEvent) {
	// We'll take request ID manually since it's not in "Data"
	existingRequestID := qe.oldRequestID
	if existingRequestID != "" {
		ctx = requestid.WithGitHubRequestID(ctx, existingRequestID)
	} else {
		q.obsv.Logger.WithContext(ctx).Error("missing request ID in context in async queue's processEvent")
	}

	// Re-hydrate queue's context data for internal telemetry.
	if qe.ctxData != nil {
		ctx = requestctx.AddData(ctx, qe.ctxData)
	} else {
		ctx = requestctx.AddData(ctx, &requestctx.CtxData{})
	}

	_, _, kvps := requestctx.ToTelemetry(ctx, nil)
	logger := q.obsv.Logger.WithContext(ctx).WithFields(kvps...)
	logger.Info("processing queued event")

	var err error
	defer func() {
		if panicErr := recover(); panicErr != nil {
			err = fmt.Errorf("PANIC: processing event %s: %v", qe.id, panicErr)
		}

		// Call ToTelemetry again here, since the context may have changed
		// during event processing.
		tags, ex, kvps := requestctx.ToTelemetry(ctx, nil)

		// Determine if the event was processed successfully. This doesn't mean that
		// it kicked off a job, just that we didn't encounter an unexpected error during processing.
		eventProcessedSuccessfully := q.determineEventSuccess(err)

		tags = tags.Merge(stats.Tags{"success": fmt.Sprintf("%t", eventProcessedSuccessfully)})
		q.obsv.Statter.Counter("events.queue.processed", tags, 1)

		// This is the primary place where we bubble up errors from
		// processing events. If an error occurs deep within the event handlers,
		// it will be caught here and reported.
		if err != nil {
			logger.WithFields(kvps...).WithError(err).Error("failed to process event")
			if !eventProcessedSuccessfully {
				_ = q.obsv.Reporter.Report(ctx, err, ex)
			}
		}
	}()

	err = q.processWithTimeout(ctx, qe, logger)
	if err != nil {
		return // err handled in defer
	}

	logger.Info("event processed")
}

func (q *AsyncQueue) processWithTimeout(ctx context.Context, qe queuedEvent, logger log.Logger) error {
	defer q.measureEventProcessing(ctx, qe.id)()

	ctx, cancel := context.WithTimeout(context.WithoutCancel(ctx), q.eventTimeout)
	defer cancel()

	if q.allowRunAwayProcesses {
		// This is a workaround to allow potentially long-running processes to go
		// past the timeout but not deadlock the queue. Not ideal, but it's a temporary
		// solution until we land the aqueue refactor.
		done := make(chan error, 1) // buffered channel to avoid blocking
		go func() {
			defer close(done)
			done <- q.handler.Process(ctx, qe.event)
		}()

		select {
		case <-ctx.Done():
			// logger should contain event information and other telemetry
			// data, so we can log it here.
			tags, _, _ := requestctx.ToTelemetry(ctx, nil)
			q.obsv.Statter.Counter("events.queue.runaway", tags, 1)
			logger.Info("event processing timed out, potential runaway process")

			return ctx.Err()
		case err := <-done:
			return err
		}
	}

	return q.handler.Process(ctx, qe.event)
}

func (q *AsyncQueue) measureEventQueuing(ctx context.Context, eventID string) func() {
	tags, _, kvps := requestctx.ToTelemetry(ctx, nil)

	start := time.Now()
	queuedEventsBeforeActivating := atomic.LoadInt64(&q.queuedEvents)
	activeCount := atomic.AddInt64(&q.queuedEvents, 1)
	q.obsv.Statter.Gauge("queue.queued_events", tags, activeCount)
	q.obsv.Logger.WithContext(ctx).WithFields(kvps...).Info("start: event queuing", kvp.String("event.id", eventID), kvp.Time("start_time", start), kvp.Int64("queued_events_before", queuedEventsBeforeActivating))

	return func() {
		// Previous code pulled these out again after for the done event, so we are doing the same.
		// This can result in different values because of how requestctx embeds data pointers into the context.
		tags, _, kvps := requestctx.ToTelemetry(ctx, nil)
		duration := time.Since(start)
		activeCount := atomic.AddInt64(&q.queuedEvents, -1)
		queuedEventsAfterFinishing := atomic.LoadInt64(&q.queuedEvents)
		q.obsv.Statter.Gauge("queue.queued_events", tags, activeCount)
		q.obsv.Statter.DistributionMs("queue.queue_dist", tags, duration)
		q.obsv.Logger.WithContext(ctx).WithFields(kvps...).Info("done: event queuing", kvp.String("event.id", eventID), kvp.Duration("duration", duration), kvp.Int64("queued_events_after", queuedEventsAfterFinishing))

		q.obsv.Statter.Counter("events.queue.enqueue", tags, 1)
		q.obsv.Logger.WithContext(ctx).WithFields(kvps...).Info("event queued: " + eventID)
	}
}

func (q *AsyncQueue) measureEventProcessing(ctx context.Context, eventID string) func() {
	tags, _, kvps := requestctx.ToTelemetry(ctx, nil)
	start := time.Now()
	processesBeforeActivating := atomic.LoadInt64(&q.activeEventProcesses)
	activeCount := atomic.AddInt64(&q.activeEventProcesses, 1)
	q.obsv.Statter.Gauge("queue.active_processes", tags, activeCount)
	q.obsv.Logger.WithContext(ctx).WithFields(kvps...).Info("start: event processing", kvp.String("event.id", eventID), kvp.Time("start_time", start), kvp.Int64("active_processes_before", processesBeforeActivating))

	return func() {
		// Previous code pulled these out again after for the done event, so we are doing the same.
		// This can result in different values because of how requestctx embeds data pointers into the context.
		tags, _, kvps := requestctx.ToTelemetry(ctx, nil)
		duration := time.Since(start)
		activeCount := atomic.AddInt64(&q.activeEventProcesses, -1)
		processesAfterFinishing := atomic.LoadInt64(&q.activeEventProcesses)
		q.obsv.Statter.Gauge("queue.active_processes", tags, activeCount)
		q.obsv.Statter.DistributionMs("queue.process_dist", tags, duration)
		q.obsv.Logger.WithContext(ctx).WithFields(kvps...).Info("done: event processing", kvp.String("event.id", eventID), kvp.Duration("duration", duration), kvp.Int64("active_processes_after", processesAfterFinishing))
	}
}

func (q *AsyncQueue) determineEventSuccess(err error) bool {
	switch {
	case err == nil:
		return true
	// We want to treat ErrTooManyRequests as a non-failure, because a user can
	// trigger this by assigning Copilot to too many issues at once.
	case errors.Is(err, mark.ErrTooManyRequests):
		return true
	// TODO: Should we handle other specific errors here? ex: mark.ErrBadRequest?
	default:
		return false
	}
}
