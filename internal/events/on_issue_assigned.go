package events

import (
	"context"
	"fmt"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/joberrors"
	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/jobutils"
	"github.com/github/sweagentd/internal/problemstatement"
	"github.com/github/sweagentd/internal/requestctx"
)

const timeCheckBufferSeconds = 1

func (h *Handler) OnIssueAssigned(ctx context.Context, event IssueAssignmentEvent) error {
	gh, err := h.ghFactory.NewClientFromRepo(ctx, event.Repo.ID, event.Repo.OwnerID)
	if err != nil {
		return fmt.Errorf("failed to create GitHub client: %w", err)
	}

	// for this event, the actor is the user that set/cleared the assignee in the issue.
	assigner := event.Actor

	// Determine if this event assigned Copilot.
	// Use new singular Assignee field if feature flag enabled, otherwise use legacy Assignees slice.
	var assignedToCopilot bool
	if h.ffClient.IsEnabledForUserOrRepoOrOwner(ctx, "sweagentd_cleanup_dup_assignment_hack", event.Actor.ID, event.Repo.ID, event.Repo.OwnerID) {
		// Feature flag enabled: use singular Assignee if present; fallback to legacy slice if empty
		if event.Assignee != "" {
			assignedToCopilot = gh.UserIsCopilot(event.Assignee)
		} else {
			h.obsv.LoggerWithTelemetry(ctx).Warn("empty Assignee field; falling back to Assignees slice")
			assignedToCopilot = slices.ContainsFunc(event.Assignees, gh.UserIsCopilot)
		}
	} else {
		// Legacy behavior: check slice of Assignees
		assignedToCopilot = slices.ContainsFunc(event.Assignees, gh.UserIsCopilot)
	}
	// Ignore events for closed issues or issues not assigned to Copilot
	if !event.Issue.IsOpen || !assignedToCopilot {
		return nil
	}

	// Only process events where the actor has write access to the repo
	permission, err := gh.GetUserPermissionsForRepositoryByID(ctx, event.Repo.ID, assigner.Login)
	if err != nil {
		return fmt.Errorf("getting user permissions for repository: %w", err)
	}
	if !permission.Push {
		h.obsv.LoggerWithTelemetry(ctx).Info("Ignoring assignment because actor does not have write access to the repo")
		return nil
	}

	// The issue assigned event includes an array of assignees, so we don't know
	// if this specific event represents the bot being assigned or not.
	assignment, err := h.getAssignmentForIssue(ctx, event.Repo.OwnerID, event.Repo.ID, event.Issue.ID)
	if err != nil {
		return fmt.Errorf("getting assignment: %w", err)
	}

	if assignment != nil {
		// We have an existing active assignment, so we can assume that this
		// event does not represent the bot being assigned to the issue.
		return nil
	}

	actx, err := gh.GetIssueContext(ctx, event.Repo.NodeID, event.Issue.Number)
	if err != nil {
		return fmt.Errorf("failed to get issue context: %w", err)
	}

	// If the issue has been edited since the event was triggered, we need to use an older version of the issue body to ensure
	// nobody altered the issue between the event trigger and processing. See https://github.com/github/sweagentd/issues/2473
	if h.ffClient.IsEnabledForUserOrRepoOrOwner(ctx, featureflags.CopilotSWEAgentIssueBodyEditCheck, assigner.ID, event.Repo.ID, event.Repo.OwnerID) {
		h.obsv.LoggerWithTelemetry(ctx).Info("Feature flag to check issue assignment timestamp enabled")
		success := h.adjustIssueBodyIfRequired(ctx, gh, actx, event)
		if !success {
			return nil
		}
	} else {
		h.obsv.LoggerWithTelemetry(ctx).Info("Feature flag to check issue assignment timestamp NOT enabled")
	}

	repo := actx.Repository
	owner := repo.Owner
	issue := actx.Issue

	// Check if the repository has been initialized (has any actual contents)
	// before we create an assignment.
	// TODO: Support uninitialized repos, and let the agent do the initialization.
	if repo.DefaultBranchRef == nil {
		tags, _, _ := requestctx.ToTelemetry(ctx, nil)
		h.obsv.Statter.Counter("events.Handler.onIssueAssigned.failed.uninitializedRepo", tags, 1)

		if err := h.addErrorComment(ctx, gh, actx, joberrors.ErrorRepositoryUninitialized); err != nil {
			return err
		}
		return nil
	}

	// We don't have an existing active assignment, so we can assume that this
	// event does represent the bot being assigned to the issue.
	assignment = jobs.NewAssignment(owner.GetID(), repo.ID, owner.NodeID, repo.NodeID, owner.Login, repo.Name, issueResource(issue.GetID()))

	assignment, err = h.jobsStore.CreateAssignment(ctx, assignment)
	if err != nil {
		return err
	}

	err = h.onIssueAssigned(ctx, gh, assignment, actx, event)
	if err != nil {
		return jobutils.NewUnhandledErrorWithIssueComment(ctx, err, assignment, true, event.Actor.Login, event.Issue.Number)
	}
	return nil
}

// Function to handle race condition. If the issue has been edited since the event was triggered, we need to use an older version of the
// issue body to ensure nobody altered the issue between the event trigger and processing. See https://github.com/github/sweagentd/issues/2473
//
// The function returns true if any required adjustments were successfully made or not adjustments were needed.
func (h *Handler) adjustIssueBodyIfRequired(ctx context.Context, gh github.ClientInterface, actx *github.IssueContext, event IssueAssignmentEvent) bool {
	// We are dealing with UTC time values here, so we nee to factor in that the update could have happened in the same second
	adjustedTS := event.Timestamp.Add(-timeCheckBufferSeconds * time.Second)
	// See if there are any edits to the issue body, and if so, see if we can find a previous edit within 1s of
	// the event time window. Note that LastEditedAt can be nil, but CreatedAt and UpdatedAt will always have a value.
	if actx.Issue.LastEditedAt == nil || actx.Issue.CreatedAt.Equal(*actx.Issue.LastEditedAt) || actx.Issue.LastEditedAt.Before(adjustedTS) {
		return true
	}

	h.obsv.LoggerWithTelemetry(ctx).Info("Issue may have been updated since assignment, verifying correct body")
	// Get most recent 20 content edits for the issue body to see if we can find one before the timestamp
	edits, err := gh.GetIssueUserContentEdits(ctx, actx.Issue.NodeID)
	if err != nil {
		h.obsv.LogAndReportError(ctx, err, "failed to get user content edits for issue")
		return false
	}

	// We should never be here in a scenario where there are no edits, but double check for future changes
	if len(edits) == 0 {
		// Should never happen, since actx.Issue.LastEditedAt should be nil, but cover bases for replication lag
		h.obsv.LoggerWithTelemetry(ctx).Info("No edits found for issue, using current Body")
		return true
	}

	// Sort by EditedAt descending in case default sort order from API changes.
	// NOTE: UpdatedAt may != EditedAt since when the first edit happens, two records are added
	// with the same update time, but different edit times (one being for the original body)
	slices.SortFunc(edits, func(a, b github.UserContentEdit) int {
		return b.EditedAt.Compare(a.EditedAt)
	})
	diff := ""
	for _, edit := range edits {
		if edit.EditedAt.Before(adjustedTS) {
			// Use the body from the last edit before the event timestamp
			h.obsv.LoggerWithTelemetry(ctx).Info("Found diff for earlier timestamp")
			diff = edit.Diff
			break
		}
	}
	if diff == "" {
		// If we have seen 20 edits within the same second, this is likely an attempt to exploit a race condition, so ignore
		h.obsv.LogAndReportError(ctx, err, "failed to find older edit for issue")
		return false
	}

	// Otherwise, use the found diff as the issue body
	actx.Issue.Body = diff
	return true
}

func (h *Handler) onIssueAssigned(ctx context.Context, gh github.ClientInterface, assignment *jobs.Assignment, actx *github.IssueContext, event IssueAssignmentEvent) error {
	ctx, span := h.obsv.Tracer.Start(ctx, "events.Handler.onIssueAssigned")
	defer span.End()

	actionType := jobs.AgentActionFix

	repo := actx.Repository
	issue := actx.Issue
	assigner := event.Actor

	// Add a 👀 reaction to indicate we've started work
	if err := issue.AddReaction(ctx, gh, github.GQLReactionContentEyes); err != nil {
		h.obsv.LogAndReportError(ctx, err, "failed to add reaction")
	}

	title := fmt.Sprintf("[WIP] %s", issue.Title)
	branchSuffix := fmt.Sprintf("fix-%d", issue.Number)

	newBranchName, err := gh.GenerateBranchName(ctx, repo.Owner.Login, repo.Name, branchSuffix)
	if err != nil {
		return fmt.Errorf("failed to generate branch name: %w", err)
	}

	rulesResult := h.CheckRules(ctx, gh, repo, newBranchName)
	if len(rulesResult.Rules) > 0 {
		if err := h.jobsStore.DetachAssignmentResources(ctx, assignment); err != nil {
			return fmt.Errorf("failed to detach assignment after checking rules: %w", err)
		}
		if err = h.handleRulesViolation(ctx, gh, assigner.Login, assignment, actx, rulesResult); err != nil {
			return err
		}
		return nil
	}

	job := assignment.NewJob(actionType, &requestctx.UserInfo{
		ID:         uint64(assigner.ID),
		Login:      assigner.Login,
		TrackingID: assigner.TrackingID,
	}, repo.DefaultBranchRef.Name)
	job.IssueNumber = issue.Number

	job, err = h.jobsStore.CreateJob(ctx, job)
	if err != nil {
		return fmt.Errorf("failed to create job: %w", err)
	}

	jobutils.UpdateRequestContextJob(ctx, h.obsv, job)
	logger := h.obsv.LoggerWithTelemetry(ctx)

	logger.Info("Job pending for assigned issue")

	err = h.createBranch(ctx, gh, job, repo, newBranchName)
	if err != nil {
		return fmt.Errorf("failed to create branch: %w", err)
	}

	body := h.generatePRBody(ctx, job, issue.Body, issue.Number)
	pr, err := h.OpenPullRequest(ctx, gh, job, assignment, repo, newBranchName, title, body)
	if err != nil {
		return fmt.Errorf("failed to create pull request: %w", err)
	}

	// Copy across any labels that start with "copilot:" since these are used for varying behaviors at an issue/PR level
	allowModelCustomization := h.ffClient.IsEnabledForUserOrRepoOrOwner(ctx, featureflags.CopilotSWEAgentCanSelectModel, assigner.ID, repo.ID, repo.Owner.GetID())
	if allowModelCustomization && issue.Labels.Len() > 0 {
		var labels = []string{}
		for _, label := range issue.Labels.Nodes {
			if strings.HasPrefix(label.Name, "copilot:") {
				labels = append(labels, label.Name)
			}
		}
		err := gh.AddLabelsToIssueOrPR(ctx, repo.Owner.Login, repo.Name, pr.Number, labels)
		if err != nil {
			return fmt.Errorf("failed to add labels to issue: %w", err)
		}
	}

	// Minimize existing error comments on the issue before we create the problem
	// statement so we don't have to worry about them being included.
	joberrors.MinimizeIssueErrorComments(ctx, h.obsv, gh, actx)

	// Get the problem statement for the issue. It won't include the PR since it's
	// empty at this point and will throw off the agent if it's included.
	problemStatement, err := problemstatement.NewIssueBuilder(h.obsv, gh, actx, job).Build(ctx)
	if err != nil {
		return fmt.Errorf("failed to create problem statement: %w", err)
	}
	job.ProblemStatement = problemStatement

	labelsAsStrings := make([]string, 0, len(issue.Labels.Nodes))
	for _, label := range issue.Labels.Nodes {
		labelsAsStrings = append(labelsAsStrings, label.GetName())
	}

	if err := h.experiments.SetExperimentsOnNewAssignment(ctx, h.jobsStore, assignment, job, labelsAsStrings); err != nil {
		h.obsv.LoggerWithTelemetry(ctx).WithError(err).Error("Failed to set experiments on new assignment")
	} else {
		job.Experiments = assignment.Experiments
	}

	job.Model = jobutils.DetermineModel(ctx, h.obsv, h.ffClient, h.ghTwirp, job, issue.Labels.Nodes)

	if jobutils.IsOsweExperimentEnabled(ctx, h.ffClient, job) {
		if err := jobutils.UpdatePullRequestBodyWithOsweExperimentNote(ctx, gh, h.ffClient, job); err != nil {
			h.obsv.LoggerWithTelemetry(ctx).WithError(err).Error("Failed to update PR body with OSWE experiment note")
		}
	}

	job.EventType = jobs.EventTypeIssueAssignment
	job.EventURL = event.EventUrl
	job.EventIdentifiers = []string{fmt.Sprintf("%d", issue.GetID())}

	job, err = h.jobsStore.UpdateJob(ctx, job)
	if err != nil {
		return fmt.Errorf("failed to update job: %w", err)
	}

	h.obsv.LoggerWithTelemetry(ctx).Info("Queuing new job for assigned issue")

	if err := h.jobExecutor.QueueJob(ctx, gh, job); err != nil {
		return fmt.Errorf("failed to queue job: %w", err)
	}

	return nil
}

func (h *Handler) generatePRBody(ctx context.Context, job *jobs.Job, issueBody string, issueNumber int) string {
	baseBody := fmt.Sprintf("Thanks for assigning this issue to me. I'm starting to work on it and will keep this PR's description up to date as I form a plan and make progress.\n\nOriginal issue description:\n\n> %s\n\n\nFixes #%d.", strings.ReplaceAll(issueBody, "\n", "\n> "), issueNumber)

	tip := jobutils.GetRandomTipForJob(ctx, h.ffClient, job.ActorID, job.RepoID, job.OwnerID, job.RepoOwner, job.RepoName)
	if tip != "" {
		baseBody = baseBody + "\n\n" + jobutils.PullRequestBodyTipSeparator + "\n---\n\n" + tip
	}

	return baseBody
}

func issueResource(id int64) jobs.AssignmentResource {
	return jobs.AssignmentResource{
		Type: "issue",
		ID:   strconv.FormatInt(id, 10),
	}
}

func (h *Handler) getAssignmentForIssue(ctx context.Context, ownerID, repoID, issueID int64) (*jobs.Assignment, error) {
	resource := issueResource(issueID)

	assignment, err := h.jobsStore.GetAssignmentForResource(ctx, ownerID, repoID, resource)
	if err != nil {
		return nil, fmt.Errorf("failed to get assignment: %w", err)
	}

	return assignment, nil
}

func (h *Handler) handleRulesViolation(ctx context.Context, gh github.ClientInterface, actorLogin string, assignment *jobs.Assignment, actx *github.IssueContext, rulesResult *jobutils.CheckRulesResult) error {
	errWith := jobutils.NewUnhandledRulesErrorWithIssueComment(ctx, nil, assignment, actorLogin, actx.Issue.Number, rulesResult)
	body, err := errWith.GenerateComment()
	if err != nil {
		return fmt.Errorf("failed to generate comment body: %w", err)
	}

	tags, _, _ := requestctx.ToTelemetry(ctx, nil)
	h.obsv.Statter.Counter("events.Handler.onIssueAssigned.failed.rulesViolation", tags, 1)

	if err := h.addErrorComment(ctx, gh, actx, body); err != nil {
		return fmt.Errorf("failed to create comment for rules violation: %w", err)
	}

	return nil
}

// addErrorComment adds a comment to the issue with the given body.
// If there are existing error comments by Copilot, it minimizes them as 'outdated'
// before adding the new comment.
// This is to ensure that the user is not overwhelmed with multiple error comments
// from Copilot on the same issue, and only sees the most recent one.
func (h *Handler) addErrorComment(ctx context.Context, gh github.ClientInterface, actx *github.IssueContext, body string) error {
	if err := actx.Issue.RemoveReaction(ctx, gh, github.GQLReactionContentEyes); err != nil {
		h.obsv.LogAndReportError(ctx, err, "failed to remove reaction")
	}
	return joberrors.CreateErrorCommentOnIssue(ctx, h.obsv, gh, actx, body)
}
