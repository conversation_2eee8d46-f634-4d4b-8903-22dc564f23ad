package events

import (
	"context"
	"testing"

	"github.com/github/go-stats"
	"github.com/github/sweagentd/internal/capi"
	et "github.com/github/sweagentd/internal/events/eventstest"
	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/observability"
	"github.com/github/sweagentd/internal/requestctx"
	gg "github.com/google/go-github/v72/github"
	"github.com/stretchr/testify/require"
)

func TestOnWorkflowRunCompleted(t *testing.T) {
	obsv := observability.NewNoopExporters()
	ffClient := featureflags.NewNoopClient(map[string]bool{
		featureflags.CopilotSWEAgentOnlyWakeOnAtCopilot: true,
		featureflags.CopilotSWEAgentAqueductJobQueuing:  true,
	})
	ghFactory := &github.NoopClientFactory{
		GitHubAppLogin: et.GitHubAppLogin,
		GitHubBotLogin: et.GitHubBotLogin,
	}
	capiClient, _, ghTwirp := commonHandlerDependencies()
	capiClient.MockGetSession = func(ctx context.Context, sessionID string) (*capi.Session, error) {
		return &capi.Session{
			ID: sessionID,
		}, nil
	}
	capiClient.MockUpdateSession = func(ctx context.Context, sessionID string, update capi.SessionUpdate) error {
		return nil
	}

	t.Run("queues the pending job", func(t *testing.T) {
		tests := []struct {
			name           string
			queuingEnabled bool
		}{
			{
				name:           "queues pending job when queuing is enabled",
				queuingEnabled: true,
			},
			{
				name:           "executes pending job when queuing is disabled",
				queuingEnabled: false,
			},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				ffc := featureflags.NewNoopClient(map[string]bool{
					featureflags.CopilotSWEAgentAqueductJobQueuing: tt.queuingEnabled,
				})
				h, executor := NewTestHandler(obsv, capiClient, ffc, jobs.NewMemoryStore(), ghTwirp, ghFactory)
				h.jobsStore.CreateAssignment(context.Background(), et.Assignment())

				h.jobsStore.CreateAssignment(context.Background(), et.Assignment())
				job := et.JobWithSessionID(jobs.JobStatusRunning, "session")
				jobB := et.Job(jobs.JobStatusPending)
				jobB.ComputeID = ""
				h.jobsStore.CreateJob(context.Background(), job)
				h.jobsStore.CreateJob(context.Background(), jobB)

				actor := et.ActorWriter()
				pr := et.PullRequest(*et.UserCopilot())
				pr = et.AddCommentToPullRequest(pr, true, *actor)

				ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

				ctx := requestctx.AddData(context.Background(), &requestctx.CtxData{})
				err := h.OnWorkflowRunCompleted(ctx, WorkflowRunCompletedEvent{
					Repo:        EventWorkflowRepository(),
					WorkflowRun: EventWorkflowRunWithConclusion("failure"),
				})

				require.NoError(t, err)

				if tt.queuingEnabled {
					require.Len(t, executor.QueuedJobs(), 1)
					require.Len(t, executor.LaunchAgentCalls(), 0)

					require.Equal(t, et.ActorWriter().GetID(), executor.QueuedJobs()[0].ActorId)
				} else {
					require.Len(t, executor.QueuedJobs(), 0)
					require.Len(t, executor.LaunchAgentCalls(), 1)

					require.Equal(t, et.ActorWriter().GetID(), executor.LaunchAgentCalls()[0].ActorID)
				}
			})
		}
	})

	t.Run("ignores workflows not from integration", func(t *testing.T) {
		h, executor := NewDefaultTestHandler(obsv, ffClient, ghFactory)
		h.jobsStore.CreateAssignment(context.Background(), et.Assignment())
		h.jobsStore.CreateJob(context.Background(), et.Job(jobs.JobStatusCompleted))

		actor := et.ActorWriter()
		pr := et.PullRequest(*et.UserCopilot())
		pr = et.AddCommentToPullRequest(pr, true, *actor)

		ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

		run := EventWorkflowRun()
		run.Path = "dynamic/some-other-app/copilot"

		ctx := requestctx.AddData(context.Background(), &requestctx.CtxData{})
		err := h.OnWorkflowRunCompleted(ctx, WorkflowRunCompletedEvent{
			Repo:        EventWorkflowRepository(),
			WorkflowRun: run,
		})

		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 0)
	})

	t.Run("fails if not job matches workflow", func(t *testing.T) {
		h, executor := NewDefaultTestHandler(obsv, ffClient, ghFactory)
		h.jobsStore.CreateAssignment(context.Background(), et.Assignment())

		actor := et.ActorWriter()
		pr := et.PullRequest(*et.UserCopilot())
		pr = et.AddCommentToPullRequest(pr, true, *actor)

		ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

		ctx := requestctx.AddData(context.Background(), &requestctx.CtxData{})
		err := h.OnWorkflowRunCompleted(ctx, WorkflowRunCompletedEvent{
			Repo:        EventWorkflowRepository(),
			WorkflowRun: EventWorkflowRun(),
		})

		require.ErrorContains(t, err, "failed to find job for workflow run")
		require.Len(t, executor.QueuedJobs(), 0)
	})

	t.Run("updates job status to completed", func(t *testing.T) {
		h, executor := NewTestHandler(obsv, capiClient, ffClient, jobs.NewMemoryStore(), ghTwirp, ghFactory)
		h.jobsStore.CreateAssignment(context.Background(), et.Assignment())
		job := et.JobWithSessionID(jobs.JobStatusRunning, "session")
		h.jobsStore.CreateJob(context.Background(), job)

		actor := et.ActorWriter()
		pr := et.PullRequest(*et.UserCopilot())
		pr = et.AddCommentToPullRequest(pr, true, *actor)

		ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

		workflowRun := EventWorkflowRun()
		ctx := requestctx.AddData(context.Background(), &requestctx.CtxData{})
		err := h.OnWorkflowRunCompleted(ctx, WorkflowRunCompletedEvent{
			Repo:        EventWorkflowRepository(),
			WorkflowRun: workflowRun,
		})

		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 0)

		job, err = h.jobsStore.GetJob(context.Background(), job.OwnerID, job.RepoID, job.ID)
		require.NoError(t, err)
		require.Equal(t, jobs.JobStatusCompleted, job.Status)
		require.NotNil(t, job.CompletedAt)
		require.Equal(t, workflowRun.CompletedAt, job.CompletedAt)
	})

	t.Run("updates job status to cancelled", func(t *testing.T) {
		h, executor := NewTestHandler(obsv, capiClient, ffClient, jobs.NewMemoryStore(), ghTwirp, ghFactory)
		h.jobsStore.CreateAssignment(context.Background(), et.Assignment())
		job := et.JobWithSessionID(jobs.JobStatusRunning, "session")
		h.jobsStore.CreateJob(context.Background(), job)

		actor := et.ActorWriter()
		pr := et.PullRequest(*et.UserCopilot())
		pr = et.AddCommentToPullRequest(pr, true, *actor)

		ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

		workflowRun := EventWorkflowRunWithConclusion("cancelled")
		ctx := requestctx.AddData(context.Background(), &requestctx.CtxData{})
		err := h.OnWorkflowRunCompleted(ctx, WorkflowRunCompletedEvent{
			Repo:        EventWorkflowRepository(),
			WorkflowRun: workflowRun,
		})

		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 0)

		job, err = h.jobsStore.GetJob(context.Background(), job.OwnerID, job.RepoID, job.ID)
		require.NoError(t, err)
		require.Equal(t, jobs.JobStatusCancelled, job.Status)
		require.NotNil(t, job.CompletedAt)
		require.Equal(t, workflowRun.CompletedAt, job.CompletedAt)
	})

	t.Run("updates job status to failed, marks session as failed and requests review", func(t *testing.T) {
		testSessionID := "session-1"
		capiClient, jobStore, ghTwirp := commonHandlerDependencies()

		capiClient.MockGetSession = func(ctx context.Context, sessionID string) (*capi.Session, error) {
			return &capi.Session{
				ID:    sessionID,
				State: capi.SessionStateInProgress,
			}, nil
		}
		updateSessionCalled := false
		capiClient.MockUpdateSession = func(ctx context.Context, sessionID string, update capi.SessionUpdate) error {
			require.Equal(t, capi.SessionStateFailed, update.State)
			require.Equal(t, testSessionID, sessionID)
			updateSessionCalled = true
			return nil
		}

		gh := &github.NoopClient{
			MockRepositoryContext: &github.RepositoryContext{
				Repository: *et.Repo(),
			},
			MockIssueContext: &github.IssueContext{Repository: *et.Repo(), Issue: *et.Issue(*et.UserCopilot())},
			GithubAppLogin:   et.GitHubAppLogin,
			GithubBotLogin:   et.GitHubBotLogin,
		}
		ghFactory := &github.NoopClientFactory{
			GitHubAppLogin:   et.GitHubAppLogin,
			GitHubBotLogin:   et.GitHubBotLogin,
			MockIssueContext: &github.IssueContext{Repository: *et.Repo(), Issue: *et.Issue(*et.UserCopilot())},
			MockNewClientFromRepo: func(ctx context.Context, repoId, repoOwnerId int64) (github.ClientInterface, error) {
				return gh, nil
			},
		}

		h, executor := NewTestHandler(obsv, capiClient, ffClient, jobStore, ghTwirp, ghFactory)
		h.jobsStore.CreateAssignment(context.Background(), et.Assignment())
		job := et.Job(jobs.JobStatusRunning)
		job.SessionID = testSessionID
		h.jobsStore.CreateJob(context.Background(), job)

		actor := et.ActorWriter()
		pr := et.PullRequest(*et.UserCopilot())
		pr = et.AddCommentToPullRequest(pr, true, *actor)

		ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

		ctx := requestctx.AddData(context.Background(), &requestctx.CtxData{})
		err := h.OnWorkflowRunCompleted(ctx, WorkflowRunCompletedEvent{
			Repo:        EventWorkflowRepository(),
			WorkflowRun: EventWorkflowRunWithConclusion("failure"),
		})

		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 0)

		job, err = h.jobsStore.GetJob(context.Background(), job.OwnerID, job.RepoID, job.ID)
		require.NoError(t, err)
		require.Equal(t, jobs.JobStatusFailed, job.Status)
		require.NotNil(t, job.CompletedAt)

		require.True(t, updateSessionCalled, "expected update session to be called")

		require.Len(t, gh.MethodCalls, 1)
		var requestReviewersCall github.MethodCall
		for _, call := range gh.MethodCalls {
			if call.Method == "RequestReviewers" {
				requestReviewersCall = call
				break
			}
		}
		require.NotEmpty(t, requestReviewersCall)
		require.Len(t, requestReviewersCall.Args, 4)
		require.Equal(t, requestReviewersCall.Args[0], job.RepoOwner)
		require.Equal(t, requestReviewersCall.Args[1], job.RepoName)
		require.Equal(t, requestReviewersCall.Args[2], job.PullRequestNumber)
		require.Equal(t, requestReviewersCall.Args[3], []string{job.ActorLogin})
	})

	t.Run("launches pending job", func(t *testing.T) {
		h, executor := NewTestHandler(obsv, capiClient, ffClient, jobs.NewMemoryStore(), ghTwirp, ghFactory)
		h.jobsStore.CreateAssignment(context.Background(), et.Assignment())
		job := et.JobWithSessionID(jobs.JobStatusRunning, "session")
		jobB := et.Job(jobs.JobStatusPending)
		jobB.ComputeID = ""
		h.jobsStore.CreateJob(context.Background(), job)
		h.jobsStore.CreateJob(context.Background(), jobB)

		actor := et.ActorWriter()
		pr := et.PullRequest(*et.UserCopilot())
		pr = et.AddCommentToPullRequest(pr, true, *actor)

		ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

		ctx := requestctx.AddData(context.Background(), &requestctx.CtxData{})
		err := h.OnWorkflowRunCompleted(ctx, WorkflowRunCompletedEvent{
			Repo:        EventWorkflowRepository(),
			WorkflowRun: EventWorkflowRunWithConclusion("failure"),
		})

		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 1)

		job, err = h.jobsStore.GetJob(context.Background(), job.OwnerID, job.RepoID, job.ID)
		require.NoError(t, err)
		require.Equal(t, jobs.JobStatusFailed, job.Status)
		require.NotNil(t, job.CompletedAt)
	})

	t.Run("skips launching pending job on PRs that are closed", func(t *testing.T) {
		h, executor := NewTestHandler(obsv, capiClient, ffClient, jobs.NewMemoryStore(), ghTwirp, ghFactory)
		h.jobsStore.CreateAssignment(context.Background(), et.Assignment())
		job := et.JobWithSessionID(jobs.JobStatusRunning, "session")
		jobB := et.Job(jobs.JobStatusPending)
		jobB.ComputeID = ""
		h.jobsStore.CreateJob(context.Background(), job)
		h.jobsStore.CreateJob(context.Background(), jobB)

		actor := et.ActorWriter()
		pr := et.PullRequest(*et.UserCopilot())
		pr = et.AddCommentToPullRequest(pr, true, *actor)
		pr.Closed = true

		ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

		ctx := requestctx.AddData(context.Background(), &requestctx.CtxData{})
		err := h.OnWorkflowRunCompleted(ctx, WorkflowRunCompletedEvent{
			Repo:        EventWorkflowRepository(),
			WorkflowRun: EventWorkflowRunWithConclusion("failure"),
		})

		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 0)
	})

	t.Run("skips launching pending job on PRs not authored by Copilot", func(t *testing.T) {
		h, executor := NewTestHandler(obsv, capiClient, ffClient, jobs.NewMemoryStore(), ghTwirp, ghFactory)
		h.jobsStore.CreateAssignment(context.Background(), et.Assignment())
		job := et.JobWithSessionID(jobs.JobStatusRunning, "session")
		jobB := et.Job(jobs.JobStatusPending)
		jobB.ComputeID = ""
		h.jobsStore.CreateJob(context.Background(), job)
		h.jobsStore.CreateJob(context.Background(), jobB)

		actor := et.ActorWriter()
		pr := et.PullRequestWithAuthor(et.ActorAdmin(), *et.UserCopilot())
		pr = et.AddCommentToPullRequest(pr, true, *actor)

		ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

		ctx := requestctx.AddData(context.Background(), &requestctx.CtxData{})
		err := h.OnWorkflowRunCompleted(ctx, WorkflowRunCompletedEvent{
			Repo:        EventWorkflowRepository(),
			WorkflowRun: EventWorkflowRunWithConclusion("failure"),
		})

		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 0)
	})

	t.Run("skips launching pending job on PRs assigned to other users but not Copilot", func(t *testing.T) {
		h, executor := NewTestHandler(obsv, capiClient, ffClient, jobs.NewMemoryStore(), ghTwirp, ghFactory)
		h.jobsStore.CreateAssignment(context.Background(), et.Assignment())
		job := et.JobWithSessionID(jobs.JobStatusRunning, "session")
		jobB := et.Job(jobs.JobStatusPending)
		jobB.ComputeID = ""
		h.jobsStore.CreateJob(context.Background(), job)
		h.jobsStore.CreateJob(context.Background(), jobB)

		actor := et.ActorWriter()
		pr := et.PullRequest(*et.UserReader())
		pr = et.AddCommentToPullRequest(pr, true, *actor)

		ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

		ctx := requestctx.AddData(context.Background(), &requestctx.CtxData{})
		err := h.OnWorkflowRunCompleted(ctx, WorkflowRunCompletedEvent{
			Repo:        EventWorkflowRepository(),
			WorkflowRun: EventWorkflowRunWithConclusion("failure"),
		})

		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 0)
	})

	inferFromAnnotationTestCases := []struct {
		name                  string
		annotationMessage     string
		expectedSessionState  capi.SessionState
		expectedErrorContains string
		expectedJobStatus     jobs.JobStatus
		ffClient              featureflags.Client
		workflowRunConclusion string
		workflowJobConclusion string
	}{
		{
			name:                  "check annotation for out-of-disk error",
			annotationMessage:     "System.IO.IOException: No space left on device : at GitHub.Runner.Worker.Program.Main(String[] args)",
			expectedSessionState:  capi.SessionStateFailed,
			expectedErrorContains: "Copilot had to stop work due to insufficient disk space.",
			expectedJobStatus:     jobs.JobStatusFailed,
			ffClient:              ffClient,
			workflowRunConclusion: "failure",
			workflowJobConclusion: "failure",
		},
		{
			name:                  "check annotation for timed out error",
			annotationMessage:     "The job has exceeded the maximum execution time of 1m0s",
			expectedSessionState:  capi.SessionStateTimedOut,
			expectedErrorContains: "Copilot had to stop work due to a timeout.",
			expectedJobStatus:     jobs.JobStatusCancelled,
			ffClient: featureflags.NewNoopClient(map[string]bool{
				"agent_session_update_allow_timedout":           true,
				featureflags.CopilotSWEAgentOnlyWakeOnAtCopilot: true,
				featureflags.CopilotSWEAgentAqueductJobQueuing:  true,
			}),
			workflowRunConclusion: "cancelled",
			workflowJobConclusion: "cancelled",
		},
	}

	for _, tc := range inferFromAnnotationTestCases {
		t.Run(tc.name, func(t *testing.T) {
			testSessionID := "session-1"

			capiClient, jobStore, ghTwirp := commonHandlerDependencies()
			capiClient.MockGetSession = func(ctx context.Context, sessionID string) (*capi.Session, error) {
				return &capi.Session{
					ID:    sessionID,
					State: capi.SessionStateInProgress,
				}, nil
			}
			updateSessionCalled := false
			capiClient.MockUpdateSession = func(ctx context.Context, sessionID string, update capi.SessionUpdate) error {
				require.Equal(t, tc.expectedSessionState, update.State)
				require.Equal(t, testSessionID, sessionID)
				require.NotNil(t, update.Error)
				require.Contains(t, update.Error.Message, tc.expectedErrorContains)
				updateSessionCalled = true
				return nil
			}
			gh := &github.NoopClient{
				MockGetWorkflowJobByID: func(ctx context.Context, owner, repo string, jobID int64) (*gg.WorkflowJob, error) {
					return &gg.WorkflowJob{
						ID:         &jobID,
						Conclusion: &tc.workflowJobConclusion,
					}, nil
				},
				MockListCheckRunAnnotations: func(ctx context.Context, owner, repo string, jobID int64) ([]*gg.CheckRunAnnotation, error) {
					return []*gg.CheckRunAnnotation{
						{
							AnnotationLevel: gg.Ptr("failure"),
							Message:         &tc.annotationMessage,
						},
					}, nil
				},
			}
			ghFactory := &github.NoopClientFactory{
				MockNewClient: func(token string) (github.ClientInterface, error) {
					return gh, nil
				},
			}
			h, executor := NewTestHandler(obsv, capiClient, ffClient, jobStore, ghTwirp, ghFactory)
			h.jobsStore.CreateAssignment(context.Background(), et.Assignment())
			job := et.Job(jobs.JobStatusRunning)
			job.SessionID = testSessionID
			h.jobsStore.CreateJob(context.Background(), job)

			actor := et.ActorWriter()
			pr := et.PullRequest(*et.UserCopilot())
			pr = et.AddCommentToPullRequest(pr, true, *actor)

			ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

			ctx := requestctx.AddData(context.Background(), &requestctx.CtxData{})
			err := h.OnWorkflowRunCompleted(ctx, WorkflowRunCompletedEvent{
				Repo:        EventWorkflowRepository(),
				WorkflowRun: EventWorkflowRunWithConclusion(tc.workflowRunConclusion),
			})

			require.NoError(t, err)
			require.Len(t, executor.QueuedJobs(), 0)

			job, err = h.jobsStore.GetJob(context.Background(), job.OwnerID, job.RepoID, job.ID)
			require.NoError(t, err)
			require.Equal(t, tc.expectedJobStatus, job.Status)
			require.NotNil(t, job.CompletedAt)

			require.True(t, updateSessionCalled, "expected update session to be called")
		})
	}

	cancellationTestCases := []struct {
		name                  string
		annotation            string
		expectedMetricEmitted bool
		ffClient              featureflags.Client
	}{
		{
			name:                  "user cancellation should be ignored",
			annotation:            "The run was canceled by @monalisa.",
			expectedMetricEmitted: false,
			ffClient:              ffClient,
		},
		{
			name:                  "timeout cancellation should be ignored",
			annotation:            "The job has exceeded the maximum execution time of",
			expectedMetricEmitted: false,
			ffClient: featureflags.NewNoopClient(map[string]bool{
				"agent_session_update_allow_timedout":           true,
				featureflags.CopilotSWEAgentOnlyWakeOnAtCopilot: true,
				featureflags.CopilotSWEAgentAqueductJobQueuing:  true,
			}),
		},
		{
			name:                  "non-user cancellation should be tracked",
			annotation:            "The job was not acquired by Runner of type hosted even after multiple attempts",
			expectedMetricEmitted: true,
			ffClient:              ffClient,
		},
	}
	for _, tc := range cancellationTestCases {
		t.Run(tc.name, func(t *testing.T) {
			testSessionID := "session-1"
			cancelled := "cancelled"
			failure := "failure"
			annotationMessage := tc.annotation

			counterCalled := false
			obsv.Statter = &observability.MockStatter{
				MockCounter: func(key string, tags stats.Tags, value int64) {
					require.Equal(t, "dynamic_workflow_unknown_cancellation", key)
					require.Equal(t, int64(1), value)
					counterCalled = true
				},
			}
			capiClient, jobStore, ghTwirp := commonHandlerDependencies()
			capiClient.MockGetSession = func(ctx context.Context, sessionID string) (*capi.Session, error) {
				return &capi.Session{
					ID:    sessionID,
					State: capi.SessionStateInProgress,
				}, nil
			}
			gh := &github.NoopClient{
				MockGetWorkflowJobByID: func(ctx context.Context, owner, repo string, jobID int64) (*gg.WorkflowJob, error) {
					return &gg.WorkflowJob{
						ID:         &jobID,
						Conclusion: &cancelled,
					}, nil
				},
				MockListCheckRunAnnotations: func(ctx context.Context, owner, repo string, jobID int64) ([]*gg.CheckRunAnnotation, error) {
					return []*gg.CheckRunAnnotation{
						{
							AnnotationLevel: &failure,
							Message:         &annotationMessage,
						},
					}, nil
				},
			}
			ghFactory := &github.NoopClientFactory{
				MockNewClient: func(token string) (github.ClientInterface, error) {
					return gh, nil
				},
			}
			h, executor := NewTestHandler(obsv, capiClient, tc.ffClient, jobStore, ghTwirp, ghFactory)
			h.jobsStore.CreateAssignment(context.Background(), et.Assignment())
			job := et.Job(jobs.JobStatusRunning)
			job.SessionID = testSessionID
			h.jobsStore.CreateJob(context.Background(), job)

			actor := et.ActorWriter()
			pr := et.PullRequest(*et.UserCopilot())
			pr = et.AddCommentToPullRequest(pr, true, *actor)

			ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

			ctx := requestctx.AddData(context.Background(), &requestctx.CtxData{})
			err := h.OnWorkflowRunCompleted(ctx, WorkflowRunCompletedEvent{
				Repo:        EventWorkflowRepository(),
				WorkflowRun: EventWorkflowRunWithConclusion(failure),
			})

			require.NoError(t, err)
			require.Len(t, executor.QueuedJobs(), 0)

			job, err = h.jobsStore.GetJob(context.Background(), job.OwnerID, job.RepoID, job.ID)
			require.NoError(t, err)
			require.Equal(t, jobs.JobStatusFailed, job.Status)
			require.NotNil(t, job.CompletedAt)

			require.Equal(t, tc.expectedMetricEmitted, counterCalled)
		})
	}
}
