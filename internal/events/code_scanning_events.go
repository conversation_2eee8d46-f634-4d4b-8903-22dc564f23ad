package events

type CodeScanningAlertsAssignmentEvent struct {
	Actor     *Actor
	Repo      *Repository
	Alerts    []*CodeScanningAlert
	Assignees []string
	EventUrl  string
}

type CodeScanningAlert struct {
	Number       int64
	Title        string
	Url          string
	SuggestedFix *SuggestedFix
}
type SuggestedFix struct {
	Files []*SuggestedFixFile
}
type SuggestedFixFile struct {
	FilePath    string
	DiffContent []byte
}
