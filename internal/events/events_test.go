package events

import (
	"fmt"
	"testing"
	"time"

	"github.com/github/sweagentd/internal/capi"
	et "github.com/github/sweagentd/internal/events/eventstest"
	"github.com/github/sweagentd/internal/experiments"
	"github.com/github/sweagentd/internal/featureflags"
	gh "github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/githubtwirp"
	"github.com/github/sweagentd/internal/jobexecutor"
	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/observability"
	"github.com/github/sweagentd/internal/requestctx"
	"github.com/stretchr/testify/require"
)

// EventActor returns a new [Actor] with the from a given [GQLActorOrUser].
func EventActor(a gh.GQLActorOrUser) *Actor {
	return &Actor{
		ID:     a.GetID(),
		NodeID: a.GetNodeID(),
		Login:  a.GetLogin(),
		Type:   a.GetType(),
	}
}

func eventActor(login string) *Actor {
	id, nodeID := et.IDsFromString(login)
	actor := &Actor{
		ID:     id,
		NodeID: nodeID,
		Login:  login,
	}

	if login == et.GitHubBotLogin || login == et.ActorLoginBot {
		actor.Type = "Bot"
	} else {
		actor.Type = "User"
	}

	return actor
}

// EventActorCopilot is a convenience function to return an [Actor] for Copilot.
func EventActorCopilot() *Actor {
	return eventActor(et.GitHubBotLogin)
}

// EventActorBot is a convenience function to return an [Actor] for a bot.
func EventActorBot() *Actor {
	return eventActor(et.GitHubBotLogin)
}

// EventActorAdmin is a convenience function to return an [Actor] with admin permissions.
func EventActorAdmin() *Actor {
	return eventActor(et.ActorLoginAdmin)
}

// EventActorWriter is a convenience function to return an [Actor] with write permissions.
func EventActorWriter() *Actor {
	return eventActor(et.ActorLoginWriter)
}

// EventActorReader is a convenience function to return an [Actor] with read permissions.
func EventActorReader() *Actor {
	return eventActor(et.ActorLoginReader)
}

// EventActorNone is a convenience function to return an [Actor] with no permissions.
func EventActorNone() *Actor {
	return eventActor(et.ActorLoginNone)
}

// EventRepository returns a new [Repository] that can be used across tests.
// The IDs are derived from the repo name and owner, and will be consistent across
// calls and other types representing the same repo.
func EventRepository() *Repository {
	id, nodeID := et.IDsFromString(et.RepoName)
	ownerID, _ := et.IDsFromString(et.RepoOwner)
	return &Repository{
		ID:        id,
		NodeID:    nodeID,
		Name:      et.RepoName,
		OwnerID:   ownerID,
		IsPrivate: true,
	}
}

// EventIssue returns a new [Issue] using the default issue number.
func EventIssue() *Issue {
	return EventIssueWithNumber(et.IssueNumber)
}

// EventIssueWithNumber returns a new [Issue] using the given issue number.
func EventIssueWithNumber(number int) *Issue {
	id, nodeID := et.IDsFromStringAndInt(et.NameForIDsIssue, number)
	return &Issue{
		ID:     id,
		NodeID: nodeID,
		Number: number,
		IsOpen: true,
	}
}

// EventPullRequest returns a new [PullRequest] using the default pull request number.
func EventPullRequest() *PullRequest {
	return EventPullRequestWithNumber(et.PullRequestNumber)
}

// EventPullRequestWithNumber returns a new [PullRequest] using the given pull request number.
func EventPullRequestWithNumber(number int) *PullRequest {
	id, nodeID := et.IDsFromStringAndInt(et.NameForIDsPullRequest, number)
	draft := false
	return &PullRequest{
		ID:      &id,
		NodeID:  nodeID,
		Number:  number,
		IsOpen:  true,
		IsDraft: &draft,
	}
}

// EventPullRequestReview returns a new [PullRequestReview] attributed to the given author.
func EventPullRequestReview(author gh.GQLActorOrUser) *PullRequestReview {
	id, _ := et.IDsFromStrings(et.NameForIDsPullRequestReview, author.GetLogin())
	return &PullRequestReview{
		ID: id,
	}
}

// EventPullRequestReviewForPullRequest returns a new [PullRequestComment] attributed to the given author.
func EventPullRequestComment(author gh.GQLActorOrUser) *PullRequestComment {
	id, nodeID := et.IDsFromStrings(et.NameForIDsIssueComment, author.GetLogin())
	return &PullRequestComment{
		ID:     id,
		NodeID: nodeID,
	}
}

// EventPullRequestReviewComment returns a new [PullRequestComment] attributed to the given author.
func EventPullRequestReviewComment(author gh.GQLActorOrUser) *PullRequestComment {
	id, nodeID := et.IDsFromStrings(et.NameForIDsPullRequestReviewComment, author.GetLogin())
	return &PullRequestComment{
		ID:     id,
		NodeID: nodeID,
	}
}

// EventWorkflowRepository returns a new [WorkflowRepository] using the default repo name and owner.
func EventWorkflowRepository() *WorkflowRepository {
	id, _ := et.IDsFromString(et.RepoName)
	ownerID, _ := et.IDsFromString(et.RepoOwner)
	return &WorkflowRepository{
		ID:      id,
		OwnerID: ownerID,
	}
}

// EventWorkflowRun returns a new [WorkflowRun] using the default workflow run number.
func EventWorkflowRun() *WorkflowRun {
	return EventWorkflowRunWithNumber(et.PullRequestNumber)
}

// EventWorkflowRunWithNumber returns a new [WorkflowRun] using the given workflow run number.
func EventWorkflowRunWithNumber(number int) *WorkflowRun {
	return EventWorkflowRunWithNumberAndConclusion(number, "success")
}

// EventWorkflowJobWithConclusion returns a new [WorkflowJob] using the given conclusion.
func EventWorkflowRunWithConclusion(conclusion string) *WorkflowRun {
	return EventWorkflowRunWithNumberAndConclusion(et.PullRequestNumber, conclusion)
}

// EventWorkflowRunWithNumber returns a new [WorkflowRun] using the given workflow run number.
func EventWorkflowRunWithNumberAndConclusion(number int, conclusion string) *WorkflowRun {
	id, _ := et.IDsFromStringAndInt(et.NameForIDsWorkflowRun, number)
	cID, _ := et.IDsFromString(et.GitHubBotLogin)
	startedAt := time.Now().Add(-time.Minute * 30)
	completedAt := time.Now()
	jobID := int64(1)

	return &WorkflowRun{
		ID:           id,
		UserID:       cID,
		Path:         fmt.Sprintf("dynamic/%s/copilot", et.GitHubAppLogin),
		Status:       WorkflowCompletedEventStatusCompleted,
		Conclusion:   conclusion,
		CheckSuiteID: 0,
		Attempt:      1,
		StartedAt:    startedAt.Unix(),
		CompletedAt:  completedAt.Unix(),
		UsageMs:      completedAt.Sub(startedAt).Milliseconds(),
		JobID:        &jobID,
	}
}

func EventCodeScanningAlert() *CodeScanningAlert {
	sf_file := &SuggestedFixFile{
		FilePath:    "test.txt",
		DiffContent: []byte("diff content"),
	}
	sf := &SuggestedFix{
		Files: []*SuggestedFixFile{
			sf_file,
		},
	}
	alert := &CodeScanningAlert{
		Number:       123,
		SuggestedFix: sf,
	}
	return alert
}

func commonHandlerDependencies() (capiClient *capi.NoopClient, jobStore *jobs.MemoryStore, ghTwirp *githubtwirp.NoopClient) {
	return capi.NewNoopClientWithFixedShouldWakeResponse(true),
		jobs.NewMemoryStore(),
		githubtwirp.NewNoopClient()
}

func NewTestHandler(obsv observability.Exporters, capiClient capi.Client, ffClient featureflags.Client, jobsStore jobs.JobsStore, ghTwirp githubtwirp.ClientInterface, ghFactory gh.ClientFactoryInterface) (*Handler, *jobexecutor.TestJobExecutor) {
	if ffClient == nil {
		ffClient = featureflags.NewNoopClient(nil)
	}
	if jobsStore == nil {
		jobsStore = jobs.NewMemoryStore()
	}
	if ghFactory == nil {
		ghFactory = &gh.NoopClientFactory{
			GitHubAppLogin: et.GitHubAppLogin,
			GitHubBotLogin: et.GitHubBotLogin,
		}
	}
	if ghTwirp == nil {
		ghTwirp = githubtwirp.NewNoopClient()
	}
	if capiClient == nil {
		capiClient = capi.NewNoopClientWithFixedShouldWakeResponse(true)
	}

	executor := jobexecutor.NewTestJobExecutor(obsv, ffClient, jobsStore, ghFactory, ghTwirp, capiClient)
	handler := NewHandler(obsv, capiClient, ffClient, executor, jobsStore, ghTwirp, ghFactory, experiments.NewNoopClient())
	return handler, executor
}

func NewDefaultTestHandler(obsv observability.Exporters, ffClient featureflags.Client, ghFactory gh.ClientFactoryInterface) (*Handler, *jobexecutor.TestJobExecutor) {
	capiClient, jobStore, ghTwirp := commonHandlerDependencies()
	return NewTestHandler(obsv, capiClient, ffClient, jobStore, ghTwirp, ghFactory)
}

func TestCreatePendingJob(t *testing.T) {
	assignment := et.Assignment()

	repo := et.Repo()

	t.Run("creates new pending job for pull request comment", func(t *testing.T) {
		actor := et.ActorWriter()
		pr := et.PullRequest()
		pr = et.AddCommentToPullRequest(pr, true, *actor)

		require.Len(t, pr.Comments.Nodes, 1)

		comment := pr.Comments.Nodes[0]

		job := assignment.NewJob(jobs.AgentActionFixPRComment, &requestctx.UserInfo{
			ID:         uint64(comment.Author.GetID()),
			Login:      comment.Author.GetLogin(),
			TrackingID: "tracking-id",
		}, pr.HeadRef.Name)
		require.NotNil(t, job)

		require.Equal(t, assignment.ID, job.AssignmentID)
		require.Equal(t, repo.ID, job.RepoID)
		require.Equal(t, repo.Owner.GetID(), job.OwnerID)
		require.Equal(t, repo.Name, job.RepoName)
		require.Equal(t, repo.Owner.Login, job.RepoOwner)
		require.Equal(t, repo.Name, job.RepoName)
		require.Equal(t, jobs.AgentActionFixPRComment, job.Action)
		require.Equal(t, jobs.JobStatusPending, job.Status)

		require.Equal(t, actor.GetLogin(), job.ActorLogin)
		require.Equal(t, actor.GetID(), job.ActorID)
		require.Equal(t, pr.HeadRef.Name, job.BaseCommit)
	})

	t.Run("creates new pending job for pull request review", func(t *testing.T) {
		actor := et.ActorWriter()
		pr := et.PullRequest()
		pr = et.AddReviewToPullRequest(pr, true, *actor, *et.ActorWriter())

		require.Len(t, pr.Reviews.Nodes, 1)
		require.Len(t, pr.Reviews.Nodes[0].Comments.Nodes, 1)

		review := pr.Reviews.Nodes[0]

		job := assignment.NewJob(jobs.AgentActionFixPRComment, &requestctx.UserInfo{
			ID:         uint64(review.Author.GetID()),
			Login:      review.Author.GetLogin(),
			TrackingID: "tracking-id",
		}, pr.HeadRef.Name)
		require.NotNil(t, job)

		require.Equal(t, assignment.ID, job.AssignmentID)
		require.Equal(t, repo.ID, job.RepoID)
		require.Equal(t, repo.Owner.GetID(), job.OwnerID)
		require.Equal(t, repo.Name, job.RepoName)
		require.Equal(t, repo.Owner.Login, job.RepoOwner)
		require.Equal(t, repo.Name, job.RepoName)
		require.Equal(t, jobs.AgentActionFixPRComment, job.Action)
		require.Equal(t, jobs.JobStatusPending, job.Status)

		require.Equal(t, actor.GetLogin(), job.ActorLogin)
		require.Equal(t, actor.GetID(), job.ActorID)
		require.Equal(t, pr.HeadRef.Name, job.BaseCommit)
	})

	t.Run("creates new pending job for issue", func(t *testing.T) {
		actor := EventActorWriter()
		job := assignment.NewJob(jobs.AgentActionFix, &requestctx.UserInfo{
			ID:         uint64(actor.ID),
			Login:      actor.Login,
			TrackingID: "tracking-id",
		}, repo.DefaultBranchRef.Name)

		require.NotNil(t, job)

		require.Equal(t, assignment.ID, job.AssignmentID)
		require.Equal(t, repo.ID, job.RepoID)
		require.Equal(t, repo.Owner.GetID(), job.OwnerID)
		require.Equal(t, repo.Name, job.RepoName)
		require.Equal(t, repo.Owner.Login, job.RepoOwner)
		require.Equal(t, repo.Name, job.RepoName)
		require.Equal(t, jobs.AgentActionFix, job.Action)
		require.Equal(t, jobs.JobStatusPending, job.Status)

		require.Equal(t, actor.Login, job.ActorLogin)
		require.Equal(t, actor.ID, job.ActorID)
		require.Empty(t, job.BranchName)
		require.Equal(t, repo.DefaultBranchRef.Name, job.BaseCommit)
	})
}
