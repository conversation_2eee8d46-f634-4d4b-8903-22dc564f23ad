package events

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/github/github-telemetry-go/kvp"
	"github.com/github/go-stats"
	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/joberrors"
	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/jobutils"
	"github.com/github/sweagentd/internal/problemstatement"
	"github.com/github/sweagentd/internal/requestctx"
)

func (h *Handler) OnWorkflowRunCompleted(ctx context.Context, event WorkflowRunCompletedEvent) error {
	repo := event.Repo
	run := event.WorkflowRun

	logger := h.obsv.LoggerWithTelemetry(ctx)

	// short circuit if the workflow run is not for the copilot-swe-agent
	if !strings.HasPrefix(run.Path, "dynamic/copilot-swe-agent/") {
		logger.Info("Ignoring workflow run because it is not for the copilot-swe-agent")
		return nil
	}

	computeID := strconv.FormatInt(run.ID, 10)
	job, err := h.jobsStore.GetJobForComputeID(ctx, repo.OwnerID, repo.ID, computeID)
	if err != nil {
		return fmt.Errorf("getting job for workflow run %w", err)
	}

	// We should always have a job for dynamic/copilot-swe-agent workflows runs
	if job == nil {
		return fmt.Errorf("failed to find job for workflow run")
	}

	jobutils.UpdateRequestContextJob(ctx, h.obsv, job)
	if err := requestctx.AddPullRequest(ctx, requestctx.CtxPullRequest{
		ID:     &job.PullRequestID,
		Number: job.PullRequestNumber,
	}); err != nil {
		h.obsv.LoggerWithTelemetry(ctx).WithError(err).Error("failed to add pull request to context")
	}

	session, sessionClient, err := jobutils.GetSessionForJob(ctx, h.obsv, h.capiClient, h.ghTwirp, job)
	if err != nil {
		return fmt.Errorf("failed to get session for job: %w", err)
	}

	rateLimited := session.Error != nil && (session.Error.Code == joberrors.ErrorCAPIRateLimited.String() || session.Error.Code == "api: 429")
	tags := h.obsv.TelemetryTags(ctx).Merge(stats.Tags{
		"workflow_run_conclusion": run.Conclusion,
		"rate_limited":            strconv.FormatBool(rateLimited),
	})
	h.obsv.Statter.DistributionMs("dynamic_workflow_completed", tags, time.Duration(run.UsageMs)*time.Millisecond)

	if err := jobutils.FinalizeJobForWorkflow(ctx, h.obsv, h.ffClient, h.jobsStore, job, run.Conclusion, run.CompletedAt); err != nil {
		return fmt.Errorf("failed to finalize job: %w", err)
	}

	gh, err := h.ghFactory.NewClientFromRepo(ctx, event.Repo.ID, event.Repo.OwnerID)
	if err != nil {
		return fmt.Errorf("failed to create GitHub client: %w", err)
	}

	// TODO(colbylwilliams): For now, we don't want to fail the handler if we
	// can't finalize the session since we're still obligated to handle pending
	// jobs for the assignment. So we'll just log the error and move on
	finalizeSession := jobutils.FinalizeSessionParams{
		Gh:            gh,
		Job:           job,
		WorkflowJob:   nil,
		WorkflowJobID: run.JobID,
		SessionClient: sessionClient,
	}
	if err := jobutils.FinalizeSessionForWorkflow(ctx, h.obsv, finalizeSession); err != nil {
		h.obsv.LogAndReportError(ctx, err, "failed to finalize session")
	}

	actx, err := gh.GetPullRequestContext(ctx, job.RepoNodeID, job.PullRequestNumber)
	if err != nil {
		return fmt.Errorf("failed to get pull request context: %w", err)
	}

	// Remove the 👀 reactions from all comments and reviews for the job
	jobutils.RemoveReactionsForJob(ctx, h.obsv, gh, actx, job)

	return h.handlePendingJobsForAssignment(ctx, gh, job, actx)
}

func (h *Handler) handlePendingJobsForAssignment(ctx context.Context, gh github.ClientInterface, job *jobs.Job, actx *github.PullRequestContext) error {
	ctx, span := h.obsv.Tracer.Start(ctx, "events.Handler.handlePendingJobsForAssignment")
	defer span.End()

	assignment, err := h.jobsStore.GetAssignment(ctx, job.OwnerID, job.RepoID, job.AssignmentID)
	if err != nil {
		return fmt.Errorf("failed to get assignment: %w", err)
	}

	logger := h.obsv.LoggerWithTelemetry(ctx)

	allJobs, pendingJob, err := jobutils.JobsForAssignment(ctx, h.obsv, h.ffClient, gh, h.capiClient, h.ghTwirp, h.jobsStore, assignment)
	if err != nil {
		return fmt.Errorf("failed to get jobs for assignment: %w", err)
	}

	if pendingJob == nil {
		logger.Info("No pending job to process")
		return nil
	}

	jobutils.UpdateRequestContextJob(ctx, h.obsv, pendingJob)

	if has, runningJobID := jobs.HasRunningJobs(allJobs); has {
		// TODO(colbylwilliams): We should only get here after a job reaches a
		// terminal state. Since only one job can be running at a time, if we got
		// here that's a bug. Log the error so we can investigate.
		logger.Error("Running job found after job completed for assignment",
			kvp.String("running_job_id", runningJobID))
		return nil
	}

	if actx == nil {
		actx, err = gh.GetPullRequestContext(ctx, job.RepoNodeID, job.PullRequestNumber)
		if err != nil {
			return fmt.Errorf("failed to get pull request context: %w", err)
		}
	}

	pr := actx.PullRequest

	// Make sure the PR is still open and the author is Copilot
	if pr.IsClosed() || !pr.Author.IsCopilot(gh) {
		logger.Info("Not processing pending jobs because PR is closed or not authored by Copilot")
		return nil
	}

	// Make sure the PR is still assigned to Copilot
	if !pr.IsCopilotAssigned(gh) {
		logger.Info("Not processing pending jobs because PR is not assigned to Copilot")
		return nil
	}

	pendingJob, err = jobutils.ProcessPendingJob(ctx, gh, actx, allJobs, pendingJob)
	if err != nil {
		return fmt.Errorf("failed to process pending job: %w", err)
	}

	// Reload the logger with any job fields that may have changed
	jobutils.UpdateRequestContextJob(ctx, h.obsv, pendingJob)
	logger = h.obsv.LoggerWithTelemetry(ctx)

	logger.Info("Processing pending job for pull request")

	problemStatement, err := problemstatement.NewPullRequestBuilder(h.obsv, gh, actx, pendingJob).Build(ctx)
	if err != nil {
		return fmt.Errorf("failed to create problem statement: %w", err)
	}
	pendingJob.ProblemStatement = problemStatement

	pendingJob, err = h.jobsStore.UpsertJob(ctx, pendingJob)
	if err != nil {
		return fmt.Errorf("failed to update job: %w", err)
	}

	jobutils.UpdateRequestContextJob(ctx, h.obsv, pendingJob)
	logger = h.obsv.LoggerWithTelemetry(ctx)

	logger.Info("Job upserted for pull request")

	logger.Info("Executing pending job for pull request")

	if err := h.jobExecutor.QueueJob(ctx, gh, pendingJob); err != nil {
		return fmt.Errorf("failed to queue job: %w", err)
	}

	return nil
}
