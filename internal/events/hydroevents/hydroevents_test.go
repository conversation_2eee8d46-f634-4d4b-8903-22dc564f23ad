package hydroevents

import (
	"context"
	"errors"
	"fmt"
	"testing"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore/to"
	hydro_pb "github.com/github/hydro-client-go/v7/generated/hydro/v1"
	hydro_lib "github.com/github/hydro-client-go/v7/pkg/hydro"

	githubv1 "github.com/github/hydro-schemas-go/hydro/schemas/github/v1"
	"github.com/github/hydro-schemas-go/hydro/schemas/github/v1/entities"
	"github.com/github/sweagentd/internal/config"
	"github.com/github/sweagentd/internal/events"
	et "github.com/github/sweagentd/internal/events/eventstest"
	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/freno"
	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/githubtwirp"
	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/jobutils"
	"github.com/github/sweagentd/internal/observability"
	"github.com/github/sweagentd/internal/requestctx"
	sweagentdTwirp "github.com/github/sweagentd/proto/sweagentd/v1"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/proto"
)

type SyncQueue struct {
	h events.EventsHandler
}

func NewSyncQueue(t *testing.T, h events.EventsHandler) *SyncQueue {
	t.Helper()
	return &SyncQueue{h}
}

func (s *SyncQueue) Enqueue(ctx context.Context, event any, eventId string) error {
	// The actual async queue implementation will not bubble up errors from the Process, so we will return nil here to mimic that behavior
	s.h.Process(ctx, event)
	return nil
}

func (s *SyncQueue) Start(ctx context.Context) error {
	return nil
}

func TestDispatcher(t *testing.T) {
	obsv := observability.NewNoopExporters()
	topic := IssueUpdateAssigneeTopic

	t.Run("returns nil if the dispatcher returns any error", func(t *testing.T) {
		ctx := requestctx.AddData(context.Background(), &requestctx.CtxData{})
		ffClient := featureflags.NewNoopClient(nil)
		ghFactory := &github.NoopClientFactory{}

		didRun := false
		h := &events.NoopHandler{
			MockProcess: func(ctx context.Context, event any) error {
				didRun = true
				return fmt.Errorf("process bombed")
			},
			MockOnIssueAssigned: func(ctx context.Context, event events.IssueAssignmentEvent) error {
				didRun = true
				return errors.New("test")
			},
		}

		queue := NewSyncQueue(t, h)
		noopClient := githubtwirp.NewNoopClient()
		noopClient.NoopSweagentdAPI.ConfigurationForRepositoryResponse = to.Ptr(sweagentdTwirp.ConfigurationForRepositoryResponse{
			IsSweagentdEnabled: true,
		})
		e := NewDispatcher(obsv, ffClient, ghFactory, h, queue, freno.NewFrenoClient(obsv, "", ""), noopClient, config.EnvironmentDevelopment)

		msg := newHydroMessage(t, topic, &githubv1.IssueUpdateAssignee{
			Action:     "issue.events.assigned",
			Actor:      et.EntitiesUserWriter(),
			Repository: et.EntitiesRepository(),
			Issue:      et.EntitiesIssue(),
			Assignees:  []*entities.User{et.EntitiesUserReader()},
		})

		err := e.Dispatch(ctx, msg)
		require.NoError(t, err)
		require.True(t, didRun)
	})

	t.Run("comments on the assignment target if the error is unhandled", func(t *testing.T) {
		ctx := requestctx.AddData(context.Background(), &requestctx.CtxData{})

		obsv := observability.NewNoopExporters()
		gh := &github.NoopClient{}
		ghFactory := &github.NoopClientFactory{
			MockNewClientFromRepo: func(ctx context.Context, repoId, repoOwnerId int64) (github.ClientInterface, error) {
				return gh, nil
			},
		}

		didRun := false
		h := &events.NoopHandler{
			MockObservability:       obsv,
			MockGitHubClientFactory: ghFactory,
			MockOnIssueAssigned: func(ctx context.Context, event events.IssueAssignmentEvent) error {
				didRun = true
				return jobutils.NewUnhandledErrorWithIssueComment(ctx, errors.New("test"), &jobs.Assignment{
					RepoOwner: "owner",
					RepoName:  "name",
				}, true, "actor", 1)
			},
		}
		queue := NewSyncQueue(t, h)
		noopClient := githubtwirp.NewNoopClient()
		noopClient.NoopSweagentdAPI.ConfigurationForRepositoryResponse = to.Ptr(sweagentdTwirp.ConfigurationForRepositoryResponse{
			IsSweagentdEnabled: true,
		})
		e := NewDispatcher(obsv, featureflags.NewNoopClient(nil), ghFactory, h, queue, freno.NewFrenoClient(obsv, "", ""), noopClient, config.EnvironmentDevelopment)

		msg := newHydroMessage(t, IssueUpdateAssigneeTopic, &githubv1.IssueUpdateAssignee{
			Action:     "issue.events.assigned",
			Actor:      et.EntitiesUserWriter(),
			Repository: et.EntitiesRepository(),
			Issue:      et.EntitiesIssue(),
			Assignees:  []*entities.User{et.EntitiesUserReader()},
		})

		err := e.Dispatch(ctx, msg)
		require.True(t, didRun)
		require.NoError(t, err)

		require.Len(t, gh.MethodCalls, 1)
		require.Equal(t, "CreateComment", gh.MethodCalls[0].Method)
		commentBody := gh.MethodCalls[0].Args[2]
		require.Contains(t, commentBody, "@actor Unfortunately I hit an unexpected error while trying to work on issue #")
	})
}

func TestDispatcher_IssueUpdateAssignee(t *testing.T) {
	ghFactory := &github.NoopClientFactory{}
	obsv := observability.NewNoopExporters()
	ffClient := featureflags.NewNoopClient(nil)
	topic := IssueUpdateAssigneeTopic

	t.Run("executes the right event handler for assigned", func(t *testing.T) {
		ctx := requestctx.AddData(context.Background(), &requestctx.CtxData{})
		didRun := false

		h := &events.NoopHandler{
			MockOnIssueAssigned: func(ctx context.Context, event events.IssueAssignmentEvent) error {
				didRun = true
				return nil
			},
		}
		queue := NewSyncQueue(t, h)
		noopClient := githubtwirp.NewNoopClient()
		noopClient.NoopSweagentdAPI.ConfigurationForRepositoryResponse = to.Ptr(sweagentdTwirp.ConfigurationForRepositoryResponse{
			IsSweagentdEnabled: true,
		})
		e := NewDispatcher(obsv, ffClient, ghFactory, h, queue, freno.NewFrenoClient(obsv, "", ""), noopClient, config.EnvironmentDevelopment)

		msg := newHydroMessage(t, topic, &githubv1.IssueUpdateAssignee{
			Action:     "issue.events.assigned",
			Actor:      et.EntitiesUserWriter(),
			Repository: et.EntitiesRepository(),
			Issue:      et.EntitiesIssue(),
			Assignees:  []*entities.User{et.EntitiesUserReader()},
		})

		err := e.Dispatch(ctx, msg)
		require.NoError(t, err)
		require.True(t, didRun)
	})

	t.Run("execution is gated on repo enablement when FF is enabled", func(t *testing.T) {
		ctx := requestctx.AddData(context.Background(), &requestctx.CtxData{})
		didRun := false

		h := &events.NoopHandler{
			MockOnIssueAssigned: func(ctx context.Context, event events.IssueAssignmentEvent) error {
				didRun = true
				return nil
			},
		}
		queue := NewSyncQueue(t, h)

		responseToReturn := to.Ptr(sweagentdTwirp.ConfigurationForRepositoryResponse{
			IsSweagentdEnabled: false,
		})

		noopClient := githubtwirp.NewNoopClient()
		noopClient.NoopSweagentdAPI.ConfigurationForRepositoryResponse = responseToReturn
		e := NewDispatcher(obsv, ffClient, ghFactory, h, queue, freno.NewFrenoClient(obsv, "", ""), noopClient, config.EnvironmentDevelopment)

		msg := newHydroMessage(t, topic, &githubv1.IssueUpdateAssignee{
			Action:     "issue.events.assigned",
			Actor:      et.EntitiesUserWriter(),
			Repository: et.EntitiesRepository(),
			Issue:      et.EntitiesIssue(),
			Assignees:  []*entities.User{et.EntitiesUserReader()},
		})

		err := e.Dispatch(ctx, msg)
		require.NoError(t, err)
		require.False(t, didRun)

		responseToReturn.IsSweagentdEnabled = true
		err = e.Dispatch(ctx, msg)
		require.NoError(t, err)
		require.True(t, didRun)

	})

	t.Run("executes the right event handler for unassigned", func(t *testing.T) {
		ctx := requestctx.AddData(context.Background(), &requestctx.CtxData{})
		didRun := false
		h := &events.NoopHandler{
			MockOnIssueUnassigned: func(ctx context.Context, event events.IssueAssignmentEvent) error {
				didRun = true
				return nil
			},
		}
		queue := NewSyncQueue(t, h)
		noopClient := githubtwirp.NewNoopClient()
		noopClient.NoopSweagentdAPI.ConfigurationForRepositoryResponse = to.Ptr(sweagentdTwirp.ConfigurationForRepositoryResponse{
			IsSweagentdEnabled: true,
		})
		e := NewDispatcher(obsv, ffClient, ghFactory, h, queue, freno.NewFrenoClient(obsv, "", ""), noopClient, config.EnvironmentDevelopment)

		msg := newHydroMessage(t, topic, &githubv1.IssueUpdateAssignee{
			Action:     "issue.events.unassigned",
			Actor:      et.EntitiesUserWriter(),
			Repository: et.EntitiesRepository(),
			Issue:      et.EntitiesIssue(),
			Assignees:  []*entities.User{et.EntitiesUserReader()},
		})

		err := e.Dispatch(ctx, msg)
		require.NoError(t, err)
		require.True(t, didRun)
	})

	t.Run("ignores events for actions other than assigned or unassigned ", func(t *testing.T) {
		ctx := requestctx.AddData(context.Background(), &requestctx.CtxData{})
		didRun := false
		h := &events.NoopHandler{
			MockOnIssueUnassigned: func(ctx context.Context, event events.IssueAssignmentEvent) error {
				didRun = true
				return nil
			},
		}
		queue := NewSyncQueue(t, h)
		noopClient := githubtwirp.NewNoopClient()
		noopClient.NoopSweagentdAPI.ConfigurationForRepositoryResponse = to.Ptr(sweagentdTwirp.ConfigurationForRepositoryResponse{
			IsSweagentdEnabled: true,
		})
		e := NewDispatcher(obsv, ffClient, ghFactory, h, queue, freno.NewFrenoClient(obsv, "", ""), noopClient, config.EnvironmentDevelopment)

		msg := newHydroMessage(t, topic, &githubv1.IssueUpdateAssignee{
			Action:     "issue.events.foo",
			Actor:      et.EntitiesUserWriter(),
			Repository: et.EntitiesRepository(),
			Issue:      et.EntitiesIssue(),
			Assignees:  []*entities.User{et.EntitiesUserReader()},
		})

		err := e.Dispatch(ctx, msg)
		require.NoError(t, err)
		require.False(t, didRun)
	})

	t.Run("ignores events for pull request assigned", func(t *testing.T) {
		ctx := requestctx.AddData(context.Background(), &requestctx.CtxData{})
		didRun := false
		h := &events.NoopHandler{
			MockOnIssueUnassigned: func(ctx context.Context, event events.IssueAssignmentEvent) error {
				didRun = true
				return nil
			},
		}
		queue := NewSyncQueue(t, h)
		noopClient := githubtwirp.NewNoopClient()
		noopClient.NoopSweagentdAPI.ConfigurationForRepositoryResponse = to.Ptr(sweagentdTwirp.ConfigurationForRepositoryResponse{
			IsSweagentdEnabled: true,
		})
		e := NewDispatcher(obsv, ffClient, ghFactory, h, queue, freno.NewFrenoClient(obsv, "", ""), noopClient, config.EnvironmentDevelopment)

		msg := newHydroMessage(t, topic, &githubv1.IssueUpdateAssignee{
			Action:      "issue.events.assigned",
			Actor:       et.EntitiesUserWriter(),
			Repository:  et.EntitiesRepository(),
			Issue:       et.EntitiesIssue(),
			PullRequest: et.EntitiesPullRequest(),
			Assignees:   []*entities.User{et.EntitiesUserReader()},
		})

		err := e.Dispatch(ctx, msg)
		require.NoError(t, err)
		require.False(t, didRun)
	})

	t.Run("ignores events for pull request unassigned", func(t *testing.T) {
		ctx := requestctx.AddData(context.Background(), &requestctx.CtxData{})
		didRun := false
		h := &events.NoopHandler{
			MockOnIssueUnassigned: func(ctx context.Context, event events.IssueAssignmentEvent) error {
				didRun = true
				return nil
			},
		}
		queue := NewSyncQueue(t, h)
		noopClient := githubtwirp.NewNoopClient()
		noopClient.NoopSweagentdAPI.ConfigurationForRepositoryResponse = to.Ptr(sweagentdTwirp.ConfigurationForRepositoryResponse{
			IsSweagentdEnabled: true,
		})
		e := NewDispatcher(obsv, ffClient, ghFactory, h, queue, freno.NewFrenoClient(obsv, "", ""), noopClient, config.EnvironmentDevelopment)

		msg := newHydroMessage(t, topic, &githubv1.IssueUpdateAssignee{
			Action:      "issue.events.unassigned",
			Actor:       et.EntitiesUserWriter(),
			Repository:  et.EntitiesRepository(),
			Issue:       et.EntitiesIssue(),
			PullRequest: et.EntitiesPullRequest(),
			Assignees:   []*entities.User{et.EntitiesUserReader()},
		})

		err := e.Dispatch(ctx, msg)
		require.NoError(t, err)
		require.False(t, didRun)
	})
}

func TestDispatcher_PullRequestTimelineCommentCreate(t *testing.T) {
	ghFactory := &github.NoopClientFactory{}
	obsv := observability.NewNoopExporters()
	ffClient := featureflags.NewNoopClient(nil)
	topic := PullRequestTimelineCommentCreateTopic

	t.Run("executes the right event handler for pull request comments", func(t *testing.T) {
		ctx := requestctx.AddData(context.Background(), &requestctx.CtxData{})
		didRun := false
		h := &events.NoopHandler{
			MockOnPullRequestCommentCreated: func(ctx context.Context, event events.PullRequestCommentCreatedEvent) error {
				didRun = true
				return nil
			},
		}
		queue := NewSyncQueue(t, h)
		noopClient := githubtwirp.NewNoopClient()
		noopClient.NoopSweagentdAPI.ConfigurationForRepositoryResponse = to.Ptr(sweagentdTwirp.ConfigurationForRepositoryResponse{
			IsSweagentdEnabled: true,
		})
		e := NewDispatcher(obsv, ffClient, ghFactory, h, queue, freno.NewFrenoClient(obsv, "", ""), noopClient, config.EnvironmentDevelopment)

		msg := newHydroMessage(t, topic, &githubv1.PullRequestTimelineCommentCreate{
			Actor:        et.EntitiesUserWriter(),
			Repository:   et.EntitiesRepository(),
			Issue:        et.EntitiesIssue(),
			IssueComment: et.EntitiesIssueComment(et.ActorLoginWriter),
		})

		err := e.Dispatch(ctx, msg)
		require.NoError(t, err)
		require.True(t, didRun)
	})

	t.Run("execution is gated on repo enablement when FF is enabled", func(t *testing.T) {
		ctx := requestctx.AddData(context.Background(), &requestctx.CtxData{})
		didRun := false
		h := &events.NoopHandler{
			MockOnPullRequestCommentCreated: func(ctx context.Context, event events.PullRequestCommentCreatedEvent) error {
				didRun = true
				return nil
			},
		}
		queue := NewSyncQueue(t, h)
		responseToReturn := to.Ptr(sweagentdTwirp.ConfigurationForRepositoryResponse{
			IsSweagentdEnabled: false,
		})

		noopClient := githubtwirp.NewNoopClient()
		noopClient.NoopSweagentdAPI.ConfigurationForRepositoryResponse = responseToReturn
		e := NewDispatcher(obsv, ffClient, ghFactory, h, queue, freno.NewFrenoClient(obsv, "", ""), noopClient, config.EnvironmentDevelopment)

		msg := newHydroMessage(t, topic, &githubv1.PullRequestTimelineCommentCreate{
			Actor:        et.EntitiesUserWriter(),
			Repository:   et.EntitiesRepository(),
			Issue:        et.EntitiesIssue(),
			IssueComment: et.EntitiesIssueComment(et.ActorLoginWriter),
		})

		err := e.Dispatch(ctx, msg)
		require.NoError(t, err)
		require.False(t, didRun)

		responseToReturn.IsSweagentdEnabled = true
		err = e.Dispatch(ctx, msg)
		require.NoError(t, err)
		require.True(t, didRun)
	})
}

func TestDispatcher_PullRequestReviewSubmit(t *testing.T) {
	ghFactory := &github.NoopClientFactory{}
	obsv := observability.NewNoopExporters()
	ffClient := featureflags.NewNoopClient(nil)
	topic := PullRequestReviewSubmitTopic

	t.Run("executes the right event handler for pull request review comments", func(t *testing.T) {
		ctx := requestctx.AddData(context.Background(), &requestctx.CtxData{})
		didRun := false
		h := &events.NoopHandler{
			MockOnPullRequestReviewSubmit: func(ctx context.Context, event events.PullRequestReviewSubmitEvent) error {
				didRun = true
				return nil
			},
		}
		queue := NewSyncQueue(t, h)
		noopClient := githubtwirp.NewNoopClient()
		noopClient.NoopSweagentdAPI.ConfigurationForRepositoryResponse = to.Ptr(sweagentdTwirp.ConfigurationForRepositoryResponse{
			IsSweagentdEnabled: true,
		})
		e := NewDispatcher(obsv, ffClient, ghFactory, h, queue, freno.NewFrenoClient(obsv, "", ""), noopClient, config.EnvironmentDevelopment)

		msg := newHydroMessage(t, topic, &githubv1.PullRequestReviewSubmit{
			Actor:             et.EntitiesUserWriter(),
			Repository:        et.EntitiesRepository(),
			Issue:             et.EntitiesIssue(),
			PullRequest:       et.EntitiesPullRequestWithNumber(et.PullRequestNumber),
			PullRequestReview: et.EntitiesPullRequestReview(et.PullRequestNumber, et.ActorLoginWriter),
		})

		err := e.Dispatch(ctx, msg)
		require.NoError(t, err)
		require.True(t, didRun)
	})

	t.Run("execution is gated on repo enablement when FF is enabled", func(t *testing.T) {
		ctx := requestctx.AddData(context.Background(), &requestctx.CtxData{})
		didRun := false
		h := &events.NoopHandler{
			MockOnPullRequestReviewSubmit: func(ctx context.Context, event events.PullRequestReviewSubmitEvent) error {
				didRun = true
				return nil
			},
		}
		queue := NewSyncQueue(t, h)
		responseToReturn := to.Ptr(sweagentdTwirp.ConfigurationForRepositoryResponse{
			IsSweagentdEnabled: false,
		})

		noopClient := githubtwirp.NewNoopClient()
		noopClient.NoopSweagentdAPI.ConfigurationForRepositoryResponse = responseToReturn
		e := NewDispatcher(obsv, ffClient, ghFactory, h, queue, freno.NewFrenoClient(obsv, "", ""), noopClient, config.EnvironmentDevelopment)

		msg := newHydroMessage(t, topic, &githubv1.PullRequestReviewSubmit{
			Actor:             et.EntitiesUserWriter(),
			Repository:        et.EntitiesRepository(),
			Issue:             et.EntitiesIssue(),
			PullRequest:       et.EntitiesPullRequestWithNumber(et.PullRequestNumber),
			PullRequestReview: et.EntitiesPullRequestReview(et.PullRequestNumber, et.ActorLoginWriter),
		})

		err := e.Dispatch(ctx, msg)
		require.NoError(t, err)
		require.False(t, didRun)

		responseToReturn.IsSweagentdEnabled = true
		err = e.Dispatch(ctx, msg)
		require.NoError(t, err)
		require.True(t, didRun)
	})

	t.Run("ignores events in repos that don't have the feature flag", func(t *testing.T) {
		ctx := requestctx.AddData(context.Background(), &requestctx.CtxData{})
		ffClient := featureflags.NewNoopClient(nil)
		didRun := false
		h := &events.NoopHandler{
			MockOnPullRequestReviewSubmit: func(ctx context.Context, event events.PullRequestReviewSubmitEvent) error {
				didRun = true
				return nil
			},
		}
		queue := NewSyncQueue(t, h)
		noopClient := githubtwirp.NewNoopClient()
		noopClient.NoopSweagentdAPI.ConfigurationForRepositoryResponse = to.Ptr(sweagentdTwirp.ConfigurationForRepositoryResponse{
			IsSweagentdEnabled: false,
		})
		e := NewDispatcher(obsv, ffClient, ghFactory, h, queue, freno.NewFrenoClient(obsv, "", ""), noopClient, config.EnvironmentDevelopment)

		msg := newHydroMessage(t, topic, &githubv1.PullRequestReviewSubmit{
			Actor:             et.EntitiesUserWriter(),
			Repository:        et.EntitiesRepository(),
			Issue:             et.EntitiesIssue(),
			PullRequest:       et.EntitiesPullRequestWithNumber(et.PullRequestNumber),
			PullRequestReview: et.EntitiesPullRequestReview(et.PullRequestNumber, et.ActorLoginWriter),
		})

		err := e.Dispatch(ctx, msg)
		require.NoError(t, err)
		require.False(t, didRun)
	})
}

func TestDispatcher_ComputeUsageEvent(t *testing.T) {
	ghFactory := &github.NoopClientFactory{}
	obsv := observability.NewNoopExporters()
	ffClient := featureflags.NewNoopClient(nil)
	topic := ComputeUsageTopic

	t.Run("executes the right event handler for workflow comments", func(t *testing.T) {
		ctx := requestctx.AddData(context.Background(), &requestctx.CtxData{})
		didRun := false
		h := &events.NoopHandler{
			MockOnWorkflowRunCompleted: func(ctx context.Context, event events.WorkflowRunCompletedEvent) error {
				didRun = true
				return nil
			},
		}
		queue := NewSyncQueue(t, h)
		noopClient := githubtwirp.NewNoopClient()
		noopClient.NoopSweagentdAPI.ConfigurationForRepositoryResponse = to.Ptr(sweagentdTwirp.ConfigurationForRepositoryResponse{
			IsSweagentdEnabled: true,
		})
		e := NewDispatcher(obsv, ffClient, ghFactory, h, queue, freno.NewFrenoClient(obsv, "", ""), noopClient, config.EnvironmentDevelopment)

		msg := newHydroMessage(t, topic, et.ActionsComputeUsage())

		err := e.Dispatch(ctx, msg)
		require.NoError(t, err)
		require.True(t, didRun)
	})

	t.Run("ignores events in repos that don't have the feature flag", func(t *testing.T) {
		ctx := requestctx.AddData(context.Background(), &requestctx.CtxData{})
		ffClient := featureflags.NewNoopClient(nil)
		didRun := false
		h := &events.NoopHandler{
			MockOnWorkflowRunCompleted: func(ctx context.Context, event events.WorkflowRunCompletedEvent) error {
				didRun = true
				return nil
			},
		}
		queue := NewSyncQueue(t, h)
		noopClient := githubtwirp.NewNoopClient()
		noopClient.NoopSweagentdAPI.ConfigurationForRepositoryResponse = to.Ptr(sweagentdTwirp.ConfigurationForRepositoryResponse{
			IsSweagentdEnabled: false,
		})
		e := NewDispatcher(obsv, ffClient, ghFactory, h, queue, freno.NewFrenoClient(obsv, "", ""), noopClient, config.EnvironmentDevelopment)

		msg := newHydroMessage(t, topic, et.ActionsComputeUsage())

		err := e.Dispatch(ctx, msg)
		require.NoError(t, err)
		require.False(t, didRun)
	})
}

func TestDispatcher_CodeScanningAlertsAssignment(t *testing.T) {
	ghFactory := &github.NoopClientFactory{}
	obsv := observability.NewNoopExporters()
	ffClient := featureflags.NewNoopClient(nil)
	topic := CodeScanningAlertAssignmentTopic

	t.Run("executes the right event handler for assigned", func(t *testing.T) {
		ctx := requestctx.AddData(context.Background(), &requestctx.CtxData{})
		didRun := false
		h := &events.NoopHandler{
			MockOnCodeScanningAlertsAssigned: func(ctx context.Context, event events.CodeScanningAlertsAssignmentEvent) error {
				didRun = true
				return nil
			},
		}
		queue := NewSyncQueue(t, h)
		noopClient := githubtwirp.NewNoopClient()
		noopClient.NoopSweagentdAPI.ConfigurationForRepositoryResponse = to.Ptr(sweagentdTwirp.ConfigurationForRepositoryResponse{
			IsSweagentdEnabled: true,
		})
		e := NewDispatcher(obsv, ffClient, ghFactory, h, queue, freno.NewFrenoClient(obsv, "", ""), noopClient, config.EnvironmentDevelopment)

		msg := newHydroMessage(t, topic, et.EntitiesAlertsAssignmentAssigneeCopilot())

		err := e.Dispatch(ctx, msg)
		require.NoError(t, err)
		require.True(t, didRun)
	})

	t.Run("execution is gated on repo enablement when FF is enabled", func(t *testing.T) {
		ctx := requestctx.AddData(context.Background(), &requestctx.CtxData{})
		didRun := false

		h := &events.NoopHandler{
			MockOnCodeScanningAlertsAssigned: func(ctx context.Context, event events.CodeScanningAlertsAssignmentEvent) error {
				didRun = true
				return nil
			},
		}
		queue := NewSyncQueue(t, h)

		responseToReturn := to.Ptr(sweagentdTwirp.ConfigurationForRepositoryResponse{
			IsSweagentdEnabled: false,
		})

		noopClient := githubtwirp.NewNoopClient()
		noopClient.NoopSweagentdAPI.ConfigurationForRepositoryResponse = responseToReturn
		e := NewDispatcher(obsv, ffClient, ghFactory, h, queue, freno.NewFrenoClient(obsv, "", ""), noopClient, config.EnvironmentDevelopment)

		msg := newHydroMessage(t, topic, et.EntitiesAlertsAssignmentAssigneeCopilot())

		err := e.Dispatch(ctx, msg)
		require.NoError(t, err)
		require.False(t, didRun)

		responseToReturn.IsSweagentdEnabled = true
		err = e.Dispatch(ctx, msg)
		require.NoError(t, err)
		require.True(t, didRun)
	})

	t.Run("ignores events in repos that don't have the feature flag", func(t *testing.T) {
		ctx := requestctx.AddData(context.Background(), &requestctx.CtxData{})
		ffClient := featureflags.NewNoopClient(nil)
		didRun := false
		h := &events.NoopHandler{
			MockOnCodeScanningAlertsAssigned: func(ctx context.Context, event events.CodeScanningAlertsAssignmentEvent) error {
				didRun = true
				return nil
			},
		}
		queue := NewSyncQueue(t, h)
		noopClient := githubtwirp.NewNoopClient()
		noopClient.NoopSweagentdAPI.ConfigurationForRepositoryResponse = to.Ptr(sweagentdTwirp.ConfigurationForRepositoryResponse{
			IsSweagentdEnabled: false,
		})
		e := NewDispatcher(obsv, ffClient, ghFactory, h, queue, freno.NewFrenoClient(obsv, "", ""), noopClient, config.EnvironmentDevelopment)

		msg := newHydroMessage(t, topic, et.EntitiesAlertsAssignmentAssigneeCopilot())

		err := e.Dispatch(ctx, msg)
		require.NoError(t, err)
		require.False(t, didRun)
	})
}

func TestDispatcher_IgnoresEventsInStagingEnvironment(t *testing.T) {
	obsv := observability.NewNoopExporters()
	ghFactory := &github.NoopClientFactory{}
	ffClient := featureflags.NewNoopClient(nil)
	topic := IssueUpdateAssigneeTopic

	// Create a test event handler that sets a flag when it's called
	didRun := false
	h := &events.NoopHandler{
		MockOnIssueAssigned: func(ctx context.Context, event events.IssueAssignmentEvent) error {
			didRun = true
			return nil
		},
	}

	queue := NewSyncQueue(t, h)
	throttler := freno.NewFrenoClient(obsv, "", "")
	ghTwirp := githubtwirp.NewNoopClient()
	ghTwirp.NoopSweagentdAPI.ConfigurationForRepositoryResponse = to.Ptr(sweagentdTwirp.ConfigurationForRepositoryResponse{
		IsSweagentdEnabled: true,
	})

	dispatcher := NewDispatcher(
		obsv,
		ffClient,
		ghFactory,
		h,
		queue,
		throttler,
		ghTwirp,
		config.EnvironmentStaging,
	)

	// Create a valid hydro message that would be processed if not in staging
	msg := newHydroMessage(t, topic, &githubv1.IssueUpdateAssignee{
		Action:     "issue.events.assigned",
		Actor:      et.EntitiesUserWriter(),
		Repository: et.EntitiesRepository(),
		Issue:      et.EntitiesIssue(),
		Assignees:  []*entities.User{et.EntitiesUserReader()},
	})

	err := dispatcher.Dispatch(context.Background(), msg)

	require.NoError(t, err, "Dispatch should not return an error")
	require.False(t, didRun, "Event handler should not be called in staging environment")
}

func TestDispatcher_ProcessesEventsInProductionEnvironment(t *testing.T) {
	obsv := observability.NewNoopExporters()
	ghFactory := &github.NoopClientFactory{}
	ffClient := featureflags.NewNoopClient(nil)
	topic := IssueUpdateAssigneeTopic

	didRun := false
	h := &events.NoopHandler{
		MockOnIssueAssigned: func(ctx context.Context, event events.IssueAssignmentEvent) error {
			didRun = true
			return nil
		},
	}

	queue := NewSyncQueue(t, h)
	throttler := freno.NewFrenoClient(obsv, "", "")
	ghTwirp := githubtwirp.NewNoopClient()
	ghTwirp.NoopSweagentdAPI.ConfigurationForRepositoryResponse = to.Ptr(sweagentdTwirp.ConfigurationForRepositoryResponse{
		IsSweagentdEnabled: true,
	})

	dispatcher := NewDispatcher(
		obsv,
		ffClient,
		ghFactory,
		h,
		queue,
		throttler,
		ghTwirp,
		config.EnvironmentProduction, // Use production environment
	)

	// Create a valid hydro message that should be processed in production
	msg := newHydroMessage(t, topic, &githubv1.IssueUpdateAssignee{
		Action:     "issue.events.assigned",
		Actor:      et.EntitiesUserWriter(),
		Repository: et.EntitiesRepository(),
		Issue:      et.EntitiesIssue(),
		Assignees:  []*entities.User{et.EntitiesUserReader()},
	})

	err := dispatcher.Dispatch(context.Background(), msg)

	require.NoError(t, err, "Dispatch should not return an error")
	require.True(t, didRun, "Event handler should be called in production environment")
}

func TestDispatcher_HydroTenantHeaders(t *testing.T) {
	ghFactory := &github.NoopClientFactory{}
	obsv := observability.NewNoopExporters()
	ffClient := featureflags.NewNoopClient(nil)
	topic := IssueUpdateAssigneeTopic
	t.Run("extracts tenant headers in Proxima environment", func(t *testing.T) {
		ctx := requestctx.AddData(context.Background(), &requestctx.CtxData{})

		didRun := false

		h := &events.NoopHandler{
			MockOnIssueAssigned: func(ctx context.Context, event events.IssueAssignmentEvent) error {
				didRun = true
				return nil
			},
		}

		queue := NewSyncQueue(t, h)
		noopClient := githubtwirp.NewNoopClient()
		noopClient.NoopSweagentdAPI.ConfigurationForRepositoryResponse = to.Ptr(sweagentdTwirp.ConfigurationForRepositoryResponse{
			IsSweagentdEnabled: true,
		})

		e := NewDispatcher(obsv, ffClient, ghFactory, h, queue, freno.NewFrenoClient(obsv, "", ""), noopClient, config.EnvironmentProxima)

		// Create a message with tenant headers
		msg := newHydroMessageWithHeaders(t, topic, &githubv1.IssueUpdateAssignee{
			Action:     "issue.events.assigned",
			Actor:      et.EntitiesUserWriter(),
			Repository: et.EntitiesRepository(),
			Issue:      et.EntitiesIssue(),
			Assignees:  []*entities.User{et.EntitiesUserReader()},
		}, map[string]string{
			requestctx.GitHubTenantIDHeader: "test-tenant-id",
			requestctx.GitHubTenantHeader:   "test-tenant-slug",
		})

		err := e.Dispatch(ctx, msg)
		require.NoError(t, err)
		require.True(t, didRun)

		// Verify the tenant information was added to the request context
		// This is validated by checking that the event was processed (didRun is true)
		// and that the dispatcher completed successfully
	})

	t.Run("skips tenant header extraction in non-Proxima environment", func(t *testing.T) {
		ctx := requestctx.AddData(context.Background(), &requestctx.CtxData{})

		didRun := false

		h := &events.NoopHandler{
			MockOnIssueAssigned: func(ctx context.Context, event events.IssueAssignmentEvent) error {
				didRun = true
				return nil
			},
		}

		queue := NewSyncQueue(t, h)
		noopClient := githubtwirp.NewNoopClient()
		noopClient.NoopSweagentdAPI.ConfigurationForRepositoryResponse = to.Ptr(sweagentdTwirp.ConfigurationForRepositoryResponse{
			IsSweagentdEnabled: true,
		})

		// Use Development environment instead of Proxima
		e := NewDispatcher(obsv, ffClient, ghFactory, h, queue, freno.NewFrenoClient(obsv, "", ""), noopClient, config.EnvironmentDevelopment)

		// Create a message with tenant headers (should be ignored)
		msg := newHydroMessageWithHeaders(t, topic, &githubv1.IssueUpdateAssignee{
			Action:     "issue.events.assigned",
			Actor:      et.EntitiesUserWriter(),
			Repository: et.EntitiesRepository(),
			Issue:      et.EntitiesIssue(),
			Assignees:  []*entities.User{et.EntitiesUserReader()},
		}, map[string]string{
			requestctx.GitHubTenantIDHeader: "test-tenant-id",
			requestctx.GitHubTenantHeader:   "test-tenant-slug",
		})

		err := e.Dispatch(ctx, msg)
		require.NoError(t, err)
		require.True(t, didRun)
	})
}

func TestHydroTenantHeaders(t *testing.T) {
	t.Run("returns empty values for nil headers", func(t *testing.T) {
		id, slug, err := HydroTenantHeaders(nil)
		require.NoError(t, err)
		require.Empty(t, id)
		require.Empty(t, slug)
	})

	t.Run("returns empty values for empty headers", func(t *testing.T) {
		id, slug, err := HydroTenantHeaders(map[string]string{})
		require.NoError(t, err)
		require.Empty(t, id)
		require.Empty(t, slug)
	})

	t.Run("extracts tenant ID when present", func(t *testing.T) {
		headers := map[string]string{
			requestctx.GitHubTenantIDHeader: "test-tenant-id",
		}
		id, slug, err := HydroTenantHeaders(headers)
		require.NoError(t, err)
		require.Equal(t, "test-tenant-id", id)
		require.Empty(t, slug)
	})

	t.Run("extracts tenant slug when present", func(t *testing.T) {
		headers := map[string]string{
			requestctx.GitHubTenantHeader: "test-tenant-slug",
		}
		id, slug, err := HydroTenantHeaders(headers)
		require.NoError(t, err)
		require.Empty(t, id)
		require.Equal(t, "test-tenant-slug", slug)
	})

	t.Run("extracts both tenant ID and slug when present", func(t *testing.T) {
		headers := map[string]string{
			requestctx.GitHubTenantIDHeader: "test-tenant-id",
			requestctx.GitHubTenantHeader:   "test-tenant-slug",
		}
		id, slug, err := HydroTenantHeaders(headers)
		require.NoError(t, err)
		require.Equal(t, "test-tenant-id", id)
		require.Equal(t, "test-tenant-slug", slug)
	})
}

func TestDispatcher_checkEnablement(t *testing.T) {
	t.Run("returns false when user is flagged with disallow feature flag", func(t *testing.T) {
		obsv := observability.NewNoopExporters()
		ghFactory := &github.NoopClientFactory{}
		ffClient := featureflags.NewNoopClient(map[string]bool{
			featureflags.CopilotSWEAgentAPIDisallow: true,
		})
		h := &events.NoopHandler{}
		queue := NewSyncQueue(t, h)
		throttler := freno.NewFrenoClient(obsv, "", "")
		ghTwirp := githubtwirp.NewNoopClient()
		ghTwirp.NoopSweagentdAPI.ConfigurationForRepositoryResponse = to.Ptr(sweagentdTwirp.ConfigurationForRepositoryResponse{
			IsSweagentdEnabled: true,
		})

		dispatcher := NewDispatcher(
			obsv,
			ffClient,
			ghFactory,
			h,
			queue,
			throttler,
			ghTwirp,
			config.EnvironmentStaging,
		)

		enabled, err := dispatcher.checkEnablement(context.Background(), 123, 123)
		require.NoError(t, err)
		require.False(t, enabled, "Feature should be disabled for user with disallow flag")
	})
}

// newHydroMessage creates a hydro message with the given topic and message.
// It marshals the message into the envelope's message field.
func newHydroMessage(t *testing.T, topic string, message proto.Message) hydro_lib.Message {
	t.Helper()
	val, err := proto.Marshal(message)
	require.NoError(t, err)
	env, err := proto.Marshal(&hydro_pb.Envelope{
		TypeUrl: "test",
		Message: val,
	})
	require.NoError(t, err)
	return hydro_lib.Message{
		Topic: topic,
		Value: env,
	}
}

// newHydroMessageWithHeaders creates a hydro message with the given topic, message, and headers.
// It marshals the message into the envelope's message field and includes the provided headers.
func newHydroMessageWithHeaders(t *testing.T, topic string, message proto.Message, headers map[string]string) hydro_lib.Message {
	t.Helper()
	val, err := proto.Marshal(message)
	require.NoError(t, err)
	env, err := proto.Marshal(&hydro_pb.Envelope{
		TypeUrl: "test",
		Message: val,
	})
	require.NoError(t, err)
	return hydro_lib.Message{
		Topic:   topic,
		Value:   env,
		Headers: headers,
	}
}
