package hydroevents

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/github/github-telemetry-go/kvp"
	"github.com/github/github-telemetry-go/log"
	"github.com/github/go-http/v2/middleware/requestid"
	"github.com/github/go-stats"
	hydro_pb "github.com/github/hydro-client-go/v7/generated/hydro/v1"
	hydro_lib "github.com/github/hydro-client-go/v7/pkg/hydro"
	cs_pb "github.com/github/hydro-schemas-go/hydro/schemas/code_scanning/v0"
	actionsv0 "github.com/github/hydro-schemas-go/hydro/schemas/github/actions/v0"
	githubv1 "github.com/github/hydro-schemas-go/hydro/schemas/github/v1"
	"github.com/github/sweagentd/internal/config"
	"github.com/github/sweagentd/internal/events"
	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/freno"
	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/githubtwirp"
	"github.com/github/sweagentd/internal/observability"
	"github.com/github/sweagentd/internal/requestctx"
	sweagentdTwirp "github.com/github/sweagentd/proto/sweagentd/v1"
	"google.golang.org/protobuf/proto"
)

var (
	ComputeUsageTopic                     = "github.actions.v0.ComputeUsage"
	WorkflowRunExecution                  = "github.actions.v0.WorkflowRunExecution"
	IssueUpdateAssigneeTopic              = "github.v1.IssueUpdateAssignee"
	PullRequestTimelineCommentCreateTopic = "github.v1.PullRequestTimelineCommentCreate"
	PullRequestReviewSubmitTopic          = "cp1-iad.ingest.github.v1.PullRequestReviewSubmit"
	CodeScanningAlertAssignmentTopic      = "code_scanning.v0.AlertsAssignment"

	HydroTopics = []string{
		ComputeUsageTopic,
		WorkflowRunExecution,
		IssueUpdateAssigneeTopic,
		PullRequestTimelineCommentCreateTopic,
		PullRequestReviewSubmitTopic,
		CodeScanningAlertAssignmentTopic,
	}
)

type Dispatcher struct {
	obsv         observability.Exporters
	ffClient     featureflags.Client
	ghFactory    github.ClientFactoryInterface
	eventHandler events.EventsHandler
	queue        events.Queue
	throttler    freno.FrenoClient
	ghTwirp      githubtwirp.ClientInterface
	environment  config.Environment
}

func NewDispatcher(
	obsv observability.Exporters,
	ffClient featureflags.Client,
	ghFactory github.ClientFactoryInterface,
	eventHandler events.EventsHandler,
	queue events.Queue,
	throttler freno.FrenoClient,
	ghTwirp githubtwirp.ClientInterface,
	environment config.Environment,
) *Dispatcher {
	return &Dispatcher{
		obsv:         obsv,
		ffClient:     ffClient,
		ghFactory:    ghFactory,
		eventHandler: eventHandler,
		queue:        queue,
		throttler:    throttler,
		ghTwirp:      ghTwirp,
		environment:  environment,
	}
}

func (d *Dispatcher) Dispatch(ctx context.Context, m hydro_lib.Message) error {
	// Throttle according to real-time replication lag
	err := d.throttler.WaitOnThrottler(ctx)
	if err != nil {
		return err
	}

	// Let's start with a consolidated request ID for our background job
	if requestID := requestid.GetGitHubRequestID(ctx); requestID == "" {
		ctx = requestid.WithNewGitHubRequestID(ctx)
	}

	var envelope hydro_pb.Envelope
	if err := proto.Unmarshal(m.Value, &envelope); err != nil {
		return fmt.Errorf("unmarshaling envelope: %w", err)
	}

	// Add the request context data to the context
	ctx = requestctx.AddData(ctx, &requestctx.CtxData{
		HydroMessage: &requestctx.HydroEvent{
			EnvelopeId: envelope.Id,
			Topic:      m.Topic,
			Partition:  m.Partition,
			Offset:     m.Offset,
		},
	})

	if d.environment == config.EnvironmentProxima {
		id, slug, err := HydroTenantHeaders(m.Headers)
		if err != nil {
			return fmt.Errorf("getting hydro tenant headers: %w", err)
		}

		requestctx.AddTenantID(ctx, id)
		requestctx.AddTenantSlug(ctx, slug)
	}

	tags, _, kvps := requestctx.ToTelemetry(ctx, nil)
	logger := d.obsv.Logger.WithContext(ctx).WithFields(kvps...)

	logger.Info("start: hydro event")
	d.obsv.Statter.Counter("hydro.event_received", tags, 1)

	start := time.Now()
	err = d.dispatch(ctx, logger, m.Topic, &envelope)
	duration := time.Since(start)

	// Reload the request context data to get the latest values,
	// for anything that was added in the dispatch method
	tags, ex, kvps := requestctx.ToTelemetry(ctx, nil)
	logger = d.obsv.Logger.WithContext(ctx).WithFields(kvps...)

	// NOTE: Given how queuing works, and err will **never** bubble up from the underlying event handler
	// to the dispatcher. Add error logic related to handlers to events.Handler.Process(...) instead.
	if err != nil {
		if errors.Is(err, context.Canceled) {
			// If the context is canceled, that means the consumer is shutting down
			// And the errors will not be sent to Splunk / Sentry / Datadog
			return err
		}

		// If the error is not a context.Canceled error, we want to report it
		// to Sentry and log it, because it is unusual to get here. Most errors
		// happen in the event handlers, and are bubbled up to the event queue.
		d.obsv.Statter.Counter("hydro.event_error", tags, 1)
		logger.WithError(err).Error("hydro event error")

		// Report the error to Sentry
		_ = d.obsv.Reporter.Report(ctx, err, ex)

		// It is important that we return errors from this method, as the hydro
		// consumer will retry the message if we do. This is important for
		// transient errors, such as network issues and to ensure we don't drop
		// messages.
		// return err

		// FOR NOW: we are encountering too many errors that we aren't handling properly.
		// We are going to log the error and return nil so that the message is acked and
		// we can move on. This will prevent infinite retries that cause rate-limiting.
		return nil
	}

	d.obsv.Statter.DistributionMs("hydro.dispatch_dist", tags, duration)
	logger.Info("done: hydro event", kvp.Duration("duration", duration))

	return nil
}

func (d *Dispatcher) dispatch(ctx context.Context, logger log.Logger, topic string, envelope *hydro_pb.Envelope) error {
	switch topic {
	case IssueUpdateAssigneeTopic:
		return d.dispatchIssueUpdateAssignee(ctx, logger, envelope)

	case PullRequestReviewSubmitTopic:
		return d.dispatchPullRequestReviewSubmit(ctx, logger, envelope)

	case PullRequestTimelineCommentCreateTopic:
		return d.dispatchPullRequestCommentCreated(ctx, logger, envelope)

	case ComputeUsageTopic:
		return d.dispatchWorkflowRunCompleted(ctx, logger, envelope)

	case WorkflowRunExecution:
		return d.dispatchWorkflowRunExecutionCompleted(ctx, logger, envelope)

	case CodeScanningAlertAssignmentTopic:
		return d.dispatchCodeScanningAlertAssignment(ctx, logger, envelope)

	default:
		d.obsv.Logger.Error("unknown topic")
		return fmt.Errorf("unknown topic: %s", topic)
	}
}

func (d *Dispatcher) dispatchIssueUpdateAssignee(ctx context.Context, logger log.Logger, envelope *hydro_pb.Envelope) error {
	var payload githubv1.IssueUpdateAssignee
	if err := proto.Unmarshal(envelope.Message, &payload); err != nil {
		return fmt.Errorf("unmarshaling envelope: %w", err)
	}

	// Ignore assignment events for pull requests
	if payload.PullRequest != nil {
		return nil
	}

	if err := requestctx.AddUser(ctx, &requestctx.UserInfo{
		ID:         uint64(payload.Actor.Id),
		Login:      payload.Actor.Login,
		TrackingID: payload.Actor.AnalyticsTrackingId,
	}); err != nil {
		return fmt.Errorf("adding user to context: %w", err)
	}
	if err := requestctx.AddRepo(ctx, requestctx.CtxRepo{
		ID:      int64(payload.Repository.Id),
		OwnerID: int64(payload.Repository.OwnerId.Value),
	}); err != nil {
		return fmt.Errorf("adding repo to context: %w", err)
	}
	if err := requestctx.AddIssue(ctx, requestctx.CtxIssue{
		ID:     getID(payload.Issue),
		Number: int(payload.Issue.Number),
	}); err != nil {
		return fmt.Errorf("adding issue to context: %w", err)
	}

	enabled, err := d.checkEnablement(ctx, int64(payload.Actor.Id), int64(payload.Repository.Id))
	if err != nil {
		return fmt.Errorf("failed to assert repo enabled: %w", err)
	} else if !enabled {
		return nil
	}

	var action string
	switch payload.Action {
	case "issue.events.assigned":
		action = events.IssueAssignmentEventActionAssigned
	case "issue.events.unassigned":
		action = events.IssueAssignmentEventActionUnassigned
	default:
		logger.Info("unknown hydroevent action for issue update assignee: " + payload.Action)
		return nil
	}

	actor := EventActor(payload.Actor)
	if actor == nil {
		return fmt.Errorf("getting actor")
	}

	repo := EventRepository(payload.Repository)
	if repo == nil {
		return fmt.Errorf("getting repository")
	}

	issue := EventIssue(payload.Issue)
	if issue == nil {
		return fmt.Errorf("getting issue")
	}

	assignees := make([]string, 0, len(payload.Assignees))
	for _, assignee := range payload.Assignees {
		assignees = append(assignees, assignee.Login)
	}

	// Determine the specific user changed by this event
	assigneeLogin := ""
	if payload.Assignee != nil {
		assigneeLogin = payload.Assignee.Login
	}
	evt := events.IssueAssignmentEvent{
		Actor: actor,
		Repo:  repo,
		Issue: issue,
		// All current assignees on the issue
		Assignees: assignees,
		// The specific user that was changed by this event
		Assignee:  assigneeLogin,
		Action:    action,
		EventUrl:  payload.EventUrl,
		Timestamp: envelope.Timestamp.AsTime(),
	}

	logger.Info("enqueuing event")
	return d.queue.Enqueue(ctx, evt, envelope.Id)
}

func (d *Dispatcher) dispatchPullRequestReviewSubmit(ctx context.Context, logger log.Logger, envelope *hydro_pb.Envelope) error {
	var payload githubv1.PullRequestReviewSubmit
	if err := proto.Unmarshal(envelope.Message, &payload); err != nil {
		return fmt.Errorf("unmarshaling envelope: %w", err)
	}

	if err := requestctx.AddUser(ctx, &requestctx.UserInfo{
		ID:         uint64(payload.Actor.Id),
		Login:      payload.Actor.Login,
		TrackingID: payload.Actor.AnalyticsTrackingId,
	}); err != nil {
		return fmt.Errorf("adding user to context: %w", err)
	}
	if err := requestctx.AddRepo(ctx, requestctx.CtxRepo{
		ID:      int64(payload.Repository.Id),
		OwnerID: int64(payload.Repository.OwnerId.Value),
	}); err != nil {
		return fmt.Errorf("adding repo to context: %w", err)
	}
	if err := requestctx.AddIssue(ctx, requestctx.CtxIssue{
		ID:     getID(payload.Issue),
		Number: int(payload.Issue.Number),
	}); err != nil {
		return fmt.Errorf("adding issue to context: %w", err)
	}
	prID := int64(payload.PullRequest.Id)
	reviewID := int64(payload.PullRequestReview.Id)
	if err := requestctx.AddPullRequest(ctx, requestctx.CtxPullRequest{
		ID:       &prID,
		Number:   int(payload.Issue.Number),
		ReviewID: &reviewID,
	}); err != nil {
		return fmt.Errorf("adding pull request to context: %w", err)
	}

	enabled, err := d.checkEnablement(ctx, int64(payload.Actor.Id), int64(payload.Repository.Id))
	if err != nil {
		return fmt.Errorf("failed to assert repo enabled: %w", err)
	} else if !enabled {
		return nil
	}

	actor := EventActor(payload.Actor)
	if actor == nil {
		return fmt.Errorf("getting actor")
	}

	repo := EventRepository(payload.Repository)
	if repo == nil {
		return fmt.Errorf("getting repository")
	}

	pullRequest := EventPullRequest(payload.Issue, payload.PullRequest)
	if pullRequest == nil {
		return fmt.Errorf("getting pull request")
	}

	pullRequestReview := EventPullRequestReview(payload.PullRequestReview)
	if pullRequestReview == nil {
		return fmt.Errorf("getting pull request review")
	}

	evt := events.PullRequestReviewSubmitEvent{
		Actor:             actor,
		Repo:              repo,
		PullRequest:       pullRequest,
		PullRequestReview: pullRequestReview,
		EventUrl:          payload.EventUrl,
		Timestamp:         envelope.Timestamp.AsTime(),
	}

	logger.Info("enqueuing event")
	return d.queue.Enqueue(ctx, evt, envelope.Id)
}

func (d *Dispatcher) dispatchPullRequestCommentCreated(ctx context.Context, logger log.Logger, envelope *hydro_pb.Envelope) error {
	var payload githubv1.PullRequestTimelineCommentCreate
	if err := proto.Unmarshal(envelope.Message, &payload); err != nil {
		return fmt.Errorf("unmarshaling envelope: %w", err)
	}

	if err := requestctx.AddUser(ctx, &requestctx.UserInfo{
		ID:         uint64(payload.Actor.Id),
		Login:      payload.Actor.Login,
		TrackingID: payload.Actor.AnalyticsTrackingId,
	}); err != nil {
		return fmt.Errorf("adding user to context: %w", err)
	}
	if err := requestctx.AddRepo(ctx, requestctx.CtxRepo{
		ID:      int64(payload.Repository.Id),
		OwnerID: int64(payload.Repository.OwnerId.Value),
	}); err != nil {
		return fmt.Errorf("adding repo to context: %w", err)
	}
	if err := requestctx.AddIssue(ctx, requestctx.CtxIssue{
		ID:     getID(payload.Issue),
		Number: int(payload.Issue.Number),
	}); err != nil {
		return fmt.Errorf("adding issue to context: %w", err)
	}
	commentID := getID(payload.IssueComment)
	if err := requestctx.AddPullRequest(ctx, requestctx.CtxPullRequest{
		Number:    int(payload.Issue.Number),
		CommentID: &commentID,
	}); err != nil {
		return fmt.Errorf("adding pull request to context: %w", err)
	}

	enabled, err := d.checkEnablement(ctx, int64(payload.Actor.Id), int64(payload.Repository.Id))
	if err != nil {
		return fmt.Errorf("failed to assert repo enabled: %w", err)
	} else if !enabled {
		return nil
	}

	actor := EventActor(payload.Actor)
	if actor == nil {
		return fmt.Errorf("getting actor")
	}

	repo := EventRepository(payload.Repository)
	if repo == nil {
		return fmt.Errorf("getting repository")
	}

	pullRequest := EventPullRequest(payload.Issue, nil)
	if pullRequest == nil {
		return fmt.Errorf("getting pull request")
	}

	pullRequestComment := EventPullRequestComment(payload.IssueComment)
	if pullRequestComment == nil {
		return fmt.Errorf("getting pull request comment")
	}

	evt := events.PullRequestCommentCreatedEvent{
		Actor:       actor,
		Repo:        repo,
		PullRequest: pullRequest,
		Comment:     pullRequestComment,
		EventUrl:    payload.EventUrl,
		Timestamp:   envelope.Timestamp.AsTime(),
	}

	logger.Info("enqueuing event")
	return d.queue.Enqueue(ctx, evt, envelope.Id)
}

func (d *Dispatcher) dispatchWorkflowRunCompleted(ctx context.Context, logger log.Logger, envelope *hydro_pb.Envelope) error {
	// Note: this event is actually published on completion of workflow jobs,
	// not the workflow runs. However, we only have a single job in our workflow
	// so we can assume that the conclusion of the job is the same as the
	// conclusion of the workflow run
	var payload actionsv0.ComputeUsage
	if err := proto.Unmarshal(envelope.Message, &payload); err != nil {
		return fmt.Errorf("unmarshaling envelope: %w", err)
	}

	// TODO(colbylwilliams): There is currently an issue causing hydro events
	// to be sent with repository_owner_id = 0. This is a bug in the hydro
	// event producer and should be fixed. In the meantime, we can ignore
	// these events to avoid flooding the queue with errors.
	if payload.RepositoryOwnerId == 0 {
		logger.Info("Ignoring workflow run because repository_owner_id is 0")
		return nil
	}

	// We need to consume the hydro events at least as fast as they are produced,
	// otherwise we get behind and a lag builds up between when the event is
	// produced and when we consume it. To make sure we handle events quickly,
	// we'll short-circuit here for events that we don't care about before doing
	// any time-consuming work like checking the feature flag or creating a client.

	event := payload.InvokingEventType
	// we currently only care about dynamic workflow runs
	if event != "dynamic" {
		return nil
	}

	path := string(payload.WorkflowFilePath)
	// short circuit if the workflow run is not for the copilot-swe-agent
	if path == "" || !strings.HasPrefix(path, "dynamic/copilot-swe-agent/") {
		return nil
	}

	if err := requestctx.AddRepo(ctx, requestctx.CtxRepo{
		ID:      payload.RepositoryId,
		OwnerID: payload.RepositoryOwnerId,
	}); err != nil {
		return fmt.Errorf("adding repo to context: %w", err)
	}
	if err := requestctx.AddWorkflowRun(ctx, &requestctx.CtxWorkflowRun{
		ID:    int64(payload.WorkflowRunId),
		JobID: &payload.CheckRunId,
	}); err != nil {
		return fmt.Errorf("adding workflow run to context: %w", err)
	}

	// InvokingUserId is going to be copilot, this means that for now, for things like staging assignment will only work for this code path when the FF is enabled on a repository level.
	enabled, err := d.checkEnablement(ctx, payload.InvokingUserId, payload.RepositoryId)
	if err != nil {
		return fmt.Errorf("failed to assert repo enabled: %w", err)
	} else if !enabled {
		return nil
	}

	repo := EventWorkflowRepository(&payload)
	if repo == nil {
		return fmt.Errorf("getting repository")
	}

	run := EventWorkflowRunFromJob(&payload)
	if run == nil {
		return fmt.Errorf("getting workflow run")
	}

	evt := events.WorkflowRunCompletedEvent{
		Repo:        repo,
		WorkflowRun: run,
		Timestamp:   envelope.Timestamp.AsTime(),
	}

	logger.Info("enqueuing event")
	return d.queue.Enqueue(ctx, evt, envelope.Id)
}

func (d *Dispatcher) dispatchWorkflowRunExecutionCompleted(ctx context.Context, logger log.Logger, envelope *hydro_pb.Envelope) error {
	var payload actionsv0.WorkflowRunExecution
	if err := proto.Unmarshal(envelope.Message, &payload); err != nil {
		return fmt.Errorf("unmarshaling envelope: %w", err)
	}

	if payload.RepositoryOwnerId == 0 {
		logger.Info("Ignoring workflow run because repository_owner_id is 0")
		return nil
	}

	// We need to consume the hydro events at least as fast as they are produced,
	// otherwise we get behind and a lag builds up between when the event is
	// produced and when we consume it. To make sure we handle events quickly,
	// we'll short-circuit here for events that we don't care about before doing
	// any time-consuming work like checking the feature flag or creating a client.
	path := payload.WorkflowRunPath
	// short circuit if the workflow run is not for the copilot-swe-agent
	if path == "" || !strings.HasPrefix(path, "dynamic/copilot-swe-agent/") {
		return nil
	}

	// TODO(colbylwilliams): We're currently consuming ComputeUsage events which
	// are published on completion of workflow jobs, in addition to this event
	// which is published on completion of workflow runs. We should probably
	// migrate to only consuming this event, but to start we'll just use this
	// event to handle edge cases where the ComputeUsage event is not published
	// (e.g. when the workflow run is cancelled or fails before the job is
	// started).
	if payload.Conclusion == "" || payload.Conclusion != "startup_failure" {
		return nil
	}

	if err := requestctx.AddRepo(ctx, requestctx.CtxRepo{
		ID:      payload.RepositoryId,
		OwnerID: payload.RepositoryOwnerId,
	}); err != nil {
		return fmt.Errorf("adding repo to context: %w", err)
	}
	if err := requestctx.AddWorkflowRun(ctx, &requestctx.CtxWorkflowRun{
		ID: int64(payload.WorkflowRunId),
	}); err != nil {
		return fmt.Errorf("adding workflow run to context: %w", err)
	}

	// ActorId is going to be copilot, this means that for now, for things like staging assignment will only work for this code path when the FF is enabled on a repository level.
	enabled, err := d.checkEnablement(ctx, payload.ActorId, payload.RepositoryId)
	if err != nil {
		return fmt.Errorf("failed to assert repo enabled: %w", err)
	} else if !enabled {
		return nil
	}

	repo := EventWorkflowRepository(&payload)
	if repo == nil {
		return fmt.Errorf("getting repository")
	}

	run := EventWorkflowRun(&payload)
	if run == nil {
		return fmt.Errorf("getting workflow run")
	}

	evt := events.WorkflowRunCompletedEvent{
		Repo:        repo,
		WorkflowRun: run,
		Timestamp:   envelope.Timestamp.AsTime(),
	}

	logger.Info("enqueuing event")
	return d.queue.Enqueue(ctx, evt, envelope.Id)
}

func (d *Dispatcher) dispatchCodeScanningAlertAssignment(ctx context.Context, logger log.Logger, envelope *hydro_pb.Envelope) error {
	var payload cs_pb.AlertsAssignment
	if err := proto.Unmarshal(envelope.Message, &payload); err != nil {
		return fmt.Errorf("unmarshaling envelope: %w", err)
	}

	if err := requestctx.AddUser(ctx, &requestctx.UserInfo{
		ID:         uint64(payload.Actor.Id),
		Login:      payload.Actor.Login,
		TrackingID: payload.Actor.AnalyticsTrackingId,
	}); err != nil {
		return fmt.Errorf("adding user to context: %w", err)
	}
	if err := requestctx.AddRepo(ctx, requestctx.CtxRepo{
		ID:      int64(payload.Repository.Id),
		OwnerID: int64(payload.Repository.OwnerId.Value),
	}); err != nil {
		return fmt.Errorf("adding repo to context: %w", err)
	}

	enabled, err := d.checkEnablement(ctx, int64(payload.Actor.Id), int64(payload.Repository.Id))
	if err != nil {
		return fmt.Errorf("failed to assert repo enabled: %w", err)
	} else if !enabled {
		return nil
	}

	actor := EventActor(payload.Actor)
	if actor == nil {
		return fmt.Errorf("getting actor: %w", err)
	}

	repo := EventRepository(payload.Repository)
	if repo == nil {
		return fmt.Errorf("getting repository: %w", err)
	}

	var alerts = make([]*events.CodeScanningAlert, len(payload.CodeScanningAlerts))
	for ix, alert := range payload.CodeScanningAlerts {
		a := &events.CodeScanningAlert{
			Number: alert.AlertNumber,
			Title:  alert.AlertTitle,
			Url:    alert.AlertUrl,
			SuggestedFix: &events.SuggestedFix{
				Files: make([]*events.SuggestedFixFile, len(alert.SuggestedFix.Files)),
			},
		}
		for i, file := range alert.SuggestedFix.Files {
			f := &events.SuggestedFixFile{
				FilePath:    file.FilePath,
				DiffContent: []byte(file.DiffContent),
			}
			a.SuggestedFix.Files[i] = f
		}
		alerts[ix] = a
	}

	assignees := make([]string, 0, len(payload.Assignees))
	for _, assignee := range payload.Assignees {
		assignees = append(assignees, assignee.Login)
	}

	evt := events.CodeScanningAlertsAssignmentEvent{
		Actor:     actor,
		Repo:      repo,
		Alerts:    alerts,
		Assignees: assignees,
		EventUrl:  payload.EventUrl,
	}

	logger.Info("enqueuing event")
	return d.queue.Enqueue(ctx, evt, envelope.Id)
}

func (d *Dispatcher) checkEnablement(ctx context.Context, actorID int64, repositoryID int64) (shouldConsiderEnabled bool, err error) {
	tags, _, kvps := requestctx.ToTelemetry(ctx, nil)

	if d.ffClient.IsEnabledForUser(ctx, featureflags.CopilotSWEAgentAPIDisallow, actorID) {
		d.obsv.Statter.Counter("repo_enablement", tags.Merge(stats.Tags{"was_enabled": "false"}), 1)
		d.obsv.Logger.WithContext(ctx).WithFields(kvps...).Info("Ignoring Hydro event because user is flagged")
		return false, nil
	}

	routeUserToStaging := d.ffClient.IsEnabledForUserOrRepo(ctx, "sweagentd_route_user_or_repo_to_staging", actorID, repositoryID)
	if routeUserToStaging && d.environment != config.EnvironmentStaging || !routeUserToStaging && d.environment == config.EnvironmentStaging {
		d.obsv.Logger.WithContext(ctx).WithFields(kvps...).Info("Ignoring assignment because environment does not match feature flag routing")
		return false, nil
	}

	result, err := d.ghTwirp.SweagentdAPI().ConfigurationForRepository(ctx, &sweagentdTwirp.ConfigurationForRepositoryRequest{
		RepositoryId: repositoryID,
		UserId:       uint64(actorID),
	})

	if err != nil {
		return false, fmt.Errorf("failed to get monolith configuration for repository: %w", err)
	}

	d.obsv.Statter.Counter("repo_enablement", tags.Merge(stats.Tags{"was_enabled": strconv.FormatBool(result.IsSweagentdEnabled)}), 1)

	if !result.IsSweagentdEnabled {
		d.obsv.Logger.WithContext(ctx).WithFields(kvps...).Info("Ignoring assignment because repo is not enabled for Copilot")
	}

	return result.IsSweagentdEnabled, nil
}

func HydroTenantHeaders(headers map[string]string) (string, string, error) {
	id := ""
	slug := ""

	if headers == nil {
		return id, slug, nil
	}

	found, ok := headers[requestctx.GitHubTenantIDHeader]
	if ok {
		id = found
	}

	found, ok = headers[requestctx.GitHubTenantHeader]
	if ok {
		slug = found
	}

	return id, slug, nil
}
