package hydroevents

import (
	actionsv0 "github.com/github/hydro-schemas-go/hydro/schemas/github/actions/v0"
	entities "github.com/github/hydro-schemas-go/hydro/schemas/github/v1/entities"
	"github.com/github/sweagentd/internal/events"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

// EventActor returns an [events.Actor] from a given [entities.User] (public for tests)
func EventActor(user *entities.User) *events.Actor {
	if user == nil {
		return nil
	}
	return &events.Actor{
		ID:         int64(user.Id),
		NodeID:     getGlobalID(user),
		Login:      user.Login,
		TrackingID: user.AnalyticsTrackingId,
		Type:       user.Type.String(),
	}
}

// EventRepository returns an [events.Repository] from a given [entities.Repository] (public for tests)
func EventRepository(repo *entities.Repository) *events.Repository {
	if repo == nil {
		return nil
	}
	var ownerID int64
	if repo.OwnerId != nil {
		ownerID = int64(repo.OwnerId.Value)
	}
	return &events.Repository{
		ID:        int64(repo.Id),
		NodeID:    getGlobalID(repo),
		Name:      repo.Name,
		OwnerID:   ownerID,
		IsPrivate: repo.Visibility == entities.Repository_PRIVATE || repo.Visibility == entities.Repository_INTERNAL,
	}
}

// EventIssue returns an [events.Issue] from a given [entities.Issue] (public for tests)
func EventIssue(issue *entities.Issue) *events.Issue {
	if issue == nil {
		return nil
	}
	return &events.Issue{
		ID:     getID(issue),
		NodeID: issue.GlobalRelayId,
		Number: int(issue.Number),
		IsOpen: issue.IssueState == entities.Issue_OPEN,
	}
}

// EventPullRequest returns an [events.PullRequest] from a given [entities.Issue] and
// [entities.PullRequest] (public for tests)
func EventPullRequest(issue *entities.Issue, pr *entities.PullRequest) *events.PullRequest {
	if issue == nil {
		return nil
	}
	issuePR := &events.PullRequest{
		NodeID: issue.GlobalRelayId,
		Number: int(issue.Number),
		IsOpen: issue.IssueState == entities.Issue_OPEN,
	}
	if pr != nil {
		id := int64(pr.Id)
		issuePR.ID = &id
		issuePR.NodeID = pr.GlobalRelayId
		issuePR.IsOpen = pr.PullRequestState == entities.PullRequest_OPEN
		issuePR.IsDraft = &pr.Draft
	}
	return issuePR
}

// EventPullRequestComment returns an [events.PullRequestComment] from a given
// [entities.IssueComment] (public for tests)
func EventPullRequestComment(comment *entities.IssueComment) *events.PullRequestComment {
	if comment == nil {
		return nil
	}
	return &events.PullRequestComment{
		ID:     getID(comment),
		NodeID: comment.GlobalRelayId,
	}
}

// EventPullRequestReview returns an [events.PullRequestReview] from a given
// [entities.PullRequestReview] (public for tests)
func EventPullRequestReview(review *entities.PullRequestReview) *events.PullRequestReview {
	if review == nil {
		return nil
	}
	return &events.PullRequestReview{
		ID: int64(review.Id),
	}
}

func EventWorkflowRepository(job interface {
	GetRepositoryId() int64
	GetRepositoryOwnerId() int64
}) *events.WorkflowRepository {
	if job == nil {
		return nil
	}
	if job.GetRepositoryId() == 0 || job.GetRepositoryOwnerId() == 0 {
		return nil
	}
	return &events.WorkflowRepository{
		ID:      job.GetRepositoryId(),
		OwnerID: job.GetRepositoryOwnerId(),
	}
}

func EventWorkflowRun(job *actionsv0.WorkflowRunExecution) *events.WorkflowRun {
	if job == nil {
		return nil
	}
	run := &events.WorkflowRun{
		ID:           int64(job.WorkflowRunId),
		Path:         job.WorkflowRunPath, // dynamic/copilot-swe-agent/copilot
		UserID:       job.ActorId,         // only interested in copilot (id)
		CheckSuiteID: job.CheckSuiteId,
		Status:       job.Status,
		Conclusion:   workflowConclusionFromRun(job),
		Attempt:      int(job.Attempt),
		UsageMs:      workflowUsageMS(job),
	}
	if job.CompletedAt != nil {
		run.CompletedAt = job.CompletedAt.AsTime().Unix()
	}
	if job.StartedAt != nil {
		run.StartedAt = job.StartedAt.AsTime().Unix()
	}
	return run
}

func EventWorkflowRunFromJob(job *actionsv0.ComputeUsage) *events.WorkflowRun {
	if job == nil {
		return nil
	}
	run := &events.WorkflowRun{
		ID:           int64(job.WorkflowRunId),
		Path:         string(job.WorkflowFilePath), // dynamic/copilot-swe-agent/copilot
		UserID:       job.InvokingUserId,           // only interested in copilot (id)
		CheckSuiteID: job.CheckSuiteId,
		Status:       events.WorkflowCompletedEventStatusCompleted,
		Conclusion:   workflowConclusionFromJob(job),
		Attempt:      int(job.WorkflowRunAttempt),
		UsageMs:      workflowUsageMS(job),
		JobID:        &job.CheckRunId,
	}
	if job.CompletedAt != nil {
		run.CompletedAt = job.CompletedAt.AsTime().Unix()
	}
	if job.StartedAt != nil {
		run.StartedAt = job.StartedAt.AsTime().Unix()
	}
	return run
}

func workflowConclusionFromJob(job *actionsv0.ComputeUsage) string {
	if job == nil {
		return ""
	}
	switch job.CheckRunConclusion {
	case actionsv0.ComputeUsage_RESULT_SUCCESS:
		return events.WorkflowCompletedEventConclusionSuccess
	case actionsv0.ComputeUsage_RESULT_CANCELLED:
		return events.WorkflowCompletedEventConclusionCancelled
	default:
		return events.WorkflowCompletedEventConclusionFailure
	}
}

func workflowConclusionFromRun(job *actionsv0.WorkflowRunExecution) string {
	if job == nil {
		return ""
	}
	switch job.Conclusion {
	case events.WorkflowCompletedEventConclusionSuccess:
		return events.WorkflowCompletedEventConclusionSuccess
	case events.WorkflowCompletedEventConclusionCancelled:
		return events.WorkflowCompletedEventConclusionCancelled
	default:
		return events.WorkflowCompletedEventConclusionFailure
	}
}

func workflowUsageMS(job interface {
	GetStartedAt() *timestamppb.Timestamp
	GetCompletedAt() *timestamppb.Timestamp
}) int64 {
	if job == nil {
		return 0
	}
	if job.GetStartedAt() == nil || job.GetCompletedAt() == nil {
		return 0
	}
	return job.GetCompletedAt().AsTime().Sub(job.GetStartedAt().AsTime()).Milliseconds()
}

// getID returns the value of IdValue if it exists, otherwise it returns the value of Id as an int64.
func getID(e interface {
	GetId() uint32
	GetIdValue() int64
}) int64 {
	if idv := e.GetIdValue(); idv != 0 {
		return idv
	}
	return int64(e.GetId())
}

// getGlobalID returns the value of NextGlobalId if it exists, otherwise it returns the value of GlobalRelayId.
func getGlobalID(e interface {
	GetGlobalRelayId() string
	GetNextGlobalId() string
}) string {
	if id := e.GetNextGlobalId(); id != "" {
		return id
	}
	if id := e.GetGlobalRelayId(); id != "" {
		return id
	}
	return ""
}
