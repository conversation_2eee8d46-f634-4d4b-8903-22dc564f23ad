package events

import (
	"context"
	"fmt"
	"math/rand"
	"slices"
	"strconv"
	"strings"

	"github.com/github/feature-management-client-go/vexi"
	"github.com/github/feature-management-client-go/vexi/extensions"
	"github.com/github/github-telemetry-go/kvp"
	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/jobutils"
	"github.com/github/sweagentd/internal/requestctx"
)

func (h *Handler) OnCodeScanningAlertsAssigned(ctx context.Context, event CodeScanningAlertsAssignmentEvent) error {
	// for this event, the actor is the user that set the assignee
	assigner := event.Actor

	var alert_numbers []int64
	for _, alert := range event.Alerts {
		alert_numbers = append(alert_numbers, alert.Number)
	}

	if err := requestctx.AddCodeScanningAlerts(ctx, &requestctx.CtxCodeScanningAlerts{
		RepoID:       event.Repo.ID,
		OwnerID:      event.Repo.OwnerID,
		ActorID:      assigner.ID,
		Assignees:    event.Assignees,
		AlertNumbers: alert_numbers,
	}); err != nil {
		h.obsv.LoggerWithTelemetry(ctx).WithError(err).Error("failed to add code scanning alerts to context")
	}

	logger := h.obsv.LoggerWithTelemetry(ctx)

	actors := []vexi.Actor{
		extensions.NewRepository(strconv.FormatInt(event.Repo.ID, 10)),
		extensions.NewOrganization(strconv.FormatInt(event.Repo.OwnerID, 10)),
	}
	if !h.ffClient.IsEnabled(ctx, featureflags.CopilotSWEAgentIntegrationCodeScanningAlerts, actors...) {
		// do nothing and return early
		logger.Info("Ignoring code scanning alerts because feature flag is not enabled")
		return nil
	}

	useAlertLinksInPR := h.ffClient.IsEnabled(ctx, featureflags.CopilotSWEAgentIntegrationCodeScanningAlertsLinksInPR, actors...)

	gh, err := h.ghFactory.NewClientFromRepo(ctx, event.Repo.ID, event.Repo.OwnerID)
	if err != nil {
		return fmt.Errorf("failed to create GitHub client: %w", err)
	}

	// Ignore events if not assigned to Copilot
	if !slices.ContainsFunc(event.Assignees, gh.UserIsCopilot) {
		logger.Info("Ignoring code scanning alerts because they are not assigned to Copilot")
		return nil
	}

	// Only process events where the actor has write access to the repo
	permission, err := gh.GetUserPermissionsForRepositoryByID(ctx, event.Repo.ID, assigner.Login)
	if err != nil {
		return fmt.Errorf("getting user permissions for repository: %w", err)
	}
	if !permission.Push {
		logger.Info("Ignoring code scanning alerts because actor does not have write access to the repo")
		return nil
	}

	actx, err := gh.GetRepositoryContext(ctx, event.Repo.NodeID)
	if err != nil {
		return fmt.Errorf("failed to get repository context: %w", err)
	}

	repo := actx.Repository
	owner := repo.Owner

	var resources []jobs.AssignmentResource
	for _, alert := range event.Alerts {
		resources = append(resources, jobs.AssignmentResource{
			Type: "code_scanning_alert",
			ID:   strconv.FormatInt(alert.Number, 10),
		})
	}

	// We will always create a new assignment for this event
	assignment := jobs.NewAssignment(owner.GetID(), repo.ID, owner.NodeID, repo.NodeID, owner.Login, repo.Name)
	assignment.AddDetachedResources(resources...)
	assignment, err = h.jobsStore.CreateAssignment(ctx, assignment)
	if err != nil {
		return fmt.Errorf("failed to create assignment: %w", err)
	}

	logger.Info("Assignment created for code scanning alerts",
		kvp.String("job.assignment_id", assignment.ID))

	err = h.onCodeScanningAlertsAssigned(ctx, gh, assignment, &repo, event, useAlertLinksInPR)
	if err != nil {
		// NewErrUnhandledAssignmentError is used to create a new comment on the
		// issue or PR to inform the user that the assignment was not handled
		// successfully. For this event, we don't have an issue and don't have a
		// PR yet, so we can't do that. We can just log the error and return.
		return fmt.Errorf("failed to handle code scanning alerts assignment: %w", err)
	}
	return nil
}

func (h *Handler) onCodeScanningAlertsAssigned(ctx context.Context, gh github.ClientInterface, assignment *jobs.Assignment, repo *github.GQLRepository, event CodeScanningAlertsAssignmentEvent, useAlertLinksInPR bool) error {
	ctx, span := h.obsv.Tracer.Start(ctx, "events.Handler.onCodeScanningAlertsAssigned")
	defer span.End()

	assigner := event.Actor
	actionType := jobs.AgentActionFix

	h.obsv.LoggerWithTelemetry(ctx).Info("Processing code scanning alerts")

	job := assignment.NewJob(actionType, &requestctx.UserInfo{
		ID:         uint64(assigner.ID),
		Login:      assigner.Login,
		TrackingID: assigner.TrackingID,
	}, repo.DefaultBranchRef.Name)
	job, err := h.jobsStore.CreateJob(ctx, job)

	if err != nil {
		return fmt.Errorf("failed to create job: %w", err)
	}

	jobutils.UpdateRequestContextJob(ctx, h.obsv, job)
	h.obsv.LoggerWithTelemetry(ctx).Info("Job pending for code scanning alerts assignment")

	alertNumbers := make([]string, len(event.Alerts))
	for i, alert := range event.Alerts {
		alertNumbers[i] = strconv.FormatInt(alert.Number, 10)
	}

	// TODO: use proper title and body, to keep it consistent we can add a prefix for title and a template for body
	// LLM summarizes the fixes and add them to the PR body with a caveat Fixes 0 suffix :)
	title := fmt.Sprintf("[WIP] %s", "Autofix Code Scanning Alert")
	body := generatePRBodyForCodeScanningAlerts(alertNumbers)

	// TODO: we need to use near sequential number to avoid collisions
	num := 1 + rand.Intn(1000)
	branchSuffix := fmt.Sprintf("apply-autofixes-%d", num)

	newBranchName, err := gh.GenerateBranchName(ctx, repo.Owner.Login, repo.Name, branchSuffix)
	if err != nil {
		return fmt.Errorf("failed to generate branch name: %w", err)
	}

	err = h.createBranch(ctx, gh, job, *repo, newBranchName)
	if err != nil {
		return fmt.Errorf("failed to create branch: %w", err)
	}

	pr, err := h.OpenPullRequest(ctx, gh, job, assignment, *repo, newBranchName, title, body)
	if err != nil {
		return fmt.Errorf("failed to create pull request: %w", err)
	}

	// Get the problem statement for the issue. It won't include the PR since it's
	// empty at this point and will throw off the agent if it's included.
	problemStatement, err := buildProblemStatement(event, useAlertLinksInPR, repo.NameWithOwner)
	if err != nil {
		return fmt.Errorf("failed to create problem statement: %w", err)
	}
	job.ProblemStatement = problemStatement

	job.RunOptions = jobs.RunOptions{
		RunName: fmt.Sprintf("Applying Code Scanning Autofix on PR #%d", pr.Number),
	}

	job.EventType = jobs.EventTypeCodeScanningAlertsAssignment
	job.EventURL = event.EventUrl
	job.EventIdentifiers = alertNumbers

	job, err = h.jobsStore.UpdateJob(ctx, job)
	if err != nil {
		return fmt.Errorf("failed to update job: %w", err)
	}

	h.obsv.LoggerWithTelemetry(ctx).Info("Queuing new job for code scanning alerts")

	if err := h.jobExecutor.QueueJob(ctx, gh, job); err != nil {
		return fmt.Errorf("failed to queue job: %w", err)
	}

	return nil
}

func generatePRBodyForCodeScanningAlerts(alertNumbers []string) string {
	return fmt.Sprintf("Thanks for assigning these code scanning alerts (%s) to me.\nI'm starting to work on it and will keep this PR's description up to date as I form a plan and make progress.", strings.Join(alertNumbers, ", "))
}

func buildProblemStatement(event CodeScanningAlertsAssignmentEvent, userAlertLinkInPR bool, repoNameWithOwner string) (jobs.ProblemStatement, error) {
	diff_ex := `
diff --git a/index.js b/index.js
--- a/index.js
+++ b/index.js
@@ -1 +1,2 @@
+const escape = require('escape-html');
 const express = require('express');
@@ -3,2 +4,2 @@
 const app = express();
-app.get('/', (req, res) => res.send("foo bar"));
\ No newline at end of file
+app.get('/', (req, res) => res.send(" foo bar baz"));
\ No newline at end of file
`

	var sb strings.Builder
	sb.WriteString("\n")
	sb.WriteString("This section lists some code changes.\n")
	sb.WriteString("You can find the code changes in following format\n")
	sb.WriteString("File: <file_path>\n")
	sb.WriteString("Diff:\n")
	sb.WriteString("<diff_content>\n")
	sb.WriteString("\n<diff_content> is base64 encoded string, Below is an example <diff_content> after decoding\n")
	sb.WriteString("\n\n")
	sb.WriteString(diff_ex)
	sb.WriteString("\n\n")
	sb.WriteString("It is possible to have multiple <diff_content> for same <file_path>.\n")
	sb.WriteString("Your goal is to apply the diffs in given files in following steps.\n")
	sb.WriteString("1. Decode base64 encoded <diff_content> to get the diff\n")
	sb.WriteString("2. Apply a <diff_content> for given <file_path>\n")
	sb.WriteString("3. Resolve any conflicts\n")
	sb.WriteString("4. Commit the changes for <file_path>, do not commit any other files!\n")
	sb.WriteString("5. Repeat step 1 to 4 for all the <diff_content> and <file_path>\n")
	sb.WriteString("Do not make any other changes anywhere in the repo, focus only the <diff_content> and <file_path>, only commit given <file_path>\n")
	sb.WriteString("\n")
	alert_links := make([]string, len(event.Alerts))
	for i, alert := range event.Alerts {
		alert_title := alert.Title
		alert_url := alert.Url
		if alert_title != "" && alert_url != "" {
			alert_links[i] = fmt.Sprintf("* [%s](%s)", alert_title, alert_url)
		} else {
			// fallback to just the alert number
			alert_links[i] = fmt.Sprintf("* [/security/code-scanning/%d](https://github.com/%s/security/code-scanning/%d)", alert.Number, repoNameWithOwner, alert.Number)
		}
		sb.WriteString("-- diffs to apply\n")
		for _, file := range alert.SuggestedFix.Files {
			sb.WriteString(fmt.Sprintf("File:%s\n", file.FilePath))
			sb.WriteString(fmt.Sprintf("Diff:\n%s\n", file.DiffContent))
		}
	}
	if userAlertLinkInPR {
		alert_links_str := strings.Join(alert_links, "\n")
		sb.WriteString("\n")
		sb.WriteString(fmt.Sprintf("In the final PR description add a block with section ### Potential fix for alerts ### then list these alerts %s", alert_links_str))
		sb.WriteString("\n Here is an example:\n")
		sb.WriteString("### Potential fix for alerts ###\n")
		sb.WriteString(alert_links_str)
	}
	sb.WriteString("\n")

	return jobs.ProblemStatement{
		Content:           sb.String(),
		ContentFilterMode: jobs.ProblemStatementContentFilterModeHiddenCharacters,
	}, nil
}
