package events

import (
	"context"
	"testing"

	et "github.com/github/sweagentd/internal/events/eventstest"
	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/observability"
	"github.com/stretchr/testify/require"
)

func TestOnIssueUnassigned(t *testing.T) {
	obsv := observability.NewNoopExporters()
	ghFactory := &github.NoopClientFactory{
		GitHubAppLogin: et.GitHubAppLogin,
		GitHubBotLogin: et.GitHubBotLogin,
	}
	capiClient, jobStore, ghTwirp := commonHandlerDependencies()

	t.Run("ignores events where Co<PERSON><PERSON> was not assigned/unassigned", func(t *testing.T) {
		tests := []struct {
			name             string
			featureFlagState bool
		}{
			{
				name:             "inspect assignee when feature flag is enabled",
				featureFlagState: true,
			},
			{
				name:             "use old assignees when feature flag is disabled",
				featureFlagState: false,
			},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				ffClient := featureflags.NewNoopClient(map[string]bool{
					"sweagentd_cleanup_dup_assignment_hack":         tt.featureFlagState,
					featureflags.CopilotSWEAgentOnlyWakeOnAtCopilot: true,
				})
				ghFactory.MockIssueContext = &github.IssueContext{Repository: *et.Repo(), Issue: *et.Issue()}

				h, _ := NewTestHandler(obsv, capiClient, ffClient, jobStore, ghTwirp, ghFactory)

				err := h.OnIssueUnassigned(context.Background(), IssueAssignmentEvent{
					Actor:     EventActorReader(),
					Repo:      EventRepository(),
					Issue:     EventIssue(),
					Action:    IssueAssignmentEventActionUnassigned,
					Assignees: []string{et.ActorLoginNone},
					Assignee:  et.ActorLoginNone,
				})

				require.NoError(t, err)
			})
		}
	})
}
