package events

import (
	"context"
	"fmt"
	"testing"

	copilotUsersTwirp "github.com/github/copilot-twirp/proto/users/v1"
	"github.com/github/hydro-client-go/v7/pkg/hydro"
	sweagentv0 "github.com/github/hydro-schemas-go/hydro/schemas/sweagentd/v0"
	sweagentEntitiesv0 "github.com/github/hydro-schemas-go/hydro/schemas/sweagentd/v0/entities"
	"github.com/github/sweagentd/internal/capi"
	et "github.com/github/sweagentd/internal/events/eventstest"
	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/githubtwirp"
	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/observability"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/encoding/protojson"
)

func TestOnPullRequestCommentCreated(t *testing.T) {
	obsv := observability.NewNoopExporters()
	ghFactory := &github.NoopClientFactory{
		GitHubAppLogin: et.GitHubAppLogin,
		GitHubBotLogin: et.GitHubBotLogin,
	}
	ffClient := featureflags.NewNoopClient(map[string]bool{
		featureflags.CopilotSWEAgentOnlyWakeOnAtCopilot: true,
		featureflags.CopilotSWEAgentAqueductJobQueuing:  true,
	})

	t.Run("queues the agent job", func(t *testing.T) {
		tests := []struct {
			name           string
			queuingEnabled bool
		}{
			{
				name:           "queues job when queuing is enabled",
				queuingEnabled: true,
			},
			{
				name:           "executes job when queuing is disabled",
				queuingEnabled: false,
			},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				ffc := featureflags.NewNoopClient(map[string]bool{
					featureflags.CopilotSWEAgentAqueductJobQueuing: tt.queuingEnabled,
				})
				capiClient, jobStore, ghTwirp := commonHandlerDependencies()
				ghTwirp.NoopUserDetailAPI.MockGetCopilotUser = func(ctx context.Context, req *copilotUsersTwirp.GetCopilotUserRequest) (*copilotUsersTwirp.GetCopilotUserResponse, error) {
					return &copilotUsersTwirp.GetCopilotUserResponse{
						UserDetails: &copilotUsersTwirp.CopilotUserDetails{
							CopilotPlan: copilotUsersTwirp.CopilotPlan_COPILOT_PLAN_ENTERPRISE,
						},
					}, nil
				}
				h, executor := NewTestHandler(obsv, capiClient, ffc, jobStore, ghTwirp, ghFactory)
				h.jobsStore.CreateAssignment(context.Background(), et.Assignment())

				actor := et.ActorWriter()
				pr := et.PullRequest(*et.UserCopilot())
				pr = et.AddCommentToPullRequest(pr, true, *actor)

				ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

				err := h.OnPullRequestCommentCreated(context.Background(), PullRequestCommentCreatedEvent{
					Actor:       EventActor(actor),
					Repo:        EventRepository(),
					PullRequest: EventPullRequest(),
					Comment: &PullRequestComment{
						ID:     pr.Comments.Nodes[0].GetID(),
						NodeID: pr.Comments.Nodes[0].NodeID,
					},
				})
				require.NoError(t, err)

				if tt.queuingEnabled {
					require.Len(t, executor.QueuedJobs(), 1)
					require.Len(t, executor.LaunchAgentCalls(), 0)

					require.Equal(t, et.ActorWriter().GetID(), executor.QueuedJobs()[0].ActorId)
				} else {
					require.Len(t, executor.QueuedJobs(), 0)
					require.Len(t, executor.LaunchAgentCalls(), 1)

					require.Equal(t, et.ActorWriter().GetID(), executor.LaunchAgentCalls()[0].ActorID)
				}
			})
		}
	})

	t.Run("ignores events on PRs that are closed", func(t *testing.T) {
		h, executor := NewDefaultTestHandler(obsv, ffClient, ghFactory)
		h.jobsStore.CreateAssignment(context.Background(), et.Assignment())
		h.jobsStore.CreateJob(context.Background(), et.Job(jobs.JobStatusCompleted))

		actor := et.ActorWriter()
		pr := et.PullRequest(*et.UserCopilot())
		pr = et.AddCommentToPullRequest(pr, true, *actor)
		pr.Closed = true

		ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

		err := h.OnPullRequestCommentCreated(context.Background(), PullRequestCommentCreatedEvent{
			Actor:       EventActor(actor),
			Repo:        EventRepository(),
			PullRequest: EventPullRequest(),
			Comment: &PullRequestComment{
				ID:     pr.Comments.Nodes[0].GetID(),
				NodeID: pr.Comments.Nodes[0].NodeID,
			},
		})

		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 0)
	})

	t.Run("ignores events on PRs not authored by Copilot", func(t *testing.T) {
		h, executor := NewDefaultTestHandler(obsv, ffClient, ghFactory)
		h.jobsStore.CreateAssignment(context.Background(), et.Assignment())
		h.jobsStore.CreateJob(context.Background(), et.Job(jobs.JobStatusCompleted))

		actor := et.ActorWriter()
		pr := et.PullRequestWithAuthor(et.ActorAdmin(), *et.UserCopilot())
		pr = et.AddCommentToPullRequest(pr, true, *actor)

		ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

		err := h.OnPullRequestCommentCreated(context.Background(), PullRequestCommentCreatedEvent{
			Actor:       EventActor(actor),
			Repo:        EventRepository(),
			PullRequest: EventPullRequest(),
			Comment: &PullRequestComment{
				ID:     pr.Comments.Nodes[0].GetID(),
				NodeID: pr.Comments.Nodes[0].NodeID,
			},
		})

		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 0)
	})

	t.Run("ignores events on PRs assigned to other users but not Copilot", func(t *testing.T) {
		h, executor := NewDefaultTestHandler(obsv, ffClient, ghFactory)
		h.jobsStore.CreateAssignment(context.Background(), et.Assignment())
		h.jobsStore.CreateJob(context.Background(), et.Job(jobs.JobStatusCompleted))

		actor := et.ActorWriter()
		pr := et.PullRequest(*et.UserReader())
		pr = et.AddCommentToPullRequest(pr, true, *actor)
		et.AddAssigneesToPullRequest(pr, *et.UserWriter())

		ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

		err := h.OnPullRequestCommentCreated(context.Background(), PullRequestCommentCreatedEvent{
			Actor:       EventActor(actor),
			Repo:        EventRepository(),
			PullRequest: EventPullRequest(),
			Comment: &PullRequestComment{
				ID:     pr.Comments.Nodes[0].GetID(),
				NodeID: pr.Comments.Nodes[0].NodeID,
			},
		})

		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 0)
	})

	t.Run("ignores comments where the commenter doesn't have repo write access", func(t *testing.T) {
		h, executor := NewDefaultTestHandler(obsv, ffClient, ghFactory)
		h.jobsStore.CreateAssignment(context.Background(), et.Assignment())
		h.jobsStore.CreateJob(context.Background(), et.Job(jobs.JobStatusCompleted))

		actor := et.ActorReader()
		pr := et.PullRequest()
		pr = et.AddCommentToPullRequest(pr, true, *actor)

		ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

		err := h.OnPullRequestCommentCreated(context.Background(), PullRequestCommentCreatedEvent{
			Actor:       EventActor(actor),
			Repo:        EventRepository(),
			PullRequest: EventPullRequest(),
			Comment: &PullRequestComment{
				ID:     pr.Comments.Nodes[0].GetID(),
				NodeID: pr.Comments.Nodes[0].NodeID,
			},
		})

		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 0)
	})

	t.Run("ignores comments created by bots", func(t *testing.T) {
		h, executor := NewDefaultTestHandler(obsv, ffClient, ghFactory)
		h.jobsStore.CreateAssignment(context.Background(), et.Assignment())
		h.jobsStore.CreateJob(context.Background(), et.Job(jobs.JobStatusCompleted))

		bot := et.ActorBot()
		pr := et.PullRequest(*et.UserCopilot())
		pr = et.AddCommentToPullRequest(pr, true, *bot)

		ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

		err := h.OnPullRequestCommentCreated(context.Background(), PullRequestCommentCreatedEvent{
			Actor:       EventActor(bot),
			Repo:        EventRepository(),
			PullRequest: EventPullRequest(),
			Comment: &PullRequestComment{
				ID:     pr.Comments.Nodes[0].GetID(),
				NodeID: pr.Comments.Nodes[0].NodeID,
			},
		})

		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 0)
	})

	t.Run("skips launch agent with pending and running jobs", func(t *testing.T) {
		h, executor := NewDefaultTestHandler(obsv, ffClient, ghFactory)
		assignment := et.Assignment()
		h.jobsStore.CreateAssignment(context.Background(), assignment)
		h.jobsStore.CreateJob(context.Background(), et.Job(jobs.JobStatusRunning))
		h.jobsStore.CreateJob(context.Background(), et.Job(jobs.JobStatusPending))

		actor := et.ActorWriter()
		pr := et.PullRequest(*et.UserCopilot())
		pr = et.AddCommentToPullRequest(pr, true, *actor)

		ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

		err := h.OnPullRequestCommentCreated(context.Background(), PullRequestCommentCreatedEvent{
			Actor:       EventActor(actor),
			Repo:        EventRepository(),
			PullRequest: EventPullRequest(),
			Comment: &PullRequestComment{
				ID:     pr.Comments.Nodes[0].GetID(),
				NodeID: pr.Comments.Nodes[0].NodeID,
			},
		})

		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 0)
	})

	t.Run("launches agent with pending and completed jobs", func(t *testing.T) {
		h, executor := NewDefaultTestHandler(obsv, ffClient, ghFactory)
		assignment := et.Assignment()
		h.jobsStore.CreateAssignment(context.Background(), assignment)
		h.jobsStore.CreateJob(context.Background(), et.Job(jobs.JobStatusCompleted))
		h.jobsStore.CreateJob(context.Background(), et.Job(jobs.JobStatusPending))

		actor := et.ActorWriter()
		pr := et.PullRequest(*et.UserCopilot())
		pr = et.AddCommentToPullRequest(pr, true, *actor)

		ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

		err := h.OnPullRequestCommentCreated(context.Background(), PullRequestCommentCreatedEvent{
			Actor:       EventActor(actor),
			Repo:        EventRepository(),
			PullRequest: EventPullRequest(),
			Comment: &PullRequestComment{
				ID:     pr.Comments.Nodes[0].GetID(),
				NodeID: pr.Comments.Nodes[0].NodeID,
			},
		})

		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 1)
	})

	t.Run("launches agent with pending and completed jobs", func(t *testing.T) {
		messageChan := make(chan hydro.Message, 1)
		sink, err := hydro.NewMemorySink(messageChan)
		require.NoError(t, err)
		publisher, _ := hydro.NewPublisher(sink, hydro.WithEncoder(hydro.NewJSONEncoder()))
		obsvWithPublisher := obsv.WithPublisher(publisher)
		ffClient := featureflags.NewNoopClient(map[string]bool{
			"sweagentd_publish_telemetry_events_to_hydro":   true,
			featureflags.CopilotSWEAgentOnlyWakeOnAtCopilot: true,
			featureflags.CopilotSWEAgentAqueductJobQueuing:  false, // Disable queuing for this test
		})
		h, executor := NewDefaultTestHandler(obsvWithPublisher, ffClient, ghFactory)

		assignment := et.Assignment()
		h.jobsStore.CreateAssignment(context.Background(), assignment)
		h.jobsStore.CreateJob(context.Background(), et.Job(jobs.JobStatusCompleted))
		h.jobsStore.CreateJob(context.Background(), et.Job(jobs.JobStatusPending))
		actor := et.ActorWriter()
		pr := et.PullRequest(*et.UserCopilot())
		pr = et.AddCommentToPullRequest(pr, true, *actor)

		ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

		eventRepo := EventRepository()
		prComment := &PullRequestComment{
			ID:     pr.Comments.Nodes[0].GetID(),
			NodeID: pr.Comments.Nodes[0].NodeID,
		}

		err = h.OnPullRequestCommentCreated(context.Background(), PullRequestCommentCreatedEvent{
			Actor:       EventActor(actor),
			Repo:        eventRepo,
			PullRequest: EventPullRequest(),
			Comment:     prComment,
		})

		require.NoError(t, err)
		require.Len(t, executor.LaunchAgentCalls(), 1)

		select {
		case eventReceived := <-messageChan:
			var jobStartedEvent sweagentv0.JobStarted
			protojson.Unmarshal(eventReceived.Value, &jobStartedEvent)
			require.Equal(t, sweagentEntitiesv0.JobTrigger_PULL_REQUEST_COMMENT_CREATED, jobStartedEvent.Trigger.Type)
			require.Equal(t, prComment.ID, jobStartedEvent.Trigger.IdentifierId)
			require.Equal(t, sweagentEntitiesv0.ActionType_FIX, jobStartedEvent.Action)
			require.Equal(t, eventRepo.ID, jobStartedEvent.Job.RepositoryId)
			require.Equal(t, assignment.OwnerID, jobStartedEvent.Job.OwnerId)
			require.Equal(t, *pr.ID.Ptr(), jobStartedEvent.Job.PullRequestId)
		default:
			t.Fail()
		}
	})

	t.Run("launches the agent with no existing jobs", func(t *testing.T) {
		capiClient, jobStore, ghTwirp := commonHandlerDependencies()
		ghTwirp.NoopUserDetailAPI.MockGetCopilotUser = func(ctx context.Context, req *copilotUsersTwirp.GetCopilotUserRequest) (*copilotUsersTwirp.GetCopilotUserResponse, error) {
			return &copilotUsersTwirp.GetCopilotUserResponse{
				UserDetails: &copilotUsersTwirp.CopilotUserDetails{
					CopilotPlan: copilotUsersTwirp.CopilotPlan_COPILOT_PLAN_ENTERPRISE,
				},
			}, nil
		}
		h, executor := NewTestHandler(obsv, capiClient, ffClient, jobStore, ghTwirp, ghFactory)
		h.jobsStore.CreateAssignment(context.Background(), et.Assignment())

		actor := et.ActorWriter()
		pr := et.PullRequest(*et.UserCopilot())
		pr = et.AddCommentToPullRequest(pr, true, *actor)

		ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

		err := h.OnPullRequestCommentCreated(context.Background(), PullRequestCommentCreatedEvent{
			Actor:       EventActor(actor),
			Repo:        EventRepository(),
			PullRequest: EventPullRequest(),
			Comment: &PullRequestComment{
				ID:     pr.Comments.Nodes[0].GetID(),
				NodeID: pr.Comments.Nodes[0].NodeID,
			},
		})

		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 1)
	})

	t.Run("skips launching the agent with existing running job", func(t *testing.T) {
		capiClient, jobStore, ghTwirp := commonHandlerDependencies()
		ghTwirp.NoopUserDetailAPI.MockGetCopilotUser = func(ctx context.Context, req *copilotUsersTwirp.GetCopilotUserRequest) (*copilotUsersTwirp.GetCopilotUserResponse, error) {
			return &copilotUsersTwirp.GetCopilotUserResponse{
				UserDetails: &copilotUsersTwirp.CopilotUserDetails{
					CopilotPlan: copilotUsersTwirp.CopilotPlan_COPILOT_PLAN_ENTERPRISE,
				},
			}, nil
		}
		h, executor := NewTestHandler(obsv, capiClient, ffClient, jobStore, ghTwirp, ghFactory)
		h.jobsStore.CreateAssignment(context.Background(), et.Assignment())
		h.jobsStore.CreateJob(context.Background(), et.Job(jobs.JobStatusRunning))

		actor := et.ActorWriter()
		pr := et.PullRequest(*et.UserCopilot())
		pr = et.AddCommentToPullRequest(pr, true, *actor)

		ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

		err := h.OnPullRequestCommentCreated(context.Background(), PullRequestCommentCreatedEvent{
			Actor:       EventActor(actor),
			Repo:        EventRepository(),
			PullRequest: EventPullRequest(),
			Comment: &PullRequestComment{
				ID:     pr.Comments.Nodes[0].GetID(),
				NodeID: pr.Comments.Nodes[0].NodeID,
			},
		})

		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 0)
	})

	t.Run("launches the agent with existing terminal jobs", func(t *testing.T) {
		capiClient, jobStore, ghTwirp := commonHandlerDependencies()
		ghTwirp.NoopUserDetailAPI.MockGetCopilotUser = func(ctx context.Context, req *copilotUsersTwirp.GetCopilotUserRequest) (*copilotUsersTwirp.GetCopilotUserResponse, error) {
			return &copilotUsersTwirp.GetCopilotUserResponse{
				UserDetails: &copilotUsersTwirp.CopilotUserDetails{
					CopilotPlan: copilotUsersTwirp.CopilotPlan_COPILOT_PLAN_ENTERPRISE,
				},
			}, nil
		}
		h, executor := NewTestHandler(obsv, capiClient, ffClient, jobStore, ghTwirp, ghFactory)
		h.jobsStore.CreateAssignment(context.Background(), et.Assignment())
		h.jobsStore.CreateJob(context.Background(), et.Job(jobs.JobStatusCompleted))

		actor := et.ActorWriter()
		pr := et.PullRequest(*et.UserCopilot())
		pr = et.AddCommentToPullRequest(pr, true, *actor)

		ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

		err := h.OnPullRequestCommentCreated(context.Background(), PullRequestCommentCreatedEvent{
			Actor:       EventActor(actor),
			Repo:        EventRepository(),
			PullRequest: EventPullRequest(),
			Comment: &PullRequestComment{
				ID:     pr.Comments.Nodes[0].GetID(),
				NodeID: pr.Comments.Nodes[0].NodeID,
			},
		})

		require.NoError(t, err)
		require.Len(t, executor.QueuedJobs(), 1)
	})

	t.Run("handles wake logic properly", func(t *testing.T) {
		type scenario struct {
			bodyHasAtCopilot      bool
			onlyWakeOnAtCopilotFF bool
			shouldWakeResult      bool
			queued                bool
		}
		scenarios := []scenario{
			// Referencing @copilot should always launch
			{bodyHasAtCopilot: true, onlyWakeOnAtCopilotFF: true, shouldWakeResult: true, queued: true},
			{bodyHasAtCopilot: true, onlyWakeOnAtCopilotFF: true, shouldWakeResult: false, queued: true},
			{bodyHasAtCopilot: true, onlyWakeOnAtCopilotFF: false, shouldWakeResult: true, queued: true},
			{bodyHasAtCopilot: true, onlyWakeOnAtCopilotFF: false, shouldWakeResult: false, queued: true},
			// Not referencing @copilot should only launch if the feature flag is off and shouldWake returns true
			{bodyHasAtCopilot: false, onlyWakeOnAtCopilotFF: true, shouldWakeResult: true, queued: false},
			{bodyHasAtCopilot: false, onlyWakeOnAtCopilotFF: true, shouldWakeResult: false, queued: false},
			{bodyHasAtCopilot: false, onlyWakeOnAtCopilotFF: false, shouldWakeResult: true, queued: true},
			{bodyHasAtCopilot: false, onlyWakeOnAtCopilotFF: false, shouldWakeResult: false, queued: false},
		}
		for _, s := range scenarios {
			skipsText := "does not launch"
			if s.queued {
				skipsText = "launches"
			}
			atCopilotText := "with @copilot"
			if !s.bodyHasAtCopilot {
				atCopilotText = "without @copilot"
			}
			t.Run(fmt.Sprintf("%s %s, onlyWakeOnAtCopilot: %v, and shouldWake: %v", skipsText, atCopilotText, s.onlyWakeOnAtCopilotFF, s.shouldWakeResult), func(t *testing.T) {
				ffAlt := featureflags.NewNoopClient(map[string]bool{
					featureflags.CopilotSWEAgentOnlyWakeOnAtCopilot: s.onlyWakeOnAtCopilotFF,
					featureflags.CopilotSWEAgentAqueductJobQueuing:  true,
				})

				jobStore := jobs.NewMemoryStore()
				capiClient := capi.NewNoopClientWithFixedShouldWakeResponse(s.shouldWakeResult)
				ghTwirp := githubtwirp.NewNoopClient()
				ghTwirp.NoopUserDetailAPI.MockGetCopilotUser = func(ctx context.Context, req *copilotUsersTwirp.GetCopilotUserRequest) (*copilotUsersTwirp.GetCopilotUserResponse, error) {
					return &copilotUsersTwirp.GetCopilotUserResponse{
						UserDetails: &copilotUsersTwirp.CopilotUserDetails{
							CopilotPlan: copilotUsersTwirp.CopilotPlan_COPILOT_PLAN_ENTERPRISE,
						},
					}, nil
				}
				h, executor := NewTestHandler(obsv, capiClient, ffAlt, jobStore, ghTwirp, ghFactory)

				h.jobsStore.CreateAssignment(context.Background(), et.Assignment())
				h.jobsStore.CreateJob(context.Background(), et.Job(jobs.JobStatusCompleted))

				actor := et.ActorWriter()
				pr := et.PullRequest(*et.UserCopilot())
				if s.bodyHasAtCopilot {
					pr = et.AddCommentToPullRequestWithBody(pr, s.bodyHasAtCopilot, *actor, "@copilot comment-body")
				} else {
					pr = et.AddCommentToPullRequestWithBody(pr, s.bodyHasAtCopilot, *actor, "comment-body")
				}

				ghFactory.MockPullRequestContext = &github.PullRequestContext{Repository: *et.Repo(), PullRequest: *pr}

				err := h.OnPullRequestCommentCreated(context.Background(), PullRequestCommentCreatedEvent{
					Actor:       EventActor(actor),
					Repo:        EventRepository(),
					PullRequest: EventPullRequest(),
					Comment: &PullRequestComment{
						ID:     pr.Comments.Nodes[0].GetID(),
						NodeID: pr.Comments.Nodes[0].NodeID,
					},
				})

				require.NoError(t, err)
				if s.queued {
					require.Len(t, executor.QueuedJobs(), 1)
				} else {
					require.Len(t, executor.QueuedJobs(), 0)
				}
			})
		}
	})
}

func TestOnPullRequestCommentCreated_DoesMentionCopilot(t *testing.T) {
	atMentionTests := []struct {
		name     string
		body     string
		expected bool
	}{
		{
			name:     "@-mention",
			body:     "@Copilot comment-body",
			expected: true,
		},
		{
			name:     "@-mention with leading space",
			body:     " @Copilot comment-body",
			expected: true,
		},
		{
			name:     "@-mention with comma",
			body:     "@Copilot, comment-body",
			expected: true,
		},
		{
			name:     "@-mention with colon",
			body:     "@Copilot: comment-body",
			expected: true,
		},
		{
			name:     "@-mention with exclamation mark",
			body:     "@Copilot! comment-body",
			expected: true,
		},
		{
			name:     "@-mention with question mark",
			body:     "@Copilot? comment-body",
			expected: true,
		},
		{
			name:     "@-mention with period",
			body:     "@Copilot. comment-body",
			expected: true,
		},
		{
			name:     "@-mention with ellipsis",
			body:     "@Copilot... comment-body",
			expected: true,
		},
		{
			name:     "@-mention with newline",
			body:     "@Copilot\ncomment-body",
			expected: true,
		},
		{
			name:     "@-mention with random casing",
			body:     "@cOpIlOt comment-body",
			expected: true,
		},
		{
			name:     "no @-mention",
			body:     "comment-body",
			expected: false,
		},
		{
			name:     "ee@copilot",
			body:     "ee@copilot comment-body",
			expected: false,
		},
	}
	for _, tt := range atMentionTests {
		t.Run(tt.name, func(t *testing.T) {
			require.Equal(t, tt.expected, doesMentionCopilot(tt.body))
		})
	}
}
