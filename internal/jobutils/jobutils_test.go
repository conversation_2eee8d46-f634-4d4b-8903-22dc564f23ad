package jobutils

import (
	"context"
	"errors"
	"testing"

	copilotUsersTwirp "github.com/github/copilot-twirp/proto/users/v1"
	"github.com/github/sweagentd/internal/events/eventstest"
	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/githubtwirp"
	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/observability"
	"github.com/github/sweagentd/internal/requestctx"
	"github.com/stretchr/testify/require"
)

var mockTwirpClientForEnterpriseUser = newMockTwirpClientWithUserDetails(&copilotUsersTwirp.CopilotUserDetails{
	CopilotPlan: copilotUsersTwirp.CopilotPlan_COPILOT_PLAN_ENTERPRISE,
})

var mockTwirpClientForBusinessUser = newMockTwirpClientWithUserDetails(&copilotUsersTwirp.CopilotUserDetails{
	CopilotPlan: copilotUsersTwirp.CopilotPlan_COPILOT_PLAN_BUSINESS,
})

var mockTwirpClientForBusinessUserWithCfeAccess = newMockTwirpClientWithUserDetails(&copilotUsersTwirp.CopilotUserDetails{
	HasCfeAccess: true,
	CopilotPlan:  copilotUsersTwirp.CopilotPlan_COPILOT_PLAN_BUSINESS,
})

var mockTwirpClientForProPlusUser = newMockTwirpClientWithUserDetails(&copilotUsersTwirp.CopilotUserDetails{
	CopilotPlan: copilotUsersTwirp.CopilotPlan_COPILOT_PLAN_INDIVIDUAL_PRO,
})

var mockTwirpClientForProUser = newMockTwirpClientWithUserDetails(&copilotUsersTwirp.CopilotUserDetails{
	HasPaidAccess: true,
	CopilotPlan:   copilotUsersTwirp.CopilotPlan_COPILOT_PLAN_INDIVIDUAL,
})

func TestNewPendingJobForIssue(t *testing.T) {
	t.Run("determines model selection if model customization is enabled", func(t *testing.T) {
		issue := eventstest.Issue()
		issue.Labels = github.GQLConnection[github.GQLLabel]{
			Nodes: []github.GQLLabel{
				{
					Name: modelLabelPrefix + "gpt-12",
				},
			},
		}
		model := DetermineModel(
			context.Background(),
			observability.NewNoopExporters(),
			featureflags.NewNoopClient(map[string]bool{
				featureflags.CopilotSWEAgentCanSelectModel: true,
			}),
			mockTwirpClientForEnterpriseUser,
			eventstest.Assignment().NewJob(
				jobs.AgentActionFix,
				&requestctx.UserInfo{
					ID:         uint64(eventstest.UserWriter().ID),
					Login:      eventstest.UserWriter().Login,
					TrackingID: "tracking-id",
				},
				eventstest.Repo().DefaultBranchRef.Name,
			),
			issue.Labels.Nodes)

		require.Equal(t, "gpt-12", model)
	})

	t.Run("ignores model selection if model customization is not enabled", func(t *testing.T) {
		issue := eventstest.Issue()
		issue.Labels = github.GQLConnection[github.GQLLabel]{
			Nodes: []github.GQLLabel{
				{
					Name: modelLabelPrefix + "gpt-12",
				},
			},
		}

		model := DetermineModel(
			context.Background(),
			observability.NewNoopExporters(),
			featureflags.NewNoopClient(nil),
			mockTwirpClientForEnterpriseUser,
			eventstest.Assignment().NewJob(
				jobs.AgentActionFix,
				&requestctx.UserInfo{
					ID:         uint64(eventstest.UserWriter().ID),
					Login:      eventstest.UserWriter().Login,
					TrackingID: "tracking-id",
				},
				eventstest.Repo().DefaultBranchRef.Name,
			),
			issue.Labels.Nodes)

		require.Equal(t, DefaultModel, model)
	})
}

func TestNewPendingJobForPR(t *testing.T) {
	t.Run("ignores model selection if model customization is enabled", func(t *testing.T) {
		pr := eventstest.PullRequest()
		pr.Labels = github.GQLConnection[github.GQLLabel]{
			Nodes: []github.GQLLabel{
				{
					Name: modelLabelPrefix + "gpt-12",
				},
			},
		}

		model := DetermineModel(
			context.Background(),
			observability.NewNoopExporters(),
			featureflags.NewNoopClient(map[string]bool{
				featureflags.CopilotSWEAgentCanSelectModel: true,
			}),
			mockTwirpClientForEnterpriseUser,
			eventstest.Assignment().NewJob(
				jobs.AgentActionFixPRComment,
				&requestctx.UserInfo{
					ID:         uint64(eventstest.ActorWriter().GetID()),
					Login:      eventstest.ActorWriter().GetLogin(),
					TrackingID: "tracking-id",
				},
				pr.HeadRef.Name,
			),
			pr.Labels.Nodes)

		require.Equal(t, "gpt-12", model)
	})

	t.Run("ignores model selection if model customization is not enabled", func(t *testing.T) {
		pr := eventstest.PullRequest()
		pr.Labels = github.GQLConnection[github.GQLLabel]{
			Nodes: []github.GQLLabel{
				{
					Name: modelLabelPrefix + "gpt-12",
				},
			},
		}
		model := DetermineModel(
			context.Background(),
			observability.NewNoopExporters(),
			featureflags.NewNoopClient(nil),
			mockTwirpClientForEnterpriseUser,
			eventstest.Assignment().NewJob(
				jobs.AgentActionFixPRComment,
				&requestctx.UserInfo{
					ID:         uint64(eventstest.ActorWriter().GetID()),
					Login:      eventstest.ActorWriter().GetLogin(),
					TrackingID: "tracking-id",
				},
				pr.HeadRef.Name,
			),
			pr.Labels.Nodes)

		require.Equal(t, DefaultModel, model)
	})
}

func TestDetermineModel(t *testing.T) {
	t.Run("returns model if customization is enabled", func(t *testing.T) {
		pr := eventstest.PullRequest()
		pr.Labels = github.GQLConnection[github.GQLLabel]{
			Nodes: []github.GQLLabel{
				{
					Name: modelLabelPrefix + "gpt-12",
				},
			},
		}
		model := DetermineModel(
			context.Background(),
			observability.NewNoopExporters(),
			featureflags.NewNoopClient(map[string]bool{
				featureflags.CopilotSWEAgentCanSelectModel: true,
			}),
			mockTwirpClientForEnterpriseUser,
			eventstest.Assignment().NewJob(
				jobs.AgentActionFixPRComment,
				&requestctx.UserInfo{
					ID:         uint64(eventstest.UserWriter().ID),
					Login:      eventstest.UserWriter().Login,
					TrackingID: "tracking-id",
				},
				pr.HeadRef.Name,
			),
			pr.Labels.Nodes)

		require.Equal(t, "gpt-12", model)
	})

	t.Run("returns default model if customization is not enabled", func(t *testing.T) {
		job := eventstest.Assignment().NewJob(jobs.AgentActionFix, &requestctx.UserInfo{
			ID:         uint64(eventstest.UserWriter().ID),
			Login:      eventstest.UserWriter().Login,
			TrackingID: "tracking-id",
		}, "")

		model := DetermineModel(
			context.Background(),
			observability.NewNoopExporters(),
			featureflags.NewNoopClient(nil),
			mockTwirpClientForEnterpriseUser,
			job,
			eventstest.Issue().Labels.Nodes,
		)

		require.Equal(t, DefaultModel, model)
	})

	t.Run("returns candidate model if the sweagentd_use_candidate_model_as_default is enabled", func(t *testing.T) {
		model := DetermineModel(
			context.Background(),
			observability.NewNoopExporters(),
			featureflags.NewNoopClient(map[string]bool{
				"sweagentd_use_candidate_model_as_default": true,
			}),
			mockTwirpClientForEnterpriseUser,
			eventstest.Assignment().NewJob(
				jobs.AgentActionFix,
				&requestctx.UserInfo{
					ID:         uint64(eventstest.UserWriter().ID),
					Login:      eventstest.UserWriter().Login,
					TrackingID: "tracking-id",
				},
				"",
			),
			eventstest.Issue().Labels.Nodes,
		)
		require.Equal(t, CandidateModel, model)
	})

	t.Run("Uses sku to determine default model for Copilot users", func(t *testing.T) {
		for name, tt := range map[string]struct {
			client   githubtwirp.ClientInterface
			expected string
		}{
			"enterprise user": {
				client:   mockTwirpClientForEnterpriseUser,
				expected: DefaultModel,
			},
			"business user": {
				client:   mockTwirpClientForBusinessUser,
				expected: DefaultModelForBusinessOrPro,
			},
			"business user with CFE access": {
				client:   mockTwirpClientForBusinessUserWithCfeAccess,
				expected: DefaultModel,
			},
			"pro plus user": {
				client:   mockTwirpClientForProPlusUser,
				expected: DefaultModel,
			},
			"pro user": {
				client:   mockTwirpClientForProUser,
				expected: DefaultModelForBusinessOrPro,
			},
			"error fetching sku": {
				client: func() githubtwirp.ClientInterface {
					client := githubtwirp.NewNoopClient()
					client.NoopUserDetailAPI.MockGetCopilotUser = func(ctx context.Context, req *copilotUsersTwirp.GetCopilotUserRequest) (*copilotUsersTwirp.GetCopilotUserResponse, error) {
						return nil, errors.New("error fetching copilot user details")
					}
					return client
				}(),
				expected: DefaultModelForBusinessOrPro,
			},
		} {
			t.Run(name, func(t *testing.T) {
				model := DetermineModel(
					context.Background(),
					observability.NewNoopExporters(),
					featureflags.NewNoopClient(nil),
					tt.client,
					eventstest.Assignment().NewJob(
						jobs.AgentActionFixPRComment,
						&requestctx.UserInfo{
							ID:         uint64(eventstest.UserWriter().ID),
							Login:      eventstest.UserWriter().Login,
							TrackingID: "tracking-id",
						},
						eventstest.PullRequest().HeadRef.Name,
					),
					eventstest.PullRequest().Labels.Nodes)

				require.Equal(t, tt.expected, model)
			})
		}
	})
}

// newMockTwirpClientWithUserDetails returns a mock githubtwirp.ClientInterface that returns a response for GetCopilotUser
// with the provided CopilotUserDetails.
func newMockTwirpClientWithUserDetails(userDetails *copilotUsersTwirp.CopilotUserDetails) githubtwirp.ClientInterface {
	client := githubtwirp.NewNoopClient()
	client.NoopUserDetailAPI.MockGetCopilotUser = func(ctx context.Context, req *copilotUsersTwirp.GetCopilotUserRequest) (*copilotUsersTwirp.GetCopilotUserResponse, error) {
		return &copilotUsersTwirp.GetCopilotUserResponse{
			UserDetails: userDetails,
		}, nil
	}

	return client
}
