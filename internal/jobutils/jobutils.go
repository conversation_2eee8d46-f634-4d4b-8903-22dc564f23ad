package jobutils

import (
	"cmp"
	"context"
	"fmt"
	"math/rand"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/github/github-telemetry-go/kvp"
	"github.com/github/github-telemetry-go/log"
	sweagentv0 "github.com/github/hydro-schemas-go/hydro/schemas/sweagentd/v0"
	sweagentEntitiesv0 "github.com/github/hydro-schemas-go/hydro/schemas/sweagentd/v0/entities"
	"github.com/github/sweagentd/internal/capi"
	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/githubtwirp"
	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/launcher"
	"github.com/github/sweagentd/internal/observability"
	"github.com/github/sweagentd/internal/requestctx"
	sweagentdTwirp "github.com/github/sweagentd/proto/sweagentd/v1"
	gg "github.com/google/go-github/v72/github"
	"google.golang.org/protobuf/types/known/timestamppb"
)

const DefaultSessionErrorMessage string = "Copilot has encountered an error. See logs for additional details."

const PullRequestBodySuffixSeparator string = "<!-- START COPILOT CODING AGENT SUFFIX -->"
const PullRequestBodyTipSeparator string = "<!-- START COPILOT CODING AGENT TIPS -->"

// JobsForAssignment returns all jobs for a assignment, as well as the pending job (if any).
// If there are multiple pending jobs, an error is returned.
func JobsForAssignment(ctx context.Context, obsv observability.Exporters, ffClient featureflags.Client, gh github.ClientInterface, capiClient capi.Client, ghTwirp githubtwirp.ClientInterface, jobStore jobs.JobsStore, assignment *jobs.Assignment) (all []*jobs.Job, pending *jobs.Job, err error) {
	var pendingJob *jobs.Job

	allJobs, err := jobStore.GetJobsForAssignment(ctx, assignment)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get jobs for assignment: %w", err)
	}

	for _, job := range allJobs {
		if job.IsPending() {
			if pendingJob != nil {
				// There should only ever be one pending job for an assignment.
				return nil, nil, fmt.Errorf("multiple pending jobs found for assignment %s", assignment.ID)
			}
			pendingJob = job
		}
		if job.IsRunning() {
			// If the job status running, confirm that it is actually still running
			// and not stuck in a inaccurate state because of a failed action run
			if err := confirmRunningJob(ctx, obsv, ffClient, gh, capiClient, ghTwirp, jobStore, job); err != nil {
				return nil, nil, fmt.Errorf("failed to confirm running job: %w", err)
			}
		}
	}

	return allJobs, pendingJob, nil
}

// confirmRunningJob confirms that a job is still running by querying the workflow
// run and updates the job status if it is not.
func confirmRunningJob(ctx context.Context, obsv observability.Exporters, ffClient featureflags.Client, gh github.ClientInterface, capiClient capi.Client, ghTwirp githubtwirp.ClientInterface, jobStore jobs.JobsStore, job *jobs.Job) error {
	// ignore jobs that aren't running
	if job.Status != jobs.JobStatusRunning {
		return nil
	}

	// ignore jobs that aren't running on Actions
	// (or those created before we started tracking the launcher)
	if job.Launcher != "" && job.Launcher != launcher.LauncherTypeActions {
		return nil
	}

	UpdateRequestContextJob(ctx, obsv, job)
	obsv.LoggerWithTelemetry(ctx).Info("confirming running job status")

	runID, err := strconv.ParseInt(job.ComputeID, 10, 64)
	if err != nil {
		return fmt.Errorf("failed to parse compute id for running job: %w", err)
	}

	originalWorkflowCtx, _ := requestctx.WorkflowRun(ctx)
	if err := requestctx.AddWorkflowRun(ctx, &requestctx.CtxWorkflowRun{ID: runID}); err != nil {
		obsv.LoggerWithTelemetry(ctx).WithError(err).Error("failed to add workflow run to context")
	}

	workflowJobs, err := gh.ListWorkflowJobs(ctx, job.RepoOwner, job.RepoName, runID)
	if err != nil {
		return fmt.Errorf("failed to list workflow jobs for running job: %w", err)
	}

	obsv.LoggerWithTelemetry(ctx).WithFields(kvp.Int("gh.workflow_run.jobs", len(workflowJobs))).
		Info("found workflow jobs for running job")

	if len(workflowJobs) == 0 {
		return fmt.Errorf("no workflow jobs found for running job")
	}

	if len(workflowJobs) != 1 {
		// our dynamic workflow will only have one job so this should never happen
		return fmt.Errorf("unexpected number of workflow jobs found for running job")
	}

	workflowJob := workflowJobs[0]

	// only workflow runs in a final state will have a conclusion
	conclusion := workflowJob.GetConclusion()
	if conclusion == "" {
		obsv.LoggerWithTelemetry(ctx).Info("confirmed job is still running")

		// clean up the context
		UpdateRequestContextJob(ctx, obsv, nil)
		if err := requestctx.AddWorkflowRun(ctx, originalWorkflowCtx); err != nil {
			obsv.LoggerWithTelemetry(ctx).WithError(err).Error("failed to add workflow run to context")
		}

		return nil // still running
	}

	obsv.LoggerWithTelemetry(ctx).Info("workflow job concluded, finalizing job")

	if err := FinalizeJobForWorkflow(ctx, obsv, ffClient, jobStore, job, conclusion, workflowJob.GetCompletedAt().Unix()); err != nil {
		return fmt.Errorf("failed to finalize job: %w", err)
	}

	sessionClient, err := createSessionClient(ctx, capiClient, ghTwirp, job.ActorID, job.RepoID)
	if err != nil {
		return fmt.Errorf("failed to create session client: %w", err)
	}
	if sessionClient == nil {
		obsv.LoggerWithTelemetry(ctx).Info("managed sessions are not enabled for this user/repo")
	} else {
		finalizeSession := FinalizeSessionParams{
			Gh:            gh,
			Job:           job,
			WorkflowJob:   workflowJob,
			WorkflowJobID: workflowJob.ID,
			SessionClient: sessionClient,
		}
		if err := FinalizeSessionForWorkflow(ctx, obsv, finalizeSession); err != nil {
			return fmt.Errorf("failed to finalize job session: %w", err)
		}
	}

	// clean up the context
	UpdateRequestContextJob(ctx, obsv, nil)
	if err := requestctx.AddWorkflowRun(ctx, originalWorkflowCtx); err != nil {
		obsv.LoggerWithTelemetry(ctx).WithError(err).Error("failed to add workflow run to context")
	}

	return nil
}

func FinalizeJobForWorkflow(ctx context.Context, obsv observability.Exporters, ffClient featureflags.Client, jobsStore jobs.JobsStore, job *jobs.Job, conclusion string, completedAt int64) error {
	logger := obsv.LoggerWithTelemetry(ctx)
	logger.Info("finalizing job for workflow run")

	var jobStatus jobs.JobStatus

	switch conclusion {
	case "success":
		jobStatus = jobs.JobStatusCompleted
	case "cancelled":
		jobStatus = jobs.JobStatusCancelled
	case "failure":
		jobStatus = jobs.JobStatusFailed
	default:
		logger.Error("unexpected workflow job conclusion", kvp.String("gh.workflow_run.conclusion", conclusion))
		jobStatus = jobs.JobStatusFailed
	}

	return FinalizeJob(ctx, obsv, ffClient, jobsStore, job, jobStatus, completedAt)
}

func FinalizeJob(ctx context.Context, obsv observability.Exporters, ffClient featureflags.Client, jobsStore jobs.JobsStore, job *jobs.Job, jobStatus jobs.JobStatus, completedAt int64) error {
	logger := obsv.LoggerWithTelemetry(ctx)
	logger.Info("finalizing job")

	if job.IsTerminal() && job.Status == jobStatus {
		logger.Info("job is already in a terminal state, nothing to do")
		return nil // already in terminal state
	}

	patch := jobs.JobPatch{Status: &jobStatus}
	if job.CompletedAt == 0 {
		if completedAt == 0 {
			completedAt = time.Now().Unix()
		}
		patch.CompletedAt = &completedAt
	}

	if err := jobsStore.PatchJob(ctx, job, patch); err != nil {
		return fmt.Errorf("failed to patch job: %w", err)
	}

	logger.Info("patched job with terminal status")

	if ffClient.IsEnabledForUserOrRepo(ctx, "sweagentd_publish_telemetry_events_to_hydro", job.ActorID, job.RepoID) {
		jobCompletedEvent := ToJobCompleted(job)
		err := obsv.HydroPbl.Publish(jobCompletedEvent)
		if err != nil {
			logger.WithContext(ctx).WithError(err).Error("failed to publish job_completed event")
		}

		restrictedJobCompletedEvent := ToRestrictedJobCompleted(job)
		err = obsv.HydroPbl.Publish(restrictedJobCompletedEvent)
		if err != nil {
			logger.WithContext(ctx).WithError(err).Error("failed to publish restricted job_completed event")
		}
	}

	return nil
}

// TODO(colbylwilliams): move to a telemetry package
func ToJobCompleted(job *jobs.Job) *sweagentv0.JobCompleted {
	event := &sweagentv0.JobCompleted{
		Job:           jobs.ToHydro(job),
		CompletedAt:   timestamppb.New(time.Unix(job.CompletedAt, 0)),
		TerminalState: toTerminalStateProtobuf(job.Status),
		DurationSec:   job.CompletedAt - job.CreatedAt,
	}
	if job.Experiments != nil && job.Experiments.AssignmentContext != "" {
		event.ExpAssignmentContext = job.Experiments.AssignmentContext
	}
	return event
}

// TODO(colbylwilliams): move to a telemetry package
func ToRestrictedJobCompleted(job *jobs.Job) *sweagentv0.RestrictedJobCompleted {
	event := sweagentv0.RestrictedJobCompleted{
		Job:           jobs.ToHydro(job),
		CompletedAt:   timestamppb.New(time.Unix(job.CompletedAt, 0)),
		TerminalState: toTerminalStateProtobuf(job.Status),
		DurationSec:   job.CompletedAt - job.CreatedAt,
	}
	if job.Experiments != nil && job.Experiments.AssignmentContext != "" {
		event.ExpAssignmentContext = job.Experiments.AssignmentContext
	}
	if job.Error != nil && job.Error.Message != "" {
		event.Result = job.Error.Message
	}
	return &event
}

func toTerminalStateProtobuf(terminalState jobs.JobStatus) sweagentEntitiesv0.TerminalState {
	switch terminalState {
	case jobs.JobStatusCompleted:
		return sweagentEntitiesv0.TerminalState_COMPLETED
	case jobs.JobStatusFailed:
		return sweagentEntitiesv0.TerminalState_FAILED
	case jobs.JobStatusCancelled:
		return sweagentEntitiesv0.TerminalState_CANCELLED
	default:
		return sweagentEntitiesv0.TerminalState_UNKNOWN_STATE
	}
}

func GetSessionForJob(ctx context.Context, obsv observability.Exporters, capiClient capi.Client, ghTwirp githubtwirp.ClientInterface, job *jobs.Job) (*capi.Session, capi.SessionClient, error) {
	logger := obsv.LoggerWithTelemetry(ctx)
	logger.Info("getting session for job")

	requestctx.AddSession(ctx, &requestctx.CtxSession{
		ResourceType: string(capi.ResourceTypePullRequest),
		ResourceID:   job.PullRequestID,
		EventType:    string(job.EventType),
	})

	if job.SessionID == "" {
		return nil, nil, fmt.Errorf("no session ID found for job")
	}

	sessionClient, err := createSessionClient(ctx, capiClient, ghTwirp, job.ActorID, job.RepoID)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to create session client: %w", err)
	}

	if sessionClient == nil {
		return nil, nil, fmt.Errorf("managed sessions are not enabled for this user/repo")
	}

	session, err := sessionClient.GetSession(ctx, job.SessionID)
	if err != nil {
		return nil, sessionClient, fmt.Errorf("failed to get session: %w", err)
	}
	return session, sessionClient, nil
}

type FinalizeSessionParams struct {
	Gh            github.ClientInterface
	Job           *jobs.Job
	WorkflowJob   *gg.WorkflowJob
	WorkflowJobID *int64
	SessionClient capi.SessionClient
}

func FinalizeSessionForWorkflow(ctx context.Context, obsv observability.Exporters, params FinalizeSessionParams) error {
	logger := obsv.LoggerWithTelemetry(ctx)
	logger.Info("finalizing session for workflow run")

	job := params.Job
	if job == nil {
		return fmt.Errorf("parameter Job is nil")
	}
	workflowJob := params.WorkflowJob
	workflowJobID := params.WorkflowJobID

	requestctx.AddSession(ctx, &requestctx.CtxSession{
		ResourceType: string(capi.ResourceTypePullRequest),
		ResourceID:   job.PullRequestID,
		EventType:    string(job.EventType),
	})

	if !job.IsTerminal() {
		logger.Info("job is not in a terminal state, nothing to do")
		return nil
	}

	if job.SessionID == "" {
		logger.Info("no session ID found for job")
		return nil
	}

	session, err := params.SessionClient.GetSession(ctx, job.SessionID)
	if err != nil {
		return fmt.Errorf("failed to get session: %w", err)
	}
	AddRequestContextSession(ctx, obsv, session)

	if capi.IsFinalState(session.State) {
		// Session is already in a final state, nothing to do
		logger.Info("session is already in a final state")
		return nil
	}

	newState := MapJobStatusToSessionState(job.Status)
	if newState == "" {
		logger.Error("Failed to map job status to session state", kvp.String("job.status", string(job.Status)))
		return nil
	}
	session.State = newState
	sessionUpdate := capi.SessionUpdate{State: session.State}

	// If the job failed, and the session doesn't already have an error message,
	// use the workflow job details to (conditionally) create a useful error
	hasError := session.Error != nil && session.Error.Message != ""
	// When Actions job timed out, the job conclusion is "cancelled"
	if (session.State == capi.SessionStateFailed || session.State == capi.SessionStateCancelled) && !hasError {
		if workflowJob == nil && workflowJobID != nil {
			workflowJob, err = params.Gh.GetWorkflowJobByID(ctx, job.RepoOwner, job.RepoName, *workflowJobID)
			if err != nil {
				return fmt.Errorf("failed to get workflow job by ID: %w", err)
			}
		}
		errorMessage := GetErrorMessageFromSteps(ctx, params.Gh, workflowJob)
		// Step errors prevail, but if not available we try to infer the state and error message from the check run annotations
		if errorMessage == nil {
			var inferredState *capi.SessionState
			inferredState, errorMessage = GetStateAndErrorMessageFromAnnotation(ctx, obsv, params.Gh, job.RepoOwner, job.RepoName, workflowJob)
			if inferredState != nil {
				session.State = *inferredState
				sessionUpdate = capi.SessionUpdate{State: session.State}
			}
		}

		// If we have an error message, set it on the session
		if errorMessage != nil {
			session.Error = &capi.SessionError{Message: *errorMessage}
		} else {
			session.Error = &capi.SessionError{Message: DefaultSessionErrorMessage}
		}
		sessionUpdate.Error = session.Error
	}

	if err := params.SessionClient.UpdateSession(ctx, job.SessionID, sessionUpdate); err != nil {
		return fmt.Errorf("failed to update session: %w", err)
	}
	UpdateRequestContextSession(ctx, obsv, sessionUpdate)

	if err := params.Gh.RequestReviewers(ctx, job.RepoOwner, job.RepoName, job.PullRequestNumber, []string{job.ActorLogin}); err != nil {
		return fmt.Errorf("failed to request reviewers: %w", err)
	}

	logger.Info("finalized job session")

	return nil
}

func MapJobStatusToSessionState(status jobs.JobStatus) capi.SessionState {
	switch status {
	case jobs.JobStatusQueued:
		return capi.SessionStateQueued
	case jobs.JobStatusRunning:
		return capi.SessionStateInProgress
	case jobs.JobStatusCompleted:
		return capi.SessionStateCompleted
	case jobs.JobStatusCancelled:
		return capi.SessionStateCancelled
	case jobs.JobStatusFailed:
		return capi.SessionStateFailed
	default:
		return ""
	}
}

// GetErrorMessageFromSteps returns the error message for a failed step in the workflow job
func GetErrorMessageFromSteps(ctx context.Context, gh github.ClientInterface, workflowJob *gg.WorkflowJob) *string {
	const failure string = "failure"

	var failedStep *gg.TaskStep

	if workflowJob == nil {
		return nil
	}

	if workflowJob.Conclusion == nil {
		return nil
	}

	jobConclusion := *workflowJob.Conclusion
	if jobConclusion != failure {
		return nil
	}

	for _, step := range workflowJob.Steps {
		if step.Conclusion == nil {
			continue
		}
		if *step.Conclusion == failure {
			failedStep = step
			break
		}
	}

	if failedStep == nil || failedStep.Name == nil {
		return nil
	}

	// If the step is a "known" step (part of our dynamic workflow), we don't
	// want to include a custom error message with the failed step name.
	if slices.Contains(launcher.ActionsWorkflowJobKnownSteps(), failedStep.GetName()) {
		return nil
	}

	stepError := fmt.Sprintf("The \"%s\" custom setup step from your .github/workflows/copilot-setup-steps.yml file failed.", *failedStep.Name)
	return &stepError
}

// GetStateAndErrorMessageFromAnnotation returns the state and error message inferred from the check run annotations
func GetStateAndErrorMessageFromAnnotation(ctx context.Context, obsv observability.Exporters, gh github.ClientInterface, owner, repo string, workflowJob *gg.WorkflowJob) (*capi.SessionState, *string) {
	if workflowJob == nil || workflowJob.Conclusion == nil {
		obsv.LoggerWithTelemetry(ctx).Info("failed to get workflow job or its conclusion")
		return nil, nil
	}

	annotations, err := gh.ListCheckRunAnnotations(ctx, owner, repo, *workflowJob.ID)
	if err != nil || annotations == nil {
		// If we can't get the annotations, we can't infer an error message from them.
		obsv.LoggerWithTelemetry(ctx).WithError(err).Error("failed to list checkrun annotations", kvp.String("workflowJobID", strconv.FormatInt(*workflowJob.ID, 10)))
		return nil, nil
	}

	switch *workflowJob.Conclusion {
	case "failure":
		// If a well-known error is found in annotations, it supersedes the step failure error message
		if annotationContainError(annotations, "failure", outOfDiskSpaceError) {
			errorMessage := "Copilot had to stop work due to insufficient disk space. If you see this regularly in this repository, you may need to switch to using GitHub Actions larger runners. For more information, see https://gh.io/copilot-coding-agent-larger-runners."
			return nil, &errorMessage
		}
	case "cancelled":
		if annotationContainError(annotations, "failure", runningTimedOutError) {
			errorMessage := "Copilot had to stop work due to a timeout."
			return &capi.SessionStateTimedOut, &errorMessage
		}

		if annotationContainError(annotations, "failure", userCancelledError) {
			return nil, nil
		}

		if annotationContainError(annotations, "failure", waitingTimedOutError) {
			// Users misconfigured a custom label that does not exist.
			// e.g. The job has exceeded the maximum execution time while awaiting a runner for 24h0m0s
			return nil, nil
		}

		// If we get here, chances are Actions is having issues, emit the metric and logs.
		obsv.Statter.Counter("dynamic_workflow_unknown_cancellation", obsv.TelemetryTags(ctx), 1)
		logCancellationAnnotations(obsv.LoggerWithTelemetry(ctx), annotations)
	}

	return nil, nil
}

// It is unfortunate we have to resort to these arbitrary annotation strings to identify specific issues.
// But at the time of writing, Actions API doesn't always reveal the correct info (e.g. conclusion being cancelled for timed-out workflows).
const outOfDiskSpaceError string = "System.IO.IOException: No space left on device"
const userCancelledError string = "The run was canceled by @"
const runningTimedOutError string = "The job has exceeded the maximum execution time of"
const waitingTimedOutError string = "The job has exceeded the maximum execution time while awaiting a runner for"

func annotationContainError(annotations []*gg.CheckRunAnnotation, level, errorMessage string) bool {
	for _, annotation := range annotations {
		if annotation.AnnotationLevel != nil && *annotation.AnnotationLevel == level {
			if annotation.Message != nil && strings.Contains(*annotation.Message, errorMessage) {
				return true
			}
		}
	}

	return false
}

func logCancellationAnnotations(logger log.Logger, annotations []*gg.CheckRunAnnotation) {
	for _, annotation := range annotations {
		if annotation.Message != nil && annotation.AnnotationLevel != nil {
			logger.Warn(fmt.Sprintf("unknown cancellation annotation: (%s) %s", *annotation.AnnotationLevel, *annotation.Message))
		}
	}
}

func getUnprocessedCommentsAndReviews(gh github.ClientInterface, actx *github.PullRequestContext, allJobs []*jobs.Job, pendingJob *jobs.Job) (comments []github.GQLIssueComment, reviews []github.GQLPullRequestReview) {
	// TODO(colbylwilliams): Confirm this is the behavior we want for this filter.
	filterOpts := github.FilterCommentOpts{
		IncludeMinimized:                   false,
		IncludeOtherBots:                   false,
		IncludeUsersWithoutPushPermissions: false, // https://github.com/github/sweagentd/issues/623
		IncludeCopilotReviews:              true,  // must be true to include inline review comment replies
	}

	// Get filtered reviews that have not yet been processed by another job
	filteredReviews := actx.FilteredReviews(gh, filterOpts)
	filteredReviews = slices.DeleteFunc(filteredReviews, func(r github.GQLPullRequestReview) bool {
		return slices.ContainsFunc(allJobs, func(j *jobs.Job) bool {
			return j.ID != pendingJob.ID && slices.Contains(j.ReviewIDs, r.GetID()) // ignore the pending job
		})
	})

	// Get filtered comments that have not yet been processed by another job
	filteredComments := actx.FilteredComments(gh, filterOpts)
	filteredComments = slices.DeleteFunc(filteredComments, func(c github.GQLIssueComment) bool {
		return slices.ContainsFunc(allJobs, func(j *jobs.Job) bool {
			return j.ID != pendingJob.ID && slices.Contains(j.CommentIDs, c.GetID()) // ignore the pending job
		})
	})

	return filteredComments, filteredReviews
}

// getLatestActionableComment returns the most recent comment/review that can trigger
// the agent to start a job. It's used to determine the last actor to trigger the job.
func getLatestActionableComment(gh github.ClientInterface, actx *github.PullRequestContext, comments []github.GQLIssueComment, reviews []github.GQLPullRequestReview) *github.GQLComment {
	// Add all the comments and reviews to a single slice, sorted most recent first
	all := []*github.GQLComment{}
	for _, c := range comments {
		all = append(all, &c.GQLComment)
	}
	for _, r := range reviews {
		all = append(all, &r.GQLComment)
	}

	slices.SortStableFunc(all, func(a, b *github.GQLComment) int {
		// reverse b and a to get the most recent first
		return cmp.Compare(b.PublishedAt.Unix(), a.PublishedAt.Unix())
	})

	// TODO(colbylwilliams): Do we nee need to consider if the associated review/
	// comment is "actionable" (i.e. the LLM returned that we should process it)
	// when determining which user to use as the actor for a job? If so, we need
	// to persist that with the review/comment ids on the job.
	var lastActionable *github.GQLComment

	for _, comment := range all {

		// TODO(colbylwilliams):
		// This logic is duplicated in several places, refactor to make it reusable.
		author := comment.GetAuthor()

		// Ignore comment/review authored by Copilot or other bots
		if author.IsCopilot(gh) || author.IsBot() {
			continue
		}

		// Only process events where the actor has write access to the repo
		if !actx.PermissionsFor(author).Push {
			continue
		}

		lastActionable = comment
		break
	}

	return lastActionable
}

// AddUnprocessedCommentsAndReviewsToJob updates the pending job ReviewIDs and CommentIDs to include
// the comments/reviews in the pull request that have not yet been processed by another job.
func AddUnprocessedCommentsAndReviewsToJob(gh github.ClientInterface, actx *github.PullRequestContext, allJobs []*jobs.Job, pendingJob *jobs.Job) {

	// Get all (qualifying) comments and reviews that have not yet been processed by another job
	comments, reviews := getUnprocessedCommentsAndReviews(gh, actx, allJobs, pendingJob)

	// Add the filtered, unprocessed comment ids to the pending job
	for _, c := range comments {
		if !slices.Contains(pendingJob.CommentIDs, c.GetID()) {
			pendingJob.CommentIDs = append(pendingJob.CommentIDs, c.GetID())
		}
	}

	// Add the filtered, unprocessed review ids to the pending job
	for _, r := range reviews {
		if !slices.Contains(pendingJob.ReviewIDs, r.GetID()) {
			pendingJob.ReviewIDs = append(pendingJob.ReviewIDs, r.GetID())
		}
	}
}

// ProcessPendingJob processes a pending job by updating the job with the comments/reviews that have
// not yet been processed by another job, setting the job actor to the most recent user that took
// a "triggering" action, and then launches then executes the job.
func ProcessPendingJob(ctx context.Context, gh github.ClientInterface, actx *github.PullRequestContext, allJobs []*jobs.Job, pendingJob *jobs.Job) (*jobs.Job, error) {

	// Get all (qualifying) comments and reviews that have not yet been processed by another job
	comments, reviews := getUnprocessedCommentsAndReviews(gh, actx, allJobs, pendingJob)

	// Add the filtered, unprocessed comment ids to the pending job
	for _, c := range comments {
		if !slices.Contains(pendingJob.CommentIDs, c.GetID()) {
			pendingJob.CommentIDs = append(pendingJob.CommentIDs, c.GetID())
		}
	}

	// Add the filtered, unprocessed review ids to the pending job
	for _, r := range reviews {
		if !slices.Contains(pendingJob.ReviewIDs, r.GetID()) {
			pendingJob.ReviewIDs = append(pendingJob.ReviewIDs, r.GetID())
		}
	}

	// Get the most recent comment/review that's considered actionable to determine the actor
	latestActionableComment := getLatestActionableComment(gh, actx, comments, reviews)
	if latestActionableComment == nil {
		return nil, fmt.Errorf("no actionable comments found to determine actor")
	}

	actor := latestActionableComment.Author

	// Set the job actor to the author of the most recent "triggering" comment/review
	pendingJob.ActorID = actor.GetID()
	pendingJob.ActorLogin = actor.GetLogin()

	return pendingJob, nil
}

const modelLabelPrefix = "copilot:agent="

// DefaultModel model is the model that is used by the agent when the `sweagentd_use_candidate_model_as_default` feature flag is not enabled
// and an explicit model label has not been applied.
const DefaultModel = "sweagent-capi:claude-sonnet-4"

// DefaultModelForBusinessOrPro is the model that is used instead of [DefaultModel] when the user is a Business or Pro user.
// For capacity or cost reasons, we may use a different model for these users.
const DefaultModelForBusinessOrPro = "sweagent-capi:claude-sonnet-4"

// CandidateModel is the model that is used by the agent when the `sweagentd_use_candidate_model_as_default` feature flag is enabled.
const CandidateModel = "sweagent-capi:claude-3.7-sonnet"

// ExperimentalModel is the model that is used by the agent when the `IsOsweExperimentEnabled` returns true.
const ExperimentalModel = "sweagent-capi:oswe"

// DetermineModel determines the model to use when the agent is launched based on the labels applied to an item (e.g. issue or PR) as
// well as the feature flags that are enabled for the actor, repo or repo owner.
func DetermineModel[E interface{ GetName() string }](ctx context.Context, obsv observability.Exporters, ffClient featureflags.Client, twirpClient githubtwirp.ClientInterface, job *jobs.Job, labels []E) string {
	logger := obsv.LoggerWithTelemetry(ctx)

	// If experiments contain 'padawan_exp_oswe' set to true in config parameters, use the candidate model
	if IsOsweExperimentEnabled(ctx, ffClient, job) {
		logger.Info("Using experimental model due to padawan_exp_oswe experiment", kvp.String("model", ExperimentalModel))
		return ExperimentalModel
	}

	// If explicitly setting a model via a label is allowed and a model label is present, use that model.
	if ffClient.IsEnabledForUserOrRepoOrOwner(ctx, featureflags.CopilotSWEAgentCanSelectModel, job.ActorID, job.RepoID, job.OwnerID) {
		if m := GetModelFromLabels(labels); m != "" {
			logger.Info("using explicit model provided by label", kvp.String("model", m))
			return m
		}
	}

	// If the actor, owner or repo has been feature flagged into the candidate model, use that model.
	if ffClient.IsEnabledForUserOrRepoOrOwner(ctx, "sweagentd_use_candidate_model_as_default", job.ActorID, job.RepoID, job.OwnerID) {
		logger.Info("using candidate model due to feature flag enabled for actor, repo or owner", kvp.String("model", CandidateModel))
		return CandidateModel
	}

	// Otherwise, we determine the model to use based on the user's Copilot plan.
	plan, err := githubtwirp.GetCopilotPlan(ctx, twirpClient, job.ActorID)
	if err != nil {
		logger.WithError(err).Error("failed to get Copilot plan for user, using default business or pro model", kvp.String("model", DefaultModelForBusinessOrPro))
		return DefaultModelForBusinessOrPro
	}

	if plan == githubtwirp.CopilotForBusiness || plan == githubtwirp.CopilotForIndividual {
		logger.Info("Using business or pro model for Copilot user due to plan", kvp.String("plan", string(plan)), kvp.String("model", DefaultModelForBusinessOrPro))
		return DefaultModelForBusinessOrPro
	}

	logger.Info("Using default model", kvp.String("plan", string(plan)), kvp.String("model", DefaultModel))
	return DefaultModel
}

// GetModelFromLabels returns the model name from the labels with the form "copilot:agent=<model_name>". It returns "" if
// no such label is found.
func GetModelFromLabels[E interface{ GetName() string }](labels []E) string {
	for _, label := range labels {
		if strings.HasPrefix(label.GetName(), modelLabelPrefix) {
			var after, _ = strings.CutPrefix(label.GetName(), modelLabelPrefix)
			return after
		}
	}

	return ""
}

// DetermineModelWithLabelNames is a convenience function for calling DetermineModel with an array of label names represented as strings.
func DetermineModelWithLabelNames(ctx context.Context, obsv observability.Exporters, ffClient featureflags.Client, twirpClient githubtwirp.ClientInterface, job *jobs.Job, labelNames []string) string {
	labels := make([]github.GQLLabel, 0, len(labelNames))
	for _, name := range labelNames {
		labels = append(labels, github.GQLLabel{
			Name: name,
		})
	}

	return DetermineModel(ctx, obsv, ffClient, twirpClient, job, labels)
}

// AddReactionsForReview adds the 👀 reaction to the PR review and all of its comments.
func AddReactionsForReview(ctx context.Context, obsv observability.Exporters, gh github.ClientInterface, review *github.GQLPullRequestReview) error {
	// We add 👀 to the review only if the reviewer left a top-level comment when
	// submitting the review. Top-level comments appear in the UI similar to a "normal"
	// comments, but are not returned by the comments or review comments API endpoints.
	// The UI supports reactions on top-level comments, but the REST API does not. So we
	// use the GraphQL mutation instead.
	if (review.Commented() || review.ChangesRequested()) && review.Body != "" {
		if err := review.AddReaction(ctx, gh, github.GQLReactionContentEyes); err != nil {
			obsv.LogAndReportError(ctx, err, "failed to add reaction")
		}
	}

	// TODO(colbylwilliams):
	// Need to confirm, but I believe every PR review comment *reply* creates a new, separate
	// PR review. If this is the case, we can work under the assumption that every PR review
	// comment is authored by the author of the parent PR review. As such, we can just use
	// the review author when checking whether or not to include this comment in the job.
	for _, comment := range review.Comments.Nodes {
		if err := comment.AddReaction(ctx, gh, github.GQLReactionContentEyes); err != nil {
			obsv.LogAndReportError(ctx, err, "failed to add reaction")
		}
	}

	return nil
}

// RemoveReactionsForJob removes the 👀 reaction from the PR comments, reviews and all their comments.
func RemoveReactionsForJob(ctx context.Context, obsv observability.Exporters, gh github.ClientInterface, actx *github.PullRequestContext, job *jobs.Job) {
	// currently we only will want to remove reactions for PR reviews and comments

	// Loop through the reviewIDs and remove the 👀 reaction from the review and all of its comments
	for _, reviewID := range job.ReviewIDs {
		for _, review := range actx.PullRequest.Reviews.Nodes {
			if review.GetID() == reviewID {
				if err := review.RemoveReaction(ctx, gh, github.GQLReactionContentEyes); err != nil {
					obsv.LogAndReportError(ctx, err, "failed to remove reaction")
				}

				for _, comment := range review.Comments.Nodes {
					if err := comment.RemoveReaction(ctx, gh, github.GQLReactionContentEyes); err != nil {
						obsv.LogAndReportError(ctx, err, "failed to remove reaction")
					}
				}
			}
		}
	}

	// Loop through the commentIDs and remove the 👀 reaction from the comments
	for _, commentID := range job.CommentIDs {
		for _, comment := range actx.PullRequest.Comments.Nodes {
			if comment.GetID() == commentID {
				if err := comment.RemoveReaction(ctx, gh, github.GQLReactionContentEyes); err != nil {
					obsv.LogAndReportError(ctx, err, "failed to remove reaction")
				}
			}
		}
	}
}

func createSessionClient(ctx context.Context, capiClient capi.Client, ghTwirp githubtwirp.ClientInterface, userID, repoID int64) (capi.SessionClient, error) {
	response, err := ghTwirp.SweagentdAPI().MintUserToServerTokenForRepo(ctx, &sweagentdTwirp.MintUserToServerTokenForRepoRequest{
		UserId:       uint64(userID),
		RepositoryId: uint64(repoID),
	})

	if err != nil {
		return nil, fmt.Errorf("failed to mint user to server token for repo: %w", err)
	}

	if response == nil || response.Token == "" {
		return nil, fmt.Errorf("failed to mint user to server token for repo. token is invalid or empty")
	}

	client := capiClient.NewSessionClient(userID, response.Token)

	return client, nil
}

func GetRandomTipForJob(
	ctx context.Context,
	ffClient featureflags.Client,
	actorID int64,
	repoID int64,
	ownerID int64,
	repoOwner string,
	repoName string) string {
	if !ffClient.IsEnabledForUserOrRepoOrOwner(ctx, "copilot_swe_agent_tips", actorID, repoID, ownerID) {
		return ""
	}
	tips := GetEnabledTips(ctx, actorID, repoID, ownerID, ffClient, repoOwner, repoName)
	if len(tips) == 0 {
		return ""
	}
	randomIndex := rand.Intn(len(tips))
	return tips[randomIndex]
}

type Tip struct {
	Flag    string
	Content string
}

func GetAllTips() []Tip {
	return []Tip{
		{Flag: "copilot_swe_agent_tips_feedback", Content: github.FeedbackTip},
		{Flag: "copilot_swe_agent_tips_best_practices", Content: github.BestPracticesTip},
		{Flag: "copilot_swe_agent_tips_onboarding", Content: github.OnboardingTip},
	}
}

// Remove the Copilot tips section from the pull request body, if present
func RemoveTips(pullRequestBody string) string {
	if strings.Contains(pullRequestBody, PullRequestBodyTipSeparator) {
		parts := strings.Split(pullRequestBody, PullRequestBodyTipSeparator)
		if len(parts) > 1 {
			return parts[0]
		}
	}
	return pullRequestBody
}

func GetEnabledTips(
	ctx context.Context,
	actorID int64,
	repoID int64,
	ownerID int64,
	ffClient featureflags.Client,
	repoOwner string,
	repoName string) []string {
	tips := make([]string, 0)
	for _, tip := range GetAllTips() {
		if ffClient.IsEnabledForUserOrRepoOrOwner(ctx, tip.Flag, actorID, repoID, ownerID) {

			// Skip tips requiring repo parameters if they are missing.
			content, err := SubstituteJobParameters(tip.Content, repoOwner, repoName)
			if err == nil {
				tips = append(tips, content)
			}
		}
	}
	return tips
}

// Takes a body string and replaces the {repo_owner} and {repo_name} placeholders with the actual values.
//
// Returns an error only if the content is invalid due to missing repoOwner or repoName. Strings that are
// unchanged do not return an error.
func SubstituteJobParameters(body string, repoOwner string, repoName string) (string, error) {
	newBody := strings.ReplaceAll(body, "{repo_owner}", repoOwner)
	newBody = strings.ReplaceAll(newBody, "{repo_name}", repoName)

	if body != newBody && (len(repoOwner) == 0 || len(repoName) == 0) {
		return "", fmt.Errorf("repoOwner and repoName cannot be empty")
	}

	return newBody, nil
}

// IsOsweExperimentEnabled checks if the OSWE experiment is enabled for the given job.
// It first verifies that the experimentation feature flag is enabled, then checks if
// the job has experiments with the 'padawan_exp_oswe' parameter set to true.
func IsOsweExperimentEnabled(ctx context.Context, ffClient featureflags.Client, job *jobs.Job) bool {
	// Check if experimentation feature flag is enabled
	if !ffClient.IsEnabledForUserOrRepoOrOwner(ctx, featureflags.CopilotSWEAgentExperimentation, job.ActorID, job.RepoID, job.OwnerID) {
		return false
	}

	// Check if job has experiments and configs
	if job.Experiments == nil || job.Experiments.Configs == nil {
		return false
	}

	// If the repo owner is not GitHub, we don't consider the OSWE experiment enabled.
	// This is a safeguard to ensure that only GitHub-owned repositories can trigger this experiment.
	if job.RepoOwner != "github" {
		return false
	}

	// Look for the padawan_exp_oswe parameter set to true
	for _, config := range job.Experiments.Configs {
		if config.Parameters != nil {
			if value, exists := config.Parameters["padawan_exp_oswe"]; exists {
				if boolValue, ok := value.(bool); ok && boolValue {
					return true
				}
			}
		}
	}

	return false
}

// UpdatePullRequestBodyWithOsweExperimentNote adds a note to the end of the pull request body about the OSWE experiment being
// enabled for work on this pull request, if it is enabled for the job.
func UpdatePullRequestBodyWithOsweExperimentNote(ctx context.Context, gh github.ClientInterface, ffClient featureflags.Client, job *jobs.Job) error {
	pr, err := gh.GetPullRequest(ctx, job.RepoID, job.PullRequestNumber)
	if err != nil {
		return fmt.Errorf("failed to get pull request: %w", err)
	}
	newBody := ""
	if pr.Body != nil {
		newBody = *pr.Body
	}

	// If we didn't add a tip to the PR body, we need to add the seperator since it isn't there yet. Otherwise, we can just append to the job.
	if GetRandomTipForJob(ctx, ffClient, job.ActorID, job.RepoID, job.OwnerID, job.RepoOwner, job.RepoName) == "" {
		newBody = fmt.Sprintf("%s\n\n%s\n---\n\n%s", newBody, PullRequestBodyTipSeparator, github.OsweExperimentNote)
	} else {
		newBody = fmt.Sprintf("%s\n\n%s", newBody, github.OsweExperimentNote)
	}
	pr.Body = &newBody
	if _, err := gh.UpdatePullRequest(ctx, job.RepoID, pr); err != nil {
		return fmt.Errorf("failed to update pull request body with OSWE experiment note: %w", err)
	}

	return nil
}
