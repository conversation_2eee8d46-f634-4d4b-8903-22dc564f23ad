package jobutils

import (
	"context"
	_ "embed"
	"strconv"

	"github.com/github/sweagentd/internal/capi"
	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/observability"
	"github.com/github/sweagentd/internal/requestctx"
)

func AddRequestCtxSessionFromJob(ctx context.Context, obsv observability.Exporters, job *jobs.Job) {
	workflowRunID, err := strconv.ParseUint(job.ComputeID, 10, 64)
	if err != nil {
		workflowRunID = 0
	}
	if err := requestctx.AddSession(ctx, &requestctx.CtxSession{
		ID:            job.SessionID,
		State:         string(MapJobStatusToSessionState(job.Status)),
		ResourceType:  string(capi.ResourceTypePullRequest),
		ResourceID:    job.PullRequestID,
		EventType:     string(job.EventType),
		WorkflowRunID: workflowRunID,
	}); err != nil {
		obsv.LoggerWithTelemetry(ctx).WithError(err).Error("failed to add session to request context with job")
	}
}

func AddRequestContextSession(ctx context.Context, obsv observability.Exporters, session *capi.Session) {
	if session == nil {
		if err := requestctx.AddSession(ctx, nil); err != nil {
			obsv.LoggerWithTelemetry(ctx).WithError(err).Error("failed to remove session from request context")
		}
		return
	}
	if err := requestctx.AddSession(ctx, &requestctx.CtxSession{
		ID:            session.ID,
		State:         string(session.State),
		ResourceType:  session.ResourceType,
		ResourceID:    session.ResourceID,
		EventType:     session.EventType,
		WorkflowRunID: session.WorkflowRunID,
	}); err != nil {
		obsv.LoggerWithTelemetry(ctx).WithError(err).Error("failed to add session to request context")
	}
}

func UpdateRequestContextSession(ctx context.Context, obsv observability.Exporters, update capi.SessionUpdate) {
	ctxSession, ok := requestctx.Session(ctx)
	if !ok {
		ctxSession = &requestctx.CtxSession{}
	}
	if update.ResourceID != 0 {
		ctxSession.ResourceID = update.ResourceID
	}
	if update.ResourceType != "" {
		ctxSession.ResourceType = update.ResourceType
	} else {
		ctxSession.ResourceType = string(capi.ResourceTypePullRequest)
	}
	if update.State != "" {
		ctxSession.State = string(update.State)
	}
	if update.WorkflowRunID != 0 {
		ctxSession.WorkflowRunID = update.WorkflowRunID
	}
	if err := requestctx.AddSession(ctx, ctxSession); err != nil {
		obsv.LoggerWithTelemetry(ctx).WithError(err).Error("failed to update session in request context")
	}
}

func UpdateRequestContextJob(ctx context.Context, obsv observability.Exporters, job *jobs.Job) {
	if job == nil {
		if err := requestctx.AddJob(ctx, nil); err != nil {
			obsv.LoggerWithTelemetry(ctx).WithError(err).Error("failed to remove job from request context")
		}
		return
	}

	ctxJob := &requestctx.CtxJob{
		ID:                job.ID,
		RepoID:            job.RepoID,
		OwnerID:           job.OwnerID,
		AssignmentID:      job.AssignmentID,
		ComputeID:         job.ComputeID,
		SessionID:         job.SessionID,
		EventType:         string(job.EventType),
		ActorID:           job.ActorID,
		PullRequestID:     job.PullRequestID,
		PullRequestNumber: job.PullRequestNumber,
		CreatedAt:         job.CreatedAt,
		CompletedAt:       job.CompletedAt,
		QueuedAt:          job.QueuedAt,
		Status:            string(job.Status),
		BranchName:        job.BranchName,
	}
	if job.Experiments != nil {
		ctxJob.Experiments = requestctx.CtxExperiments{
			Features:          job.Experiments.Features,
			Flights:           job.Experiments.Flights,
			FlightingVersion:  job.Experiments.FlightingVersion,
			ImpressionID:      job.Experiments.ImpressionID,
			AssignmentContext: job.Experiments.AssignmentContext,
		}
		for _, cfg := range job.Experiments.Configs {
			ctxJob.Experiments.Configs = append(ctxJob.Experiments.Configs, requestctx.CtxExperimentsConfig{
				ID:         cfg.ID,
				Parameters: cfg.Parameters,
			})
		}
	}

	if err := requestctx.AddJob(ctx, ctxJob); err != nil {
		obsv.LoggerWithTelemetry(ctx).WithError(err).Error("failed to add job to request context")
	}
}

func UpdateRequestContextJobUpdate(ctx context.Context, obsv observability.Exporters, job *jobs.JobUpdate) {
	if err := requestctx.AddJobUpdate(ctx, &requestctx.CtxJobUpdate{
		Action:    string(job.Action),
		Kind:      string(job.Kind),
		CreatedAt: job.CreatedAt.Unix(),
	}); err != nil {
		obsv.LoggerWithTelemetry(ctx).WithError(err).Error("failed to add job update to request context")
	}
}

func UpdateRequestContextJobQueue(ctx context.Context, obsv observability.Exporters, queueId string) {
	if err := requestctx.AddJobQueue(ctx, &requestctx.CtxJobQueue{
		QueueID: queueId,
	}); err != nil {
		obsv.LoggerWithTelemetry(ctx).WithError(err).Error("failed to add job queue to request context")
	}
}
