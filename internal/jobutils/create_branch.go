package jobutils

import (
	"context"
	"fmt"

	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/observability"
)

type CreateBranchOpts struct {
	Job            *jobs.Job
	RepoOwner      string
	RepoName       string
	BaseBranchName string
	NewBranchName  string
}

// CreateBranch creates a new branch in the repository for the given job, then updates
// the job with the new branch name in the database.
// If the feature flag `sweagentd_skip_empty_branch_creation` is enabled, it skips creating
// an empty branch and only updates the job with the branch name.
func CreateBranch(
	ctx context.Context,
	obsv observability.Exporters,
	ffClient featureflags.Client,
	jobsStore jobs.JobsStore,
	gh github.ClientInterface,
	opts CreateBranchOpts,
) error {
	job := opts.Job

	if !ffClient.IsEnabledForUser(ctx, featureflags.SweagentdSkipEmptyBranchCreation, job.ActorID) {
		_, err := gh.CreateNewEmptyBranch(ctx, opts.RepoOwner, opts.RepoName, opts.BaseBranchName, opts.NewBranchName)
		if err != nil {
			return err
		}
	}

	err := jobsStore.PatchJob(ctx, job, jobs.JobPatch{BranchName: &opts.NewBranchName})
	if err != nil {
		return fmt.Errorf("failed to patch job: %w", err)
	}

	UpdateRequestContextJob(ctx, obsv, job)
	return nil
}
