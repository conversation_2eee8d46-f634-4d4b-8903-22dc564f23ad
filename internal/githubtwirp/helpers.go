package githubtwirp

import (
	"context"
	"fmt"

	coreTwirp "github.com/github/copilot-api/proto/twirp/core/v1"
	copilotLimitersTwirp "github.com/github/copilot-twirp/proto/limiter/v1"
	copilotUsersTwirp "github.com/github/copilot-twirp/proto/users/v1"
	"github.com/github/sweagentd/internal/requestctx"
	sweagentdTwirp "github.com/github/sweagentd/proto/sweagentd/v1"
	"github.com/google/uuid"
)

const (
	PREMIUM_REQUESTS = copilotLimitersTwirp.QuotaType_QUOTA_TYPE_PREMIUM_INTERACTIONS
)

func IsCopilotSweEnabled(ctx context.Context, twirpClient ClientInterface, userID int64, repositoryID int64) (bool, error) {
	result, err := twirpClient.SweagentdAPI().ConfigurationForRepository(ctx, &sweagentdTwirp.ConfigurationForRepositoryRequest{
		RepositoryId: repositoryID,
		UserId:       uint64(userID),
	})
	if err != nil {
		return false, fmt.Errorf("failed to get monolith configuration for repository: %w", err)
	}

	return result.IsSweagentdEnabled, nil
}

func GetQuotaRemaining(ctx context.Context, twirpClient ClientInterface, userID int64, quotaType copilotLimitersTwirp.QuotaType) (remainingQuota int32, totalQuota int32, overagePermitted bool, err error) {
	user, err := twirpClient.UserDetailAPI().GetCopilotUser(ctx, &copilotUsersTwirp.GetCopilotUserRequest{
		Id: uint64(userID),
	})
	if err != nil {
		return 0, 0, false, fmt.Errorf("failed to get user details: %w", err)
	}

	quotaRemaining, err := twirpClient.LimiterAPI().GetQuotaRemaining(ctx, &copilotLimitersTwirp.GetQuotaRemainingRequest{
		CopilotTrackingId: user.UserDetails.GetAnalyticsTrackingId(),
	})
	if err != nil {
		return 0, 0, false, fmt.Errorf("failed to get quota remaining: %w", err)
	}

	if quotaRemaining.GetQuotaDetails() == nil {
		return 0, 0, false, fmt.Errorf("quota details are nil for user %d", userID)
	}

	for _, quota := range quotaRemaining.QuotaDetails {
		if quota.GetQuotaType() == quotaType {
			// Include unlimited flag as overage permitted to allow unlimited users access
			overage := quota.GetOveragePermitted() || quota.GetUnlimited()
			return quota.GetRemaining(), quota.GetEntitlement(), overage, nil
		}
	}

	return 0, 0, false, fmt.Errorf("quota type %v not found for user %d", quotaType, userID)
}

type HasAccessCheck struct {
	// An action that should be checked. These actions are defined in
	// https://github.com/github/github/blob/HEAD/config/access_control
	Action string
	// The ID resource to check access against. This is a dotcom model's numeric ID.
	ResourceId int64
	// The type of resource to check access against. This should map to a Rails model,
	// like Repository or Organization.
	ResourceType string
	// Optional: Specify an organization ID, for checks that require it.
	OrganizationID int64
	// Optional: Specify a repository ID, for checks that require it.
	RepositoryID int64
	// Optional: Specify a repository NWO (name with owner), for checks that require it.
	RepositoryNWO string
}

// This helper function takes in a user + token and a set of access checks to perform and returns true if they all pass
func TokenHasAccess(ctx context.Context, twirpClient ClientInterface, userInfo *requestctx.UserInfo, reqs []*HasAccessCheck) (bool, error) {
	// We want to guard against checking empty permissions so error in this case
	if len(reqs) == 0 {
		return false, fmt.Errorf("no access checks provided to verify")
	}

	reqObjs := make([]*coreTwirp.AuthorizationRequestObject, len(reqs))
	for i, req := range reqs {
		reqObjs[i] = &coreTwirp.AuthorizationRequestObject{
			Id:             fmt.Sprintf("%s-%s-%s", req.Action, req.ResourceType, uuid.NewString()),
			Action:         req.Action,
			ResourceId:     req.ResourceId,
			ResourceType:   req.ResourceType,
			OrganizationId: req.OrganizationID,
			RepositoryId:   req.RepositoryID,
			RepositoryNwo:  req.RepositoryNWO,
		}
	}
	batch := &coreTwirp.BatchControlAccessRequest{
		AccessToken:           userInfo.TokenInfo.Token,
		AuthorizationRequests: reqObjs,
		RequestIp:             userInfo.RequestIP,
	}

	api := twirpClient.AuthorizationAPI()
	result, err := api.BatchControlAccess(ctx, batch)
	if err != nil {
		return false, fmt.Errorf("failed to check access: %w", err)
	}
	// We want to know if all of the checks passed in where successful, so
	// iterate through each "decision" as to whether the check passed and
	// return false if any one of them failed, otherwise return true.
	for _, decision := range result.Decisions {
		if !decision.GetDecision() {
			return false, nil
		}
	}
	return true, nil
}
