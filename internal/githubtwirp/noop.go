package githubtwirp

import (
	"context"

	coreTwirp "github.com/github/copilot-api/proto/twirp/core/v1"
	copilotLimitersTwirp "github.com/github/copilot-twirp/proto/limiter/v1"
	copilotUsersTwirp "github.com/github/copilot-twirp/proto/users/v1"
	sweagentdTwirp "github.com/github/sweagentd/proto/sweagentd/v1"
)

type NoopClient struct {
	ClientInterface
	NoopSweagentdAPI     *NoopSweagentdAPI
	NoopUserDetailAPI    *NoopUserDetailAPI
	NoopLimiterAPI       *NoopLimiterAPI
	NoopAuthorizationAPI *NoopAuthorizationAPI
}

// Ensure NoopClient implements ClientInterface
var _ ClientInterface = &NoopClient{}

func NewNoopClient() *NoopClient {
	return &NoopClient{
		NoopSweagentdAPI:     &NoopSweagentdAPI{},
		NoopUserDetailAPI:    &NoopUserDetailAPI{},
		NoopLimiterAPI:       &NoopLimiterAPI{},
		NoopAuthorizationAPI: &NoopAuthorizationAPI{},
	}
}

func (n *NoopClient) SweagentdAPI() sweagentdTwirp.SweagentdAPI {
	return n.NoopSweagentdAPI
}

func (n *NoopClient) UserDetailAPI() copilotUsersTwirp.CopilotUserDetailAPI {
	return n.NoopUserDetailAPI
}

func (n *NoopClient) LimiterAPI() copilotLimitersTwirp.CopilotLimiterAPI {
	return n.NoopLimiterAPI
}

func (n *NoopClient) TestExecution(ctx context.Context) error {
	return nil
}

func (n *NoopClient) AuthorizationAPI() coreTwirp.AuthorizationAPI {
	return n.NoopAuthorizationAPI
}
