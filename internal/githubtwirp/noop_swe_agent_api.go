package githubtwirp

import (
	"context"

	sweagentdTwirp "github.com/github/sweagentd/proto/sweagentd/v1"
)

type NoopSweagentdAPI struct {
	sweagentdTwirp.SweagentdAPI
	ConfigurationForRepositoryResponse *sweagentdTwirp.ConfigurationForRepositoryResponse
	ConfigurationForRepositoryError    *error
	MockMCPConfigurationForRepository  func(ctx context.Context, req *sweagentdTwirp.MCPConfigurationForRepositoryRequest) (*sweagentdTwirp.MCPConfigurationForRepositoryResponse, error)
	MockCreatePullRequest              func(ctx context.Context, req *sweagentdTwirp.CreatePullRequestRequest) (*sweagentdTwirp.CreatePullRequestResponse, error)
}

// Ensure NoopClient implements ClientInterface
var _ sweagentdTwirp.SweagentdAPI = &NoopSweagentdAPI{}

func (n *NoopSweagentdAPI) ConfigurationForRepository(context.Context, *sweagentdTwirp.ConfigurationForRepositoryRequest) (*sweagentdTwirp.ConfigurationForRepositoryResponse, error) {
	if n.ConfigurationForRepositoryError != nil {
		return nil, *n.ConfigurationForRepositoryError
	} else if n.ConfigurationForRepositoryResponse != nil {
		return n.ConfigurationForRepositoryResponse, nil
	} else {
		return &sweagentdTwirp.ConfigurationForRepositoryResponse{
			IsSweagentdEnabled: false,
		}, nil
	}
}

func (n *NoopSweagentdAPI) MCPConfigurationForRepository(ctx context.Context, req *sweagentdTwirp.MCPConfigurationForRepositoryRequest) (*sweagentdTwirp.MCPConfigurationForRepositoryResponse, error) {
	if n.MockMCPConfigurationForRepository != nil {
		return n.MockMCPConfigurationForRepository(ctx, req)
	}

	panic("MCPConfigurationForRepository not implemented, provide a Mock method")
}

func (n *NoopSweagentdAPI) CreatePullRequest(ctx context.Context, req *sweagentdTwirp.CreatePullRequestRequest) (*sweagentdTwirp.CreatePullRequestResponse, error) {
	if n.MockCreatePullRequest != nil {
		return n.MockCreatePullRequest(ctx, req)
	}
	return &sweagentdTwirp.CreatePullRequestResponse{
		PullRequest: &sweagentdTwirp.PullRequest{
			Id:           123,
			GithubNumber: 789,
			RepositoryId: 456,
		},
	}, nil
}

func (n *NoopSweagentdAPI) MintUserToServerTokenForRepo(ctx context.Context, req *sweagentdTwirp.MintUserToServerTokenForRepoRequest) (*sweagentdTwirp.MintUserToServerTokenForRepoResponse, error) {
	return &sweagentdTwirp.MintUserToServerTokenForRepoResponse{
		Token: "mock-token",
	}, nil
}
