package launcher

import (
	"bytes"
	"context"
	_ "embed"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"html/template"
	"regexp"
	"slices"
	"strconv"
	"strings"

	"github.com/github/go-stats"
	"github.com/github/sweagentd/internal/config"
	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/githubtwirp"
	"github.com/github/sweagentd/internal/observability"
	"github.com/github/sweagentd/internal/requestctx"
	sweagentdAPITwirp "github.com/github/sweagentd/proto/sweagentd/v1"
	gg "github.com/google/go-github/v72/github"
)

// These are variables explicitly called out in the workflow yaml. They should not
// be passed into the actions_workflow.yaml template to avoid duplicates.
var workflowDirectlyReferencedVarNames = []string{
	"COPILOT_AGENT_RUNS_ON_LABELS",
	"COPILOT_AGENT_FIREWALL_ENABLED",
	"COPILOT_AGENT_FIREWALL_ALLOW_LIST_ADDITIONS",
	"COPILOT_AGENT_FIREWALL_ALLOW_LIST",
	"COPILOT_AGENT_DEBUG",
}

// Same as the previous list, but these are only ref'd in development mode
var developmentWorkflowDirectlyReferencedVarNames = []string{
	"COPILOT_INTEGRATION_ID_OVERRIDE",
	"CAPI_HMAC_KEY_OVERRIDE",
}

type ActionsLauncherOpts struct {
	AgentLauncherOpts
	ActionsDefaultRunsOnLabels string
}

type ActionsLauncher struct {
	obsv                observability.Exporters
	ffClient            featureflags.Client
	githubClientFactory github.ClientFactoryInterface
	ghTwirp             githubtwirp.ClientInterface
	opts                ActionsLauncherOpts
}

// Ensure that ActionsLauncher implements AgentLauncher
var _ AgentLauncher = &ActionsLauncher{}

func NewActionsLauncher(obsv observability.Exporters, ffClient featureflags.Client, githubClientFactory github.ClientFactoryInterface, ghTwirp githubtwirp.ClientInterface, opts ActionsLauncherOpts) (*ActionsLauncher, error) {
	return &ActionsLauncher{
		obsv:                obsv,
		ffClient:            ffClient,
		githubClientFactory: githubClientFactory,
		ghTwirp:             ghTwirp,
		opts:                opts,
	}, nil
}

func (a *ActionsLauncher) LauncherType() LauncherType {
	return LauncherTypeActions
}

func (a *ActionsLauncher) LaunchAgent(ctx context.Context, opts LaunchAgentOpts) (string, error) {
	ctx, span := a.obsv.Tracer.Start(ctx, "launcher.actions.LaunchAgent")
	defer span.End()

	environment, err := config.ParseEnvironment(a.opts.Environment)
	if err != nil {
		return "", fmt.Errorf("failed to parse environment: %w", err)
	}
	copilotAPIURL, actionDownloadURL, agentCallbackURL, err := a.getURLsForEnvironment(ctx, environment)
	if err != nil {
		return "failed to generate tenant specific capi urls", err
	}

	overrides := &variables{
		// Callback URL is passed into the dynamic workflow twice, this could probably be addressed to only use the workflowData.
		"COPILOT_AGENT_CALLBACK_URL": {Value: agentCallbackURL},
		"COPILOT_API_URL":            {Value: copilotAPIURL},
	}

	inputs, err := a.environmentVariables(ctx, opts, overrides)
	if err != nil {
		return "", err
	}

	if err := validateInputs(inputs); err != nil {
		return "", fmt.Errorf("invalid inputs: %w", err)
	}

	client, err := a.githubClientFactory.NewClient(opts.GitHubToken)
	if err != nil {
		return "", err
	}

	secrets, variables, err := client.GetCopilotEnvironmentSecretsAndVariables(ctx, opts.GitHubRepoOwner, opts.GitHubRepoName, opts.GitHubRepoID)
	if err != nil {
		return "", err
	}

	data := workflowData{
		RunName:                          GenerateRunName(opts),
		Secrets:                          secrets,
		Variables:                        variables,
		AgentDebug:                       a.opts.AgentDebug,
		AgentFirewallEnabledDefault:      isFirewallEnabledByDefault(ctx, a.ffClient, a.opts.AgentLauncherOpts),
		AgentFirewallAllowListDefault:    template.HTML(a.opts.AgentFirewallAllowListDefault),
		AgentFirewallRequiredAllowList:   template.HTML(a.opts.AgentFirewallRequiredAllowList),
		AgentCallbackURL:                 agentCallbackURL,
		ContinueOnSetupStepsErrorDefault: a.ffClient.IsEnabledForUserOrRepoOrOwner(ctx, "sweagentd_continue_on_setup_steps_error", opts.ActorID, opts.GitHubRepoID, opts.GitHubRepoOwnerID),
		JobID:                            opts.JobID,
		IsDevelopmentEnvironment:         a.opts.IsDevelopmentEnvironment,
		Environment:                      a.opts.Environment,
		MCPEnabled:                       a.opts.MCPEnabled,
		MCP3PEnabled:                     false,
		McpRemoteEnabled:                 a.ffClient.IsEnabledForUserOrRepoOrOwner(ctx, "sweagentd_mcp_remote_enabled", opts.ActorID, opts.GitHubRepoID, opts.GitHubRepoOwnerID),
		MCPVariables:                     []string{},
		MCPSecrets:                       []string{},
		Customization: &map[string]template.HTML{
			"runs-on":         template.HTML(fmt.Sprintf("${{ vars.COPILOT_AGENT_RUNS_ON_LABELS || '%s' }}", a.opts.ActionsDefaultRunsOnLabels)),
			"timeout-minutes": template.HTML(fmt.Sprintf("%d", a.opts.AgentTimeoutMin)),
		},
		BlackbirdMode:                 "disabled",
		InjectedSecretNames:           strings.Join(secrets, ","),
		AgentOnlineEvaluationDisabled: "false",
		FeatureFlags: strings.Join(
			getFeatureFlags(a.ffClient, ctx, opts.ActorID, opts.GitHubRepoID, opts.GitHubRepoOwnerID),
			","),
	}

	switch environment {
	case config.EnvironmentStaging:
		fallthrough
	case config.EnvironmentProxima:
		data.ActionDownloadURL = actionDownloadURL
	}

	if a.ffClient.IsEnabledForUserOrRepo(ctx, "sweagentd_firewall_use_ruleset_allow_list", opts.ActorID, opts.GitHubRepoID) {
		data.AgentFirewallRuleSetAllowListDefault = template.HTML(a.opts.AgentFirewallRuleSetAllowListDefault)
		// Clear this out as the default list is passed by the env above when feature flag is enabled.
		data.AgentFirewallAllowListDefault = ""
	}

	// data.MCP3PEnabled is currently controlled by a FF, but eventually this will be the MCP 3P Policy
	// i.e. data.MCP3PEnabled = true means this user has 3P MCP policy enabled, and false means they don't
	if a.ffClient.IsEnabledForUserOrRepoOrOwner(ctx, "sweagentd_mcp_3p_enabled", opts.ActorID, opts.GitHubRepoID, opts.GitHubRepoOwnerID) {
		// This value can be overridden by the policy check for MCP configuration.
		data.MCP3PEnabled = true
	}

	if a.ffClient.IsEnabledForUserOrRepoOrOwner(ctx, "sweagentd_mcp_disable", opts.ActorID, opts.GitHubRepoID, opts.GitHubRepoOwnerID) {
		// This is a FF used to meet a ProdSec requirement that we have a mechanism to disable MCP entirely for specific actors
		data.MCPEnabled = false
	}

	logger := a.obsv.LoggerWithTelemetry(ctx)

	data, err = a.ApplyMCPSettings(ctx, client, opts, data, inputs)
	if err != nil {
		logger.WithError(err).Error("Failed to apply MCP settings from dotcom")
		return "", err
	}

	// Determine the blackbird mode
	if a.ffClient.IsEnabledForUserOrRepoOrOwner(ctx, "sweagentd_blackbird_initial_search", opts.ActorID, opts.GitHubRepoID, opts.GitHubRepoOwnerID) {
		data.BlackbirdMode = "initial-search"
	}
	if a.ffClient.IsEnabledForUserOrRepoOrOwner(ctx, "sweagentd_blackbird_tool", opts.ActorID, opts.GitHubRepoID, opts.GitHubRepoOwnerID) {
		data.BlackbirdMode = "tool"
	}

	if isOnlineEvaluationDisabled(ctx, a.ffClient, a.opts.AgentLauncherOpts, opts.ActorID, opts.GitHubRepoID, opts.GitHubRepoOwnerID) {
		data.AgentOnlineEvaluationDisabled = "true"
	}

	// Try to find a user Actions workflow steps file at ".github/workflows/copilot-setup-steps.yml/yaml" for the
	// repo and branch and then process it. Only the permissions, runs-on, and steps from the copilot-setup-steps job are used.
	data, err = a.applySetupSteps(ctx, client, opts, data)
	if err != nil {
		logger.WithError(err).Error("Failed to process actions setup steps")
		return "", err
	}

	workflowContent, err := a.workflowContent(data)
	if err != nil {
		return "", err
	}

	// Compress inputs to avoid exceeding the Actions input size limit.

	workflow := &github.NewDynamicWorkflowRun{
		Ref:           gg.Ptr(opts.BranchName),
		Slug:          gg.Ptr("copilot"),
		WorkflowName:  gg.Ptr("Copilot"), // should match `name:` in workflow yaml
		Workflow:      gg.Ptr(workflowContent),
		Inputs:        gg.Ptr(inputs),
		PreventReruns: true,
	}

	run, err := client.CreateDynamicWorkflowRun(ctx, opts.GitHubRepoOwner, opts.GitHubRepoName, opts.GitHubRepoID, workflow)
	tags := a.obsv.TelemetryTags(ctx).Merge(stats.Tags{"success": strconv.FormatBool(err == nil)})
	a.obsv.Statter.Counter("dynamic_workflow_creation", tags, 1)

	if err != nil {
		logger.WithError(err).Error("Failed to create dynamic workflow run")
		return "", err
	}

	requestctx.AddWorkflowRun(ctx, &requestctx.CtxWorkflowRun{ID: *run.WorkflowRunID})
	a.obsv.LoggerWithTelemetry(ctx).Info("Created dynamic workflow run")
	return strconv.FormatInt(*run.WorkflowRunID, 10), nil
}

func (a *ActionsLauncher) TerminateAgent(ctx context.Context, opts TerminateAgentOpts) error {
	ctx, span := a.obsv.Tracer.Start(ctx, "launcher.actions.TerminateAgent")
	defer span.End()

	runID, err := strconv.ParseInt(opts.ComputeID, 10, 64)
	if err != nil {
		return fmt.Errorf("invalid compute ID: %s", opts.ComputeID)
	}

	if opts.Client == nil {
		client, err := a.githubClientFactory.NewClientFromRepo(ctx, opts.RepoID, opts.OwnerID)
		if err != nil {
			return fmt.Errorf("failed to create GitHub client: %w", err)
		}
		opts.Client = client
	}

	err = opts.Client.CancelWorkflowRunByID(ctx, opts.RepoOwner, opts.RepoName, runID)
	return err
}

// getURLsForEnvironment returns the action download URL and agent callback URL for the given environment
func (a *ActionsLauncher) getURLsForEnvironment(ctx context.Context, environment config.Environment) (copilotAPIURL, actionDownloadURL, agentCallbackURL string, err error) {
	copilotAPIURL = a.opts.CopilotAPIURL

	switch environment {
	case config.EnvironmentStaging:
		actionDownloadURL = a.opts.CopilotAPIURL + "/agents/swe-staging/runtime"
		agentCallbackURL = a.opts.AgentCallbackURL
	case config.EnvironmentProxima:
		copilotAPIURL, err = githubtwirp.ReplaceTenantPlaceholder(ctx, copilotAPIURL)
		if err != nil {
			return "", "", "", fmt.Errorf("failed to replace tenant placeholder in copilot API URL: %w", err)
		}

		actionDownloadURL = copilotAPIURL + "/agents/swe/runtime"
		agentCallbackURL, err = githubtwirp.ReplaceTenantPlaceholder(ctx, a.opts.AgentCallbackURL)
		if err != nil {
			return "", "", "", fmt.Errorf("failed to replace tenant placeholder in agent callback URL: %w", err)
		}
	default:
		agentCallbackURL = a.opts.AgentCallbackURL
	}

	return copilotAPIURL, actionDownloadURL, agentCallbackURL, nil
}

//go:embed actions_workflow.yaml
var workflowTemplate string

type workflowData struct {
	RunName                              string
	Secrets                              []string
	Variables                            []string
	AgentDebug                           bool
	AgentFirewallEnabledDefault          bool
	AgentFirewallRuleSetAllowListDefault template.HTML
	AgentFirewallAllowListDefault        template.HTML
	AgentFirewallRequiredAllowList       template.HTML
	AgentCallbackURL                     string
	ContinueOnSetupStepsErrorDefault     bool
	JobID                                string
	IsDevelopmentEnvironment             bool
	Environment                          string
	ActionDownloadURL                    string                    // Used to bootstrap the runtime from sweagentd itself
	Customization                        *map[string]template.HTML // Don't want to escape this since we need to use ""
	MCPEnabled                           bool
	MCP3PEnabled                         bool
	MCPVariables                         []string
	MCPSecrets                           []string
	McpRemoteEnabled                     bool
	PlaywrightUseFirewall                bool
	BlackbirdMode                        string
	InjectedSecretNames                  string
	AgentOnlineEvaluationDisabled        string
	FeatureFlags                         string
}

// Actions limits the number of inputs to 10.
const ActionsInputNumberLimit = 10

// Actions limits the inputs serialized to a JSON string to 2^16 - 1 bytes, which
// is the limit for MySQL BLOB and TEXT fields.
//
//   - https://github.com/github/github/blob/17b228a224bc48b3bae6e5602b498d8500b0f1ef/app/api/actions_dynamic_workflows.rb#L23-L25
//   - https://github.com/github/github/blob/17b228a224bc48b3bae6e5602b498d8500b0f1ef/lib/github/config/mysql.rb#L236-L239
const ActionsInputJSONLengthLimit = 65535

// MCP config can be at most 1/5 of the action input JSON limit
const McpConfigJSONLengthLimit = 13107

func validateInputs(inputs map[string]string) error {
	if len(inputs) > ActionsInputNumberLimit {
		return fmt.Errorf("number of inputs: %d exceeds maximum: %d", len(inputs), ActionsInputNumberLimit)
	}
	inputsJson, err := json.Marshal(inputs)
	if err != nil {
		return fmt.Errorf("failed to marshal inputs to JSON: %w", err)
	}
	if len(inputsJson) > ActionsInputJSONLengthLimit {
		return fmt.Errorf("length of inputs JSON: %d exceeds maximum: %d", len(inputsJson), ActionsInputJSONLengthLimit)
	}
	return nil
}

func (a *ActionsLauncher) ApplyMCPSettings(ctx context.Context, client github.ClientInterface, opts LaunchAgentOpts, data workflowData, inputs map[string]string) (workflowData, error) {
	response, err := a.ghTwirp.SweagentdAPI().MCPConfigurationForRepository(ctx, &sweagentdAPITwirp.MCPConfigurationForRepositoryRequest{
		RepositoryId: uint64(opts.GitHubRepoID),
		UserId:       uint64(opts.ActorID),
	})

	if err != nil {
		return data, fmt.Errorf("failed to get MCP configuration from dotcom: %w", err)
	}

	skipLoadingMCPConfig := false
	data.MCP3PEnabled = response.IsMcpEnabled
	if !data.MCP3PEnabled {
		skipLoadingMCPConfig = true
	}

	if !skipLoadingMCPConfig && data.MCP3PEnabled {
		// We base 64 encode the MCP configuration JSON to avoid issues with escaping quotes in the JSON string, which actions inputs seems to complicate.
		encoded := base64.StdEncoding.EncodeToString([]byte(response.McpConfigurationJson))

		// This shouldn't happen in production since we should be guarding this on the edit experience.
		if len(encoded) > McpConfigJSONLengthLimit {
			return data, fmt.Errorf("length of base64 encoded MCP configuration JSON: %d exceeds maximum: %d", len(encoded), McpConfigJSONLengthLimit)
		}

		inputs[MCPServerConfigVarName] = encoded
	}

	inputs[MCPInteractionIDVarName] = opts.SessionID

	return data, nil
}

func (a *ActionsLauncher) workflowContent(data workflowData) (string, error) {
	// Remove any secrets or variables that are explicitly called out in the workflow yaml.
	if data.Secrets != nil {
		data.Secrets = slices.DeleteFunc(data.Secrets, func(v string) bool {
			return slices.Contains(workflowDirectlyReferencedVarNames, v)
		})
		if data.IsDevelopmentEnvironment {
			data.Secrets = slices.DeleteFunc(data.Secrets, func(v string) bool {
				return slices.Contains(developmentWorkflowDirectlyReferencedVarNames, v)
			})
		}
	}
	if data.Variables != nil {
		data.Variables = slices.DeleteFunc(data.Variables, func(v string) bool {
			return slices.Contains(workflowDirectlyReferencedVarNames, v)
		})
		if data.IsDevelopmentEnvironment {
			data.Variables = slices.DeleteFunc(data.Variables, func(v string) bool {
				return slices.Contains(developmentWorkflowDirectlyReferencedVarNames, v)
			})
		}
	}
	// For secrets and variables with the same name, the secret takes precedence.
	if data.Secrets != nil && data.Variables != nil {
		data.Variables = slices.DeleteFunc(data.Variables, func(v string) bool {
			return slices.Contains(data.Secrets, v)
		})
	}

	if data.MCPEnabled {
		a.extractMCPVars(&data)
	}

	tmpl, err := template.New("workflow").Delims("<<", ">>").Parse(workflowTemplate)
	if err != nil {
		return "", err
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, data); err != nil {
		return "", err
	}
	return buf.String(), nil
}

func (a *ActionsLauncher) environmentVariables(ctx context.Context, opts LaunchAgentOpts, overrides *variables) (map[string]string, error) {
	gzip := a.ffClient.IsEnabledForUserOrRepoOrOwner(ctx, featureflags.CopilotSWEAgentGZipProblemStatement, opts.ActorID, opts.GitHubRepoID, opts.GitHubRepoOwnerID)
	all := GetVariables(a.opts, opts, overrides, gzip)

	inputs := map[string]string{}
	for k, v := range all {
		inputs[k] = v.Value
	}
	// Actions limits us to 10 inputs, so we serialize the inputs to a JSON string
	// and pass them as the value to a single COPILOT_AGENT_INPUTS input.
	inputsJson, err := json.Marshal(inputs)
	if err != nil {
		return nil, err
	}

	return map[string]string{
		"COPILOT_AGENT_INPUTS": string(inputsJson),
		// When we send a dynamic workflow to Actions, the Actions service calls back
		// into dotcom to get secrets (e.g. the CAPI u2s token). We use the actor ID to
		// to associate agent job with a user. This logic requires COPILOT_AGENT_ACTOR_ID
		// to be a top-level input, so we pass it here as well.
		"COPILOT_AGENT_ACTOR_ID": inputs["COPILOT_AGENT_ACTOR_ID"],
	}, nil
}

// We only perform this operation on items we read in from the `copilot` environment
func (a *ActionsLauncher) extractMCPVars(data *workflowData) {
	mcpVariables := []string{}
	nonMCPVariables := []string{}

	for _, variable := range data.Variables {
		if isMCPVar(variable) {
			mcpVariables = append(mcpVariables, variable)
		} else {
			nonMCPVariables = append(nonMCPVariables, variable)
		}
	}

	mcpSecrets := []string{}
	nonMCPSecrets := []string{}

	for _, secret := range data.Secrets {
		if isMCPVar(secret) {
			mcpSecrets = append(mcpSecrets, secret)
		} else {
			nonMCPSecrets = append(nonMCPSecrets, secret)
		}
	}

	data.Variables = nonMCPVariables
	data.Secrets = nonMCPSecrets
	data.MCPVariables = mcpVariables
	data.MCPSecrets = mcpSecrets
}

var mcpVarRegex = regexp.MustCompile(`^COPILOT_MCP_([a-zA-Z0-9_]*)$`)

const MCPServerConfigVarName = "GITHUB_COPILOT_MCP_JSON"
const MCPInteractionIDVarName = "GITHUB_COPILOT_INTERACTION_ID"

// We need a way to identify which variables are MCP-related.
func isMCPVar(name string) bool {
	return mcpVarRegex.MatchString(name)
}

func GenerateRunName(opts LaunchAgentOpts) string {
	if opts.RunName != "" {
		// Escape single quotes as Actions YAML quote the value as `run-name: '<value>'`
		return strings.ReplaceAll(opts.RunName, "'", "''")
	}
	if opts.Action == "fix-pr-comment" && opts.PRNumber != 0 {
		return fmt.Sprintf("Addressing comment on PR #%d", opts.PRNumber)
	}
	if opts.IssueNumber != 0 {
		return fmt.Sprintf("Fixing issue #%d", opts.IssueNumber)
	}
	return "Running Copilot"
}

// ActionsWorkflowJobKnownSteps returns the known steps in the dynamic workflow job.
// This is used to determine if a failed step is part of the core workflow or
// a custom step defined by the user (copilot-setup-steps.yml).
func ActionsWorkflowJobKnownSteps() []string {
	return []string{
		"OS Setup",
		"Prepare Copilot",
		"Start MCP Servers",
		"Processing Request",
		"Clean Up",
		"Save Data",
		"[Optional] Archive Details",
	}
}
