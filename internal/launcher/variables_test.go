package launcher

import (
	"context"
	"testing"

	"github.com/github/sweagentd/internal/featureflags"
	"github.com/stretchr/testify/assert"
)

func TestFirewallFeatureFlagging(t *testing.T) {
	ctx := context.Background()
	ffClientEnabled := featureflags.NewNoopClient(map[string]bool{copilotSWEAgentFirewallFFName: true})
	ffClientDisabled := featureflags.NewNoopClient(map[string]bool{copilotSWEAgentFirewallFFName: false})

	// FF enabled, config is disabled
	assert.True(t, isFirewallEnabledByDefault(ctx, ffClientEnabled, AgentLauncherOpts{AgentFirewallEnabledDefault: false}))
	// FF enabled, config is enabled
	assert.True(t, isFirewallEnabledByDefault(ctx, ffClientEnabled, AgentLauncherOpts{AgentFirewallEnabledDefault: true}))
	// FF disabled, config is disabled
	assert.False(t, isFirewallEnabledByDefault(ctx, ffClientDisabled, AgentLauncherOpts{AgentFirewallEnabledDefault: false}))
	// FF enabled, config is enabled
	assert.True(t, isFirewallEnabledByDefault(ctx, ffClientDisabled, AgentLauncherOpts{AgentFirewallEnabledDefault: true}))

}
