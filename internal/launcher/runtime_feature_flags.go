package launcher

import (
	"context"
	"strings"

	"github.com/github/sweagentd/internal/featureflags"
)

// The set of feature flags that will be pre-queried and passed to the agent
// runtime process.
//
// Only flags listed here are passed to the runtime.
//
// To add a new flag:
// - Define it in accordance with the feature flag docs: https://thehub.github.com/epd/engineering/products-and-services/dotcom/features/feature-flags/
// - Add it to runtime_feature_flags.txt
// - Check whether it exists in the following object.
//
// NOTE: feature flag names may be visible to the user in logs or other output.
var runtimeFeatureFlags []string = []string{
	"copilot_swe_agent_firewall_enabled_by_default",
	"copilot_swe_agent_online_evaluation_disabled",
	// TODO: Remove this once we are confident about error handling in prod
	"copilot_swe_agent_simulate_runtime_git_clone_error",
	// TODO: Remove this once we are confident about error handling in prod
	"copilot_swe_agent_simulate_runtime_git_push_error",
	"copilot_swe_agent_resolve_repo_images",
	"copilot_swe_agent_vision",
	"copilot_swe_agent_mcp_filtering",
	"copilot_swe_agent_initiator_agent",
	"copilot_swe_agent_parallel_tool_execution",
	"copilot_swe_agent_memory_in_repo_store",
	"copilot_swe_agent_str_replace_editor_get_lsp_diagnostics",
	"copilot_swe_agent_str_replace_editor_use_lsp_diagnostics",
	"copilot_swe_agent_playwright_use_firewall",
	"copilot_swe_agent_sync_pr_title_description",
}

func getFeatureFlags(
	ffClient featureflags.Client,
	ctx context.Context,
	actorID int64,
	repoId int64,
	repoOwnerId int64) []string {
	var result []string

	for _, flag := range runtimeFeatureFlags {
		// Only retain the feature flags that are enabled for the user or repo or owner.
		if ffClient.IsEnabledForUserOrRepoOrOwner(ctx, flag, actorID, repoId, repoOwnerId) {
			result = append(result, strings.TrimSpace(flag))
		}
	}

	return result
}
