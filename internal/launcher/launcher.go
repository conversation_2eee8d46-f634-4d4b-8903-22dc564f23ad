package launcher

import (
	"context"
	"fmt"

	"github.com/github/sweagentd/internal/github"
	"github.com/google/uuid"
)

const copilotSWEAgentFirewallFFName = "copilot_swe_agent_firewall_enabled_by_default"
const copilotSWEAgentOnlineEvalDisabledFFName = "copilot_swe_agent_online_evaluation_disabled"

type LauncherType string

const (
	LauncherTypeActions LauncherType = "actions"
	LauncherTypeDocker  LauncherType = "docker"
)

// AgentLauncher is an interface for launching an agent.
type AgentLauncher interface {
	LaunchAgent(ctx context.Context, opts LaunchAgentOpts) (string, error)
	TerminateAgent(ctx context.Context, opts TerminateAgentOpts) error
	LauncherType() LauncherType
}

type LaunchAgentOpts struct {
	// The GitHub repository in the form of `owner/repository`
	GitHubRepo string `json:"github_repo"`
	// The GitHub repository global ID
	GitHubRepoID   int64  `json:"github_repo_id"`
	GitHubRepoName string `json:"github_repo_name"`
	// The GitHub repository owner login
	GitHubRepoOwner   string `json:"github_repo_owner"`
	GitHubRepoOwnerID int64  `json:"github_repo_owner_id"`
	GitHubBaseCommit  string `json:"github_base_commit"`
	// The issue number that the agent is responding to
	IssueNumber int `json:"issue_number"`
	// Properties related to the task being performed
	ProblemStatement   string `json:"problem_statement"`
	ContentFilterMode  string `json:"content_filter_mode"`
	RunName            string `json:"run_name"`
	ResponseBodySuffix string `json:"response_body_suffix"`

	JobID      string `json:"job_id"`
	Action     string `json:"action"`
	Push       bool   `json:"push"`
	Model      string `json:"model"`
	PRNumber   int    `json:"pr_number"`
	BranchName string `json:"branch_name"`
	// ActorID is the ID of the GitHub user that triggered the copilot agent job.
	// This is the actor that will be attributed to API calls made by the agent
	// as well as git commits made by the agent. Thus, the actor must have the
	// necessary permissions to perform the required actions.
	ActorID int64 `json:"actor_id"`
	// ActorLogin is the login of the GitHub user that triggered the copilot agent
	// job. This is the actor that will be attributed to API calls made by the
	// agent as well as git commits made by the agent. Thus, the actor must have
	// the necessary permissions to perform the required actions.
	ActorLogin string `json:"actor_login"`
	// Encrypted github token or "" after the job is done
	GitHubToken string `json:"-"`
	// Number of commits to pull for PR comment fix scenarios
	CommitCount int `json:"commit_count"`
	// UseSessions reports whether the user has this feature enabled
	UseSessions bool   `json:"use_sessions"`
	SessionID   string `json:"session_id"`
	// Nonce is a random value used to validate callback requests come from the right workflow run
	Nonce string `json:"-"` // IGNORE: This shouldn't be persisted in the job cosmos document
}

type TerminateAgentOpts struct {
	OwnerID   int64
	RepoID    int64
	RepoOwner string
	RepoName  string
	ComputeID string
	Client    github.ClientInterface
}

func generateComputeID() string {
	return fmt.Sprintf("agent-%s", uuid.NewString())
}
