package problemstatement

import (
	"cmp"
	"context"
	"fmt"
	"slices"
	"strconv"
	"strings"
	"testing"

	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/jobutils"
	"github.com/github/sweagentd/internal/observability"
	"github.com/github/sweagentd/internal/requestctx"
	"github.com/stretchr/testify/require"
)

func TestPullRequestCommentOrderingAndFiltering(t *testing.T) {
	ctx := context.Background()
	obsv := observability.NewNoopExporters()
	gh := &github.NoopClient{GithubAppLogin: GitHubAppLogin, GithubBotLogin: GitHubBotLogin}
	assignment := getAssignment()

	actx := getPullRequestContext()

	actx.PullRequest.Reviews.Nodes = []github.GQLPullRequestReview{ // out of order
		*testPullRequestReview(*actorWriter(), false, 100, "review writer one", []github.GQLPullRequestReviewComment{ // out of order
			*prReviewCommentLine(*actorWriter(), false, 101, 100, "review 1 comment one", "", 0, 0),
			*prReviewCommentLine(*actorWriter(), true, 102, 100, "review 1 comment minimized", "", 0, 0), // minimized
			*prReviewCommentLine(*actorBot(), false, 104, 100, "review 1 comment bot", "", 0, 0),         // bot
			*prReviewCommentLine(*actorReader(), false, 105, 100, "review 1 comment reader", "", 0, 0),   // reader
			*prReviewCommentLine(*actorCopilot(), false, 103, 100, "review 1 comment copilot", "", 0, 0), // copilot
			*prReviewCommentLine(*actorWriter(), false, 106, 100, "review 1 comment six", "", 0, 0),
		}),
		*testPullRequestReview(*actorWriter(), false, 600, "review writer six", []github.GQLPullRequestReviewComment{ // out of order
			*prReviewCommentFile(*actorWriter(), false, 601, 600, "review 6 comment one", ""),
			*prReviewCommentFile(*actorWriter(), true, 602, 600, "review 6 comment minimized", ""), // minimized
			*prReviewCommentFile(*actorBot(), false, 604, 600, "review 6 comment bot", ""),         // bot
			*prReviewCommentFile(*actorReader(), false, 605, 600, "review 6 comment reader", ""),   // reader
			*prReviewCommentFile(*actorCopilot(), false, 603, 600, "review 6 comment copilot", ""), // copilot
			*prReviewCommentFile(*actorWriter(), false, 606, 600, "review 6 comment six", ""),
		}),
		*testPullRequestReview(*actorWriter(), true, 200, "review minimized", []github.GQLPullRequestReviewComment{ // minimized
			*prReviewCommentLine(*actorWriter(), false, 201, 200, "review 2 comment one", "", 0, 0),
		}),
		*testPullRequestReview(*actorBot(), false, 400, "review bot", []github.GQLPullRequestReviewComment{ // bot
			*prReviewCommentLine(*actorWriter(), false, 401, 400, "review 4 comment one", "", 0, 0),
		}),
		*testPullRequestReview(*actorReader(), false, 500, "review reader", []github.GQLPullRequestReviewComment{ // reader
			*prReviewCommentLine(*actorWriter(), false, 501, 500, "review 5 comment one", "", 0, 0),
		}),
		*testPullRequestReview(*actorCopilot(), false, 300, "review copilot", []github.GQLPullRequestReviewComment{ // copilot
			*prReviewCommentLine(*actorWriter(), false, 301, 300, "review 3 comment one", "", 0, 0),
		}),
		*testPullRequestReview(*actorWriter(), false, 700, "review writer seven", []github.GQLPullRequestReviewComment{ // copilot
			*prReviewCommentLine(*actorWriter(), false, 701, 700, "review 7 comment one", "", 0, 0),
		}),
	}

	oldJob := getJob()
	oldJob.ReviewIDs = getReviewIDs(100, 200, 300, 400, 500) // mark some as processed (old)
	review := actx.PullRequest.Reviews.Nodes[1]
	job := assignment.NewJob(jobs.AgentActionFixPRComment, &requestctx.UserInfo{
		ID:         uint64(review.Author.GetID()),
		Login:      review.Author.GetLogin(),
		TrackingID: "tracking-id",
	}, actx.PullRequest.HeadRef.Name)
	jobutils.AddUnprocessedCommentsAndReviewsToJob(gh, actx, []*jobs.Job{oldJob}, job)

	problemStatement, err := NewPullRequestBuilder(obsv, gh, actx, job).Build(ctx)
	require.NoError(t, err)

	statement := problemStatement.Content
	commitCount := problemStatement.CommitCount

	require.NotEmpty(t, statement)

	// reviews
	require.Contains(t, statement, "review writer one")
	require.Contains(t, statement, "review writer six")
	require.Contains(t, statement, "review writer seven")
	require.Contains(t, statement, "review copilot")

	require.NotContains(t, statement, "review minimized")
	require.NotContains(t, statement, "review bot")
	require.NotContains(t, statement, "review reader")

	// comments
	require.Contains(t, statement, "comment copilot")
	require.Contains(t, statement, "comment one")
	require.Contains(t, statement, "comment six")

	require.NotContains(t, statement, "comment minimized")
	require.NotContains(t, statement, "comment bot")
	require.NotContains(t, statement, "comment reader")

	type index struct {
		Index     int
		Timestamp int64
	}
	indexes := []index{}

	// Find the indexes of the comments in the statement
	for _, review := range actx.PullRequest.Reviews.Nodes {
		i := strings.Index(statement, review.Body)
		if i >= 0 {
			indexes = append(indexes, index{
				Index:     i,
				Timestamp: review.SubmittedAt.Unix(),
			})
			for _, comment := range review.Comments.Nodes {
				i = strings.Index(statement, comment.Body)
				if i >= 0 {
					indexes = append(indexes, index{
						Index:     i,
						Timestamp: comment.PublishedAt.Unix(),
					})
				}
			}
		}
	}

	// Sort the indexes so we can check the order of the comments
	slices.SortStableFunc(indexes, func(i, j index) int {
		return cmp.Compare(i.Index, j.Index)
	})

	// Check the indexes are sorted by published at date
	isSorted := slices.IsSortedFunc(indexes, func(i, j index) int {
		return cmp.Compare(i.Timestamp, j.Timestamp)
	})

	require.True(t, isSorted, "pull request reviews and comments are not sorted by published at date")

	require.Equal(t, commitCount, actx.PullRequest.Commits.TotalCount)
	require.Contains(t, statement, "The last **5** git commits in this branch")
	// require.Contains(t, statement, "You have already submitted a PR to fix the problem above")
}

func TestAutoFilterOldPRCommentContents(t *testing.T) {
	ctx := context.Background()
	obsv := observability.NewNoopExporters()
	gh := &github.NoopClient{GithubAppLogin: GitHubAppLogin, GithubBotLogin: GitHubBotLogin}
	assignment := getAssignment()

	actx := getPullRequestContext()

	actx.PullRequest.Comments.Nodes = []github.GQLIssueComment{
		*testIssueComment(*actorWriter(), false, 101, "comment writer one"),
		*testIssueComment(*actorWriter(), false, 102, "comment writer two"),
		*testIssueComment(*actorWriter(), false, 103, "comment writer three"),
		*testIssueComment(*actorWriter(), false, 104, "comment writer four"),
		*testIssueComment(*actorWriter(), false, 105, "comment writer five"),
	}

	actx.PullRequest.Comments.Nodes[2].Body = actx.PullRequest.Comments.Nodes[2].Body + strings.Repeat("-", tooBigCharacterCount)

	oldJob := getJob()
	oldJob.CommentIDs = getCommentIDs(100, 200, 300) // mark some as processed (old)
	comment := actx.PullRequest.Comments.Nodes[3]
	job := assignment.NewJob(jobs.AgentActionFixPRComment, &requestctx.UserInfo{
		ID:         uint64(comment.Author.GetID()),
		Login:      comment.Author.GetLogin(),
		TrackingID: "tracking-id",
	}, actx.PullRequest.HeadRef.Name)
	jobutils.AddUnprocessedCommentsAndReviewsToJob(gh, actx, []*jobs.Job{oldJob}, job)

	problemStatement, err := NewPullRequestBuilder(obsv, gh, actx, job).Build(ctx)
	require.NoError(t, err)

	statement := problemStatement.Content
	commitCount := problemStatement.CommitCount

	require.NotEmpty(t, statement)
	// Too long comment should be truncated
	require.Contains(t, statement, "----...")
	// But all comments should be present
	require.Contains(t, statement, "writer one")
	require.Contains(t, statement, "writer two")
	require.Contains(t, statement, "writer three")
	require.Contains(t, statement, "writer four")
	require.Contains(t, statement, "writer five")

	require.Equal(t, commitCount, actx.PullRequest.Commits.TotalCount)
	require.Contains(t, statement, "The last **5** git commits in this branch")
}

func TestPRCommentCountAutoReduction(t *testing.T) {
	ctx := context.Background()
	obsv := observability.NewNoopExporters()
	gh := &github.NoopClient{GithubAppLogin: GitHubAppLogin, GithubBotLogin: GitHubBotLogin}
	assignment := getAssignment()

	actx := getPullRequestContext()

	actx.PullRequest.Comments.Nodes = []github.GQLIssueComment{
		*testIssueComment(*actorWriter(), false, 101, "comment writer one"),
		*testIssueComment(*actorWriter(), false, 102, "comment writer two"),
		*testIssueComment(*actorWriter(), false, 103, "comment writer three"),
		*testIssueComment(*actorWriter(), false, 104, "comment writer four"),
		*testIssueComment(*actorWriter(), false, 105, "comment writer five"),
	}

	actx.PullRequest.Comments.Nodes[2].Body = actx.PullRequest.Comments.Nodes[2].Body + strings.Repeat("-", tooBigCharacterCount)
	actx.PullRequest.Comments.Nodes[3].Body = actx.PullRequest.Comments.Nodes[3].Body + strings.Repeat("-", tooBigCharacterCount)

	oldJob := getJob()
	oldJob.CommentIDs = getCommentIDs(100, 200, 300) // mark some as processed (old)
	comment := actx.PullRequest.Comments.Nodes[3]
	job := assignment.NewJob(jobs.AgentActionFixPRComment, &requestctx.UserInfo{
		ID:         uint64(comment.Author.GetID()),
		Login:      comment.Author.GetLogin(),
		TrackingID: "tracking-id",
	}, actx.PullRequest.HeadRef.Name)
	jobutils.AddUnprocessedCommentsAndReviewsToJob(gh, actx, []*jobs.Job{oldJob}, job)

	problemStatement, err := NewPullRequestBuilder(obsv, gh, actx, job).Build(ctx)
	require.NoError(t, err)

	statement := problemStatement.Content
	commitCount := problemStatement.CommitCount

	require.NotEmpty(t, statement)
	// Too long comment should be truncated
	require.Contains(t, statement, "----...")
	// But all comments should be present
	require.Contains(t, statement, "writer one")
	require.Contains(t, statement, "writer two")
	require.Contains(t, statement, "writer three")
	require.Contains(t, statement, "writer four")
	require.Contains(t, statement, "writer five")

	require.Equal(t, commitCount, actx.PullRequest.Commits.TotalCount)
	require.Contains(t, statement, "The last **5** git commits in this branch")
}

func TestReviewFileAndLineNumbers(t *testing.T) {
	ctx := context.Background()
	obsv := observability.NewNoopExporters()
	gh := &github.NoopClient{GithubAppLogin: GitHubAppLogin, GithubBotLogin: GitHubBotLogin}
	assignment := getAssignment()

	actx := getPullRequestContext()

	actx.PullRequest.Reviews.Nodes = []github.GQLPullRequestReview{ // out of order
		*testPullRequestReview(*actorWriter(), false, 100, "review writer one", []github.GQLPullRequestReviewComment{ // out of order
			*prReviewCommentLine(*actorWriter(), false, 101, 100, "review 1 comment line multi", "my/line/path", 8, 20),
			*prReviewCommentLine(*actorWriter(), false, 102, 100, "review 1 comment line single", "my/one/path", 8, 8),
			*prReviewCommentFile(*actorWriter(), false, 103, 100, "review 1 comment file", "my/file/path"),
		}),
	}

	review := actx.PullRequest.Reviews.Nodes[0]
	job := assignment.NewJob(jobs.AgentActionFixPRComment, &requestctx.UserInfo{
		ID:         uint64(review.Author.GetID()),
		Login:      review.Author.GetLogin(),
		TrackingID: "tracking-id",
	}, actx.PullRequest.HeadRef.Name)
	jobutils.AddUnprocessedCommentsAndReviewsToJob(gh, actx, []*jobs.Job{}, job)

	problemStatement, err := NewPullRequestBuilder(obsv, gh, actx, job).Build(ctx)
	require.NoError(t, err)

	statement := problemStatement.Content

	require.NotEmpty(t, statement)

	require.Contains(t, statement, "review writer one")
	require.Contains(t, statement, "review 1 comment line multi")
	require.Contains(t, statement, "my/line/path:8-20")
	require.Contains(t, statement, "review 1 comment line single")
	require.Contains(t, statement, "my/one/path:8")
	require.Contains(t, statement, "review 1 comment file")
	require.Contains(t, statement, "my/file/path")
}

func TestReviewCommentsArePresentForNew(t *testing.T) {
	ctx := context.Background()
	obsv := observability.NewNoopExporters()
	gh := &github.NoopClient{GithubAppLogin: GitHubAppLogin, GithubBotLogin: GitHubBotLogin}
	assignment := getAssignment()

	actx := getPullRequestContext()

	actx.PullRequest.Reviews.Nodes = []github.GQLPullRequestReview{ // out of order
		*testPullRequestReview(*actorWriter(), false, 100, "review writer one", []github.GQLPullRequestReviewComment{ // out of order
			*prReviewCommentLine(*actorWriter(), false, 101, 100, "review 1 comment line multi", "my/line/path", 8, 20),
			*prReviewCommentLine(*actorWriter(), false, 102, 100, "review 1 comment line single", "my/one/path", 8, 8),
			*prReviewCommentFile(*actorWriter(), false, 103, 100, "review 1 comment file", "my/file/path"),
		}),
		*testPullRequestReview(*actorWriter(), false, 200, "review writer two", []github.GQLPullRequestReviewComment{ // out of order
			*prReviewCommentLine(*actorWriter(), false, 201, 200, "review 2 comment line multi", "my/line/path", 8, 20),
			*prReviewCommentLine(*actorWriter(), false, 202, 200, "review 2 comment line single", "my/one/path", 8, 8),
			*prReviewCommentFile(*actorWriter(), false, 203, 200, "review 2 comment file", "my/file/path"),
		}),
	}

	for _, review := range actx.PullRequest.Reviews.Nodes {
		fmt.Println(review.PublishedAt)
		for _, comment := range review.Comments.Nodes {
			fmt.Println(comment.PublishedAt)
		}
	}

	oldJob := getJob()
	oldJob.ReviewIDs = getReviewIDs(100) // mark some as processed (old)
	review := actx.PullRequest.Reviews.Nodes[1]
	job := assignment.NewJob(jobs.AgentActionFixPRComment, &requestctx.UserInfo{
		ID:         uint64(review.Author.GetID()),
		Login:      review.Author.GetLogin(),
		TrackingID: "tracking-id",
	}, actx.PullRequest.HeadRef.Name)
	jobutils.AddUnprocessedCommentsAndReviewsToJob(gh, actx, []*jobs.Job{oldJob}, job)

	problemStatement, err := NewPullRequestBuilder(obsv, gh, actx, job).Build(ctx)
	require.NoError(t, err)

	statement := problemStatement.Content

	require.NotEmpty(t, statement)

	rid, _ := idsFromStrings(NameForIDsPullRequestReview, strconv.Itoa(200))
	require.Contains(t, statement, fmt.Sprintf("<comment_id>%d</comment_id>", rid))

	// First review comments in a thread should include the comment ID
	for _, id := range getReviewCommentIDs(201, 202, 203) {
		require.Contains(t, statement, fmt.Sprintf("<comment_id>%d</comment_id>", id))
	}
}

func TestTipSectionIsRemoved(t *testing.T) {
	ctx := context.Background()
	obsv := observability.NewNoopExporters()
	gh := &github.NoopClient{GithubAppLogin: GitHubAppLogin, GithubBotLogin: GitHubBotLogin}
	assignment := getAssignment()

	actx := getPullRequestContext()

	actx.PullRequest.Reviews.Nodes = []github.GQLPullRequestReview{ // out of order
		*testPullRequestReview(*actorWriter(), false, 100, "review writer one", []github.GQLPullRequestReviewComment{ // out of order
			*prReviewCommentLine(*actorWriter(), false, 101, 100, "review 1 comment line multi", "my/line/path", 8, 20),
			*prReviewCommentLine(*actorWriter(), false, 102, 100, "review 1 comment line single", "my/one/path", 8, 8),
			*prReviewCommentFile(*actorWriter(), false, 103, 100, "review 1 comment file", "my/file/path"),
		}),
	}

	// Add a tip
	actx.PullRequest.Body = actx.PullRequest.Body + "\n\n" + jobutils.PullRequestBodyTipSeparator + "\n---\n\n" + github.FeedbackTip

	review := actx.PullRequest.Reviews.Nodes[0]
	job := assignment.NewJob(jobs.AgentActionFixPRComment, &requestctx.UserInfo{
		ID:         uint64(review.Author.GetID()),
		Login:      review.Author.GetLogin(),
		TrackingID: "tracking-id",
	}, actx.PullRequest.HeadRef.Name)
	jobutils.AddUnprocessedCommentsAndReviewsToJob(gh, actx, []*jobs.Job{}, job)

	require.Contains(t, actx.PullRequest.Body, github.FeedbackTip)

	problemStatement, err := NewPullRequestBuilder(obsv, gh, actx, job).Build(ctx)
	require.NoError(t, err)

	statement := problemStatement.Content

	require.NotEmpty(t, statement)
	require.NotContains(t, statement, github.FeedbackTip)
	require.NotContains(t, statement, jobutils.PullRequestBodyTipSeparator)
}

func TestBestPracticesTipIsRemoved(t *testing.T) {
	ctx := context.Background()
	obsv := observability.NewNoopExporters()
	gh := &github.NoopClient{GithubAppLogin: GitHubAppLogin, GithubBotLogin: GitHubBotLogin}
	assignment := getAssignment()

	actx := getPullRequestContext()

	actx.PullRequest.Reviews.Nodes = []github.GQLPullRequestReview{ // out of order
		*testPullRequestReview(*actorWriter(), false, 100, "review writer one", []github.GQLPullRequestReviewComment{ // out of order
			*prReviewCommentLine(*actorWriter(), false, 101, 100, "review 1 comment line multi", "my/line/path", 8, 20),
			*prReviewCommentLine(*actorWriter(), false, 102, 100, "review 1 comment line single", "my/one/path", 8, 8),
			*prReviewCommentFile(*actorWriter(), false, 103, 100, "review 1 comment file", "my/file/path"),
		}),
	}

	// Add a tips advert
	actx.PullRequest.Body = actx.PullRequest.Body + "\n\n" + jobutils.PullRequestBodyTipSeparator + "\n---\n\n" + github.BestPracticesTip

	review := actx.PullRequest.Reviews.Nodes[0]
	job := assignment.NewJob(jobs.AgentActionFixPRComment, &requestctx.UserInfo{
		ID:         uint64(review.Author.GetID()),
		Login:      review.Author.GetLogin(),
		TrackingID: "tracking-id",
	}, actx.PullRequest.HeadRef.Name)
	jobutils.AddUnprocessedCommentsAndReviewsToJob(gh, actx, []*jobs.Job{}, job)

	require.Contains(t, actx.PullRequest.Body, github.BestPracticesTip)

	problemStatement, err := NewPullRequestBuilder(obsv, gh, actx, job).Build(ctx)
	require.NoError(t, err)

	statement := problemStatement.Content

	require.NotEmpty(t, statement)
	require.NotContains(t, statement, github.BestPracticesTip)
}

func TestCopilotCommentAreTreatedAsOld(t *testing.T) {
	ctx := context.Background()
	obsv := observability.NewNoopExporters()
	gh := &github.NoopClient{GithubAppLogin: GitHubAppLogin, GithubBotLogin: GitHubBotLogin}
	assignment := getAssignment()

	actx := getPullRequestContext()

	actx.PullRequest.Reviews.Nodes = []github.GQLPullRequestReview{ // out of order
		*testPullRequestReview(*actorWriter(), false, 100, "review writer one", []github.GQLPullRequestReviewComment{ // out of order
			*prReviewCommentLine(*actorWriter(), false, 101, 100, "review 1 comment one", "", 0, 0),
			*prReviewCommentLine(*actorWriter(), true, 102, 100, "review 1 comment minimized", "", 0, 0), // minimized
			*prReviewCommentLine(*actorBot(), false, 104, 100, "review 1 comment bot", "", 0, 0),         // bot
			*prReviewCommentLine(*actorReader(), false, 105, 100, "review 1 comment reader", "", 0, 0),   // reader
			*prReviewCommentLine(*actorCopilot(), false, 103, 100, "review 1 comment copilot", "", 0, 0), // copilot
			*prReviewCommentLine(*actorWriter(), false, 106, 100, "review 1 comment six", "", 0, 0),
		}),
		*testPullRequestReview(*actorWriter(), false, 600, "review writer six", []github.GQLPullRequestReviewComment{ // out of order
			*prReviewCommentFile(*actorWriter(), false, 601, 600, "review 6 comment one", ""),
			*prReviewCommentFile(*actorWriter(), true, 602, 600, "review 6 comment minimized", ""), // minimized
			*prReviewCommentFile(*actorBot(), false, 604, 600, "review 6 comment bot", ""),         // bot
			*prReviewCommentFile(*actorReader(), false, 605, 600, "review 6 comment reader", ""),   // reader
			*prReviewCommentFile(*actorCopilot(), false, 603, 600, "review 6 comment copilot", ""), // copilot
			*prReviewCommentFile(*actorWriter(), false, 606, 600, "review 6 comment six", ""),
		}),
	}

	actx.PullRequest.Comments.Nodes = []github.GQLIssueComment{
		*testIssueComment(*actorWriter(), false, 200, "comment writer one"),
		*testIssueComment(*actorCopilot(), false, 201, "comment copilot two"),
		*testIssueComment(*actorWriter(), false, 202, "comment writer three"),
		*testIssueComment(*actorCopilot(), false, 203, "comment copilot four"),
	}

	oldJob := getJob()
	oldJob.ReviewIDs = []int64{}  // all reviews are new
	oldJob.CommentIDs = []int64{} // all comments are new

	review := actx.PullRequest.Reviews.Nodes[0]
	job := assignment.NewJob(jobs.AgentActionFixPRComment, &requestctx.UserInfo{
		ID:         uint64(review.Author.GetID()),
		Login:      review.Author.GetLogin(),
		TrackingID: "tracking-id",
	}, actx.PullRequest.HeadRef.Name)
	jobutils.AddUnprocessedCommentsAndReviewsToJob(gh, actx, []*jobs.Job{oldJob}, job)

	problemStatement, err := NewPullRequestBuilder(obsv, gh, actx, job).Build(ctx)
	require.NoError(t, err)

	statement := problemStatement.Content

	require.NotEmpty(t, statement)

	// First review comments in a thread should include the comment ID
	for _, id := range getReviewIDs(100, 600) {
		require.Contains(t, statement, fmt.Sprintf("<comment_id>%d</comment_id>", id))
	}

	for _, id := range getReviewCommentIDs(101, 106, 601, 606) {
		require.Contains(t, statement, fmt.Sprintf("<comment_id>%d</comment_id>", id))
	}

	for _, id := range getReviewCommentIDs(102, 103, 104, 105, 602, 603, 604, 605) {
		require.NotContains(t, statement, fmt.Sprintf("<comment_id>%d</comment_id>", id))
	}

	// All copilot comments should be marked as old
	require.Contains(t, statement, "<comment_old>\n<author>@copilot</author>")
	require.NotContains(t, statement, "<comment_new>\n<author>@copilot</author>")
}
