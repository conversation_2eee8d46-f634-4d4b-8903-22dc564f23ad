package experiments

import (
	"context"

	"github.com/github/sweagentd/internal/jobs"
)

// Not implemented for proxima, but we use this passthrough client to avoid any additional logic.

type ProximaClient struct {
}

func NewProximaClient() *ProximaClient {
	return &ProximaClient{}
}

func (e *ProximaClient) SetExperimentsOnNewAssignment(ctx context.Context, jobStore jobs.JobsStore, assignment *jobs.Assignment, job *jobs.Job, labelNames []string) error {
	return nil
}
