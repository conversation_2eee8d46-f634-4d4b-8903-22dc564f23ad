package experiments

import (
	"context"
	"fmt"

	"github.com/github/sweagentd/internal/jobs"
)

type NoopClient struct {
}

func NewNoopClient() *NoopClient {
	return &NoopClient{}
}

func (e *NoopClient) SetExperimentsOnNewAssignment(ctx context.Context, jobStore jobs.JobsStore, assignment *jobs.Assignment, job *jobs.Job, labelNames []string) error {
	patchExp := &jobs.AssignmentExperiments{
		AssignmentContext: "noop",
	}

	if err := jobStore.PatchAssignment(ctx, assignment, jobs.AssignmentPatch{Experiments: patchExp}); err != nil {
		return fmt.Errorf("failed to patch assignment with experiments: %w", err)
	}

	if err := jobStore.PatchJob(ctx, job, jobs.JobPatch{Experiments: patchExp}); err != nil {
		return fmt.Errorf("failed to patch job with experiments: %w", err)
	}

	return nil
}
