package experiments

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/jobutils"
	"github.com/github/sweagentd/internal/observability"
)

const (
	VSCodeExtensionNameHeader = "X-VSCode-ExtensionName"
	GitHubPullRequestIDHeader = "X-GitHub-PullRequestId"
	CopilotRepositoryHeader   = "X-Copilot-Repository"
	CopilotOrgsHeader         = "X-Copilot-Orgs"
)

type ExpTransport struct {
	RoundTripper  http.RoundTripper
	extensionName string
}

func (et *ExpTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	req.Header.Add(VSCodeExtensionNameHeader, et.extensionName)
	return et.RoundTripper.RoundTrip(req)
}

type Client interface {
	SetExperimentsOnNewAssignment(ctx context.Context, jobStore jobs.JobsStore, assignment *jobs.Assignment, job *jobs.Job, labelNames []string) error
}

type ExperimentsClient struct {
	obsv          observability.Exporters
	ff            featureflags.Client
	url           url.URL
	extensionName string
	http          *http.Client
}

var _ Client = &ExperimentsClient{}

func NewClient(obsv observability.Exporters, ff featureflags.Client, endpoint, extensionName string) *ExperimentsClient {
	tr := &ExpTransport{
		RoundTripper:  observability.NewTelemetryRoundTripper(http.DefaultTransport, obsv, "experiments"),
		extensionName: extensionName,
	}

	u, err := url.Parse(endpoint)
	if err != nil {
		log.Fatalf("failed to parse baseURL: %s\n", err)
	}

	httpClient := &http.Client{
		Transport: tr,
	}

	return &ExperimentsClient{
		obsv:          obsv,
		ff:            ff,
		url:           *u,
		extensionName: extensionName,
		http:          httpClient,
	}
}

type CopilotExpAssignmentResponse struct {
	Features          []string          `json:"Features"`
	Flights           map[string]string `json:"Flights"`
	Configs           []ConfigEntry     `json:"Configs"`
	ParameterGroups   any               `json:"ParameterGroups"`
	FlightingVersion  int               `json:"FlightingVersion"`
	ImpressionID      string            `json:"ImpressionId"`
	AssignmentContext string            `json:"AssignmentContext"`
}

type ConfigEntry struct {
	ID         string         `json:"Id"`
	Parameters map[string]any `json:"Parameters"`
}

func (e *ExperimentsClient) getExperiments(ctx context.Context, job *jobs.Job) (*CopilotExpAssignmentResponse, error) {
	ctx, span := e.obsv.Tracer.Start(ctx, "experiments.getExperiments")
	defer span.End()

	logger := e.obsv.LoggerWithTelemetry(ctx)
	logger.Info("getting assignment experiments")

	if job.PullRequestID == 0 {
		return nil, fmt.Errorf("job does not have a pull request ID")
	}

	if job.RepoOwner != "github" {
		return nil, fmt.Errorf("job repo owner is not github")
	}

	start := time.Now()
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, e.url.String(), http.NoBody)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set(GitHubPullRequestIDHeader, strconv.FormatInt(job.PullRequestID, 10))
	req.Header.Set(CopilotRepositoryHeader, job.RepoName)
	req.Header.Set(CopilotOrgsHeader, job.RepoOwner)

	resp, err := e.http.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	e.obsv.LogWithDuration(ctx, logger, start, "experiments.getExperiments.httpRequest")

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	var response CopilotExpAssignmentResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	logger.Info("retrieved assignment experiments")

	return &response, nil
}

func (e *ExperimentsClient) SetExperimentsOnNewAssignment(ctx context.Context, jobStore jobs.JobsStore, assignment *jobs.Assignment, job *jobs.Job, labelNames []string) error {
	e.obsv.LoggerWithTelemetry(ctx).Info("setting experiments on new assignment")

	if !e.ff.IsEnabledForUserOrRepoOrOwner(ctx, featureflags.CopilotSWEAgentExperimentation, job.ActorID, job.RepoID, job.OwnerID) {
		e.obsv.LoggerWithTelemetry(ctx).Info("experimentation feature flag is not enabled, skipping assignment of experiments")
		return nil
	}

	// If chosing a custom model via labels is allowed and this has been done, skip assignment of experiments. If we computed the
	// experiments, it is possible the experimentation platform would tell us to use a custom model, overriding the label. We MUST
	// ensure that we do not compute experiments in this cass. If we did, the experimentation platform would think we are using the
	// model that they assigned, it would confuse up the reporting and metrics.
	if e.ff.IsEnabledForUserOrRepoOrOwner(ctx, featureflags.CopilotSWEAgentCanSelectModel, job.ActorID, job.RepoID, job.OwnerID) {
		labels := make([]github.GQLLabel, 0, len(labelNames))
		for _, name := range labelNames {
			labels = append(labels, github.GQLLabel{
				Name: name,
			})
		}

		if jobutils.GetModelFromLabels(labels) != "" {
			e.obsv.LoggerWithTelemetry(ctx).Info("job has custom model label, skipping assignment of experiments")
			return nil
		}
	}

	exp, err := e.getExperiments(ctx, job)
	if err != nil {
		return fmt.Errorf("failed to call copilot agent experimentation API: %w", err)
	}

	if exp == nil || exp.AssignmentContext == "" {
		e.obsv.LoggerWithTelemetry(ctx).Info("no experiments returned for assignment, skipping")
		return nil
	}

	patchExp := exp.toAssignmentExperiments()

	if err := jobStore.PatchAssignment(ctx, assignment, jobs.AssignmentPatch{Experiments: patchExp}); err != nil {
		return fmt.Errorf("failed to patch assignment with experiments: %w", err)
	}

	jobutils.UpdateRequestContextJob(ctx, e.obsv, job)

	return nil
}

func (e *CopilotExpAssignmentResponse) toAssignmentExperiments() *jobs.AssignmentExperiments {
	exp := &jobs.AssignmentExperiments{
		AssignmentContext: e.AssignmentContext,
		Features:          e.Features,
		Flights:           e.Flights,
		FlightingVersion:  e.FlightingVersion,
		ImpressionID:      e.ImpressionID,
	}
	for _, config := range e.Configs {
		exp.Configs = append(exp.Configs, config.toAssignmentExperimentsConfig())
	}
	return exp
}

func (e *ConfigEntry) toAssignmentExperimentsConfig() jobs.AssignmentExperimentsConfig {
	return jobs.AssignmentExperimentsConfig{
		ID:         e.ID,
		Parameters: e.Parameters,
	}
}
