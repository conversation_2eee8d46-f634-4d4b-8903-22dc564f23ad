{"kind": "telemetry", "telemetry": {"event": "get_completion_with_tools_turn", "properties": {"model": "claude-3.7-sonnet", "agent": "sweagent-capi", "toolCalls": "[{\"toolCallId\":\"tooluse_exx2e9SmTHqMz6sKvmfcwQ\",\"toolName\":\"bash\"}]", "toolCallExecutions": "[{\"properties\":{\"toolCallId\":\"tooluse_exx2e9SmTHqMz6sKvmfcwQ\",\"toolName\":\"bash\",\"resultType\":\"success\"},\"restrictedProperties\":{},\"metrics\":{\"durationMs\":100}}]"}, "restrictedProperties": {}, "metrics": {"preTruncationTokensInMessages": 3943, "preTruncationMessagesLength": 8, "postTruncationTokensInMessages": 3943, "postTruncationMessagesLength": 8, "tokensRemovedDuringTruncation": 0, "messagesRemovedDuringTruncation": 0, "modelCallDurationMs": 3270, "turn": 3, "retriesUsed": 0, "turnDurationMs": 4054, "numToolCalls": 1, "numToolExecutions": 1}}}