package aqueduct

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/github/aqueduct-client-go/v3/pkg/aqueduct"
	"github.com/github/github-telemetry-go/kvp"
	"github.com/github/sweagentd/internal/aqueduct/jobs"
	db "github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/observability"
	"go.opentelemetry.io/otel/codes"
)

type aqueductClient struct {
	appName       string
	aqueductURL   string
	apiKey        string
	apiKeyVersion int
	client        aqueduct.Client
	obsv          observability.Exporters
	statsConfig   *aqueduct.StatsConfig
}

type AqueductClientOption func(*aqueductClient)

// WithAqueductURL sets the Aqueduct URL for the client.
func WithAqueductURL(url string) AqueductClientOption {
	return func(a *aqueductClient) {
		a.aqueductURL = url
	}
}

// WithAPIKey sets the API key for the client.
func WithAPIKey(apiKey string) AqueductClientOption {
	return func(a *aqueductClient) {
		a.apiKey = apiKey
	}
}

// WithAPIKeyVersion sets the API key version for the client.
func WithAPIKeyVersion(version int) AqueductClientOption {
	return func(a *aqueductClient) {
		a.apiKeyVersion = version
	}
}

// AqueductClient defines the interface for interacting with the Aqueduct service.
type AqueductClient interface {
	AppName() string
	AqueductURL() string

	Client() aqueduct.Client

	getAPIKey() string     // for testing
	getAPIKeyVersion() int // for testing

	// QueueJob queues a job (domain object stored in Cosmos) to Aqueduct.
	QueueJob(ctx context.Context, job *db.Job) (string, error)
	// QueueJobRaw queues a job (raw aqueduct payload) to Aqueduct, useful for re-queueing jobs.
	QueueJobRaw(ctx context.Context, job *jobs.AgentJob, opts ...aqueduct.SendOption) (string, error)

	SendTestJob(ctx context.Context) error
}

var _ AqueductClient = (*aqueductClient)(nil)

func (ac *aqueductClient) AppName() string { return ac.appName }

func (ac *aqueductClient) AqueductURL() string { return ac.aqueductURL }

func (ac *aqueductClient) Client() aqueduct.Client { return ac.client }

func (ac *aqueductClient) getAPIKey() string { return ac.apiKey }

func (ac *aqueductClient) getAPIKeyVersion() int { return ac.apiKeyVersion }

// NewAqueductClient creates a new Aqueduct client with the provided configuration.
func NewAqueductClient(ctx context.Context, obsv observability.Exporters, appName string, options ...AqueductClientOption) (AqueductClient, error) {
	ctx, span := obsv.Tracer.Start(ctx, "aqueduct.NewAqueductClient")
	defer span.End() // Ensure the span is ended

	methodLogger := obsv.Logger.WithContext(ctx).WithFields(
		kvp.String("code.function", "NewAqueductClient"),
		kvp.String("code.namespace", "aqueduct"),
	)
	defer obsv.LogWithDuration(ctx, methodLogger, time.Now(), "NewAqueductClient")

	ac := &aqueductClient{
		appName:       appName,
		aqueductURL:   "",
		apiKey:        "",
		apiKeyVersion: 0,
		obsv:          obsv,
	}
	for _, option := range options {
		option(ac)
	}

	methodLogger.Info("Initializing Aqueduct client")

	if err := ac.configureClient(); err != nil {
		span.RecordError(err)
		span.SetStatus(codes.Error, err.Error())
		methodLogger.WithError(err).Error("error creating Aqueduct client")
		return nil, err
	}

	methodLogger.Info("Aqueduct client created successfully")

	return ac, nil
}

func (ac *aqueductClient) configureClient() error {
	if ac.client != nil {
		return fmt.Errorf("aqueduct client already exists")
	}

	// we need to create a new client
	// first, we need to check if the aqueduct URL is set
	if ac.aqueductURL == "" {
		return fmt.Errorf("aqueduct URL is not set")
	}
	statsConfig, err := aqueduct.NewStatsConfig(
		aqueduct.WithStatsClient(ac.obsv.Statter),
	)
	if err != nil {
		return fmt.Errorf("could not create stats config for aqueduct client")
	}
	ac.statsConfig = statsConfig

	opts := []aqueduct.ClientOption{
		aqueduct.WithClientStats(statsConfig),
		aqueduct.WithHTTPClient(&http.Client{
			Transport: observability.NewTelemetryRoundTripper(http.DefaultTransport, ac.obsv, "aqueduct"),
		}),
	}
	// if we have an API key and version, we'll use that.
	if ac.apiKey != "" {
		opts = append(
			opts,
			aqueduct.WithAPIKey(ac.apiKey),
			aqueduct.WithAPIKeyVersion(ac.apiKeyVersion),
		)
	}
	client, err := aqueduct.NewClient(
		ac.aqueductURL,
		opts...,
	)
	if err != nil {
		return fmt.Errorf("failed to create Aqueduct client: %w", err)
	}
	ac.client = client
	return nil
}
