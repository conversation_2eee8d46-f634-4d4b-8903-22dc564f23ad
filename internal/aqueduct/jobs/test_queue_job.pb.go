// Update generated code with script/protoc

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v5.27.2
// source: test_queue_job.proto

package jobs

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TestQueueJob struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message     string `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	ShouldPanic bool   `protobuf:"varint,2,opt,name=shouldPanic,proto3" json:"shouldPanic,omitempty"`
}

func (x *TestQueueJob) Reset() {
	*x = TestQueueJob{}
	if protoimpl.UnsafeEnabled {
		mi := &file_test_queue_job_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestQueueJob) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestQueueJob) ProtoMessage() {}

func (x *TestQueueJob) ProtoReflect() protoreflect.Message {
	mi := &file_test_queue_job_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestQueueJob.ProtoReflect.Descriptor instead.
func (*TestQueueJob) Descriptor() ([]byte, []int) {
	return file_test_queue_job_proto_rawDescGZIP(), []int{0}
}

func (x *TestQueueJob) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *TestQueueJob) GetShouldPanic() bool {
	if x != nil {
		return x.ShouldPanic
	}
	return false
}

var File_test_queue_job_proto protoreflect.FileDescriptor

var file_test_queue_job_proto_rawDesc = []byte{
	0x0a, 0x14, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x5f, 0x6a, 0x6f, 0x62,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x73,
	0x77, 0x65, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x64, 0x2e, 0x61, 0x71, 0x75, 0x65, 0x64, 0x75, 0x63,
	0x74, 0x2e, 0x6a, 0x6f, 0x62, 0x73, 0x22, 0x4a, 0x0a, 0x0c, 0x54, 0x65, 0x73, 0x74, 0x51, 0x75,
	0x65, 0x75, 0x65, 0x4a, 0x6f, 0x62, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x50, 0x61, 0x6e, 0x69, 0x63, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x50, 0x61, 0x6e,
	0x69, 0x63, 0x42, 0x09, 0x5a, 0x07, 0x2e, 0x2f, 0x3b, 0x6a, 0x6f, 0x62, 0x73, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_test_queue_job_proto_rawDescOnce sync.Once
	file_test_queue_job_proto_rawDescData = file_test_queue_job_proto_rawDesc
)

func file_test_queue_job_proto_rawDescGZIP() []byte {
	file_test_queue_job_proto_rawDescOnce.Do(func() {
		file_test_queue_job_proto_rawDescData = protoimpl.X.CompressGZIP(file_test_queue_job_proto_rawDescData)
	})
	return file_test_queue_job_proto_rawDescData
}

var file_test_queue_job_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_test_queue_job_proto_goTypes = []interface{}{
	(*TestQueueJob)(nil), // 0: github.sweagentd.aqueduct.jobs.TestQueueJob
}
var file_test_queue_job_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_test_queue_job_proto_init() }
func file_test_queue_job_proto_init() {
	if File_test_queue_job_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_test_queue_job_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestQueueJob); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_test_queue_job_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_test_queue_job_proto_goTypes,
		DependencyIndexes: file_test_queue_job_proto_depIdxs,
		MessageInfos:      file_test_queue_job_proto_msgTypes,
	}.Build()
	File_test_queue_job_proto = out.File
	file_test_queue_job_proto_rawDesc = nil
	file_test_queue_job_proto_goTypes = nil
	file_test_queue_job_proto_depIdxs = nil
}
