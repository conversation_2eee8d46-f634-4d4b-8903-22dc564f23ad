// Update generated code with script/protoc

syntax = "proto3";

package github.sweagentd.aqueduct.jobs;

option go_package = "./;jobs";

message Agent<PERSON><PERSON> {
  // The sweagentd assignment ID.
  string assignment_id = 1;
  // The sweagentd job ID that this (aqueduct) job is associated with.
  string job_id = 2;

  // The owner ID of the repository.
  int64 owner_id = 3;

  // The repository ID.
  int64 repository_id = 4;

  // The actor ID is the GitHub ID of the user that initiated the job.
  int64 actor_id = 5;

  // The pull request ID is the GitHub ID of the pull request.
  int64 pull_request_id = 6;
  int32 pull_request_number = 7;

  // The session ID is the ID of the session that this job is associated with.
  string session_id = 8;
}