package jobs

import (
	"context"

	"github.com/github/sweagentd/internal/observability"
	"github.com/pkg/errors"
	"google.golang.org/protobuf/proto"
)

const TestQueueJobQueueName = "test-job-queue"

var _ AqueductJob = (*TestQueueJob)(nil)

func (j *TestQueueJob) QueueName() string { return TestQueueJobQueueName }

func (j *TestQueueJob) Marshal() ([]byte, error) {
	data, err := proto.Marshal(j)
	return data, err
}

func (j *TestQueueJob) Unmarshal(data []byte) error {
	if unmarshalErr := (proto.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(data, j); unmarshalErr != nil {
		return errors.Wrapf(unmarshalErr, "failed to unmarshal %T", j)
	}
	return nil
}

func (j *TestQueueJob) Process(ctx context.Context, obsv observability.Exporters) error {
	// Simulate processing the job
	obsv.LoggerWithTelemetry(ctx).Info("Processing TestJob")

	if j.Should<PERSON>ani<PERSON> {
		obsv.LoggerWithTelemetry(ctx).Error("Simulated panic in TestJob processing")
		panic("Simulated panic in TestJob processing")
	}
	// Here you would add your actual job processing logic
	return nil
}
