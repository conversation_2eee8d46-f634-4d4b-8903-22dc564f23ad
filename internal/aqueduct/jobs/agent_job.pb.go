// Update generated code with script/protoc

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v5.27.2
// source: agent_job.proto

package jobs

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AgentJob struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The sweagentd assignment ID.
	AssignmentId string `protobuf:"bytes,1,opt,name=assignment_id,json=assignmentId,proto3" json:"assignment_id,omitempty"`
	// The sweagentd job ID that this (aqueduct) job is associated with.
	JobId string `protobuf:"bytes,2,opt,name=job_id,json=jobId,proto3" json:"job_id,omitempty"`
	// The owner ID of the repository.
	OwnerId int64 `protobuf:"varint,3,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	// The repository ID.
	RepositoryId int64 `protobuf:"varint,4,opt,name=repository_id,json=repositoryId,proto3" json:"repository_id,omitempty"`
	// The actor ID is the GitHub ID of the user that initiated the job.
	ActorId int64 `protobuf:"varint,5,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// The pull request ID is the GitHub ID of the pull request.
	PullRequestId     int64 `protobuf:"varint,6,opt,name=pull_request_id,json=pullRequestId,proto3" json:"pull_request_id,omitempty"`
	PullRequestNumber int32 `protobuf:"varint,7,opt,name=pull_request_number,json=pullRequestNumber,proto3" json:"pull_request_number,omitempty"`
	// The session ID is the ID of the session that this job is associated with.
	SessionId string `protobuf:"bytes,8,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
}

func (x *AgentJob) Reset() {
	*x = AgentJob{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_job_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AgentJob) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentJob) ProtoMessage() {}

func (x *AgentJob) ProtoReflect() protoreflect.Message {
	mi := &file_agent_job_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentJob.ProtoReflect.Descriptor instead.
func (*AgentJob) Descriptor() ([]byte, []int) {
	return file_agent_job_proto_rawDescGZIP(), []int{0}
}

func (x *AgentJob) GetAssignmentId() string {
	if x != nil {
		return x.AssignmentId
	}
	return ""
}

func (x *AgentJob) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (x *AgentJob) GetOwnerId() int64 {
	if x != nil {
		return x.OwnerId
	}
	return 0
}

func (x *AgentJob) GetRepositoryId() int64 {
	if x != nil {
		return x.RepositoryId
	}
	return 0
}

func (x *AgentJob) GetActorId() int64 {
	if x != nil {
		return x.ActorId
	}
	return 0
}

func (x *AgentJob) GetPullRequestId() int64 {
	if x != nil {
		return x.PullRequestId
	}
	return 0
}

func (x *AgentJob) GetPullRequestNumber() int32 {
	if x != nil {
		return x.PullRequestNumber
	}
	return 0
}

func (x *AgentJob) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

var File_agent_job_proto protoreflect.FileDescriptor

var file_agent_job_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x6a, 0x6f, 0x62, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x1e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x73, 0x77, 0x65, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x64, 0x2e, 0x61, 0x71, 0x75, 0x65, 0x64, 0x75, 0x63, 0x74, 0x2e, 0x6a, 0x6f, 0x62,
	0x73, 0x22, 0x98, 0x02, 0x0a, 0x08, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4a, 0x6f, 0x62, 0x12, 0x23,
	0x0a, 0x0d, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x77,
	0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6f, 0x77,
	0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x72, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x75, 0x6c, 0x6c, 0x5f, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d,
	0x70, 0x75, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x2e, 0x0a,
	0x13, 0x70, 0x75, 0x6c, 0x6c, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x70, 0x75, 0x6c, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1d, 0x0a,
	0x0a, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x42, 0x09, 0x5a, 0x07,
	0x2e, 0x2f, 0x3b, 0x6a, 0x6f, 0x62, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_agent_job_proto_rawDescOnce sync.Once
	file_agent_job_proto_rawDescData = file_agent_job_proto_rawDesc
)

func file_agent_job_proto_rawDescGZIP() []byte {
	file_agent_job_proto_rawDescOnce.Do(func() {
		file_agent_job_proto_rawDescData = protoimpl.X.CompressGZIP(file_agent_job_proto_rawDescData)
	})
	return file_agent_job_proto_rawDescData
}

var file_agent_job_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_agent_job_proto_goTypes = []interface{}{
	(*AgentJob)(nil), // 0: github.sweagentd.aqueduct.jobs.AgentJob
}
var file_agent_job_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_agent_job_proto_init() }
func file_agent_job_proto_init() {
	if File_agent_job_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_agent_job_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AgentJob); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_agent_job_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_agent_job_proto_goTypes,
		DependencyIndexes: file_agent_job_proto_depIdxs,
		MessageInfos:      file_agent_job_proto_msgTypes,
	}.Build()
	File_agent_job_proto = out.File
	file_agent_job_proto_rawDesc = nil
	file_agent_job_proto_goTypes = nil
	file_agent_job_proto_depIdxs = nil
}
