package jobs

import (
	"context"

	db "github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/observability"
	"github.com/pkg/errors"
	"google.golang.org/protobuf/proto"
)

const AgentJobQueueName = "agent-job-queue"

var _ AqueductJob = (*AgentJob)(nil)

func (j *AgentJob) QueueName() string { return AgentJobQueueName }

func (j *AgentJob) Marshal() ([]byte, error) {
	data, err := proto.Marshal(j)
	return data, err
}

func (j *AgentJob) Unmarshal(data []byte) error {
	if unmarshalErr := (proto.UnmarshalOptions{
		DiscardUnknown: true,
	}).Unmarshal(data, j); unmarshalErr != nil {
		return errors.Wrapf(unmarshalErr, "failed to unmarshal %T", j)
	}
	return nil
}

func (j *Agent<PERSON>ob) Process(ctx context.Context, obsv observability.Exporters) error {
	obsv.LoggerWithTelemetry(ctx).Info("Processing AgentJob")

	// Here you would add your actual job processing logic
	return nil
}

func ToAgentJob(job *db.Job) (*AgentJob, error) {
	if job == nil {
		return nil, errors.New("job cannot be nil")
	}

	agentJob := &AgentJob{
		AssignmentId:      job.AssignmentID,
		JobId:             job.ID,
		OwnerId:           job.OwnerID,
		RepositoryId:      job.RepoID,
		ActorId:           job.ActorID,
		PullRequestId:     job.PullRequestID,
		PullRequestNumber: int32(job.PullRequestNumber),
		SessionId:         job.SessionID,
	}

	return agentJob, nil
}
