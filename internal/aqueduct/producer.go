package aqueduct

import (
	"context"

	"github.com/github/aqueduct-client-go/v3/pkg/aqueduct"
	"github.com/github/sweagentd/internal/aqueduct/jobs"
	db "github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/requestctx"
	"github.com/pkg/errors"
)

func (ac *aqueductClient) SendTestJob(ctx context.Context) error {
	// Send a test message to verify that everything is functional.
	job := jobs.TestQueueJob{}
	job.Message = "test message for job"

	_, err := ac.queueJob(ctx, &job)
	if err != nil {
		return errors.Wrap(err, "failed to queue test job")
	}

	return nil
}

func (ac *aqueductClient) QueueJob(ctx context.Context, job *db.Job) (string, error) {
	j, err := jobs.ToAgentJob(job)
	if err != nil {
		return "", errors.Wrap(err, "failed to convert job to message")
	}

	return ac.QueueJobRaw(ctx, j)
}

func (ac *aqueductClient) QueueJobRaw(ctx context.Context, job *jobs.AgentJob, opts ...aqueduct.SendOption) (string, error) {
	id, err := ac.queueJob(ctx, job, opts...)
	if err != nil {
		return "", errors.Wrap(err, "failed to queue job")
	}

	return id, err
}

func (ac *aqueductClient) queueJob(ctx context.Context, job jobs.AqueductJob, opts ...aqueduct.SendOption) (string, error) {
	// serialize the job payload
	payload, err := job.Marshal()
	if err != nil {
		return "", errors.Wrap(err, "failed to marshal job payload")
	}

	// set headers and such
	aqueductJob := &aqueduct.Job{
		App:     ac.appName,
		Queue:   job.QueueName(),
		Payload: payload,
		Headers: getTenantHeaders(ctx),
	}

	id, err := ac.client.Send(ctx, *aqueductJob, opts...)
	if err != nil {
		return "", errors.Wrap(err, "failed to send job to aqueduct")
	}

	return id, nil
}

func getTenantHeaders(ctx context.Context) map[string]string {
	headers := make(map[string]string)
	if tenantSlug := requestctx.TenantSlug(ctx); tenantSlug != "" {
		headers[requestctx.GitHubTenantHeader] = tenantSlug
	}
	if tenantID := requestctx.TenantID(ctx); tenantID != "" {
		headers[requestctx.GitHubTenantIDHeader] = tenantID
	}
	return headers
}
