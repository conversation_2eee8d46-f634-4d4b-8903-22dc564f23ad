// Package consumer provides worker pool options for Aqueduct job consumers.
package consumer

import (
	"time"

	"github.com/github/aqueduct-client-go/v3/pkg/aqueduct"
)

// This file is based heavily on copilot-limiter's worker pool implementation.

// PoolOption defines a function type that modifies the workerConfig configuration.
type PoolOption func(*workerConfig)

// WithWorkerCount sets the number of workers in the pool.
func WithWorkerCount(count int) PoolOption {
	return func(pool *workerConfig) {
		pool.workerCount = count
	}
}

// WithHeartbeatInterval sets the heartbeat interval for the workers.
func WithHeartbeatInterval(interval time.Duration) PoolOption {
	return func(pool *workerConfig) {
		pool.heartBeatInterval = interval
	}
}

// WithHeartbeatTimeout sets the heartbeat timeout for the workers.
func WithHeartbeatTimeout(timeout time.Duration) PoolOption {
	return func(pool *workerConfig) {
		pool.heartBeatTimeout = timeout
	}
}

// <PERSON><PERSON><PERSON><PERSON> sets the job handler for the worker pool.
func WithHandler(handler aqueduct.JobHandler) PoolOption {
	return func(pool *workerConfig) {
		pool.handler = handler
	}
}

// WithQueues sets the queues for the worker pool.
func WithQueues(queues []string) PoolOption {
	return func(pool *workerConfig) {
		pool.queues = queues
	}
}

// WithPoolName sets the name of the worker pool.
func WithPoolName(name string) PoolOption {
	return func(pool *workerConfig) {
		pool.poolName = name
	}
}
