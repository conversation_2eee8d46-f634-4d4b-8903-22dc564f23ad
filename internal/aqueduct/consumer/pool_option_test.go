package consumer

import (
	"context"
	"testing"
	"time"

	"github.com/github/sweagentd/internal/aqueduct"
	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/jobexecutor"
	"github.com/github/sweagentd/internal/observability"
	"github.com/stretchr/testify/require"
)

const (
	TestAppName       string = "test-app"
	TestAqueductURL   string = "aqueduct-lite:18081"
	TestAPIKey        string = "test-api-key"
	TestAPIKeyVersion int    = 123
)

func getTestClient() aqueduct.AqueductClient {
	ac, _ := aqueduct.NewAqueductClient(
		context.Background(),
		observability.NewNoopExporters(),
		TestAppName,
		aqueduct.WithAqueductURL(TestAqueductURL),
		aqueduct.WithAPIKey(TestAPIKey),
		aqueduct.WithAPIKeyVersion(TestAPIKeyVersion),
	)
	return ac
}

func TestPoolOptions(t *testing.T) {
	obsv := observability.NewNoopExporters()
	executor := jobexecutor.NewNoopJobExecutor(obsv, nil)
	client := getTestClient()
	ctx := context.Background()
	pool, err := NewWorkerPool(
		ctx,
		obsv,
		client,
		WithWorkerCount(2),
		WithHeartbeatInterval(30*time.Second),
		WithHeartbeatTimeout(60*time.Second),
		WithQueues([]string{"test"}),
		WithPoolName("test-pool"),
		WithHandler(ProcessJob(obsv, executor, featureflags.NewNoopClient(nil), nil)),
	)
	require.NoError(t, err)
	require.NotEmpty(t, pool)
}
