package consumer

import (
	"context"
	"fmt"
	"time"

	"github.com/github/aqueduct-client-go/v3/pkg/aqueduct"
	"github.com/github/github-telemetry-go/kvp"
	client "github.com/github/sweagentd/internal/aqueduct"
	"github.com/github/sweagentd/internal/observability"
	"go.opentelemetry.io/otel/codes"
)

const defaultPoolName = "default-pool" // should be overwritten by options
const defaultWorkerCount int = 1
const defaultHeartBeatInterval = 30 * time.Second
const defaultHeartBeatTimeout = 60 * time.Second

// workerConfig holds the configuration for the worker pool.
type workerConfig struct {
	obsv              observability.Exporters
	workerCount       int
	heartBeatInterval time.Duration
	heartBeatTimeout  time.Duration
	handler           aqueduct.JobHandler
	queues            []string
	poolName          string
	statsConfig       *aqueduct.StatsConfig
}

// NewWorkerPool creates a new WorkerPool with the provided options.
func NewWorkerPool(ctx context.Context, obsv observability.Exporters, ac client.AqueductClient, options ...PoolOption) (*aqueduct.WorkerPool, error) {
	ctx, span := obsv.Tracer.Start(ctx, "aqueduct.NewWorkerPool")
	defer span.End() // Ensure the span is ended

	methodLogger := obsv.Logger.WithContext(ctx).WithFields(
		kvp.String("code.function", "NewWorkerPool"),
		kvp.String("code.namespace", "aqueduct"),
	)
	defer obsv.LogWithDuration(ctx, methodLogger, time.Now(), "NewWorkerPool ")

	wp := &workerConfig{
		workerCount:       defaultWorkerCount,
		heartBeatInterval: defaultHeartBeatInterval,
		heartBeatTimeout:  defaultHeartBeatTimeout,
		poolName:          defaultPoolName,
		obsv:              obsv,
	}
	for _, option := range options {
		option(wp)
	}

	methodLogger.Info("Initializing Aqueduct worker pool")

	statsConfig, err := aqueduct.NewStatsConfig(
		aqueduct.WithStatsClient(wp.obsv.Statter),
	)
	if err != nil {
		span.RecordError(err)
		span.SetStatus(codes.Error, err.Error())
		methodLogger.WithError(err).Error("error creating stats config for Aqueduct worker pool")
		return nil, err
	}
	wp.statsConfig = statsConfig

	heartBeatConfig, err := aqueduct.NewHeartbeatConfig(
		aqueduct.WithHeartbeatInterval(wp.heartBeatInterval),
		aqueduct.WithHeartbeatTimeout(wp.heartBeatTimeout),
	)
	if err != nil {
		err = fmt.Errorf("failed to create heartbeat config: %w", err)
		span.RecordError(err)
		span.SetStatus(codes.Error, err.Error())
		methodLogger.WithError(err).Error("error creating heartbeat config for Aqueduct worker pool")
		return nil, err
	}

	pool, err := aqueduct.NewWorkerPool(
		ac.Client(),
		ac.AppName(),
		wp.poolName,
		wp.queues,
		wp.handler,
		wp.workerCount,
		aqueduct.WithHeartbeatConfig(heartBeatConfig),
		aqueduct.WithLogger(wp.obsv.Logger.Named("aqueduct-worker")),
		aqueduct.WithWorkerStats(wp.statsConfig),
	)
	if err != nil {
		span.RecordError(err)
		span.SetStatus(codes.Error, err.Error())
		methodLogger.WithError(err).Error("error creating Aqueduct worker pool")
		return nil, fmt.Errorf("failed to create Aqueduct worker pool: %w", err)
	}

	methodLogger.Info("Aqueduct worker pool created successfully")

	return pool, nil
}
