package consumer

import (
	"context"
	stderrors "errors"
	"fmt"

	"github.com/github/aqueduct-client-go/v3/pkg/aqueduct"
	"github.com/github/sweagentd/internal/aqueduct/jobs"
	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/jobexecutor"
	ju "github.com/github/sweagentd/internal/jobutils"
	"github.com/github/sweagentd/internal/observability"
	"github.com/github/sweagentd/internal/requestctx"
	"github.com/pkg/errors"
)

func ProcessJob(obsv observability.Exporters, executor jobexecutor.JobExecutor, ffClient featureflags.Client, requeueOptions *jobexecutor.RequeueOptions) aqueduct.JobHandler {
	return func(ctx context.Context, rr aqueduct.ReceiveResult) (err error) {
		ctx = requestctx.AddData(ctx, &requestctx.CtxData{
			AqueductJob: &requestctx.CtxAqueductJob{
				ID:    rr.ID,
				Queue: rr.Queue,
			},
		})

		if err := ForwardTenantInformation(ctx, rr.Headers); err != nil {
			return errors.Wrap(err, "failed to forward tenant information")
		}

		defer func() {
			if p := recover(); p != nil {
				if e, ok := p.(error); ok {
					err = stderrors.Join(err, e)
				} else {
					err = stderrors.Join(err, errors.New("job handler panic"))
				}
			}

			if err != nil {
				obsv.LoggerWithTelemetry(ctx).WithError(err).Error("error processing job off queue")
				_, ex, _ := requestctx.ToTelemetry(ctx, nil)
				_ = obsv.Reporter.Report(ctx, fmt.Errorf("error processing job off queue: %w", err), ex)
			}
		}()

		// The assumptions this code makes are simple and can be easily iterated
		// on, but probably will be fine for a while:
		// There is a 1-1 relationship between an (aqueduct) job type and a queue,
		// which lets us couple queue handling to specific job types.
		// We could easily iterate by adding a message header which allows a
		// reflection or a registration pattern to handle different job types in
		// the same queue.
		switch rr.Queue {
		case jobs.AgentJobQueueName:
			ju.UpdateRequestContextJobQueue(ctx, obsv, rr.ID)

			var job jobs.AgentJob
			if err := job.Unmarshal(rr.Job.Payload); err != nil {
				return err
			}

			if ffClient.IsEnabledForUserOrRepoOrOwner(ctx, featureflags.CopilotSWEAgentJobProcessorDisabled, job.ActorId, job.RepositoryId, job.OwnerId) {
				return executor.RequeueJob(ctx, &job, requeueOptions)
			}
      
			ju.UpdateRequestContextJobQueue(ctx, obsv, rr.ID)

			return executor.ProcessJob(ctx, &job)

		case jobs.TestQueueJobQueueName:
			var job jobs.TestQueueJob
			if err := job.Unmarshal(rr.Job.Payload); err != nil {
				return err
			}
			return job.Process(ctx, obsv)

		default:
			return errors.Errorf("unknown queue %q", rr.Queue)
		}
	}
}

func ForwardTenantInformation(ctx context.Context, headers map[string]string) error {
	val, ok := headers[requestctx.GitHubTenantHeader]
	if ok {
		err := requestctx.AddTenantSlug(ctx, val)
		if err != nil {
			return errors.Wrap(err, "failed to add tenant slug to request context")
		}
	}

	val, ok = headers[requestctx.GitHubTenantIDHeader]
	if ok {
		err := requestctx.AddTenantID(ctx, val)
		if err != nil {
			return errors.Wrap(err, "failed to add tenant id to request context")
		}
	}

	return nil
}
