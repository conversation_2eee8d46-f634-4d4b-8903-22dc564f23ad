package consumer

import (
	"context"
	"testing"

	"github.com/github/aqueduct-client-go/v3/pkg/aqueduct"
	"github.com/github/sweagentd/internal/aqueduct/jobs"
	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/jobexecutor"
	"github.com/github/sweagentd/internal/observability"
	"github.com/github/sweagentd/internal/requestctx"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/proto"
)

func TestProcessJob(t *testing.T) {
	obsv := observability.NewNoopExporters()
	executor := jobexecutor.NewNoopJobExecutor(obsv, nil)

	t.Run("successfully processes test-job-queue", func(t *testing.T) {
		testJob := &jobs.TestQueueJob{Message: "test message"}

		payload, err := proto.Marshal(testJob)
		require.NoError(t, err)

		receiveResult := aqueduct.ReceiveResult{
			Job: aqueduct.Job{
				ID:      "test-job-id",
				Queue:   jobs.TestQueueJobQueueName,
				Payload: payload,
			},
		}

		handler := ProcessJob(obsv, executor, featureflags.NewNoopClient(nil), nil)

		err = handler(context.Background(), receiveResult)
		require.NoError(t, err)
	})

	t.Run("returns error for unknown queue", func(t *testing.T) {
		receiveResult := aqueduct.ReceiveResult{
			Job: aqueduct.Job{
				ID:      "test-job-id",
				Queue:   "unknown-queue",
				Payload: []byte("test payload"),
			},
		}

		handler := ProcessJob(obsv, executor, featureflags.NewNoopClient(nil), nil)

		err := handler(context.Background(), receiveResult)

		require.Error(t, err)
		require.Contains(t, err.Error(), `unknown queue "unknown-queue"`)
	})

	t.Run("returns error for invalid test job payload", func(t *testing.T) {
		receiveResult := aqueduct.ReceiveResult{
			Job: aqueduct.Job{
				ID:      "test-job-id",
				Queue:   jobs.TestQueueJobQueueName,
				Payload: []byte("invalid protobuf payload"),
			},
		}

		handler := ProcessJob(obsv, executor, featureflags.NewNoopClient(nil), nil)
		err := handler(context.Background(), receiveResult)

		require.Error(t, err)
		require.Contains(t, err.Error(), "failed to unmarshal")
	})

	t.Run("handles panic in job processing", func(t *testing.T) {
		// This test demonstrates the panic recovery mechanism
		// We would need a job type that panics to test this properly
		// For now, we'll test with valid input to ensure the defer mechanism works

		testJob := &jobs.TestQueueJob{
			Message:     "test message",
			ShouldPanic: true,
		}

		payload, err := proto.Marshal(testJob)
		require.NoError(t, err)

		receiveResult := aqueduct.ReceiveResult{
			Job: aqueduct.Job{
				ID:      "test-job-id",
				Queue:   jobs.TestQueueJobQueueName,
				Payload: payload,
			},
		}

		handler := ProcessJob(obsv, executor, featureflags.NewNoopClient(nil), nil)
		err = handler(context.Background(), receiveResult)

		require.Error(t, err)
	})

	t.Run("errors when job processing fails", func(t *testing.T) {
		// Test the error logging mechanism by using an invalid payload
		receiveResult := aqueduct.ReceiveResult{
			Job: aqueduct.Job{
				ID:      "test-job-id",
				Queue:   jobs.TestQueueJobQueueName,
				Payload: []byte("invalid payload"),
			},
		}

		handler := ProcessJob(obsv, executor, featureflags.NewNoopClient(nil), nil)
		err := handler(context.Background(), receiveResult)

		require.Error(t, err)
		require.Contains(t, err.Error(), "failed to unmarshal")
	})

	t.Run("forwards tenant information from AgentJob", func(t *testing.T) {
		ctx := context.Background()
		ctx = requestctx.AddData(ctx, &requestctx.CtxData{})

		testTenantSlug := "test-org"
		testTenantID := "test-tenant-123"

		agentJob := &jobs.AgentJob{
			AssignmentId: "test-assignment",
			JobId:        "test-job",
		}

		payload, err := proto.Marshal(agentJob)
		require.NoError(t, err)

		receiveResult := aqueduct.ReceiveResult{
			Job: aqueduct.Job{
				ID:      "test-job-id",
				Queue:   jobs.AgentJobQueueName,
				Payload: payload,
				Headers: map[string]string{
					requestctx.GitHubTenantHeader:   testTenantSlug,
					requestctx.GitHubTenantIDHeader: testTenantID,
				},
			},
		}

		// Use a mock executor that captures the context
		mockExecutor := jobexecutor.NewMockJobExecutor(t)
		var capturedCtx context.Context
		mockExecutor.On("ProcessJob", mock.MatchedBy(func(ctx context.Context) bool {
			capturedCtx = ctx
			return true
		}), mock.MatchedBy(func(job *jobs.AgentJob) bool {
			return job.AssignmentId == "test-assignment" && job.JobId == "test-job"
		})).Return(nil)

		handler := ProcessJob(obsv, mockExecutor, featureflags.NewNoopClient(nil), nil)
		err = handler(ctx, receiveResult)
		require.NoError(t, err)

		// Verify that the tenant information was forwarded to the context
		require.Equal(t, testTenantSlug, requestctx.TenantSlug(capturedCtx))
		require.Equal(t, testTenantID, requestctx.TenantID(capturedCtx))
		mockExecutor.AssertExpectations(t)
	})
}
