package jobfactory

import (
	"context"
	_ "embed"
	"testing"

	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/jobutils"
	"github.com/github/sweagentd/internal/observability"
	gg "github.com/google/go-github/v72/github"
	"github.com/stretchr/testify/require"
)

func TestContextUtils(t *testing.T) {
	cf := &github.NoopClientFactory{}
	ffClient := featureflags.NewNoopClient(map[string]bool{
		"copilot_swe_agent":         true,
		"copilot_swe_agent_job_api": true,
	})

	problemStatement := "You died of dysentery"
	owner := gg.User{
		ID:     gg.Ptr(int64(1)),
		NodeID: gg.Ptr("oregon-node-id"),
		Login:  gg.Ptr("oregon"),
		Type:   gg.Ptr("organization"),
		Name:   gg.Ptr("Oregon Wagon Co. Inc."),
	}

	repo := gg.Repository{
		ID:            gg.Ptr(int64(1)),
		NodeID:        gg.Ptr("trail-node-id"),
		Name:          gg.Ptr("trail"),
		Owner:         &owner,
		DefaultBranch: gg.Ptr("main"),
		Private:       gg.Ptr(true),
	}
	cf.MockRepositories = &map[string]gg.Repository{
		"oregon/trail": repo,
	}

	gh, err := cf.NewClient("token")
	require.NoError(t, err)

	t.Run("gathers context for task", func(t *testing.T) {
		job := &jobs.Job{
			RepoOwner: *repo.Owner.Login,
			RepoName:  *repo.Name,
			ProblemStatement: jobs.ProblemStatement{
				Content: problemStatement,
			},
		}
		verifyContext(t, gh, &repo, job, ffClient)
	})

	t.Run("format PR content for task with defaults", func(t *testing.T) {
		job := &jobs.Job{
			RepoOwner: *repo.Owner.Login,
			RepoName:  *repo.Name,
			ProblemStatement: jobs.ProblemStatement{
				Content: problemStatement,
			},
		}
		a := verifyContext(t, gh, &repo, job, ffClient)
		a.FormatResponseContent()
		require.Equal(t, "Copilot Request", job.RunOptions.ResponseTitlePlaceholder)
		require.Contains(t, job.ProblemStatement.Content, problemStatement)
		require.Len(t, job.RunOptions.ResponseLabels, 0)
	})

	t.Run("format PR content for task", func(t *testing.T) {
		job := &jobs.Job{
			RepoOwner:  *repo.Owner.Login,
			RepoName:   *repo.Name,
			BaseCommit: "horse",
			ProblemStatement: jobs.ProblemStatement{
				Content: problemStatement,
			},
			RunOptions: jobs.RunOptions{
				ResponseType:             jobs.JobResponseTypePullRequest,
				ResponseTitlePlaceholder: "Oregon Trail Problem",
				ResponseBodyPlaceholder:  "You just got back from hunting and died of dysentery.",
				ResponseBodySuffix:       "Try #3",
				ResponseLabels: []string{
					"apple][",
				},
			},
		}
		a := verifyContext(t, gh, &repo, job, ffClient)
		a.FormatResponseContent()
		require.Equal(t, "Oregon Trail Problem", job.RunOptions.ResponseTitlePlaceholder)
		require.Contains(t, job.RunOptions.ResponseBodyPlaceholder, "You just got back from hunting and died of dysentery.\nTry #3")
		require.Equal(t, "apple][", job.RunOptions.ResponseLabels[0])
		require.NotContains(t, job.RunOptions.ResponseBodyPlaceholder, jobutils.PullRequestBodyTipSeparator)
		require.NotContains(t, job.RunOptions.ResponseBodyPlaceholder, github.FeedbackTip)
		require.NotContains(t, job.RunOptions.ResponseBodyPlaceholder, github.BestPracticesTip)
	})

	t.Run("adds feedback tip when only that tip flag is enabled", func(t *testing.T) {
		ffAlt := featureflags.NewNoopClient(map[string]bool{
			"copilot_swe_agent":               true,
			"copilot_swe_agent_job_api":       true,
			"copilot_swe_agent_tips":          true,
			"copilot_swe_agent_tips_feedback": true,
		})
		job := &jobs.Job{
			RepoOwner: *repo.Owner.Login,
			RepoName:  *repo.Name,
			ProblemStatement: jobs.ProblemStatement{
				Content: problemStatement,
			},
		}
		a := verifyContext(t, gh, &repo, job, ffAlt)
		a.FormatResponseContent()
		require.Contains(t, job.RunOptions.ResponseBodyPlaceholder, jobutils.PullRequestBodyTipSeparator)
		require.Contains(t, job.RunOptions.ResponseBodyPlaceholder, github.FeedbackTip)
		require.NotContains(t, job.RunOptions.ResponseBodyPlaceholder, github.BestPracticesTip)
	})

	t.Run("adds best practices tip when only that tip flag is enabled", func(t *testing.T) {
		ffClient := featureflags.NewNoopClient(map[string]bool{
			"copilot_swe_agent":                     true,
			"copilot_swe_agent_job_api":             true,
			"copilot_swe_agent_tips":                true,
			"copilot_swe_agent_tips_best_practices": true,
		})
		job := &jobs.Job{
			RepoOwner: *repo.Owner.Login,
			RepoName:  *repo.Name,
			ProblemStatement: jobs.ProblemStatement{
				Content: problemStatement,
			},
			RunOptions: jobs.RunOptions{
				ResponseType: jobs.JobResponseTypePullRequest,
			},
		}
		a := verifyContext(t, gh, &repo, job, ffClient)
		a.FormatResponseContent()
		require.Contains(t, job.RunOptions.ResponseBodyPlaceholder, jobutils.PullRequestBodyTipSeparator)
		require.Contains(t, job.RunOptions.ResponseBodyPlaceholder, github.BestPracticesTip)
		require.NotContains(t, job.RunOptions.ResponseBodyPlaceholder, github.FeedbackTip)
	})

	t.Run("format job content with defaults", func(t *testing.T) {
		job := &jobs.Job{
			RepoOwner: *repo.Owner.Login,
			RepoName:  *repo.Name,
			ProblemStatement: jobs.ProblemStatement{
				Content: problemStatement,
			},
			RunOptions: jobs.RunOptions{
				ResponseType: jobs.JobResponseTypePullRequest,
			},
		}
		a := verifyContext(t, gh, &repo, job, ffClient)
		a.FormatJobContent()
		require.Equal(t, problemStatement, job.ProblemStatement.Content)
		require.Equal(t, "Running Copilot", job.RunOptions.RunName)
		require.Equal(t, jobs.ProblemStatementContentFilterModeHiddenCharacters, job.ProblemStatement.ContentFilterMode)
		require.Equal(t, 1, job.ProblemStatement.CommitCount)
	})

	t.Run("format job content", func(t *testing.T) {
		job := &jobs.Job{
			RepoOwner: *repo.Owner.Login,
			RepoName:  *repo.Name,
			ProblemStatement: jobs.ProblemStatement{
				Content:           problemStatement,
				ContentFilterMode: jobs.ProblemStatementContentFilterModeNone,
			},
			RunOptions: jobs.RunOptions{
				ResponseType: jobs.JobResponseTypePullRequest,
				RunName:      "Playing Oregon Trail",
			},
		}
		a := verifyContext(t, gh, &repo, job, ffClient)
		a.FormatJobContent()
		require.Equal(t, problemStatement, job.ProblemStatement.Content)
		require.Equal(t, "Playing Oregon Trail", job.RunOptions.RunName)
		require.Equal(t, jobs.ProblemStatementContentFilterModeNone, job.ProblemStatement.ContentFilterMode)
		require.Equal(t, 1, job.ProblemStatement.CommitCount)
	})

}

func verifyContext(t *testing.T, gh github.ClientInterface, repo *gg.Repository, job *jobs.Job, ffClient featureflags.Client) *DefaultJobContextAssembler {
	obsv := observability.NewNoopExporters()
	jobAssembly := &JobAssembly{
		Job: job,
	}
	a := NewDefaultJobContextAssembler(context.Background(), obsv, ffClient, gh, jobAssembly, 1)

	require.Equal(t, *repo.Name, job.RepoName)
	require.Equal(t, *repo.Owner.Login, job.RepoOwner)

	return a
}
