package jobfactory

import (
	"context"
	_ "embed"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"testing"

	copilotUsersTwirp "github.com/github/copilot-twirp/proto/users/v1"
	"github.com/github/go-http/v2/middleware/headers"
	mark "github.com/github/go-mark"
	"github.com/github/sweagentd/internal/experiments"
	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/githubtwirp"
	"github.com/github/sweagentd/internal/joberrors"
	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/jobutils"
	"github.com/github/sweagentd/internal/observability"
	"github.com/github/sweagentd/internal/problemstatement"
	"github.com/github/sweagentd/internal/requestctx"
	sweagentdTwirp "github.com/github/sweagentd/proto/sweagentd/v1"
	gg "github.com/google/go-github/v72/github"
	"github.com/stretchr/testify/require"
)

func TestJobFactory(t *testing.T) {
	ffClient := featureflags.NewNoopClient(map[string]bool{
		"copilot_swe_agent": true,
		featureflags.CopilotSWEAgentEnableExperimentsForAPI: true,
	})
	obsv := observability.NewNoopExporters()
	cf := &github.NoopClientFactory{}
	ghTwirp := githubtwirp.NewNoopClient()
	ghTwirp.NoopUserDetailAPI.MockGetCopilotUser = func(ctx context.Context, req *copilotUsersTwirp.GetCopilotUserRequest) (*copilotUsersTwirp.GetCopilotUserResponse, error) {
		return &copilotUsersTwirp.GetCopilotUserResponse{
			UserDetails: &copilotUsersTwirp.CopilotUserDetails{
				CopilotPlan: copilotUsersTwirp.CopilotPlan_COPILOT_PLAN_ENTERPRISE,
			},
		}, nil
	}
	jobsStore := jobs.NewMemoryStore()
	experimentsClient := experiments.NewNoopClient()
	h := New(obsv, ffClient, jobsStore, ghTwirp, experimentsClient)

	runName := "Oregon Trail Problem"
	problemStatement := "You died of dysentery"
	owner := gg.User{
		ID:     gg.Ptr(int64(1)),
		NodeID: gg.Ptr("oregon-node-id"),
		Login:  gg.Ptr("oregon"),
		Type:   gg.Ptr("organization"),
		Name:   gg.Ptr("Oregon Wagon Co. Inc."),
	}
	repo := gg.Repository{
		ID:            gg.Ptr(int64(1)),
		NodeID:        gg.Ptr("trail-node-id"),
		Name:          gg.Ptr("trail"),
		Owner:         &owner,
		DefaultBranch: gg.Ptr("main"),
		Private:       gg.Ptr(true),
	}

	cf.MockRepositories = &map[string]gg.Repository{
		"oregon/trail": repo,
	}

	userInfo := &requestctx.UserInfo{
		Login:      "user-login",
		TrackingID: "tracking-id",
		ID:         1,
	}

	t.Run("new assignment for task", func(t *testing.T) {
		gh, err := cf.NewClient("token")
		require.NoError(t, err)
		jobAssembly, err := h.NewJobAssembly(context.Background(), &repo, &NewJobArgs{
			RunName:          runName,
			ProblemStatement: problemStatement,
		}, userInfo)
		require.NoError(t, err)
		err = h.AssembleJob(context.Background(), gh, jobAssembly, userInfo)
		require.NoError(t, err)
		require.NotNil(t, jobAssembly)
		verifyAssembly(t, jobAssembly, &repo, problemStatement)
	})

	t.Run("new assignment with base ref", func(t *testing.T) {
		for _, ref := range []string{"refs/heads/some-branch", "some-branch"} {
			gh, err := cf.NewClient("token")
			gh.(*github.NoopClient).MockPullRequestList = []*gg.PullRequest{}
			require.NoError(t, err)
			jobAssembly, err := h.NewJobAssembly(context.Background(), &repo, &NewJobArgs{
				RunName:          runName,
				ProblemStatement: problemStatement,
				ResponseOptions: ResponseOptions{
					ResponseType: jobs.JobResponseTypePullRequest,
					PullRequest: PullRequestResponseArgs{
						BaseRef: ref,
					},
				},
			}, userInfo)
			require.NoError(t, err)
			err = h.AssembleJob(context.Background(), gh, jobAssembly, userInfo)
			require.NoError(t, err)
			require.NotNil(t, jobAssembly)
			verifyAssembly(t, jobAssembly, &repo, problemStatement)
			require.Equal(t, ref, jobAssembly.Job.BaseCommit)
		}
	})

	t.Run("new assignment with head ref", func(t *testing.T) {
		for _, ref := range []string{"refs/heads/copilot/vscode-1234", "copilot/vscode-1234"} {
			gh, err := cf.NewClient("token")
			gh.(*github.NoopClient).MockPullRequestList = []*gg.PullRequest{}
			require.NoError(t, err)
			jobAssembly, err := h.NewJobAssembly(context.Background(), &repo, &NewJobArgs{
				RunName:          runName,
				ProblemStatement: problemStatement,
				ResponseOptions: ResponseOptions{
					ResponseType: jobs.JobResponseTypePullRequest,
					PullRequest: PullRequestResponseArgs{
						HeadRef: ref,
					},
				},
			}, userInfo)
			require.NoError(t, err)
			err = h.AssembleJob(context.Background(), gh, jobAssembly, userInfo)
			require.NoError(t, err)
			require.NotNil(t, jobAssembly)
			verifyAssembly(t, jobAssembly, &repo, problemStatement)
			require.Equal(t, strings.TrimPrefix(ref, "refs/heads/"), jobAssembly.Job.BranchName)
		}
	})

	t.Run("new assignment with head ref for existing PR", func(t *testing.T) {
		for _, ref := range []string{"refs/heads/copilot/vscode-1234", "copilot/vscode-1234"} {
			gh, err := cf.NewClient("token")
			gh.(*github.NoopClient).MockPullRequestList = []*gg.PullRequest{{Number: gg.Ptr(1234), Head: &gg.PullRequestBranch{Ref: gg.Ptr(ref), SHA: gg.Ptr("sha-1234")}}}
			require.NoError(t, err)
			jobAssembly, err := h.NewJobAssembly(context.Background(), &repo, &NewJobArgs{
				RunName:          runName,
				ProblemStatement: problemStatement,
				ResponseOptions: ResponseOptions{
					ResponseType: jobs.JobResponseTypePullRequest,
					PullRequest: PullRequestResponseArgs{
						HeadRef: ref,
					},
				},
			}, userInfo)
			require.NoError(t, err)
			err = h.AssembleJob(context.Background(), gh, jobAssembly, userInfo)
			require.Error(t, fmt.Errorf("a pull request already exists for refs/heads/copilot/vscode-1234 to refs/heads/main"), err)
		}
	})

	t.Run("new assignment with invalid head ref", func(t *testing.T) {
		gh, err := cf.NewClient("token")
		gh.(*github.NoopClient).MockPullRequestList = []*gg.PullRequest{}
		require.NoError(t, err)
		jobAssembly, err := h.NewJobAssembly(context.Background(), &repo, &NewJobArgs{
			RunName:          runName,
			ProblemStatement: problemStatement,
			ResponseOptions: ResponseOptions{
				ResponseType: jobs.JobResponseTypePullRequest,
				PullRequest: PullRequestResponseArgs{
					HeadRef: "refs/heads/test-1234",
				},
			},
		}, userInfo)
		require.NoError(t, err)
		err = h.AssembleJob(context.Background(), gh, jobAssembly, userInfo)
		require.Error(t, fmt.Errorf("cannot reuse base branch refs/heads/test-1234 for pull request, it must start with 'copilot/'"), err)

	})

	t.Run("new assignment not enough args", func(t *testing.T) {
		jobAssembly, err := h.NewJobAssembly(context.Background(), &repo, &NewJobArgs{}, userInfo)
		require.Error(t, err)
		require.Nil(t, jobAssembly)

		// Fake it problem statement being empty and test assemble job
		jobAssembly, err = h.NewJobAssembly(context.Background(), &repo, &NewJobArgs{ProblemStatement: "temp"}, userInfo)
		require.NoError(t, err)
		jobAssembly.Job.ProblemStatement.Content = ""
		err = h.AssembleJob(context.Background(), nil, jobAssembly, userInfo)
		require.Error(t, err)
	})

	t.Run("truncate problem statement", func(t *testing.T) {
		tooLongProblemStatement := strings.Repeat("X", problemstatement.MaxProblemStatementLength+10)
		expectedProblemStatement := strings.Repeat("X", problemstatement.MaxProblemStatementLength-3) + "..."
		gh, err := cf.NewClient("token")
		gh.(*github.NoopClient).MockPullRequestList = []*gg.PullRequest{}
		require.NoError(t, err)
		jobAssembly, err := h.NewJobAssembly(context.Background(), &repo, &NewJobArgs{
			RunName:          runName,
			ProblemStatement: tooLongProblemStatement,
		}, userInfo)
		require.NoError(t, err)
		err = h.AssembleJob(context.Background(), gh, jobAssembly, userInfo)
		require.NoError(t, err)
		require.NotNil(t, jobAssembly)
		verifyAssembly(t, jobAssembly, &repo, expectedProblemStatement)
	})

	t.Run("handle invalid UTF-8 error from PR twirp API", func(t *testing.T) {
		ghTwirpAlt := githubtwirp.NewNoopClient()
		ghTwirpAlt.NoopUserDetailAPI.MockGetCopilotUser = ghTwirp.NoopUserDetailAPI.MockGetCopilotUser
		ghTwirpAlt.NoopSweagentdAPI = &githubtwirp.NoopSweagentdAPI{
			MockCreatePullRequest: func(ctx context.Context, req *sweagentdTwirp.CreatePullRequestRequest) (*sweagentdTwirp.CreatePullRequestResponse, error) {
				// Simulate an error with invalid UTF-8 characters
				return nil, fmt.Errorf("some error: %w", fmt.Errorf("string field contains invalid UTF-8: %w", fmt.Errorf("other error")))
			},
		}
		hAlt := New(obsv, ffClient, jobsStore, ghTwirpAlt, experimentsClient)
		gh, err := cf.NewClient("token")
		require.NoError(t, err)
		jobAssembly, err := hAlt.NewJobAssembly(context.Background(), &repo, &NewJobArgs{
			RunName:          runName,
			ProblemStatement: "pretend invalid UTF-8 characters are here",
		}, userInfo)
		require.NoError(t, err)
		err = hAlt.AssembleJob(context.Background(), gh, jobAssembly, userInfo)
		require.Error(t, err)
		require.True(t, errors.Is(err, mark.ErrBadRequest))
		require.Contains(t, err.Error(), "invalid UTF-8 characters passed into API")
	})

	t.Run("handle unexpected error from PR twirp API", func(t *testing.T) {
		ghTwirpAlt := githubtwirp.NewNoopClient()
		ghTwirpAlt.NoopUserDetailAPI.MockGetCopilotUser = ghTwirp.NoopUserDetailAPI.MockGetCopilotUser
		ghTwirpAlt.NoopSweagentdAPI = &githubtwirp.NoopSweagentdAPI{
			MockCreatePullRequest: func(ctx context.Context, req *sweagentdTwirp.CreatePullRequestRequest) (*sweagentdTwirp.CreatePullRequestResponse, error) {
				// Simulate an error with invalid UTF-8 characters
				return nil, fmt.Errorf("some error")
			},
		}
		hAlt := New(obsv, ffClient, jobsStore, ghTwirpAlt, experimentsClient)
		gh, err := cf.NewClient("token")
		require.NoError(t, err)
		jobAssembly, err := hAlt.NewJobAssembly(context.Background(), &repo, &NewJobArgs{
			RunName:          runName,
			ProblemStatement: "pretend invalid UTF-8 characters are here",
		}, userInfo)
		require.NoError(t, err)
		err = hAlt.AssembleJob(context.Background(), gh, jobAssembly, userInfo)
		require.Error(t, err)
		// Make sure that it didn't get marked as a bad request like the UTF scenario.
		require.False(t, errors.Is(err, mark.ErrBadRequest))
	})

	t.Run("handle 422 from GitHub API", func(t *testing.T) {
		client, err := cf.NewClient("token")
		require.NoError(t, err)
		ghAlt := client.(*github.NoopClient)
		ghAlt.MockCreateNewEmptyBranch = func(ctx context.Context, ownerLogin, repoName, baseBranchName, branchName string) (*gg.Reference, error) {
			return nil, github.NewGitHubAPIError(&gg.Response{
				Response: &http.Response{
					StatusCode: 422,
					Header: http.Header{
						headers.GitHubRequestID: []string{"1234"},
					},
				},
			}, "some context here", fmt.Errorf("PATCH https://api.github.com/repos/:owner/:repo/git/refs/heads/copilot/<branch>: 422 Repository rule violations found\n\nCommits must have verified signatures."))
		}
		require.NoError(t, err)
		jobAssembly, err := h.NewJobAssembly(context.Background(), &repo, &NewJobArgs{
			RunName:          runName,
			ProblemStatement: "pretend invalid UTF-8 characters are here",
		}, userInfo)
		require.NoError(t, err)
		err = h.AssembleJob(context.Background(), ghAlt, jobAssembly, userInfo)
		require.Error(t, err)
		// Make sure we got back the custom error for unprocessable entity
		require.True(t, errors.Is(err, joberrors.ErrUnprocessableEntity))
		// Ensure a wrapped version of this error still identifies properly
		wrapped := fmt.Errorf("wrapping error: %w", err)
		wrappedCode := joberrors.StatusCodeFromMarkedError(wrapped)
		require.Equal(t, http.StatusUnprocessableEntity, wrappedCode)
	})

	t.Run("experiments enabled when CopilotSWEAgentEnableExperimentsForAPI feature flag is on", func(t *testing.T) {
		// Create a feature flag client with the experiments API flag enabled
		ffAlt := featureflags.NewNoopClient(map[string]bool{
			"copilot_swe_agent": true,
			featureflags.CopilotSWEAgentEnableExperimentsForAPI: true,
		})

		// Create a job factory with the experiments-enabled feature flag client
		hAlt := New(obsv, ffAlt, jobsStore, ghTwirp, experimentsClient)

		gh, err := cf.NewClient("token")
		require.NoError(t, err)

		jobAssembly, err := hAlt.NewJobAssembly(context.Background(), &repo, &NewJobArgs{
			RunName:          runName,
			ProblemStatement: "pretend invalid UTF-8 characters are here",
		}, userInfo)
		require.NoError(t, err)
		err = hAlt.AssembleJob(context.Background(), gh, jobAssembly, userInfo)
		require.NoError(t, err)
		require.NotNil(t, jobAssembly)

		// Verify experiments were set on both assignment and job
		require.NotNil(t, jobAssembly.Assignment.Experiments)
		require.Equal(t, "noop", jobAssembly.Assignment.Experiments.AssignmentContext)
		require.NotNil(t, jobAssembly.Job.Experiments)
		require.Equal(t, "noop", jobAssembly.Job.Experiments.AssignmentContext)
	})

	t.Run("experiments disabled when CopilotSWEAgentEnableExperimentsForAPI feature flag is off", func(t *testing.T) {
		// Create a feature flag client with the experiments API flag disabled
		ffAlt := featureflags.NewNoopClient(map[string]bool{
			"copilot_swe_agent": true,
			featureflags.CopilotSWEAgentEnableExperimentsForAPI: false,
		})

		// Create a job factory with the experiments-disabled feature flag client
		hAlt := New(obsv, ffAlt, jobsStore, ghTwirp, experimentsClient)

		gh, err := cf.NewClient("token")
		require.NoError(t, err)

		jobAssembly, err := hAlt.NewJobAssembly(context.Background(), &repo, &NewJobArgs{
			RunName:          runName,
			ProblemStatement: "pretend invalid UTF-8 characters are here",
		}, userInfo)
		require.NoError(t, err)
		err = hAlt.AssembleJob(context.Background(), gh, jobAssembly, userInfo)
		require.NoError(t, err)
		require.NotNil(t, jobAssembly)

		// Verify experiments were not set when feature flag is disabled
		require.Nil(t, jobAssembly.Assignment.Experiments)
		require.Nil(t, jobAssembly.Job.Experiments)
	})

}

func verifyAssembly(t *testing.T, result *JobAssembly, repo *gg.Repository, expectedProblemStatement string) {
	assignment := result.Assignment
	require.NotNil(t, assignment)
	require.NotEmpty(t, assignment.ID)
	require.Equal(t, *repo.ID, assignment.RepoID)
	require.Equal(t, *repo.NodeID, assignment.RepoNodeID)
	require.Equal(t, *repo.Name, assignment.RepoName)
	require.Equal(t, *repo.Owner.Login, assignment.RepoOwner)
	require.Equal(t, repo.Owner.GetID(), assignment.OwnerID)
	require.Equal(t, *repo.Owner.NodeID, assignment.OwnerNodeID)

	job := result.Job
	require.NotNil(t, job)
	require.NotEmpty(t, job.ID)
	require.Equal(t, *repo.ID, job.RepoID)
	require.Equal(t, *repo.NodeID, job.RepoNodeID)
	require.Equal(t, *repo.Name, job.RepoName)
	require.Equal(t, *repo.Owner.Login, job.RepoOwner)
	require.Equal(t, repo.Owner.GetID(), job.OwnerID)
	require.Equal(t, *repo.Owner.NodeID, job.OwnerNodeID)
	require.Equal(t, jobs.AgentActionFix, job.Action)
	require.NotEmpty(t, job.BranchName)
	require.Equal(t, jobutils.DefaultModel, job.Model)
	require.Equal(t, jobs.JobStatusPending, job.Status)
	require.Equal(t, int64(1), job.ActorID)
	require.Equal(t, "user-login", job.ActorLogin)

	require.Equal(t, assignment.ID, job.AssignmentID)

	require.Contains(t, job.ProblemStatement.Content, expectedProblemStatement)
	require.Equal(t, 1, job.ProblemStatement.CommitCount)
}
