package jobfactory

import (
	"context"
	"fmt"
	"strings"

	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/jobutils"
	"github.com/github/sweagentd/internal/observability"
)

const PRBodyPrefix = "Thanks for asking me to work on this. I will get started on it and keep this PR's description up to date as I form a plan and make progress.\n\nOriginal description:\n"

// DefaultJobContextAssembler is responsible for gathering context and formatting job content. It keeps any additional context
// required beyond within the object so it the job assembly process can handle a wide variety of concrete types, events, and contexts without
// needing to resort passings around maps a large number of args, or other arbitrary data structures.
type DefaultJobContextAssembler struct {
	ctx         context.Context
	obsv        observability.Exporters
	ffClient    featureflags.Client
	gh          github.ClientInterface
	jobAssembly *JobAssembly
	userID      int64
}

func NewDefaultJobContextAssembler(ctx context.Context, obsv observability.Exporters, ffClient featureflags.Client, gh github.ClientInterface, jobAssembly *JobAssembly, userID int64) *DefaultJobContextAssembler {
	return &DefaultJobContextAssembler{
		ctx:         ctx,
		obsv:        obsv,
		ffClient:    ffClient,
		gh:          gh,
		userID:      userID,
		jobAssembly: jobAssembly,
	}
}

func (a *DefaultJobContextAssembler) JobAssembly() *JobAssembly {
	return a.jobAssembly
}

func (a *DefaultJobContextAssembler) FormatJobContent() {
	_, span := a.obsv.Tracer.Start(a.ctx, "jobapi.FormatJobContent")
	defer span.End()

	job := a.jobAssembly.Job
	if job.ProblemStatement.ContentFilterMode == "" {
		job.ProblemStatement.ContentFilterMode = jobs.ProblemStatementContentFilterModeHiddenCharacters
	}

	if job.RunOptions.RunName == "" {
		job.RunOptions.RunName = "Running Copilot"
	}

	// TODO: Get the actual commit count for the branch on the job
	if job.ProblemStatement.CommitCount == 0 {
		job.ProblemStatement.CommitCount = 1
	}
}

func (a *DefaultJobContextAssembler) FormatResponseContent() {
	_, span := a.obsv.Tracer.Start(a.ctx, "jobapi.FormatResponseContent")
	defer span.End()

	runOpts := a.jobAssembly.Job.RunOptions
	if runOpts.ResponseTitlePlaceholder == "" {
		runOpts.ResponseTitlePlaceholder = "Copilot Request"
	}

	placeholder := runOpts.ResponseBodyPlaceholder
	if placeholder == "" {
		placeholder = fmt.Sprintf("%s\n> %s\n", PRBodyPrefix, strings.ReplaceAll(a.jobAssembly.Job.ProblemStatement.Content, "\n", "\n> "))
	}

	suffix := runOpts.ResponseBodySuffix
	tip := jobutils.GetRandomTipForJob(
		a.ctx, a.ffClient,
		a.userID,
		a.jobAssembly.Job.RepoID,
		a.jobAssembly.Job.OwnerID,
		a.jobAssembly.Job.RepoOwner,
		a.jobAssembly.Job.RepoName)
	if tip != "" {
		suffix = fmt.Sprintf("%s\n\n%s\n---\n\n%s", suffix, jobutils.PullRequestBodyTipSeparator, tip)
	}

	runOpts.ResponseBodyPlaceholder = fmt.Sprintf("%s\n%s", placeholder, suffix)
	a.jobAssembly.Job.RunOptions = runOpts
}
