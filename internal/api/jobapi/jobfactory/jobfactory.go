package jobfactory

import (
	"context"
	"errors"
	"fmt"
	"strings"

	mark "github.com/github/go-mark"
	"github.com/github/sweagentd/internal/experiments"
	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/githubtwirp"
	"github.com/github/sweagentd/internal/joberrors"
	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/jobutils"
	"github.com/github/sweagentd/internal/observability"
	ps "github.com/github/sweagentd/internal/problemstatement"
	"github.com/github/sweagentd/internal/requestctx"
	sweagentdTwirp "github.com/github/sweagentd/proto/sweagentd/v1"
	gg "github.com/google/go-github/v72/github"
	"github.com/google/uuid"
)

const (
	MaxBodySuffixLengthIfBodyTooLong = 3000 // Maximum length of the body suffix for pull requests
)

type JobAssembly struct {
	Assignment *jobs.Assignment
	Job        *jobs.Job

	// TODO: Remove once V0 job factory is no longer used
	Repository *gg.Repository

	// TODO: Support data other than PRs for different type of job responses
	PullRequest *sweagentdTwirp.CreatePullRequestResponse
}

type NewJobArgs struct {
	ResponseOptions

	ProblemStatement  string                                 `json:"problem_statement"`
	ContentFilterMode jobs.ProblemStatementContentFilterMode `json:"content_filter_mode,omitempty"`

	RunName string `json:"run_name"`

	// [Optional] Information about the triggering event
	EventType        jobs.EventType `json:"event_type,omitempty"`
	EventURL         string         `json:"event_url,omitempty"`
	EventIdentifiers []string       `json:"event_identifiers,omitempty"`

	// TODO: Support other agent actions (e.g. "ask") - is always "fix" for now
	// TODO: Add support for resources to identify an existing assignment for job once supported
}

func (n *NewJobArgs) HydrateJob(job *jobs.Job) {
	problemStatement := n.ProblemStatement
	if len(problemStatement) > ps.MaxProblemStatementLength {
		problemStatement = problemStatement[:ps.MaxProblemStatementLength-3] + "..."
	}

	job.BranchName = n.PullRequest.HeadRef

	if n.EventType == "" {
		job.EventType = jobs.EventTypeApiCallReceived
	} else {
		job.EventType = n.EventType
	}
	job.EventURL = n.EventURL
	job.EventIdentifiers = n.EventIdentifiers

	// Store baseline problem statement before we modify it - needed for queuing
	job.ProblemStatement = jobs.ProblemStatement{
		Content:           problemStatement,
		ContentFilterMode: n.ContentFilterMode,
	}

	// TODO: Compress all string fields
	responseOptArgs := n.ResponseOptions
	if responseOptArgs.ResponseType == "" || responseOptArgs.ResponseType == jobs.JobResponseTypeDefault {
		responseOptArgs.ResponseType = jobs.JobResponseTypePullRequest
	}
	jobRunOpts := jobs.RunOptions{
		ResponseType: responseOptArgs.ResponseType,
	}
	if responseOptArgs.ResponseType == jobs.JobResponseTypePullRequest {
		prArgs := responseOptArgs.PullRequest
		if len(prArgs.Title) > github.MaxPullRequestTitleLength {
			prArgs.Title = prArgs.Title[:github.MaxPullRequestTitleLength-3] + "..."
		}
		jobRunOpts.ResponseTitlePlaceholder = prArgs.Title
		if len(prArgs.BodyPlaceholder)+len(prArgs.BodySuffix) > github.MaxPullRequestBodyLength {
			if len(prArgs.BodySuffix) > MaxBodySuffixLengthIfBodyTooLong {
				prArgs.BodySuffix = prArgs.BodySuffix[:MaxBodySuffixLengthIfBodyTooLong-3] + "..."
			}
			prArgs.BodyPlaceholder = prArgs.BodyPlaceholder[:github.MaxPullRequestBodyLength-len(prArgs.BodySuffix)-3] + "..."
		}
		jobRunOpts.ResponseBodyPlaceholder = prArgs.BodyPlaceholder
	}
	runName := n.RunName
	if len(runName) > github.MaxRunNameLength {
		runName = runName[:github.MaxRunNameLength-3] + "..."
	}
	jobRunOpts.RunName = runName
	job.RunOptions = jobRunOpts
}

func (n *NewJobArgs) Validate() error {
	if n.ProblemStatement == "" {
		// TODO(josebalius): We shouldn't return "bad request" from this layer
		return mark.With(mark.ErrBadRequest, errors.New("problem statement is required"))
	}

	return nil
}

type ResponseOptions struct {
	// TODO: Support response types other than pull request
	ResponseType jobs.JobResponseType    `json:"response_type,omitempty"`
	PullRequest  PullRequestResponseArgs `json:"pull_request"`
}

type PullRequestResponseArgs struct {
	Title           string   `json:"title,omitempty"`
	BodyPlaceholder string   `json:"body_placeholder,omitempty"` // Placeholder text while the
	BodySuffix      string   `json:"body_suffix,omitempty"`      // Always included at the end of the body of the PR
	BaseRef         string   `json:"base_ref,omitempty"`         // Defaults to the default branch of the repo
	HeadRef         string   `json:"head_ref,omitempty"`         // This branch will be used for the PR (but still targeting the BaseRef). If not specified a new branch will be created.
	Labels          []string `json:"labels,omitempty"`           // Labels to add to the PR
}

type JobFactory struct {
	obsv        observability.Exporters
	ffClient    featureflags.Client
	jobsStore   jobs.JobsStore
	ghTwirp     githubtwirp.ClientInterface
	experiments experiments.Client
}

func New(
	obsv observability.Exporters,
	ffClient featureflags.Client,
	jobsStore jobs.JobsStore,
	ghTwirp githubtwirp.ClientInterface,
	experiments experiments.Client) *JobFactory {
	return &JobFactory{
		obsv:        obsv,
		ffClient:    ffClient,
		jobsStore:   jobsStore,
		ghTwirp:     ghTwirp,
		experiments: experiments,
	}
}

// NewJobAssembly either drafts a new assignment and pending job or updates the job with the latest job context
func (h *JobFactory) NewJobAssembly(
	ctx context.Context, repo *gg.Repository, newJobArgs *NewJobArgs, user *requestctx.UserInfo) (*JobAssembly, error) {
	// TODO: Support looking up an existing assignment
	if err := newJobArgs.Validate(); err != nil {
		return nil, err
	}

	baseRef := newJobArgs.PullRequest.BaseRef
	if baseRef == "" {
		baseRef = fmt.Sprintf("refs/heads/%s", repo.GetDefaultBranch())
	}

	assignment := jobs.NewAssignment(repo.Owner.GetID(), *repo.ID, *repo.Owner.NodeID, *repo.NodeID, *repo.Owner.Login, *repo.Name)
	assignment, err := h.jobsStore.CreateAssignment(ctx, assignment)
	if err != nil {
		return nil, fmt.Errorf("failed to create assignment: %w", err)
	}

	// TODO: Allow the requested agent action to be passed in
	job := assignment.NewJob(jobs.AgentActionFix, user, baseRef)
	newJobArgs.HydrateJob(job)

	job, err = h.jobsStore.CreateJob(ctx, job)
	if err != nil {
		return nil, fmt.Errorf("failed to persist new pending job: %w", err)
	}

	return &JobAssembly{
		Assignment: assignment,
		Job:        job,
	}, nil
}

func (h *JobFactory) AssembleJob(ctx context.Context, gh github.ClientInterface, jobAssembly *JobAssembly, user *requestctx.UserInfo) error {
	a := NewDefaultJobContextAssembler(ctx, h.obsv, h.ffClient, gh, jobAssembly, int64(user.ID))
	return h.AssembleJobWithAssembler(ctx, gh, a, user)
}

func (h *JobFactory) AssembleJobWithAssembler(ctx context.Context, gh github.ClientInterface, a *DefaultJobContextAssembler, user *requestctx.UserInfo) error {
	ctx, span := h.obsv.Tracer.Start(ctx, "jobfactory.AssembleJob")
	defer span.End()

	// Gather any information needed to create a processed problem statement or response body contents
	jobAssembly := a.JobAssembly()
	job, assignment := jobAssembly.Job, jobAssembly.Assignment

	jobutils.UpdateRequestContextJob(ctx, h.obsv, job)
	logger := h.obsv.LoggerWithTelemetry(ctx)

	// TODO(josebalius): Wasn't this already validated in NewJobArgs.Validate?
	if job.ProblemStatement.Content == "" {
		return mark.With(mark.ErrBadRequest, errors.New("problem statement is required"))
	}

	// Start populating the job assembly
	logger.Info("formatting response content")
	a.FormatResponseContent()

	// TODO: Handle other response types other than PR here
	// TODO: Once we can queue jobs, we can reuse the PR for an existing assignment rather than always creating a new one
	if job.RunOptions.ResponseType == jobs.JobResponseTypePullRequest {
		logger.Info("creating pull request")
		if err := h.createPullRequest(ctx, gh, jobAssembly); err != nil {
			// Error here should already be of the right type and message, so no need to use fmt.Errorf
			return err
		}
	}

	// Experiments can have a perf impact on the create process, so we want to be able to control which users/repos have the
	// API path enabled separately from the rest of the job creation process that is inherently asynchronous already.
	if h.ffClient.IsEnabledForUserOrRepoOrOwner(ctx, featureflags.CopilotSWEAgentEnableExperimentsForAPI, job.RepoID, job.OwnerID, int64(user.ID)) {
		if err := h.experiments.SetExperimentsOnNewAssignment(ctx, h.jobsStore, assignment, job, jobAssembly.Job.RunOptions.ResponseLabels); err != nil {
			h.obsv.LoggerWithTelemetry(ctx).WithError(err).Error("Failed to set experiments on new assignment")
		} else {
			job.Experiments = assignment.Experiments
		}

		job.Model = jobutils.DetermineModelWithLabelNames(ctx, h.obsv, h.ffClient, h.ghTwirp, job, jobAssembly.Job.RunOptions.ResponseLabels)

		if jobutils.IsOsweExperimentEnabled(ctx, h.ffClient, job) && job.RunOptions.ResponseType == jobs.JobResponseTypePullRequest {
			if err := jobutils.UpdatePullRequestBodyWithOsweExperimentNote(ctx, gh, h.ffClient, job); err != nil {
				h.obsv.LoggerWithTelemetry(ctx).WithError(err).Error("Failed to update PR body with OSWE experiment note")
			}
		}
	}

	// Get problem statement and updated task title
	logger.Info("formatting job content for request")
	a.FormatJobContent()

	// Truncate the revised problem statement if it has exceeded max length
	if len(job.ProblemStatement.Content)+len(job.RunOptions.ResponseBodySuffix) > ps.MaxProblemStatementLength {
		job.ProblemStatement.Content = job.ProblemStatement.Content[:ps.MaxProblemStatementLength-len(job.RunOptions.ResponseBodySuffix)-3] + "..."
	}

	// Update the job with the final result
	job, err := h.jobsStore.UpdateJob(ctx, job)
	if err != nil {
		return fmt.Errorf("failed to update job: %w", err)
	}
	jobutils.UpdateRequestContextJob(ctx, h.obsv, job)
	logger = h.obsv.LoggerWithTelemetry(ctx)

	logger.Info("successfully created job with assignment for task")

	return nil
}

func (h *JobFactory) createPullRequest(ctx context.Context, gh github.ClientInterface, jobAssembly *JobAssembly) error {
	ctx, span := h.obsv.Tracer.Start(ctx, "jobfactory.createPullRequest")
	defer span.End()

	// TODO: Cleanup branch, PR on error

	logger := h.obsv.LoggerWithTelemetry(ctx)

	job := jobAssembly.Job
	assignment := jobAssembly.Assignment

	var branchName string
	// If headRef is specified, we can attempt to reuse this branch for our PR
	if job.BranchName != "" {
		// Handle passing in either an actual ref or a branch name
		if strings.HasPrefix(job.BranchName, "refs/") {
			branchName = strings.TrimPrefix(job.BranchName, "refs/heads/")
		} else {
			branchName = job.BranchName
		}

		// We currently can only use branches that start with "copilot/" and do not have an existing PR
		if !strings.HasPrefix(branchName, fmt.Sprintf("%s/", github.CopilotBranchPrefix)) {
			// TODO(josebalius): don't mark requests at this layer
			return mark.With(mark.ErrBadRequest, fmt.Errorf("cannot reuse base branch for pull request, it must start with '%s/'", github.CopilotBranchPrefix))
		}
		// See if there are any open pull requests for this branch and error if so
		pr, err := gh.GetPullRequests(ctx, job.RepoOwner, job.RepoName, &gg.PullRequestListOptions{
			State: "open",
			Head:  fmt.Sprintf("%s:%s", job.RepoOwner, branchName), // <org>:<branch name>
			Base:  job.BaseCommit,
		})
		if err != nil {
			return joberrors.MarkExistingError(err, "failed to get pull requests")
		}
		if len(pr) > 0 {
			return mark.With(mark.ErrAlreadyExists, errors.New("a pull request already exists from for this base and head ref"))
		}
		// Now add an empty commit to be sure the head ref and base ref differ or the PR create will fail
		_, err = gh.CreateEmptyCommit(ctx, job.RepoOwner, job.RepoName, branchName)
		if err != nil {
			return joberrors.MarkExistingError(err, "failed to create initial commit")
		}
		// Update the job with the branch name so it can be used in the PR
		err = h.jobsStore.PatchJob(ctx, job, jobs.JobPatch{BranchName: &branchName})
		if err != nil {
			return fmt.Errorf("failed to patch job: %w", err)
		}
		jobutils.UpdateRequestContextJob(ctx, h.obsv, job)
		logger = h.obsv.LoggerWithTelemetry(ctx)

		logger.Info("using head branch for pull request")
	} else {
		logger.Info("generating branch for pull request")
		branchSuffix := fmt.Sprintf("%s-%s", job.Action, uuid.New())
		newBranchName, err := gh.GenerateBranchName(ctx, job.RepoOwner, job.RepoName, branchSuffix)
		if err != nil {
			return joberrors.MarkExistingError(err, "failed to generate branch name")
		}
		err = jobutils.CreateBranch(ctx, h.obsv, h.ffClient, h.jobsStore, gh, jobutils.CreateBranchOpts{
			Job:            job,
			RepoOwner:      job.RepoOwner,
			RepoName:       job.RepoName,
			BaseBranchName: job.BaseCommit,
			NewBranchName:  newBranchName,
		})
		if err != nil {
			return joberrors.MarkExistingError(err, "failed to create new branch")
		}
		branchName = newBranchName
	}

	logger.Info("using twirp to create pull request")
	pr, err := h.ghTwirp.SweagentdAPI().CreatePullRequest(ctx, &sweagentdTwirp.CreatePullRequestRequest{
		RepositoryId:      uint64(job.RepoID),
		PrName:            fmt.Sprintf("[WIP] %s", job.RunOptions.ResponseTitlePlaceholder),
		PrDescription:     job.RunOptions.ResponseBodyPlaceholder,
		HeadBranchName:    branchName,
		BaseBranchName:    job.BaseCommit,
		Draft:             true,
		AttributionUserId: uint64(job.ActorID),
	})
	if err != nil {
		// We see errors with invalid UTF-8 in the API request, so mark this as something other than an internal error
		if strings.Contains(err.Error(), "string field contains invalid UTF-8") {
			return mark.With(mark.ErrBadRequest, fmt.Errorf("invalid UTF-8 characters passed into API: %w", err))
		}
		return fmt.Errorf("failed to create pull request: %w", err)
	}

	prID := int64(pr.PullRequest.Id)
	prNumber := int(pr.PullRequest.GithubNumber)

	// Save the PR details to the assignment
	logger.Info("saving pull request details on assignment")
	if err := h.jobsStore.PatchAssignment(ctx, assignment, jobs.AssignmentPatch{
		PullRequestID:     &prID,
		PullRequestNumber: &prNumber,
	}); err != nil {
		return fmt.Errorf("failed to patch assignment with PR: %w", err)
	}

	// Save the PR details to the job
	logger.Info("saving pull request details on job")
	if err := h.jobsStore.PatchJob(ctx, job, jobs.JobPatch{
		PullRequestID:     &prID,
		PullRequestNumber: &prNumber,
	}); err != nil {
		return fmt.Errorf("failed to patch job with PR: %w", err)
	}
	jobutils.UpdateRequestContextJob(ctx, h.obsv, job)
	logger = h.obsv.LoggerWithTelemetry(ctx)

	if len(job.RunOptions.ResponseLabels) > 0 {
		logger.Info("adding labels to pull request")
		err = gh.AddLabelsToIssueOrPR(ctx, job.RepoOwner, job.RepoName, job.PullRequestNumber, job.RunOptions.ResponseLabels)
		if err != nil {
			return fmt.Errorf("failed to add labels to pull request: %w", err)
		}
	}

	jobAssembly.PullRequest = pr
	return nil
}
