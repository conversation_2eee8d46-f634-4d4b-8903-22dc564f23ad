package jobapi

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	authd "github.com/github/authnd/client"
	coreTwirp "github.com/github/copilot-api/proto/twirp/core/v1"
	copilotLimitersTwirp "github.com/github/copilot-twirp/proto/limiter/v1"
	copilotUsersTwirp "github.com/github/copilot-twirp/proto/users/v1"
	"github.com/github/sweagentd/internal/capi"
	"github.com/github/sweagentd/internal/experiments"
	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/githubtwirp"
	"github.com/github/sweagentd/internal/jobexecutor"
	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/launcher"
	"github.com/github/sweagentd/internal/observability"
	"github.com/github/sweagentd/internal/requestctx"
	sweagentdTwirp "github.com/github/sweagentd/proto/sweagentd/v1"
	"github.com/go-chi/chi/v5"
	gg "github.com/google/go-github/v72/github"
	"github.com/stretchr/testify/assert"
)

const (
	sweagentdAppID = 1
	testAppID      = 2
)

func NewTestHandler(obsv observability.Exporters, cf github.ClientFactoryInterface, jobsStore jobs.JobsStore, ffClient featureflags.Client, capiClient capi.Client, ghTwirp githubtwirp.ClientInterface) (*Handler, *jobexecutor.TestJobExecutor) {
	executor := jobexecutor.NewTestJobExecutor(obsv, ffClient, jobsStore, cf, ghTwirp, capiClient)
	handler := NewHandler(obsv, cf, jobsStore, ffClient, capiClient, executor, ghTwirp, experiments.NewNoopClient(), sweagentdAppID)
	return handler, executor
}

func TestCommonAPILogic(t *testing.T) {
	ffClient := featureflags.NewNoopClient(map[string]bool{
		featureflags.CopilotSweAgentPremQuotaCheckFlagName: true,
	})
	obsv := observability.NewNoopExporters()
	cf := &github.NoopClientFactory{}
	capiClient := capi.NewNoopClientWithFixedShouldWakeResponse(false)
	jobsStore := jobs.NewMemoryStore()
	ghTwirp := &githubtwirp.NoopClient{
		NoopSweagentdAPI: &githubtwirp.NoopSweagentdAPI{
			ConfigurationForRepositoryResponse: &sweagentdTwirp.ConfigurationForRepositoryResponse{
				IsSweagentdEnabled: true,
			},
		},
		NoopUserDetailAPI: &githubtwirp.NoopUserDetailAPI{
			MockGetCopilotUser: func(ctx context.Context, req *copilotUsersTwirp.GetCopilotUserRequest) (*copilotUsersTwirp.GetCopilotUserResponse, error) {
				return &copilotUsersTwirp.GetCopilotUserResponse{
					UserDetails: &copilotUsersTwirp.CopilotUserDetails{
						AnalyticsTrackingId: "test-tracking-id",
					},
				}, nil
			},
		},
		NoopLimiterAPI: &githubtwirp.NoopLimiterAPI{
			MockGetQuotaRemaining: func(ctx context.Context, req *copilotLimitersTwirp.GetQuotaRemainingRequest) (*copilotLimitersTwirp.GetQuotaRemainingResponse, error) {
				return &copilotLimitersTwirp.GetQuotaRemainingResponse{
					QuotaDetails: []*copilotLimitersTwirp.QuotaDetails{
						{
							QuotaType:        copilotLimitersTwirp.QuotaType_QUOTA_TYPE_PREMIUM_INTERACTIONS,
							Remaining:        1,
							Entitlement:      100,
							OveragePermitted: true,
						},
					},
				}, nil
			},
		},
		NoopAuthorizationAPI: &githubtwirp.NoopAuthorizationAPI{},
	}

	runName := "Oregon Trail Problem"
	problemStatement := "You died of dysentery"
	owner := gg.User{
		ID:     gg.Ptr(int64(1)),
		NodeID: gg.Ptr("oregon-node-id"),
		Login:  gg.Ptr("oregon"),
		Type:   gg.Ptr("organization"),
		Name:   gg.Ptr("Oregon Wagon Co. Inc."),
	}
	repo := gg.Repository{
		ID:            gg.Ptr(int64(1)),
		NodeID:        gg.Ptr("trail-node-id"),
		Name:          gg.Ptr("trail"),
		Owner:         &owner,
		DefaultBranch: gg.Ptr("main"),
	}
	cf.MockRepositories = &map[string]gg.Repository{
		"oregon/trail": repo,
	}
	testJob, err := jobsStore.CreateJob(context.Background(), &jobs.Job{
		ID:        "job-1234",
		SessionID: "session-1234",
		RepoOwner: *owner.Login,
		OwnerID:   *owner.ID,
		RepoID:    *repo.ID,
		RepoName:  *repo.Name,
		Status:    jobs.JobStatusRunning,
	})
	assert.NoError(t, err)

	// Scenarios to test
	urls := map[string]map[string]string{
		"POST:/jobs/{owner}/{repo}": {
			"owner": *owner.Login,
			"repo":  *repo.Name,
		},
		"POST:/jobs/{owner}/{repo}/{job}/cancel": {
			"owner": testJob.RepoOwner,
			"repo":  testJob.RepoName,
			"job":   testJob.ID,
		},
		"POST:/jobs/{owner}/{repo}/sessions/{session}/cancel": {
			"owner":   testJob.RepoOwner,
			"repo":    testJob.RepoName,
			"session": testJob.SessionID,
		},
		"GET:/jobs/{owner}/{repo}/{job}": {
			"owner": testJob.RepoOwner,
			"repo":  testJob.RepoName,
			"job":   testJob.ID,
		},
		"GET:/jobs/{owner}/{repo}/sessions/{session}": {
			"owner":   testJob.RepoOwner,
			"repo":    testJob.RepoName,
			"session": testJob.SessionID,
		},
	}

	// For each supported url above...
	for scenario, params := range urls {
		scenarioParts := strings.Split(scenario, ":")
		method := scenarioParts[0]
		urlPath := scenarioParts[1]
		t.Run(fmt.Sprintf("%s %s", method, strings.ReplaceAll(urlPath, "/", "_")), func(t *testing.T) {
			// Test that the AuthZ flags are properly handled
			t.Run("Authz flags", func(t *testing.T) {
				ghTwirp.NoopSweagentdAPI.ConfigurationForRepositoryResponse = &sweagentdTwirp.ConfigurationForRepositoryResponse{
					IsSweagentdEnabled: true,
				}
				scenarios := map[string]map[string][]string{
					string(authd.CredentialTypeUserToServerToken): {
						featureflags.CopilotSWEAgentAPIInternalApp:   requiredAccessActionsInternal,
						featureflags.CopilotSWEAgentAPIFirstPartyApp: requiredAccessActionsFirstParty,
					},
					string(authd.CredentialTypeOauthApplicationAccessToken): {
						featureflags.CopilotSWEAgentAPIInternalApp:   requiredAccessActionsDefault, // OAuth apps can't be considered internal
						featureflags.CopilotSWEAgentAPIFirstPartyApp: requiredAccessActionsFirstParty,
					},
					string(authd.CredentialTypeLegacyPersonalAccessToken): {
						featureflags.CopilotSWEAgentAPIInternalApp:   requiredAccessActionsDefault,
						featureflags.CopilotSWEAgentAPIFirstPartyApp: requiredAccessActionsDefault,
					},
					string(authd.CredentialTypeFineGrainedPersonalAccessToken): {
						featureflags.CopilotSWEAgentAPIInternalApp:   requiredAccessActionsDefault,
						featureflags.CopilotSWEAgentAPIFirstPartyApp: requiredAccessActionsDefault,
					},
					string(authd.CredentialTypeServerToServerToken): {
						"unauthorized": {},
					},
				}
				for strType, requiredActionsByFF := range scenarios {
					for ffName, requiredActions := range requiredActionsByFF {
						ghTwirp.NoopAuthorizationAPI = &githubtwirp.NoopAuthorizationAPI{
							MockBatchControlAccess: func(ctx context.Context, req *coreTwirp.BatchControlAccessRequest) (*coreTwirp.BatchControlAccessResponse, error) {
								if ffName == "unauthorized" {
									return &coreTwirp.BatchControlAccessResponse{
										Decisions: []*coreTwirp.AuthorizationDecision{
											{
												Id:       "test-id",
												Decision: false,
											},
										},
									}, nil
								}
								if len(req.AuthorizationRequests) != len(requiredActions) {
									return nil, fmt.Errorf("test failed - error to trigger 500 response")
								}
								for i, action := range req.AuthorizationRequests {
									if action.Action != requiredActions[i] {
										return nil, fmt.Errorf("test failed - error to trigger 500 response")
									}
								}
								return &coreTwirp.BatchControlAccessResponse{
									Decisions: []*coreTwirp.AuthorizationDecision{
										{
											Id:       "test-id",
											Decision: true,
										},
									},
								}, nil
							},
						}
						ff := map[string]bool{featureflags.CopilotSWEAgentAPIRevisedAuthZ: true, ffName: true}
						ffAltClient := featureflags.NewNoopClientWithAppID(ff, testAppID)

						t.Run(fmt.Sprintf("%s %s", strType, ffName), func(t *testing.T) {
							h, _ := NewTestHandler(obsv, cf, jobsStore, ffAltClient, capiClient, ghTwirp)
							ctx := context.Background()
							if strType == string(authd.CredentialTypeLegacyPersonalAccessToken) || strType == string(authd.CredentialTypeFineGrainedPersonalAccessToken) {
								ctx = addPATToContext(t, ctx, strType)
							} else {
								ctx = addAppIDToContext(t, ctx, strType)
							}
							resp, _, err := callHandler(t, ctx, h, method, urlPath, params, NewJobAPIRequestV0HeaderValue, problemStatement, runName)
							assert.NoError(t, err)
							if ffName == "unauthorized" {
								assert.Equal(t, http.StatusUnauthorized, resp.StatusCode)
							} else if method == http.MethodPost {
								assert.Equal(t, http.StatusCreated, resp.StatusCode)
							} else if method == http.MethodDelete {
								assert.Equal(t, http.StatusNoContent, resp.StatusCode)
							}
							assert.NoError(t, err)
						})
					}
				}
			})

			// Test that, even if authorized from the token, users can't push unless their user really does have push rights
			t.Run("user with no push rights", func(t *testing.T) {
				ffAltClient := featureflags.NewNoopClientWithAppID(map[string]bool{
					featureflags.CopilotSWEAgentAPIRevisedAuthZ: true,
				}, testAppID)
				ghTwirp.NoopAuthorizationAPI = &githubtwirp.NoopAuthorizationAPI{}
				ctx := addUserWithoutRepoPushAccessToContext(t, context.Background())
				altH, _ := NewTestHandler(obsv, cf, jobsStore, ffAltClient, capiClient, ghTwirp)
				resp, _, err := callHandler(t, ctx, altH, method, urlPath, params, NewJobAPIRequestV0HeaderValue, problemStatement, runName)
				assert.NoError(t, err)
				assert.Equal(t, http.StatusUnauthorized, resp.StatusCode)
			})

			// Test that disallowed users or apps are in fact disallowed
			t.Run("disallowed user", func(t *testing.T) {
				testApi := func(ffAltClient featureflags.Client) {
					ghTwirp.NoopAuthorizationAPI = &githubtwirp.NoopAuthorizationAPI{}
					ctx := addUserToContext(t, context.Background())
					altH, _ := NewTestHandler(obsv, cf, jobsStore, ffAltClient, capiClient, ghTwirp)
					resp, _, err := callHandler(t, ctx, altH, method, urlPath, params, NewJobAPIRequestV0HeaderValue, problemStatement, runName)
					assert.NoError(t, err)
					assert.Equal(t, http.StatusNotFound, resp.StatusCode)
				}
				// Test user
				testApi(featureflags.NewNoopClient(map[string]bool{
					featureflags.CopilotSWEAgentAPIDisallow: true,
				}))
				// Test app
				testApi(featureflags.NewNoopClientWithAppID(map[string]bool{
					featureflags.CopilotSWEAgentAPIDisallow: true,
				}, testAppID))
			})

			t.Run("invalid API version", func(t *testing.T) {
				ghTwirp.NoopAuthorizationAPI = &githubtwirp.NoopAuthorizationAPI{}
				ctx := addUserToContext(t, context.Background())
				h, _ := NewTestHandler(obsv, cf, jobsStore, ffClient, capiClient, ghTwirp)
				resp, w, err := callHandler(t, ctx, h, method, urlPath, params, "some-invalid-version", problemStatement, runName)
				assert.NoError(t, err)
				assert.Equal(t, http.StatusBadRequest, resp.StatusCode)
				assert.Contains(t, w.Body.String(), "invalid API version")
			})
		})
	}
}

func TestCreateJob(t *testing.T) {
	ffClient := featureflags.NewNoopClient(map[string]bool{
		featureflags.CopilotSweAgentPremQuotaCheckFlagName: true,
	})
	obsv := observability.NewNoopExporters()
	cf := &github.NoopClientFactory{}
	capiClient := capi.NewNoopClientWithFixedShouldWakeResponse(false)
	jobsStore := jobs.NewMemoryStore()
	ghTwirp := &githubtwirp.NoopClient{
		NoopSweagentdAPI: &githubtwirp.NoopSweagentdAPI{
			ConfigurationForRepositoryResponse: &sweagentdTwirp.ConfigurationForRepositoryResponse{
				IsSweagentdEnabled: true,
			},
		},
		NoopUserDetailAPI: &githubtwirp.NoopUserDetailAPI{
			MockGetCopilotUser: func(ctx context.Context, req *copilotUsersTwirp.GetCopilotUserRequest) (*copilotUsersTwirp.GetCopilotUserResponse, error) {
				return &copilotUsersTwirp.GetCopilotUserResponse{
					UserDetails: &copilotUsersTwirp.CopilotUserDetails{
						AnalyticsTrackingId: "test-tracking-id",
					},
				}, nil
			},
		},
		NoopLimiterAPI: &githubtwirp.NoopLimiterAPI{
			MockGetQuotaRemaining: func(ctx context.Context, req *copilotLimitersTwirp.GetQuotaRemainingRequest) (*copilotLimitersTwirp.GetQuotaRemainingResponse, error) {
				return &copilotLimitersTwirp.GetQuotaRemainingResponse{
					QuotaDetails: []*copilotLimitersTwirp.QuotaDetails{
						{
							QuotaType:        copilotLimitersTwirp.QuotaType_QUOTA_TYPE_PREMIUM_INTERACTIONS,
							Remaining:        1,
							Entitlement:      100,
							OveragePermitted: true,
						},
					},
				}, nil
			},
		},
		NoopAuthorizationAPI: &githubtwirp.NoopAuthorizationAPI{},
	}

	h, _ := NewTestHandler(obsv, cf, jobsStore, ffClient, capiClient, ghTwirp)

	runName := "Oregon Trail Problem"
	problemStatement := "You died of dysentery"
	owner := gg.User{
		ID:     gg.Ptr(int64(1)),
		NodeID: gg.Ptr("oregon-node-id"),
		Login:  gg.Ptr("oregon"),
		Type:   gg.Ptr("organization"),
		Name:   gg.Ptr("Oregon Wagon Co. Inc."),
	}
	repo := gg.Repository{
		ID:            gg.Ptr(int64(1)),
		NodeID:        gg.Ptr("trail-node-id"),
		Name:          gg.Ptr("trail"),
		Owner:         &owner,
		DefaultBranch: gg.Ptr("main"),
	}
	cf.MockRepositories = &map[string]gg.Repository{
		"oregon/trail": repo,
	}

	urlPath := "/jobs/{owner}/{repo}"
	urlParams := map[string]string{"owner": *owner.Login, "repo": *repo.Name}

	t.Run("create new job for API v0, 2025-06-19", func(t *testing.T) {
		ghTwirp.NoopAuthorizationAPI = &githubtwirp.NoopAuthorizationAPI{}
		ctx := addUserToContext(t, context.Background())
		resp, w, err := callHandler(t, ctx, h, http.MethodPost, urlPath, urlParams, NewJobAPIRequestV0HeaderValue, problemStatement, runName)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusCreated, resp.StatusCode)
		assert.NotEmpty(t, resp.Body)
		result := &BaseJobResponse{}
		err = json.NewDecoder(w.Body).Decode(&result)
		assert.NoError(t, err)
		assert.NotEmpty(t, result.ID)
		assert.NotNil(t, result.PullRequest)
		assert.Equal(t, NewJobAPIRequestV0HeaderValue, resp.Header.Get("X-GitHub-Api-Version"))
	})

	// Most of the tests here set the header to test the functionality w/o worrying about versioning, this tests defaulting
	t.Run("create new job using default API version", func(t *testing.T) {
		ghTwirp.NoopAuthorizationAPI = &githubtwirp.NoopAuthorizationAPI{}
		ctx := addUserToContext(t, context.Background())
		resp, w, err := callHandler(t, ctx, h, http.MethodPost, urlPath, urlParams, "", problemStatement, runName)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusCreated, resp.StatusCode)
		assert.NotEmpty(t, resp.Body)
		result := &BaseJobResponse{}
		err = json.NewDecoder(w.Body).Decode(&result)
		assert.NoError(t, err)
		assert.NotEmpty(t, result.ID)
		assert.NotNil(t, result.PullRequest)
		assert.Equal(t, NewJobAPIRequestDefaultVersion, resp.Header.Get("X-GitHub-Api-Version"))
	})

	t.Run("Handle 4xx and 5xx errors", func(t *testing.T) {
		ghTwirp.NoopAuthorizationAPI = &githubtwirp.NoopAuthorizationAPI{}
		callApi := func(h *Handler) (*http.Response, *httptest.ResponseRecorder) {
			ctx := addUserToContext(t, context.Background())
			resp, w, err := callHandler(t, ctx, h, http.MethodPost, urlPath, urlParams, NewJobAPIRequestV0HeaderValue, problemStatement, runName)
			assert.NoError(t, err)
			return resp, w
		}
		// Simulate a bad argument error
		cfTest := &github.NoopClientFactory{}
		cfTest.MockGetRepository = func(ctx context.Context, owner, repo string) (*gg.Repository, error) {
			resp := gg.Response{Response: &http.Response{
				StatusCode: http.StatusBadRequest,
			}}
			return nil, github.NewGitHubAPIError(&resp, "failed to get required information", fmt.Errorf("invalid repository"))
		}
		h1, _ := NewTestHandler(obsv, cfTest, jobsStore, ffClient, capiClient, ghTwirp)
		resp1, w1 := callApi(h1)
		assert.Equal(t, http.StatusBadRequest, resp1.StatusCode)
		assert.NotEmpty(t, resp1.Body)
		result1 := make(map[string]interface{})
		err1 := json.NewDecoder(w1.Body).Decode(&result1)
		assert.NoError(t, err1)
		assert.Equal(t, "invalid repository: failed to get required information: invalid repository: bad request", result1["message"])

		// Simulate internal error
		ghTwirpAlt := &githubtwirp.NoopClient{
			NoopSweagentdAPI: &githubtwirp.NoopSweagentdAPI{
				ConfigurationForRepositoryResponse: &sweagentdTwirp.ConfigurationForRepositoryResponse{
					IsSweagentdEnabled: true,
				},
				MockCreatePullRequest: func(ctx context.Context, req *sweagentdTwirp.CreatePullRequestRequest) (*sweagentdTwirp.CreatePullRequestResponse, error) {
					return nil, fmt.Errorf("internal error")
				},
			},
			NoopLimiterAPI:       ghTwirp.NoopLimiterAPI,
			NoopUserDetailAPI:    ghTwirp.NoopUserDetailAPI,
			NoopAuthorizationAPI: &githubtwirp.NoopAuthorizationAPI{},
		}
		h2, _ := NewTestHandler(obsv, cf, jobsStore, ffClient, capiClient, ghTwirpAlt)
		resp2, w2 := callApi(h2)
		assert.Equal(t, http.StatusInternalServerError, resp2.StatusCode)
		assert.NotEmpty(t, resp2.Body)
		result2 := make(map[string]interface{})
		err2 := json.NewDecoder(w2.Body).Decode(&result2)
		assert.NoError(t, err2)
		assert.Equal(t, "failed to assemble job: failed to create pull request: internal error", result2["message"])
	})

	// Only applies to create scenario
	t.Run("insufficient premium quota", func(t *testing.T) {
		ghTwirp.NoopAuthorizationAPI = &githubtwirp.NoopAuthorizationAPI{}
		ffAltClient := featureflags.NewNoopClient(map[string]bool{
			featureflags.CopilotSWEAgentAPIPATFlagName:         true,
			featureflags.CopilotSweAgentPremQuotaCheckFlagName: true,
		})
		ghTwirp.NoopSweagentdAPI.ConfigurationForRepositoryResponse = &sweagentdTwirp.ConfigurationForRepositoryResponse{
			IsSweagentdEnabled: true,
		}
		ghTwirp.NoopUserDetailAPI.MockGetCopilotUser = func(ctx context.Context, req *copilotUsersTwirp.GetCopilotUserRequest) (*copilotUsersTwirp.GetCopilotUserResponse, error) {
			return &copilotUsersTwirp.GetCopilotUserResponse{
				UserDetails: &copilotUsersTwirp.CopilotUserDetails{
					AnalyticsTrackingId: "test-tracking-id",
				},
			}, nil
		}
		ghTwirp.NoopLimiterAPI.MockGetQuotaRemaining = func(ctx context.Context, req *copilotLimitersTwirp.GetQuotaRemainingRequest) (*copilotLimitersTwirp.GetQuotaRemainingResponse, error) {
			return &copilotLimitersTwirp.GetQuotaRemainingResponse{
				QuotaDetails: []*copilotLimitersTwirp.QuotaDetails{
					{
						QuotaType:        copilotLimitersTwirp.QuotaType_QUOTA_TYPE_PREMIUM_INTERACTIONS,
						Remaining:        0,
						Entitlement:      100,
						OveragePermitted: false,
					},
				},
			}, nil
		}
		altH, _ := NewTestHandler(obsv, cf, jobsStore, ffAltClient, capiClient, ghTwirp)
		ctx := addUserToContext(t, context.Background())
		resp, _, err := callHandler(t, ctx, altH, http.MethodPost, urlPath, urlParams, NewJobAPIRequestV0HeaderValue, problemStatement, runName)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusPaymentRequired, resp.StatusCode)
		assert.NoError(t, err)
	})
}

func TestCancelJob(t *testing.T) {
	ffClient := featureflags.NewNoopClient(map[string]bool{
		featureflags.CopilotSweAgentPremQuotaCheckFlagName: true,
	})
	obsv := observability.NewNoopExporters()
	cf := &github.NoopClientFactory{}
	capiClient := capi.NewNoopClientWithFixedShouldWakeResponse(false)
	jobsStore := jobs.NewMemoryStore()
	ghTwirp := &githubtwirp.NoopClient{
		NoopSweagentdAPI: &githubtwirp.NoopSweagentdAPI{
			ConfigurationForRepositoryResponse: &sweagentdTwirp.ConfigurationForRepositoryResponse{
				IsSweagentdEnabled: true,
			},
		},
		NoopUserDetailAPI: &githubtwirp.NoopUserDetailAPI{
			MockGetCopilotUser: func(ctx context.Context, req *copilotUsersTwirp.GetCopilotUserRequest) (*copilotUsersTwirp.GetCopilotUserResponse, error) {
				return &copilotUsersTwirp.GetCopilotUserResponse{
					UserDetails: &copilotUsersTwirp.CopilotUserDetails{
						AnalyticsTrackingId: "test-tracking-id",
					},
				}, nil
			},
		},
		NoopLimiterAPI: &githubtwirp.NoopLimiterAPI{
			MockGetQuotaRemaining: func(ctx context.Context, req *copilotLimitersTwirp.GetQuotaRemainingRequest) (*copilotLimitersTwirp.GetQuotaRemainingResponse, error) {
				return &copilotLimitersTwirp.GetQuotaRemainingResponse{
					QuotaDetails: []*copilotLimitersTwirp.QuotaDetails{
						{
							QuotaType:        copilotLimitersTwirp.QuotaType_QUOTA_TYPE_PREMIUM_INTERACTIONS,
							Remaining:        1,
							Entitlement:      100,
							OveragePermitted: true,
						},
					},
				}, nil
			},
		},
		NoopAuthorizationAPI: &githubtwirp.NoopAuthorizationAPI{},
	}

	h, executor := NewTestHandler(obsv, cf, jobsStore, ffClient, capiClient, ghTwirp)

	runName := "Oregon Trail Problem"
	problemStatement := "You died of dysentery"
	owner := gg.User{
		ID:     gg.Ptr(int64(1)),
		NodeID: gg.Ptr("oregon-node-id"),
		Login:  gg.Ptr("oregon"),
		Type:   gg.Ptr("organization"),
		Name:   gg.Ptr("Oregon Wagon Co. Inc."),
	}
	repo := gg.Repository{
		ID:            gg.Ptr(int64(1)),
		NodeID:        gg.Ptr("trail-node-id"),
		Name:          gg.Ptr("trail"),
		Owner:         &owner,
		DefaultBranch: gg.Ptr("main"),
	}
	cf.MockRepositories = &map[string]gg.Repository{
		"oregon/trail": repo,
	}

	testJob, _ := jobsStore.CreateJob(context.Background(), &jobs.Job{
		ID:        "job-1234",
		SessionID: "session-1234",
		RepoOwner: *owner.Login,
		OwnerID:   *owner.ID,
		RepoID:    *repo.ID,
		RepoName:  *repo.Name,
		ComputeID: "test-compute-id",
		Status:    jobs.JobStatusRunning,
	})

	urlParams := map[string]string{"owner": testJob.RepoOwner, "repo": testJob.RepoName, "job": testJob.ID, "session": testJob.SessionID}
	for _, urlPath := range []string{
		"/jobs/{owner}/{repo}/{job}",
		"/jobs/{owner}/{repo}/sessions/{session}",
	} {
		t.Run(fmt.Sprintf("cancel job via %s", urlPath), func(t *testing.T) {
			executor.Launcher.TerminateAgentCalls = []launcher.TerminateAgentOpts{}
			ctx := addUserToContext(t, context.Background())
			resp, _, err := callHandler(t, ctx, h, http.MethodDelete, urlPath, urlParams, NewJobAPIRequestV0HeaderValue, problemStatement, runName)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusNoContent, resp.StatusCode)
			assert.Equal(t, NewJobAPIRequestV0HeaderValue, resp.Header.Get("X-GitHub-Api-Version"))
			assert.Equal(t, 1, len(executor.TerminateAgentCalls()))
			terminateCallOpts := executor.TerminateAgentCalls()[0]
			assert.Equal(t, testJob.RepoID, terminateCallOpts.RepoID)
			assert.Equal(t, testJob.OwnerID, terminateCallOpts.OwnerID)
			assert.Equal(t, testJob.RepoName, terminateCallOpts.RepoName)
			assert.Equal(t, testJob.RepoOwner, terminateCallOpts.RepoOwner)
			assert.Equal(t, testJob.ComputeID, terminateCallOpts.ComputeID)
		})
	}
}

func TestGetJob(t *testing.T) {
	ffClient := featureflags.NewNoopClient(map[string]bool{
		featureflags.CopilotSweAgentPremQuotaCheckFlagName: true,
	})
	obsv := observability.NewNoopExporters()
	cf := &github.NoopClientFactory{}
	capiClient := capi.NewNoopClientWithFixedShouldWakeResponse(false)
	jobsStore := jobs.NewMemoryStore()
	ghTwirp := &githubtwirp.NoopClient{
		NoopSweagentdAPI: &githubtwirp.NoopSweagentdAPI{
			ConfigurationForRepositoryResponse: &sweagentdTwirp.ConfigurationForRepositoryResponse{
				IsSweagentdEnabled: true,
			},
		},
		NoopUserDetailAPI: &githubtwirp.NoopUserDetailAPI{
			MockGetCopilotUser: func(ctx context.Context, req *copilotUsersTwirp.GetCopilotUserRequest) (*copilotUsersTwirp.GetCopilotUserResponse, error) {
				return &copilotUsersTwirp.GetCopilotUserResponse{
					UserDetails: &copilotUsersTwirp.CopilotUserDetails{
						AnalyticsTrackingId: "test-tracking-id",
					},
				}, nil
			},
		},
		NoopLimiterAPI: &githubtwirp.NoopLimiterAPI{
			MockGetQuotaRemaining: func(ctx context.Context, req *copilotLimitersTwirp.GetQuotaRemainingRequest) (*copilotLimitersTwirp.GetQuotaRemainingResponse, error) {
				return &copilotLimitersTwirp.GetQuotaRemainingResponse{
					QuotaDetails: []*copilotLimitersTwirp.QuotaDetails{
						{
							QuotaType:        copilotLimitersTwirp.QuotaType_QUOTA_TYPE_PREMIUM_INTERACTIONS,
							Remaining:        1,
							Entitlement:      100,
							OveragePermitted: true,
						},
					},
				}, nil
			},
		},
		NoopAuthorizationAPI: &githubtwirp.NoopAuthorizationAPI{},
	}

	h, executor := NewTestHandler(obsv, cf, jobsStore, ffClient, capiClient, ghTwirp)

	runName := "Oregon Trail Problem"
	problemStatement := "You died of dysentery"
	owner := gg.User{
		ID:     gg.Ptr(int64(1)),
		NodeID: gg.Ptr("oregon-node-id"),
		Login:  gg.Ptr("oregon"),
		Type:   gg.Ptr("organization"),
		Name:   gg.Ptr("Oregon Wagon Co. Inc."),
	}
	repo := gg.Repository{
		ID:            gg.Ptr(int64(1)),
		NodeID:        gg.Ptr("trail-node-id"),
		Name:          gg.Ptr("trail"),
		Owner:         &owner,
		DefaultBranch: gg.Ptr("main"),
	}
	cf.MockRepositories = &map[string]gg.Repository{
		"oregon/trail": repo,
	}
	pr := &gg.PullRequest{
		ID:     gg.Ptr(int64(1)),
		NodeID: gg.Ptr("pull-request-node-id"),
		Title:  gg.Ptr("Pull Request Title"),
		Body:   gg.Ptr("Pull Request Body"),
		Number: gg.Ptr(1),
		Base: &gg.PullRequestBranch{
			SHA: gg.Ptr("base-sha"),
		},
		Head: &gg.PullRequestBranch{
			Ref: gg.Ptr("head-ref"),
		},
	}
	cf.MockPullRequest = pr
	workflowRun := gg.WorkflowRun{
		ID:         gg.Ptr(int64(12345678)),
		NodeID:     gg.Ptr("workflow-run-node-id"),
		Status:     gg.Ptr("completed"),
		HeadSHA:    gg.Ptr("head-sha"),
		HeadBranch: gg.Ptr("head-branch"),
		Name:       gg.Ptr("test-workflow"),
		Repository: &repo,
	}
	cf.MockWorkflowRun = &workflowRun

	testJob, _ := jobsStore.CreateJob(context.Background(), &jobs.Job{
		ID:                "job-1234",
		SessionID:         "session-1234",
		RepoOwner:         *owner.Login,
		OwnerID:           *owner.ID,
		RepoID:            *repo.ID,
		RepoName:          *repo.Name,
		ComputeID:         fmt.Sprintf("%d", *workflowRun.ID),
		PullRequestID:     *pr.ID,
		PullRequestNumber: *pr.Number,
		ActorID:           2,
		ActorLogin:        "test-user",
		Status:            jobs.JobStatusRunning,
		ProblemStatement: jobs.ProblemStatement{
			Content:           problemStatement,
			ContentFilterMode: jobs.ProblemStatementContentFilterModeMarkdown,
		},
	})

	urlParams := map[string]string{"owner": testJob.RepoOwner, "repo": testJob.RepoName, "job": testJob.ID, "session": testJob.SessionID}
	for _, urlPath := range []string{
		"/jobs/{owner}/{repo}/{job}",
		"/jobs/{owner}/{repo}/sessions/{session}",
	} {
		t.Run(fmt.Sprintf("get job via %s", strings.ReplaceAll(urlPath, "/", "_")), func(t *testing.T) {
			executor.Launcher.TerminateAgentCalls = []launcher.TerminateAgentOpts{}
			ctx := addUserToContext(t, context.Background())
			resp, w, err := callHandler(t, ctx, h, http.MethodGet, urlPath, urlParams, NewJobAPIRequestV0HeaderValue, problemStatement, runName)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusOK, resp.StatusCode)
			assert.NotEmpty(t, resp.Body)
			result := &FullJobResponse{}
			err = json.NewDecoder(w.Body).Decode(&result)
			assert.NoError(t, err)
			assert.NotEmpty(t, result.ID)
			assert.Nil(t, result.Error)
			assert.Equal(t, NewJobAPIRequestDefaultVersion, resp.Header.Get("X-GitHub-Api-Version"))
			assert.Equal(t, testJob.SessionID, result.SessionID)
			assert.Equal(t, testJob.ActorID, *result.Actor.ID)
			assert.Equal(t, testJob.ActorLogin, *result.Actor.Login)
			assert.Equal(t, jobs.JobStatusRunning, result.Status)
			assert.Equal(t, testJob.PullRequestID, *result.PullRequest.ID)
			assert.Equal(t, testJob.PullRequestNumber, *result.PullRequest.Number)
			assert.Equal(t, testJob.ComputeID, fmt.Sprintf("%d", *result.WorkflowRun.ID))
			assert.Equal(t, testJob.PullRequestNumber, *result.PullRequest.Number)
			assert.Equal(t, testJob.ProblemStatement.Content, result.ProblemStatement)
			assert.Equal(t, testJob.ProblemStatement.ContentFilterMode, result.ContentFilterMode)
			// Since this API does not return details, verify that we're not outputting the PR title and run status
			assert.Empty(t, result.PullRequest.GetTitle())
			assert.Empty(t, result.WorkflowRun.GetStatus())
		})
	}
}

func addUserToContext(t *testing.T, ctx context.Context) context.Context {
	t.Helper()
	return requestctx.AddData(ctx, &requestctx.CtxData{
		UserInfo: &requestctx.UserInfo{
			ID:         1,
			Login:      "user-login",
			TrackingID: "tracking-id",
			TokenInfo: &requestctx.TokenInfo{
				Type:          string(authd.CredentialTypeUserToServerToken),
				ApplicationID: 1,
				Token:         "test-token",
			},
		},
	})
}

func addUserWithoutRepoPushAccessToContext(t *testing.T, ctx context.Context) context.Context {
	t.Helper()
	return requestctx.AddData(ctx, &requestctx.CtxData{
		UserInfo: &requestctx.UserInfo{
			ID:         1,
			Login:      "user-login-with-access-level-none", // Users ending in "-none" are treated as having no privs in github.NoopClient
			TrackingID: "tracking-id",
			TokenInfo: &requestctx.TokenInfo{
				Type:          string(authd.CredentialTypeUserToServerToken),
				ApplicationID: 1,
				Token:         "test-token",
			},
		},
	})
}

func addPATToContext(t *testing.T, ctx context.Context, strType string) context.Context {
	t.Helper()
	return requestctx.AddData(ctx, &requestctx.CtxData{
		UserInfo: &requestctx.UserInfo{
			ID:         1,
			Login:      "user-login",
			TrackingID: "tracking-id",
			TokenInfo: &requestctx.TokenInfo{
				Type:  strType,
				Token: "test-token",
			},
		},
	})
}

func addAppIDToContext(t *testing.T, ctx context.Context, strType string) context.Context {
	t.Helper()
	return requestctx.AddData(ctx, &requestctx.CtxData{
		UserInfo: &requestctx.UserInfo{
			ID:         1,
			Login:      "user-login",
			TrackingID: "tracking-id",
			TokenInfo: &requestctx.TokenInfo{
				Type:          strType,
				ApplicationID: testAppID,
				Token:         "test-token",
			},
		},
	})
}

func callHandler(t *testing.T, ctx context.Context, h *Handler, method, urlPath string, urlParams map[string]string, apiVersion, problemStatement, runName string) (*http.Response, *httptest.ResponseRecorder, error) {
	t.Helper()
	var err error
	reqBytes := []byte{}
	if method == http.MethodPost {
		requestBody := &NewJobAPIRequestV0{}
		requestBody.ProblemStatement = problemStatement
		requestBody.RunName = runName
		reqBytes, err = json.Marshal(requestBody)
		if err != nil {
			return nil, nil, err
		}
	}
	req := httptest.NewRequestWithContext(ctx, method, urlPath, bytes.NewReader(reqBytes))
	if apiVersion != "" {
		req.Header.Set("X-GitHub-Api-Version", apiVersion)
	}
	rctx := chi.NewRouteContext()
	for k, v := range urlParams {
		rctx.URLParams.Add(k, v)
	}
	req = req.WithContext(context.WithValue(req.Context(), chi.RouteCtxKey, rctx))
	w := httptest.NewRecorder()
	switch method {
	case http.MethodPost:
		h.HandleNewJob(w, req)
	case http.MethodDelete:
		h.HandleCancelJob(w, req)
	case http.MethodGet:
		h.HandleGetJobByIDOrSessionID(w, req)
	default:
		return nil, nil, fmt.Errorf("unsupported method %s", method)
	}
	resp := w.Result()
	return resp, w, nil
}
