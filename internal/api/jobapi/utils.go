package jobapi

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore"
	authd "github.com/github/authnd/client"
	"github.com/github/github-telemetry-go/log"
	mark "github.com/github/go-mark"
	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/githubtwirp"
	"github.com/github/sweagentd/internal/joberrors"
	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/requestctx"
	gg "github.com/google/go-github/v72/github"
)

var (
	allowedAPICredentialTypes       = []authd.CredentialType{authd.CredentialTypeOauthApplicationAccessToken, authd.CredentialTypeUserToServerToken, authd.CredentialTypeFineGrainedPersonalAccessToken, authd.CredentialTypeLegacyPersonalAccessToken}
	requiredAccessActionsDefault    = []string{"pull", "push", "create_pull_request", "write_actions"}
	requiredAccessActionsInternal   = []string{"pull"}
	requiredAccessActionsFirstParty = []string{"pull", "push", "create_pull_request"}
)

// BaseJobResponse values that are always present - though SessionID may be "" in development
type BaseJobResponse struct {
	ID          string          `json:"job_id"`
	SessionID   string          `json:"session_id"`
	Status      jobs.JobStatus  `json:"status"`
	Actor       *gg.User        `json:"actor"`
	CreatedAt   gg.Timestamp    `json:"created_at"`
	UpdatedAt   gg.Timestamp    `json:"updated_at"`
	PullRequest *gg.PullRequest `json:"pull_request,omitempty"`
}

// FullJobResponse is the base reponse + extended properties returned from GET / PUT endpoint, but not from create
type FullJobResponse struct {
	BaseJobResponse
	ProblemStatement  string                                 `json:"problem_statement,omitempty"`
	ContentFilterMode jobs.ProblemStatementContentFilterMode `json:"content_filter_mode,omitempty"`
	WorkflowRun       *gg.WorkflowRun                        `json:"workflow_run,omitempty"`
	Error             *jobs.JobError                         `json:"error,omitempty"`
	EventType         jobs.EventType                         `json:"event_type,omitempty"`
	EventURL          string                                 `json:"event_url,omitempty"`
	EventIdentifiers  []string                               `json:"event_identifiers,omitempty"`
}

// Hydrates base job details for the passed in BaseJobResponse object
func (h *Handler) hydrateBaseJobResponse(ctx context.Context, gh github.ClientInterface, job *jobs.Job, baseJobResponse *BaseJobResponse, verbose bool) error {
	var err error
	actor := &gg.User{
		ID:    &job.ActorID,
		Login: &job.ActorLogin,
	}
	if verbose {
		actor, err = gh.GetUserById(ctx, uint64(job.ActorID))
		if err != nil {
			return fmt.Errorf("failed to get actor by ID: %d", job.ActorID)
		}
	}

	// Adapt to the two types of job details we support
	baseJobResponse.ID = job.ID
	baseJobResponse.SessionID = job.SessionID
	baseJobResponse.CreatedAt = gg.Timestamp{Time: time.Unix(job.CreatedAt, 0)}
	baseJobResponse.UpdatedAt = gg.Timestamp{Time: time.Unix(job.UpdatedAt, 0)}
	baseJobResponse.Status = job.Status
	baseJobResponse.Actor = actor

	// If a pull request is set on the job, fetch the PR details and add it
	if job.PullRequestID != 0 {
		baseJobResponse.PullRequest = &gg.PullRequest{
			ID:     &job.PullRequestID,
			Number: &job.PullRequestNumber,
		}
		if verbose {
			// On error we'll just include the PR info already set
			pr, err := gh.GetPullRequest(ctx, job.RepoID, job.PullRequestNumber)
			if err == nil {
				baseJobResponse.PullRequest = pr
			}
		}
	}

	return nil
}

// Hydrates full job details for the passed in FullJobResponse object
func (h *Handler) hydrateFullJobResponse(ctx context.Context, gh github.ClientInterface, job *jobs.Job, fullJobResponse *FullJobResponse, verbose bool) error {
	if fullJobResponse == nil {
		return errors.New("fullJobResponse cannot be nil")
	}

	err := h.hydrateBaseJobResponse(ctx, gh, job, &fullJobResponse.BaseJobResponse, verbose)
	if err != nil {
		return fmt.Errorf("failed to hydrate base job response: %w", err)
	}

	fullJobResponse.ProblemStatement = job.ProblemStatement.Content
	fullJobResponse.ContentFilterMode = job.ProblemStatement.ContentFilterMode
	fullJobResponse.Error = job.Error
	fullJobResponse.EventType = job.EventType
	fullJobResponse.EventURL = job.EventURL
	fullJobResponse.EventIdentifiers = job.EventIdentifiers
	// If a compute ID is set and includeWorkflowRun is true, fetch the run details
	if job.ComputeID != "" {
		runID, err := strconv.ParseInt(job.ComputeID, 10, 64)
		if err != nil {
			return fmt.Errorf("invalid compute ID: %s", job.ComputeID)
		}
		fullJobResponse.WorkflowRun = &gg.WorkflowRun{
			ID: &runID,
		}
		if verbose {
			// On error we'll just include the run ID already set
			run, err := gh.GetWorkflowRunByID(ctx, job.RepoOwner, job.RepoName, runID)
			if err == nil {
				fullJobResponse.WorkflowRun = run
			}
		}
	}

	return nil
}

func (h *Handler) errorResponseWithError(ctx context.Context, w http.ResponseWriter, logger log.Logger, err error, message string) error {
	// Azure Cosmos DB errors are far too verbose to bubble up and can, so log it but only add info on the status code to the actual text
	statusCode := 0
	var azErr *azcore.ResponseError
	if errors.As(err, &azErr) {
		statusCode = azErr.StatusCode
		if message != "" {
			err = fmt.Errorf("%s: %s", message, strings.ToLower(http.StatusText(azErr.StatusCode)))
		} else {
			err = errors.New(strings.ToLower(http.StatusText(azErr.StatusCode)))
		}
	} else {
		err = joberrors.MarkExistingError(err, message)
		statusCode = joberrors.StatusCodeFromMarkedError(err)
	}

	// Unmarked errors will be treated as internal server errors
	if statusCode < 500 {
		// Don't report 4xx status codes since they are likely user or transient errors, but log them.
		logger.WithError(err).Error(message)
	} else {
		h.obsv.LogAndReportError(ctx, err, message)
	}

	return h.errorResponse(w, err.Error(), statusCode)
}

func (h *Handler) errorResponse(w http.ResponseWriter, message string, code int) error {
	errResponse := gg.ErrorResponse{
		Message: message,
	}

	json, err := json.Marshal(errResponse)
	if err != nil {
		return err
	}
	http.Error(w, string(json), code)
	return nil
}

func (h *Handler) verifyTokenAccess(ctx context.Context, user *requestctx.UserInfo, repoOwner, repoName string, requiredAccessActions []string) (bool, error) {
	rules := make([]*githubtwirp.HasAccessCheck, len(requiredAccessActions))
	nwo := fmt.Sprintf("%s/%s", repoOwner, repoName)
	for i, action := range requiredAccessActions {
		rules[i] = &githubtwirp.HasAccessCheck{
			Action:        action,
			ResourceType:  "Repository",
			RepositoryNWO: nwo,
		}
	}
	return githubtwirp.TokenHasAccess(ctx, h.ghTwirp, user, rules)
}

// Returns a response code based on authorization result and a related error if applicable.
// Does *NOT* check whether Copilot coding agent is enabled for the repo for the user has quota.
//
// If authorization fails, a error with a go-mark will be returned:
//
//	mark.ErrNotFound - User is on the disallow list, so we obfuscate the fact that they are blocked - should report and log
//	mark.ErrUnauthorized - user is not authorized to call this endpoint or does not have push rights to the repo
//	mark.ErrForbidden - user is authorized to call this endpoint, but the user or repository does not have the Coding Agent enabled.
func (h *Handler) authorizeUser(ctx context.Context, logger log.Logger, repoOwner, repoName string, user *requestctx.UserInfo) error {
	// See if the user or app has been blocked
	requestAppID := int64(user.TokenInfo.ApplicationID)
	if h.ffClient.IsEnabledForUser(ctx, featureflags.CopilotSWEAgentAPIDisallow, int64(user.ID)) ||
		h.ffClient.IsEnabledForGitHubApp(ctx, featureflags.CopilotSWEAgentAPIDisallow, requestAppID) ||
		h.ffClient.IsEnabledForOAuthApp(ctx, featureflags.CopilotSWEAgentAPIDisallow, requestAppID) {
		return mark.With(mark.ErrNotFound, errors.New("user or app is not allowed to use the API")) // Use 404 to obfuscate the fact that the user or app is blocked
	}

	// Verify token has appropriate privs
	var requiredAccessActions []string
	credentialType := authd.CredentialType(user.TokenInfo.Type)
	if !slices.Contains(allowedAPICredentialTypes, credentialType) {
		return mark.With(mark.ErrUnauthorized, errors.New("API was passed unsupported token type"))
	}

	switch credentialType {
	case authd.CredentialTypeUserToServerToken:
		if requestAppID == h.githubAppID || h.ffClient.IsEnabledForGitHubApp(ctx, featureflags.CopilotSWEAgentAPIInternalApp, requestAppID) {
			// Only for use inside GitHub. Still will check if the user specified has push rights regardless of token later
			logger.Info("API called by internal GitHub App token")
			requiredAccessActions = requiredAccessActionsInternal
		} else if h.ffClient.IsEnabledForGitHubApp(ctx, featureflags.CopilotSWEAgentAPIFirstPartyApp, requestAppID) {
			// Trusted first party app that uses GitHub App tokens
			logger.Info("API called by trusted first party GitHub App token")
			requiredAccessActions = requiredAccessActionsFirstParty
		} else {
			logger.Info("API called using 3p GitHub App token")
			requiredAccessActions = requiredAccessActionsDefault
		}
	case authd.CredentialTypeOauthApplicationAccessToken:
		if h.ffClient.IsEnabledForOAuthApp(ctx, featureflags.CopilotSWEAgentAPIFirstPartyApp, int64(user.TokenInfo.ApplicationID)) {
			// Trusted first party app that uses OAuth App tokens
			logger.Info("API called by trusted first party OAuth App token")
			requiredAccessActions = requiredAccessActionsFirstParty
		} else {
			logger.Info("API called using 3rd party OAuth App token")
			requiredAccessActions = requiredAccessActionsDefault
		}
	default:
		logger.Info(fmt.Sprintf("API called using %s", string(credentialType)))
		requiredAccessActions = requiredAccessActionsDefault
	}
	hasAccess, err := h.verifyTokenAccess(ctx, user, repoOwner, repoName, requiredAccessActions)
	if err != nil {
		return fmt.Errorf("API failed to verify token's access: %w", err)
	}
	if !hasAccess {
		return mark.With(mark.ErrUnauthorized, errors.New("user does not have access to create PRs or push to the specified repository"))
	}
	logger.Info("user token has appropriate permissions to use API")

	// Even if the token has the correct permissions we **still** want to verify that the user
	// actually has push rights regardless of what the token says out of an abundance of caution.
	// We will use the token passed in to call this endpoint since it only needs "metadata" and this
	// will also ensure per-user **rate limits** are also applied based on calls to GitHub itself.
	userTokenGH, err := h.cf.NewClient(user.TokenInfo.Token)
	if err != nil {
		return mark.With(mark.ErrUnauthorized, fmt.Errorf("unable to create user client: %w", err))
	}
	permission, err := userTokenGH.GetUserPermissionsForRepository(ctx, repoOwner, repoName, user.Login)
	if err != nil {
		// Possible we will hit a rate limit, so get the response code so we use the right one for the response
		if githubErr, ok := err.(*github.GitHubAPIError); ok {
			if githubErr.Response != nil {
				return joberrors.MarkExistingError(githubErr, githubErr.Response.Status)
			}
		}
		return err
	}
	// If people can push, they can also create PRs. So we do not need to do a separate check for this bit.
	if !permission.Push {
		return mark.With(mark.ErrUnauthorized, errors.New("user does not have write permissions to specified repository"))
	}
	logger.Info("user has push rights")
	return nil
}
