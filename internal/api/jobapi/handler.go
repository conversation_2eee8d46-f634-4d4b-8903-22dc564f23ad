package jobapi

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	mark "github.com/github/go-mark"
	"github.com/github/sweagentd/internal/api/jobapi/jobfactory"
	"github.com/github/sweagentd/internal/capi"
	"github.com/github/sweagentd/internal/experiments"
	"github.com/github/sweagentd/internal/featureflags"
	"github.com/github/sweagentd/internal/github"
	"github.com/github/sweagentd/internal/githubtwirp"
	"github.com/github/sweagentd/internal/joberrors"
	"github.com/github/sweagentd/internal/jobexecutor"
	"github.com/github/sweagentd/internal/jobs"
	"github.com/github/sweagentd/internal/jobutils"
	"github.com/github/sweagentd/internal/observability"
	"github.com/github/sweagentd/internal/requestctx"
	"github.com/go-chi/chi/v5"
)

// Currently the job API request is the same as the jobfactory.NewJobArgs struct, but we should keep it separate so it can evolve over time
const (
	NewJobAPIRequestV0HeaderValue  = "2025-06-19"
	NewJobAPIRequestDefaultVersion = NewJobAPIRequestV0HeaderValue
)

type NewJobAPIRequestV0 struct {
	jobfactory.NewJobArgs `json:",inline"`
}

func getAPIVersionForRequest(version string) (string, error) {
	if version == "" {
		return NewJobAPIRequestDefaultVersion, nil
	}
	if version == NewJobAPIRequestV0HeaderValue {
		return NewJobAPIRequestV0HeaderValue, nil
	}
	return "", mark.With(mark.ErrBadRequest, fmt.Errorf("invalid API version: %s", version))
}

// Takes in a request body and header version string and returns the appropriate NewJobArgs
// and the API version that was resolved for use elsewhere and in the response.
func newJobArgsForNewJobRequest(body io.Reader, version string) (*jobfactory.NewJobArgs, string, error) {
	version, err := getAPIVersionForRequest(version)
	if err != nil {
		return nil, "", err
	}
	if version == NewJobAPIRequestV0HeaderValue {
		var args NewJobAPIRequestV0
		if err := json.NewDecoder(body).Decode(&args); err != nil {
			return nil, "", fmt.Errorf("failed to decode request body: %w", err)
		}
		return &args.NewJobArgs, NewJobAPIRequestV0HeaderValue, nil
	}
	return nil, "", mark.With(mark.ErrBadRequest, fmt.Errorf("invalid API version: %s", version))
}

type Handler struct {
	obsv        observability.Exporters
	cf          github.ClientFactoryInterface
	jobsStore   jobs.JobsStore
	ffClient    featureflags.Client
	capiClient  capi.Client
	jobExecutor jobexecutor.JobExecutor
	ghTwirp     githubtwirp.ClientInterface
	jobFactory  *jobfactory.JobFactory
	githubAppID int64
}

func NewHandler(obsv observability.Exporters, cf github.ClientFactoryInterface, jobsStore jobs.JobsStore, ffClient featureflags.Client, capiClient capi.Client, jobExecutor jobexecutor.JobExecutor, ghTwirp githubtwirp.ClientInterface, experiments experiments.Client, githubAppID int64) *Handler {
	jobFactory := jobfactory.New(obsv, ffClient, jobsStore, ghTwirp, experiments)
	return &Handler{obsv, cf, jobsStore, ffClient, capiClient, jobExecutor, ghTwirp, jobFactory, githubAppID}
}

func (h *Handler) RegisterRoutes(r chi.Router) {
	// API for use by external systems
	// NOTE: No update / PUT endpoints should be implemented until we have a way to ensure the agent cannot use it.
	// These endpoints should not require the nonce used for the /agent/job endpoint so we need a scope or a way to
	// block the Copilot Coding agent app from using it w/o a passed in shared secret or encrypted token.
	r.Post("/jobs/{owner}/{repo}", h.HandleNewJob)
	r.Post("/jobs/{owner}/{repo}/{job}/cancel", h.HandleCancelJob)
	r.Post("/jobs/{owner}/{repo}/session/{session}/cancel", h.HandleCancelJob)
	r.Get("/jobs/{owner}/{repo}/{job}", h.HandleGetJobByIDOrSessionID)
	r.Get("/jobs/{owner}/{repo}/{job}/status", h.HandleGetJobByIDOrSessionID)
	r.Get("/jobs/{owner}/{repo}/session/{session}", h.HandleGetJobByIDOrSessionID)

	r.Route("/v0", func(r chi.Router) { // Endpoint for 2025-06-19 version of the API
		r.Post("/jobs/{owner}/{repo}", h.HandleNewJobV0)
		r.Post("/jobs/{owner}/{repo}/{job}/cancel", h.HandleCancelJob)
		r.Post("/jobs/{owner}/{repo}/session/{session}/cancel", h.HandleCancelJobV0)
		r.Get("/jobs/{owner}/{repo}/{job}", h.HandleGetJobByIDOrSessionIDV0)
		r.Get("/jobs/{owner}/{repo}/{job}/status", h.HandleGetJobByIDOrSessionIDV0)
		r.Get("/jobs/{owner}/{repo}/session/{session}", h.HandleGetJobByIDOrSessionIDV0)
	})
}

// HandleNewJobV0 is a wrapper around HandleNewJob that sets the API version header for the request.
// Since this API currently proxies through CAPI, we need a URL based version identifier as well
// All this method does is set the appropriate header value before passing it on to the main handler.
// We will no longer need to do this once sweagentd is no longer proxied.
func (h *Handler) HandleNewJobV0(w http.ResponseWriter, r *http.Request) {
	ctx, span := h.obsv.Tracer.Start(r.Context(), "jobapi.HandleNewJobV0")
	defer span.End()
	r = r.WithContext(ctx)

	// Set the API version header for the request
	r.Header.Set("X-GitHub-Api-Version", NewJobAPIRequestV0HeaderValue)

	// Call the main handler with the updated request
	h.HandleNewJob(w, r)
}

// HandleNewJob is the New job endpoint handler, non-versioned.
//
// NOTE: Since things like skills need to be able to tell the difference between auth failures and feature flag failures,
// response codes are as follows:
//
//	201 - Created - job was successfully created and queued for execution
//	400 - Bad Request - user did not provide valid info to do the work
//	401 - Unauthorized - user is not authorized to call this endpoint or does not have push rights to the repo
//	402 - Payment Required - user has no remaining quota for premium requests
//	403 - Forbidden - user is authorized to call this endpoint, but the user or repository does not have the Coding Agent enabled.
//	404 - Not Found - repository or other resource not found. Also used when an app or user is on the "disallow" list to obfuscate.
//	409 - Conflict - A PR already exists for the specified base and head refs
//	500 - Internal Server Error - something went wrong on the server side
func (h *Handler) HandleNewJob(w http.ResponseWriter, r *http.Request) {
	ctx, span := h.obsv.Tracer.Start(r.Context(), "jobapi.HandleNewJob")
	defer span.End()
	r = r.WithContext(ctx)

	logger := h.obsv.LoggerWithRequestTelemetry(ctx, r)
	logger.Info("handling new job request")

	repoName := chi.URLParam(r, "repo")
	repoOwner := chi.URLParam(r, "owner")

	if repoName == "" || repoOwner == "" {
		logger.Error("API call requires repo_owner and repo_name")
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	user, ok := requestctx.User(ctx)
	if !ok {
		http.Error(w, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
		return
	}
	logger.Info("user authenticated")

	// User is authorized to call the API (note that whether the repo has the coding agent and user has quota is later)
	err := h.authorizeUser(ctx, logger, repoOwner, repoName, user)
	if err != nil {
		// A "not found" marked error means that the user was explicitly blocked by a feature flag likely for abuse.
		// So, if this is the case, or we hit some sort of internal server error, report. Otherwise, just log the error.
		statusCode := joberrors.StatusCodeFromMarkedError(err)
		if statusCode == http.StatusNotFound || statusCode == http.StatusInternalServerError {
			h.obsv.LogAndReportError(ctx, err, "authorization failed for user or app")
		} else {
			logger.WithError(err).Error("authorization failed for user or app")
		}
		http.Error(w, http.StatusText(statusCode), statusCode)
		return
	}

	// Get an installation client for all GitHub API calls
	gh, err := h.cf.NewClientFromRepoName(ctx, repoName, repoOwner)
	if err != nil {
		logger.WithError(err).Error("unable to create client")
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	apiVersion := r.Header.Get("X-GitHub-Api-Version")
	jobArgs, apiVersion, err := newJobArgsForNewJobRequest(r.Body, apiVersion)
	if err != nil {
		h.errorResponseWithError(ctx, w, logger, err, "")
		return
	}
	logger.Info("API request is valid")

	// TODO: Make this a twirp call to just get essential repo details - mainly IDs
	repo, err := gh.GetRepository(ctx, repoOwner, repoName)
	if err != nil {
		h.errorResponseWithError(ctx, w, logger, err, "invalid repository") // Bubble up the inner error since this likely is marked with the right response
		return
	}
	logger.Info("got repository for request")

	// Next verify the repository actually has Copilot coding agent enabled
	enabled, err := githubtwirp.IsCopilotSweEnabled(ctx, h.ghTwirp, int64(user.ID), repo.GetID())
	if err != nil {
		h.errorResponseWithError(ctx, w, logger, err, "failed to check whether copilot is enabled")
		return
	}
	if !enabled {
		logger.Error("Copilot coding agent not enabled for user or repo")
		http.Error(w, http.StatusText(http.StatusForbidden), http.StatusForbidden)
		return
	}
	logger.Info("copilot coding agent enabled")

	if !h.ffClient.IsEnabledForUserOrRepoOrOwner(ctx, featureflags.CopilotSWEAgentAqueductJobQueuing, int64(user.ID), repo.GetID(), repo.Owner.GetID()) {
		// Quota is checked when processing the job/launching the agent. This will soon to be removed once the FF is fully rolled out.
		hasSufficientQuota, _, err := h.jobExecutor.GetPremiumQuotaStatus(ctx, repo.Owner.GetID(), repo.GetID(), int64(user.ID))
		if err != nil {
			h.errorResponseWithError(ctx, w, logger, err, "failed to check quota remaining")
			return
		}
		if !hasSufficientQuota {
			logger.Error("User has no remaining quota for premium requests")
			http.Error(w, http.StatusText(http.StatusPaymentRequired), http.StatusPaymentRequired)
			return
		}
		logger.Info("user has quota")
	}

	jobAssembly, err := h.jobFactory.NewJobAssembly(ctx, repo, jobArgs, user)
	if err != nil {
		h.errorResponseWithError(ctx, w, logger, err, "failed to draft job")
		return
	}
	err = h.jobFactory.AssembleJob(ctx, gh, jobAssembly, user)
	if err != nil {
		h.errorResponseWithError(ctx, w, logger, err, "failed to assemble job")
		return
	}

	jobutils.UpdateRequestContextJob(ctx, h.obsv, jobAssembly.Job)
	logger = h.obsv.LoggerWithRequestTelemetry(ctx, r)
	logger.Info("assignment and pending job created")

	// If there's an existing active assignment, queue the job. Otherwise, execute it
	if err := h.jobExecutor.QueueJob(ctx, gh, jobAssembly.Job); err != nil {
		h.errorResponseWithError(ctx, w, logger, err, "failed to queue job")
		return
	}
	// refresh the job with updates from the job executor
	jobutils.UpdateRequestContextJob(ctx, h.obsv, jobAssembly.Job)
	logger = h.obsv.LoggerWithRequestTelemetry(ctx, r)
	logger.Info("job queued")

	// We'll omit the workflow run details here since we are considering
	// moving execution to a queue, so it would not be ready here
	jobResponse := &BaseJobResponse{}
	err = h.hydrateBaseJobResponse(ctx, gh, jobAssembly.Job, jobResponse, true)
	if err != nil {
		h.errorResponseWithError(ctx, w, logger, err, "failed to generate job details")
		return
	}
	logger.Info("job details gathered")

	json, err := json.Marshal(jobResponse)
	if err != nil {
		h.errorResponseWithError(ctx, w, logger, err, "failed to marshal job details")
		return
	}
	w.Header().Set("Content-Length", fmt.Sprintf("%d", len(json)))
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Cache-Control", "no-store")
	w.Header().Set("Pragma", "no-cache")
	w.Header().Set("X-GitHub-Api-Version", apiVersion)
	w.WriteHeader(http.StatusCreated)
	w.Write(json)

	logger.Info("new job api request successful")
}

// HandleCancelJobV0 is a wrapper around HandleCancelJob that sets the API version header for the request.
// Since this API currently proxies through CAPI, we need a URL based version identifier as well
// All this method does is set the appropriate header value before passing it on to the main handler.
// We will no longer need to do this once sweagentd is no longer proxied.
func (h *Handler) HandleCancelJobV0(w http.ResponseWriter, r *http.Request) {
	ctx, span := h.obsv.Tracer.Start(r.Context(), "jobapi.HandleCancelJob")
	defer span.End()
	r = r.WithContext(ctx)

	// Set the API version header for the request
	r.Header.Set("X-GitHub-Api-Version", NewJobAPIRequestV0HeaderValue)

	// Call the main handler with the updated request
	h.HandleCancelJob(w, r)
}

// HandleCancelJob is the Cancel job endpoint handler - non-versioned.
//
// Response codes:
//
//	204 - No Content - job was successfully canceled
//	400 - Bad Request - user did not provide valid info to do the work
//	401 - Unauthorized - user is not authorized to call this endpoint or does not have push rights to the repo
//	404 - Not Found - repository or job not found. Also used when an app or user is on the "disallow" list to obfuscate.
//	500 - Internal Server Error - something went wrong on the server side
func (h *Handler) HandleCancelJob(w http.ResponseWriter, r *http.Request) {
	ctx, span := h.obsv.Tracer.Start(r.Context(), "jobapi.HandleCancelJob")
	defer span.End()
	r = r.WithContext(ctx)

	logger := h.obsv.LoggerWithRequestTelemetry(ctx, r)
	logger.Info("handling cancel job request")

	repoName := chi.URLParam(r, "repo")
	repoOwner := chi.URLParam(r, "owner")
	jobID := chi.URLParam(r, "job")
	sessionID := chi.URLParam(r, "session")

	if repoName == "" || repoOwner == "" || (jobID == "" && sessionID == "") {
		logger.Error("API call missing required parameters")
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	user, ok := requestctx.User(ctx)
	if !ok {
		http.Error(w, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
		return
	}
	logger.Info("user authenticated")

	// User is authorized to call the API (note that whether the repo has the coding agent and user has quota is later)
	err := h.authorizeUser(ctx, logger, repoOwner, repoName, user)
	if err != nil {
		// A "not found" marked error means that the user was explicitly blocked by a feature flag likely for abuse.
		// So, if this is the case, or we hit some sort of internal server error, report. Otherwise, just log the error.
		statusCode := joberrors.StatusCodeFromMarkedError(err)
		if statusCode == http.StatusNotFound || statusCode == http.StatusInternalServerError {
			h.obsv.LogAndReportError(ctx, err, "authorization failed for user or app")
		} else {
			logger.WithError(err).Error("authorization failed for user or app")
		}
		http.Error(w, http.StatusText(statusCode), statusCode)
		return
	}

	// Get an installation client for all GitHub API calls
	gh, err := h.cf.NewClientFromRepoName(ctx, repoName, repoOwner)
	if err != nil {
		logger.WithError(err).Error("unable to create client")
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	// errorResponseWithError should adapt to a 404 or 500, etc
	repo, err := gh.GetRepository(ctx, repoOwner, repoName)
	if err != nil {
		h.errorResponseWithError(ctx, w, logger, err, "")
		return
	}

	// Next verify the repository actually has Copilot coding agent enabled
	// NOTE: We do not need to check for quota in this case since we are not starting new work.
	enabled, err := githubtwirp.IsCopilotSweEnabled(ctx, h.ghTwirp, int64(user.ID), repo.GetID())
	if err != nil {
		h.errorResponseWithError(ctx, w, logger, err, "failed to check whether copilot is enabled")
		return
	}
	if !enabled {
		logger.Error("Copilot coding agent not enabled for user or repo")
		http.Error(w, http.StatusText(http.StatusForbidden), http.StatusForbidden)
		return
	}
	logger.Info("copilot coding agent enabled")

	// Make sure we a valid API version was passed in - but no need to adapt to it right now
	apiVersion, err := getAPIVersionForRequest(r.Header.Get("X-GitHub-Api-Version"))
	if err != nil {
		h.errorResponseWithError(ctx, w, logger, err, "")
		return
	}

	// Actually do the cancellation
	if err := h.jobExecutor.CancelJob(ctx, &jobexecutor.CancelJobOpts{
		OwnerID:   repo.Owner.GetID(),
		RepoID:    repo.GetID(),
		JobID:     jobID,
		SessionID: sessionID,
		Client:    gh,
	}); err != nil {
		h.errorResponseWithError(ctx, w, logger, err, "") // Upstream error already mentions its failed to cancel the job
		return
	}

	w.Header().Set("Cache-Control", "no-store")
	w.Header().Set("Pragma", "no-cache")
	w.Header().Set("X-GitHub-Api-Version", apiVersion)
	w.WriteHeader(http.StatusNoContent)
	logger.Info("cancel job api request successful")
}

// HandleGetJobByIDOrSessionIDV0 is a wrapper around HandleGetJobByIDOrSessionID that sets the API version header for the request.
// Since this API currently proxies through CAPI, we need a URL based version identifier as well
// All this method does is set the appropriate header value before passing it on to the main handler.
// We will no longer need to do this once sweagentd is no longer proxied.
func (h *Handler) HandleGetJobByIDOrSessionIDV0(w http.ResponseWriter, r *http.Request) {
	ctx, span := h.obsv.Tracer.Start(r.Context(), "jobapi.HandleGetJobV0")
	defer span.End()
	r = r.WithContext(ctx)

	// Set the API version header for the request
	r.Header.Set("X-GitHub-Api-Version", NewJobAPIRequestV0HeaderValue)

	// Call the main handler with the updated request
	h.HandleGetJobByIDOrSessionID(w, r)
}

// HandleGetJobByIDOrSessionID is the Get job endpoint - job ID or session ID is required - non-versioned.
//
// Response codes:
//
//	200 - OK - Job was successfully retrieved
//	400 - Bad Request - user did not provide valid info to do the work
//	401 - Unauthorized - user is not authorized to call this endpoint or does not have push rights to the repo
//	404 - Not Found - repository or job not found. Also used when an app or user is on the "disallow" list to obfuscate.
//	500 - Internal Server Error - something went wrong on the server side
func (h *Handler) HandleGetJobByIDOrSessionID(w http.ResponseWriter, r *http.Request) {
	ctx, span := h.obsv.Tracer.Start(r.Context(), "jobapi.HandleGetJob")
	defer span.End()
	r = r.WithContext(ctx)

	logger := h.obsv.LoggerWithRequestTelemetry(ctx, r)
	logger.Info("handling get job request")

	repoName := chi.URLParam(r, "repo")
	repoOwner := chi.URLParam(r, "owner")
	jobID := chi.URLParam(r, "job")
	sessionID := chi.URLParam(r, "session")

	if repoName == "" || repoOwner == "" || (jobID == "" && sessionID == "") {
		logger.Error("API call missing required parameters")
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	user, ok := requestctx.User(ctx)
	if !ok {
		http.Error(w, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
		return
	}
	logger.Info("user authenticated")

	// User is authorized to call the API (note that whether the repo has the coding agent and user has quota is later)
	err := h.authorizeUser(ctx, logger, repoOwner, repoName, user)
	if err != nil {
		// A "not found" marked error means that the user was explicitly blocked by a feature flag likely for abuse.
		// So, if this is the case, or we hit some sort of internal server error, report. Otherwise, just log the error.
		statusCode := joberrors.StatusCodeFromMarkedError(err)
		if statusCode == http.StatusNotFound || statusCode == http.StatusInternalServerError {
			h.obsv.LogAndReportError(ctx, err, "authorization failed for user or app")
		} else {
			logger.WithError(err).Error("authorization failed for user or app")
		}
		http.Error(w, http.StatusText(statusCode), statusCode)
		return
	}

	// Get an installation client for all GitHub API calls
	gh, err := h.cf.NewClientFromRepoName(ctx, repoName, repoOwner)
	if err != nil {
		logger.WithError(err).Error("unable to create client")
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	// errorResponseWithError should adapt to a 404 or 500, etc
	repo, err := gh.GetRepository(ctx, repoOwner, repoName)
	if err != nil {
		h.errorResponseWithError(ctx, w, logger, err, "")
		return
	}

	// Next verify the repository actually has Copilot coding agent enabled
	// NOTE: We do not need to check for quota in this case since we are not starting new work.
	enabled, err := githubtwirp.IsCopilotSweEnabled(ctx, h.ghTwirp, int64(user.ID), repo.GetID())
	if err != nil {
		h.errorResponseWithError(ctx, w, logger, err, "failed to check whether copilot is enabled")
		return
	}
	if !enabled {
		logger.Error("Copilot coding agent not enabled for user or repo")
		http.Error(w, http.StatusText(http.StatusForbidden), http.StatusForbidden)
		return
	}
	logger.Info("copilot coding agent enabled")

	// Make sure we a valid API version was passed in - but no need to adapt to it right now
	apiVersion, err := getAPIVersionForRequest(r.Header.Get("X-GitHub-Api-Version"))
	if err != nil {
		h.errorResponseWithError(ctx, w, logger, err, "")
		return
	}

	var job *jobs.Job
	if jobID != "" {
		job, err = h.jobsStore.GetJob(ctx, repo.Owner.GetID(), repo.GetID(), jobID)
	} else {
		job, err = h.jobsStore.GetJobForSessionID(ctx, repo.Owner.GetID(), repo.GetID(), sessionID)
	}
	if err != nil {
		h.errorResponseWithError(ctx, w, logger, joberrors.MarkExistingError(err, "failed to get job"), "")
		return
	}

	// Get full job details, but not in a verbose form
	jobResponse := &FullJobResponse{}
	err = h.hydrateFullJobResponse(ctx, gh, job, jobResponse, false)
	if err != nil {
		h.errorResponseWithError(ctx, w, logger, err, "failed to generate job details")
		return
	}
	logger.Info("job details gathered")
	json, err := json.Marshal(jobResponse)
	if err != nil {
		h.errorResponseWithError(ctx, w, logger, err, "failed to marshal job details")
		return
	}
	w.Header().Set("Content-Length", fmt.Sprintf("%d", len(json)))
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Cache-Control", "no-store")
	w.Header().Set("Pragma", "no-cache")
	w.Header().Set("X-GitHub-Api-Version", apiVersion)
	w.WriteHeader(http.StatusOK)
	w.Write(json)

	logger.Info("get job api request successful")

}
