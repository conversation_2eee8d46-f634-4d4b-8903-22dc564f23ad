FROM ghcr.io/github/gh-base-image/gh-builder-focal:20250328-230512-g0fb6e776a AS builder

WORKDIR /app
COPY . .
RUN apt-get update && \
    apt-get install -y curl && \
    curl -fsSL https://deb.nodesource.com/setup_22.x | bash - && \
    apt-get install -y nodejs && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*
WORKDIR /app/runtime
RUN npm ci
RUN  --mount=type=secret,id=EBPF_DOWNLOAD_GITHUB_TOKEN \
    EBPF_DOWNLOAD_GITHUB_TOKEN="$(cat /run/secrets/EBPF_DOWNLOAD_GITHUB_TOKEN)" script/action-build ./copilot-developer-action-main
RUN tar -czf action.tar.gz ./copilot-developer-action-main
WORKDIR /app
RUN make build

FROM ghcr.io/github/gh-base-image/gh-base-focal:20250328-000138-g12b5d5ff0 AS run
COPY --chown=github:github --from=builder /app/runtime/action.tar.gz /app/runtime/
ENV RUNTIME_APP_PATH=/app/runtime/action.tar.gz
COPY --chown=github:github --from=builder /app/bin/ /app/bin/
USER github
ENV PORT=8080
EXPOSE 8080
ENTRYPOINT ["/app/bin/sweagentd"]
