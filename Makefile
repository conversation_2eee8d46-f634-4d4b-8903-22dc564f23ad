# Makefile for building Go code

# Go parameters
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod
GOGENERATE=$(GOCMD) generate
GOFMT=$(GOCMD) fmt
BINARY_NAME=bin/sweagentd

# Targets
all: test build

build:
	$(GOBUILD) -o $(BINARY_NAME) -v ./cmd/http

clean:
	$(GOCLEAN)
	rm -f $(BINARY_NAME)

test:
	script/test

format:
	$(GOFMT) fmt ./...

tidy:
	$(GOMOD) tidy

proto:
	$(GOGENERATE) ./proto

.PHONY: all build clean test tidy proto
