#!/bin/bash
set -e
# Ubuntu 20.04's default gcc and g++ are 9.x, but we need >= 10.x for the runtime

# Check if sudo is available
if command -v sudo >/dev/null 2>&1; then
    SUDO_CMD="sudo"
else
    SUDO_CMD=""
    echo "Warning: sudo not available, running apt-get commands without sudo"
fi

. /etc/os-release
if [ "$VERSION_CODENAME" = "focal" ]; then
    if ! type gcc-10 >/dev/null 2>&1 || ! type g++-10 >/dev/null 2>&1; then
        echo "gcc-10 or g++-10 not found, installing..."
        $SUDO_CMD apt-get update
        $SUDO_CMD apt-get install -y gcc-10 g++-10
    fi
    # Use the system gcc and g++ in the codespace
    export CC="$(which gcc-10)" CXX="$(which g++-10)"
fi
npm "$@"
