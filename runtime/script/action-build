#!/usr/bin/env bash
#
# Usage: action-build [target-dir] [exit-zero-if-no-token]
#
# Builds all required contents for the action and places them in the target directory.
# The exit-zero-if-no-token flag is used to indicate whether the build should fail if
# EBPF_DOWNLOAD_GITHUB_TOKEN is not set and the firewall binaries cannot be downloaded. 

set -e

script_dir="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
runtime_dir="$( cd "$script_dir/.." &> /dev/null && pwd )"
action_dir="$( cd "$script_dir/../action" &> /dev/null && pwd )"

if [ -z "$1" ]; then
    target_dir="${action_dir}/dist"
else
    target_dir="$(realpath "$1")"
fi
exit_zero_if_no_token="${2:-false}"

# Clean out target directory and create necessary directories
if [ -e "${target_dir}" ]; then
    rm -rf "${target_dir}/*" "${target_dir}/dist" "${target_dir}/ebpf" "${target_dir}/mcp"
fi
mkdir -p "${target_dir}/dist" "${target_dir}/script" "${target_dir}/ebpf" "${target_dir}/mcp/dist"

echo -e "\nCopying action files..."
echo "$(git rev-parse --verify HEAD)" > "${target_dir}/version"
cp "${action_dir}/action.yml" "${target_dir}"
cp "${action_dir}/script"/* "${target_dir}/script"
chmod +x "${target_dir}/script/"*.sh
cp "${action_dir}/node-req.env" "${target_dir}"
# Make sure .gitignore is not in target directory since we don't want to ignore files in the action
rm -f "${target_dir}/.gitignore"

echo -e "\nBuilding runtime..."
cd "${runtime_dir}"
"${script_dir}/npm" install
"${script_dir}/npm" run build
cp -r dist/* "${target_dir}/dist"
cp package.json "${target_dir}"
cp package-lock.json "${target_dir}"
cp LICENSE.md "${target_dir}"

echo -e "\nBuilding MCP client..."
cd "${runtime_dir}"
"${script_dir}/npm" install
"${script_dir}/npm" run build:mcp-client
cp -r dist-mcp-client/* "${target_dir}/mcp/dist"
cp package.json "${target_dir}/mcp"
cp package-lock.json "${target_dir}/mcp"

echo -e "\nBuilding firewall..."
bash "${script_dir}/firewall-build" "${target_dir}/ebpf" "${exit_zero_if_no_token}"

echo -e "\nBuilding Blackbird MCP server..."
bash "${script_dir}/blackbird-mcp-build" "${target_dir}/blackbird-mcp-server"

echo -e "\nPulling GitHub MCP server..."
bash "${script_dir}/github-mcp-pull" "${target_dir}/github-mcp-server"

echo -e "\nAction build complete!"
