#!/bin/bash
#
# Usage: firewall-build [target-dir] [exit-zero-if-no-token] [ebpf-padawan-egress-firewall-version] [mkcert-version]
#
# Acquires firewall binaries and copies launch script to the target directory. Over time we may also switch
# launch.sh to javascript or go code. So that's why this script is named "firewall-build".

# firewall-test
# firewall-test test
# firewall-test test-root
# firewall-test test-non-root
# firewall-test test-local
# firewall-test "<command>" "<allow_list>"
# firewall-test run "<command>" "<allow_list>"
# firewall-test run-root "<command>" "<allow_list>"
# firewall-test run-non-root "<command>" "<allow_list>"
# firewall-test run-local "<command>" "<allow_list>"

set -e

script_dir="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
runtime_dir="$( cd "$script_dir/.." &> /dev/null && pwd )"
firewall_dir="$( cd "$script_dir/../firewall" &> /dev/null && pwd )"
firewall_test_dir="$( cd "$firewall_dir/test" &> /dev/null && pwd )"
non_root_user="vscode"

cd "${firewall_dir}"

tmp_dir="${TMPDIR:-/tmp}/padawan-fw-test-$(date +%Y%m%d-%H%M%S)"
mkdir -p "${tmp_dir}"

runtime_image="cpd-runtime-test-image"
# Note: We use host network to allow running a service on localhost to validate firewall handling
#       of local traffic.
docker_command="$(cat <<EOF
  docker run -e COPILOT_AGENT_DEBUG=true \
    --userns=host \
    --cgroupns=host \
    --network=host \
    --privileged \
    --pid=host \
    --mount=type=bind,source=${firewall_dir},target=/fw,readonly \
    --mount=type=bind,source=/tmp,target=/tmp \
    --mount=type=bind,source=/sys/fs/cgroup,target=/sys/fs/cgroup \
    -i \
    --rm
EOF
)"

# run "<use_docker>" "<inner_command>" <env_vars>...
run() {
  local run_type="$1"
  local inner_command="$2"
  shift 2
  local command_to_execute=""
  if [ "$run_type" = "local" ]; then
    command_to_execute="${firewall_dir}/dist/ebpf/launch.sh"
  elif [ "$run_type" = "docker-non-root" ]; then
    command_to_execute="${docker_command} --user ${non_root_user}"
  else
    command_to_execute="${docker_command} --user root"
  fi

  # Append prefix to rest of args one by one, or export vars for local exec
  for arg in "$@"; do
    if [ "$run_type" != "local" ]; then
      command_to_execute="${command_to_execute} -e ${arg}"
    else
      export ${arg}
    fi
  done

  # Now run the command in a container, or directly through bash
  if [ "$run_type" != "local" ]; then
    echo "${command_to_execute} ${runtime_image} \"${inner_command}\""
    ${command_to_execute} ${runtime_image} "${inner_command}"
  else
    export COPILOT_AGENT_DEBUG=true
    echo "bash \"${command_to_execute}\" \"${inner_command}\""
    bash "${command_to_execute}" "${inner_command}"
  fi

  return $?
}

# run_test "<use_docker>" "<name>" "<should_fail>" <env_vars>...
run_test() {
  local run_type="$1"
  local test_name="$2"
  shift 2
  local test_command=""
  if [ "${run_type}" != "local" ]; then
    test_command="node /fw/test/smoke-test.js"
  else
    test_command="node ${firewall_test_dir// /\\ }/smoke-test.js"
  fi
  echo "🧪 Running test: $test_name"
  set +e
  set -o pipefail
  run "${run_type}" "${test_command}" "$@" 2>&1 > "${tmp_dir}/${test_name// /_}.log"
  exit_code=$?
  set -e
  set +o pipefail
  if [ $exit_code -eq 0 ]; then
    echo "✅ \"$test_name\" passed."
    echo ""
  else
    echo "❌ \"$test_name\" failed."
    echo "Output:"
    cat "${tmp_dir}/${test_name// /_}.log"
    exit 1
  fi
  return ${exit_code}
}

test_all() {
  local run_type="${1:-docker-root}"
  local large_allow_list='https://github.com/,https://raw.githubusercontent.com/,https://github-cloud.githubusercontent.com/,https://github-cloud.s3.amazonaws.com/,https://api.githubcopilot.com/,https://api.githubcopilot.com/,https://npmjs.org/,https://npmjs.com/,https://registry.npmjs.com/,https://registry.npmjs.org/,https://skimdb.npmjs.com/,https://npm.pkg.github.com/,https://api.npms.io/,https://npm.jsr.io/,https://registry.bower.io/,https://pypi.python.org/,https://pypi.org/,https://pip.pypa.io/,https://pythonhosted.org/,https://files.pythonhosted.org/,https://bootstrap.pypa.io/,https://conda.binstar.org/,https://conda.anaconda.org/,https://binstar.org/,https://anaconda.org/,https://repo.continuum.io/,https://repo.anaconda.com/,https://nuget.org/,https://dist.nuget.org/,https://api.nuget.org/,https://nuget.pkg.github.com/,https://rubygems.org/,https://api.rubygems.org/,https://rubygems.pkg.github.com/,https://bundler.rubygems.org/,https://gems.rubyforge.org/,https://gems.rubyonrails.org/,https://json-schema.org/'
  echo ""
  echo "🏃 Running tests..."
  echo "-------------------------------------"
  echo "Run type: ${run_type}"
  echo ""

  echo "Starting HTTP server on localhost:32101"
  python3 -m http.server 32101 > /dev/null 2>&1 &
  HTTP_SERVER_PID=$!

  # Setup trap to ensure HTTP server is killed on script exit
  cleanup() {
      echo "Cleaning up HTTP server process ($HTTP_SERVER_PID)"
      kill -9 $HTTP_SERVER_PID 2>/dev/null || true
  }
  trap cleanup EXIT


  run_test ${run_type} "Allow github.localhost domain" \
    TEST_FETCH_URL="http://github.localhost:32101" \
    TEST_SUCCESS_STATUS="fulfilled" \
    COPILOT_AGENT_FIREWALL_ENABLED=true \
    COPILOT_AGENT_FIREWALL_ALLOW_LIST="localhost"
  if [ $? -ne 0 ]; then
    return $?
  fi

  run_test ${run_type} "Allow localhost domain" \
    TEST_FETCH_URL="http://localhost:32101" \
    TEST_SUCCESS_STATUS="fulfilled" \
    COPILOT_AGENT_FIREWALL_ENABLED=true \
    COPILOT_AGENT_FIREWALL_ALLOW_LIST="localhost"
  if [ $? -ne 0 ]; then
    return $?
  fi

  run_test ${run_type} "Allow domain test" \
    TEST_FETCH_URL="https://github.com" \
    TEST_SUCCESS_STATUS="fulfilled" \
    COPILOT_AGENT_FIREWALL_ENABLED=true \
    COPILOT_AGENT_FIREWALL_ALLOW_LIST="github.com"
  if [ $? -ne 0 ]; then
    return $?
  fi

  run_test ${run_type} "Block domain test" \
    TEST_FETCH_URL="https://github.com" \
    TEST_SUCCESS_STATUS="rejected" \
    COPILOT_AGENT_FIREWALL_ENABLED=true \
    COPILOT_AGENT_FIREWALL_ALLOW_LIST="google.com"
  if [ $? -ne 0 ]; then
    return $?
  fi

  run_test ${run_type} "Large allow list URL test" \
    TEST_FETCH_URL="https://rubygems.pkg.github.com/" \
    TEST_SUCCESS_STATUS="fulfilled" \
    COPILOT_AGENT_FIREWALL_ENABLED=true \
    COPILOT_AGENT_FIREWALL_ALLOW_LIST="$large_allow_list"
  if [ $? -ne 0 ]; then
    return $?
  fi

  run_test ${run_type} "Large allow list block URL test" \
    TEST_FETCH_URL="https://wikipedia.org" \
    TEST_SUCCESS_STATUS="rejected" \
    COPILOT_AGENT_FIREWALL_ENABLED=true \
    COPILOT_AGENT_FIREWALL_ALLOW_LIST="$large_allow_list"
  if [ $? -ne 0 ]; then
    return $?
  fi

  run_test ${run_type} "Dangling comma test" \
    TEST_FETCH_URL="https://github.com" \
    TEST_SUCCESS_STATUS="fulfilled" \
    COPILOT_AGENT_FIREWALL_ENABLED=true \
    COPILOT_AGENT_FIREWALL_ALLOW_LIST='https://github.com/,'
  if [ $? -ne 0 ]; then
    return $?
  fi

  run_test ${run_type} "Dangling comma block test" \
    TEST_FETCH_URL="https://wikipedia.org" \
    TEST_SUCCESS_STATUS="rejected" \
    COPILOT_AGENT_FIREWALL_ENABLED=true \
    COPILOT_AGENT_FIREWALL_ALLOW_LIST='https://github.com/,'
  if [ $? -ne 0 ]; then
    return $?
  fi

  run_test ${run_type} "Firewall disabled test" \
    TEST_FETCH_URL="https://github.com" \
    TEST_SUCCESS_STATUS="fulfilled" \
    COPILOT_AGENT_FIREWALL_ENABLED=false \
    COPILOT_AGENT_FIREWALL_ALLOW_LIST="https://wikipedia.org"
  if [ $? -ne 0 ]; then
    return $?
  fi

  echo "-------------------------------------"
  echo "🏆 All tests passed!"
  return $?
}


# Build the image if not doing something locally
if [ "$1" != "test-local" ] && [ "$1" != "run-local" ]; then
  if [ -z "${EBPF_DOWNLOAD_GITHUB_TOKEN}" ] && [ -e "${script_dir}/../.env.local" ]; then
    declare -x $(cat "${script_dir}/../.env.local" | grep -E '^EBPF_DOWNLOAD_GITHUB_TOKEN')
  fi
  if [ -z "${EBPF_DOWNLOAD_GITHUB_TOKEN}" ]; then
    echo "The EBPF_DOWNLOAD_GITHUB_TOKEN environment variable needed to download binaries is not set. "
    echo "Set it in your environment or create a .env.local file in runtime folder of the repo with it set."
    exit 1
  fi
  docker buildx build \
    --platform linux/amd64 \
    --build-arg ALLOW_SKIP_FIREWALL_INSTALL=false \
    --secret id=EBPF_DOWNLOAD_GITHUB_TOKEN \
    --load \
    -t ${runtime_image} \
    -f "${firewall_test_dir}/Dockerfile" \
    ${runtime_dir}
fi

# Test scenarios
if [ -z "$1" ] || [ "$1" = "test" ] || [ "$1" = "test-non-root" ]; then
  test_all docker-non-root
  exit $?
elif [ "$1" = "test-root" ]; then
  test_all docker-root
  exit $?
elif [ "$1" = "test-local" ]; then
  test_all local
  exit $?
fi

# Run in container or local
allow_list_var=""
if [ ! -z "$3" ]; then
  allow_list_var="COPILOT_AGENT_FIREWALL_ALLOW_LIST='$3'"
fi
if [ "$1" = "run-non-root" ] || [ "$1" = "run" ]; then
  run docker-non-root "$2" COPILOT_AGENT_FIREWALL_ENABLED=true $allow_list_var
  exit $?
elif [ "$1" = "run-root" ]; then
  run docker-root "$2" COPILOT_AGENT_FIREWALL_ENABLED=true $allow_list_var
  exit $?
elif [ "$1" = "run-local" ]; then
  run local "$2" COPILOT_AGENT_FIREWALL_ENABLED=true $allow_list_var
  exit $?
fi
# A bash command was entered directly
if [ ! -z "$2" ]; then
  allow_list_var="COPILOT_AGENT_FIREWALL_ALLOW_LIST='$2'"
fi
run docker-non-root "$1" COPILOT_AGENT_FIREWALL_ENABLED=true $allow_list_var
exit $?

