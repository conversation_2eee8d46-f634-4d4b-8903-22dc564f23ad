#!/usr/bin/env node
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import * as fs from "fs/promises";
import * as path from "path";

/**
 * Writes the package.json for the CLI to dist-cli/ updating versions and
 * dependency versions.
 *
 * Usage:
 *
 *     script/cli-package-json.js [version]
 */
export async function main() {
    // Paths relative to the sweagentd/runtime directory
    const rootPkgPath = "package.json";
    const cliPkgSrc = "src/cli/package-cli.json";
    const cliPkgDst = "dist-cli/package.json";

    // Set the cwd to the runtime directory
    process.chdir(path.join(import.meta.dirname, ".."));

    const rootPkg = JSON.parse(await fs.readFile(rootPkgPath, "utf8"));
    const cliPkg = JSON.parse(await fs.readFile(cliPkgSrc, "utf8"));

    // Set the package version to the first arg passed to this script
    // or default to the root package version
    cliPkg.version = process.argv[2] || rootPkg.version;

    // Add node-pty as a dependency with the version from the root package.json
    // This means on install users will build the native addon for their
    // platform which requires some environment setup:
    // https://github.com/microsoft/node-pty#dependencies
    // TODO: Revisit this decision post-staffship
    cliPkg.dependencies = { "node-pty": rootPkg.dependencies["node-pty"] };

    await fs.mkdir(path.dirname(cliPkgDst), { recursive: true });
    await fs.writeFile(cliPkgDst, JSON.stringify(cliPkg, null, 2) + "\n");
}

void main();
