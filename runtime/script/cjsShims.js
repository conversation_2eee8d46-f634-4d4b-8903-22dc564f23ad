/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import path from "path";
import { fileURLToPath } from "url";

// Provide shims for CommonJS globals `__filename` and `__dirname` to be
// available for CJS dependencies when bundled into the output index.js file
// which transpiled to and runs as ESM.
export const __filename = fileURLToPath(import.meta.url);
export const __dirname = path.dirname(__filename);
