#!/bin/bash
#
# Usage: blackbird-mcp-build [target-dir]
#
# Downloads the blackbird MCP server source and constructs a go binary, and drops it into the target dir.

set -e

script_dir="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
action_dir="$( cd "$script_dir/../action" &> /dev/null && pwd )"

if [ -z "$1" ]; then
    target_dir="${action_dir}/dist"
else
    target_dir="$(realpath "$1")"
fi

if [ -z "${BLACKBIRD_DOWNLOAD_GITHUB_TOKEN}" ] && [ -e "${script_dir}/../.env.local" ]; then
  declare -x $(cat "${script_dir}/../.env.local" | grep -E '^BLACKBIRD_DOWNLOAD_GITHUB_TOKEN' || '')
fi
if [ -z "${BLACKBIRD_DOWNLOAD_GITHUB_TOKEN}" ]; then
  echo "The BLACKBIRD_DOWNLOAD_GITHUB_TOKEN environment variable is not set and is required to acquire dependencies. "
  exit 0
fi

rm -rf "${target_dir}/src"
mkdir -p "${target_dir}/src"
cd "${target_dir}/src"

GITHUB_TOKEN=$BLACKBIRD_DOWNLOAD_GITHUB_TOKEN gh repo clone github/mcp-server-playground
cd mcp-server-playground
git checkout padawan-blackbird

# Build the binary itself
echo "Go build... (currently $PWD)"
go build cmd/github-mcp-server/main.go

echo "Copy the output binary"
mv main ../../blackbird-mcp-server

rm -rf "${target_dir}/src"

echo -e "\nMCP server build complete!"
