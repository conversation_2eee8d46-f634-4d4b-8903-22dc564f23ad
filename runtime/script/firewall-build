#!/bin/bash
#
# Usage: firewall-build [target-dir] [exit-zero-if-no-token] [ebpf-padawan-egress-firewall-version] [mkcert-version]
#
# Acquires firewall binaries and copies launch script to the target directory. Over time we may also switch
# launch.sh to javascript or go code. So that's why this script is named "firewall-build".

set -e

script_dir="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
firewall_dir="$( cd "$script_dir/../firewall" &> /dev/null && pwd )"
target_dir="${1:-"${firewall_dir}/dist/ebpf"}"
exit_zero_if_no_token="${2:-false}"
version="${3:-0.0.35}"
mkcert_version="${4:-1.4.4}"

mkdir -p "${target_dir}/in-path"
# Add version information to file
sha="$(git rev-parse --verify HEAD 2>/dev/null || echo 'development')"
echo "${sha}, ${version}, ${mkcert_version}" > "${target_dir}/version"

# Copy the launch script to the target directory - this where we'd do a real build if needed
cp "${firewall_dir}/launch.sh" "${target_dir}"
chmod +x "${target_dir}/launch.sh"

# Now get the dependencies
cd "${target_dir}/in-path"

if [ -z "${EBPF_DOWNLOAD_GITHUB_TOKEN}" ] && [ -e "${script_dir}/../.env.local" ]; then
  declare -x $(cat "${script_dir}/../.env.local" | grep -E '^EBPF_DOWNLOAD_GITHUB_TOKEN' || '')
fi
if [ -z "${EBPF_DOWNLOAD_GITHUB_TOKEN}" ]; then
  echo "The EBPF_DOWNLOAD_GITHUB_TOKEN environment variable is not set and is required to acquire dependencies. "
  echo "Create a GitHub access token with read access to the github/ebpf-padawan-egress-firewall repo and place it"
  echo "in your environment, or create a .env.local file in runtime folder with EBPF_DOWNLOAD_GITHUB_TOKEN set."
  if [ "$exit_zero_if_no_token" = "true" ]; then
    echo -e"\nSkipping download of firewall binaries."
    exit 0
  else
    exit 1
  fi
fi

# Download mkcert
mkcert_dl_url="https://dl.filippo.io/mkcert/v${mkcert_version}?for=linux/amd64"
echo "Downloading ${mkcert_dl_url}..."
curl -sfSL "${mkcert_dl_url}" -o mkcert
chmod +x mkcert
echo "Downloaded mkcert."

# Download ebpf archive and move binary to target location
binary_name="ebpf-padawan-egress-firewall"
repo="github/ebpf-padawan-egress-firewall"
if [ "${version}" = "0.0.3" ] || [ "${version}" = "0.0.5" ]; then
  binary_name="ebpf-cgroup-firewall"
  repo="lawrencegripper/ebpf-cgroup-firewall"
fi
filename_base="${binary_name}_${version}"
dl_filename="${filename_base}_linux_amd64.tar.gz"

checksum_filename="${filename_base}_checksums.txt"
if [ ! -e "${firewall_dir}/checksums/${checksum_filename}" ]; then
    checksum_url="https://github.com/${repo}/releases/download/v${version}/${checksum_filename}"
    echo "Checksum file not found: ${checksum_filename}"
    echo "Download the file from ${checksum_url} and place it in the /runtime/firewall/checksums directory in the source tree"
    exit 1
fi
cp "${firewall_dir}/checksums/${checksum_filename}" .

echo "Downloading ${dl_filename}..."
dl_url="$(curl -sfSL \
  -H "Accept: application/vnd.github+json" \
  -H "Authorization: Bearer ${EBPF_DOWNLOAD_GITHUB_TOKEN}" \
  -H "X-GitHub-Api-Version: 2022-11-28" \
  https://api.github.com/repos/${repo}/releases/tags/v${version} | jq -r '.assets[] | select(.name == "'${dl_filename}'") | .url')"
curl -sfSL \
  -H "Accept: application/octet-stream" \
  -H "Authorization: Bearer ${EBPF_DOWNLOAD_GITHUB_TOKEN}" \
  -H "X-GitHub-Api-Version: 2022-11-28" \
  -o "${dl_filename}" \
  "${dl_url}"
echo "Verifying checksum..."
sha256sum --check --status "${checksum_filename}" || { echo "Checksum verification failed"; exit 1; }
tar -xz -C . -f "$dl_filename"
chmod +x "${binary_name}"
rm -f LICENSE README.md
echo "Downloaded and verified \"${binary_name}\" from \"${repo}\""

# Rename to a common name and add the launch.sh script
mv "${binary_name}" "padawan-fw"

# Clean up
rm -f "$dl_filename" "$checksum_filename"

echo -e "\nFirewall build complete!"
