#!/usr/bin/env bash
#
# Used in local development within a github/github Codespace - this script
# will build the action and push the built files to the repo.

# Exit immediately if a command exits with a non-zero status
set -e

# Get the root folder of the runtime
script_dir="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

if [ "${CODESPACES}" != "true" ] || [ "${GITHUB_REPOSITORY}" != "github/github" ] || [ ! -e "/workspaces/github" ]; then
  echo "Not running in a codespace for github/github. Exiting."
  exit 1
fi

if [ ! -e "/workspaces/copilot-developer-action-local" ]; then
  echo "Missing cloned instance of copilot-developer-action. Attempting to clone."
  mkdir -p /workspaces/copilot-developer-action-local

  set +e

  git clone http://monalisa:$<EMAIL>/github/copilot-developer-action /workspaces/copilot-developer-action-local > /dev/null 2>&1
  if [ $? -ne 0 ]; then
    echo "Attempting to seed and and clone."
  
    /workspaces/github/bin/seed github_repo --nwo github/copilot-developer-action -t $GITHUB_TOKEN
    if [ $? -ne 0 ]; then
      echo "Seeding failed. To work around this issue, manually create a private"
      echo "github/copilot-developer-action repo in your Github instance with just"
      echo "a README.md and re-run this script. Exiting."
      rm -rf /workspaces/copilot-developer-action
      exit 1
    fi

    git clone http://monalisa:$<EMAIL>/github/copilot-developer-action /workspaces/copilot-developer-action-local
    if [ $? -ne 0 ]; then
      echo "Cloning repo failed. Exiting."
      rm -rf /workspaces/copilot-developer-action-local
      exit 1
    fi
  fi

  set -e
  echo "Cloning complete."
fi

# Build the action - but do not fail if firewall binaries cannot be downloaded. Will warn instead.
bash "${script_dir}/action-build" /workspaces/copilot-developer-action-local true

cd /workspaces/copilot-developer-action-local

echo -e "\nCommitting and pushing the changes..."
git add -A .
git commit -m "chore: update action"
git push -f

echo -e "\n🎉 Done!"