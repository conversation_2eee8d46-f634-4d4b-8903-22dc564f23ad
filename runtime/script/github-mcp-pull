#!/bin/bash
#
# Usage: github-mcp-pull [target-dir]
#
# Downloads the github-mcp server binary and drops it into the target dir.
set -e

script_dir="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
action_dir="$( cd "$script_dir/../action" &> /dev/null && pwd )"

if [ -z "$1" ]; then
    target_dir="${action_dir}/dist"
else
    target_dir="$(realpath "$1")"
fi

GITHUB_MCP_SERVER_VERSION="0.5.0"
TEMP_DIR=$(mktemp -d)
BINARY_NAME="github-mcp-server"
PLATFORM="Linux_x86_64"

echo "Creating temporary directory at ${TEMP_DIR}"
pushd "${TEMP_DIR}" > /dev/null

# Download the binary and checksums
echo "Downloading ${BINARY_NAME} version ${GITHUB_MCP_SERVER_VERSION}..."
curl -L "https://github.com/github/github-mcp-server/releases/download/v${GITHUB_MCP_SERVER_VERSION}/${BINARY_NAME}_${PLATFORM}.tar.gz" -o "${BINARY_NAME}_${PLATFORM}.tar.gz"

echo "Downloading checksums..."
curl -L "https://github.com/github/github-mcp-server/releases/download/v${GITHUB_MCP_SERVER_VERSION}/${BINARY_NAME}_${GITHUB_MCP_SERVER_VERSION}_checksums.txt" -o checksums.txt
# Verify checksum
if grep "${BINARY_NAME}_${PLATFORM}.tar.gz" checksums.txt | shasum -a 256 -c; then
    echo "Checksum verification passed!"
else
    echo "Checksum verification failed! Aborting."
    rm -rf "${TEMP_DIR}"
    exit 1
fi

# Extract binary
echo "Extracting binary..."
tar -xzf "${BINARY_NAME}_${PLATFORM}.tar.gz" "${BINARY_NAME}"

# Move binary to output directory
if [ -d "${target_dir}" ]; then
    rm -rf "${target_dir}"
fi
mkdir -p "${target_dir}"
echo "Moving binary to target directory..."
mv "${BINARY_NAME}" "${target_dir}"

# Clean up
echo "Cleaning up temporary files..."
popd > /dev/null
rm -rf "${TEMP_DIR}"

echo "Successfully downloaded and verified ${BINARY_NAME} version ${GITHUB_MCP_SERVER_VERSION}"