# Log Visualizer

Nightly evals trend report generator for GitHub Copilot coding agent.

## How to run

```
npm install
npm start report.html [model]
```

- This will download the logs from the last 10 runs of the nightly evals tests for the specified model (default: 'default').
- The ZIPs are then extracted.
- A report is generated that indicates the trend of test pass/fail over the runs.

### Parameters

- `report.html`: Output path for the generated HTML report
- `model` (optional): Model name to filter runs by. Defaults to 'default' if not specified.

### Examples

```bash
# Generate report for default model
npm start report.html

# Generate report for claude-3.7-sonnet model
npm start report.html claude-3.7-sonnet

# Generate report for oswe model
npm start report.html oswe
```
