/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { spawnSync } from "child_process";

const EVENT = "schedule";
const STATUS = "completed";
const WORKFLOW_NAME = "Nightly Evals";

export function isGitHubCliInstalled(): boolean {
    try {
        const result = spawnSync("gh", ["--version"], { stdio: "ignore" });
        return result.status !== undefined && result.status === 0;
    } catch {
        return false;
    }
}

export function isLoggedIn(): boolean {
    try {
        const result = spawnSync("gh", ["auth", "status"]);
        return result.stdout.toString().includes("Logged in to github.com");
    } catch (error) {
        console.error("Error checking GitHub CLI authentication status:", error);
        return false;
    }
}

export function tryLogIn(): boolean {
    try {
        // Use inherit here so that the output passes to the user's terminal.
        const logInResult = spawnSync("gh", ["auth", "login"], {
            stdio: "inherit",
        });
        return logInResult.status !== undefined && logInResult.status === 0;
    } catch (error) {
        console.error("Error logging in to GitHub CLI:", error);
        return false;
    }
}

export interface RunInfo {
    runId: string;
    date: string;
    event: string;
    branch: string;
    model?: string;
}

export function getLastCompletedNightlyRunInfos(branchName: string, count: number): RunInfo[] {
    try {
        const result = spawnSync("gh", [
            "run",
            "list",
            "--json",
            "databaseId,status,createdAt,event,headBranch",
            "--limit",
            String(count),
            "--branch",
            branchName,
            "--workflow",
            WORKFLOW_NAME,
        ]);
        if (result.status === undefined || result.status !== 0) {
            console.error("Failed to get last nightly run IDs from GitHub CLI.");
            console.error("Output:", result.stderr.toString());
            return [];
        }

        const resultsJson = result.stdout.toString();
        const parsed = JSON.parse(resultsJson);

        if (Array.isArray(parsed)) {
            const runInfos: RunInfo[] = parsed
                .filter((run) => run.status === STATUS && run.event === EVENT)
                .map((run) => ({
                    runId: run.databaseId.toString(),
                    date: run.createdAt,
                    event: run.event,
                    branch: run.headBranch,
                }));

            return runInfos.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
        }

        console.error("Unexpected response format from GitHub CLI:", resultsJson);
        return [];
    } catch (error) {
        console.error("Error getting last N nightly eval run IDs from GitHub CLI:", error);
        return [];
    }
}

export function downloadRun(runId: string, outputDir: string): boolean {
    try {
        const result = spawnSync("gh", ["run", "download", runId, "--dir", outputDir]);

        if (result.status === undefined || result.status !== 0) {
            console.error(`Failed to download run ${runId} from GitHub CLI.`);
            console.error("Output:", result.stderr.toString());
            return false;
        }
        return true;
    } catch (error) {
        console.error(`Error downloading run ${runId} from GitHub CLI:`, error);
        return false;
    }
}

export function getArtifactsForRun(runId: string): string[] {
    try {
        const result = spawnSync("gh", [
            "api",
            `repos/github/sweagentd/actions/runs/${runId}/artifacts`,
            "--jq",
            ".artifacts[].name",
        ]);

        if (result.status === undefined || result.status !== 0) {
            console.error(`Failed to get artifacts for run ${runId} from GitHub CLI.`);
            console.error("Output:", result.stderr.toString());
            return [];
        }

        return result.stdout
            .toString()
            .trim()
            .split("\n")
            .filter((line) => line.trim().length > 0);
    } catch (error) {
        console.error(`Error getting artifacts for run ${runId} from GitHub CLI:`, error);
        return [];
    }
}

export function extractModelFromArtifacts(artifacts: string[]): string {
    // Look for artifacts with the pattern: eval-results-run{number}-{model}
    const evalResultsArtifacts = artifacts.filter((artifact) => artifact.startsWith("eval-results-run"));

    if (evalResultsArtifacts.length === 0) {
        return "default";
    }

    // Extract model from the first matching artifact
    const artifact = evalResultsArtifacts[0];

    // Pattern: eval-results-run{number}-{model}
    // Use regex to match the pattern and extract the model
    const match = artifact.match(/^eval-results-run\d+-(.+)$/);
    if (match && match[1]) {
        return match[1];
    }

    // If no model suffix is found (just eval-results-run{number}), it's the default model
    if (artifact.match(/^eval-results-run\d+$/)) {
        return "default";
    }

    return "default";
}

export function filterRunsByModel(runInfos: RunInfo[], targetModel: string): RunInfo[] {
    console.log(`Filtering ${runInfos.length} runs for model '${targetModel}'...`);

    const filteredRuns: RunInfo[] = [];

    for (const runInfo of runInfos) {
        const artifacts = getArtifactsForRun(runInfo.runId);
        const detectedModel = extractModelFromArtifacts(artifacts);

        if (detectedModel === targetModel) {
            filteredRuns.push({
                ...runInfo,
                model: detectedModel,
            });
        }
    }

    return filteredRuns;
}
