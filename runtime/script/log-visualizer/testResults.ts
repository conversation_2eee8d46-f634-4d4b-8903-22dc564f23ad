/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import * as fs from "fs";
import path from "path";

export interface CheckResults {
    name: string;
    passedInRuns: Set<string>;
    failedInRuns: Set<string>;
    // Track attempts within each run: runId -> attemptId -> passed
    runAttemptResults: Map<string, Map<string, boolean>>;
}

export class AggregatedTestResults {
    // Records which of the checks passed in which runs.
    public readonly checkResults = new Map<string, CheckResults>();

    recordTestCaseResult(
        testCaseName: string,
        checkName: string,
        runId: string,
        attemptId: string,
        passed: boolean,
    ): void {
        const combinedName = `${testCaseName};${checkName}`;

        if (!this.checkResults.has(combinedName)) {
            this.checkResults.set(combinedName, {
                name: combinedName,
                passedInRuns: new Set(),
                failedInRuns: new Set(),
                runAttemptResults: new Map(),
            });
        }

        const testCase = this.checkResults.get(combinedName)!;

        // Track attempt-level results
        if (!testCase.runAttemptResults.has(runId)) {
            testCase.runAttemptResults.set(runId, new Map());
        }
        testCase.runAttemptResults.get(runId)!.set(attemptId, passed);

        // Also update the run-level results (for backward compatibility)
        if (passed) {
            testCase.passedInRuns.add(runId);
        } else {
            testCase.failedInRuns.add(runId);
        }
    }

    recordTestCaseTimeout(testCaseName: string, runId: string, attemptId: string): void {
        // Use previous test runs to determine which checks we need to mark as failed due to timeout.
        // This isn't perfect. It won't work for the first run in the series and it will potentially miss
        // checks added later or mark checks that don't exist in the current run, but it should be good enough.
        const checkNames = Array.from(this.checkResults.keys())
            .map((key) => key.split(";"))
            .filter((parts) => parts[0] === testCaseName)
            .map((parts) => parts[1]);

        checkNames.forEach((checkName) => {
            this.recordTestCaseResult(testCaseName, checkName, runId, attemptId, false);
        });
    }
}

function isDirectory(path: string): boolean {
    try {
        const stats = fs.statSync(path);
        return stats.isDirectory();
    } catch {
        return false;
    }
}

export function readTestResults(tempDir: string): AggregatedTestResults {
    const aggregateResults = new AggregatedTestResults();

    // The run ids.
    for (const runDirectory of fs.readdirSync(tempDir)) {
        const runPath = path.join(tempDir, runDirectory);
        if (!isDirectory(runPath)) {
            continue;
        }

        // The attempts (there are typically 3 attempts per run).
        for (const attemptDirectory of fs.readdirSync(runPath)) {
            const attemptPath = path.join(runPath, attemptDirectory);
            if (!isDirectory(attemptPath)) {
                continue;
            }

            // The test cases (typically named).
            for (const testCaseDirectory of fs.readdirSync(attemptPath)) {
                const testCasePath = path.join(attemptPath, testCaseDirectory);
                if (!isDirectory(testCasePath)) {
                    continue;
                }

                let seenAtLeastOneChecksJson = false;

                // For some reason there's an id of some sort.
                for (const idDirectory of fs.readdirSync(testCasePath)) {
                    const idPath = path.join(testCasePath, idDirectory);
                    if (!isDirectory(idPath)) {
                        continue;
                    }
                    const checksPath = path.join(idPath, "checks.json");

                    if (fs.existsSync(checksPath)) {
                        const checks = fs.readFileSync(checksPath, "utf-8");
                        const parsedChecks = JSON.parse(checks);

                        seenAtLeastOneChecksJson = true;

                        if (Array.isArray(parsedChecks)) {
                            for (const check of parsedChecks) {
                                if (check.name) {
                                    aggregateResults.recordTestCaseResult(
                                        testCaseDirectory,
                                        check.name,
                                        runDirectory,
                                        attemptDirectory,
                                        check.assertion === true,
                                    );
                                } else {
                                    console.warn(`Skipping malformed check in ${checksPath}:`, check);
                                }
                            }
                        } else {
                            console.warn(`Expected an array of checks in ${checksPath}, but got:`, parsedChecks);
                        }
                    }
                }

                // It appears that we can have multiple sub-attempts within a single attempt run.
                // Only mark a test as failed if all sub-attempts failed to produce a checks.json.
                if (!seenAtLeastOneChecksJson) {
                    aggregateResults.recordTestCaseTimeout(testCaseDirectory, runDirectory, attemptDirectory);
                }
            }
        }
    }

    return aggregateResults;
}
