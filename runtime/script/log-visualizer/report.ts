/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

// Generate an HTML report from aggregated test results with the following information:
// - Run summary section:
//  - Number of check cases and checks.
//  - Percentage of tests that are passing.

import { RunInfo } from "./githubCli";
import { AggregatedTestResults } from "./testResults";

//  - Percentage of checks that are passing.
export function generateReport(runInfos: RunInfo[], aggregatedResults: AggregatedTestResults, model: string): string {
    // Calculate percentage of test cases where all checks passed for each run
    // 1. Group checks by test case name
    const testCaseChecks = new Map();
    aggregatedResults.checkResults.forEach((check, checkName) => {
        const testCase = checkName.split(";")[0];
        if (!testCaseChecks.has(testCase)) testCaseChecks.set(testCase, []);
        testCaseChecks.get(testCase).push(check);
    });
    // 2. For each run, count test cases where all checks passed
    const runTestCasePassPercentages = runInfos.map((runInfo) => {
        let totalCases = 0;
        let passingCases = 0;
        testCaseChecks.forEach((checks) => {
            totalCases++;
            // A test case passes if all its checks passed all attempts
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            const allChecksPassed = checks.every((check: any) => {
                const runAttempts = check.runAttemptResults.get(runInfo.runId);

                // Calculate total attempts
                const successfulAttempts = runAttempts ? runAttempts.size : 0;

                if (successfulAttempts === 0) {
                    return false;
                }

                // A check passes only if ALL attempts passed
                if (runAttempts && runAttempts.size > 0) {
                    const passedAttempts = Array.from(runAttempts.values()).filter((passed) => passed).length;
                    return passedAttempts === successfulAttempts;
                }

                return false;
            });
            if (allChecksPassed) passingCases++;
        });
        return totalCases > 0 ? (passingCases / totalCases) * 100 : 0;
    });

    return `
        <html>
            <head>
                <title>GitHub Copilot coding agent Nightly Evals report</title>
                <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
                <style>
                    body {
                        background: #0d1117;
                        color: #c9d1d9;
                        font-family: 'Segoe UI', 'Liberation Sans', Arial, sans-serif;
                        margin: 0;
                        padding: 0;
                    }
                    h1, h2 {
                        color: #58a6ff;
                        margin-left: 32px;
                    }
                    .container {
                        max-width: 900px;
                        margin: 40px auto 40px auto;
                        background: #161b22;
                        border-radius: 12px;
                        box-shadow: 0 4px 24px 0 #0008;
                        padding: 32px 40px 40px 40px;
                    }
                    p {
                        color: #8b949e;
                        font-size: 1.1em;
                    }
                    .charts {
                        display: flex;
                        flex-wrap: wrap;
                        gap: 32px;
                        justify-content: center;
                        margin-top: 32px;
                    }
                    .chart-card {
                        background: #21262d;
                        border-radius: 10px;
                        box-shadow: 0 2px 8px 0 #0006;
                        padding: 24px 20px 20px 20px;
                        width: 760px;
                        min-width: 640px;
                        max-width: 840px;
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                    }
                    canvas {
                        max-width: 700px !important;
                        max-height: 220px !important;
                    }
                    table {
                        width: 100%;
                        border-collapse: collapse;
                        margin: 32px 0 0 0;
                        background: #161b22;
                        color: #c9d1d9;
                        border-radius: 8px;
                        overflow: hidden;
                    }
                    th, td {
                        padding: 6px 12px;
                        text-align: left;
                    }
                    th {
                        background: #21262d;
                        color: #58a6ff;
                        font-weight: 600;
                    }
                    tr {
                        border-bottom: 1px solid #30363d;
                    }
                    tr:last-child {
                        border-bottom: none;
                    }
                    td.status {
                        font-size: 1.5em;
                        text-align: center;
                        width: 60px;
                    }
                    .status-pass {
                        color: #3fb950;
                    }
                    .status-warn {
                        color: #f1c40f;
                    }
                    .status-fail {
                        color: #f85149;
                    }
                    td.link {
                        text-align: right;
                    }
                    a.run-link {
                        color: #58a6ff;
                        text-decoration: underline;
                        font-weight: 500;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>GitHub Copilot coding agent <a href="https://github.com/github/sweagentd/actions/workflows/nightly-eval.yaml?query=branch%3Amain+event%3Aschedule">Nightly Evals report</a> - Model: ${model}</h1>
                    <h2>Trend</h2>
                    <div class="charts">
                        <div class="chart-card">
                            <canvas id="testCasePassingChart"></canvas>
                        </div>
                    </div>
                    <h2>Run Status Table</h2>
                    <table>
                        <thead>
                            <tr>
                                <th>Status</th>
                                <th>Run ID</th>
                                <th>Model</th>
                                <th>Triggering Event</th>
                                <th>Date</th>
                                <th>Branch</th>
                                <th>Link</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${runInfos
                                .map((runInfo) => {
                                    // Calculate overall status for this run
                                    let totalChecks = 0;
                                    let passedChecks = 0;

                                    aggregatedResults.checkResults.forEach((check) => {
                                        const runAttempts = check.runAttemptResults.get(runInfo.runId);

                                        // Count checks that have attempts
                                        if (runAttempts && runAttempts.size > 0) {
                                            totalChecks++;

                                            // Calculate successful vs total attempts
                                            const successfulAttempts = runAttempts ? runAttempts.size : 0;

                                            if (runAttempts && runAttempts.size > 0) {
                                                const passedAttempts = Array.from(runAttempts.values()).filter(
                                                    (passed) => passed,
                                                ).length;

                                                // A check is considered "passed" for the run if ALL its attempts passed.
                                                if (passedAttempts === successfulAttempts) {
                                                    passedChecks++;
                                                }
                                            }
                                        }
                                    });

                                    // Determine status icon and class based on fraction
                                    let statusIcon, statusClass;
                                    if (passedChecks === totalChecks && totalChecks > 0) {
                                        statusIcon = "✅";
                                        statusClass = "status-pass";
                                    } else if (passedChecks === 0) {
                                        statusIcon = "❌";
                                        statusClass = "status-fail";
                                    } else {
                                        statusIcon = "⚠️";
                                        statusClass = "status-warn";
                                    }

                                    const fraction = totalChecks > 0 ? `${passedChecks}⁄${totalChecks}` : "0⁄0";

                                    // Extract run info fields
                                    const event = runInfo.event || "";
                                    const date = runInfo.date || "";
                                    const branch = runInfo.branch || "";
                                    return (
                                        `<tr>\n` +
                                        `<td class='status ${statusClass}'>${statusIcon} ${fraction}</td>` +
                                        `<td>${runInfo.runId}</td>` +
                                        `<td>${runInfo.model || model}</td>` +
                                        `<td>${event}</td>` +
                                        `<td>${date}</td>` +
                                        `<td>${branch}</td>` +
                                        `<td class='link'><a class='run-link' href='https://github.com/github/sweagentd/actions/runs/${runInfo.runId}' target='_blank'>${runInfo.runId}</a></td>` +
                                        `</tr>`
                                    );
                                })
                                .join("")}
                        </tbody>
                    </table>

                    <h2>Failures Summary</h2>
                    <table>
                        <thead>
                            <tr>
                                <th>Test</th>
                                <th>Check</th>
                                ${runInfos.map((runInfo) => `<th><a class='run-link' href='https://github.com/github/sweagentd/actions/runs/${runInfo.runId}' target='_blank'>${runInfo.runId}</a></th>`).join("")}
                            </tr>
                        </thead>
                        <tbody>
                            ${(() => {
                                // Filter to only show Test;Check pairs that failed at least one iteration in at least one run
                                const failedChecks = Array.from(aggregatedResults.checkResults.entries()).filter(
                                    ([_, check]) => {
                                        // Check if this Test;Check pair failed at least one iteration in at least one run
                                        return Array.from(check.runAttemptResults.entries()).some(([_, attempts]) => {
                                            const totalAttempts = attempts.size;
                                            const passedAttempts = Array.from(attempts.values()).filter(
                                                (passed) => passed,
                                            ).length;
                                            return passedAttempts < totalAttempts;
                                        });
                                    },
                                );

                                return failedChecks
                                    .map(([combinedName, check]) => {
                                        const [testName, checkName] = combinedName.split(";");

                                        // Create GitHub search URLs
                                        const testNameForSearch = testName.replace(/_/g, " ");
                                        const testSearchUrl = `https://github.com/search?q=repo%3Agithub%2Fsweagentd%20%22${encodeURIComponent(testNameForSearch)}%22&type=code`;
                                        const checkNameForSearch = checkName.replace(/_/g, " ");
                                        const checkSearchUrl = `https://github.com/search?q=repo%3Agithub%2Fsweagentd%20%22${encodeURIComponent(checkNameForSearch)}%22&type=code`;

                                        return `
                                        <tr>
                                            <td><a href="${testSearchUrl}" target="_blank" style="color: #58a6ff; text-decoration: none;">${testName}</a></td>
                                            <td><a href="${checkSearchUrl}" target="_blank" style="color: #58a6ff; text-decoration: none;">${checkName}</a></td>
                                            ${runInfos
                                                .map((runInfo) => {
                                                    const runAttempts = check.runAttemptResults.get(runInfo.runId);

                                                    // Calculate total attempts
                                                    const totalAttempts = runAttempts ? runAttempts.size : 0;

                                                    if (totalAttempts === 0) {
                                                        // No attempts recorded for this run
                                                        return `<td class='status status-fail'>❌</td>`;
                                                    }

                                                    const passedAttempts = runAttempts
                                                        ? Array.from(runAttempts.values()).filter((passed) => passed)
                                                              .length
                                                        : 0;
                                                    const failedAttempts = totalAttempts - passedAttempts;

                                                    // Create tooltip with detailed breakdown
                                                    const tooltip = `${passedAttempts} passed, ${failedAttempts} failed, (${totalAttempts} total)`;

                                                    // If all attempts passed
                                                    if (passedAttempts === totalAttempts) {
                                                        return `<td class='status status-pass' title='${tooltip}'>✅</td>`;
                                                    }

                                                    // Mixed results - use warning style if some fail, fail style if all fail
                                                    if (passedAttempts === 0) {
                                                        const statusClass = "status-fail";
                                                        const icon = "❌";
                                                        return `<td class='status ${statusClass}' title='${tooltip}'>${icon} 0⁄${totalAttempts}</td>`;
                                                    } else {
                                                        // Some passes, some fails - show as warning
                                                        return `<td class='status status-warn' title='${tooltip}'>⚠️ ${passedAttempts}⁄${totalAttempts}</td>`;
                                                    }
                                                })
                                                .join("")}
                                        </tr>
                                    `;
                                    })
                                    .join("");
                            })()}
                        </tbody>
                    </table>

                    <h2>Individual tests</h2>
                    ${(() => {
                        // Group checks by test name (part before ';')
                        const testGroups = new Map<
                            string,
                            // eslint-disable-next-line @typescript-eslint/no-explicit-any
                            Array<{ checkName: string; check: any }>
                        >();
                        aggregatedResults.checkResults.forEach((check, combinedName) => {
                            const [testName, checkName] = combinedName.split(";");
                            if (!testGroups.has(testName)) {
                                testGroups.set(testName, []);
                            }
                            testGroups.get(testName)!.push({ checkName, check });
                        });

                        // Generate HTML for each test as a separate table
                        return Array.from(testGroups.entries())
                            .map(([testName, checks]) => {
                                // Create GitHub search URL for the test name
                                const testNameForSearch = testName.replace(/_/g, " ");
                                const testSearchUrl = `https://github.com/search?q=repo%3Agithub%2Fsweagentd%20%22${encodeURIComponent(testNameForSearch)}%22&type=code`;

                                return `
                    <h3><a href="${testSearchUrl}" target="_blank" style="color: #58a6ff; text-decoration: none;">${testName}</a></h3>
                    <table>
                        <thead>
                            <tr>
                                <th>Check</th>
                                ${runInfos.map((runInfo) => `<th><a class='run-link' href='https://github.com/github/sweagentd/actions/runs/${runInfo.runId}' target='_blank'>${runInfo.runId}</a></th>`).join("")}
                            </tr>
                        </thead>
                        <tbody>
                            ${checks
                                .map(({ checkName, check }) => {
                                    // Create GitHub search URL for the check name
                                    const checkNameForSearch = checkName.replace(/_/g, " ");
                                    const checkSearchUrl = `https://github.com/search?q=repo%3Agithub%2Fsweagentd%20%22${encodeURIComponent(checkNameForSearch)}%22&type=code`;

                                    return `
                                    <tr>
                                        <td><a href="${checkSearchUrl}" target="_blank" style="color: #58a6ff; text-decoration: none;">${checkName}</a></td>
                                        ${runInfos
                                            .map((runInfo) => {
                                                const runAttempts = check.runAttemptResults.get(runInfo.runId);

                                                // Calculate total attempts.
                                                const successfulAttempts = runAttempts ? runAttempts.size : 0;
                                                const totalAttempts = successfulAttempts;

                                                if (totalAttempts === 0) {
                                                    // No attempts recorded for this run
                                                    return `<td class='status status-fail'>❌</td>`;
                                                }

                                                const passedAttempts = runAttempts
                                                    ? Array.from(runAttempts.values()).filter((passed) => passed).length
                                                    : 0;
                                                const failedAttempts = successfulAttempts - passedAttempts;

                                                // Create tooltip with detailed breakdown
                                                const tooltip = `${passedAttempts} passed, ${failedAttempts} failed, (${totalAttempts} total)`;

                                                // If all attempts passed
                                                if (passedAttempts === successfulAttempts) {
                                                    return `<td class='status status-pass' title='${tooltip}'>✅</td>`;
                                                }

                                                // Mixed results - use warning style if some fail, fail style if all fail
                                                if (passedAttempts === 0) {
                                                    const statusClass = "status-fail";
                                                    const icon = "❌";
                                                    return `<td class='status ${statusClass}' title='${tooltip}'>${icon} 0⁄${totalAttempts}</td>`;
                                                } else {
                                                    // Some passes, some fails - show as warning
                                                    return `<td class='status status-warn' title='${tooltip}'>⚠️ ${passedAttempts}⁄${totalAttempts}</td>`;
                                                }
                                            })
                                            .join("")}
                                    </tr>
                                `;
                                })
                                .join("")}
                        </tbody>
                    </table>
                `;
                            })
                            .join("");
                    })()}
                <script>
                    // New chart for test cases where all checks passed
                    const ctx2 = document.getElementById('testCasePassingChart').getContext('2d');
                    const runIds = ${JSON.stringify(runInfos.map((info) => info.runId))};
                    const dates = ${JSON.stringify(runInfos.map((info) => new Date(info.date).toLocaleDateString()))};
                    const fullDates = ${JSON.stringify(runInfos.map((info) => info.date))};
                    const passPercentages = ${JSON.stringify(runTestCasePassPercentages)};

                    // Calculate trend line (simple linear regression)
                    function linearRegression(y, x) {
                        const n = y.length;
                        const sum_x = x.reduce((a, b) => a + b, 0);
                        const sum_y = y.reduce((a, b) => a + b, 0);
                        const sum_xy = x.reduce((sum, xi, i) => sum + xi * y[i], 0);
                        const sum_xx = x.reduce((sum, xi) => sum + xi * xi, 0);
                        const slope = (n * sum_xy - sum_x * sum_y) / (n * sum_xx - sum_x * sum_x);
                        const intercept = (sum_y - slope * sum_x) / n;
                        return x.map(xi => slope * xi + intercept);
                    }
                    // Use index as x-axis for regression
                    const xVals = passPercentages.map((_, i) => i);
                    const trend = linearRegression(passPercentages, xVals);

                    new Chart(ctx2, {
                        type: 'line',
                        data: {
                            labels: dates,
                            datasets: [
                                {
                                    label: 'Test Cases All Checks Passing',
                                    data: passPercentages,
                                    fill: false,
                                    borderColor: 'rgba(63, 185, 80, 1)',
                                    backgroundColor: 'rgba(63, 185, 80, 0.7)',
                                    tension: 0.2,
                                    pointRadius: 4,
                                    pointBackgroundColor: 'rgba(63, 185, 80, 1)',
                                    pointBorderColor: '#222',
                                    borderWidth: 3
                                },
                                {
                                    label: 'Trend',
                                    data: trend,
                                    fill: false,
                                    borderColor: 'rgba(88, 166, 255, 0.8)',
                                    borderDash: [8, 6],
                                    pointRadius: 0,
                                    borderWidth: 2
                                }
                            ]
                        },
                        options: {
                            plugins: {
                                legend: { labels: { color: '#c9d1d9' } },
                                tooltip: {
                                    callbacks: {
                                        title: function(context) {
                                            const index = context[0].dataIndex;
                                            return \`Run \${runIds[index]}\`;
                                        },
                                        afterTitle: function(context) {
                                            const index = context[0].dataIndex;
                                            return fullDates[index];
                                        },
                                        label: function(context) {
                                            return \`\${context.dataset.label}: \${context.parsed.y.toFixed(1)}%\`;
                                        }
                                    }
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    max: 100,
                                    title: { display: true, text: 'Percentage Test Cases All Checks Passing (%)', color: '#c9d1d9' },
                                    ticks: { color: '#c9d1d9' },
                                    grid: { color: '#30363d' }
                                },
                                x: {
                                    title: { display: true, text: 'Date', color: '#c9d1d9' },
                                    ticks: { color: '#c9d1d9' },
                                    grid: { color: '#30363d' }
                                }
                            }
                        }
                    });
                </script>
            </body>
        </html>
    `;
}
