/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { randomUUID } from "node:crypto";
import * as fs from "node:fs";
import path from "node:path";
import {
    downloadRun,
    filterRunsByModel,
    getLastCompletedNightlyRunInfos,
    isGitHubCliInstalled,
    isLoggedIn,
    tryLogIn,
} from "./githubCli";
import { generateReport } from "./report";
import { readTestResults } from "./testResults";

const RUNS_COUNT = 10;
const BRANCH_NAME = "main";

if (process.argv.length < 3 || process.argv.length > 4) {
    printHelp();
    process.exit(1);
}

const outputFilePath = process.argv[2];
const model = process.argv[3] || "default";

if (!tryGenerateReport(outputFilePath, model)) {
    process.exit(2);
}

function printHelp(): void {
    console.log("Github Copilot coding agent Nightly Eval report generator");
    console.log("Usage: node index.js <outputFilePath> [model]");
    console.log("  outputFilePath: Path to output HTML report file");
    console.log("  model: Optional model name to filter runs by (default: 'default')");
}

function tryGenerateReport(outputFilePath: string, model: string): boolean {
    if (!tryEnsureLoggedIn()) {
        return false;
    }

    // Get more runs to filter by model
    const allRunInfos = getLastCompletedNightlyRunInfos(BRANCH_NAME, 100);
    console.log(`Found ${allRunInfos.length} completed nightly eval runs for branch '${BRANCH_NAME}'`);

    // Filter runs by model based on artifact names
    const filteredRunInfos = filterRunsByModel(allRunInfos, model);
    console.log(`Filtered to ${filteredRunInfos.length} runs for model '${model}'`);

    // Take only the most recent 10 runs
    const lastNightlyRunInfos = filteredRunInfos.slice(
        Math.max(0, filteredRunInfos.length - RUNS_COUNT),
        filteredRunInfos.length,
    );
    console.log(
        `Using last ${lastNightlyRunInfos.length} runs for model '${model}':`,
        lastNightlyRunInfos.map((r) => r.runId),
    );

    // Create a temporary directory to store the output. This will be cleared after the report is generated.
    const tempDir = fs.mkdtempSync(`/tmp/nightly-eval-report-${randomUUID()}`);
    try {
        // Download all of the runs to the temporary directory.
        downloadAllRuns(
            lastNightlyRunInfos.map((runInfo) => runInfo.runId),
            tempDir,
        );

        // Parse the downloaded runs.
        const aggregatedResults = readTestResults(tempDir);

        // Generate the report text, including model information.
        const reportText = generateReport(lastNightlyRunInfos, aggregatedResults, model);
        fs.writeFileSync(outputFilePath, reportText);
    } finally {
        fs.rmSync(tempDir, { recursive: true, force: true });
    }

    return true;
}

function tryEnsureLoggedIn(): boolean {
    if (!isGitHubCliInstalled()) {
        console.error("GitHub CLI is not installed. Please install it to continue.");
        return false;
    }

    if (!isLoggedIn()) {
        if (!tryLogIn()) {
            console.error("Failed to log in to GitHub CLI. Please try again.");
            return false;
        }
    }

    return true;
}

function downloadAllRuns(lastNightlyRunIds: string[], tempDir: string): boolean {
    // Download all of the runs to the temporary directory.
    for (const runId of lastNightlyRunIds) {
        console.log(`Downloading run ${runId}...`);

        if (!downloadRun(runId, path.join(tempDir, runId))) {
            console.warn(`Failed to download run ${runId}.`);
        }
    }

    return true;
}
