# Runtime environment variables for the agent scenario

# this needs to be the part of the URL after github.com/
GITHUB_REPOSITORY=
COPILOT_AGENT_BASE_COMMIT=
COPILOT_AGENT_ISSUE_NUMBER=

# base64 encoded string of the issue description
COPILOT_AGENT_PROMPT=

# Any unique identifier for the folder to use. This should be unique for each run or delete the /tmp/repo-<COPILOT_AGENT_JOB_ID> folder before running
COPILOT_AGENT_JOB_ID=

# If using the claude agent
# ANTHROPIC_API_KEY=
COPILOT_AGENT_CALLBACK_URL=

# "fix" or "fix-pr-comment"
COPILOT_AGENT_ACTION=fix
COPILOT_AGENT_PUSH=false

# The agent to use 'sweagent-aip' or 'sweagent-anthropic' or for models - 'sweagent-aip:o3-mini'
COPILOT_AGENT_MODEL=

# Your GitHub Personal Access Token
GITHUB_TOKEN=
# PAT for git handler test with repo write access to ghcpd/runtime-git-test-repo
GIT_HANDLER_TEST_GITHUB_TOKEN=

# Your GitHub username
COPILOT_AGENT_COMMIT_LOGIN=
COPILOT_AGENT_COMMIT_EMAIL=
GITHUB_HOST=github.com

# Optional - For use with CAPI
GITHUB_COPILOT_INTEGRATION_ID=copilot-developer-dev
GITHUB_COPILOT_API_TOKEN=
CAPI_AZURE_KEY_VAULT_URI=
CAPI_HASHICORP_VAULT_URI=
CAPI_HASHICORP_VAULT_TOKEN=
CAPI_HMAC_KEY=


# AZURE_CLIENT_ID=
# AZURE_ACCESS_TOKEN_SECRET_VAULT_URI=
# AZURE_ACCESS_TOKEN_SECRET_NAME=

# Used when launching the github MCP server in our eval tests. This should be a valid PAT with repo and user access (it needs to pull issues from sweagentd repo).
# If unset, the eval tests will use GITHUB_TOKEN if it is set (which is true when developing in a codespace).
GITHUB_MCP_ACCESS_TOKEN=

# Optional - A token that is used by scripts/action-build to download from the releases at https://github.com/github/ebpf-padawan-egress-firewall
EBPF_DOWNLOAD_GITHUB_TOKEN=

# Optional - A token that is used by runtime/script/blackbird-mcp-build to clone https://github.com/github/mcp-server-playground
BLACKBIRD_DOWNLOAD_GITHUB_TOKEN=