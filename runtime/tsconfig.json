{
  "compilerOptions": {
    "types": ["vitest/globals"],
    // Compilation settings (only used for type checking)
    "module": "ESNext",
    "moduleResolution": "bundler",
    "target": "ESNext",
    "lib": ["ESNext"],
    // Module settings
    "esModuleInterop": true,
    "resolveJsonModule": true,
    // Type checking and linting options
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "noPropertyAccessFromIndexSignature": false,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "skipLibCheck": true,
    "jsx": "react"
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "test/**/*.ts",
    "test/**/*.tsx",
    "vitest.config.ts",
    "vitest.cli.config.ts",
    "esbuild.ts"
  ],
  "exclude": ["node_modules", "dist", "build", "scripts", ".env*", ".npmrc"]
}
