#!/bin/bash

set +e

# Ensure HOME is set for the installer
if [ -z "$HOME" ]; then
    echo "Error: HOME environment variable is not set. Required for installation."
    exit 1
fi

# Check if pipx is already installed
if type pipx > /dev/null 2>&1; then  
    echo "pipx already installed."  
    exit 0  
fi  

# Pin the version of pipx to install
VERSION="1.7.1"
FILENAME="pipx.pyz"
DOWNLOAD_URL="https://github.com/pypa/pipx/releases/download/${VERSION}/${FILENAME}"

# Create a directory for the download
echo "Downloading pipx ${VERSION} directly..."
mkdir -p ~/downloads

#D Download, move onto path
curl -L -o ~/downloads/${FILENAME} ${DOWNLOAD_URL}
chmod +x ~/downloads/${FILENAME}
echo "Moving ${FILENAME} to /usr/local/bin"
sudo mv ~/downloads/${FILENAME} /usr/local/bin/pipx

echo "Confirming binary is on the path"
PIPX_PATH=$(which pipx)
echo "pipx path: $PIPX_PATH"
if [ ! -f "$PIPX_PATH" ]; then
    echo "Error: pipx binary not found"
    exit 0
fi

INSTALLED_VERSION=$(pipx --version)
echo "Successfully installed pipx-${VERSION}"

# Double-check the version
if [[ "$INSTALLED_VERSION" != *"${VERSION}"* ]]; then
    echo "Warning: Installed version ($INSTALLED_VERSION) may not match requested version (${VERSION})"
fi
EXPECTED_CHECKSUM="1d4f46f86830640f1d7c4e29b280a7a42265d6e8af2c063f40baed4513f03ae8"
ACTUAL_CHECKSUM=$(sha256sum "$PIPX_PATH" | cut -d ' ' -f1)
# Compare checksums
if [[ "$ACTUAL_CHECKSUM" = "$EXPECTED_CHECKSUM" ]]; then
    echo "Checksum validation successful"
else
    echo "Checksum validation failed!"
    echo "Expected: $EXPECTED_CHECKSUM"
    echo "Actual:   $ACTUAL_CHECKSUM"
    echo "The installed binary may be corrupted or tampered with"
    exit 1
fi

echo "Download complete!"