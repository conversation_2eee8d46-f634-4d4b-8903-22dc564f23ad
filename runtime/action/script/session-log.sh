#!/bin/sh
SESSION_URL=$1
NAME=$2
OUTPUT=${3:-""}

if [ -z "$GITHUB_COPILOT_LOG_ID" ]; then
  echo "Error: GITHUB_COPILOT_LOG_ID environment variable is not set."
  exit 1
fi

if [ -z "$GITHUB_COPILOT_TOOL_CALL_ID" ]; then
  echo "Error: GITHUB_COPILOT_TOOL_CALL_ID environment variable is not set."
  exit 1
fi

if [ -z "$GITHUB_COPILOT_API_TOKEN" ]; then
  echo "Error: GITHUB_COPILOT_API_TOKEN environment variable is not set."
  exit 1
fi

if [ "$OUTPUT" = "" ]; then
  echo "$NAME"
  # Omit delta.content if OUTPUT is empty
  printf 'data: {"id":"%s","created":"%s","model":"","object":"chat.completion.chunk","choices":[{"finish_reason":"tool_calls","index":0,"delta":{"role":"assistant","tool_calls":[{"index":0,"id":"%s","function":{"name":"run_custom_setup_step","arguments":"{\"name\":\"%s\"}"}}]}}]}\n\n' \
  "$GITHUB_COPILOT_LOG_ID" "$(date +%s)" "$GITHUB_COPILOT_TOOL_CALL_ID" "$NAME" \
  | curl -s -X PUT -H "Authorization: Bearer ${GITHUB_COPILOT_API_TOKEN}" "$SESSION_URL" --data-binary @-
else
  echo "$NAME / $OUTPUT"
  printf 'data: {"id":"%s","created":"%s","model":"","object":"chat.completion.chunk","choices":[{"finish_reason":"tool_calls","index":0,"delta":{"content":"%s", "role":"assistant","tool_calls":[{"index":0,"id":"%s","function":{"name":"run_custom_setup_step","arguments":"{\"name\":\"%s\"}"}}]}}]}\n\n' \
  "$GITHUB_COPILOT_LOG_ID" "$(date +%s)" "$OUTPUT" "$GITHUB_COPILOT_TOOL_CALL_ID" "$NAME" \
  | curl -s -X PUT -H "Authorization: Bearer ${GITHUB_COPILOT_API_TOKEN}" "$SESSION_URL" --data-binary @-
fi
