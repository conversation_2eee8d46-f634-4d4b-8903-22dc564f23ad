#!/bin/bash
set -e
cd "$(dirname "${BASH_SOURCE[0]}")"

if [ -z "${RUNNER_TOOL_CACHE}" ] || [ -z "${RUNNER_TEMP}" ]; then
    echo "RUNNER_TOOL_CACHE is not set. Please set it to the correct path."
    exit 1
fi

. /etc/os-release
if [ "${ID}" = "alpine" ] || [ "${ID_LIKE}" = "alpine" ]; then
    echo "Alpine Linux is not supported. Please use a different base image."
    exit 1
fi

if [ "$(uname -m)" != "x86_64" ]; then
    echo "Only the x86_64 architecture is supported. Please use a different architecture."
    exit 1
fi

target_location="${1:-"${RUNNER_TEMP}/ghcca-node"}"

. ../node-req.env

mkdir -p "${target_location}"
cache_dir="$(find "${RUNNER_TOOL_CACHE}/node" -mindepth 1 -maxdepth 1 -type d -name "${TARGET_NODE_VERSION}" 2>/dev/null | sort -V | tail -n 1 | tr -d '[:blank:]')"
if [ "${cache_dir}" != "" ]; then
    cache_dir="${cache_dir}/x64"
    echo "Using node from tool cache."
else
    echo "Acquiring required version of node."
    version="${FALLBACK_DL_NODE_VERSION}"
    cache_dir="${RUNNER_TOOL_CACHE}/node/${version}/x64"
    mkdir -p "${cache_dir}"
    curl -sSLf "${FALLBACK_DL_URL}" -o "${cache_dir}/node.tar.gz"
    tar -xzf "${cache_dir}/node.tar.gz" -C "${cache_dir}"
    rm -f "${cache_dir}/node.tar.gz"
fi
cache_dir="$(realpath "${cache_dir}")"
link_loc="$(realpath "${target_location}/node")"
ln -sf "${cache_dir}" "${link_loc}"
"${target_location}/node/bin/node" --version

