#!/bin/bash

set +e

# Ensure HOME is set for the installer
if [[ -z "$HOME" ]]; then
    echo "Error: HOME environment variable is not set. Required for installation."
    exit 1
fi

# Check if UVX is already installed
if type uvx > /dev/null 2>&1; then  
    echo "uvx already installed."  
    exit 0  
fi  

# Pin the version of uv to install
UV_VERSION="0.6.14"

# Download, move onto path
DIR="$HOME/.local/bin"
echo "Installing uv ${UV_VERSION} directly..."
curl -LsSf https://astral.sh/uv/${UV_VERSION}/install.sh |  env UV_UNMANAGED_INSTALL="$DIR" sh
echo "installed uv ${UV_VERSION} binary."

echo "Creating symlinks in /usr/local/bin"
sudo ln -sf $DIR/uvx /usr/local/bin/uvx
sudo ln -sf $DIR/uv /usr/local/bin/uv

echo "Confirming binary is on the path"
UV_PATH=$(which uvx)
echo "uvx path: $UV_PATH"
if [ ! -f "$UV_PATH" ]; then
    echo "Error: UV binary not found"
    exit 0
fi

INSTALLED_VERSION=$(uvx --version)
echo "Successfully installed uv-${UV_VERSION}"
    
# Double-check the version
if [[ "$INSTALLED_VERSION" != *"${UV_VERSION}"* ]]; then
    echo "Warning: Installed version ($INSTALLED_VERSION) may not match requested version (${UV_VERSION})"
fi


EXPECTED_CHECKSUM="3047d8fe3730fdb71e3f2db6e3f2c64e0fcbcf7b4acd06614f474084ee06343e"
ACTUAL_CHECKSUM=$(sha256sum "$UV_PATH" | cut -d ' ' -f1)

# Compare checksums
if [[ "$ACTUAL_CHECKSUM" = "$EXPECTED_CHECKSUM" ]]; then
    echo "Checksum validation successful"
else
    echo "Checksum validation failed!"
    echo "Expected: $EXPECTED_CHECKSUM"
    echo "Actual:   $ACTUAL_CHECKSUM"
    echo "The installed binary may be corrupted or tampered with"
    exit 1
fi
