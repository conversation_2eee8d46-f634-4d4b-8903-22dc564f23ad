#!/bin/bash

set +e
if [[ -n "$GITHUB_COPILOT_MCP_JSON_FROM_INPUT" ]]; then
    export GITHUB_COPILOT_MCP_JSON="$(echo "$GITHUB_COPILOT_MCP_JSON_FROM_INPUT" | base64 --decode)"
else
    export GITHUB_COPILOT_MCP_JSON='{"mcpServers":{}}'
fi

if [[ "$GITHUB_COPILOT_MCP_JSON" =~ .*uvx.* ]]; then
    echo "uvx servers detected. Installing uvx if not already present..."
    "$RUNNER_PATH/copilot-developer-action-main/script/install-uv.sh" > /dev/null 2>&1
    if [[ $? -eq 0 ]]; then
        echo "uvx installed."
    else
        echo "Something went wrong during uvx install."
    fi
fi

if [[ "$GITHUB_COPILOT_MCP_JSON" =~ .*python.* ]]; then
    echo "python servers detected. Installing pipx if not already present..."
    "$RUNNER_PATH/copilot-developer-action-main/script/install-pipx.sh" > /dev/null 2>&1
    if [[ $? -eq 0 ]]; then
        echo "pipx installed."
    else
        echo "Something went wrong during pipx install."
    fi
fi

set -e
mkdir -p "$RUNNER_PATH/mcp-server" && \
"$RUNNER_PATH/ghcca-node/node/bin/node" "$RUNNER_PATH/copilot-developer-action-main/mcp/dist/index.js" &

MAX_RETRIES=20
RETRY_COUNT=0
echo "Waiting for MCP servers to be ready..."
while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
if curl -s -f http://localhost:2301/health > /dev/null; then
    echo "MCP servers are ready."
    break
else
    RETRY_COUNT=$((RETRY_COUNT + 1))
    echo "MCP servers not ready yet. Retrying in 5 seconds... ($RETRY_COUNT/$MAX_RETRIES)"
    sleep 5
fi
done

if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
    echo "Failed to start MCP servers after $MAX_RETRIES attempts"
fi
