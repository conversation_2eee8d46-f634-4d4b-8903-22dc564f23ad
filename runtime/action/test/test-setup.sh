#!/bin/bash
set -e 
cd "$(dirname "${BASH_SOURCE[0]}")"

if [ -z "${RUNNER_TOOL_CACHE}" ] || [ -z "${RUNNER_TEMP}" ]; then
    echo "RUNNER_TOOL_CACHE and RUNNER_TEMP need to be set to use this test script and are automatically set by the GitHub Actions runner."
    exit 1
fi

test_variant="${1:-"cached"}"

if [ "$test_variant" = "cached" ]; then
    ../script/setup.sh "${RUNNER_TEMP}/ghcca-node" | tee ./result.log
    if ! grep -q "Using node from tool cache." ./result.log; then
        echo "FAILED: Node was not found in tool cache."
        exit 1
    fi
    if ! "${RUNNER_TEMP}/ghcca-node/node/bin/node" --version | grep -q "v22\."; then
        echo "FAILED: Node version was not set as expected."
        exit 1
    fi
    exit 0
fi

# Download test
export RUNNER_TOOL_CACHE="${RUNNER_TEMP}/fake-cache"
failed=false
mkdir -p "${RUNNER_TOOL_CACHE}"
../script/setup.sh "${RUNNER_TEMP}/ghcca-node" | tee ./result.log
if ! grep -q "Acquiring required version of node." ./result.log; then
    echo "FAILED: Node was not downloaded as expected."
    failed=true
fi
if ! "${RUNNER_TEMP}/ghcca-node/node/bin/node" --version | grep -q "v22\."; then
    echo "FAILED: Node version was not set as expected."
    failed=true
fi

rm -rf "${RUNNER_TOOL_CACHE}"

if [ "$failed" = true ]; then
    exit 1
fi
