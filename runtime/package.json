{"name": "@github/copilot-developer-action", "version": "0.0.1", "type": "module", "repository": {"type": "git", "url": "git+https://github.com/github/copilot-developer-action.git"}, "bugs": {"url": "https://github.com/github/copilot-developer-action/issues"}, "homepage": "https://github.com/github/copilot-developer-action/#readme", "author": "GitHub", "devDependencies": {"@babel/core": "^7.27.4", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@eslint/js": "^9.30.0", "@github/local-action": "^3.2.0", "@parcel/watcher": "^2.5.1", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@testing-library/react": "^16.3.0", "@types/copy-paste": "^2.1.0", "@types/dompurify": "^3.0.5", "@types/express": "^5.0.1", "@types/lodash.merge": "^4.6.9", "@types/node": "^22.14.1", "@types/react": "^19.1.8", "@types/shell-escape": "^0.2.3", "@types/supertest": "^6.0.3", "@types/ws": "^8.18.1", "@vitest/coverage-v8": "^3.1.4", "cross-env": "^7.0.3", "dedent": "^1.6.0", "esbuild": "^0.25.8", "esbuild-plugin-copy": "^2.1.1", "eslint": "^9.30.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-headers": "^1.3.3", "ink-testing-library": "^4.0.0", "javascript-obfuscator": "^4.1.1", "jsdom": "^26.1.0", "prettier": "3.6.2", "rollup": "^4.40.0", "rollup-plugin-copy": "^3.5.0", "rollup-plugin-delete": "^3.0.1", "rollup-plugin-obfuscator": "^1.1.0", "supertest": "^7.1.1", "ts-node": "^10.9.2", "tsx": "^4.20.3", "typescript": "^5.8.3", "typescript-eslint": "^8.35.1", "typescript-language-server": "^4.3.4", "vitest": "^3.1.3"}, "dependencies": {"@actions/core": "^1.11.1", "@actions/exec": "^1.1.1", "@actions/io": "^1.1.3", "@anthropic-ai/sdk": "^0.39.0", "@azure/identity": "^4.9.1", "@azure/keyvault-secrets": "^4.9.0", "@dsnp/parquetjs": "^1.8.6", "@inkjs/ui": "^2.0.0", "@modelcontextprotocol/sdk": "^1.17.0", "axios": "^1.8.4", "chalk": "^5.4.1", "commander": "^13.1.0", "copy-paste": "^2.2.0", "dompurify": "^3.2.5", "dotenv": "^16.5.0", "express": "^5.0.1", "fast-myers-diff": "^3.2.0", "glob": "^11.0.3", "happy-dom": "^17.4.4", "image-size": "^2.0.2", "ink": "^6.1.0", "ink-select-input": "^6.2.0", "ink-text-input": "^6.0.0", "js-tiktoken": "^1.0.20", "lodash.merge": "^4.6.2", "lookpath": "^1.2.3", "node-pty": "^1.1.0-beta33", "open": "^10.2.0", "openai": "^4.96.0", "os-name": "^6.0.0", "react": "^19.1.1", "shell-escape": "0.2.0", "strip-ansi": "^7.1.0", "tar": "^7.4.3", "uuid": "^11.1.0", "vscode-jsonrpc": "^8.2.1", "vscode-languageserver-protocol": "^3.17.5", "vscode-languageserver-types": "^3.17.5", "ws": "^8.18.1", "yaml": "^2.7.1"}, "bin": {"cpd": "dist/index.js", "copilot-dev": "dist-cli/index.js", "mcp-client": "dist-mcp-client/index.js"}, "scripts": {"build": "npx rollup --config rollup.config.ts --configPlugin @rollup/plugin-typescript", "build-debug": "npx rollup --config rollup.config.debug.ts --configPlugin @rollup/plugin-typescript", "build:mcp-client": "npx rollup --config rollup.mcp-client.config.ts --configPlugin @rollup/plugin-typescript", "build:cli": "npx rollup --config rollup.cli.config.ts --configPlugin @rollup/plugin-typescript", "build:cli:watch": "tsx esbuild.ts -w", "build:msbench": "npx rollup --config rollup.msbench.config.ts --configPlugin @rollup/plugin-typescript", "start:mcp-client": "node dist-mcp-client/index.js", "start:cli": "node --enable-source-maps --no-deprecation dist-cli/index.js", "start:cli:watch": "node --watch --enable-source-maps --no-deprecation dist-cli/index.js", "lint": "npx eslint .", "lint:fix": "npx eslint . --fix", "test": "vitest run", "test:cli": "vitest run --config vitest.cli.config.ts", "test:watch": "vitest --watch", "test:cli:watch": "vitest --watch --config vitest.cli.config.ts", "test:coverage": "vitest run --coverage", "test:mcp-client": "vitest run test/mcp-client", "test:mcp-client:watch": "vitest --watch test/mcp-client", "format": "npx prettier . --write", "format:check": "npx prettier . --check", "test:unit": "vitest run test/unit", "test:unit:watch": "vitest --watch test/unit", "sync-action-repo": "script/action-sync-repo"}}