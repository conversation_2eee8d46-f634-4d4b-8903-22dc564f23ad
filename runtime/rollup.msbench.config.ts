/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

// See: https://rollupjs.org/introduction/

import commonjs from "@rollup/plugin-commonjs";
import json from "@rollup/plugin-json";
import nodeResolve from "@rollup/plugin-node-resolve";
import terser from "@rollup/plugin-terser";
import typescript from "@rollup/plugin-typescript";
import copy from "rollup-plugin-copy";
import del from "rollup-plugin-delete";
import obfuscator from "rollup-plugin-obfuscator";

const BANNER = `#!/usr/bin/env node
/*
 * © GitHub ${new Date().getFullYear()}. All rights reserved.
 * Unauthorized copying, modification, or distribution of this software,
 * in whole or in part, is strictly prohibited.
 */`;

const config = {
    input: "src/msbench/index.ts",
    output: {
        esModule: true,
        dir: "dist-msbench",
        format: "es",
        sourcemap: true,
        banner: BANNER,
    },
    // Exclude node-pty from the bundle. It has native modules that can't be bundled
    // and it doesn't play well with the natives plugin due to having a requires()
    // for conpty.node, which isn't built on Linux.
    external: ["node-pty"],
    plugins: [
        typescript({
            noEmitOnError: true,
        }),
        nodeResolve({ preferBuiltins: true }),
        commonjs(),
        json(),
        del({ targets: "dist-msbench/*" }),
        copy({
            targets: [
                {
                    src: "node_modules/node-pty/**/*",
                    dest: "dist-msbench/node_modules/node-pty",
                },
            ],
        }),
        terser({
            format: {
                comments: (_node, comment) => {
                    return comment.value.includes("© GitHub");
                },
            },
            compress: {
                passes: 2,
            },
        }),
        obfuscator({
            compact: true,
            controlFlowFlattening: true,
            controlFlowFlatteningThreshold: 0.75,
            deadCodeInjection: true,
            deadCodeInjectionThreshold: 0.4,
            numbersToExpressions: true,
            simplify: true,
            stringArrayShuffle: true,
            splitStrings: true,
            stringArrayThreshold: 0.75,
            transformObjectKeys: true,
            unicodeEscapeSequence: true,
        }),
    ],
};

export default config;
