/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

// See: https://rollupjs.org/introduction/

import commonjs from "@rollup/plugin-commonjs";
import json from "@rollup/plugin-json";
import nodeResolve from "@rollup/plugin-node-resolve";
import typescript from "@rollup/plugin-typescript";
import copy from "rollup-plugin-copy";
import del from "rollup-plugin-delete";

const config = {
    input: "src/index.ts",
    output: {
        esModule: true,
        // file: 'dist/index.js',
        dir: "dist",
        format: "es",
        sourcemap: true,
        banner: "#!/usr/bin/env node",
    },
    // Exclude node-pty from the bundle. It has native modules that can't be bundled
    // and it doesn't play well with the natives plugin due to having a requires()
    // for conpty.node, which isn't built on Linux.
    external: ["node-pty"],
    plugins: [
        typescript({
            noEmitOnError: true,
        }),
        nodeResolve({ preferBuiltins: true }),
        commonjs(),
        json(),
        del({ targets: "dist/*" }),
        copy({
            targets: [
                {
                    src: "node_modules/node-pty/**/*",
                    dest: "dist/node_modules/node-pty",
                },
            ],
        }),
    ],
};

export default config;
