/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

// CLI rollup configuration
import babel from "@rollup/plugin-babel";
import commonjs from "@rollup/plugin-commonjs";
import json from "@rollup/plugin-json";
import nodeResolve from "@rollup/plugin-node-resolve";
import replace from "@rollup/plugin-replace";
import typescript from "@rollup/plugin-typescript";
import { chmod } from "fs/promises";
import copy from "rollup-plugin-copy";
import del from "rollup-plugin-delete";
const BANNER = `/*
 * © GitHub ${new Date().getFullYear()}. All rights reserved.
 * Unauthorized copying, modification, or distribution of this software,
 * in whole or in part, is strictly prohibited.
 */`;

const config = {
    input: "src/cli/index.ts",
    treeshake: {
        moduleSideEffects: false,
    },
    output: {
        esModule: true,
        file: "dist-cli/index.js",
        format: "es",
        sourcemap: true,
        banner: BANNER,
        inlineDynamicImports: true,
    },
    external: [
        // Node.js built-ins
        "fs",
        "path",
        "os",
        "child_process",
        "crypto",
        "util",
        "stream",
        "events",
        "url",
        "http",
        "https",
        "zlib",
        // Native modules that can't be bundled
        "node-pty",
        "sqlite3",
        "canvas",
        "sharp",
        "typescript-language-server",
        // React DevTools - exclude from bundle to avoid browser-specific code
        "react-devtools-core",
        "react-devtools-shared",
        "react-devtools-backend",
        /react-devtools/,
    ],
    plugins: [
        replace({
            preventAssignment: true,
            values: {
                "process.env.NODE_ENV": JSON.stringify("production"),
                "process.env.DEV": JSON.stringify("false"),
                "process.env['DEV']": JSON.stringify("false"),
            },
            delimiters: ["", ""],
        }),
        replace({
            preventAssignment: true,
            values: {
                "process__default.env['DEV'] === 'true'": "false",
                "process__default.env['DEV']": "'false'",
                __DEV__: "false",
                __REACT_DEVTOOLS_GLOBAL_HOOK__: "undefined",
            },
            delimiters: ["", ""],
        }),
        replace({
            // copy-paste uses __dirname in its win32 fallback for 'paste' operations, which causes a
            // fatal error on startup on Windows. We're not using 'paste' operations so can disable this.
            preventAssignment: true,
            include: "node_modules/copy-paste/platform/win32.js",
            values: { __dirname: '"unused_see_rollup_config"' },
        }),
        typescript({
            noEmitOnError: false,
        }),
        babel({
            babelHelpers: "bundled",
            presets: [
                ["@babel/preset-react", { runtime: "automatic" }],
                ["@babel/preset-typescript", { allowNamespaces: true }],
            ],
            extensions: [".js", ".jsx", ".ts", ".tsx"],
            exclude: ["node_modules/**"],
        }),
        nodeResolve({
            preferBuiltins: true,
            exportConditions: ["node"],
            browser: false,
            extensions: [".js", ".jsx", ".ts", ".tsx"],
            // Ignore browser-specific packages
            ignore: ["react-devtools-core", "react-devtools-shared", "react-devtools-backend"],
        }),
        commonjs(),
        json(),
        del({ targets: "dist-cli/*" }),
        copy({
            targets: [
                {
                    src: "src/cli/README.md",
                    dest: "dist-cli/",
                },
            ],
        }),
        exec({ file: "dist-cli/index.js" }),
    ],
};

function exec({ file }) {
    return {
        name: "exec",
        async writeBundle() {
            await chmod(file, 0o755);
        },
    };
}

export default config;
