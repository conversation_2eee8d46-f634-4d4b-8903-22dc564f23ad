{"version": "0.2.0", "configurations": [{"name": "Debug (TypeScript)", "type": "node", "request": "launch", "program": "${workspaceFolder:sweagentd}/runtime/src/index.ts", "preLaunchTask": "npm: build", "cwd": "${workspaceFolder:sweagentd}/runtime", "envFile": "${workspaceFolder:sweagentd}/runtime/.env.local", "stopOnEntry": false}, {"name": "Debug CPD (TypeScript)", "type": "node", "request": "launch", "program": "${workspaceFolder:sweagentd}/runtime/src/index.ts", "args": ["fix"], "preLaunchTask": "npm: build", "cwd": "${workspaceFolder:sweagentd}/runtime", "envFile": "${workspaceFolder:sweagentd}/runtime/.env.local", "stopOnEntry": false}, {"name": "Debug (built bundle)", "type": "node", "request": "launch", "program": "${workspaceFolder:sweagentd}/runtime/dist/index.js", "preLaunchTask": "npm: build", "cwd": "${workspaceFolder:sweagentd}/runtime", "envFile": "${workspaceFolder:sweagentd}/runtime/.env.local", "stopOnEntry": true}, {"name": "Debug Attach (local container)", "type": "node", "request": "attach", "port": 9229, "address": "127.0.0.1", "localRoot": "${workspaceFolder}", "remoteRoot": "/usr/src/app"}, {"type": "node", "request": "attach", "name": "Attach to Node", "port": 9229}, {"name": "E2E Tests", "type": "node", "request": "launch", "program": "${workspaceFolder:sweagentd}/runtime/node_modules/vitest/vitest.mjs", "args": ["run", "test/evals/client-e2e.test.ts"], "cwd": "${workspaceFolder:sweagentd}/runtime", "envFile": "${workspaceFolder:sweagentd}/runtime/.env.local", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}, {"name": "P0 Basic Tests", "type": "node", "request": "launch", "program": "${workspaceFolder:sweagentd}/runtime/node_modules/vitest/vitest.mjs", "args": ["run", "test/evals/basic.test.ts"], "cwd": "${workspaceFolder:sweagentd}/runtime", "envFile": "${workspaceFolder:sweagentd}/runtime/.env.local", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}, {"name": "P0 Basic Comment Tests", "type": "node", "request": "launch", "program": "${workspaceFolder:sweagentd}/runtime/node_modules/vitest/vitest.mjs", "args": ["run", "test/evals/basic.commentReply.test.ts"], "cwd": "${workspaceFolder:sweagentd}/runtime", "envFile": "${workspaceFolder:sweagentd}/runtime/.env.local", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}, {"name": "Dogfood Tests", "type": "node", "request": "launch", "program": "${workspaceFolder:sweagentd}/runtime/node_modules/vitest/vitest.mjs", "args": ["run", "test/evals/dogfood.test.ts"], "cwd": "${workspaceFolder:sweagentd}/runtime", "envFile": "${workspaceFolder:sweagentd}/runtime/.env.local", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}, {"name": "Tools Tests", "type": "node", "request": "launch", "program": "${workspaceFolder:sweagentd}/runtime/node_modules/vitest/vitest.mjs", "args": ["run", "test/tools/tools.test.ts"], "cwd": "${workspaceFolder:sweagentd}/runtime", "envFile": "${workspaceFolder:sweagentd}/runtime/.env.local", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}, {"name": "MCP Tests", "type": "node", "request": "launch", "program": "${workspaceFolder:sweagentd}/runtime/node_modules/vitest/vitest.mjs", "args": ["run", "test/mcp-client"], "cwd": "${workspaceFolder:sweagentd}/runtime", "envFile": "${workspaceFolder:sweagentd}/runtime/.env.local", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}, {"name": "Debug CLI", "type": "node", "request": "launch", "runtimeExecutable": "npm", "runtimeArgs": ["run", "start:cli"], "cwd": "${workspaceFolder:sweagentd}/runtime", "console": "integratedTerminal"}, {"name": "Chat Completion Tests", "type": "node", "request": "launch", "program": "${workspaceFolder:sweagentd}/runtime/node_modules/vitest/vitest.mjs", "args": ["run", "test/model/capi-chat-completion-client.test.ts"], "cwd": "${workspaceFolder:sweagentd}/runtime", "envFile": "${workspaceFolder:sweagentd}/runtime/.env.local", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}, {"name": "LSP Related Tests", "type": "node", "request": "launch", "program": "${workspaceFolder:sweagentd}/runtime/node_modules/vitest/vitest.mjs", "args": ["run", "test/lsp"], "cwd": "${workspaceFolder:sweagentd}/runtime", "envFile": "${workspaceFolder:sweagentd}/runtime/.env.local", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}]}