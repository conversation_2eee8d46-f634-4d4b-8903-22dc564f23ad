/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { mkdir, readFile, rm, writeFile } from "fs/promises";
import { join } from "path";
import { afterEach, beforeEach, expect, test } from "vitest";
import { fixCommentTools, fixTools } from "../../src/agents/config";
import { pathExists } from "../../src/helpers/file-reader";
import { Runner<PERSON>ogger } from "../../src/runner";
import { DefaultExec } from "../../src/runner/exec/default";
import { ConsoleLogger } from "../../src/runner/logger/console";
import { getOrInitSettings } from "../../src/settings/factory";
import {
    alwaysProceedToolConfirmationCallback,
    createToolSet,
    ToolConfig,
    ToolConfirmationCallback,
    ToolIntention,
    ToolResultExpanded,
} from "../../src/tools";
import { RuntimeSettings } from "../../src/settings";

const TEST_DIR_FILEBASE = "swebench-test";
const TEST_DIR = process.platform === "win32" ? `C:\\tmp\\${TEST_DIR_FILEBASE}` : `/tmp/${TEST_DIR_FILEBASE}`;
const TEST_TIMEOUT = 5000; // 5 seconds for testing
const config: ToolConfig = {
    location: TEST_DIR,
    timeout: TEST_TIMEOUT,
    requireReasoning: true,
    toolConfirmationCallback: alwaysProceedToolConfirmationCallback,
};

async function retry(message: string, fn: () => Promise<void>, maxTries: number = 100, delay: number = 100) {
    let failedAttempts = 0;
    while (true) {
        try {
            await fn();
            return;
        } catch (error: unknown) {
            failedAttempts++;
            if (failedAttempts >= maxTries) {
                throw new Error(`Failed to ${message} after ${maxTries} attempts\n${error}`);
            }
            await new Promise((resolve) => setTimeout(resolve, delay));
        }
    }
}

// Initialize tool set before tests
let toolSet: ReturnType<typeof createToolSet>;

// Initialize logger before tests
let logger: RunnerLogger;

beforeEach(async () => {
    // Create test directory and sample files
    await mkdir(TEST_DIR, { recursive: true });

    const settings = await getOrInitSettings();

    // Initialize logger fresh
    logger = new ConsoleLogger();

    // Initialize tools fresh
    const exec = new DefaultExec(logger);
    toolSet = createToolSet(await getSweBenchTools({ ...config }, 1, settings, logger, exec));

    // Initialize bash session to ensure it's ready
    await toolSet.bash.callback({ command: "echo init" });
});

afterEach(async () => {
    // Shutdown tools
    for (const tool of Object.values(toolSet)) {
        if (tool.shutdown) {
            await tool.shutdown();
        }
    }

    // Clean up test directory
    await retry(`delete ${TEST_DIR} directory`, async () => {
        await rm(TEST_DIR, { recursive: true });
    });
});

test("str_replace_editor view command on file", async () => {
    const testFile = join(TEST_DIR, "test.txt");
    const content = "line 1\nline 2\nline 3";
    await writeFile(testFile, content);

    const result = await toolSet.str_replace_editor.callback({
        command: "view",
        path: testFile,
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    expect(resultStr).toBe(
        content
            .split("\n")
            .map((line, index) => `${index + 1}.${line}`)
            .join("\n"),
    );

    if (typeof result !== "string") {
        expect(result.sessionLog).toBe(`
diff --git a${TEST_DIR}/test.txt b${TEST_DIR}/test.txt
index 0000000..0000000 100644
--- a${TEST_DIR}/test.txt
+++ b${TEST_DIR}/test.txt
@@ -1,3 +1,3 @@
 line 1
 line 2
 line 3
`);
    }
});

test("str_replace_editor view command on directory", async () => {
    const testFile1 = join(TEST_DIR, "test1.txt");
    await writeFile(testFile1, "content1");
    const testFile2 = join(TEST_DIR, "test2.txt");
    await writeFile(testFile2, "content2");

    const result = await toolSet.str_replace_editor.callback({
        command: "view",
        path: TEST_DIR,
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    expect(resultStr).toBe("test1.txt\ntest2.txt");

    if (typeof result !== "string") {
        expect(result.sessionLog).toBe(`
diff --git a${TEST_DIR} b${TEST_DIR}
create file mode 100644\nindex 0000000..0000000
--- a/dev/null
+++ b${TEST_DIR}
@@ -1,0 +1,2 @@
 test1.txt
 test2.txt
`);
    }
});

test("str_replace_editor view command on directory", async () => {
    const testFile = join(TEST_DIR, "test.txt");
    await writeFile(testFile, "content");

    const result = await toolSet.str_replace_editor.callback({
        command: "view",
        path: TEST_DIR,
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    expect(resultStr).toContain("test.txt");

    if (typeof result !== "string") {
        expect(result.sessionLog).toContain("test.txt");
    }
});

test("str_replace_editor create command", async () => {
    const testFile = join(TEST_DIR, "new.txt");
    const content = "new file content";

    const result = await toolSet.str_replace_editor.callback({
        command: "create",
        path: testFile,
        file_text: content,
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    expect(resultStr).toContain("Created file");
    const fileContent = await readFile(testFile, "utf-8");
    expect(fileContent).toBe(content);

    if (typeof result !== "string") {
        expect(result.sessionLog).toBe(`
diff --git a${TEST_DIR}/new.txt b${TEST_DIR}/new.txt
create file mode 100644
index 0000000..0000000
--- a/dev/null
+++ b${TEST_DIR}/new.txt
@@ -1,0 +1,1 @@
+new file content
`);
    }
});

test("str_replace_editor create command should succeed when file_text parameter is undefined", async () => {
    const testFile = join(TEST_DIR, "new.txt");

    const result = await toolSet.str_replace_editor.callback({
        command: "create",
        path: testFile,
        file_text: undefined,
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    expect(resultStr).toContain("Created file");
    const fileContent = await readFile(testFile, "utf-8");
    expect(fileContent).toBe("");
});

test("str_replace_editor create command should fail gracefully when the path already exists as a file", async () => {
    const testFile = join(TEST_DIR, "new.txt");
    const content = "new file content";

    // First create - should succeed
    await toolSet.str_replace_editor.callback({
        command: "create",
        path: testFile,
        file_text: content,
    });
    expect(await pathExists(testFile)).toBe("file");

    // Second create - should fail
    const result = await toolSet.str_replace_editor.callback({
        command: "create",
        path: testFile,
        file_text: content,
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    expect(resultStr).toContain(`Path ${testFile} already exists as a file`);
});

test("str_replace_editor create command should fail gracefully when the path already exists as a directory", async () => {
    const testFile = join(TEST_DIR);
    const content = "new file content";

    const result = await toolSet.str_replace_editor.callback({
        command: "create",
        path: testFile,
        file_text: content,
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    expect(resultStr).toContain(`Path ${testFile} already exists as a directory`);
});

test("str_replace_editor create command should fail gracefully when the parent directory does not exist", async () => {
    // Use a path with a non-existent parent directory
    const nonExistentParentDir = join(TEST_DIR, "non-existent-dir");
    const testFile = join(nonExistentParentDir, "test.txt");
    const content = "test content";

    const result = await toolSet.str_replace_editor.callback({
        command: "create",
        path: testFile,
        file_text: content,
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    // The file shouldn't exist
    expect(await pathExists(testFile)).toBe(false);

    // Check for the appropriate error message
    expect(resultStr).toContain(`Parent directory ${nonExistentParentDir} does not exist`);

    // Check that the result type is "failure"
    if (typeof result !== "string") {
        expect(result.resultType).toBe("failure");
        expect(result.error).toBe("Parent directory does not exist");
    }
});

test("str_replace_editor create command should fail gracefully with deeply nested non-existent parent directories", async () => {
    // Use a path with multiple levels of non-existent parent directories
    const nonExistentNestedParentDir = join(TEST_DIR, "non-existent-dir", "level1", "level2", "level3");
    const testFile = join(nonExistentNestedParentDir, "test.txt");
    const content = "test content for deeply nested file";

    const result = await toolSet.str_replace_editor.callback({
        command: "create",
        path: testFile,
        file_text: content,
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    // The file shouldn't exist
    expect(await pathExists(testFile)).toBe(false);

    // Check for the appropriate error message
    expect(resultStr).toContain(`Parent directory ${nonExistentNestedParentDir} does not exist`);

    // Check that the result type is "failure"
    if (typeof result !== "string") {
        expect(result.resultType).toBe("failure");
        expect(result.error).toBe("Parent directory does not exist");
    }
});

test("str_replace_editor str_replace command", async () => {
    const testFile = join(TEST_DIR, "test.txt");
    const originalContent = "line 1\nline 2\nline 3";
    await writeFile(testFile, originalContent);

    const result = await toolSet.str_replace_editor.callback({
        command: "str_replace",
        path: testFile,
        old_str: "line 2",
        new_str: "replaced line",
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    expect(resultStr).toContain("updated with changes");
    const fileContent = await readFile(testFile, "utf-8");
    expect(fileContent).toBe("line 1\nreplaced line\nline 3");

    if (typeof result !== "string") {
        expect(result.sessionLog).toBe(`
diff --git a${TEST_DIR}/test.txt b${TEST_DIR}/test.txt
index 0000000..0000000 100644
--- a${TEST_DIR}/test.txt
+++ b${TEST_DIR}/test.txt
@@ -1,3 +1,3 @@
 line 1
-line 2
+replaced line
 line 3
`);
    }
});

test("str_replace_editor str_replace command with multiple diffs", async () => {
    const testFile = join(TEST_DIR, "test.txt");
    const originalContent = "line 1\nline 2\nline 3";
    await writeFile(testFile, originalContent);

    const result = await toolSet.str_replace_editor.callback({
        command: "str_replace",
        path: testFile,
        old_str: "line 2",
        new_str: "replaced line\nline 2\nanother replaced line",
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    expect(resultStr).toContain("updated with changes");
    const fileContent = await readFile(testFile, "utf-8");
    expect(fileContent).toBe("line 1\nreplaced line\nline 2\nanother replaced line\nline 3");

    if (typeof result !== "string") {
        expect(result.sessionLog).toBe(`
diff --git a${TEST_DIR}/test.txt b${TEST_DIR}/test.txt
index 0000000..0000000 100644
--- a${TEST_DIR}/test.txt
+++ b${TEST_DIR}/test.txt
@@ -1,3 +1,5 @@
 line 1
+replaced line
 line 2
+another replaced line
 line 3
`);
    }
});

test("str_replace_editor str_replace command with no match", async () => {
    const testFile = join(TEST_DIR, "test.txt");
    const content = "line 1\nline 2\nline 3";
    await writeFile(testFile, content);

    const result = await toolSet.str_replace_editor.callback({
        command: "str_replace",
        path: testFile,
        old_str: "nonexistent line",
        new_str: "replacement",
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    expect(resultStr).toContain("No match found");
    const fileContent = await readFile(testFile, "utf-8");
    expect(fileContent).toBe(content);
});

test("str_replace_editor str_replace command with multiple matches returns an error result", async () => {
    const testFile = join(TEST_DIR, "test.txt");
    const content = "line 1\nline 2\nline 2\nline 3";
    await writeFile(testFile, content);

    const result = await toolSet.str_replace_editor.callback({
        command: "str_replace",
        path: testFile,
        old_str: "line 2",
        new_str: "replaced line",
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    expect(resultStr).toContain("Multiple matches found");
});

test("str_replace_editor str_replace command with no new_str (empty) can delete all content", async () => {
    const testFile = join(TEST_DIR, "test.txt");
    const content = "line 1\nline 2\nline 3\nline 4";
    await writeFile(testFile, content);

    const result = await toolSet.str_replace_editor.callback({
        command: "str_replace",
        path: testFile,
        old_str: "line 1\nline 2\nline 3\nline 4",
        new_str: "",
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    expect(resultStr).toContain("updated with changes");
    const fileContent = await readFile(testFile, "utf-8");
    expect(fileContent).toBe("");
});

test("str_replace_editor str_replace command with no new_str (undefined) fails", async () => {
    const testFile = join(TEST_DIR, "test.txt");
    const content = "line 1\nline 2\nline 3\nline 4";
    await writeFile(testFile, content);

    const result = await toolSet.str_replace_editor.callback({
        command: "str_replace",
        path: testFile,
        old_str: "line 1\nline 2\nline 3\nline 4",
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    expect(resultStr).toEqual("Invalid inputs: new_str is required for str_replace");
    const fileContent = await readFile(testFile, "utf-8");
    expect(fileContent).toBe(content);
});

test("str_replace_editor str_replace command with no new_str (empty) can delete partial start-file content", async () => {
    const testFile = join(TEST_DIR, "test.txt");
    const content = "line 1\nline 2\nline 3\nline 4";
    await writeFile(testFile, content);

    const result = await toolSet.str_replace_editor.callback({
        command: "str_replace",
        path: testFile,
        old_str: "line 1\nline 2\n",
        new_str: "",
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    expect(resultStr).toContain("updated with changes");
    const fileContent = await readFile(testFile, "utf-8");
    expect(fileContent).toBe("line 3\nline 4");
});

test("str_replace_editor str_replace command with no new_str (undefined) fails", async () => {
    const testFile = join(TEST_DIR, "test.txt");
    const content = "line 1\nline 2\nline 3\nline 4";
    await writeFile(testFile, content);

    const result = await toolSet.str_replace_editor.callback({
        command: "str_replace",
        path: testFile,
        old_str: "line 1\nline 2\n",
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    expect(resultStr).toEqual("Invalid inputs: new_str is required for str_replace");
    const fileContent = await readFile(testFile, "utf-8");
    expect(fileContent).toBe(content);
});

test("str_replace_editor str_replace command with no new_str (empty) can delete partial mid-file content", async () => {
    const testFile = join(TEST_DIR, "test.txt");
    const content = "line 1\nline 2\nline 3\nline 4";
    await writeFile(testFile, content);

    const result = await toolSet.str_replace_editor.callback({
        command: "str_replace",
        path: testFile,
        old_str: "line 2\nline 3\n",
        new_str: "",
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    expect(resultStr).toContain("updated with changes");
    const fileContent = await readFile(testFile, "utf-8");
    expect(fileContent).toBe("line 1\nline 4");
});

test("str_replace_editor str_replace command with no new_str (undefined) fails", async () => {
    const testFile = join(TEST_DIR, "test.txt");
    const content = "line 1\nline 2\nline 3\nline 4";
    await writeFile(testFile, content);

    const result = await toolSet.str_replace_editor.callback({
        command: "str_replace",
        path: testFile,
        old_str: "line 2\nline 3\n",
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    expect(resultStr).toEqual("Invalid inputs: new_str is required for str_replace");
    const fileContent = await readFile(testFile, "utf-8");
    expect(fileContent).toBe(content);
});

test("str_replace_editor str_replace command with no new_str (empty) can delete partial end-file content", async () => {
    const testFile = join(TEST_DIR, "test.txt");
    const content = "line 1\nline 2\nline 3\nline 4";
    await writeFile(testFile, content);

    const result = await toolSet.str_replace_editor.callback({
        command: "str_replace",
        path: testFile,
        old_str: "line 3\nline 4",
        new_str: "",
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    expect(resultStr).toContain("updated with changes");
    const fileContent = await readFile(testFile, "utf-8");
    expect(fileContent).toBe("line 1\nline 2\n");
});

test("str_replace_editor str_replace command with no new_str (undefined) fails", async () => {
    const testFile = join(TEST_DIR, "test.txt");
    const content = "line 1\nline 2\nline 3\nline 4";
    await writeFile(testFile, content);

    const result = await toolSet.str_replace_editor.callback({
        command: "str_replace",
        path: testFile,
        old_str: "line 3\nline 4",
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    expect(resultStr).toEqual("Invalid inputs: new_str is required for str_replace");
    const fileContent = await readFile(testFile, "utf-8");
    expect(fileContent).toBe(content);
});

test("str_replace_editor view command with range", async () => {
    const testFile = join(TEST_DIR, "test.txt");
    const content = "line 1\nline 2\nline 3\nline 4\nline 5";
    await writeFile(testFile, content);

    const result = await toolSet.str_replace_editor.callback({
        command: "view",
        path: testFile,
        view_range: [2, 4],
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    expect(resultStr).toBe("2.line 2\n3.line 3\n4.line 4");
    if (typeof result !== "string") {
        expect(result.sessionLog).toBe(`
diff --git a${TEST_DIR}/test.txt b${TEST_DIR}/test.txt
index 0000000..0000000 100644
--- a${TEST_DIR}/test.txt
+++ b${TEST_DIR}/test.txt
@@ -2,3 +2,3 @@
 line 2
 line 3
 line 4
`);
    }
});

test("bash command execution", async () => {
    const result = await toolSet.bash.callback({
        command: 'echo "test"',
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    expect(resultStr).toContain("test");
});

test("bash command with error output", async () => {
    const result = await toolSet.bash.callback({
        command: 'echo "error message" >&2',
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    expect(resultStr).toContain("error message");
});

test("bash command respects working directory", async () => {
    const result = await toolSet.bash.callback({
        command: "pwd",
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;
    expect(resultStr.trim()).toMatch(new RegExp(`${TEST_DIR_FILEBASE}\n<exited with exit code 0>$`));
});

test("str_replace_editor insert command - between lines", async () => {
    const testFile = join(TEST_DIR, "test.txt");
    const originalContent = "line 1\nline 2\nline 3";
    await writeFile(testFile, originalContent);

    const result = await toolSet.str_replace_editor.callback({
        command: "insert",
        path: testFile,
        insert_line: 1,
        new_str: "inserted line",
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    expect(resultStr).toContain("Inserted");
    const fileContent = await readFile(testFile, "utf-8");
    expect(fileContent).toBe("line 1\ninserted line\nline 2\nline 3");
    if (typeof result !== "string") {
        expect(result.sessionLog).toBe(`
diff --git a${TEST_DIR}/test.txt b${TEST_DIR}/test.txt
index 0000000..0000000 100644
--- a${TEST_DIR}/test.txt
+++ b${TEST_DIR}/test.txt
@@ -1,3 +1,4 @@
 line 1
+inserted line
 line 2
 line 3
`);
    }
});

test("str_replace_editor insert command - beginning of file", async () => {
    const testFile = join(TEST_DIR, "test.txt");
    const originalContent = "line 1\nline 2\nline 3";
    await writeFile(testFile, originalContent);

    const result = await toolSet.str_replace_editor.callback({
        command: "insert",
        path: testFile,
        insert_line: 0,
        new_str: "inserted first",
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    expect(resultStr).toContain("Inserted");
    const fileContent = await readFile(testFile, "utf-8");
    expect(fileContent).toBe("inserted first\nline 1\nline 2\nline 3");
    if (typeof result !== "string") {
        expect(result.sessionLog).toBe(`
diff --git a${TEST_DIR}/test.txt b${TEST_DIR}/test.txt
index 0000000..0000000 100644
--- a${TEST_DIR}/test.txt
+++ b${TEST_DIR}/test.txt
@@ -1,3 +1,4 @@
+inserted first
 line 1
 line 2
 line 3
`);
    }
});

test("str_replace_editor insert command - end of file", async () => {
    const testFile = join(TEST_DIR, "test.txt");
    const originalContent = "line 1\nline 2\nline 3";
    await writeFile(testFile, originalContent);

    const result = await toolSet.str_replace_editor.callback({
        command: "insert",
        path: testFile,
        insert_line: 3,
        new_str: "inserted last",
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    expect(resultStr).toContain("Inserted");
    const fileContent = await readFile(testFile, "utf-8");
    expect(fileContent).toBe("line 1\nline 2\nline 3\ninserted last");
    if (typeof result !== "string") {
        expect(result.sessionLog).toBe(`
diff --git a${TEST_DIR}/test.txt b${TEST_DIR}/test.txt
index 0000000..0000000 100644
--- a${TEST_DIR}/test.txt
+++ b${TEST_DIR}/test.txt
@@ -1,3 +1,4 @@
 line 1
 line 2
 line 3
+inserted last
`);
    }
});

test("str_replace_editor insert command - invalid line negative", async () => {
    const testFile = join(TEST_DIR, "test.txt");
    const originalContent = "line 1\nline 2\nline 3";
    await writeFile(testFile, originalContent);

    const result = await toolSet.str_replace_editor.callback({
        command: "insert",
        path: testFile,
        insert_line: -1,
        new_str: "invalid line",
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    expect(resultStr).toContain("Invalid line number");
});

test("str_replace_editor insert command - just beyond end of file", async () => {
    const testFile = join(TEST_DIR, "test.txt");
    const originalContent = "line 1\nline 2\nline 3";
    await writeFile(testFile, originalContent);

    const result = await toolSet.str_replace_editor.callback({
        command: "insert",
        path: testFile,
        insert_line: 4,
        new_str: "inserted just beyond end",
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    expect(resultStr).toContain("Inserted");
    const fileContent = await readFile(testFile, "utf-8");
    expect(fileContent).toBe("line 1\nline 2\nline 3\n\ninserted just beyond end");
    if (typeof result !== "string") {
        expect(result.sessionLog).toContain("inserted just beyond end");
    }
});

test("str_replace_editor insert command - beyond end of file", async () => {
    const testFile = join(TEST_DIR, "test.txt");
    const originalContent = "line 1\nline 2\nline 3";
    await writeFile(testFile, originalContent);

    const result = await toolSet.str_replace_editor.callback({
        command: "insert",
        path: testFile,
        insert_line: 5,
        new_str: "inserted beyond end",
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    expect(resultStr).toContain("Inserted");
    const fileContent = await readFile(testFile, "utf-8");
    expect(fileContent).toBe("line 1\nline 2\nline 3\n\n\ninserted beyond end");
    if (typeof result !== "string") {
        expect(result.sessionLog).toContain("inserted beyond end");
    }
});

test("str_replace_editor insert command - empty file", async () => {
    const testFile = join(TEST_DIR, "empty.txt");
    const emptyContent = "";
    await writeFile(testFile, emptyContent);

    // Testing insert at beginning of empty file (line 0)
    const result1 = await toolSet.str_replace_editor.callback({
        command: "insert",
        path: testFile,
        insert_line: 0,
        new_str: "inserted into empty file",
    });
    const resultStr1 = typeof result1 === "string" ? result1 : result1.textResultForLlm;

    expect(resultStr1).toContain("Inserted");
    let fileContent = await readFile(testFile, "utf-8");
    expect(fileContent).toBe("inserted into empty file\n");

    // Reset the file to empty
    await writeFile(testFile, emptyContent);

    // Testing insert at position 1 (just beyond end of empty file)
    const result2 = await toolSet.str_replace_editor.callback({
        command: "insert",
        path: testFile,
        insert_line: 1,
        new_str: "inserted beyond empty file",
    });
    const resultStr2 = typeof result2 === "string" ? result2 : result2.textResultForLlm;

    expect(resultStr2).toContain("Inserted");
    fileContent = await readFile(testFile, "utf-8");
    // Note: When inserting beyond the end, the empty lines are added first
    expect(fileContent).toBe("\ninserted beyond empty file");

    // Reset the file to empty
    await writeFile(testFile, emptyContent);

    // Testing insert several lines beyond end of empty file
    const result3 = await toolSet.str_replace_editor.callback({
        command: "insert",
        path: testFile,
        insert_line: 3,
        new_str: "inserted far beyond empty file",
    });
    const resultStr3 = typeof result3 === "string" ? result3 : result3.textResultForLlm;

    expect(resultStr3).toContain("Inserted");
    fileContent = await readFile(testFile, "utf-8");
    // Note: When inserting beyond the end, the empty lines are added first
    expect(fileContent).toBe("\n\n\ninserted far beyond empty file");
});

test("str_replace_editor insert command - blank line - start", async () => {
    const testFile = join(TEST_DIR, "test.txt");
    const originalContent = "line 1\nline 2\nline 3";
    await writeFile(testFile, originalContent);

    const result = await toolSet.str_replace_editor.callback({
        command: "insert",
        path: testFile,
        insert_line: 0,
        new_str: "",
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    expect(resultStr).toContain("Inserted");
    const fileContent = await readFile(testFile, "utf-8");
    expect(fileContent).toBe("\nline 1\nline 2\nline 3");
    if (typeof result !== "string") {
        expect(result.sessionLog).toBe(`
diff --git a/tmp/swebench-test/test.txt b/tmp/swebench-test/test.txt
index 0000000..0000000 100644
--- a/tmp/swebench-test/test.txt
+++ b/tmp/swebench-test/test.txt
@@ -1,3 +1,4 @@
+
 line 1
 line 2
 line 3
`);
    }
});

test("str_replace_editor insert command - blank line - middle", async () => {
    const testFile = join(TEST_DIR, "test.txt");
    const originalContent = "line 1\nline 2\nline 3";
    await writeFile(testFile, originalContent);

    const result = await toolSet.str_replace_editor.callback({
        command: "insert",
        path: testFile,
        insert_line: 2,
        new_str: "",
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    expect(resultStr).toContain("Inserted");
    const fileContent = await readFile(testFile, "utf-8");
    expect(fileContent).toBe("line 1\nline 2\n\nline 3");
    if (typeof result !== "string") {
        expect(result.sessionLog).toBe(`
diff --git a/tmp/swebench-test/test.txt b/tmp/swebench-test/test.txt
index 0000000..0000000 100644
--- a/tmp/swebench-test/test.txt
+++ b/tmp/swebench-test/test.txt
@@ -1,3 +1,4 @@
 line 1
 line 2
+
 line 3
`);
    }
});

test("str_replace_editor insert command - blank line - end", async () => {
    const testFile = join(TEST_DIR, "test.txt");
    const originalContent = "line 1\nline 2\nline 3";
    await writeFile(testFile, originalContent);

    const result = await toolSet.str_replace_editor.callback({
        command: "insert",
        path: testFile,
        insert_line: 3,
        new_str: "",
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    expect(resultStr).toContain("Inserted");
    const fileContent = await readFile(testFile, "utf-8");
    expect(fileContent).toBe("line 1\nline 2\nline 3\n");
    if (typeof result !== "string") {
        expect(result.sessionLog).toBe(`
diff --git a/tmp/swebench-test/test.txt b/tmp/swebench-test/test.txt
index 0000000..0000000 100644
--- a/tmp/swebench-test/test.txt
+++ b/tmp/swebench-test/test.txt
@@ -1,3 +1,4 @@
 line 1
 line 2
 line 3
+
`);
    }
});

test("str_replace_editor - uses cwd callback to resolve relative paths", { timeout: 500000 }, async () => {
    // create sub directory
    const testDir = join(TEST_DIR, "tmp-test");
    await toolSet.bash.callback({ command: `mkdir -p ${testDir}` });
    await toolSet.bash.callback({ command: `cd ${testDir}` });

    // create test file
    const testFileName = "test.txt";
    const testFileContent = "THIS IS A TEST";
    await toolSet.bash.callback({
        command: `echo "${testFileContent}" > ${testFileName}`,
    });

    // make sure the file was created properly
    const testFile = join(testDir, testFileName);
    const fileContent = await readFile(testFile, "utf-8");
    expect(fileContent.trim()).toBe(testFileContent);

    // make sure str_replace_editor uses the cwd callback to resolve the path
    const result = await toolSet.str_replace_editor.callback({
        command: "view",
        path: `./${testFileName}`,
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    expect(resultStr.trim()).toContain(testFileContent);
});

test("str_replace_editor fails when path is not absolute", async () => {
    const result = await toolSet.str_replace_editor.callback({
        command: "view",
        path: "src/test.txt",
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;
    expect(resultStr).toContain('Path "src/test.txt" is not absolute.');
});

test("unrecognized command", async () => {
    await expect(
        toolSet.str_replace_editor.callback({
            command: "view_range",
            path: join(TEST_DIR, "test.txt"),
            view_range: [1, 3],
        }),
    ).rejects.toThrow("Unknown command: view_range");
});

test(
    "bash command times out for long running processes",
    async () => {
        const sleepOneSecondTooMany = (TEST_TIMEOUT + 1000) / 1000;
        console.log(`sleeping for ${sleepOneSecondTooMany} seconds`);
        const result = await toolSet.bash.callback({
            command: `echo "start" && sleep ${sleepOneSecondTooMany} && echo "stop_scenario"`,
        });
        const resultStr = typeof result === "string" ? result : result.error;
        expect(resultStr).toContain("start");
        expect(resultStr).not.toContain("stop_scenario");
        const timeoutMessage = `<waiting for command with sessionId: default-bash-session to exit timed out after ${TEST_TIMEOUT / 1000} seconds. try again with read_bash or abort it with stop_bash tool>`;
        expect(resultStr).toContain(timeoutMessage);
    },
    TEST_TIMEOUT * 2,
);

test("str_replace_editor view command fails gracefully when file path does not exist", async () => {
    const nonExistentFilePath = join(TEST_DIR, "non-existent-file.txt");

    const result = await toolSet.str_replace_editor.callback({
        command: "view",
        path: nonExistentFilePath,
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    expect(resultStr).toContain("Path");
    expect(resultStr).toContain("does not exist");
    expect(resultStr).toContain("non-existent-file.txt");

    if (typeof result !== "string") {
        expect(result.resultType).toBe("failure");
        expect(result.error).toContain("Path does not exist");
    }
});

test("str_replace_editor view command fails gracefully when directory path does not exist", async () => {
    const nonExistentDirPath = join(TEST_DIR, "non-existent-directory");

    const result = await toolSet.str_replace_editor.callback({
        command: "view",
        path: nonExistentDirPath,
    });
    const resultStr = typeof result === "string" ? result : result.textResultForLlm;

    expect(resultStr).toContain("Path");
    expect(resultStr).toContain("does not exist");
    expect(resultStr).toContain("non-existent-directory");

    if (typeof result !== "string") {
        expect(result.resultType).toBe("failure");
        expect(result.error).toContain("Path does not exist");
    }
});

async function getSweBenchTools(
    config: ToolConfig,
    maxRuns: number,
    settings: RuntimeSettings,
    logger: RunnerLogger,
    exec: DefaultExec,
) {
    return [
        ...(await fixTools(config, maxRuns, settings, logger, exec)),
        ...(await fixCommentTools(config, maxRuns, settings, logger, exec)),
    ];
}

// OpenAI models have a restriction on the length of tool names and descriptions (e.g. o3-mini, oswe, gpt4o)
// Ensure changes to tools meet this requirement via this test.
test.each(
    await getSweBenchTools(
        config,
        Number.MAX_VALUE,
        await getOrInitSettings(),
        new ConsoleLogger(),
        new DefaultExec(new ConsoleLogger()),
    ),
)("$name - specs, has name, description under 1024", (tool) => {
    // Name limit not in API spec, but user reported in issues.
    // See: https://community.openai.com/t/function-call-description-max-length/529902
    const maxNameLength = 64;
    expect(tool.name).toBeDefined();
    expect(tool.name).not.toBe("");
    expect(tool.name.length).toBeLessThan(maxNameLength);

    // Description limit not in API spec, but user reported in issues.
    // See: https://community.openai.com/t/tool-calling-api-upgrade-1024-char-limit-is-limiting/951951
    const maxDescriptionLength = 1024;
    expect(tool.description).toBeDefined();
    expect(tool.description).not.toBe("");
    expect(tool.description.length).toBeLessThan(maxDescriptionLength);
});

describe("str_replace_editor confirmation callback integration", () => {
    let mockConfirmationCallback: ToolConfirmationCallback;
    let capturedIntention: ToolIntention | null;
    let confirmationToolSet: ReturnType<typeof createToolSet>;

    // Type assertion helper to help narrow the types for the tests
    function assertEditIntention(intention: ToolIntention | null): asserts intention is {
        type: "edit";
        title: string;
        fileName: string;
        diff: string;
    } {
        expect(intention).not.toBeNull();
        expect(intention!.type).toBe("edit");
    }

    beforeEach(async () => {
        capturedIntention = null;
        mockConfirmationCallback = async (intention) => {
            capturedIntention = intention;
            return "proceed-once";
        };

        const confirmationConfig = {
            ...config,
            toolConfirmationCallback: mockConfirmationCallback,
        };

        const settings = await getOrInitSettings();
        const exec = new DefaultExec(logger);
        confirmationToolSet = createToolSet(await getSweBenchTools(confirmationConfig, 1, settings, logger, exec));
    });

    test("str_replace command should call confirmation with correct intention", async () => {
        const testFile = join(TEST_DIR, "test.txt");
        const originalContent = "line 1\nline 2\nline 3";
        await writeFile(testFile, originalContent);

        await confirmationToolSet.str_replace_editor.callback({
            command: "str_replace",
            path: testFile,
            old_str: "line 2",
            new_str: "modified line",
        });

        assertEditIntention(capturedIntention);
        expect(capturedIntention).toEqual({
            type: "edit",
            title: "Edit file",
            fileName: testFile,
            diff: expect.stringMatching(/-line 2[\s\S]*\+modified line/),
        });
    });

    test("create command should call confirmation with correct intention", async () => {
        const testFile = join(TEST_DIR, "new_file.txt");
        const content = "new file content\nline 2";

        await confirmationToolSet.str_replace_editor.callback({
            command: "create",
            path: testFile,
            file_text: content,
        });

        assertEditIntention(capturedIntention);
        expect(capturedIntention).toEqual({
            type: "edit",
            title: "Create file",
            fileName: testFile,
            diff: expect.stringMatching(/\+new file content[\s\S]*\+line 2/),
        });
    });

    test("insert command should call confirmation with correct intention", async () => {
        const testFile = join(TEST_DIR, "insert_test.txt");
        const originalContent = "line 1\nline 3";
        await writeFile(testFile, originalContent);

        await confirmationToolSet.str_replace_editor.callback({
            command: "insert",
            path: testFile,
            new_str: "inserted line",
            insert_line: 1,
        });

        expect(capturedIntention).toEqual({
            type: "edit",
            title: "Edit file",
            fileName: testFile,
            diff: expect.stringContaining("+inserted line"),
        });
    });

    test("should return rejected result when str_replace confirmation is rejected", async () => {
        const rejectCallback: ToolConfirmationCallback = async () => "rejected";
        const rejectConfig = {
            ...config,
            toolConfirmationCallback: rejectCallback,
        };
        const settings = await getOrInitSettings();
        const exec = new DefaultExec(logger);
        const rejectToolSet = createToolSet(await getSweBenchTools(rejectConfig, 1, settings, logger, exec));

        const testFile = join(TEST_DIR, "reject_test.txt");
        await writeFile(testFile, "original");

        const result = await rejectToolSet.str_replace_editor.callback({
            command: "str_replace",
            path: testFile,
            old_str: "original",
            new_str: "modified",
        });

        expect((result as ToolResultExpanded).resultType).toBe("rejected");
        expect((result as ToolResultExpanded).textResultForLlm).toBe(
            "The tool call required confirmation from the user, but the user rejected it. Await further instructions.",
        );

        // File should not have been modified
        const fileContent = await readFile(testFile, "utf-8");
        expect(fileContent).toBe("original");
    });

    test("should return previously-interrupted result when create confirmation is previously-interrupted", async () => {
        const interruptedCallback: ToolConfirmationCallback = async () => "previously-interrupted";
        const interruptedConfig = {
            ...config,
            toolConfirmationCallback: interruptedCallback,
        };
        const settings = await getOrInitSettings();
        const exec = new DefaultExec(logger);
        const interruptedToolSet = createToolSet(await getSweBenchTools(interruptedConfig, 1, settings, logger, exec));

        const testFile = join(TEST_DIR, "interrupted_test.txt");

        const result = await interruptedToolSet.str_replace_editor.callback({
            command: "create",
            path: testFile,
            file_text: "should not create",
        });

        expect((result as ToolResultExpanded).resultType).toBe("rejected");
        expect((result as ToolResultExpanded).textResultForLlm).toBe(
            "The user rejected an earlier tool call and does not want to take this action right now. Await further instructions.",
        );

        // File should not have been created
        expect(await pathExists(testFile)).toBe(false);
    });
});

test("ensure think tool is not created for unsupported models", async () => {
    const reasoningModel = await getSweBenchTools(
        {
            ...config,
            requireReasoning: false,
        },
        1,
        await getOrInitSettings(),
        logger,
        new DefaultExec(logger),
    );
    const thinkTool = reasoningModel.find((tool) => tool.name === "think");
    expect(thinkTool).toBeUndefined();

    const nonReasoningModel = await getSweBenchTools(
        {
            ...config,
            requireReasoning: true,
        },
        1,
        await getOrInitSettings(),
        logger,
        new DefaultExec(logger),
    );
    const thinkTool2 = nonReasoningModel.find((tool) => tool.name === "think");
    expect(thinkTool2).toBeDefined();
});
