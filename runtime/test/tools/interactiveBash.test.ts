/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { mkdtemp, realpath } from "fs/promises";
import { tmpdir } from "os";
import { join } from "path";
import { afterEach, beforeEach, describe, expect, it } from "vitest";
import { InteractiveBashSession as BashSession } from "../../src/tools/interactiveBash";

describe("BashSession", () => {
    let session: BashSession;
    let tempDir: string;

    beforeEach(async () => {
        // Create a temporary directory for testing
        tempDir = await mkdtemp(join(tmpdir(), "bash-session-test-"));
        // On some OS's, the tempDir may be a symlink, resolve it for testing cwd behavior
        tempDir = await realpath(tempDir);
        session = await BashSession.create(tempDir);
    }, 10000); // 10s for test setup

    afterEach(() => {
        session.shutdown();
    });

    it("executes a basic command", async () => {
        const result = await session.executeCommand('echo "hello world"', 5000);
        expect(result?.output.trim()).toBe("hello world");
        expect(result?.exitCode).toBe(0);
    }, 10000); // Allow 10s for basic test

    it("captures correct exit codes", async () => {
        const trueResult = await session.executeCommand("true", 5000);
        expect(trueResult?.exitCode).toBe(0);
        expect(trueResult?.output).toBe("");

        const falseResult = await session.executeCommand("false", 5000);
        expect(falseResult?.exitCode).toBe(1);
        expect(falseResult?.output).toBe("");

        // Test a command that fails with different exit code
        const result = await session.executeCommand("(exit 5)", 5000);
        expect(result?.exitCode).toBe(5);
    }, 10000);

    it("preserves environment variables between commands", async () => {
        await session.executeCommand('export TEST_VAR="test value"', 5000);
        const result = await session.executeCommand("echo $TEST_VAR", 5000);
        expect(result?.output.trim()).toBe("test value");
    }, 10000);

    it("captures stderr output", async () => {
        const result = await session.executeCommand('echo "error message" >&2', 5000);
        expect(result?.output.trim()).toBe("error message");
    }, 10000);

    it("handles command timeout", async () => {
        await expect(session.executeCommand("sleep 2", 100)).rejects.toThrow();
    }, 5000); // 5s for timeout test

    it("captures partial output on timeout", async () => {
        try {
            await session.executeCommand('echo "start"; sleep 2; echo "end"', 100);
            throw new Error("Should have thrown timeout error");
        } catch {
            const output = session.readOutput();
            expect(output?.output).toContain("start");
            expect(output?.output).not.toContain("end");
        }
    }, 5000); // 5s for timeout test

    it("handles multiple commands in sequence", async () => {
        await session.executeCommand("cd /tmp", 5000);
        const result = await session.executeCommand("pwd", 5000);
        expect(result?.output.trim()).toBe("/tmp");
    }, 10000);

    it("handles multiple commands in a single call", async () => {
        const result = await session.executeCommand("echo hello && echo goodbye", 5000);
        expect(result?.output.trim()).toBe("hello\ngoodbye");
    }, 10000);

    it("handles multi-line commands correctly", async () => {
        const result = await session.executeCommand(`\necho "A\nB\nC\n"`, 5000);
        expect(result?.output.trim()).toBe("A\nB\nC");
    }, 10000);

    it("handles special characters in output", async () => {
        const result = await session.executeCommand('echo -e "line1\\nline2\\nline3"', 5000);
        expect(result?.output.trim().split("\n")).toHaveLength(3);
    }, 10000);

    describe("cwd", () => {
        it("returns the current working directory", async () => {
            const cwd = await session.cwd();
            expect(cwd).toBe(tempDir);
        });

        it("returns new directory after cd", async () => {
            const newDirName = "testdir";
            await session.executeCommand(`mkdir -p ${newDirName}`, 5000);
            await session.executeCommand(`cd ./${newDirName}`, 5000);
            const cwd = await session.cwd();
            expect(cwd).toBe(join(tempDir, newDirName));
        });

        it("returns undefined when command times out", async () => {
            // Mock a timeout by setting an extremely short timeout
            const originalExecuteCommand = session.executeCommand.bind(session);

            // Simulate a command that takes too long
            session.executeCommand = async () => {
                await new Promise((resolve) => setTimeout(resolve, 200));
                throw new Error("Operation timed out");
            };

            const cwd = await session.cwd();
            expect(cwd).toBeUndefined();

            // Restore original method
            session.executeCommand = originalExecuteCommand;
        });
    });

    describe("session stats", () => {
        it("returns correct creation time", async () => {
            const beforeCreation = Date.now();
            const newSession = await BashSession.create(tempDir);
            const afterCreation = Date.now();

            const stats = newSession.getStats();

            expect(stats.createdAtMs).toBeGreaterThanOrEqual(beforeCreation);
            expect(stats.createdAtMs).toBeLessThanOrEqual(afterCreation);

            newSession.shutdown();
        });

        it("returns correct last used time after creation", async () => {
            const beforeCreation = Date.now();
            const newSession = await BashSession.create(tempDir);
            const afterCreation = Date.now();

            const stats = newSession.getStats();

            expect(stats.lastUsedAtMs).toBeGreaterThanOrEqual(beforeCreation);
            expect(stats.lastUsedAtMs).toBeLessThanOrEqual(afterCreation);

            newSession.shutdown();
        });

        it("updates last used time after executing a command", async () => {
            const initialStats = session.getStats();

            // Wait a bit to ensure timestamp difference
            await new Promise((resolve) => setTimeout(resolve, 10));

            await session.executeCommand('echo "test"', 5000);

            const updatedStats = session.getStats();

            expect(updatedStats.createdAtMs).toBe(initialStats.createdAtMs);
            expect(updatedStats.lastUsedAtMs).toBeGreaterThan(initialStats.lastUsedAtMs);
        });

        it("updates last used time after calling cwd()", async () => {
            const initialStats = session.getStats();

            // Wait a bit to ensure timestamp difference
            await new Promise((resolve) => setTimeout(resolve, 10));

            await session.cwd();

            const updatedStats = session.getStats();

            expect(updatedStats.createdAtMs).toBe(initialStats.createdAtMs);
            expect(updatedStats.lastUsedAtMs).toBeGreaterThan(initialStats.lastUsedAtMs);
        });

        it("updates last used time after tryExecuteAsyncCommand()", async () => {
            const initialStats = session.getStats();

            // Wait a bit to ensure timestamp difference
            await new Promise((resolve) => setTimeout(resolve, 10));

            const success = session.tryExecuteAsyncCommand('echo "async test"');
            expect(success).toBe(true);

            // Wait for the async command to complete
            let output;
            do {
                await new Promise((resolve) => setTimeout(resolve, 50));
                output = session.readOutput();
            } while (output?.exitCode === undefined);

            const updatedStats = session.getStats();

            expect(updatedStats.createdAtMs).toBe(initialStats.createdAtMs);
            expect(updatedStats.lastUsedAtMs).toBeGreaterThan(initialStats.lastUsedAtMs);
        });

        it("updates last used time after trySendInput()", async () => {
            // Start an async command that waits for input
            const success = session.tryExecuteAsyncCommand('read -p "Enter something: " input && echo "Got: $input"');
            expect(success).toBe(true);

            // Wait a bit to let the command start
            await new Promise((resolve) => setTimeout(resolve, 100));

            const initialStats = session.getStats();

            // Wait a bit to ensure timestamp difference
            await new Promise((resolve) => setTimeout(resolve, 10));

            // Send input
            const inputSent = session.trySendInput("test input\n");
            expect(inputSent).toBe(true);

            // Wait for command to complete
            let output;
            do {
                await new Promise((resolve) => setTimeout(resolve, 50));
                output = session.readOutput();
            } while (output?.exitCode === undefined);

            const updatedStats = session.getStats();

            expect(updatedStats.createdAtMs).toBe(initialStats.createdAtMs);
            expect(updatedStats.lastUsedAtMs).toBeGreaterThan(initialStats.lastUsedAtMs);
        });

        it("updates last used time after readOutput()", async () => {
            // Execute a command first
            await session.executeCommand('echo "test"', 5000);

            const initialStats = session.getStats();

            // Wait a bit to ensure timestamp difference
            await new Promise((resolve) => setTimeout(resolve, 10));

            session.readOutput();

            const updatedStats = session.getStats();

            expect(updatedStats.createdAtMs).toBe(initialStats.createdAtMs);
            expect(updatedStats.lastUsedAtMs).toBeGreaterThan(initialStats.lastUsedAtMs);
        });

        it("updates last used time after clearBuffer()", async () => {
            // Execute a command first to have some output
            await session.executeCommand('echo "test"', 5000);

            const initialStats = session.getStats();

            // Wait a bit to ensure timestamp difference
            await new Promise((resolve) => setTimeout(resolve, 10));

            session.clearBuffer();

            const updatedStats = session.getStats();

            expect(updatedStats.createdAtMs).toBe(initialStats.createdAtMs);
            expect(updatedStats.lastUsedAtMs).toBe(initialStats.lastUsedAtMs);
        });

        it("maintains consistent creation time across multiple operations", async () => {
            const initialStats = session.getStats();

            // Perform multiple operations
            await session.executeCommand('echo "test1"', 5000);
            await session.cwd();
            session.readOutput();

            const finalStats = session.getStats();

            // Creation time should never change
            expect(finalStats.createdAtMs).toBe(initialStats.createdAtMs);
            // Last used time should be updated
            expect(finalStats.lastUsedAtMs).toBeGreaterThan(initialStats.lastUsedAtMs);
        });
    });
});
