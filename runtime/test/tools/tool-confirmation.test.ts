/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { describe, expect, it } from "vitest";
import {
    alwaysProceedToolConfirmationCallback,
    llmMessageFromToolHalt,
    matchToolIntention,
    shouldHalt,
    ToolConfirmationResult,
    ToolIntention,
    toolResultFromHalt,
} from "../../src/tools";

describe("Tool Confirmation Infrastructure", () => {
    describe("ToolIntention matching", () => {
        it("should match exec intention type", () => {
            const intention: ToolIntention = {
                type: "exec",
                title: "Run command",
                command: "ls -la",
            };

            const result = matchToolIntention(intention, {
                onExec: (title, command) => ({ title, command, type: "exec" }),
                onEdit: () => ({ type: "edit" }),
                onMcp: () => ({ type: "mcp" }),
            });

            expect(result).toEqual({
                title: "Run command",
                command: "ls -la",
                type: "exec",
            });
        });

        it("should match edit intention type", () => {
            const intention: ToolIntention = {
                type: "edit",
                title: "Edit file",
                fileName: "test.txt",
                diff: "--- a/test.txt\n+++ b/test.txt\n@@ -1,1 +1,2 @@\n hello\n+world",
            };

            const result = matchToolIntention(intention, {
                onExec: () => ({ type: "exec" }),
                onEdit: (title, fileName, diff) => ({
                    title,
                    fileName,
                    diff,
                    type: "edit",
                }),
                onMcp: () => ({ type: "mcp" }),
            });

            expect(result).toEqual({
                title: "Edit file",
                fileName: "test.txt",
                diff: "--- a/test.txt\n+++ b/test.txt\n@@ -1,1 +1,2 @@\n hello\n+world",
                type: "edit",
            });
        });

        it("should match mcp intention type", () => {
            const intention: ToolIntention = {
                type: "mcp",
                toolName: "file_reader",
                args: { path: "/test/file.txt" },
            };

            const result = matchToolIntention(intention, {
                onExec: () => ({ type: "exec" }),
                onEdit: () => ({ type: "edit" }),
                onMcp: (toolName, args) => ({
                    toolName,
                    args,
                    type: "mcp",
                }),
            });

            expect(result).toEqual({
                toolName: "file_reader",
                args: { path: "/test/file.txt" },
                type: "mcp",
            });
        });
    });

    describe("ToolConfirmationResult handling", () => {
        describe("shouldHalt", () => {
            it("should return true for rejected result", () => {
                const result: ToolConfirmationResult = "rejected";
                expect(shouldHalt(result)).toBe(true);
            });

            it("should return true for previously-interrupted result", () => {
                const result: ToolConfirmationResult = "previously-interrupted";
                expect(shouldHalt(result)).toBe(true);
            });

            it("should return false for proceed-once result", () => {
                const result: ToolConfirmationResult = "proceed-once";
                expect(shouldHalt(result)).toBe(false);
            });
        });

        describe("llmMessageFromToolHalt", () => {
            it("should return appropriate message for rejected result", () => {
                const message = llmMessageFromToolHalt("rejected");
                expect(message).toBe(
                    "The tool call required confirmation from the user, but the user rejected it. Await further instructions.",
                );
            });

            it("should return appropriate message for previously-interrupted result", () => {
                const message = llmMessageFromToolHalt("previously-interrupted");
                expect(message).toBe(
                    "The user rejected an earlier tool call and does not want to take this action right now. Await further instructions.",
                );
            });
        });

        describe("toolResultFromHalt", () => {
            it("should create proper tool result for rejected confirmation", () => {
                const result = toolResultFromHalt("rejected");

                expect(result.resultType).toBe("rejected");
                expect(result.textResultForLlm).toBe(
                    "The tool call required confirmation from the user, but the user rejected it. Await further instructions.",
                );
                expect(result.error).toBe(
                    "The tool call required confirmation from the user, but the user rejected it. Await further instructions.",
                );
                expect(result.sessionLog).toBe(
                    "<error>The tool call required confirmation from the user, but the user rejected it. Await further instructions.</error>",
                );
                expect(result.toolTelemetry).toEqual({});
            });

            it("should create proper tool result for previously-interrupted confirmation", () => {
                const result = toolResultFromHalt("previously-interrupted");

                expect(result.resultType).toBe("rejected");
                expect(result.textResultForLlm).toBe(
                    "The user rejected an earlier tool call and does not want to take this action right now. Await further instructions.",
                );
                expect(result.error).toBe(
                    "The user rejected an earlier tool call and does not want to take this action right now. Await further instructions.",
                );
                expect(result.sessionLog).toBe(
                    "<error>The user rejected an earlier tool call and does not want to take this action right now. Await further instructions.</error>",
                );
                expect(result.toolTelemetry).toEqual({});
            });
        });
    });

    describe("alwaysProceedToolConfirmationCallback", () => {
        it("should always return proceed-once", async () => {
            const intention: ToolIntention = {
                type: "exec",
                title: "Run command",
                command: "echo hello",
            };

            const result = await alwaysProceedToolConfirmationCallback(intention);
            expect(result).toBe("proceed-once");
        });
    });
});
