/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { mkdir, readFile, rm, writeFile } from "fs/promises";
import { join } from "path";
import { afterEach, beforeEach, describe, expect, test, vi } from "vitest";
import {
    JsonFileMemoryStrategy,
    Memory,
    MemoryContext,
    READ_MEMORY_TOOL_NAME,
    STORE_MEMORY_TOOL_NAME,
    isMemory,
    isQuestion,
} from "../../src/tools/memory";
import { Client } from "../../src/model/client";
import { Event, ResponseEvent } from "../../src/model/event";
import { Tool, ToolResult, ToolResultExpanded } from "../../src/tools";
import type { ChatCompletionMessageParam } from "openai/resources/chat/completions";

// Helper function to create test response events
function createResponseEvent(content: string): ResponseEvent {
    return {
        kind: "response" as const,
        response: {
            role: "assistant" as const,
            content,
            refusal: null,
        },
    };
}

// Helper function for asserting ToolResultExpanded
function assertToolResult(result: ToolResult): asserts result is ToolResultExpanded {
    expect(typeof result).toBe("object");
    expect(result).toHaveProperty("resultType");
    expect(result).toHaveProperty("textResultForLlm");
}

// Helper function to get ToolResultExpanded from ToolResult
function getToolResult(result: ToolResult): ToolResultExpanded {
    assertToolResult(result);
    return result;
}

const TEST_DIR_BASE = "memory-test";
const TEST_DIR = process.platform === "win32" ? `C:\\tmp\\${TEST_DIR_BASE}` : `/tmp/${TEST_DIR_BASE}`;
const TEST_REPO_ROOT = join(TEST_DIR, "repo");

// Mock the SecretFilter with proper spy functionality
const mockSecretFilter = {
    filterSecretsFromObj: vi.fn((obj) => obj),
};

vi.mock("../../src/helpers/SecretFilter", () => ({
    SecretFilter: {
        getInstance: vi.fn(() => mockSecretFilter),
    },
}));

// No longer need to mock memoryConsolidationPrompt - we test behavior instead

// Mock Client
class MockClient implements Partial<Client> {
    public readonly model = "test-model";
    private mockResponses: Event[] = [];

    setMockResponses(responses: Event[]) {
        this.mockResponses = responses;
    }

    async *getCompletionWithTools(
        _prompt: string,
        _messages: ChatCompletionMessageParam[],
        _tools: Tool[],
    ): AsyncGenerator<Event> {
        for (const response of this.mockResponses) {
            yield response;
        }
    }
}

describe("Memory System", () => {
    let mockClient: MockClient;
    let testMemory: Memory;

    beforeEach(async () => {
        // Create test directory
        await mkdir(TEST_DIR, { recursive: true });
        await mkdir(TEST_REPO_ROOT, { recursive: true });

        mockClient = new MockClient();
        testMemory = {
            subject: "testing",
            fact: "Use Jest for unit testing",
            source: "test/example.test.js:1",
            reason: "This fact is important for maintaining consistent testing practices across the codebase. It will help future developers choose the right testing framework.",
            category: "general",
        };
    });

    afterEach(async () => {
        // Clean up test directory
        try {
            await rm(TEST_DIR, { recursive: true });
        } catch {
            // Ignore cleanup errors
        }

        // Reset mocks
        vi.clearAllMocks();
        mockSecretFilter.filterSecretsFromObj.mockClear();
    });

    describe("Memory Type Validation", () => {
        test("isMemory validates correct memory object", () => {
            expect(isMemory(testMemory)).toBe(true);
        });

        test("isMemory rejects objects with missing properties", () => {
            const incompleteMemory = { ...testMemory };
            delete (incompleteMemory as Record<string, unknown>).subject;

            expect(isMemory(incompleteMemory)).toBe(false);
        });

        test("isMemory rejects non-object values", () => {
            expect(isMemory(null)).toBe(false);
            expect(isMemory(undefined)).toBe(false);
            expect(isMemory("string")).toBe(false);
            expect(isMemory(123)).toBe(false);
            expect(isMemory([])).toBe(false);
        });

        test("isMemory rejects objects with wrong property types", () => {
            const invalidMemory = {
                ...testMemory,
                subject: 123, // Should be string
            };

            expect(isMemory(invalidMemory)).toBe(false);
        });

        test("isMemory rejects objects with null values", () => {
            const memoryWithNull = { ...testMemory, subject: null };

            expect(isMemory(memoryWithNull)).toBe(false);
        });

        test("isMemory accepts objects with additional properties", () => {
            const memoryWithExtra = { ...testMemory, extraProperty: "value" };

            // isMemory should still return true as it only checks required properties exist and are strings
            expect(isMemory(memoryWithExtra)).toBe(true);
        });
    });

    describe("Question Type Validation", () => {
        test("isQuestion validates correct question object", () => {
            const validQuestion = {
                question: "What testing framework should I use?",
            };
            expect(isQuestion(validQuestion)).toBe(true);
        });

        test("isQuestion rejects objects with missing question property", () => {
            const invalidQuestion = { notQuestion: "test" };
            expect(isQuestion(invalidQuestion)).toBe(false);
        });

        test("isQuestion rejects non-object values", () => {
            expect(isQuestion(null)).toBe(false);
            expect(isQuestion(undefined)).toBe(false);
            expect(isQuestion("string")).toBe(false);
            expect(isQuestion(123)).toBe(false);
            expect(isQuestion([])).toBe(false);
        });

        test("isQuestion rejects objects with wrong property type", () => {
            const invalidQuestion = { question: 123 };
            expect(isQuestion(invalidQuestion)).toBe(false);
        });

        test("isQuestion accepts objects with additional properties", () => {
            const questionWithExtra = {
                question: "test question",
                extraProperty: "value",
            };
            expect(isQuestion(questionWithExtra)).toBe(true);
        });

        test("isQuestion accepts empty string question", () => {
            const emptyQuestion = { question: "" };
            expect(isQuestion(emptyQuestion)).toBe(true);
        });
    });

    describe("MemoryContext", () => {
        let memoryContext: MemoryContext;
        let mockStrategy: {
            storeMemory: ReturnType<typeof vi.fn>;
            readMemory: ReturnType<typeof vi.fn>;
            shutdown: ReturnType<typeof vi.fn>;
        };

        beforeEach(() => {
            mockStrategy = {
                storeMemory: vi.fn().mockResolvedValue({
                    resultType: "success",
                    textResultForLlm: "Memory stored successfully.",
                    toolTelemetry: {},
                }),
                readMemory: vi.fn().mockResolvedValue({
                    resultType: "success",
                    textResultForLlm: "Memory retrieved.",
                    toolTelemetry: {},
                }),
                shutdown: vi.fn().mockResolvedValue({
                    kind: "telemetry",
                    telemetry: {
                        event: "memory_tool",
                        properties: {},
                        restrictedProperties: {},
                        metrics: {
                            initialMemoriesCount: 0,
                            addedMemoriesCount: 1,
                            finalMemoriesBeforeOptimizationCount: 1,
                            finalMemoriesAfterOptimizationCount: 1,
                        },
                    },
                }),
            };

            memoryContext = new MemoryContext(mockStrategy);
        });

        test("getStoreMemory returns correct tool configuration", () => {
            const tool = memoryContext.getStoreMemory();

            expect(tool.name).toBe(STORE_MEMORY_TOOL_NAME);
            expect(tool.description).toContain("Store a fact about the codebase");
            expect(tool.input_schema.type).toBe("object");
            expect(tool.input_schema.required).toEqual(["subject", "fact", "source", "reason", "category"]);
            expect(tool.safeForTelemetry).toBe(true);
            expect(typeof tool.callback).toBe("function");
            expect(typeof tool.shutdown).toBe("function");
        });

        test("storeMemory calls SecretFilter before storing", async () => {
            const tool = memoryContext.getStoreMemory();

            await tool.callback(testMemory, { client: mockClient });

            expect(mockSecretFilter.filterSecretsFromObj).toHaveBeenCalledWith(testMemory);
        });

        test("storeMemory validates input and calls strategy on success", async () => {
            const tool = memoryContext.getStoreMemory();

            const result = await tool.callback(testMemory, {
                client: mockClient,
            });

            expect(mockStrategy.storeMemory).toHaveBeenCalledWith(testMemory, mockClient);
            expect(typeof result).toBe("object");
            expect(getToolResult(result).resultType).toBe("success");
            expect(getToolResult(result).textResultForLlm).toBe("Memory stored successfully.");
        });

        test("storeMemory returns failure for invalid input", async () => {
            const tool = memoryContext.getStoreMemory();
            const invalidInput = { invalid: "data" };

            const result = await tool.callback(invalidInput, {
                client: mockClient,
            });

            expect(typeof result).toBe("object");
            expect(getToolResult(result).resultType).toBe("failure");
            expect(getToolResult(result).textResultForLlm).toContain("Invalid inputs for store_memory tool");
            expect(mockStrategy.storeMemory).not.toHaveBeenCalled();
        });

        test("shutdown calls strategy shutdown", async () => {
            const tool = memoryContext.getStoreMemory();

            const result = await tool.shutdown!();

            expect(mockStrategy.shutdown).toHaveBeenCalled();
            expect(result).toEqual({
                kind: "telemetry",
                telemetry: {
                    event: "memory_tool",
                    properties: {},
                    restrictedProperties: {},
                    metrics: {
                        initialMemoriesCount: 0,
                        addedMemoriesCount: 1,
                        finalMemoriesBeforeOptimizationCount: 1,
                        finalMemoriesAfterOptimizationCount: 1,
                    },
                },
            });
        });

        test("shutdown without client handles gracefully", async () => {
            const strategy = new JsonFileMemoryStrategy(TEST_REPO_ROOT);

            // Don't call storeMemory, so no client is set
            const telemetry = await strategy.shutdown();

            expect(telemetry.kind).toBe("telemetry");
            expect(telemetry.telemetry.metrics.finalMemoriesAfterOptimizationCount).toBe(0); // No optimization without client

            // Should still write (empty) file
            const memoryFilePath = join(TEST_REPO_ROOT, ".github", "copilot-memories.jsonl");
            const fileContent = await readFile(memoryFilePath, "utf8");
            expect(fileContent).toBe("");
        });

        test("telemetry tracks memory counts correctly throughout lifecycle", async () => {
            // Create pre-existing memories
            const existingMemories = [
                { ...testMemory, subject: "existing1" },
                { ...testMemory, subject: "existing2" },
            ];

            await mkdir(join(TEST_REPO_ROOT, ".github"), { recursive: true });
            const memoryFilePath = join(TEST_REPO_ROOT, ".github", "copilot-memories.jsonl");
            await writeFile(memoryFilePath, existingMemories.map((m) => JSON.stringify(m)).join("\n"), "utf8");

            const strategy = new JsonFileMemoryStrategy(TEST_REPO_ROOT);

            // Store multiple new memories
            await strategy.storeMemory({ ...testMemory, subject: "new1" }, mockClient);
            await strategy.storeMemory({ ...testMemory, subject: "new2" }, mockClient);
            await strategy.storeMemory({ ...testMemory, subject: "new3" }, mockClient);

            // Mock consolidation to return fewer memories (simulating deduplication)
            const consolidatedMemories = [
                { ...testMemory, subject: "consolidated1" },
                { ...testMemory, subject: "consolidated2" },
            ];

            mockClient.setMockResponses([
                createResponseEvent(consolidatedMemories.map((m) => JSON.stringify(m)).join("\n")),
            ]);

            const telemetry = await strategy.shutdown();

            // Verify all counts are tracked correctly
            expect(telemetry.telemetry.metrics.initialMemoriesCount).toBe(2); // existing
            expect(telemetry.telemetry.metrics.addedMemoriesCount).toBe(3); // new memories added
            expect(telemetry.telemetry.metrics.finalMemoriesBeforeOptimizationCount).toBe(5); // 2 + 3
            expect(telemetry.telemetry.metrics.finalMemoriesAfterOptimizationCount).toBe(2); // consolidated
        });
    });

    describe("JsonFileMemoryStrategy", () => {
        let strategy: JsonFileMemoryStrategy;
        let memoryFilePath: string;

        beforeEach(() => {
            strategy = new JsonFileMemoryStrategy(TEST_REPO_ROOT);
            memoryFilePath = join(TEST_REPO_ROOT, ".github", "copilot-memories.jsonl");
        });

        test("constructor initializes correctly", () => {
            expect(strategy).toBeInstanceOf(JsonFileMemoryStrategy);
        });

        test("storeMemory stores memory successfully", async () => {
            const result = await strategy.storeMemory(testMemory, mockClient);

            expect(typeof result).toBe("object");
            expect(getToolResult(result).resultType).toBe("success");
            expect(getToolResult(result).textResultForLlm).toBe("Memory stored successfully.");
        });

        test("storeMemory creates multiple memories", async () => {
            const memory1 = { ...testMemory, subject: "logging" };
            const memory2 = { ...testMemory, subject: "authentication" };

            await strategy.storeMemory(memory1, mockClient);
            await strategy.storeMemory(memory2, mockClient);

            // Both should succeed
            const result = await strategy.storeMemory(testMemory, mockClient);
            expect(typeof result).toBe("object");
            expect(getToolResult(result).resultType).toBe("success");
        });

        test("readMemory returns success with valid question", async () => {
            // Store a memory first
            await strategy.storeMemory(testMemory, mockClient);

            // Mock client response for memory lookup
            mockClient.setMockResponses([
                createResponseEvent(
                    "Use Jest for unit testing as documented in test/example.test.js. This is the recommended testing framework for this codebase.",
                ),
            ]);

            const result = await strategy.readMemory({ question: "What testing framework should I use?" }, mockClient);

            expect(typeof result).toBe("object");
            expect(getToolResult(result).resultType).toBe("success");
            expect(getToolResult(result).textResultForLlm).toContain("Jest");
            expect(getToolResult(result).toolTelemetry.metrics?.retrievedMemoriesCount).toBe(1);
        });

        test("readMemory returns failure when no memories exist", async () => {
            const result = await strategy.readMemory({ question: "What testing framework should I use?" }, mockClient);

            expect(typeof result).toBe("object");
            expect(getToolResult(result).resultType).toBe("failure");
            expect(getToolResult(result).textResultForLlm).toBe("No memories found.");
        });

        test("readMemory handles multiple memories correctly", async () => {
            // Store multiple memories
            const memory1 = {
                ...testMemory,
                subject: "testing",
                fact: "Use Jest for unit testing",
            };
            const memory2 = {
                ...testMemory,
                subject: "building",
                fact: "Use npm run build to build the project",
            };

            await strategy.storeMemory(memory1, mockClient);
            await strategy.storeMemory(memory2, mockClient);

            // Mock client response that incorporates multiple memories
            mockClient.setMockResponses([
                createResponseEvent("For testing, use Jest as documented. For building, use npm run build command."),
            ]);

            const result = await strategy.readMemory({ question: "How do I test and build this project?" }, mockClient);

            expect(typeof result).toBe("object");
            expect(getToolResult(result).resultType).toBe("success");
            expect(getToolResult(result).textResultForLlm).toContain("Jest");
            expect(getToolResult(result).textResultForLlm).toContain("npm run build");
            expect(getToolResult(result).toolTelemetry.metrics?.retrievedMemoriesCount).toBe(2);
        });

        test("readMemory tracks telemetry correctly", async () => {
            // Store memories
            await strategy.storeMemory(testMemory, mockClient);
            await strategy.storeMemory({ ...testMemory, subject: "another" }, mockClient);

            mockClient.setMockResponses([createResponseEvent("Test response")]);

            const result = await strategy.readMemory({ question: "test question" }, mockClient);

            expect(getToolResult(result).toolTelemetry.metrics?.retrievedMemoriesCount).toBe(2);
        });

        test("readMemory handles client response with multiple lines", async () => {
            await strategy.storeMemory(testMemory, mockClient);

            mockClient.setMockResponses([
                createResponseEvent("Line 1 of response\nLine 2 of response\nLine 3 of response"),
            ]);

            const result = await strategy.readMemory({ question: "test question" }, mockClient);

            expect(typeof result).toBe("object");
            expect(getToolResult(result).resultType).toBe("success");
            expect(getToolResult(result).textResultForLlm).toContain("Line 1");
            expect(getToolResult(result).textResultForLlm).toContain("Line 2");
            expect(getToolResult(result).textResultForLlm).toContain("Line 3");
        });

        test("readMemory handles non-response kinds gracefully", async () => {
            await strategy.storeMemory(testMemory, mockClient);

            mockClient.setMockResponses([
                {
                    kind: "turn_started",
                    model: "test-model",
                    modelInfo: {},
                    turn: 1,
                    timestampMs: Date.now(),
                },
                createResponseEvent("Actual response content"),
            ]);

            const result = await strategy.readMemory({ question: "test question" }, mockClient);

            expect(typeof result).toBe("object");
            expect(getToolResult(result).resultType).toBe("success");
            expect(getToolResult(result).textResultForLlm).toBe("Actual response content");
        });

        test("readMemory handles empty response content", async () => {
            await strategy.storeMemory(testMemory, mockClient);

            mockClient.setMockResponses([createResponseEvent("")]);

            const result = await strategy.readMemory({ question: "test question" }, mockClient);

            expect(typeof result).toBe("object");
            expect(getToolResult(result).resultType).toBe("success");
            expect(getToolResult(result).textResultForLlm).toBe("");
        });

        test("shutdown writes memories to file", async () => {
            await strategy.storeMemory(testMemory, mockClient);

            // Mock the client response for memory consolidation
            mockClient.setMockResponses([createResponseEvent(JSON.stringify(testMemory))]);

            const telemetry = await strategy.shutdown();

            expect(telemetry.kind).toBe("telemetry");
            expect(telemetry.telemetry.event).toBe("memory_tool");
            expect(telemetry.telemetry.metrics.finalMemoriesBeforeOptimizationCount).toBe(1);

            // Check that file was created
            const fileContent = await readFile(memoryFilePath, "utf8");
            expect(fileContent).toContain(testMemory.subject);
        });

        test("shutdown handles file write errors gracefully", async () => {
            await strategy.storeMemory(testMemory, mockClient);

            // Mock the client to throw an error during optimization
            mockClient.setMockResponses([]);

            const telemetry = await strategy.shutdown();

            expect(telemetry.kind).toBe("telemetry");
            expect(telemetry.telemetry.event).toBe("memory_tool");
            // Should still have telemetry even with errors
        });

        test("ensureInitialized loads existing memories from file", async () => {
            // Create a memory file with existing content
            const existingMemories = [
                { ...testMemory, subject: "existing1" },
                { ...testMemory, subject: "existing2" },
            ];

            await mkdir(join(TEST_REPO_ROOT, ".github"), { recursive: true });
            await writeFile(memoryFilePath, existingMemories.map((m) => JSON.stringify(m)).join("\n"), "utf8");

            // Create new strategy to test initialization
            const newStrategy = new JsonFileMemoryStrategy(TEST_REPO_ROOT);

            // This should trigger initialization
            await newStrategy.storeMemory(testMemory, mockClient);

            // Mock consolidation response
            mockClient.setMockResponses([
                createResponseEvent(
                    existingMemories
                        .concat([testMemory])
                        .map((m) => JSON.stringify(m))
                        .join("\n"),
                ),
            ]);

            const telemetry = await newStrategy.shutdown();

            // Should have loaded 2 existing memories plus 1 new one
            expect(telemetry.telemetry.metrics.initialMemoriesCount).toBe(2);
            expect(telemetry.telemetry.metrics.finalMemoriesBeforeOptimizationCount).toBe(3);
        });

        test("ensureInitialized handles missing file gracefully", async () => {
            // Don't create any file
            const newStrategy = new JsonFileMemoryStrategy(TEST_REPO_ROOT);

            await newStrategy.storeMemory(testMemory, mockClient);

            mockClient.setMockResponses([createResponseEvent(JSON.stringify(testMemory))]);

            const telemetry = await newStrategy.shutdown();

            // Should handle missing file gracefully - when no file exists, initialMemoriesCount is 0
            expect(telemetry.telemetry.metrics.initialMemoriesCount).toBe(0);
        });

        test("optimizeMemories consolidates memories using client", async () => {
            await strategy.storeMemory(testMemory, mockClient);

            const consolidatedMemory = {
                ...testMemory,
                fact: "Consolidated fact",
            };
            mockClient.setMockResponses([createResponseEvent(JSON.stringify(consolidatedMemory))]);

            const telemetry = await strategy.shutdown();

            // Verify that optimization ran by checking telemetry
            expect(telemetry.telemetry.metrics.finalMemoriesBeforeOptimizationCount).toBe(1);
            expect(telemetry.telemetry.metrics.finalMemoriesAfterOptimizationCount).toBe(1);

            // Check the file contains the consolidated memory
            const fileContent = await readFile(memoryFilePath, "utf8");
            expect(fileContent).toContain("Consolidated fact");
        });

        test("optimizeMemories filters invalid memories from response", async () => {
            await strategy.storeMemory(testMemory, mockClient);

            // Mix valid and invalid memories in response
            const validMemory = {
                ...testMemory,
                fact: "Valid consolidated fact",
            };
            const invalidMemory = { subject: "incomplete" }; // Missing required fields

            mockClient.setMockResponses([
                createResponseEvent(JSON.stringify(validMemory) + "\n" + JSON.stringify(invalidMemory)),
            ]);

            await strategy.shutdown();

            // Should only keep valid memories
            const fileContent = await readFile(memoryFilePath, "utf8");
            expect(fileContent).toContain("Valid consolidated fact");
            expect(fileContent).not.toContain("incomplete");
        });

        test("getMemoryFilePath creates .github directory", async () => {
            await strategy.storeMemory(testMemory, mockClient);

            mockClient.setMockResponses([createResponseEvent(JSON.stringify(testMemory))]);

            await strategy.shutdown();

            // Verify .github directory was created
            const githubDir = join(TEST_REPO_ROOT, ".github");
            try {
                await readFile(githubDir);
                // Should be able to read directory
            } catch (error: unknown) {
                // If we can't read it as a file, it should be because it's a directory
                expect((error as { code?: string }).code).not.toBe("ENOENT");
            }
        });

        test("multiple store and shutdown cycles work correctly", async () => {
            // First cycle
            await strategy.storeMemory({ ...testMemory, subject: "cycle1" }, mockClient);

            mockClient.setMockResponses([
                createResponseEvent(
                    JSON.stringify({
                        ...testMemory,
                        subject: "cycle1",
                    }),
                ),
            ]);

            let telemetry = await strategy.shutdown();
            expect(telemetry.telemetry.metrics.finalMemoriesBeforeOptimizationCount).toBe(1);

            // Second cycle with new strategy
            const strategy2 = new JsonFileMemoryStrategy(TEST_REPO_ROOT);
            await strategy2.storeMemory({ ...testMemory, subject: "cycle2" }, mockClient);

            mockClient.setMockResponses([
                createResponseEvent(
                    JSON.stringify({
                        ...testMemory,
                        subject: "cycle1",
                    }) +
                        "\n" +
                        JSON.stringify({
                            ...testMemory,
                            subject: "cycle2",
                        }),
                ),
            ]);

            telemetry = await strategy2.shutdown();
            expect(telemetry.telemetry.metrics.initialMemoriesCount).toBe(1); // Loaded from previous cycle
            expect(telemetry.telemetry.metrics.finalMemoriesBeforeOptimizationCount).toBe(2); // Previous + new
        });

        test("shutdown preserves existing memories when storeMemory is never called", async () => {
            // Create a memory file with existing content
            const existingMemories = [
                {
                    ...testMemory,
                    subject: "existing1",
                    fact: "First existing memory",
                },
                {
                    ...testMemory,
                    subject: "existing2",
                    fact: "Second existing memory",
                },
            ];

            await mkdir(join(TEST_REPO_ROOT, ".github"), { recursive: true });
            const memoryFilePath = join(TEST_REPO_ROOT, ".github", "copilot-memories.jsonl");
            await writeFile(memoryFilePath, existingMemories.map((m) => JSON.stringify(m)).join("\n"), "utf8");

            // Create new strategy but never call storeMemory (so no client is set)
            const strategy = new JsonFileMemoryStrategy(TEST_REPO_ROOT);

            // Call shutdown directly without storing any new memories
            const telemetry = await strategy.shutdown();

            // Verify existing memories were preserved
            expect(telemetry.telemetry.metrics.initialMemoriesCount).toBe(2);
            expect(telemetry.telemetry.metrics.addedMemoriesCount).toBe(0);
            expect(telemetry.telemetry.metrics.finalMemoriesBeforeOptimizationCount).toBe(2);
            expect(telemetry.telemetry.metrics.finalMemoriesAfterOptimizationCount).toBe(2); // No optimization, memories preserved

            // Verify the file still contains the original memories
            const fileContent = await readFile(memoryFilePath, "utf8");
            expect(fileContent).toContain("First existing memory");
            expect(fileContent).toContain("Second existing memory");

            // Verify both memories are still in the file
            const lines = fileContent.trim().split("\n");
            expect(lines).toHaveLength(2);
            expect(JSON.parse(lines[0])).toEqual(existingMemories[0]);
            expect(JSON.parse(lines[1])).toEqual(existingMemories[1]);
        });
    });

    describe("Integration Tests", () => {
        test("MemoryContext with JsonFileMemoryStrategy end-to-end", async () => {
            const strategy = new JsonFileMemoryStrategy(TEST_REPO_ROOT);
            const context = new MemoryContext(strategy);
            const tool = context.getStoreMemory();

            // Store a memory
            const result = await tool.callback(testMemory, {
                client: mockClient,
            });
            expect(typeof result).toBe("object");
            expect(getToolResult(result).resultType).toBe("success");

            // Mock consolidation response
            mockClient.setMockResponses([createResponseEvent(JSON.stringify(testMemory))]);

            // Shutdown and verify
            const telemetry = await tool.shutdown!();
            expect(telemetry).toBeDefined();
            if (telemetry) {
                expect(telemetry.kind).toBe("telemetry");
            }

            // Verify file was written
            const memoryFilePath = join(TEST_REPO_ROOT, ".github", "copilot-memories.jsonl");
            const fileContent = await readFile(memoryFilePath, "utf8");
            expect(JSON.parse(fileContent)).toEqual(testMemory);
        });

        test("Memory system handles concurrent operations", async () => {
            const strategy = new JsonFileMemoryStrategy(TEST_REPO_ROOT);

            // Store multiple memories concurrently
            const memories = [
                { ...testMemory, subject: "concurrent1" },
                { ...testMemory, subject: "concurrent2" },
                { ...testMemory, subject: "concurrent3" },
            ];

            const storePromises = memories.map((memory) => strategy.storeMemory(memory, mockClient));
            const results = await Promise.all(storePromises);

            // All should succeed
            results.forEach((result) => {
                expect(typeof result).toBe("object");
                expect(getToolResult(result).resultType).toBe("success");
            });

            // Mock consolidation
            mockClient.setMockResponses([createResponseEvent(memories.map((m) => JSON.stringify(m)).join("\n"))]);

            const telemetry = await strategy.shutdown();
            expect(telemetry.telemetry.metrics.finalMemoriesBeforeOptimizationCount).toBe(3);
        });
    });

    describe("Error Handling", () => {
        test("handles malformed JSON in existing memory file", async () => {
            const memoryFilePath = join(TEST_REPO_ROOT, ".github", "copilot-memories.jsonl");
            await mkdir(join(TEST_REPO_ROOT, ".github"), { recursive: true });
            await writeFile(memoryFilePath, 'invalid json\n{"valid": "json"}', "utf8");

            const strategy = new JsonFileMemoryStrategy(TEST_REPO_ROOT);

            // When there's malformed JSON, initialization fails and storeMemory returns failure
            const result = await strategy.storeMemory(testMemory, mockClient);
            expect(typeof result).toBe("object");
            expect(getToolResult(result).resultType).toBe("failure");
            expect(getToolResult(result).textResultForLlm).toContain(
                "Unable to store memory due to memory initialization error",
            );

            // Mock consolidation response
            mockClient.setMockResponses([createResponseEvent(JSON.stringify(testMemory))]);

            const telemetry = await strategy.shutdown();
            // The telemetry should show initialMemoriesCount as -1 due to the JSON parse error
            expect(telemetry.telemetry.metrics.initialMemoriesCount).toBe(-1);
        });

        test("handles empty lines in existing memory file", async () => {
            const memoryFilePath = join(TEST_REPO_ROOT, ".github", "copilot-memories.jsonl");
            await mkdir(join(TEST_REPO_ROOT, ".github"), { recursive: true });

            // Create file with empty lines that should be skipped during parsing
            const validMemory = { ...testMemory, subject: "valid" };
            await writeFile(memoryFilePath, `${JSON.stringify(validMemory)}\n\n\n`, "utf8");

            const strategy = new JsonFileMemoryStrategy(TEST_REPO_ROOT);

            // Should fail because JSON.parse('') throws an error
            const result = await strategy.storeMemory(testMemory, mockClient);
            expect(typeof result).toBe("object");
            expect(getToolResult(result).resultType).toBe("success");

            const telemetry = await strategy.shutdown();
            expect(telemetry.telemetry.metrics.initialMemoriesCount).toBe(1);
        });

        test("handles client errors during optimization", async () => {
            const strategy = new JsonFileMemoryStrategy(TEST_REPO_ROOT);
            await strategy.storeMemory(testMemory, mockClient);

            // Mock client to throw error
            const errorClient = {
                model: "test-model",
                // eslint-disable-next-line require-yield
                async *getCompletionWithTools() {
                    throw new Error("Client error");
                },
            } satisfies Client;

            const errorStrategy = new JsonFileMemoryStrategy(TEST_REPO_ROOT);
            await errorStrategy.storeMemory(testMemory, errorClient);

            // Should handle error gracefully
            const telemetry = await errorStrategy.shutdown();
            expect(telemetry.kind).toBe("telemetry");
            expect(telemetry.telemetry.restrictedProperties.writeMemoriesError).toContain("Client error");
        });

        test("handles file system permission errors", async () => {
            // Create a read-only directory to simulate permission errors
            const readOnlyDir = join(TEST_DIR, "readonly");
            await mkdir(readOnlyDir, { recursive: true });

            const strategy = new JsonFileMemoryStrategy(readOnlyDir);
            await strategy.storeMemory(testMemory, mockClient);

            mockClient.setMockResponses([createResponseEvent(JSON.stringify(testMemory))]);

            // Should handle file write errors gracefully
            const telemetry = await strategy.shutdown();
            expect(telemetry.kind).toBe("telemetry");
        });

        test("storeMemory handles missing client gracefully", async () => {
            const strategy = new JsonFileMemoryStrategy(TEST_REPO_ROOT);
            const memoryContext = new MemoryContext(strategy);
            const tool = memoryContext.getStoreMemory();

            // Call without client in options
            const result = await tool.callback(testMemory, {});

            expect(typeof result).toBe("object");
            expect(getToolResult(result).resultType).toBe("failure");
            expect(getToolResult(result).textResultForLlm).toContain(
                "Unable to store memory due to configuration error",
            );
        });

        test("handles optimization with malformed LLM response", async () => {
            const strategy = new JsonFileMemoryStrategy(TEST_REPO_ROOT);
            await strategy.storeMemory(testMemory, mockClient);

            // Mock client to return malformed JSON
            mockClient.setMockResponses([createResponseEvent("invalid json response from LLM")]);

            const telemetry = await strategy.shutdown();
            expect(telemetry.kind).toBe("telemetry");
            expect(telemetry.telemetry.restrictedProperties.writeMemoriesError).toContain("Unexpected token");
        });

        test("handles optimization with mixed valid/invalid memories", async () => {
            const strategy = new JsonFileMemoryStrategy(TEST_REPO_ROOT);
            await strategy.storeMemory(testMemory, mockClient);

            // Mock client to return mix of valid and invalid memories
            const validMemory = {
                ...testMemory,
                fact: "Valid consolidated fact",
            };
            const invalidMemory = { subject: "incomplete" }; // Missing required fields

            mockClient.setMockResponses([
                createResponseEvent(JSON.stringify(validMemory) + "\n" + JSON.stringify(invalidMemory)),
            ]);

            const telemetry = await strategy.shutdown();
            expect(telemetry.telemetry.metrics.finalMemoriesAfterOptimizationCount).toBe(1); // Only valid memory kept

            // Check the file contains only valid memory
            const memoryFilePath = join(TEST_REPO_ROOT, ".github", "copilot-memories.jsonl");
            const fileContent = await readFile(memoryFilePath, "utf8");
            expect(fileContent).toContain("Valid consolidated fact");
            expect(fileContent).not.toContain("incomplete");
        });

        test("handles optimization with empty LLM response", async () => {
            const strategy = new JsonFileMemoryStrategy(TEST_REPO_ROOT);
            await strategy.storeMemory(testMemory, mockClient);

            // Mock client to return empty response
            mockClient.setMockResponses([createResponseEvent("")]);

            const telemetry = await strategy.shutdown();
            expect(telemetry.telemetry.metrics.finalMemoriesAfterOptimizationCount).toBe(0); // No memories from empty response

            // Check the file is empty
            const memoryFilePath = join(TEST_REPO_ROOT, ".github", "copilot-memories.jsonl");
            const fileContent = await readFile(memoryFilePath, "utf8");
            expect(fileContent).toBe("");
        });

        test("handles optimization with no response content", async () => {
            const strategy = new JsonFileMemoryStrategy(TEST_REPO_ROOT);
            await strategy.storeMemory(testMemory, mockClient);

            // Mock client to return response without content
            mockClient.setMockResponses([createResponseEvent("")]);

            const telemetry = await strategy.shutdown();
            expect(telemetry.telemetry.metrics.finalMemoriesAfterOptimizationCount).toBe(0);
        });

        test("handles optimization with non-response kinds", async () => {
            const strategy = new JsonFileMemoryStrategy(TEST_REPO_ROOT);
            await strategy.storeMemory(testMemory, mockClient);

            // Mock client to return non-response kinds
            mockClient.setMockResponses([
                {
                    kind: "turn_started",
                    model: "test-model",
                    modelInfo: {},
                    turn: 1,
                    timestampMs: Date.now(),
                },
                createResponseEvent(JSON.stringify(testMemory)),
            ]);

            const telemetry = await strategy.shutdown();
            expect(telemetry.telemetry.metrics.finalMemoriesAfterOptimizationCount).toBe(1); // Only the response should be processed
        });

        test("tool schema validation for store_memory", () => {
            const strategy = new JsonFileMemoryStrategy(TEST_REPO_ROOT);
            const context = new MemoryContext(strategy);
            const tool = context.getStoreMemory();

            // Verify tool schema properties
            expect(tool.input_schema.type).toBe("object");
            expect(tool.input_schema.required).toContain("subject");
            expect(tool.input_schema.required).toContain("fact");
            expect(tool.input_schema.required).toContain("source");
            expect(tool.input_schema.required).toContain("reason");
            expect(tool.input_schema.required).toContain("category");

            // Verify property types in schema
            expect(tool.input_schema.properties.subject.type).toBe("string");
            expect(tool.input_schema.properties.fact.type).toBe("string");
            expect(tool.input_schema.properties.source.type).toBe("string");
            expect(tool.input_schema.properties.reason.type).toBe("string");
            expect(tool.input_schema.properties.category.type).toBe("string");

            // Verify descriptions exist
            expect(tool.input_schema.properties.subject.description).toBeTruthy();
            expect(tool.input_schema.properties.fact.description).toBeTruthy();
            expect(tool.input_schema.properties.source.description).toBeTruthy();
            expect(tool.input_schema.properties.reason.description).toBeTruthy();
            expect(tool.input_schema.properties.category.description).toBeTruthy();
        });

        test("tool schema validation for read_memory", () => {
            const strategy = new JsonFileMemoryStrategy(TEST_REPO_ROOT);
            const context = new MemoryContext(strategy);
            const tool = context.getReadMemory();

            // Verify tool schema properties
            expect(tool.input_schema.type).toBe("object");
            expect(tool.input_schema.required).toContain("question");

            // Verify property types in schema
            expect(tool.input_schema.properties.question.type).toBe("string");

            // Verify description exists
            expect(tool.input_schema.properties.question.description).toBeTruthy();
        });
    });

    describe("Boundary Conditions", () => {
        test("handles very long memory content", async () => {
            const longMemory = {
                ...testMemory,
                fact: "x".repeat(1000), // Very long fact
                reason: "y".repeat(2000), // Very long reason
            };

            const strategy = new JsonFileMemoryStrategy(TEST_REPO_ROOT);
            const result = await strategy.storeMemory(longMemory, mockClient);

            expect(typeof result).toBe("object");
            expect(getToolResult(result).resultType).toBe("success");
        });

        test("handles special characters in memory content", async () => {
            const specialMemory = {
                ...testMemory,
                subject: "unicode-🚀",
                fact: "Contains unicode: αβγδε and emoji: 🔥💻",
                source: "path/with spaces/and-special chars!@#$.js:123",
                reason: "Testing \"quotes\" and 'apostrophes' and \n newlines",
            };

            const strategy = new JsonFileMemoryStrategy(TEST_REPO_ROOT);
            const result = await strategy.storeMemory(specialMemory, mockClient);

            expect(typeof result).toBe("object");
            expect(getToolResult(result).resultType).toBe("success");

            mockClient.setMockResponses([createResponseEvent(JSON.stringify(specialMemory))]);

            await strategy.shutdown();

            // Verify special characters are preserved in file
            const memoryFilePath = join(TEST_REPO_ROOT, ".github", "copilot-memories.jsonl");
            const fileContent = await readFile(memoryFilePath, "utf8");
            expect(fileContent).toContain("unicode-🚀");
            expect(fileContent).toContain("🔥💻");
        });

        test("handles empty string values in memory", async () => {
            const emptyMemory = {
                subject: "",
                fact: "",
                source: "",
                reason: "",
                category: "",
            };

            // Should still pass validation since strings are present (even if empty)
            expect(isMemory(emptyMemory)).toBe(true);

            const strategy = new JsonFileMemoryStrategy(TEST_REPO_ROOT);
            const result = await strategy.storeMemory(emptyMemory, mockClient);

            expect(typeof result).toBe("object");
            expect(getToolResult(result).resultType).toBe("success");
        });

        test("handles maximum realistic memory count", async () => {
            const strategy = new JsonFileMemoryStrategy(TEST_REPO_ROOT);

            // Store many memories to test scaling
            const memories = Array.from({ length: 100 }, (_, i) => ({
                ...testMemory,
                subject: `memory-${i}`,
                fact: `Fact number ${i}`,
            }));

            for (const memory of memories) {
                await strategy.storeMemory(memory, mockClient);
            }

            // Mock consolidation to return all memories
            mockClient.setMockResponses([createResponseEvent(memories.map((m) => JSON.stringify(m)).join("\n"))]);

            const telemetry = await strategy.shutdown();
            expect(telemetry.telemetry.metrics.addedMemoriesCount).toBe(100);
            expect(telemetry.telemetry.metrics.finalMemoriesBeforeOptimizationCount).toBe(100);
            expect(telemetry.telemetry.metrics.finalMemoriesAfterOptimizationCount).toBe(100);
        });

        test("SecretFilter is applied correctly to memory input", async () => {
            const strategy = new JsonFileMemoryStrategy(TEST_REPO_ROOT);
            const memoryContext = new MemoryContext(strategy);
            const tool = memoryContext.getStoreMemory();

            const memoryWithSecrets = {
                ...testMemory,
                fact: "Password is secret123",
            };

            // Setup mock to transform the input
            const filteredMemory = {
                ...testMemory,
                fact: "Password is [FILTERED]",
            };
            mockSecretFilter.filterSecretsFromObj.mockReturnValue(filteredMemory);

            await tool.callback(memoryWithSecrets, {
                client: mockClient,
            });

            // Verify SecretFilter was called with original input
            expect(mockSecretFilter.filterSecretsFromObj).toHaveBeenCalledWith(memoryWithSecrets);
        });

        test("handles concurrent storeMemory calls correctly", async () => {
            const strategy = new JsonFileMemoryStrategy(TEST_REPO_ROOT);

            const memories = [
                { ...testMemory, subject: "concurrent1" },
                { ...testMemory, subject: "concurrent2" },
                { ...testMemory, subject: "concurrent3" },
                { ...testMemory, subject: "concurrent4" },
                { ...testMemory, subject: "concurrent5" },
            ];

            // Store all memories concurrently
            const storePromises = memories.map((memory) => strategy.storeMemory(memory, mockClient));
            const results = await Promise.all(storePromises);

            // All should succeed
            results.forEach((result) => {
                expect(getToolResult(result).resultType).toBe("success");
            });

            mockClient.setMockResponses([createResponseEvent(memories.map((m) => JSON.stringify(m)).join("\n"))]);

            const telemetry = await strategy.shutdown();
            expect(telemetry.telemetry.metrics.addedMemoriesCount).toBe(5);
        });
    });

    describe("Constants and Exports", () => {
        test("exports correct tool names", () => {
            expect(STORE_MEMORY_TOOL_NAME).toBe("store_memory");
            expect(READ_MEMORY_TOOL_NAME).toBe("read_memory");
        });

        test("isMemory export works correctly", () => {
            expect(isMemory(testMemory)).toBe(true);
            expect(isMemory({ invalid: "object" })).toBe(false);
        });

        test("MemoryContext and JsonFileMemoryStrategy are constructible", () => {
            const strategy = new JsonFileMemoryStrategy("/test/path");
            expect(strategy).toBeInstanceOf(JsonFileMemoryStrategy);

            const context = new MemoryContext(strategy);
            expect(context).toBeInstanceOf(MemoryContext);
        });
    });

    describe("ToolCallbackOptions and ToolResult Compliance", () => {
        test("handles different ToolCallbackOptions configurations", async () => {
            const strategy = new JsonFileMemoryStrategy(TEST_REPO_ROOT);
            const memoryContext = new MemoryContext(strategy);
            const tool = memoryContext.getStoreMemory();

            // Test with additional options
            const extendedOptions = {
                client: mockClient,
                toolCallId: "test-call-id",
                model: "gpt-4",
                tokenLimit: 1000,
                toolOptions: { customOption: "value" },
            };

            const result = await tool.callback(testMemory, extendedOptions);

            expect(typeof result).toBe("object");
            expect(getToolResult(result).resultType).toBe("success");
        });

        test("tool result format matches ToolResult interface", async () => {
            const strategy = new JsonFileMemoryStrategy(TEST_REPO_ROOT);
            const memoryContext = new MemoryContext(strategy);
            const tool = memoryContext.getStoreMemory();

            const result = await tool.callback(testMemory, {
                client: mockClient,
            });

            // Verify result conforms to ToolResultExpanded interface
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("resultType");
            expect(result).toHaveProperty("textResultForLlm");
            expect(result).toHaveProperty("toolTelemetry");

            expect(["success", "failure"]).toContain(getToolResult(result).resultType);
            expect(typeof getToolResult(result).textResultForLlm).toBe("string");
            expect(typeof getToolResult(result).toolTelemetry).toBe("object");
        });
    });

    describe("MemoryContext", () => {
        let memoryContext: MemoryContext;
        let mockStrategy: {
            storeMemory: ReturnType<typeof vi.fn>;
            readMemory: ReturnType<typeof vi.fn>;
            shutdown: ReturnType<typeof vi.fn>;
        };

        beforeEach(() => {
            mockStrategy = {
                storeMemory: vi.fn().mockResolvedValue({
                    resultType: "success",
                    textResultForLlm: "Memory stored successfully.",
                    toolTelemetry: {},
                }),
                readMemory: vi.fn().mockResolvedValue({
                    resultType: "success",
                    textResultForLlm: "Memory retrieved.",
                    toolTelemetry: {},
                }),
                shutdown: vi.fn().mockResolvedValue({
                    kind: "telemetry",
                    telemetry: {
                        event: "memory_tool",
                        properties: {},
                        restrictedProperties: {},
                        metrics: {
                            initialMemoriesCount: 0,
                            addedMemoriesCount: 1,
                            finalMemoriesBeforeOptimizationCount: 1,
                            finalMemoriesAfterOptimizationCount: 1,
                        },
                    },
                }),
            };

            memoryContext = new MemoryContext(mockStrategy);
        });

        test("getReadMemory returns correct tool configuration", () => {
            const tool = memoryContext.getReadMemory();

            expect(tool.name).toBe(READ_MEMORY_TOOL_NAME);
            expect(tool.description).toContain("Searches facts learned in previous sessions");
            expect(tool.input_schema.type).toBe("object");
            expect(tool.input_schema.required).toEqual(["question"]);
            expect(tool.safeForTelemetry).toBe(true);
            expect(typeof tool.callback).toBe("function");
            expect(typeof tool.shutdown).toBe("function");
        });

        test("readMemory validates input and calls strategy on success", async () => {
            const tool = memoryContext.getReadMemory();
            const question = {
                question: "What testing framework should I use?",
            };

            const result = await tool.callback(question, {
                client: mockClient,
            });

            expect(mockStrategy.readMemory).toHaveBeenCalledWith(question, mockClient);
            expect(typeof result).toBe("object");
            expect(getToolResult(result).resultType).toBe("success");
            expect(getToolResult(result).textResultForLlm).toBe("Memory retrieved.");
        });

        test("readMemory returns failure for invalid input", async () => {
            const tool = memoryContext.getReadMemory();
            const invalidInput = { invalid: "data" };

            const result = await tool.callback(invalidInput, {
                client: mockClient,
            });

            expect(typeof result).toBe("object");
            expect(getToolResult(result).resultType).toBe("failure");
            expect(getToolResult(result).textResultForLlm).toContain("Invalid inputs for read_memory tool");
            expect(mockStrategy.readMemory).not.toHaveBeenCalled();
        });

        test("readMemory returns failure when client is missing", async () => {
            const tool = memoryContext.getReadMemory();
            const question = {
                question: "What testing framework should I use?",
            };

            const result = await tool.callback(question, {});

            expect(typeof result).toBe("object");
            expect(getToolResult(result).resultType).toBe("failure");
            expect(getToolResult(result).textResultForLlm).toContain(
                "Unable to read memory due to configuration error",
            );
            expect(mockStrategy.readMemory).not.toHaveBeenCalled();
        });

        test("readMemory handles initialization failure", async () => {
            // Create a memory file with malformed JSON to trigger initialization failure
            const memoryFilePath = join(TEST_REPO_ROOT, ".github", "copilot-memories.jsonl");
            await mkdir(join(TEST_REPO_ROOT, ".github"), { recursive: true });
            await writeFile(memoryFilePath, "invalid json", "utf8");

            const failingStrategy = new JsonFileMemoryStrategy(TEST_REPO_ROOT);

            const result = await failingStrategy.readMemory({ question: "test question" }, mockClient);

            expect(typeof result).toBe("object");
            expect(getToolResult(result).resultType).toBe("failure");
            expect(getToolResult(result).textResultForLlm).toContain(
                "Unable to read memory due to memory initialization error",
            );
        });
    });
});
