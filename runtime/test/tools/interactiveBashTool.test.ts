/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { randomUUID } from "crypto";
import * as fs from "fs";
import { mkdtemp, rm } from "fs/promises";
import { tmpdir } from "os";
import { join } from "path";
import { afterEach, Assertion, beforeEach, describe, expect, it } from "vitest";
import {
    alwaysProceedToolConfirmationCallback,
    Tool,
    ToolConfirmationCallback,
    ToolIntention,
    ToolResultExpanded,
} from "../../src/tools";
import {
    InteractiveBashToolContext,
    READ_BASH_TOOL_NAME,
    STOP_BASH_TOOL_NAME,
} from "../../src/tools/interactiveBashTool";

describe("bash tool", () => {
    let tempDir: string;
    let bashTool: Tool;
    let writeTool: Tool;
    let readBashTool: Tool;
    let stopBashTool: Tool;
    let originalEnv: NodeJS.ProcessEnv;

    beforeEach(async () => {
        // Save original env and clear test vars
        originalEnv = { ...process.env };
        delete process.env.GITHUB_TOKEN;
        delete process.env.TEST_VAR;

        // Create test directory
        tempDir = await mkdtemp(join(tmpdir(), "bash-tool-test-"));

        const bashContext = new InteractiveBashToolContext(
            {
                location: tempDir,
                toolConfirmationCallback: alwaysProceedToolConfirmationCallback,
            },
            true,
        );
        bashTool = bashContext.getBashTool();
        readBashTool = bashContext.getReadBashTool();
        stopBashTool = bashContext.getStopBashTool();
        writeTool = bashContext.getWriteBashTool();

        // Initialize session
        const initResult = await bashTool.callback({
            command: 'echo "INIT_TEST"',
        });
        const initResultStr = typeof initResult === "string" ? initResult : initResult.textResultForLlm;
        expect(initResultStr).toBe("INIT_TEST\n<exited with exit code 0>");
    }, 15000);

    afterEach(async () => {
        // Restore original env
        process.env = originalEnv;

        // Clean up test directory
        await rm(tempDir, { recursive: true, force: true });
    });

    it("maintains environment state between calls", async () => {
        await bashTool.callback({
            command: 'export TEST_VAR="persistent value"',
        });
        const result = await bashTool.callback({
            command: 'echo "$TEST_VAR"',
        });
        const resultStr = typeof result === "string" ? result : result.textResultForLlm;
        expect(resultStr).toBe("persistent value\n<exited with exit code 0>");
    }, 15000);

    it("respects working directory", async () => {
        const result = await bashTool.callback({
            command: "pwd",
        });
        const resultStr = typeof result === "string" ? result : result.textResultForLlm;
        expect(resultStr).toContain(tempDir);
    }, 15000);

    it("handles timeouts with full output", async () => {
        const result = await bashTool.callback({
            command: 'echo "start" && sleep 0.1 && echo "end"',
        });
        const resultStr = typeof result === "string" ? result : result.textResultForLlm;
        expect(resultStr).toBe("start\nend\n<exited with exit code 0>");
    }, 15000);

    it("handles short timeouts", async () => {
        const shortTimeoutBash = bash({ location: tempDir, timeout: 100 });

        // Initialize session
        await shortTimeoutBash.callback({
            command: "echo init",
        });

        // Run command that outputs then sleeps
        const result = await shortTimeoutBash.callback({
            command: '{ echo "start"; sleep 2; echo "end"; }',
        });
        const resultStr = typeof result === "string" ? result : result.textResultForLlm;

        expect(resultStr).not.toContain("init");
        expect(resultStr).toContain("start");
        expect(resultStr).not.toContain("end");
        expect(resultStr).toContain("timed out");
    }, 15000);

    it("filters sensitive environment variables", async () => {
        // Set test variables
        process.env.GITHUB_TOKEN = "super-secret";
        process.env.TEST_VAR = "visible";

        // Create fresh session to pick up env vars
        const testBash = bash({ location: tempDir });
        await testBash.callback({ command: "echo init" });

        // Check secret var is filtered
        const result1 = await testBash.callback({
            command: 'echo "GITHUB_TOKEN=$GITHUB_TOKEN"',
        });
        const result1Str = typeof result1 === "string" ? result1 : result1.textResultForLlm;
        expect(result1Str).toBe("GITHUB_TOKEN=\n<exited with exit code 0>");

        // Check normal var is kept
        const result2 = await testBash.callback({
            command: 'echo "TEST_VAR=$TEST_VAR"',
        });
        const result2Str = typeof result2 === "string" ? result2 : result2.textResultForLlm;
        expect(result2Str).toBe("TEST_VAR=visible\n<exited with exit code 0>");
    }, 15000);

    it("warns of command running in default session", async () => {
        // Set environment in first session
        await bashTool.callback({
            command: "export TEST_VAR=before_timeout",
        });

        // Create new session with short timeout
        const shortTimeoutBash = bash({ location: tempDir, timeout: 100 });
        await shortTimeoutBash.callback({ command: "echo init" });

        // Run command that will timeout
        await shortTimeoutBash.callback({
            command: "sleep 2",
        });

        // Original session should be unaffected
        const result1 = await bashTool.callback({
            command: 'echo "$TEST_VAR"',
        });
        const result1Str = typeof result1 === "string" ? result1 : result1.textResultForLlm;
        expect(result1Str).toBe("before_timeout\n<exited with exit code 0>");

        // New session after timeout should be clean
        const result2 = await shortTimeoutBash.callback({
            command: 'echo "$TEST_VAR"',
        });
        const result2Str = typeof result2 === "string" ? result2 : result2.textResultForLlm;
        expect(result2Str).toBe(
            `<command with id: default-bash-session is already running, wait for output with ${READ_BASH_TOOL_NAME}, stop it with ${STOP_BASH_TOOL_NAME} tool, or use a different sessionId>`,
        );
    }, 15000);

    it("handles stderr output", async () => {
        const result = await bashTool.callback({
            command: 'echo "error message" >&2',
        });
        const resultStr = typeof result === "string" ? result : result.textResultForLlm;
        expect(resultStr).toBe("error message\n<exited with exit code 0>");
    }, 15000);

    it("handles command errors", async () => {
        const result = await bashTool.callback({
            command: "invalid_command_xyz",
        });
        const resultStr = typeof result === "string" ? result : result.textResultForLlm;
        expect(resultStr).toMatch(/not found|127/);
    }, 15000);

    it("enriches input and output in markdown format", async () => {
        const result = await bashTool.callback({
            command: 'echo "Hello, World!"',
        });

        const expandedResult = result as ToolResultExpanded;
        expect(expandedResult.sessionLog).toMatch("Hello, World!");
    }, 15000);

    it("handles multiple calls in parallel", async () => {
        const result0 = await bashTool.callback({
            command: "sleep 1 && cd /tmp && pwd",
            sessionId: "command-session-0",
            async: true,
        });
        const result1 = await bashTool.callback({
            command: "sleep 1 && cd /usr && pwd",
            sessionId: "command-session-1",
            async: true,
        });

        expect((result0 as ToolResultExpanded).textResultForLlm).toBe(
            "<command started in background with ID: command-session-0>",
        );
        expect((result1 as ToolResultExpanded).textResultForLlm).toBe(
            "<command started in background with ID: command-session-1>",
        );

        // Wait a bit before reading to ensure the commands have finished.
        await new Promise((resolve) => setTimeout(resolve, 100));

        await readResultWithRetry(readBashTool, (assertion) => assertion.toContain("/tmp"), {
            sessionId: "command-session-0",
            delay: 0,
        });
        await readResultWithRetry(readBashTool, (assertion) => assertion.toContain("/usr"), {
            sessionId: "command-session-1",
            delay: 0,
        });
    }, 15000);

    it("refuses to start command when async command is running", async () => {
        const result0 = await bashTool.callback({
            command: "sleep 10",
            sessionId: "command-session-0",
            delay: 0,
            async: true,
        });
        const result1 = await bashTool.callback({
            command: "sleep 1 && cd /usr && pwd",
            sessionId: "command-session-0",
            delay: 0,
            async: true,
        });

        expect((result0 as ToolResultExpanded).textResultForLlm).toBe(
            "<command started in background with ID: command-session-0>",
        );
        expect((result1 as ToolResultExpanded).textResultForLlm).toBe(
            `<command with id: command-session-0 is already running, wait for output with ${READ_BASH_TOOL_NAME}, stop it with ${STOP_BASH_TOOL_NAME} tool, or use a different sessionId>`,
        );
    }, 15000);

    it("refuses to start command when timed out command is running; async=false", async () => {
        const result0 = await bashTool.callback({
            command: "sleep 140",
            sessionId: "command-session-0",
            async: false,
        });
        const result1 = await bashTool.callback({
            command: "sleep 1 && cd /usr && pwd",
            sessionId: "command-session-0",
            async: true,
        });

        expect((result0 as ToolResultExpanded).textResultForLlm).toBe(
            `<waiting for command with sessionId: command-session-0 to exit timed out after 120 seconds. try again with ${READ_BASH_TOOL_NAME} or abort it with ${STOP_BASH_TOOL_NAME} tool>`,
        );
        expect((result1 as ToolResultExpanded).textResultForLlm).toBe(
            `<command with id: command-session-0 is already running, wait for output with ${READ_BASH_TOOL_NAME}, stop it with ${STOP_BASH_TOOL_NAME} tool, or use a different sessionId>`,
        );
    }, 300000);

    // Ensure that async bash commands clear the buffer after the final read
    // when the command has finished executing. This behavior is intended to
    // emphasize the '<exited with exit code ...>' message to reduce the odds
    // of the model idly looping after the process has exited, as observed in
    // https://github.com/github/sweagentd/issues/2868
    it("clears buffer on final read", async () => {
        const result = await bashTool.callback({
            command: 'sleep 1 && echo "Hello, world"',
            sessionId: "command-session-0",
            async: true,
        });

        expect((result as ToolResultExpanded).textResultForLlm).toBe(
            "<command started in background with ID: command-session-0>",
        );

        await readResultWithRetry(
            readBashTool,
            (assertion) =>
                assertion.toBe(
                    `<waiting for command with sessionId: command-session-0 to produce output timed out after 0 seconds. try again with ${READ_BASH_TOOL_NAME} or abort it with ${STOP_BASH_TOOL_NAME} tool>`,
                ),
            { sessionId: "command-session-0", delay: 0 },
        );

        await readResultWithRetry(
            readBashTool,
            (assertion) =>
                assertion.toStrictEqual("Hello, world\n<command with id: command-session-0 exited with exit code 0>"),
            { sessionId: "command-session-0", delay: 0 },
        );

        await readResultWithRetry(
            readBashTool,
            (assertion) => assertion.toStrictEqual("\n<command with id: command-session-0 exited with exit code 0>"),
            { sessionId: "command-session-0", delay: 0 },
        );
    }, 15000);

    // Ensure that the async bash command does not send an extra return.
    // Previously there was a bug where the command would send an extra return
    // causing interactive apps to respond with "invalid input" or similar after
    // launch.
    it("does not send extra return", async () => {
        // Start a command that doesn't exit on its own and doesn't print output.
        await bashTool.callback({
            command: 'read && echo "Hello, world"',
            sessionId: "command-session-0",
            async: true,
        });

        // Wait a bit to give the command time to run.
        await new Promise((resolve) => setTimeout(resolve, 1000));

        // Reading should return nothing since the command is waiting for input.
        await readResultWithRetry(
            readBashTool,
            (assertion) =>
                assertion.toBe(
                    `<waiting for command with sessionId: command-session-0 to produce output timed out after 0 seconds. try again with ${READ_BASH_TOOL_NAME} or abort it with ${STOP_BASH_TOOL_NAME} tool>`,
                ),
            { sessionId: "command-session-0", delay: 0 },
        );

        // Send an enter key to the command to trigger the output.
        const writeResult = await writeTool.callback({
            sessionId: "command-session-0",
            input: "{enter}",
            delay: 2,
        });

        // Ensure the input was received and the output was produced.
        expect((writeResult as ToolResultExpanded).textResultForLlm).toBe(
            "Hello, world\n<command with id: command-session-0 exited with exit code 0>",
        );
    }, 15000);

    it("handles reading partial output of long running command, with delay", async () => {
        const result = await bashTool.callback({
            command: 'echo "Hello, world";sleep 2;echo "Goodbye, world"',
            sessionId: "command-session-0",
            async: true,
        });

        expect((result as ToolResultExpanded).textResultForLlm).toBe(
            "<command started in background with ID: command-session-0>",
        );

        await readResultWithRetry(
            readBashTool,
            (assertion) =>
                assertion.toBe(
                    `Hello, world\n<waiting for command with sessionId: command-session-0 to produce output timed out after 0 seconds. try again with ${READ_BASH_TOOL_NAME} or abort it with ${STOP_BASH_TOOL_NAME} tool>`,
                ),
            { sessionId: "command-session-0", delay: 0 },
        );

        await readResultWithRetry(
            readBashTool,
            (assertion) => assertion.toStrictEqual("\n<command with id: command-session-0 exited with exit code 0>"),
            { sessionId: "command-session-0", delay: 2 },
        );
    }, 10000);

    it("handles reading partial output of long running command, without delay", async () => {
        const result = await bashTool.callback({
            command: 'echo "Hello, world";sleep 2;echo "Goodbye, world"',
            sessionId: "command-session-0",
            async: true,
        });

        expect((result as ToolResultExpanded).textResultForLlm).toBe(
            "<command started in background with ID: command-session-0>",
        );

        await readResultWithRetry(
            readBashTool,
            (assertion) =>
                assertion.toBe(
                    `Hello, world\n<waiting for command with sessionId: command-session-0 to produce output timed out after 0 seconds. try again with ${READ_BASH_TOOL_NAME} or abort it with ${STOP_BASH_TOOL_NAME} tool>`,
                ),
            { sessionId: "command-session-0", delay: 0 },
        );

        await readResultWithRetry(
            readBashTool,
            (assertion) => assertion.toStrictEqual("\n<command with id: command-session-0 exited with exit code 0>"),
            { sessionId: "command-session-0", delay: 0 },
        );
    }, 15000);

    it("kills processes started on shutdown", async () => {
        // Run a long-running, interactive command.
        const result = await bashTool.callback({
            command: "top",
            sessionId: "command-session-0",
            async: true,
        });

        expect((result as ToolResultExpanded).textResultForLlm).toBe(
            "<command started in background with ID: command-session-0>",
        );

        // Ensure that the process is running before shutdown.
        await readResultWithRetry(bashTool, (assertion) => assertion.toContain("top"), {
            command: 'ps -e | grep "top"',
            sessionId: "command-session-1",
            delay: 0,
        });

        await stopBashTool.callback({
            sessionId: "command-session-0",
        });

        // Ensure that the process is not running after shutdown.
        const isRunningAfterShutdownResult = await bashTool.callback({
            command: 'ps -e | grep "top"',
            sessionId: "command-session-1",
        });
        expect((isRunningAfterShutdownResult as ToolResultExpanded).textResultForLlm).not.toContain("top");
    }, 15000);

    it("works with interactive apps", async () => {
        const testFilePath = `/tmp/${randomUUID()}.txt`;

        // Run a long-running, interactive editor process.
        const result = await bashTool.callback({
            command: `nano ${testFilePath}`,
            sessionId: "command-session-0",
            async: true,
        });

        expect((result as ToolResultExpanded).textResultForLlm).toBe(
            "<command started in background with ID: command-session-0>",
        );

        // Send text to the running process.
        const typingResult = await writeTool.callback({
            sessionId: "command-session-0",
            input: "Hello world",
            delay: 1,
        });
        expect((typingResult as ToolResultExpanded).textResultForLlm).toContain("Hello world");

        // Send Ctrl+X to start the exit procedure.
        await writeTool.callback({
            sessionId: "command-session-0",
            input: "\x18",
            delay: 1,
        });

        // Nano will ask if you want to save the file. Send 'y' to confirm.
        await writeTool.callback({
            sessionId: "command-session-0",
            input: "y",
            delay: 1,
        });

        // Nano will ask if you want to save the file with the same name. Send Enter to confirm.
        await writeTool.callback({
            sessionId: "command-session-0",
            input: "\r",
            delay: 1,
        });

        // Make sure the file was created and contains the expected text.
        expect(fs.readFileSync(testFilePath, "utf8")).toContain("Hello world");
    }, 10000);

    it("handles missing input in writeBashTool", async () => {
        const result = await writeTool.callback({
            sessionId: "test-session",
            input: undefined,
            delay: 1,
        });

        const resultStr = typeof result === "string" ? result : result.textResultForLlm;
        expect(resultStr).toBe("No input provided. Please supply input to send to the running process.");

        const expandedResult = result as ToolResultExpanded;
        expect(expandedResult.resultType).toBe("failure");
        expect(expandedResult.error).toBe("No input provided. Please supply input to send to the running process.");
    }, 15000);

    it("handles missing sessionId in stopBashTool", async () => {
        const result = await stopBashTool.callback({
            sessionId: undefined,
        });

        const resultStr = typeof result === "string" ? result : result.textResultForLlm;
        expect(resultStr).toBe(
            "No background session ID provided. Please supply a valid background session ID to stop the command.",
        );

        const expandedResult = result as ToolResultExpanded;
        expect(expandedResult.resultType).toBe("failure");
        expect(expandedResult.error).toBe(
            "No background session ID provided. Please supply a valid background session ID to stop the command.",
        );
    }, 15000);

    it("should return description in summariseIntention", () => {
        const testInput = {
            command: "echo hello",
            description: "Print hello to console",
            sessionId: "test-session",
            async: false,
        };

        const intention = bashTool.summariseIntention!(testInput);
        expect(intention).toBe("Print hello to console");
    });

    it("should return command in summariseIntention if description is empty", () => {
        const testInput = {
            command: "echo hello",
            description: "",
            sessionId: "test-session",
            async: false,
        };

        const intention = bashTool.summariseIntention!(testInput);
        expect(intention).toBe("echo hello");
    });

    describe("Tool confirmation callback integration", () => {
        it("should call confirmation callback with correct intention", async () => {
            let capturedIntention: ToolIntention | null = null;

            const mockConfirmationCallback: ToolConfirmationCallback = async (intention) => {
                capturedIntention = intention;
                return "proceed-once";
            };

            const confirmationBash = bash({
                location: tempDir,
                toolConfirmationCallback: mockConfirmationCallback,
            });

            await confirmationBash.callback({
                command: 'echo "test"',
                sessionId: "test-session",
                async: false,
            });

            expect(capturedIntention).toEqual({
                type: "exec",
                title: "Bash",
                command: 'echo "test"',
            });
        }, 15000);

        it("should execute tool when confirmation returns proceed-once", async () => {
            const proceedCallback: ToolConfirmationCallback = async () => {
                return "proceed-once";
            };

            const confirmationBash = bash({
                location: tempDir,
                toolConfirmationCallback: proceedCallback,
            });

            const result = await confirmationBash.callback({
                command: 'echo "executed"',
                sessionId: "test-session",
                async: false,
            });

            expect((result as ToolResultExpanded).resultType).toBe("success");
            expect((result as ToolResultExpanded).textResultForLlm).toContain("executed");
        }, 15000);

        it("should return rejected result when confirmation is rejected", async () => {
            const rejectCallback: ToolConfirmationCallback = async () => {
                return "rejected";
            };

            const confirmationBash = bash({
                location: tempDir,
                toolConfirmationCallback: rejectCallback,
            });

            const result = await confirmationBash.callback({
                command: 'echo "should not execute"',
                sessionId: "test-session",
                async: false,
            });

            expect((result as ToolResultExpanded).resultType).toBe("rejected");
            expect((result as ToolResultExpanded).textResultForLlm).toBe(
                "The tool call required confirmation from the user, but the user rejected it. Await further instructions.",
            );
            expect((result as ToolResultExpanded).error).toBe(
                "The tool call required confirmation from the user, but the user rejected it. Await further instructions.",
            );
        }, 15000);

        it("should return previously-interrupted result when confirmation is previously-interrupted", async () => {
            const previouslyInterruptedCallback: ToolConfirmationCallback = async () => {
                return "previously-interrupted";
            };

            const confirmationBash = bash({
                location: tempDir,
                toolConfirmationCallback: previouslyInterruptedCallback,
            });

            const result = await confirmationBash.callback({
                command: 'echo "should not execute"',
                sessionId: "test-session",
                async: false,
            });

            expect((result as ToolResultExpanded).resultType).toBe("rejected");
            expect((result as ToolResultExpanded).textResultForLlm).toBe(
                "The user rejected an earlier tool call and does not want to take this action right now. Await further instructions.",
            );
            expect((result as ToolResultExpanded).error).toBe(
                "The user rejected an earlier tool call and does not want to take this action right now. Await further instructions.",
            );
        }, 15000);
    });
});

async function readResultWithRetry(
    readBash: Tool,
    assertion: (assertion: Assertion<string>) => void,
    input: { command?: string; sessionId?: string; delay: number },
): Promise<void> {
    const MaxRetries = 30;

    for (let i = 0; i <= MaxRetries; i++) {
        const result = await readBash.callback(input);

        try {
            assertion(expect((result as ToolResultExpanded).textResultForLlm));
            return;
        } catch (e) {
            // Rethrow on the last attempt.
            if (i === MaxRetries) {
                throw e;
            }
        }

        await new Promise((resolve) => setTimeout(resolve, 100));
    }
}

function bash(config: { location: string; timeout?: number; toolConfirmationCallback?: ToolConfirmationCallback }) {
    const bashContext = new InteractiveBashToolContext(
        {
            location: config.location,
            timeout: config.timeout || 120000,
            toolConfirmationCallback: config.toolConfirmationCallback || alwaysProceedToolConfirmationCallback,
        },
        true,
    );

    return bashContext.getBashTool();
}
