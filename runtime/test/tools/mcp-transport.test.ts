/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import axios, { AxiosInstance } from "axios";
import * as fs from "fs";
import * as path from "path";
import { afterEach, beforeEach, describe, expect, test, vi } from "vitest";
import * as contentFilter from "../../src/helpers/content-filter";
import { ContentFilterMode } from "../../src/helpers/content-filter";
import { RunnerLogger } from "../../src/runner";
import { RuntimeSettings } from "../../src/settings";
import { CONTENT_TYPE_IMAGE, CONTENT_TYPE_TEXT, OutOfProcMCPTransport } from "../../src/tools/mcp-transport";
import { ToolConfirmationCallback, ToolResultExpanded } from "../../src/tools";

// Mock axios
vi.mock("axios");

describe("MCPTransport", () => {
    let mockLogger: RunnerLogger;
    let mockSettings: RuntimeSettings;
    let mcpTransport: OutOfProcMCPTransport;
    let mockAxiosInstance: {
        post: ReturnType<typeof vi.fn>;
    };

    beforeEach(() => {
        // Reset mocks
        vi.resetAllMocks();

        // Create mock logger
        mockLogger = {
            info: vi.fn(),
            error: vi.fn(),
            warn: vi.fn(),
            debug: vi.fn(),
        } as unknown as RunnerLogger;

        // Create mock setttings
        mockSettings = {
            featureFlags: {
                copilot_swe_agent_mcp_filtering: false,
            },
        } as RuntimeSettings;

        // Create mock axios instance with post method
        mockAxiosInstance = {
            post: vi.fn(),
        };

        // Mock axios.create to return our mockAxiosInstance
        vi.mocked(axios.create).mockReturnValue(mockAxiosInstance as unknown as AxiosInstance);

        // Initialize MCPTransport
        mcpTransport = new OutOfProcMCPTransport(mockSettings, mockLogger);
    });

    afterEach(() => {
        vi.resetAllMocks();
    });

    describe("loadToolsFromConfig", () => {
        let tempDir: string;
        let configPath: string;

        beforeEach(() => {
            // Create a temporary directory for test files
            tempDir = fs.mkdtempSync(path.join(__dirname, "temp-"));
            configPath = path.join(tempDir, "mcp-config.json");
        });

        afterEach(() => {
            // Clean up temporary files
            if (fs.existsSync(tempDir)) {
                fs.rmSync(tempDir, { recursive: true, force: true });
            }
        });

        test("should return empty object when config file does not exist", async () => {
            // Arrange
            const nonExistentPath = path.join(tempDir, "nonexistent.json");

            // Act
            const result = await mcpTransport.loadTools(nonExistentPath);

            // Assert
            expect(result).toEqual([]);
            expect(mockLogger.info).toHaveBeenCalledWith(`Loading tools from config file: ${nonExistentPath}`);
            expect(mockLogger.info).toHaveBeenCalledWith(`Config file not found at path: ${nonExistentPath}`);
        });

        test("should load tools from valid config file", async () => {
            // Arrange
            const toolsConfig = {
                "server1/tool1": {
                    name: "test-tool",
                    description: "A test tool",
                    input_schema: {
                        type: "object",
                        properties: { param1: { type: "string" } },
                    },
                    safeForTelemetry: { name: true, inputsNames: false },
                },
                "server2/tool2": {
                    name: "another-tool",
                    description: "Another test tool",
                    input_schema: { type: "string" },
                    safeForTelemetry: false,
                    filterMode: ContentFilterMode.Markdown,
                },
            };

            fs.writeFileSync(configPath, JSON.stringify(toolsConfig, null, 2));

            // Mock successful tool invocation
            mockAxiosInstance.post.mockResolvedValue({
                data: {
                    isToolError: false,
                    content: [
                        {
                            type: CONTENT_TYPE_TEXT,
                            text: "Tool executed successfully",
                        },
                    ],
                },
            });

            // Spy on invokeTool to verify filter mode usage
            const invokeToolSpy = vi.spyOn(mcpTransport, "invokeTool");

            // Act
            const result = await mcpTransport.loadTools(configPath);

            // Assert
            expect(result).toHaveLength(2);

            // Check first tool
            const tool1FromConfig = toolsConfig["server1/tool1"];
            const tool1 = result.filter((tool) => tool.name === tool1FromConfig.name)[0];
            expect(tool1.name).toBe(tool1FromConfig.name);
            expect(tool1.description).toBe(tool1FromConfig.description);
            expect(tool1.input_schema).toEqual(tool1FromConfig.input_schema);
            expect(tool1.safeForTelemetry).toEqual(tool1FromConfig.safeForTelemetry);

            // Check second tool
            const tool2FromConfig = toolsConfig["server2/tool2"];
            const tool2 = result.filter((tool) => tool.name === tool2FromConfig.name)[0];
            expect(tool2.name).toBe(tool2FromConfig.name);
            expect(tool2.description).toBe(tool2FromConfig.description);
            expect(tool2.input_schema).toEqual(tool2FromConfig.input_schema);
            expect(tool2.safeForTelemetry).toEqual(tool2FromConfig.safeForTelemetry);

            // Test that tool callbacks use the correct filter mode
            const testInput = { param1: "test" };

            // Test tool1 callback - should use default filter mode (HiddenCharacters)
            await tool1.callback(testInput);
            expect(invokeToolSpy).toHaveBeenCalledWith("server1/tool1", testInput, ContentFilterMode.HiddenCharacters);

            // Test tool2 callback - should use specified filter mode (Markdown)
            await tool2.callback(testInput);
            expect(invokeToolSpy).toHaveBeenCalledWith("server2/tool2", testInput, ContentFilterMode.Markdown);
        });
    });

    describe("invokeTool", () => {
        test("should handle text-only responses", async () => {
            // Arrange
            const toolId = "text-tool";
            const params = { prompt: "Generate text" };
            mockAxiosInstance.post.mockResolvedValueOnce({
                data: {
                    isToolError: false,
                    content: [
                        { type: CONTENT_TYPE_TEXT, text: "Hello " },
                        { type: CONTENT_TYPE_TEXT, text: "World!" },
                    ],
                },
            });

            // Act
            const result = await mcpTransport.invokeTool(toolId, params);

            // Assert
            expect(mockAxiosInstance.post).toHaveBeenCalledWith("/invoke-tool", {
                toolId,
                params,
            });
            expect(result.textResultForLlm).toBe("Hello World!");
            expect(result.resultType).toBe("success");
            expect(result.binaryResultForLlm).toEqual([]);
            expect(mockLogger.info).toHaveBeenCalledWith(
                `Invoking tool: ${toolId} with params: ${JSON.stringify(params)}`,
            );
        });

        test("should handle image-only responses", async () => {
            // Arrange
            const toolId = "image-generator";
            const params = { prompt: "Generate an image of a cat" };
            const imageData = "base64encodedimagedata";
            const imageMimeType = "image/png";

            mockAxiosInstance.post.mockResolvedValueOnce({
                data: {
                    isToolError: false,
                    content: [
                        {
                            type: CONTENT_TYPE_IMAGE,
                            data: imageData,
                            mimeType: imageMimeType,
                        },
                    ],
                },
            });

            // Act
            const result = await mcpTransport.invokeTool(toolId, params);

            // Assert
            expect(mockAxiosInstance.post).toHaveBeenCalledWith("/invoke-tool", {
                toolId,
                params,
            });
            expect(result.textResultForLlm).toBe("");
            expect(result.resultType).toBe("success");
            expect(result.binaryResultForLlm).toEqual([
                {
                    type: CONTENT_TYPE_IMAGE,
                    data: imageData,
                    mimeType: imageMimeType,
                },
            ]);
        });

        test("should handle mixed text and image content", async () => {
            // Arrange
            const toolId = "mixed-content-tool";
            const params = { prompt: "Generate image with description" };

            mockAxiosInstance.post.mockResolvedValueOnce({
                data: {
                    isToolError: false,
                    content: [
                        {
                            text: `Screenshot viewport 1 and save it as `,
                            type: "text",
                        },
                        {
                            type: CONTENT_TYPE_IMAGE,
                            data: "base64image1",
                            mimeType: "image/jpeg",
                        },
                        {
                            text: `Screenshot viewport 2 and save it as`,
                            type: "text",
                        },
                        {
                            type: CONTENT_TYPE_IMAGE,
                            data: "base64image2",
                            mimeType: "image/png",
                        },
                    ],
                },
            });

            // Act
            const result = await mcpTransport.invokeTool(toolId, params);

            // Assert
            expect(mockAxiosInstance.post).toHaveBeenCalledWith("/invoke-tool", {
                toolId,
                params,
            });
            expect(result.textResultForLlm).toBe(
                "Screenshot viewport 1 and save it as Screenshot viewport 2 and save it as",
            );
            expect(result.resultType).toBe("success");
            expect(result.binaryResultForLlm).toEqual([
                {
                    type: CONTENT_TYPE_IMAGE,
                    data: "base64image1",
                    mimeType: "image/jpeg",
                },
                {
                    type: CONTENT_TYPE_IMAGE,
                    data: "base64image2",
                    mimeType: "image/png",
                },
            ]);
        });

        test("should handle undefined response", async () => {
            // Arrange
            const toolId = "text-tool";
            const params = { prompt: "Generate text" };
            mockAxiosInstance.post.mockResolvedValueOnce({
                data: {
                    isToolError: false,
                    content: undefined,
                },
            });

            // Act
            const result = await mcpTransport.invokeTool(toolId, params);

            // Assert
            expect(mockAxiosInstance.post).toHaveBeenCalledWith("/invoke-tool", {
                toolId,
                params,
            });
            expect(result.textResultForLlm).toBe("");
            expect(result.resultType).toBe("success");
            expect(result.binaryResultForLlm).toEqual([]);
            expect(mockLogger.info).toHaveBeenCalledWith(
                `Invoking tool: ${toolId} with params: ${JSON.stringify(params)}`,
            );
        });

        test("should ignore handle audio content for now", async () => {
            // Arrange
            const toolId = "mixed-content-tool";
            const params = { prompt: "Generate audio with description" };

            mockAxiosInstance.post.mockResolvedValueOnce({
                data: {
                    isToolError: false,
                    content: [
                        {
                            type: "audio",
                            data: "base64-encoded-audio-data",
                            mimeType: "audio/wav",
                        },
                        {
                            text: `Some audio description`,
                            type: "text",
                        },
                    ],
                },
            });

            // Act
            const result = await mcpTransport.invokeTool(toolId, params);

            // Assert
            expect(mockAxiosInstance.post).toHaveBeenCalledWith("/invoke-tool", {
                toolId,
                params,
            });
            expect(result.textResultForLlm).toBe("Some audio description");
            expect(result.resultType).toBe("success");
            expect(result.binaryResultForLlm).toEqual([]);
        });

        test("should handle error responses", async () => {
            // Arrange
            const toolId = "failing-tool";
            const params = { prompt: "This will fail" };
            const errorMessage = "Failed to generate content";

            mockAxiosInstance.post.mockResolvedValueOnce({
                data: {
                    isToolError: true,
                    content: [{ type: CONTENT_TYPE_TEXT, text: errorMessage }],
                },
            });

            // Act
            const result = await mcpTransport.invokeTool(toolId, params);

            // Assert
            expect(mockAxiosInstance.post).toHaveBeenCalledWith("/invoke-tool", {
                toolId,
                params,
            });
            expect(result.textResultForLlm).toBe(errorMessage);
            expect(result.resultType).toBe("failure");
            expect(result.error).toBe(errorMessage);
        });

        test("should handle empty content array", async () => {
            // Arrange
            const toolId = "empty-content-tool";
            const params = { prompt: "Generate nothing" };

            mockAxiosInstance.post.mockResolvedValueOnce({
                data: {
                    isToolError: false,
                    content: [],
                },
            });

            // Act
            const result = await mcpTransport.invokeTool(toolId, params);

            // Assert
            expect(mockAxiosInstance.post).toHaveBeenCalledWith("/invoke-tool", {
                toolId,
                params,
            });
            expect(result.textResultForLlm).toBe("");
            expect(result.resultType).toBe("success");
            expect(result.binaryResultForLlm).toEqual([]);
        });

        test("should apply no filtering when ContentFilterMode.None is specified", async () => {
            const toolId = "text-tool";
            const params = { prompt: "Generate text" };
            const textWithInvisibleChars = "Text with invisible char\u200B here";

            mockSettings.featureFlags!.copilot_swe_agent_mcp_filtering = true;

            mockAxiosInstance.post.mockResolvedValueOnce({
                data: {
                    isToolError: false,
                    content: [
                        {
                            type: CONTENT_TYPE_TEXT,
                            text: textWithInvisibleChars,
                        },
                    ],
                },
            });

            const applySpy = vi.spyOn(contentFilter, "applyContentFilter");

            const result = await mcpTransport.invokeTool(toolId, params, ContentFilterMode.None);

            expect(applySpy).toHaveBeenCalledWith(textWithInvisibleChars, ContentFilterMode.None);
            expect(mockAxiosInstance.post).toHaveBeenCalledWith("/invoke-tool", { toolId, params });
            expect(result.textResultForLlm).toBe(textWithInvisibleChars);
        });

        test("should apply HiddenCharacters filtering by default", async () => {
            const toolId = "text-tool";
            const params = { prompt: "Generate text" };
            const textWithInvisibleChars = "Text with invisible char\u200B here";
            const filteredText = "Text with invisible char here";
            mockSettings.featureFlags!.copilot_swe_agent_mcp_filtering = true;

            const applySpy = vi.spyOn(contentFilter, "applyContentFilter");

            mockAxiosInstance.post.mockResolvedValueOnce({
                data: {
                    isToolError: false,
                    content: [
                        {
                            type: CONTENT_TYPE_TEXT,
                            text: textWithInvisibleChars,
                        },
                    ],
                },
            });

            const result = await mcpTransport.invokeTool(toolId, params);

            expect(applySpy).toHaveBeenCalledWith(textWithInvisibleChars, ContentFilterMode.HiddenCharacters);
            expect(result.textResultForLlm).not.toContain("\u200B");
            expect(result.textResultForLlm).toBe(filteredText);
        });

        test("should apply Markdown filtering when specified", async () => {
            const toolId = "text-tool";
            const params = { prompt: "Generate text" };
            const textWithHtml = 'Text with <script>alert("xss")</script> tags';
            const filteredText = "Text with  tags";
            mockSettings.featureFlags!.copilot_swe_agent_mcp_filtering = true;

            const applySpy = vi.spyOn(contentFilter, "applyContentFilter");

            mockAxiosInstance.post.mockResolvedValueOnce({
                data: {
                    isToolError: false,
                    content: [{ type: CONTENT_TYPE_TEXT, text: textWithHtml }],
                },
            });

            const result = await mcpTransport.invokeTool(toolId, params, ContentFilterMode.Markdown);

            expect(applySpy).toHaveBeenCalledWith(textWithHtml, ContentFilterMode.Markdown);
            expect(result.textResultForLlm).not.toContain('<script>alert("xss")</script>');
            expect(result.textResultForLlm).toBe(filteredText);
        });

        test("should apply the correct filtering to error responses", async () => {
            const toolId = "failing-tool";
            const params = { prompt: "This will fail" };
            const errorWithInvisibleChars = "Error with invisible char\u200B here";
            const filteredError = "Error with invisible char here";
            mockSettings.featureFlags!.copilot_swe_agent_mcp_filtering = true;

            const applySpy = vi.spyOn(contentFilter, "applyContentFilter");

            mockAxiosInstance.post.mockResolvedValueOnce({
                data: {
                    isToolError: true,
                    content: [
                        {
                            type: CONTENT_TYPE_TEXT,
                            text: errorWithInvisibleChars,
                        },
                    ],
                },
            });

            const result = await mcpTransport.invokeTool(toolId, params, ContentFilterMode.HiddenCharacters);

            expect(applySpy).toHaveBeenCalledWith(errorWithInvisibleChars, ContentFilterMode.HiddenCharacters);
            expect(result.textResultForLlm).not.toContain("\u200B");
            expect(result.textResultForLlm).toBe(filteredError);
        });

        test("should filter all nested string fields of JSON", async () => {
            const toolId = "text-tool";
            const params = { prompt: "Generate text" };

            const JSONStringWithInvisibleChars = JSON.stringify({
                title: "Report with invisible char\u200B here",
                metadata: {
                    author: "John Do\u200Be",
                    version: "1.0\u200B.1",
                },
                sections: [
                    {
                        heading: "Introduction\u200B",
                        content: "This is the intro\u200Bduction content.",
                    },
                    {
                        heading: "Conclusion",
                        content: "Final\u200B thoughts here.",
                    },
                ],
                tags: ["important\u200B", "draft"],
            });

            const filteredJSONString = JSON.stringify({
                title: "Report with invisible char here",
                metadata: {
                    author: "John Doe",
                    version: "1.0.1",
                },
                sections: [
                    {
                        heading: "Introduction",
                        content: "This is the introduction content.",
                    },
                    {
                        heading: "Conclusion",
                        content: "Final thoughts here.",
                    },
                ],
                tags: ["important", "draft"],
            });

            mockSettings.featureFlags!.copilot_swe_agent_mcp_filtering = true;

            const filterAllJSONFieldsSpy = vi.spyOn(contentFilter, "filterAllJsonFields");

            mockAxiosInstance.post.mockResolvedValueOnce({
                data: {
                    isToolError: false,
                    content: [
                        {
                            type: CONTENT_TYPE_TEXT,
                            text: JSONStringWithInvisibleChars,
                        },
                    ],
                },
            });

            const result = await mcpTransport.invokeTool(toolId, params);
            expect(result.textResultForLlm).not.toContain("\u200B");

            const parsedResult = JSON.parse(result.textResultForLlm);
            const parsedExpected = JSON.parse(filteredJSONString);
            expect(parsedResult).toEqual(parsedExpected);

            expect(filterAllJSONFieldsSpy).toHaveBeenCalledTimes(1);

            expect(parsedResult.title).toBe("Report with invisible char here");
            expect(parsedResult.metadata.author).toBe("John Doe");
            expect(parsedResult.sections[0].heading).toBe("Introduction");
        });
    });

    describe("Tool Confirmation", () => {
        let tempDir: string;
        let configPath: string;
        let mockToolConfirmationCallback: ToolConfirmationCallback;

        beforeEach(() => {
            // Create a temporary directory for test files
            tempDir = fs.mkdtempSync(path.join(__dirname, "temp-"));
            configPath = path.join(tempDir, "mcp-config.json");

            // Create mock confirmation callback
            mockToolConfirmationCallback = vi.fn();
        });

        afterEach(() => {
            // Clean up temporary files
            if (fs.existsSync(tempDir)) {
                fs.rmSync(tempDir, { recursive: true, force: true });
            }
        });

        test("should call tool confirmation callback for non-read-only tools", async () => {
            // Arrange
            const toolsConfig = {
                "server1/tool1": {
                    name: "non-readonly-tool",
                    description: "A non-read-only tool",
                    input_schema: {
                        type: "object",
                        properties: { param1: { type: "string" } },
                    },
                    readOnly: false,
                    safeForTelemetry: { name: true, inputsNames: false },
                },
            };

            fs.writeFileSync(configPath, JSON.stringify(toolsConfig, null, 2));

            // Mock confirmation callback to return proceed-once
            vi.mocked(mockToolConfirmationCallback).mockResolvedValue("proceed-once");

            // Mock successful tool invocation
            mockAxiosInstance.post.mockResolvedValue({
                data: {
                    isToolError: false,
                    content: [
                        {
                            type: CONTENT_TYPE_TEXT,
                            text: "Tool executed successfully",
                        },
                    ],
                },
            });

            // Create transport with confirmation callback
            const transportWithConfirmation = new OutOfProcMCPTransport(
                mockSettings,
                mockLogger,
                mockToolConfirmationCallback,
            );

            // Act
            const tools = await transportWithConfirmation.loadTools(configPath);
            const tool = tools[0];
            const result = (await tool.callback({ param1: "test" })) as ToolResultExpanded;

            // Assert
            expect(mockToolConfirmationCallback).toHaveBeenCalledTimes(1);
            expect(mockToolConfirmationCallback).toHaveBeenCalledWith({
                type: "mcp",
                toolName: "non-readonly-tool",
                args: { param1: "test" },
            });
            expect(result.resultType).toBe("success");
        });

        test("should NOT call tool confirmation callback for read-only tools", async () => {
            // Arrange
            const toolsConfig = {
                "server1/tool1": {
                    name: "readonly-tool",
                    description: "A read-only tool",
                    input_schema: {
                        type: "object",
                        properties: { param1: { type: "string" } },
                    },
                    readOnly: true,
                    safeForTelemetry: { name: true, inputsNames: false },
                },
            };

            fs.writeFileSync(configPath, JSON.stringify(toolsConfig, null, 2));

            // Mock successful tool invocation
            mockAxiosInstance.post.mockResolvedValue({
                data: {
                    isToolError: false,
                    content: [
                        {
                            type: CONTENT_TYPE_TEXT,
                            text: "Tool executed successfully",
                        },
                    ],
                },
            });

            // Create transport with confirmation callback
            const transportWithConfirmation = new OutOfProcMCPTransport(
                mockSettings,
                mockLogger,
                mockToolConfirmationCallback,
            );

            // Act
            const tools = await transportWithConfirmation.loadTools(configPath);
            const tool = tools[0];
            const result = (await tool.callback({ param1: "test" })) as ToolResultExpanded;

            // Assert
            expect(mockToolConfirmationCallback).not.toHaveBeenCalled();
            expect(result.resultType).toBe("success");
        });

        test("should return rejected result when user rejects tool confirmation", async () => {
            // Arrange
            const toolsConfig = {
                "server1/tool1": {
                    name: "rejected-tool",
                    description: "A tool that will be rejected",
                    input_schema: {
                        type: "object",
                        properties: { param1: { type: "string" } },
                    },
                    readOnly: false,
                    safeForTelemetry: { name: true, inputsNames: false },
                },
            };

            fs.writeFileSync(configPath, JSON.stringify(toolsConfig, null, 2));

            // Mock confirmation callback to return rejected
            vi.mocked(mockToolConfirmationCallback).mockResolvedValue("rejected");

            // Create transport with confirmation callback
            const transportWithConfirmation = new OutOfProcMCPTransport(
                mockSettings,
                mockLogger,
                mockToolConfirmationCallback,
            );

            // Act
            const tools = await transportWithConfirmation.loadTools(configPath);
            const tool = tools[0];
            const result = (await tool.callback({ param1: "test" })) as ToolResultExpanded;

            // Assert
            expect(mockToolConfirmationCallback).toHaveBeenCalledTimes(1);
            expect(mockToolConfirmationCallback).toHaveBeenCalledWith({
                type: "mcp",
                toolName: "rejected-tool",
                args: { param1: "test" },
            });
            expect(result.resultType).toBe("rejected");
            expect(result.textResultForLlm).toBe(
                "The tool call required confirmation from the user, but the user rejected it. Await further instructions.",
            );
            expect(result.error).toBe(
                "The tool call required confirmation from the user, but the user rejected it. Await further instructions.",
            );

            // Tool should not have been invoked
            expect(mockAxiosInstance.post).not.toHaveBeenCalled();
        });

        test("should return previously-interrupted result when user previously interrupted", async () => {
            // Arrange
            const toolsConfig = {
                "server1/tool1": {
                    name: "interrupted-tool",
                    description: "A tool that was previously interrupted",
                    input_schema: {
                        type: "object",
                        properties: { param1: { type: "string" } },
                    },
                    readOnly: false,
                    safeForTelemetry: { name: true, inputsNames: false },
                },
            };

            fs.writeFileSync(configPath, JSON.stringify(toolsConfig, null, 2));

            // Mock confirmation callback to return previously-interrupted
            vi.mocked(mockToolConfirmationCallback).mockResolvedValue("previously-interrupted");

            // Create transport with confirmation callback
            const transportWithConfirmation = new OutOfProcMCPTransport(
                mockSettings,
                mockLogger,
                mockToolConfirmationCallback,
            );

            // Act
            const tools = await transportWithConfirmation.loadTools(configPath);
            const tool = tools[0];
            const result = (await tool.callback({ param1: "test" })) as ToolResultExpanded;

            // Assert
            expect(mockToolConfirmationCallback).toHaveBeenCalledTimes(1);
            expect(result.resultType).toBe("rejected");
            expect(result.textResultForLlm).toBe(
                "The user rejected an earlier tool call and does not want to take this action right now. Await further instructions.",
            );
            expect(result.error).toBe(
                "The user rejected an earlier tool call and does not want to take this action right now. Await further instructions.",
            );

            // Tool should not have been invoked
            expect(mockAxiosInstance.post).not.toHaveBeenCalled();
        });

        test("should use default alwaysProceedToolConfirmationCallback when none provided", async () => {
            // Arrange
            const toolsConfig = {
                "server1/tool1": {
                    name: "default-callback-tool",
                    description: "A tool using default callback",
                    input_schema: {
                        type: "object",
                        properties: { param1: { type: "string" } },
                    },
                    readOnly: false,
                    safeForTelemetry: { name: true, inputsNames: false },
                },
            };

            fs.writeFileSync(configPath, JSON.stringify(toolsConfig, null, 2));

            // Mock successful tool invocation
            mockAxiosInstance.post.mockResolvedValue({
                data: {
                    isToolError: false,
                    content: [
                        {
                            type: CONTENT_TYPE_TEXT,
                            text: "Tool executed successfully",
                        },
                    ],
                },
            });

            // Create transport without providing confirmation callback (should use default)
            const transportWithDefaultCallback = new OutOfProcMCPTransport(mockSettings, mockLogger);

            // Act
            const tools = await transportWithDefaultCallback.loadTools(configPath);
            const tool = tools[0];
            const result = (await tool.callback({ param1: "test" })) as ToolResultExpanded;

            // Assert - should proceed without stopping (default behavior)
            expect(result.resultType).toBe("success");
            expect(mockAxiosInstance.post).toHaveBeenCalledTimes(1);
        });
    });
});
