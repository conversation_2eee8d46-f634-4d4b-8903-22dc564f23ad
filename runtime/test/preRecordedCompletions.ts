/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import type { OpenAI } from "openai";
import { Tool } from "../src/tools";

export type PreRecordedCompletions = (PreRecordedCompletion | PreRecordedCompletion[])[];

export type PreRecordedStopMessage = {
    type: "stop-message";
    content: string;
};

export type PreRecordedToolCalls = {
    type: "tool-calls";
    toolsToCall: { tool: Tool | string; args?: unknown }[];
    content?: string;
};

/**
 * This type makes it easy to write out a pre-recorded completion that can be used in tests. It avoids you having to write out a bunch of boilerplate object
 * properties and instead just focus on the content of the completion. To get a full OpenAI chat completion from this type, you can use
 * the `inflatePreRecordedCompletion` function.
 */
export type PreRecordedCompletion =
    | PreRecordedStopMessage
    | PreRecordedToolCalls
    | Partial<OpenAI.Chat.Completions.ChatCompletion>;

/**
 * Inflates a pre-recorded completion into a full OpenAI chat completion. If an array, all are combined into a single chat completion made up of multiple choices.
 */
export function inflatePreRecordedCompletion(
    preRecordedCompletion: PreRecordedCompletion | PreRecordedCompletion[],
): OpenAI.Chat.Completions.ChatCompletion {
    if (Array.isArray(preRecordedCompletion)) {
        if (preRecordedCompletion.length === 0) {
            throw new Error("Cannot create completion from empty array");
        }
        return inflatePreRecordedCompletion({
            choices: preRecordedCompletion.map((p) => inflatePreRecordedCompletion(p)).flatMap((r) => r.choices),
        });
    } else if (isChatCompletion(preRecordedCompletion)) {
        const result: OpenAI.Chat.Completions.ChatCompletion = {
            id: getCompletionId(),
            object: "chat.completion",
            created: Date.now(),
            model: "test-model",
            choices: [],
            ...preRecordedCompletion,
        };
        if (result.choices.length === 0) {
            result.choices.push({
                index: 0,
                message: {
                    role: "assistant",
                    content: "Default response",
                    refusal: null,
                },
                finish_reason: "stop",
                logprobs: null,
            });
        }
        return result;
    } else if (preRecordedCompletion.type === "stop-message") {
        return inflatePreRecordedCompletion({
            choices: [
                {
                    index: 0,
                    message: {
                        role: "assistant",
                        content: preRecordedCompletion.content,
                        refusal: null,
                    },
                    finish_reason: "stop",
                    logprobs: null,
                },
            ],
        });
    } else if (preRecordedCompletion.type === "tool-calls") {
        const multiToolResponse = inflatePreRecordedCompletion({
            choices: [
                {
                    index: 0,
                    message: {
                        role: "assistant",
                        content: preRecordedCompletion.content ?? null,
                        tool_calls: [
                            ...preRecordedCompletion.toolsToCall.map((toolToCall, idx) => {
                                const toolName =
                                    typeof toolToCall.tool === "string" ? toolToCall.tool : toolToCall.tool.name;
                                return {
                                    id: `tool-call-${toolName}-${idx}`,
                                    type: "function" as const,
                                    function: {
                                        name: toolName,
                                        arguments: JSON.stringify(toolToCall.args ?? {}),
                                    },
                                };
                            }),
                        ],
                        refusal: null,
                    },
                    finish_reason: "tool_calls",
                    logprobs: null,
                },
            ],
        });
        return multiToolResponse;
    } else {
        throw new Error(`Unknown pre-recorded completion: ${JSON.stringify(preRecordedCompletion)}`);
    }
}

function isChatCompletion(param: PreRecordedCompletion): param is Partial<OpenAI.Chat.Completions.ChatCompletion> {
    return !("type" in param);
}

function getCompletionId(): string {
    return `chatcmpl-${Math.random().toString(36).substring(2, 15)}`;
}
