/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import { describe, expect, it } from "vitest";
import { createAzureSecretProvider } from "../src/helpers/secretProvider";
import { ConsoleLogger } from "../src/runner/logger/console";

describe.skipIf(process.env["TEST_CI"] === "true")("azure getSecret", async () => {
    it("returns the secret value", async () => {
        const provider = createAzureSecretProvider("https://kv-cpd-ci.vault.azure.net/", new ConsoleLogger());
        const result = await provider.getSecret("main-CapiHmacKey");
        expect(result).toBeTypeOf("string");
        expect(result).not.toBe("");
    });
});
