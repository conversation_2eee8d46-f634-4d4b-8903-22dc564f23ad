/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import { MCPRegistry } from "../../src/mcp-client/mcp-registry";
import { ServerConfigProcessor } from "../../src/mcp-client/server-config-processor";
import {
    MCPLocalServerConfig,
    MCPRemoteServerConfig,
    MCPServer<PERSON>onfig,
    MCPServersConfig,
} from "../../src/mcp-client/types";
import { RuntimeSettings } from "../../src/settings";
import { ConsoleLogger } from "../../src/runner/logger/console";

// Type for accessing private methods in tests
interface ServerConfigProcessorWithPrivates {
    remoteEnabled: boolean;
    buildEnvironment(serverConfig: object): Record<string, string>;
    resolveArray(vals: string[] | undefined, env: Record<string, string | undefined>): string[];
    resolveHeaders(
        headers: Record<string, string> | undefined,
        env: Record<string, string | undefined>,
    ): Record<string, string>;
    validateServerConfig(serverName: string, serverConfig: MCPServerConfig): boolean;
    convertPythonToPipx(serverConfig: MCPServerConfig): MCPServerConfig;
}

vi.mock("../../src/mcp-client/mcp-registry", () => {
    return {
        MCPRegistry: vi.fn().mockImplementation(() => ({
            startMCPClient: vi.fn().mockResolvedValue(undefined),
        })),
    };
});

describe("ServerConfigProcessor", () => {
    let processor: ServerConfigProcessor;
    let mockRegistry: MCPRegistry;

    beforeEach(() => {
        vi.clearAllMocks();

        const originalEnv = { ...process.env };
        vi.spyOn(process, "env", "get").mockReturnValue(originalEnv);

        mockRegistry = new MCPRegistry(new ConsoleLogger());
        mockRegistry.startLocalMcpClient = vi.fn().mockResolvedValue(undefined);
        processor = new ServerConfigProcessor(new ConsoleLogger(), mockRegistry);
    });

    afterEach(() => {
        vi.restoreAllMocks();
    });

    const runtimeSettings: RuntimeSettings = {
        blackbird: {
            mode: "initial-search",
            auth: {
                modelBasedRetrievalToken: "keyboard_cat",
                metisApiKey: "keyboard_cat",
            },
        },
    };

    describe("ReadMCPConfigFromEnv", () => {
        it("should point out when GITHUB_COPILOT_MCP_JSON is not set", () => {
            const consoleSpy = vi.spyOn(console, "log").mockImplementation(() => {});

            process.env.GITHUB_COPILOT_MCP_JSON = undefined;
            process.env.GITHUB_COPILOT_3P_MCP_ENABLED = "true";

            processor.ReadMcpConfigFromEnv(runtimeSettings);

            expect(consoleSpy).toHaveBeenCalledWith("No user-provided MCP servers found");
        });

        it("should point out when GITHUB_COPILOT_MCP_JSON has invalid JSON", () => {
            const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});
            process.env.GITHUB_COPILOT_3P_MCP_ENABLED = "true";
            process.env.GITHUB_COPILOT_MCP_JSON = "invalid json";
            processor.ReadMcpConfigFromEnv(runtimeSettings);

            expect(consoleSpy).toHaveBeenCalled();
            expect(consoleSpy.mock.calls[0][0]).toContain(
                "Warning: User-provided MCP servers were defined but invalid:",
            );
        });

        it("should point out GITHUB_COPILOT_MCP_JSON is missing mcpServers property", () => {
            const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});
            process.env.GITHUB_COPILOT_3P_MCP_ENABLED = "true";
            process.env.GITHUB_COPILOT_MCP_JSON = '{"someOtherProperty": {}}';

            processor.ReadMcpConfigFromEnv(runtimeSettings);

            expect(consoleSpy).toHaveBeenCalledWith(
                "Warning: User-provided MCP servers were defined but invalid: Error: User-provided config had incorrect format. Missing 'mcpServers' property.",
            );
        });

        it("should return proper config when GITHUB_COPILOT_MCP_JSON is valid", () => {
            process.env.GITHUB_COPILOT_3P_MCP_ENABLED = "true";
            const userProvidedConfig: MCPServersConfig = {
                mcpServers: {
                    server1: {
                        command: "node",
                        type: "local",
                        args: ["server.js"],
                        tools: ["*"],
                        env: { TEST_VAR: "TEST_VAR" },
                    },
                },
            };

            const validConfig: MCPServersConfig = {
                mcpServers: {
                    server1: {
                        command: "node",
                        type: "local",
                        args: ["server.js"],
                        tools: ["*"],
                        env: { TEST_VAR: "TEST_VAR" },
                        isDefaultServer: false,
                    },
                    "blackbird-mcp-server": {
                        command: "./copilot-developer-action-main/blackbird-mcp-server/blackbird-mcp-server",
                        args: ["stdio"],
                        tools: [],
                        env: {
                            GITHUB_PERSONAL_ACCESS_TOKEN: "GITHUB_PERSONAL_ACCESS_TOKEN",
                        },
                        isDefaultServer: true,
                    },
                    "github-mcp-server": {
                        command: "./copilot-developer-action-main/github-mcp-server/github-mcp-server",
                        args: ["stdio", "--read-only"],
                        tools: ["*"],
                        env: {
                            GITHUB_PERSONAL_ACCESS_TOKEN: "GITHUB_PERSONAL_ACCESS_TOKEN",
                        },
                        isDefaultServer: true,
                    },
                    playwright: {
                        command: "npx",
                        args: [
                            "@playwright/mcp@latest",
                            "--allowed-origins",
                            "localhost;localhost:*;127.0.0.1;127.0.0.1:*",
                            "--viewport-size",
                            "1280, 720",
                        ],
                        tools: ["*"],
                        isDefaultServer: true,
                    },
                },
            };

            process.env.GITHUB_COPILOT_MCP_JSON = JSON.stringify(userProvidedConfig);

            const result = processor.ReadMcpConfigFromEnv(runtimeSettings);

            expect(result).toEqual(validConfig);
        });

        it("should return METIS blackbird when BLACKBIRD_METIS_INDEX_ENABLED is set", () => {
            process.env.BLACKBIRD_METIS_INDEX_ENABLED = "true";
            process.env.GITHUB_COPILOT_3P_MCP_ENABLED = "true";

            const validConfig: MCPServersConfig = {
                mcpServers: {
                    "blackbird-mcp-server": {
                        command: "/agent/blackbird/mcp",
                        args: ["serve", "--index", "/agent/blackbird/metis"],
                        tools: [],
                        env: {
                            COPILOT_API_HMAC_KEY: "CAPI_HMAC_KEY",
                            GITHUB_PERSONAL_ACCESS_TOKEN: "GITHUB_PERSONAL_ACCESS_TOKEN",
                            METIS_API_KEY: "BLACKBIRD_AUTH_METIS_API_KEY",
                            MODEL_BASED_RETRIEVAL_TOKEN: "BLACKBIRD_AUTH_MODEL_BASED_RETRIEVAL_TOKEN",
                        },
                        isDefaultServer: true,
                    },
                    "github-mcp-server": {
                        command: "./copilot-developer-action-main/github-mcp-server/github-mcp-server",
                        args: ["stdio", "--read-only"],
                        tools: ["*"],
                        env: {
                            GITHUB_PERSONAL_ACCESS_TOKEN: "GITHUB_PERSONAL_ACCESS_TOKEN",
                        },
                        isDefaultServer: true,
                    },
                    playwright: {
                        command: "npx",
                        args: [
                            "@playwright/mcp@latest",
                            "--allowed-origins",
                            "localhost;localhost:*;127.0.0.1;127.0.0.1:*",
                            "--viewport-size",
                            "1280, 720",
                        ],
                        tools: ["*"],
                        isDefaultServer: true,
                    },
                },
            };

            process.env.GITHUB_COPILOT_MCP_JSON = undefined;

            const result = processor.ReadMcpConfigFromEnv(runtimeSettings);
            expect(result).toEqual(validConfig);
        });

        it("should return an empty config when GITHUB_COPILOT_3P_MCP_ENABLED is not set", () => {
            const validConfig: MCPServersConfig = {
                mcpServers: {
                    server1: {
                        command: "node",
                        type: "local",
                        args: ["server.js"],
                        tools: ["*"],
                        env: { TEST_VAR: "TEST_VAR" },
                        isDefaultServer: false,
                    },
                },
            };

            const minimalConfig: MCPServersConfig = {
                mcpServers: {
                    "blackbird-mcp-server": {
                        command: "./copilot-developer-action-main/blackbird-mcp-server/blackbird-mcp-server",
                        args: ["stdio"],
                        env: {
                            GITHUB_PERSONAL_ACCESS_TOKEN: "GITHUB_PERSONAL_ACCESS_TOKEN",
                        },
                        tools: [],
                        isDefaultServer: true,
                    },
                    "github-mcp-server": {
                        command: "./copilot-developer-action-main/github-mcp-server/github-mcp-server",
                        args: ["stdio", "--read-only"],
                        tools: ["*"],
                        env: {
                            GITHUB_PERSONAL_ACCESS_TOKEN: "GITHUB_PERSONAL_ACCESS_TOKEN",
                        },
                        isDefaultServer: true,
                    },
                    playwright: {
                        command: "npx",
                        args: [
                            "@playwright/mcp@latest",
                            "--allowed-origins",
                            "localhost;localhost:*;127.0.0.1;127.0.0.1:*",
                            "--viewport-size",
                            "1280, 720",
                        ],
                        tools: ["*"],
                        isDefaultServer: true,
                    },
                },
            };

            process.env.GITHUB_COPILOT_MCP_JSON = JSON.stringify(validConfig);
            process.env.GITHUB_COPILOT_3P_MCP_ENABLED = "false";

            const result = processor.ReadMcpConfigFromEnv(runtimeSettings);
            expect(result).toEqual(minimalConfig);
        });

        it("should let you overwrite github-mcp-server with command", () => {
            process.env.GITHUB_COPILOT_3P_MCP_ENABLED = "true";
            const validConfig: MCPServersConfig = {
                mcpServers: {
                    server1: {
                        command: "node",
                        type: "local",
                        args: ["server.js"],
                        env: { TEST_VAR: "TEST_VAR" },
                        tools: ["*"],
                        isDefaultServer: false,
                    },
                    "blackbird-mcp-server": {
                        command: "./copilot-developer-action-main/blackbird-mcp-server/blackbird-mcp-server",
                        args: ["stdio"],
                        tools: [],
                        env: {
                            GITHUB_PERSONAL_ACCESS_TOKEN: "GITHUB_PERSONAL_ACCESS_TOKEN",
                        },
                        isDefaultServer: true,
                    },
                    // user provided github-mcp-server will be considered not-1P even though normally it is
                    "github-mcp-server": {
                        command: "./new/github-mcp-server",
                        tools: ["create_issue"],
                        args: ["stdios"],
                        env: {
                            GITHUB_PERSONAL_ACCESS_TOKEN: "GITHUB_PERSONAL_ACCESS_TOKEN",
                        },
                        isDefaultServer: false,
                    },
                    playwright: {
                        command: "npx",
                        args: [
                            "@playwright/mcp@latest",
                            "--allowed-origins",
                            "localhost;localhost:*;127.0.0.1;127.0.0.1:*",
                            "--viewport-size",
                            "1280, 720",
                        ],
                        tools: ["*"],
                        isDefaultServer: true,
                    },
                },
            };
            const userConfigProvided: MCPServersConfig = {
                mcpServers: {
                    server1: {
                        command: "node",
                        type: "local",
                        args: ["server.js"],
                        env: { TEST_VAR: "TEST_VAR" },
                        tools: ["*"],
                    },
                    "github-mcp-server": {
                        command: "./new/github-mcp-server",
                        tools: ["create_issue"],
                        args: ["stdios"],
                        env: {
                            GITHUB_PERSONAL_ACCESS_TOKEN: "GITHUB_PERSONAL_ACCESS_TOKEN",
                        },
                    },
                },
            };

            process.env.GITHUB_COPILOT_MCP_JSON = JSON.stringify(userConfigProvided);

            const result = processor.ReadMcpConfigFromEnv(runtimeSettings);

            expect(result).toEqual(validConfig);
        });

        it("should let you overwrite github-mcp-server w/o command", () => {
            process.env.GITHUB_COPILOT_3P_MCP_ENABLED = "true";
            const validConfig: MCPServersConfig = {
                mcpServers: {
                    server1: {
                        command: "node",
                        type: "local",
                        args: ["server.js"],
                        env: { TEST_VAR: "TEST_VAR" },
                        tools: ["tool1", "tool2"],
                        isDefaultServer: false,
                    },
                    "blackbird-mcp-server": {
                        command: "./copilot-developer-action-main/blackbird-mcp-server/blackbird-mcp-server",
                        args: ["stdio"],
                        tools: [],
                        env: {
                            GITHUB_PERSONAL_ACCESS_TOKEN: "GITHUB_PERSONAL_ACCESS_TOKEN",
                        },
                        isDefaultServer: true,
                    },
                    // user provided github-mcp-server will be considered not-1P even though normally it is
                    "github-mcp-server": {
                        command: "./copilot-developer-action-main/github-mcp-server/github-mcp-server",
                        tools: ["create_issue"],
                        args: ["stdio2"],
                        env: {
                            GITHUB_PERSONAL_ACCESS_TOKEN: "GITHUB_PERSONAL_ACCESS_TOKEN",
                        },
                        isDefaultServer: true,
                    },
                    playwright: {
                        command: "npx",
                        args: [
                            "@playwright/mcp@latest",
                            "--allowed-origins",
                            "localhost;localhost:*;127.0.0.1;127.0.0.1:*",
                            "--viewport-size",
                            "1280, 720",
                        ],
                        tools: ["*"],
                        isDefaultServer: true,
                    },
                },
            };
            const userConfigProvided = {
                mcpServers: {
                    server1: {
                        command: "node",
                        type: "local",
                        args: ["server.js"],
                        env: { TEST_VAR: "TEST_VAR" },
                        tools: ["tool1", "tool2"],
                    },
                    "github-mcp-server": {
                        args: ["stdio2"],
                        tools: ["create_issue"],
                        env: {
                            GITHUB_PERSONAL_ACCESS_TOKEN: "GITHUB_PERSONAL_ACCESS_TOKEN",
                        },
                    },
                },
            };

            process.env.GITHUB_COPILOT_MCP_JSON = JSON.stringify(userConfigProvided);

            const result = processor.ReadMcpConfigFromEnv(runtimeSettings);

            expect(result).toEqual(validConfig);
        });

        it("does not let users set isDefaultServer", () => {
            process.env.GITHUB_COPILOT_3P_MCP_ENABLED = "true";
            const validConfig: MCPServersConfig = {
                mcpServers: {
                    server1: {
                        command: "node",
                        type: "local",
                        args: ["server.js"],
                        env: { TEST_VAR: "TEST_VAR" },
                        tools: ["tool1", "tool2"],
                        isDefaultServer: false,
                    },
                    "blackbird-mcp-server": {
                        command: "./copilot-developer-action-main/blackbird-mcp-server/blackbird-mcp-server",
                        args: ["stdio"],
                        env: {
                            GITHUB_PERSONAL_ACCESS_TOKEN: "GITHUB_PERSONAL_ACCESS_TOKEN",
                        },
                        tools: [],
                        isDefaultServer: true,
                    },
                    "github-mcp-server": {
                        command: "./copilot-developer-action-main/github-mcp-server/github-mcp-server",
                        args: ["stdio", "--read-only"],
                        tools: ["*"],
                        env: {
                            GITHUB_PERSONAL_ACCESS_TOKEN: "GITHUB_PERSONAL_ACCESS_TOKEN",
                        },
                        isDefaultServer: true,
                    },
                    playwright: {
                        command: "npx",
                        args: [
                            "@playwright/mcp@latest",
                            "--allowed-origins",
                            "localhost;localhost:*;127.0.0.1;127.0.0.1:*",
                            "--viewport-size",
                            "1280, 720",
                        ],
                        tools: ["*"],
                        isDefaultServer: true,
                    },
                },
            };
            const userConfigProvided = {
                mcpServers: {
                    server1: {
                        command: "node",
                        type: "local",
                        args: ["server.js"],
                        env: { TEST_VAR: "TEST_VAR" },
                        tools: ["tool1", "tool2"],
                        isDefaultServer: true,
                    },
                },
            };

            process.env.GITHUB_COPILOT_MCP_JSON = JSON.stringify(userConfigProvided);

            const result = processor.ReadMcpConfigFromEnv(runtimeSettings);

            expect(result).toEqual(validConfig);
        });
    });

    describe("processServers", () => {
        it("should process all servers in the config", async () => {
            const mockConfig: MCPServersConfig = {
                mcpServers: {
                    server1: {
                        command: "node",
                        args: ["server1.js"],
                        tools: ["*"],
                    },
                    server2: {
                        command: "python",
                        args: ["server2.py"],
                        tools: ["*"],
                    },
                },
            };

            const processSpy = vi.spyOn(processor, "processLocalServer").mockResolvedValue();

            await processor.processServers(mockConfig);

            expect(processSpy).toHaveBeenCalledTimes(2);
            expect(processSpy).toHaveBeenCalledWith("server1", mockConfig.mcpServers.server1);
            expect(processSpy).toHaveBeenCalledWith("server2", mockConfig.mcpServers.server2);
        });
    });

    describe("processServers with remote servers", () => {
        beforeEach(() => {
            vi.spyOn(processor, "processLocalServer").mockResolvedValue();
            vi.spyOn(processor, "processHttpServer").mockResolvedValue();
            vi.spyOn(processor, "processSseServer").mockResolvedValue();

            (processor as unknown as ServerConfigProcessorWithPrivates).remoteEnabled = true;
        });

        it("should process both local and remote servers when remoteEnabled is true", async () => {
            const mockConfig: MCPServersConfig = {
                mcpServers: {
                    localServer: {
                        command: "node",
                        type: "local",
                        args: ["local.js"],
                        tools: ["*"],
                    },
                    httpServer: {
                        type: "http",
                        url: "https://remote-server.com",
                        headers: {},
                        tools: ["*"],
                    },
                    sseServer: {
                        type: "sse",
                        url: "https://remote-server.com/sse",
                        headers: {},
                        tools: ["*"],
                    },
                },
            };

            await processor.processServers(mockConfig);

            expect(processor.processLocalServer).toHaveBeenCalledWith("localServer", mockConfig.mcpServers.localServer);
            expect(processor.processHttpServer).toHaveBeenCalledWith("httpServer", mockConfig.mcpServers.httpServer);
            expect(processor.processSseServer).toHaveBeenCalledWith("sseServer", mockConfig.mcpServers.sseServer);
        });

        it("should skip invalid server configurations", async () => {
            const validateSpy = vi.spyOn(
                processor as unknown as ServerConfigProcessorWithPrivates,
                "validateServerConfig",
            );
            validateSpy.mockImplementation((serverName) => serverName !== "invalidServer");

            const mockConfig: MCPServersConfig = {
                mcpServers: {
                    validLocalServer: {
                        command: "node",
                        type: "local",
                        args: ["local.js"],
                        tools: ["*"],
                    },
                    invalidServer: {
                        type: "unknown",
                        tools: [],
                    } as unknown as MCPServerConfig,
                    validHttpServer: {
                        type: "http",
                        url: "https://remote-server.com",
                        headers: {},
                        tools: ["*"],
                    },
                    validSseServer: {
                        type: "sse",
                        url: "https://remote-server.com/sse",
                        headers: {},
                        tools: ["*"],
                    },
                },
            };

            const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});

            await processor.processServers(mockConfig);

            expect(processor.processLocalServer).toHaveBeenCalledWith(
                "validLocalServer",
                mockConfig.mcpServers.validLocalServer,
            );
            expect(processor.processHttpServer).toHaveBeenCalledWith(
                "validHttpServer",
                mockConfig.mcpServers.validHttpServer,
            );
            expect(processor.processSseServer).toHaveBeenCalledWith(
                "validSseServer",
                mockConfig.mcpServers.validSseServer,
            );
            expect(consoleSpy).toHaveBeenCalledWith('Skipping server "invalidServer" due to invalid configuration.');
        });

        it("should throw an error when no servers config is provided", async () => {
            await expect(processor.processServers(undefined)).rejects.toThrow("No servers to process");
        });
    });

    describe("processLocalServer", () => {
        it("should start MCP client with correct parameters", async () => {
            const buildEnvSpy = vi
                .spyOn(processor as unknown as ServerConfigProcessorWithPrivates, "buildEnvironment")
                .mockReturnValue({ ENV_VAR: "value" });
            const resolveArraySpy = vi
                .spyOn(processor as unknown as ServerConfigProcessorWithPrivates, "resolveArray")
                .mockReturnValue(["resolved-arg"]);

            const serverConfig = {
                command: "node",
                args: ["server.js", "--port=$PORT"],
                tools: ["*"],
            };

            await processor.processLocalServer("testServer", serverConfig);

            expect(buildEnvSpy).toHaveBeenCalledWith(serverConfig);
            expect(resolveArraySpy).toHaveBeenCalledWith(serverConfig.args, {
                ENV_VAR: "value",
            });
            expect(mockRegistry.startLocalMcpClient).toHaveBeenCalledWith("testServer", { ENV_VAR: "value" }, "node", [
                "resolved-arg",
            ]);
        });

        it("should log error when starting client fails", async () => {
            const error = new Error("Start failed");
            vi.spyOn(mockRegistry, "startLocalMcpClient").mockRejectedValue(error);
            const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});

            await processor.processLocalServer("failServer", {
                command: "fail",
                args: [],
                tools: ["*"],
            });

            expect(consoleSpy).toHaveBeenCalledWith("Failed to start MCP client for failServer: Error: Start failed");
        });

        it("should return error when no tools provided", async () => {
            const error = new Error(
                'No tools specified for server "failServer". Please provide a list of tools or "*" to include all tools.',
            );
            vi.spyOn(mockRegistry, "startLocalMcpClient").mockRejectedValue(error);
            const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});
            const invalidConfig = {
                command: "fail",
                args: [],
                tools: undefined,
            } as unknown as MCPLocalServerConfig;

            await processor.processLocalServer("failServer", invalidConfig);
            expect(consoleSpy).toHaveBeenCalledWith(
                'Failed to start MCP client for failServer: Error: No tools specified for server "failServer". Please provide a list of tools or "*" to include all tools.',
            );
        });

        it("should throw an error when GITHUB_COPILOT_MCP_JSON has a non-local server", async () => {
            const error = new Error(
                'Unsupported server type "not-local" for server "failServer". Only "Local" is supported.',
            );
            vi.spyOn(mockRegistry, "startLocalMcpClient").mockRejectedValue(error);
            const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});

            await processor.processLocalServer("failServer", {
                command: "node",
                type: "not-local",
                args: ["server.js"],
                tools: ["*"],
                env: { TEST_VAR: "TEST_VAR" },
            } as unknown as MCPLocalServerConfig);
            expect(consoleSpy).toHaveBeenCalledWith(
                'Failed to start MCP client for failServer: Error: Unsupported server type "not-local" for server "failServer". Only "Local" is supported.',
            );
        });

        it("should handle python servers well", async () => {
            const buildEnvSpy = vi
                .spyOn(processor as unknown as ServerConfigProcessorWithPrivates, "buildEnvironment")
                .mockReturnValue({ ENV_VAR: "value" });
            const resolveArraySpy = vi
                .spyOn(processor as unknown as ServerConfigProcessorWithPrivates, "resolveArray")
                .mockReturnValue(["run", "module_name", "other_arg"]);

            const serverConfig: MCPServerConfig = {
                command: "python",
                args: ["-m", "module_name", "other_arg"],
                env: { ENV_VAR: "value" },
                tools: ["mytool"],
            };

            const expectedUpdatedConfig: MCPServerConfig = {
                command: "pipx",
                args: ["run", "module_name", "other_arg"],
                env: { ENV_VAR: "value" },
                tools: ["mytool"],
            };

            await processor.processLocalServer("testServer", serverConfig);

            expect(buildEnvSpy).toHaveBeenCalledWith(expectedUpdatedConfig);
            expect(resolveArraySpy).toHaveBeenCalledWith(expectedUpdatedConfig.args, expectedUpdatedConfig.env);
            expect(mockRegistry.startLocalMcpClient).toHaveBeenCalledWith("testServer", { ENV_VAR: "value" }, "pipx", [
                "run",
                "module_name",
                "other_arg",
            ]);
        });
    });

    describe("buildEnvironment", () => {
        it("should return empty object when no env is specified", () => {
            const result = (processor as unknown as ServerConfigProcessorWithPrivates).buildEnvironment({});
            expect(result).toEqual({});
        });

        it("should filter environment variables based on config", () => {
            process.env.TEST_VAR1 = "value1";
            process.env.TEST_VAR2 = "value2";
            process.env.IGNORED_VAR = "ignored";

            const serverConfig = {
                env: {
                    TEST_VAR1: "TEST_VAR1",
                    TEST_VAR2: "TEST_VAR2",
                },
            };

            const result = (processor as unknown as ServerConfigProcessorWithPrivates).buildEnvironment(serverConfig);

            expect(result).toEqual({
                TEST_VAR1: "value1",
                TEST_VAR2: "value2",
            });
        });

        it("should handle empty env object", () => {
            const serverConfig = {
                env: {},
            };

            const result = (processor as unknown as ServerConfigProcessorWithPrivates).buildEnvironment(serverConfig);
            expect(result).toEqual({});
        });

        it("should handle empty elements in array", () => {
            process.env.TEST_VAR1 = "value1";
            process.env.TEST_VAR2 = "value2";

            const serverConfig = {
                env: {
                    TEST_VAR1: "TEST_VAR1",
                    TEST_VAR3: "",
                    TEST_VAR2: "TEST_VAR2",
                },
            };

            const result = (processor as unknown as ServerConfigProcessorWithPrivates).buildEnvironment(serverConfig);
            expect(result).toEqual({
                TEST_VAR1: "value1",
                TEST_VAR2: "value2",
            });
        });

        it("should handle whitespace in env string", () => {
            process.env.TEST_VAR1 = "value1";
            process.env.TEST_VAR2 = "value2";

            const serverConfig = {
                env: {
                    TEST_VAR1: " TEST_VAR1 ",
                    TEST_VAR3: "",
                    TEST_VAR2: "TEST_VAR2    ",
                },
            };

            const result = (processor as unknown as ServerConfigProcessorWithPrivates).buildEnvironment(serverConfig);
            expect(result).toEqual({
                TEST_VAR1: "value1",
                TEST_VAR2: "value2",
            });
        });
    });

    describe("resolveArray", () => {
        it("should replace environment variables in arguments", () => {
            const args = ["--port=$PORT", "--host=$HOST", "--unchanged"];
            const env = { PORT: "3000", HOST: "localhost" };

            const result = (processor as unknown as ServerConfigProcessorWithPrivates).resolveArray(args, env);

            expect(result).toEqual(["--port=3000", "--host=localhost", "--unchanged"]);
        });

        it("should not replace variables that are not in the environment", () => {
            const args = ["--port=$PORT", "--missing=$MISSING"];
            const env = { PORT: "3000" };

            const result = (processor as unknown as ServerConfigProcessorWithPrivates).resolveArray(args, env);

            expect(result).toEqual(["--port=3000", "--missing=$MISSING"]);
        });

        it("should handle null or undefined arguments", () => {
            const env = { PORT: "3000" };

            expect(
                (processor as unknown as ServerConfigProcessorWithPrivates).resolveArray(
                    null as unknown as string[],
                    env,
                ),
            ).toEqual([]);
            expect((processor as unknown as ServerConfigProcessorWithPrivates).resolveArray(undefined, env)).toEqual(
                [],
            );
        });

        it("should handle multiple environment variables in a single argument", () => {
            const args = ["--connection=$HOST:$PORT/db"];
            const env = { HOST: "localhost", PORT: "5432" };

            const result = (processor as unknown as ServerConfigProcessorWithPrivates).resolveArray(args, env);

            expect(result).toEqual(["--connection=localhost:5432/db"]);
        });
    });

    describe("resolveHeaders", () => {
        it("should resolve environment variables in header values", () => {
            const headers = {
                Authorization: "Bearer $TOKEN",
                "X-API-Key": "$API_KEY",
                "Content-Type": "application/json",
            };
            const env = { TOKEN: "my-token-value", API_KEY: "secret-key" };

            const result = (processor as unknown as ServerConfigProcessorWithPrivates).resolveHeaders(headers, env);

            expect(result).toEqual({
                Authorization: "Bearer my-token-value",
                "X-API-Key": "secret-key",
                "Content-Type": "application/json",
            });
        });

        it("should handle multiple vars separated by spaces", () => {
            const headers = {
                Authorization: "Bearer $TOKEN $API_KEY",
                "Content-Type": "application/json",
            };
            const env = { TOKEN: "my-token-value", API_KEY: "secret-key" };

            const result = (processor as unknown as ServerConfigProcessorWithPrivates).resolveHeaders(headers, env);

            expect(result).toEqual({
                Authorization: "Bearer my-token-value secret-key",
                "Content-Type": "application/json",
            });
        });

        it("should handle multiple variables in single header value", () => {
            const headers = { "Custom-Header": "$PREFIX-$SUFFIX" };
            const env = { PREFIX: "start", SUFFIX: "end" };

            const result = (processor as unknown as ServerConfigProcessorWithPrivates).resolveHeaders(headers, env);

            expect(result).toEqual({ "Custom-Header": "start-end" });
        });

        it("should return empty object when headers is undefined", () => {
            const result = (processor as unknown as ServerConfigProcessorWithPrivates).resolveHeaders(undefined, {
                TOKEN: "value",
            });
            expect(result).toEqual({});
        });
    });

    describe("convertPythonToPipx", () => {
        it("should replace Python correctly", () => {
            const serverConfig: MCPServerConfig = {
                command: "python",
                args: ["-m", "module_name", "other_arg"],
                env: { ENV_VAR: "value" },
                tools: ["mytool"],
            };

            const expectedUpdatedConfig: MCPServerConfig = {
                command: "pipx",
                args: ["run", "module_name", "other_arg"],
                env: { ENV_VAR: "value" },
                tools: ["mytool"],
            };

            const result = (processor as unknown as ServerConfigProcessorWithPrivates).convertPythonToPipx(
                serverConfig,
            );

            expect(result).toEqual(expectedUpdatedConfig);
        });
    });

    describe("processHttpServer", () => {
        it("should start remote MCP client with correct parameters", async () => {
            mockRegistry.startHttpMcpClient = vi.fn().mockResolvedValue(undefined);

            const serverConfig: MCPRemoteServerConfig = {
                type: "http",
                url: "https://test-remote-server.com",
                headers: { Authorization: "Bearer token123" },
                tools: ["tool1", "tool2"],
            };

            await processor.processHttpServer("testRemoteServer", serverConfig);

            expect(mockRegistry.startHttpMcpClient).toHaveBeenCalledWith("testRemoteServer", serverConfig);
        });

        it("should log error when starting remote client fails", async () => {
            const error = new Error("Remote start failed");
            mockRegistry.startHttpMcpClient = vi.fn().mockRejectedValue(error);
            const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});

            const serverConfig: MCPRemoteServerConfig = {
                type: "http",
                url: "https://failing-server.com",
                headers: { Authorization: "Bearer token123" },
                tools: ["tool1"],
            };

            await processor.processHttpServer("failRemoteServer", serverConfig);

            expect(consoleSpy).toHaveBeenCalledWith(
                "Failed to start MCP client for remote server failRemoteServer: Error: Remote start failed",
            );
        });

        it("should log success when remote client starts successfully", async () => {
            mockRegistry.startHttpMcpClient = vi.fn().mockResolvedValue(undefined);
            const consoleSpy = vi.spyOn(console, "log").mockImplementation(() => {});

            const serverConfig: MCPRemoteServerConfig = {
                type: "http",
                url: "https://successful-server.com",
                headers: { Authorization: "Bearer token123" },
                tools: ["tool1"],
            };

            await processor.processHttpServer("successRemoteServer", serverConfig);

            expect(consoleSpy).toHaveBeenCalledWith("Started MCP client for remote server successRemoteServer");
        });
    });

    describe("processSseServer", () => {
        it("should start sse MCP client with correct parameters", async () => {
            mockRegistry.startSseMcpClient = vi.fn().mockResolvedValue(undefined);

            const serverConfig: MCPRemoteServerConfig = {
                type: "sse",
                url: "https://test-remote-server.com",
                headers: { Authorization: "Bearer token123" },
                tools: ["tool1", "tool2"],
            };

            await processor.processSseServer("testRemoteServer", serverConfig);

            expect(mockRegistry.startSseMcpClient).toHaveBeenCalledWith("testRemoteServer", serverConfig);
        });

        it("should log error when starting sse client fails", async () => {
            const error = new Error("Remote start failed");
            mockRegistry.startSseMcpClient = vi.fn().mockRejectedValue(error);
            const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});

            const serverConfig: MCPRemoteServerConfig = {
                type: "sse",
                url: "https://failing-server.com",
                headers: { Authorization: "Bearer token123" },
                tools: ["tool1"],
            };

            await processor.processSseServer("failRemoteServer", serverConfig);

            expect(consoleSpy).toHaveBeenCalledWith(
                "Failed to start MCP client for remote server failRemoteServer: Error: Remote start failed",
            );
        });

        it("should log success when sse client starts successfully", async () => {
            mockRegistry.startSseMcpClient = vi.fn().mockResolvedValue(undefined);
            const consoleSpy = vi.spyOn(console, "log").mockImplementation(() => {});

            const serverConfig: MCPRemoteServerConfig = {
                type: "sse",
                url: "https://successful-server.com",
                headers: { Authorization: "Bearer token123" },
                tools: ["tool1"],
            };

            await processor.processSseServer("successRemoteServer", serverConfig);

            expect(consoleSpy).toHaveBeenCalledWith("Started MCP client for remote server successRemoteServer");
        });
    });
});
