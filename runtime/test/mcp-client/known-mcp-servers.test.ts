/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { isKnownMCPTool, knownMCPServers } from "../../src/mcp-client/known-mcp-servers";

describe("isKnownMCPTool", () => {
    it("should return true for a known server with a known tool", () => {
        expect(isKnownMCPTool("fetch", "fetch")).toBe(true);
    });

    it("should return false for known server with an unknown tool", () => {
        expect(isKnownMCPTool("fetch", "unknown_tool")).toBe(false);
    });

    it("should return false for an unknown server", () => {
        expect(isKnownMCPTool("unknown_server", "any_tool")).toBe(false);
    });

    it("should be case insensitive for server names", () => {
        expect(isKnownMCPTool("FETCH", "fetch")).toBe(true);
    });

    it("should be case insensitive for server names", () => {
        expect(isKnownMCPTool("fetch", "FETCH")).toBe(true);
    });

    it("should resolve aliases correctly", () => {
        expect(isKnownMCPTool("azure-azmcp", "aks")).toBe(true);
    });

    it("should handle circular references in aliases", () => {
        const withCircularReference = { ...knownMCPServers };
        withCircularReference["circular-a"] = "circular-b";
        withCircularReference["circular-b"] = "circular-a";

        // Should return false and not hang due to infinite loop
        expect(isKnownMCPTool("circular-a", "some-tool", withCircularReference)).toBe(false);
    });

    it("should handle self-referencing aliases", () => {
        const withSelfReference = { ...knownMCPServers };
        withSelfReference["self-ref"] = "self-ref";

        // Should return false and not hang due to infinite loop
        expect(isKnownMCPTool("self-ref", "some-tool", withSelfReference)).toBe(false);
    });
});
