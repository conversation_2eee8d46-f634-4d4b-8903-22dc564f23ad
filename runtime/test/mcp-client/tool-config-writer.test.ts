/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import { randomUUID } from "crypto";
import * as fs from "fs";
import { tmpdir } from "os";
import { join as pathJoin } from "path";
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import { MCPRegistry } from "../../src/mcp-client/mcp-registry";
import { ToolConfigWriter } from "../../src/mcp-client/tool-config-writer";
import { MCPServersConfig } from "../../src/mcp-client/types";
import { ConsoleLogger } from "../../src/runner/logger/console";

process.env.COPILOT_AGENT_MCP_SERVER_TEMP = pathJoin(tmpdir(), `mcp-${randomUUID}`);

vi.mock("fs", () => ({
    promises: {
        writeFile: vi.fn(),
    },
}));

const mockTools = {
    tools: [
        { name: "tool1", description: "Test tool 1" },
        { name: "tool2", description: "Test tool 2" },
    ],
};

const mockMCPServersConfigAllTools: MCPServersConfig = {
    mcpServers: {
        server1: {
            command: "command1",
            args: ["arg1", "arg2"],
            env: { KEY1: "env1", KEY2: "env2" },
            tools: ["*"],
        },
    },
};

describe("ToolConfigWriter", () => {
    let mockRegistry: MCPRegistry;
    let toolConfigWriter: ToolConfigWriter;

    beforeEach(() => {
        vi.clearAllMocks();

        mockRegistry = {
            getTools: vi.fn().mockResolvedValue(mockTools),
        } as unknown as MCPRegistry;

        toolConfigWriter = new ToolConfigWriter(new ConsoleLogger(), mockRegistry);

        vi.spyOn(console, "log").mockImplementation(() => {});
        vi.spyOn(console, "error").mockImplementation(() => {});
    });

    afterEach(() => {
        vi.restoreAllMocks();
    });

    it("should successfully write tool configuration to file", async () => {
        const expectedPath = pathJoin(process.env.COPILOT_AGENT_MCP_SERVER_TEMP ?? ".", "mcp-config.json");
        const expectedContent = JSON.stringify(mockTools, null, 2);

        await toolConfigWriter.writeToolConfig(mockMCPServersConfigAllTools);

        expect(mockRegistry.getTools).toHaveBeenCalledTimes(1);
        expect(fs.promises.writeFile).toHaveBeenCalledWith(expectedPath, expectedContent);
        expect(console.log).toHaveBeenCalledWith(`Tool configuration written to ${expectedPath}`);
        expect(console.error).not.toHaveBeenCalled();
    });

    it("should log error when file write fails", async () => {
        const expectedError = new Error("File write error");
        vi.mocked(fs.promises.writeFile).mockRejectedValue(expectedError);

        await toolConfigWriter.writeToolConfig(mockMCPServersConfigAllTools);

        expect(mockRegistry.getTools).toHaveBeenCalledTimes(1);
        expect(fs.promises.writeFile).toHaveBeenCalled();
        expect(console.error).toHaveBeenCalledWith("Failed to write tool configuration: Error: File write error");
        expect(console.log).not.toHaveBeenCalled();
    });
});
