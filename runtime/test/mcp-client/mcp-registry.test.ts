/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import { ContentFilterMode } from "../../src/helpers/content-filter";
import { ClientFactory } from "../../src/mcp-client/factories/client-factory";
import { StdioTransportConfig, TransportFactory } from "../../src/mcp-client/factories/transport-factory";
import { MCPRegistry } from "../../src/mcp-client/mcp-registry";
import { MCPRemoteServerConfig, MCPServersConfig } from "../../src/mcp-client/types";
import { ConsoleLogger } from "../../src/runner/logger/console";

// Minimal mock types for testing
interface MockClient {
    connect: ReturnType<typeof vi.fn>;
    listTools: ReturnType<typeof vi.fn>;
    onclose?: (() => void) | null;
    onerror?: ((error: Error) => void) | null;
}

interface MockTransport {
    type: string;
}

vi.mock("@modelcontextprotocol/sdk/client/index.js", () => {
    return {
        Client: vi.fn().mockImplementation(() => ({
            connect: vi.fn().mockResolvedValue(undefined),
            listTools: vi.fn().mockResolvedValue({
                tools: [
                    {
                        name: "tool1",
                        description: "Tool 1",
                        inputSchema: { type: "object" },
                    },
                    {
                        name: "tool2",
                        description: null,
                        inputSchema: { type: "string" },
                    },
                ],
            }),
            onclose: null,
            onerror: null,
        })),
    };
});

const mockMCPServersConfigAllTools: MCPServersConfig = {
    mcpServers: {
        server1: {
            command: "command1",
            args: ["arg1", "arg2"],
            env: { env1: "value1", env2: "value2" },
            tools: ["*"],
        },
        server2: {
            command: "command2",
            args: ["arg3", "arg4"],
            env: { env3: "value3", env4: "value4" },
            tools: ["*"],
        },
    },
};

describe("MCPRegistry", () => {
    let registry: MCPRegistry;
    let mockClientFactory: ClientFactory;
    let mockTransportFactory: TransportFactory;
    let mockClient: MockClient;
    let mockTransport: MockTransport;

    beforeEach(() => {
        mockTransport = { type: "mock-transport" };

        mockClient = {
            connect: vi.fn().mockResolvedValue(undefined),
            listTools: vi.fn().mockResolvedValue({
                tools: [
                    {
                        name: "tool1",
                        description: "Tool 1",
                        inputSchema: { type: "object" },
                    },
                    {
                        name: "tool2",
                        description: null,
                        inputSchema: { type: "string" },
                    },
                ],
            }),
            onclose: undefined,
            onerror: undefined,
        };

        mockClientFactory = {
            createClient: vi.fn().mockReturnValue(mockClient),
        };

        mockTransportFactory = {
            createTransport: vi.fn().mockReturnValue(mockTransport),
        };

        registry = new MCPRegistry(new ConsoleLogger(), mockClientFactory, mockTransportFactory);

        vi.spyOn(console, "log").mockImplementation(() => {});
        vi.spyOn(console, "error").mockImplementation(() => {});
    });

    afterEach(() => {
        vi.restoreAllMocks();
    });

    describe("startMcpClient", () => {
        it("should create transport with correct parameters", async () => {
            const serverName = "test-server";
            const env = { TEST_ENV: "value" };
            const command = "test-command";
            const args = ["arg1", "arg2"];

            await registry.startLocalMcpClient(serverName, env, command, args);

            expect(mockTransportFactory.createTransport).toHaveBeenCalledWith({
                type: "stdio",
                command,
                args,
                env: {
                    ...env,
                    PATH: expect.any(String),
                },
            } as StdioTransportConfig);
        });

        it("should create client with correct parameters", async () => {
            const serverName = "test-server";
            const env = { TEST_ENV: "value" };
            const command = "test-command";
            const args = ["arg1", "arg2"];

            await registry.startLocalMcpClient(serverName, env, command, args);

            expect(mockClientFactory.createClient).toHaveBeenCalledWith(
                {
                    name: expect.stringContaining("github-copilot-developer"),
                    version: "1.0.0",
                },
                {
                    capabilities: {
                        experimental: undefined,
                        roots: undefined,
                        sampling: undefined,
                    },
                },
            );
        });

        it("should connect the client with the transport", async () => {
            const serverName = "test-server";
            const env = { TEST_ENV: "value" };
            const command = "test-command";
            const args = ["arg1", "arg2"];

            await registry.startLocalMcpClient(serverName, env, command, args);

            expect(mockClient.connect).toHaveBeenCalledWith(mockTransport);
        });

        it("should store the client in the clients registry", async () => {
            const serverName = "test-server";
            const env = { TEST_ENV: "value" };
            const command = "test-command";
            const args = ["arg1", "arg2"];

            await registry.startLocalMcpClient(serverName, env, command, args);

            expect(registry.clients[serverName]).toBe(mockClient);
        });

        it("should set up onclose handler that removes client from registry", async () => {
            const serverName = "test-server";
            const env = { TEST_ENV: "value" };
            const command = "test-command";
            const args = ["arg1", "arg2"];

            await registry.startLocalMcpClient(serverName, env, command, args);

            expect(mockClient.onclose).toBeTypeOf("function");

            // Simulate client close
            mockClient.onclose?.();

            expect(registry.clients[serverName]).toBeUndefined();
        });

        it("should set up onerror handler", async () => {
            const serverName = "test-server";
            const env = { TEST_ENV: "value" };
            const command = "test-command";
            const args = ["arg1", "arg2"];

            await registry.startLocalMcpClient(serverName, env, command, args);

            expect(mockClient.onerror).toBeTypeOf("function");
        });
    });

    describe("startSseMcpClient", () => {
        it("should create transport with correct parameters", async () => {
            const serverName = "test-server";
            const serverConfig: MCPRemoteServerConfig = {
                type: "sse",
                url: "https://example.com/sse-endpoint",
                headers: { Authorization: "Bearer token123" },
                tools: ["*"],
            };

            await registry.startSseMcpClient(serverName, serverConfig);

            expect(mockTransportFactory.createTransport).toHaveBeenCalledWith({
                type: "sse",
                url: serverConfig.url,
                headers: serverConfig.headers,
            });
        });

        it("should create client with correct parameters", async () => {
            const serverName = "test-server";
            const serverConfig: MCPRemoteServerConfig = {
                type: "sse",
                url: "https://example.com/sse-endpoint",
                headers: { Authorization: "Bearer token123" },
                tools: ["*"],
            };

            await registry.startSseMcpClient(serverName, serverConfig);

            expect(mockClientFactory.createClient).toHaveBeenCalledWith(
                {
                    name: expect.stringContaining("github-copilot-developer"),
                    version: "1.0.0",
                },
                {
                    capabilities: {
                        experimental: undefined,
                        roots: undefined,
                        sampling: undefined,
                    },
                },
            );
        });

        it("should connect the client with the transport", async () => {
            const serverName = "test-server";
            const serverConfig: MCPRemoteServerConfig = {
                type: "sse",
                url: "https://example.com/sse-endpoint",
                headers: { Authorization: "Bearer token123" },
                tools: ["*"],
            };

            await registry.startSseMcpClient(serverName, serverConfig);

            expect(mockClient.connect).toHaveBeenCalledWith(mockTransport);
        });

        it("should store the client in the clients registry", async () => {
            const serverName = "test-server";
            const serverConfig: MCPRemoteServerConfig = {
                type: "sse",
                url: "https://example.com/sse-endpoint",
                headers: { Authorization: "Bearer token123" },
                tools: ["*"],
            };

            await registry.startSseMcpClient(serverName, serverConfig);

            expect(registry.clients[serverName]).toBe(mockClient);
        });

        it("should set up onclose handler that removes client from registry", async () => {
            const serverName = "test-server";
            const serverConfig: MCPRemoteServerConfig = {
                type: "sse",
                url: "https://example.com/sse-endpoint",
                headers: { Authorization: "Bearer token123" },
                tools: ["*"],
            };

            await registry.startSseMcpClient(serverName, serverConfig);

            expect(mockClient.onclose).toBeTypeOf("function");

            // Simulate client close
            mockClient.onclose?.();

            expect(registry.clients[serverName]).toBeUndefined();
        });

        it("should set up onerror handler", async () => {
            const serverName = "test-server";
            const serverConfig: MCPRemoteServerConfig = {
                type: "sse",
                url: "https://example.com/sse-endpoint",
                headers: { Authorization: "Bearer token123" },
                tools: ["*"],
            };

            await registry.startSseMcpClient(serverName, serverConfig);

            expect(mockClient.onerror).toBeTypeOf("function");
        });
    });

    describe("startHttpMcpClient", () => {
        it("should create transport with correct parameters", async () => {
            const serverName = "test-server";
            const serverConfig: MCPRemoteServerConfig = {
                type: "http",
                url: "https://example.com/remote-endpoint",
                headers: { Authorization: "Bearer token456" },
                tools: ["*"],
            };

            await registry.startHttpMcpClient(serverName, serverConfig);

            expect(mockTransportFactory.createTransport).toHaveBeenCalledWith({
                type: "http",
                url: serverConfig.url,
                headers: serverConfig.headers,
            });
        });

        it("should create client with correct parameters", async () => {
            const serverName = "test-server";
            const serverConfig: MCPRemoteServerConfig = {
                type: "http",
                url: "https://example.com/remote-endpoint",
                headers: { Authorization: "Bearer token456" },
                tools: ["*"],
            };

            await registry.startHttpMcpClient(serverName, serverConfig);

            expect(mockClientFactory.createClient).toHaveBeenCalledWith(
                {
                    name: expect.stringContaining("github-copilot-developer"),
                    version: "1.0.0",
                },
                {
                    capabilities: {
                        experimental: undefined,
                        roots: undefined,
                        sampling: undefined,
                    },
                },
            );
        });

        it("should connect the client with the transport", async () => {
            const serverName = "test-server";
            const serverConfig: MCPRemoteServerConfig = {
                type: "http",
                url: "https://example.com/remote-endpoint",
                headers: { Authorization: "Bearer token456" },
                tools: ["*"],
            };

            await registry.startHttpMcpClient(serverName, serverConfig);

            expect(mockClient.connect).toHaveBeenCalledWith(mockTransport);
        });

        it("should store the client in the clients registry", async () => {
            const serverName = "test-server";
            const serverConfig: MCPRemoteServerConfig = {
                type: "http",
                url: "https://example.com/remote-endpoint",
                headers: { Authorization: "Bearer token456" },
                tools: ["*"],
            };

            await registry.startHttpMcpClient(serverName, serverConfig);

            expect(registry.clients[serverName]).toBe(mockClient);
        });

        it("should set up onclose handler that removes client from registry", async () => {
            const serverName = "test-server";
            const serverConfig: MCPRemoteServerConfig = {
                type: "http",
                url: "https://example.com/remote-endpoint",
                headers: { Authorization: "Bearer token456" },
                tools: ["*"],
            };

            await registry.startHttpMcpClient(serverName, serverConfig);

            expect(mockClient.onclose).toBeTypeOf("function");

            // Simulate client close
            mockClient.onclose?.();

            expect(registry.clients[serverName]).toBeUndefined();
        });

        it("should set up onerror handler", async () => {
            const serverName = "test-server";
            const serverConfig: MCPRemoteServerConfig = {
                type: "http",
                url: "https://example.com/remote-endpoint",
                headers: { Authorization: "Bearer token456" },
                tools: ["*"],
            };

            await registry.startHttpMcpClient(serverName, serverConfig);

            expect(mockClient.onerror).toBeTypeOf("function");
        });
    });

    describe("getTools", () => {
        it("should set safeForTelemetry properties correctly for known MCP server tools", async () => {
            const fetchClient = {
                ...mockClient,
                listTools: vi.fn().mockResolvedValue({
                    tools: [
                        {
                            name: "fetch",
                            description: "Fetch tool",
                            inputSchema: { type: "object" },
                        },
                        {
                            name: "fetch-async",
                            description: "Fetch tool",
                            inputSchema: { type: "object" },
                        },
                    ],
                }),
            };

            const fetchConfig = {
                mcpServers: {
                    fetch: {
                        command: "fetch-cmd",
                        args: [],
                        env: {},
                        tools: ["fetch", "fetch-async"],
                    },
                },
            };

            registry.clients = {
                fetch: fetchClient as unknown as typeof registry.clients.fetch,
            };

            const tools = await registry.getTools(fetchConfig);

            expect(tools["fetch/fetch"]).toBeDefined();
            expect(tools["fetch/fetch"].safeForTelemetry.name).toBe(true);
            expect(tools["fetch/fetch"].safeForTelemetry.inputsNames).toBe(false);

            expect(tools["fetch/fetch-async"]).toBeDefined();
            expect(tools["fetch/fetch-async"].safeForTelemetry.name).toBe(false);
            expect(tools["fetch/fetch-async"].safeForTelemetry.inputsNames).toBe(false);
        });

        it("should fetch tools from all clients", async () => {
            const client1 = { ...mockClient };
            const client2 = { ...mockClient };
            client1.listTools = vi.fn().mockResolvedValue({
                tools: [
                    {
                        name: "tool1",
                        description: "Tool 1",
                        inputSchema: { type: "object" },
                    },
                ],
            });

            client2.listTools = vi.fn().mockResolvedValue({
                tools: [
                    {
                        name: "tool2",
                        description: "Tool 2",
                        inputSchema: { type: "string" },
                    },
                ],
            });

            registry.clients = {
                server1: client1 as unknown as typeof registry.clients.server1,
                server2: client2 as unknown as typeof registry.clients.server2,
            };

            const tools = await registry.getTools(mockMCPServersConfigAllTools);

            expect(client1.listTools).toHaveBeenCalled();
            expect(client2.listTools).toHaveBeenCalled();

            expect(tools).toEqual({
                "server1/tool1": {
                    name: "server1-tool1",
                    description: "Tool 1",
                    input_schema: { type: "object" },
                    safeForTelemetry: { name: false, inputsNames: false },
                    filterMode: "hidden_characters",
                },
                "server2/tool2": {
                    name: "server2-tool2",
                    description: "Tool 2",
                    input_schema: { type: "string" },
                    safeForTelemetry: { name: false, inputsNames: false },
                    filterMode: "hidden_characters",
                },
            });
        });

        it("should handle bad values for filter mode on individual tools", async () => {
            const client1 = { ...mockClient };
            client1.listTools = vi.fn().mockResolvedValue({
                tools: [
                    {
                        name: "tool1",
                        description: "Tool 1",
                        inputSchema: { type: "object" },
                    },
                    {
                        name: "tool2",
                        description: "Tool 2",
                        inputSchema: { type: "object" },
                    },
                ],
            });

            registry.clients = {
                server1: client1 as unknown as typeof registry.clients.server1,
            };

            const mockMCPServersConfigAllToolsBadFilter: MCPServersConfig = {
                mcpServers: {
                    server1: {
                        command: "command1",
                        args: ["arg1", "arg2"],
                        env: { env1: "value1", env2: "value2" },
                        tools: ["tool1", "tool2"],
                        filterMapping: {
                            tool1: "fake" as ContentFilterMode,
                            tool2: "markdown" as ContentFilterMode,
                        },
                    },
                },
            };

            const tools = await registry.getTools(mockMCPServersConfigAllToolsBadFilter);

            expect(client1.listTools).toHaveBeenCalled();

            expect(tools).toEqual({
                "server1/tool1": {
                    name: "server1-tool1",
                    description: "Tool 1",
                    input_schema: { type: "object" },
                    safeForTelemetry: { name: false, inputsNames: false },
                    filterMode: "hidden_characters",
                },
                "server1/tool2": {
                    name: "server1-tool2",
                    description: "Tool 2",
                    input_schema: { type: "object" },
                    safeForTelemetry: { name: false, inputsNames: false },
                    filterMode: "markdown",
                },
            });
        });

        it("should handle bad values for filter mode on all tools", async () => {
            const client1 = { ...mockClient };
            client1.listTools = vi.fn().mockResolvedValue({
                tools: [
                    {
                        name: "tool1",
                        description: "Tool 1",
                        inputSchema: { type: "object" },
                    },
                ],
            });

            registry.clients = {
                server1: client1 as unknown as typeof registry.clients.server1,
            };

            const mockMCPServersConfigAllToolsBadFilter: MCPServersConfig = {
                mcpServers: {
                    server1: {
                        command: "command1",
                        args: ["arg1", "arg2"],
                        env: { env1: "value1", env2: "value2" },
                        tools: ["tool1"],
                        filterMapping: "thing-that-does-not-exist" as ContentFilterMode,
                    },
                },
            };

            const tools = await registry.getTools(mockMCPServersConfigAllToolsBadFilter);

            expect(client1.listTools).toHaveBeenCalled();

            expect(tools).toEqual({
                "server1/tool1": {
                    name: "server1-tool1",
                    description: "Tool 1",
                    input_schema: { type: "object" },
                    safeForTelemetry: { name: false, inputsNames: false },
                    filterMode: "hidden_characters",
                },
            });
        });

        it("should handle accepted values for filter mode on individual tools", async () => {
            const client1 = { ...mockClient };
            client1.listTools = vi.fn().mockResolvedValue({
                tools: [
                    {
                        name: "tool1",
                        description: "Tool 1",
                        inputSchema: { type: "object" },
                    },
                    {
                        name: "tool2",
                        description: "Tool 2",
                        inputSchema: { type: "object" },
                    },
                ],
            });

            registry.clients = {
                server1: client1 as unknown as typeof registry.clients.server1,
            };

            const mockMCPServersConfigValidFilters: MCPServersConfig = {
                mcpServers: {
                    server1: {
                        command: "command1",
                        args: ["arg1", "arg2"],
                        env: { env1: "value1", env2: "value2" },
                        tools: ["tool1", "tool2"],
                        filterMapping: {
                            tool1: "markdown" as ContentFilterMode,
                            tool2: "hidden_characters" as ContentFilterMode,
                        },
                    },
                },
            };

            const tools = await registry.getTools(mockMCPServersConfigValidFilters);

            expect(client1.listTools).toHaveBeenCalled();

            expect(tools).toEqual({
                "server1/tool1": {
                    name: "server1-tool1",
                    description: "Tool 1",
                    input_schema: { type: "object" },
                    safeForTelemetry: { name: false, inputsNames: false },
                    filterMode: "markdown",
                },
                "server1/tool2": {
                    name: "server1-tool2",
                    description: "Tool 2",
                    input_schema: { type: "object" },
                    safeForTelemetry: { name: false, inputsNames: false },
                    filterMode: "hidden_characters",
                },
            });
        });

        it("should handle accepted values for filter mode on all tools", async () => {
            const client1 = { ...mockClient };
            client1.listTools = vi.fn().mockResolvedValue({
                tools: [
                    {
                        name: "tool1",
                        description: "Tool 1",
                        inputSchema: { type: "object" },
                    },
                    {
                        name: "tool2",
                        description: "Tool 2",
                        inputSchema: { type: "object" },
                    },
                ],
            });

            registry.clients = {
                server1: client1 as unknown as typeof registry.clients.server1,
            };

            const mockMCPServersConfigAllToolsGoodFilter: MCPServersConfig = {
                mcpServers: {
                    server1: {
                        command: "command1",
                        args: ["arg1", "arg2"],
                        env: { env1: "value1", env2: "value2" },
                        tools: ["tool1", "tool2"],
                        filterMapping: "markdown" as ContentFilterMode,
                    },
                },
            };

            const tools = await registry.getTools(mockMCPServersConfigAllToolsGoodFilter);

            expect(client1.listTools).toHaveBeenCalled();

            expect(tools).toEqual({
                "server1/tool1": {
                    name: "server1-tool1",
                    description: "Tool 1",
                    input_schema: { type: "object" },
                    safeForTelemetry: { name: false, inputsNames: false },
                    filterMode: "markdown",
                },
                "server1/tool2": {
                    name: "server1-tool2",
                    description: "Tool 2",
                    input_schema: { type: "object" },
                    safeForTelemetry: { name: false, inputsNames: false },
                    filterMode: "markdown",
                },
            });
        });

        it("should fetch subset of tools", async () => {
            const client1 = { ...mockClient };
            const client2 = { ...mockClient };

            client1.listTools = vi.fn().mockResolvedValue({
                tools: [
                    {
                        name: "tool1",
                        description: "Tool 1",
                        inputSchema: { type: "object" },
                    },
                ],
            });

            client2.listTools = vi.fn().mockResolvedValue({
                tools: [
                    {
                        name: "tool2",
                        description: "Tool 2",
                        inputSchema: { type: "string" },
                    },
                ],
            });

            registry.clients = {
                server1: client1 as unknown as typeof registry.clients.server1,
                server2: client2 as unknown as typeof registry.clients.server2,
            };

            const mockMCPServersConfigTool1: MCPServersConfig = {
                mcpServers: {
                    server1: {
                        command: "command1",
                        args: ["arg1", "arg2"],
                        env: { env1: "value1", env2: "value2" },
                        tools: ["tool1"],
                    },
                },
            };

            const tools = await registry.getTools(mockMCPServersConfigTool1);

            expect(client1.listTools).toHaveBeenCalled();
            expect(client2.listTools).toHaveBeenCalled();

            expect(tools).toEqual({
                "server1/tool1": {
                    name: "server1-tool1",
                    description: "Tool 1",
                    input_schema: { type: "object" },
                    safeForTelemetry: { name: false, inputsNames: false },
                    filterMode: "hidden_characters",
                },
            });
        });

        it("should not include any tools when [] is provided", async () => {
            const client1 = { ...mockClient };
            const client2 = { ...mockClient };

            client1.listTools = vi.fn().mockResolvedValue({
                tools: [
                    {
                        name: "tool1",
                        description: "Tool 1",
                        inputSchema: { type: "object" },
                        safeForTelemetry: { name: false, inputsNames: false },
                    },
                ],
            });

            client2.listTools = vi.fn().mockResolvedValue({
                tools: [
                    {
                        name: "tool2",
                        description: "Tool 2",
                        inputSchema: { type: "string" },
                        safeForTelemetry: { name: false, inputsNames: false },
                    },
                ],
            });

            registry.clients = {
                server1: client1 as unknown as typeof registry.clients.server1,
                server2: client2 as unknown as typeof registry.clients.server2,
            };

            const mockMCPServersConfigNoTools: MCPServersConfig = {
                mcpServers: {
                    server1: {
                        command: "command1",
                        args: ["arg1", "arg2"],
                        env: { env1: "value1", env2: "value2" },
                        tools: [],
                    },
                    server2: {
                        command: "command2",
                        args: ["arg3", "arg4"],
                        env: { env3: "value3", env4: "value4" },
                        tools: [],
                    },
                },
            };

            const tools = await registry.getTools(mockMCPServersConfigNoTools);

            expect(client1.listTools).toHaveBeenCalled();
            expect(client2.listTools).toHaveBeenCalled();

            expect(tools).toEqual({});
        });

        it("should use tool name as description if description is null", async () => {
            mockClient.listTools = vi.fn().mockResolvedValue({
                tools: [
                    {
                        name: "tool1",
                        description: null,
                        inputSchema: { type: "string" },
                    },
                ],
            });

            registry.clients = {
                server1: mockClient as unknown as typeof registry.clients.server1,
            };

            const tools = await registry.getTools(mockMCPServersConfigAllTools);

            expect(tools["server1/tool1"].description).toBe("server1-tool1");
        });

        it("should mark the tools of not default servers to have names and input names not safe for telemetry", async () => {
            mockClient.listTools = vi.fn().mockResolvedValue({
                tools: [
                    {
                        name: "tool1",
                        description: null,
                        inputSchema: { type: "string" },
                    },
                ],
            });

            registry.clients = {
                server1: mockClient as unknown as typeof registry.clients.server1,
            };

            const tools = await registry.getTools(mockMCPServersConfigAllTools);

            expect(tools["server1/tool1"].safeForTelemetry.name).toBe(false);
            expect(tools["server1/tool1"].safeForTelemetry.inputsNames).toBe(false);
        });

        it("should mark tools of default servers to have names and input names as safe for telemetry", async () => {
            mockClient.listTools = vi.fn().mockResolvedValue({
                tools: [
                    {
                        name: "tool1",
                        description: null,
                        inputSchema: { type: "string" },
                    },
                ],
            });

            registry.clients = {
                server1: mockClient as unknown as typeof registry.clients.server1,
            };

            const mockMCPServersConfigTool2: MCPServersConfig = {
                mcpServers: {
                    server1: {
                        command: "command1",
                        args: ["arg1", "arg2"],
                        env: { env1: "value1", env2: "value2" },
                        tools: ["tool1"],
                        isDefaultServer: true,
                    },
                },
            };

            const tools = await registry.getTools(mockMCPServersConfigTool2);

            expect(tools["server1/tool1"].safeForTelemetry.name).toBe(true);
            expect(tools["server1/tool1"].safeForTelemetry.inputsNames).toBe(true);
        });

        it("should continue processing if a client throws an error", async () => {
            const client1 = { ...mockClient };
            const client2 = { ...mockClient };

            client1.listTools = vi.fn().mockRejectedValue(new Error("Connection failed"));

            client2.listTools = vi.fn().mockResolvedValue({
                tools: [
                    {
                        name: "tool2",
                        description: "Tool 2",
                        inputSchema: { type: "string" },
                    },
                ],
            });

            registry.clients = {
                server1: client1 as unknown as typeof registry.clients.server1,
                server2: client2 as unknown as typeof registry.clients.server2,
            };

            const tools = await registry.getTools(mockMCPServersConfigAllTools);

            expect(tools).toEqual({
                "server2/tool2": {
                    name: "server2-tool2",
                    description: "Tool 2",
                    input_schema: { type: "string" },
                    safeForTelemetry: { name: false, inputsNames: false },
                    filterMode: "hidden_characters",
                },
            });
        });

        it("should set readOnly to true when tool has readOnlyHint annotation", async () => {
            const readOnlyMockClient = { ...mockClient };
            readOnlyMockClient.listTools = vi.fn().mockResolvedValue({
                tools: [
                    {
                        name: "read-only-tool",
                        description: "A read-only tool",
                        inputSchema: { type: "object" },
                        annotations: {
                            readOnlyHint: true,
                        },
                    },
                    {
                        name: "regular-tool",
                        description: "A regular tool",
                        inputSchema: { type: "object" },
                        annotations: {
                            readOnlyHint: false,
                        },
                    },
                    {
                        name: "no-annotation-tool",
                        description: "A tool without annotations",
                        inputSchema: { type: "object" },
                    },
                ],
            });

            registry.clients = {
                server1: readOnlyMockClient as unknown as typeof registry.clients.server1,
            };

            const mockConfig: MCPServersConfig = {
                mcpServers: {
                    server1: {
                        command: "command1",
                        args: ["arg1"],
                        env: {},
                        tools: ["*"],
                    },
                },
            };

            const tools = await registry.getTools(mockConfig);

            expect(tools["server1/read-only-tool"].readOnly).toBe(true);
            expect(tools["server1/regular-tool"].readOnly).toBe(false);
            expect(tools["server1/no-annotation-tool"].readOnly).toBe(undefined);
        });

        it("should return empty object when no clients are registered", async () => {
            registry.clients = {};

            const tools = await registry.getTools(mockMCPServersConfigAllTools);

            expect(tools).toEqual({});
        });
    });
});
