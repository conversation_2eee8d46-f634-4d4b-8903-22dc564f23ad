/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import express from "express";
import supertest from "supertest";
import { beforeEach, describe, expect, it, vi } from "vitest";
import type { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { MCPRegistry } from "../../src/mcp-client/mcp-registry";
import { MCPServer } from "../../src/mcp-client/mcp-server";
import { DEFAULT_REQUEST_OPTIONS } from "../../src/tools/mcp-transport";
import { ConsoleLogger } from "../../src/runner/logger/console";

describe("MCPServer", () => {
    let server: MCPServer;
    let mockRegistry: MCPRegistry;
    let mockExpress: express.Application;
    let mockExpressFactory: { createExpressApp: () => express.Application };

    beforeEach(() => {
        mockExpress = express();
        mockExpress.use(express.json());

        mockExpressFactory = {
            createExpressApp: vi.fn().mockReturnValue(mockExpress),
        };

        mockRegistry = {
            clients: {},
        } as MCPRegistry;

        server = new MCPServer(new ConsoleLogger(), mockRegistry, mockExpressFactory);

        // Start server without actually listening on a port
        vi.spyOn(mockExpress, "listen").mockImplementation((_port, callback?: () => void) => {
            callback?.();
            return {} as ReturnType<express.Application["listen"]>;
        });
    });

    it("should handle errors thrown during tool execution and return error message in result field", async () => {
        // Arrange
        await server.startServer();
        const mockClient = {
            callTool: vi.fn().mockRejectedValue(new Error("Tool execution failed")),
        } as unknown as Client;
        mockRegistry.clients = {
            "test-server": mockClient,
        };

        // Act
        const response = await supertest(mockExpress)
            .post("/invoke-tool")
            .send({
                toolId: "test-server/test-tool",
                params: { param1: "value1" },
            });

        // Assert
        expect(mockClient.callTool).toHaveBeenCalledWith({
            name: "test-tool",
            arguments: { param1: "value1" },
            requestOptions: DEFAULT_REQUEST_OPTIONS,
        });
        expect(response.status).toBe(200); // Should return 200 not 500
        expect(response.body).toEqual({
            isToolError: true,
            content: [
                {
                    text: "Tool call failed: Tool execution failed",
                    type: "text",
                },
            ],
        });
    });

    it("should return image content when tool execution is successful", async () => {
        // Arrange
        await server.startServer();
        const mockClient = {
            callTool: vi.fn().mockResolvedValue({
                content: [
                    {
                        data: "abc",
                        mimeType: "image/png",
                        type: "image",
                    },
                    {
                        text: `Screenshot viewport and save it as`,
                        type: "text",
                    },
                ],
            }),
        } as unknown as Client;
        mockRegistry.clients = {
            "test-server": mockClient,
        };

        // Act
        const response = await supertest(mockExpress)
            .post("/invoke-tool")
            .send({
                toolId: "test-server/test-tool",
                params: { param1: "value1" },
            });

        // Assert
        expect(mockClient.callTool).toHaveBeenCalledWith({
            name: "test-tool",
            arguments: { param1: "value1" },
            requestOptions: DEFAULT_REQUEST_OPTIONS,
        });
        expect(response.status).toBe(200);
        expect(response.body).toEqual({
            content: [
                {
                    data: expect.any(String),
                    mimeType: "image/png",
                    type: "image",
                },
                {
                    text: expect.stringContaining(`Screenshot viewport and save it as`),
                    type: "text",
                },
            ],
            isToolError: false,
        });
    });
});
