/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { expect, test, vi } from "vitest";

// Import the required modules
import { MCPServer } from "../../src/mcp-client/mcp-server";
import { ServerConfigProcessor } from "../../src/mcp-client/server-config-processor";
import { ToolConfigWriter } from "../../src/mcp-client/tool-config-writer";
import { ConsoleLogger } from "../../src/runner/logger/console";

vi.mock("../../src/mcp-client/mcp-registry");
vi.mock("../../src/mcp-client/mcp-server");
vi.mock("fs");
vi.mock("../../src/mcp-client/ServerConfigProcessor");
vi.mock("../../src/mcp-client/ToolConfigWriter");
vi.mock("../../src/model/capi/sessions-client", () => ({
    getCapiHmac: vi.fn().mockResolvedValue("mocked-hmac-key"),
}));
vi.mock("../../src/mcp-client/server-config-processor");
vi.mock("../../src/mcp-client/tool-config-writer");
vi.mock("../../src/runner/logger/console");

test("should process servers, write config and start server successfully", async () => {
    const mockServerConfigProcessor = {
        ReadMcpConfigFromEnv: vi.fn().mockReturnValue({ mcpServers: {} }),
        processServers: vi.fn().mockResolvedValue(undefined),
    };

    const mockToolConfigWriter = {
        writeToolConfig: vi.fn().mockResolvedValue(undefined),
    };

    const mockMCPServer = {
        startServer: vi.fn().mockResolvedValue(undefined),
    };

    const mockLogger = {
        log: vi.fn(),
        error: vi.fn(),
        debug: vi.fn(),
        info: vi.fn(),
        notice: vi.fn(),
        warning: vi.fn(),
        startGroup: vi.fn(),
        endGroup: vi.fn(),
        isDebug: vi.fn().mockReturnValue(false),
    };

    vi.mocked(ServerConfigProcessor).mockImplementation(
        () => mockServerConfigProcessor as unknown as ServerConfigProcessor,
    );
    vi.mocked(ToolConfigWriter).mockImplementation(() => mockToolConfigWriter as unknown as ToolConfigWriter);
    vi.mocked(MCPServer).mockImplementation(() => mockMCPServer as unknown as MCPServer);
    vi.mocked(ConsoleLogger).mockImplementation(() => mockLogger as unknown as ConsoleLogger);

    const exitSpy = vi.spyOn(process, "exit").mockImplementation(() => undefined as never);

    // Clear module cache to ensure fresh import with mocked logger
    vi.resetModules();
    const module = await import("../../src/mcp-client");
    await module.main();

    expect(mockServerConfigProcessor.ReadMcpConfigFromEnv).toHaveBeenCalled();
    expect(mockServerConfigProcessor.processServers).toHaveBeenCalledWith({
        mcpServers: {},
    });
    expect(mockToolConfigWriter.writeToolConfig).toHaveBeenCalled();
    expect(mockMCPServer.startServer).toHaveBeenCalled();
    expect(mockLogger.log).toHaveBeenCalledWith("MCP Tool server started successfully");
    expect(exitSpy).not.toHaveBeenCalled();
});

test("should throw error when ReadMCPConfigFromEnv returns null", async () => {
    const mockServerConfigProcessor = {
        ReadMcpConfigFromEnv: vi.fn().mockReturnValue(null),
        processServers: vi.fn(),
    };

    vi.mocked(ServerConfigProcessor).mockImplementation(
        () => mockServerConfigProcessor as unknown as ServerConfigProcessor,
    );

    // Clear module cache to ensure fresh import with mocked logger
    vi.resetModules();
    const module = await import("../../src/mcp-client");
    await expect(module.main()).rejects.toThrow("Failed to read MCP configuration from environment variable.");

    expect(mockServerConfigProcessor.processServers).not.toHaveBeenCalled();
});

test("should handle error when processServers fails", async () => {
    const mockServerConfigProcessor = {
        ReadMcpConfigFromEnv: vi.fn().mockReturnValue({ mcpServers: {} }),
        processServers: vi.fn().mockRejectedValue(new Error("Process servers failed")),
    };

    vi.mocked(ServerConfigProcessor).mockImplementation(
        () => mockServerConfigProcessor as unknown as ServerConfigProcessor,
    );

    // Clear module cache to ensure fresh import with mocked logger
    vi.resetModules();
    const module = await import("../../src/mcp-client");
    await expect(module.main()).rejects.toThrow("Process servers failed");
});

test("should handle error when writeToolConfig fails", async () => {
    const mockServerConfigProcessor = {
        ReadMcpConfigFromEnv: vi.fn().mockReturnValue({ mcpServers: {} }),
        processServers: vi.fn().mockResolvedValue(undefined),
    };

    const mockToolConfigWriter = {
        writeToolConfig: vi.fn().mockRejectedValue(new Error("Write config failed")),
    };

    vi.mocked(ServerConfigProcessor).mockImplementation(
        () => mockServerConfigProcessor as unknown as ServerConfigProcessor,
    );
    vi.mocked(ToolConfigWriter).mockImplementation(() => mockToolConfigWriter as unknown as ToolConfigWriter);

    // Clear module cache to ensure fresh import with mocked logger
    vi.resetModules();
    const module = await import("../../src/mcp-client");
    await expect(module.main()).rejects.toThrow("Write config failed");
});

test("should exit process when startServer fails", async () => {
    const mockServerConfigProcessor = {
        ReadMcpConfigFromEnv: vi.fn().mockReturnValue({ mcpServers: {} }),
        processServers: vi.fn().mockResolvedValue(undefined),
    };

    const mockToolConfigWriter = {
        writeToolConfig: vi.fn().mockResolvedValue(undefined),
    };

    const mockMCPServer = {
        startServer: vi.fn().mockRejectedValue(new Error("Start server failed")),
    };

    const mockLogger = {
        log: vi.fn(),
        error: vi.fn(),
        debug: vi.fn(),
        info: vi.fn(),
        notice: vi.fn(),
        warning: vi.fn(),
        startGroup: vi.fn(),
        endGroup: vi.fn(),
        isDebug: vi.fn().mockReturnValue(false),
    };

    vi.mocked(ServerConfigProcessor).mockImplementation(
        () => mockServerConfigProcessor as unknown as ServerConfigProcessor,
    );
    vi.mocked(ToolConfigWriter).mockImplementation(() => mockToolConfigWriter as unknown as ToolConfigWriter);
    vi.mocked(MCPServer).mockImplementation(() => mockMCPServer as unknown as MCPServer);
    vi.mocked(ConsoleLogger).mockImplementation(() => mockLogger as unknown as ConsoleLogger);

    const exitSpy = vi.spyOn(process, "exit").mockImplementation(() => undefined as never);

    // Clear module cache to ensure fresh import with mocked logger
    vi.resetModules();
    const module = await import("../../src/mcp-client");
    await module.main();

    expect(mockLogger.error).toHaveBeenCalledWith("Failed to start MCP server: Error: Start server failed");
    expect(exitSpy).toHaveBeenCalledWith(1);
});
