/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { describe, expect, test } from "vitest";
import { Diagnostic, DiagnosticSeverity, Position, Range } from "vscode-languageserver-types";
import {
    fileUriToPath,
    getDiagnosticsEditFeedbackText,
    organizeDiagnosticsByFile,
    prettyPrintDiagnostic,
} from "../../src/lsp/diagnostics.js";

describe("diagnostics", () => {
    describe("prettyPrintDiagnostic", () => {
        test("formats error diagnostic correctly", () => {
            const diagnostic: Diagnostic = {
                range: Range.create(Position.create(10, 5), Position.create(10, 15)),
                message: 'Cannot find name "undefined_var"',
                severity: DiagnosticSeverity.Error,
            };

            const result = prettyPrintDiagnostic(diagnostic);
            expect(result).toBe('ERROR [11:6] Cannot find name "undefined_var"');
        });

        test("formats warning diagnostic correctly", () => {
            const diagnostic: Diagnostic = {
                range: Range.create(Position.create(0, 0), Position.create(0, 10)),
                message: "Unused variable",
                severity: DiagnosticSeverity.Warning,
            };

            const result = prettyPrintDiagnostic(diagnostic);
            expect(result).toBe("WARN [1:1] Unused variable");
        });

        test("formats information diagnostic correctly", () => {
            const diagnostic: Diagnostic = {
                range: Range.create(Position.create(25, 12), Position.create(25, 20)),
                message: "Consider using const instead of let",
                severity: DiagnosticSeverity.Information,
            };

            const result = prettyPrintDiagnostic(diagnostic);
            expect(result).toBe("INFO [26:13] Consider using const instead of let");
        });

        test("formats hint diagnostic correctly", () => {
            const diagnostic: Diagnostic = {
                range: Range.create(Position.create(5, 8), Position.create(5, 15)),
                message: "This can be simplified",
                severity: DiagnosticSeverity.Hint,
            };

            const result = prettyPrintDiagnostic(diagnostic);
            expect(result).toBe("HINT [6:9] This can be simplified");
        });

        test("formats diagnostic without severity correctly", () => {
            const diagnostic: Diagnostic = {
                range: Range.create(Position.create(2, 3), Position.create(2, 8)),
                message: "Generic diagnostic message",
            };

            const result = prettyPrintDiagnostic(diagnostic);
            expect(result).toBe("DIAGNOSTIC [3:4] Generic diagnostic message");
        });

        test("handles zero-based indexing correctly", () => {
            const diagnostic: Diagnostic = {
                range: Range.create(Position.create(0, 0), Position.create(0, 5)),
                message: "First line, first character",
                severity: DiagnosticSeverity.Error,
            };

            const result = prettyPrintDiagnostic(diagnostic);
            expect(result).toBe("ERROR [1:1] First line, first character");
        });
    });

    describe("organizeDiagnosticsByFile", () => {
        test("organizes single file diagnostics correctly", () => {
            const diagnostics = [
                {
                    uri: "/path/to/file.ts",
                    diagnostics: [
                        {
                            range: Range.create(Position.create(0, 0), Position.create(0, 5)),
                            message: "Error 1",
                            severity: DiagnosticSeverity.Error,
                        },
                        {
                            range: Range.create(Position.create(5, 0), Position.create(5, 10)),
                            message: "Warning 1",
                            severity: DiagnosticSeverity.Warning,
                        },
                    ],
                },
            ];

            const result = organizeDiagnosticsByFile(diagnostics);

            expect(result.size).toBe(1);
            expect(result.has("/path/to/file.ts")).toBe(true);
            expect(result.get("/path/to/file.ts")).toHaveLength(2);
            expect(result.get("/path/to/file.ts")?.[0].message).toBe("Error 1");
            expect(result.get("/path/to/file.ts")?.[1].message).toBe("Warning 1");
        });

        test("organizes multiple file diagnostics correctly", () => {
            const diagnostics = [
                {
                    uri: "/path/to/file1.ts",
                    diagnostics: [
                        {
                            range: Range.create(Position.create(0, 0), Position.create(0, 5)),
                            message: "File1 Error",
                            severity: DiagnosticSeverity.Error,
                        },
                    ],
                },
                {
                    uri: "/path/to/file2.ts",
                    diagnostics: [
                        {
                            range: Range.create(Position.create(10, 5), Position.create(10, 15)),
                            message: "File2 Warning",
                            severity: DiagnosticSeverity.Warning,
                        },
                    ],
                },
            ];

            const result = organizeDiagnosticsByFile(diagnostics);

            expect(result.size).toBe(2);
            expect(result.has("/path/to/file1.ts")).toBe(true);
            expect(result.has("/path/to/file2.ts")).toBe(true);
            expect(result.get("/path/to/file1.ts")?.[0].message).toBe("File1 Error");
            expect(result.get("/path/to/file2.ts")?.[0].message).toBe("File2 Warning");
        });

        test("handles duplicate file URIs by merging diagnostics", () => {
            const diagnostics = [
                {
                    uri: "/path/to/file.ts",
                    diagnostics: [
                        {
                            range: Range.create(Position.create(0, 0), Position.create(0, 5)),
                            message: "First diagnostic",
                            severity: DiagnosticSeverity.Error,
                        },
                    ],
                },
                {
                    uri: "/path/to/file.ts",
                    diagnostics: [
                        {
                            range: Range.create(Position.create(5, 0), Position.create(5, 10)),
                            message: "Second diagnostic",
                            severity: DiagnosticSeverity.Warning,
                        },
                    ],
                },
            ];

            const result = organizeDiagnosticsByFile(diagnostics);

            expect(result.size).toBe(1);
            expect(result.get("/path/to/file.ts")).toHaveLength(2);
            expect(result.get("/path/to/file.ts")?.[0].message).toBe("First diagnostic");
            expect(result.get("/path/to/file.ts")?.[1].message).toBe("Second diagnostic");
        });

        test("handles empty diagnostics array", () => {
            const result = organizeDiagnosticsByFile([]);
            expect(result.size).toBe(0);
        });

        test("handles files with empty diagnostics", () => {
            const diagnostics = [
                {
                    uri: "/path/to/file.ts",
                    diagnostics: [],
                },
            ];

            const result = organizeDiagnosticsByFile(diagnostics);

            expect(result.size).toBe(1);
            expect(result.get("/path/to/file.ts")).toHaveLength(0);
        });
    });

    describe("fileUriToPath", () => {
        test("removes file:// protocol correctly", () => {
            const fileUri = "file:///Users/<USER>/project/src/file.ts";
            const result = fileUriToPath(fileUri);
            expect(result).toBe("/Users/<USER>/project/src/file.ts");
        });

        test("handles file URI without trailing slash", () => {
            const fileUri = "file://C:/Windows/System32/file.txt";
            const result = fileUriToPath(fileUri);
            expect(result).toBe("C:/Windows/System32/file.txt");
        });

        test("returns path unchanged when no file:// protocol", () => {
            const path = "/absolute/path/to/file.ts";
            const result = fileUriToPath(path);
            expect(result).toBe("/absolute/path/to/file.ts");
        });

        test("returns relative path unchanged when no file:// protocol", () => {
            const path = "relative/path/to/file.ts";
            const result = fileUriToPath(path);
            expect(result).toBe("relative/path/to/file.ts");
        });

        test("handles empty string", () => {
            const result = fileUriToPath("");
            expect(result).toBe("");
        });

        test("handles file:// only", () => {
            const result = fileUriToPath("file://");
            expect(result).toBe("");
        });
    });

    describe("getDiagnosticsEditFeedbackText", () => {
        test("returns empty string when no diagnostics", () => {
            const result = getDiagnosticsEditFeedbackText("/path/to/edited.ts", []);
            expect(result).toBe("");
        });

        test("formats feedback for edited file only", () => {
            const diagnostics = [
                {
                    uri: "file:///path/to/edited.ts",
                    diagnostics: [
                        {
                            range: Range.create(Position.create(0, 0), Position.create(0, 5)),
                            message: "Syntax error",
                            severity: DiagnosticSeverity.Error,
                        },
                        {
                            range: Range.create(Position.create(5, 10), Position.create(5, 20)),
                            message: "Unused variable",
                            severity: DiagnosticSeverity.Warning,
                        },
                    ],
                },
            ];

            const result = getDiagnosticsEditFeedbackText("/path/to/edited.ts", diagnostics);

            const expected = `
<file_diagnostics>
The edited file currently has the following issues which need to be resolved:
ERROR [1:1] Syntax error
WARN [6:11] Unused variable
</file_diagnostics>`;

            expect(result).toBe(expected);
        });

        test("formats feedback for other files only", () => {
            const diagnostics = [
                {
                    uri: "file:///path/to/other.ts",
                    diagnostics: [
                        {
                            range: Range.create(Position.create(10, 5), Position.create(10, 15)),
                            message: "Type error",
                            severity: DiagnosticSeverity.Error,
                        },
                    ],
                },
            ];

            const result = getDiagnosticsEditFeedbackText("/path/to/edited.ts", diagnostics);

            const expected = `
<project_diagnostics>
Other files in the project have the following issues which need to be resolved:
/path/to/other.ts
ERROR [11:6] Type error
</project_diagnostics>
`;

            expect(result).toBe(expected);
        });

        test("formats feedback for both edited file and other files", () => {
            const diagnostics = [
                {
                    uri: "file:///path/to/edited.ts",
                    diagnostics: [
                        {
                            range: Range.create(Position.create(0, 0), Position.create(0, 5)),
                            message: "Edited file error",
                            severity: DiagnosticSeverity.Error,
                        },
                    ],
                },
                {
                    uri: "file:///path/to/other1.ts",
                    diagnostics: [
                        {
                            range: Range.create(Position.create(5, 0), Position.create(5, 10)),
                            message: "Other file 1 error",
                            severity: DiagnosticSeverity.Error,
                        },
                    ],
                },
                {
                    uri: "file:///path/to/other2.ts",
                    diagnostics: [
                        {
                            range: Range.create(Position.create(15, 8), Position.create(15, 20)),
                            message: "Other file 2 warning",
                            severity: DiagnosticSeverity.Warning,
                        },
                    ],
                },
            ];

            const result = getDiagnosticsEditFeedbackText("/path/to/edited.ts", diagnostics);

            const expected = `
<file_diagnostics>
The edited file currently has the following issues which need to be resolved:
ERROR [1:1] Edited file error
</file_diagnostics>
<project_diagnostics>
Other files in the project have the following issues which need to be resolved:
/path/to/other1.ts
ERROR [6:1] Other file 1 error

/path/to/other2.ts
WARN [16:9] Other file 2 warning
</project_diagnostics>
`;

            expect(result).toBe(expected);
        });

        test("handles multiple diagnostics in the same other file", () => {
            const diagnostics = [
                {
                    uri: "file:///path/to/other.ts",
                    diagnostics: [
                        {
                            range: Range.create(Position.create(0, 0), Position.create(0, 5)),
                            message: "First error",
                            severity: DiagnosticSeverity.Error,
                        },
                        {
                            range: Range.create(Position.create(10, 5), Position.create(10, 15)),
                            message: "Second warning",
                            severity: DiagnosticSeverity.Warning,
                        },
                    ],
                },
            ];

            const result = getDiagnosticsEditFeedbackText("/path/to/edited.ts", diagnostics);

            const expected = `
<project_diagnostics>
Other files in the project have the following issues which need to be resolved:
/path/to/other.ts
ERROR [1:1] First error
WARN [11:6] Second warning
</project_diagnostics>
`;

            expect(result).toBe(expected);
        });

        test("handles URIs without file:// protocol", () => {
            const diagnostics = [
                {
                    uri: "/path/to/edited.ts",
                    diagnostics: [
                        {
                            range: Range.create(Position.create(2, 3), Position.create(2, 8)),
                            message: "Direct path error",
                            severity: DiagnosticSeverity.Error,
                        },
                    ],
                },
            ];

            const result = getDiagnosticsEditFeedbackText("/path/to/edited.ts", diagnostics);

            const expected = `
<file_diagnostics>
The edited file currently has the following issues which need to be resolved:
ERROR [3:4] Direct path error
</file_diagnostics>`;

            expect(result).toBe(expected);
        });

        test("handles empty diagnostics for files", () => {
            const diagnostics = [
                {
                    uri: "file:///path/to/edited.ts",
                    diagnostics: [],
                },
                {
                    uri: "file:///path/to/other.ts",
                    diagnostics: [],
                },
            ];

            const result = getDiagnosticsEditFeedbackText("/path/to/edited.ts", diagnostics);
            // With the improved filtering, files with empty diagnostics are now filtered out
            // so no sections should be created when all files have empty diagnostics
            expect(result).toBe("");
        });

        test("returns empty string when edited file has empty diagnostics and no other files", () => {
            const diagnostics = [
                {
                    uri: "file:///path/to/edited.ts",
                    diagnostics: [],
                },
            ];

            const result = getDiagnosticsEditFeedbackText("/path/to/edited.ts", diagnostics);
            expect(result).toBe("");
        });
    });
});
