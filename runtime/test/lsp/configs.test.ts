/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { mkdir, mkdtemp, realpath, rm, writeFile } from "fs/promises";
import { tmpdir } from "os";
import { join } from "path";
import { afterEach, beforeEach, describe, expect, test } from "vitest";
import { findProjectRootForFile, getConfigForFile, typescriptLSPConfig } from "../../src/lsp/configs.js";

describe("LSP configs", () => {
    describe("getConfigForFile", () => {
        test("returns typescript config for supported file extensions", () => {
            const supportedPaths = [
                "/some/path/file.ts",
                "/some/path/Component.tsx",
                "/some/path/script.js",
                "/some/path/Component.jsx",
            ];

            for (const filePath of supportedPaths) {
                const config = getConfigForFile(filePath);
                expect(config).toBe(typescriptLSPConfig);
            }
        });

        test("returns undefined for unsupported file extensions", () => {
            const unsupportedPaths = [
                "/some/path/file.py",
                "/some/path/file.rb",
                "/some/path/file.xyz",
                "/some/path/file", // no extension
            ];

            for (const filePath of unsupportedPaths) {
                const config = getConfigForFile(filePath);
                expect(config).toBe(undefined);
            }
        });

        test("handles file paths without directories", () => {
            const testCases = [
                { filePath: "file.ts", expected: typescriptLSPConfig },
                { filePath: "file.py", expected: undefined },
            ];

            for (const { filePath, expected } of testCases) {
                const config = getConfigForFile(filePath);
                expect(config).toBe(expected);
            }
        });

        test("handles complex file paths", () => {
            const complexPaths = ["/very/deep/nested/path/with.multiple.dots.ts", "C:\\Windows\\path\\file.ts"];

            for (const filePath of complexPaths) {
                const config = getConfigForFile(filePath);
                expect(config).toBe(typescriptLSPConfig);
            }
        });
    });

    describe("findProjectRootForFile", () => {
        let tempDir: string;

        beforeEach(async () => {
            // Create a temporary directory for testing
            tempDir = await mkdtemp(join(tmpdir(), `lsp-configs-test-`));
            // On some OS's, the tempDir may be a symlink, resolve it for testing
            tempDir = await realpath(tempDir);
        });

        afterEach(async () => {
            // Clean up temporary directory
            if (tempDir) {
                await rm(tempDir, { recursive: true, force: true });
            }
        });

        test("returns undefined for unsupported file types", async () => {
            const filePath = join(tempDir, "file.py");
            await writeFile(filePath, 'print("hello")');

            const projectRoot = findProjectRootForFile(filePath, tempDir);
            expect(projectRoot).toBe(undefined);
        });

        test("returns undefined when no project root files found", async () => {
            const filePath = join(tempDir, "file.ts");
            await writeFile(filePath, "const x = 1;");

            const projectRoot = findProjectRootForFile(filePath, tempDir);
            expect(projectRoot).toBe(undefined);
        });

        test("finds project root with package.json", async () => {
            // Create package.json
            await writeFile(
                join(tempDir, "package.json"),
                JSON.stringify({
                    name: "test-project",
                    version: "1.0.0",
                }),
            );

            const filePath = join(tempDir, "file.ts");
            await writeFile(filePath, "const x = 1;");

            const projectRoot = findProjectRootForFile(filePath, tempDir);
            expect(projectRoot).toBe(tempDir);
        });

        test("finds project root with tsconfig.json", async () => {
            // Create tsconfig.json
            await writeFile(
                join(tempDir, "tsconfig.json"),
                JSON.stringify({
                    compilerOptions: {
                        target: "ES2020",
                    },
                }),
            );

            const filePath = join(tempDir, "file.ts");
            await writeFile(filePath, "const x = 1;");

            const projectRoot = findProjectRootForFile(filePath, tempDir);
            expect(projectRoot).toBe(tempDir);
        });

        test("finds project root from nested directory", async () => {
            // Create project root files
            await writeFile(
                join(tempDir, "package.json"),
                JSON.stringify({
                    name: "test-project",
                    version: "1.0.0",
                }),
            );

            // Create nested directory structure
            const nestedDir = join(tempDir, "src", "components", "ui");
            await mkdir(nestedDir, { recursive: true });

            const filePath = join(nestedDir, "Button.tsx");
            await writeFile(filePath, "export const Button = () => <button />;");

            const projectRoot = findProjectRootForFile(filePath, tempDir);
            expect(projectRoot).toBe(tempDir);
        });

        test("finds closest project root when multiple exist", async () => {
            // Create outer project root
            await writeFile(
                join(tempDir, "package.json"),
                JSON.stringify({
                    name: "outer-project",
                    version: "1.0.0",
                }),
            );

            // Create inner nested project
            const innerProjectDir = join(tempDir, "packages", "inner");
            await mkdir(innerProjectDir, { recursive: true });
            await writeFile(
                join(innerProjectDir, "package.json"),
                JSON.stringify({
                    name: "inner-project",
                    version: "1.0.0",
                }),
            );

            // Create file in inner project
            const filePath = join(innerProjectDir, "src", "index.ts");
            await mkdir(join(innerProjectDir, "src"), { recursive: true });
            await writeFile(filePath, 'export const hello = "world";');

            const projectRoot = findProjectRootForFile(filePath, tempDir);
            expect(projectRoot).toBe(innerProjectDir);
        });

        test("handles both package.json and tsconfig.json in same directory", async () => {
            // Create both project root files
            await writeFile(
                join(tempDir, "package.json"),
                JSON.stringify({
                    name: "test-project",
                    version: "1.0.0",
                }),
            );
            await writeFile(
                join(tempDir, "tsconfig.json"),
                JSON.stringify({
                    compilerOptions: {
                        target: "ES2020",
                    },
                }),
            );

            const filePath = join(tempDir, "file.ts");
            await writeFile(filePath, "const x = 1;");

            const projectRoot = findProjectRootForFile(filePath, tempDir);
            expect(projectRoot).toBe(tempDir);
        });

        test("stops at filesystem root when no stopAtDirectory provided", async () => {
            // Use a file path that's unlikely to have any project root files
            const filePath = "/tmp/very-unlikely-to-exist-" + Date.now() + "/file.ts";

            const projectRoot = findProjectRootForFile(filePath, "/");
            expect(projectRoot).toBe(undefined);
        });

        test("handles different typescript file extensions", async () => {
            // Create project root files
            await writeFile(
                join(tempDir, "tsconfig.json"),
                JSON.stringify({
                    compilerOptions: {
                        target: "ES2020",
                    },
                }),
            );

            const extensions = [".ts", ".tsx", ".js", ".jsx"];

            for (const ext of extensions) {
                const filePath = join(tempDir, `file${ext}`);
                await writeFile(filePath, "const x = 1;");

                const projectRoot = findProjectRootForFile(filePath, tempDir);
                expect(projectRoot).toBe(tempDir);
            }
        });

        test("respects stopAtDirectory parameter", async () => {
            // Create a nested directory structure with project root files at different levels
            await writeFile(
                join(tempDir, "package.json"),
                JSON.stringify({
                    name: "outer-project",
                    version: "1.0.0",
                }),
            );

            const subDir = join(tempDir, "subproject");
            await mkdir(subDir, { recursive: true });
            await writeFile(
                join(subDir, "package.json"),
                JSON.stringify({
                    name: "sub-project",
                    version: "1.0.0",
                }),
            );

            const deepDir = join(subDir, "src", "components");
            await mkdir(deepDir, { recursive: true });
            const filePath = join(deepDir, "Component.tsx");
            await writeFile(filePath, "export const Component = () => <div />;");

            // When we stop at subDir, it should find the sub-project root
            const projectRoot1 = findProjectRootForFile(filePath, subDir);
            expect(projectRoot1).toBe(subDir);

            // When we stop at tempDir, it should still find the closer sub-project root
            const projectRoot2 = findProjectRootForFile(filePath, tempDir);
            expect(projectRoot2).toBe(subDir);
        });

        test("returns undefined when stopAtDirectory prevents finding project root", async () => {
            // Create project root only in tempDir
            await writeFile(
                join(tempDir, "package.json"),
                JSON.stringify({
                    name: "test-project",
                    version: "1.0.0",
                }),
            );

            const deepDir = join(tempDir, "very", "deep", "nested", "path");
            await mkdir(deepDir, { recursive: true });
            const filePath = join(deepDir, "file.ts");
            await writeFile(filePath, "const x = 1;");

            // Stop searching at a directory below the project root
            const stopDir = join(tempDir, "very", "deep");
            const projectRoot = findProjectRootForFile(filePath, stopDir);
            expect(projectRoot).toBe(undefined);
        });

        test("searches through multiple directories up to stop directory", async () => {
            // Create project root files in tempDir
            await writeFile(
                join(tempDir, "package.json"),
                JSON.stringify({
                    name: "test-project",
                    version: "1.0.0",
                }),
            );

            // Create a deeply nested structure
            const level1 = join(tempDir, "level1");
            const level2 = join(level1, "level2");
            const level3 = join(level2, "level3");
            const level4 = join(level3, "level4");
            await mkdir(level4, { recursive: true });

            const filePath = join(level4, "file.ts");
            await writeFile(filePath, "const x = 1;");

            // Should find the project root by traversing up multiple directories
            const projectRoot = findProjectRootForFile(filePath, tempDir);
            expect(projectRoot).toBe(tempDir);
        });

        test("returns undefined when stopAtDirectory is empty string", async () => {
            // Create project root files
            await writeFile(
                join(tempDir, "package.json"),
                JSON.stringify({
                    name: "test-project",
                    version: "1.0.0",
                }),
            );

            const filePath = join(tempDir, "file.ts");
            await writeFile(filePath, "const x = 1;");

            // Should return undefined for empty string
            const result = findProjectRootForFile(filePath, "");
            expect(result).toBe(undefined);
        });

        test("returns undefined when stopAtDirectory is not a parent of file path", async () => {
            const otherTempDir = await mkdtemp(join(tmpdir(), "lsp-configs-test-"));

            try {
                // Create project root files
                await writeFile(
                    join(tempDir, "package.json"),
                    JSON.stringify({
                        name: "test-project",
                        version: "1.0.0",
                    }),
                );

                const filePath = join(tempDir, "file.ts");
                await writeFile(filePath, "const x = 1;");

                // Should return undefined when stopAtDirectory is not a parent
                const result = findProjectRootForFile(filePath, otherTempDir);
                expect(result).toBe(undefined);
            } finally {
                await rm(otherTempDir, { recursive: true, force: true });
            }
        });
    });
});
