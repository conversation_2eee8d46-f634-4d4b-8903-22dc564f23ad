/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { access, constants, cp, mkdir, mkdtemp, readFile, realpath, rm, writeFile } from "fs/promises";
import { tmpdir } from "os";
import { dirname, join } from "path";
import { afterAll, afterEach, beforeEach, describe, expect, test } from "vitest";
import { Diagnostic } from "vscode-languageserver-types";
import { LSPClient, LSPClientFactory } from "../../src/lsp/LSPClient.js";
import { typescriptLSPConfig } from "../../src/lsp/configs.js";
import { ConsoleLogger } from "../../src/runner/logger/console.js";

const logger = new ConsoleLogger();

describe("LSPClient", () => {
    // Clean up cached TypeScript project template when all tests are done
    afterAll(async () => {
        if (cachedTypeScriptProjectPath) {
            await rm(cachedTypeScriptProjectPath, {
                recursive: true,
                force: true,
            });
            cachedTypeScriptProjectPath = null;
        }
    });

    // @todo as other languages are supported, make these tests more config driven
    describe("typescript server", () => {
        describe("instance functionality", () => {
            let tempDir: string;
            beforeEach(async () => {
                // Create a temporary directory for testing
                tempDir = await mkdtemp(join(tmpdir(), `lsp-client-test-`));
                // On some OS's, the tempDir may be a symlink, resolve it for testing
                tempDir = await realpath(tempDir);
                // Create a minimal TypeScript project structure
                await createTypeScriptProject(tempDir);
            });

            afterEach(async () => {
                // Clean up temporary directory
                if (tempDir) {
                    await rm(tempDir, { recursive: true, force: true });
                }
            });

            describe("basics", () => {
                test("can start", async () => {
                    const client = await createAndStartClient(tempDir);

                    try {
                        // Test that client is properly initialized
                        expect(client.initialized).toBe(true);
                        expect(client.running).toBe(true);
                    } finally {
                        // Always ensure cleanup happens and verify clean exit
                        await shutdownAndVerifyCleanExit(client);
                    }
                }, 5000);

                test("can call start many times", async () => {
                    const client = await createAndStartClient(tempDir);

                    try {
                        for (let i = 0; i < 5; i++) {
                            await expect(client.start()).resolves.not.toThrow();
                        }

                        const startPromises: Promise<void>[] = [];
                        for (let i = 0; i < 5; i++) {
                            startPromises.push(client.start());
                        }
                        await expect(Promise.all(startPromises)).resolves.not.toThrow();

                        // Test that client is properly initialized
                        expect(client.initialized).toBe(true);
                        expect(client.running).toBe(true);
                    } finally {
                        // Always ensure cleanup happens and verify clean exit
                        await shutdownAndVerifyCleanExit(client);
                    }
                }, 5000);

                test("can open & close document", async () => {
                    const client = await createAndStartClient(tempDir);

                    try {
                        const { fileUri } = await writeToFile(tempDir, "test-file.ts", goodTypeScriptCode[0], client);
                        const result = await client.openOrUpdateDocument(fileUri, goodTypeScriptCode[0]);
                        expect(result).toBe("opened");

                        await expect(client.closeDocument(fileUri)).resolves.not.toThrow();
                    } finally {
                        await shutdownAndVerifyCleanExit(client);
                    }
                }, 5000);

                test('openOrUpdateDocument returns "opened" when opening new document', async () => {
                    const client = await createAndStartClient(tempDir);

                    try {
                        const { fileUri } = await writeToFile(tempDir, "new-file.ts", goodTypeScriptCode[0], client);

                        const result = await client.openOrUpdateDocument(fileUri, goodTypeScriptCode[0]);
                        expect(result).toBe("opened");
                    } finally {
                        await shutdownAndVerifyCleanExit(client);
                    }
                }, 5000);

                test('openOrUpdateDocument returns "updated" when document is already opened', async () => {
                    const client = await createAndStartClient(tempDir);

                    try {
                        const { fileUri } = await writeToFile(
                            tempDir,
                            "existing-file.ts",
                            goodTypeScriptCode[0],
                            client,
                        );

                        // First open should return "opened"
                        const firstOpen = await client.openOrUpdateDocument(fileUri, goodTypeScriptCode[0]);
                        expect(firstOpen).toBe("opened");

                        // Second call should return "updated"
                        const secondOpen = await client.openOrUpdateDocument(fileUri, goodTypeScriptCode[0]);
                        expect(secondOpen).toBe("updated");

                        // Third call should also return "updated"
                        const thirdOpen = await client.openOrUpdateDocument(fileUri, goodTypeScriptCode[1]);
                        expect(thirdOpen).toBe("updated");
                    } finally {
                        await shutdownAndVerifyCleanExit(client);
                    }
                }, 5000);

                test('openOrUpdateDocument returns "opened" after document is closed and reopened', async () => {
                    const client = await createAndStartClient(tempDir);

                    try {
                        const { fileUri } = await writeToFile(tempDir, "reopen-file.ts", goodTypeScriptCode[0], client);

                        // First open should return "opened"
                        const firstOpen = await client.openOrUpdateDocument(fileUri, goodTypeScriptCode[0]);
                        expect(firstOpen).toBe("opened");

                        // Close the document
                        await client.closeDocument(fileUri);

                        // Reopening should return "opened" again
                        const reopened = await client.openOrUpdateDocument(fileUri, goodTypeScriptCode[1]);
                        expect(reopened).toBe("opened");
                    } finally {
                        await shutdownAndVerifyCleanExit(client);
                    }
                }, 5000);
            });

            describe("waitForDiagnostics", () => {
                test("times out when no diagnostics are received", async () => {
                    const client = await createAndStartClient(tempDir);

                    try {
                        const diagnostics = await client.waitForDiagnostics(1000);
                        expect(diagnostics).toEqual([]);
                    } finally {
                        await shutdownAndVerifyCleanExit(client);
                    }
                }, 5000);

                test("returns all diagnostics emitted over the wait time - file just opened", async () => {
                    const client = await createAndStartClient(tempDir);

                    try {
                        const diagnosticsPromise = client.waitForDiagnostics(5000);

                        const { fileUri, clientOpenOrUpdateFile } = await writeToFile(
                            tempDir,
                            "test-quick-diagnostics.ts",
                            badTypeScriptCode[1],
                            client,
                        );
                        await clientOpenOrUpdateFile();

                        const diagnostics = await diagnosticsPromise;
                        verifyFilesMentionedInDiagnostics(diagnostics, [fileUri]);
                    } finally {
                        await shutdownAndVerifyCleanExit(client);
                    }
                }, 10000);

                test("returns all diagnostics emitted over the wait time - file updated", async () => {
                    const client = await createAndStartClient(tempDir);

                    try {
                        const diagnosticsPromise = client.waitForDiagnostics(5000);

                        const { fileUri, clientOpenOrUpdateFile } = await writeToFile(
                            tempDir,
                            "test-quick-diagnostics.ts",
                            goodTypeScriptCode[0],
                            client,
                        );
                        await clientOpenOrUpdateFile();

                        await new Promise((resolve) => setTimeout(resolve, 1000));

                        // Update the same file with different content
                        await writeFile(join(tempDir, "test-quick-diagnostics.ts"), badTypeScriptCode[0]);
                        await client.openOrUpdateDocument(fileUri, badTypeScriptCode[0]);

                        const diagnostics = await diagnosticsPromise;
                        verifyFilesMentionedInDiagnostics(diagnostics, [fileUri]);
                    } finally {
                        await shutdownAndVerifyCleanExit(client);
                    }
                }, 10000);

                test("returns diagnostics for multiple files", async () => {
                    const client = await createAndStartClient(tempDir);

                    try {
                        const diagnosticsPromise = client.waitForDiagnostics(8000);

                        const { fileUri: file1Uri, clientOpenOrUpdateFile: openOrUpdateFile1 } = await writeToFile(
                            tempDir,
                            "file1.ts",
                            goodTypeScriptCode[0],
                            client,
                        );
                        await openOrUpdateFile1();

                        const { fileUri: file2Uri, clientOpenOrUpdateFile: openOrUpdateFile2 } = await writeToFile(
                            tempDir,
                            "file2.ts",
                            badTypeScriptCode[0],
                            client,
                        );
                        await openOrUpdateFile2();

                        const { fileUri: file3Uri, clientOpenOrUpdateFile: openOrUpdateFile3 } = await writeToFile(
                            tempDir,
                            "file3.ts",
                            goodTypeScriptCode[1],
                            client,
                        );
                        await openOrUpdateFile3();

                        await new Promise((resolve) => setTimeout(resolve, 1500));

                        // Update file1 with bad code
                        await writeFile(join(tempDir, "file1.ts"), badTypeScriptCode[1]);
                        await client.openOrUpdateDocument(file1Uri, badTypeScriptCode[1]);

                        await new Promise((resolve) => setTimeout(resolve, 1000));

                        // Update file2 with good code
                        await writeFile(join(tempDir, "file2.ts"), goodTypeScriptCode[0]);
                        await client.openOrUpdateDocument(file2Uri, goodTypeScriptCode[0]);

                        await new Promise((resolve) => setTimeout(resolve, 1000));

                        // Update file3 with bad code
                        await writeFile(join(tempDir, "file3.ts"), badTypeScriptCode[0]);
                        await client.openOrUpdateDocument(file3Uri, badTypeScriptCode[0]);

                        const diagnostics = await diagnosticsPromise;
                        verifyFilesMentionedInDiagnostics(diagnostics, [file1Uri, file2Uri, file3Uri]);
                    } finally {
                        await shutdownAndVerifyCleanExit(client);
                    }
                }, 15000);
            });
        });

        describe("LSPClientFactory", () => {
            let tempDir: string;

            beforeEach(async () => {
                // Create a temporary directory for testing
                tempDir = await mkdtemp(join(tmpdir(), `lsp-static-test-`));
                tempDir = await realpath(tempDir);
            });

            afterEach(async () => {
                // Clean up temporary directory
                if (tempDir) {
                    await rm(tempDir, { recursive: true, force: true });
                }
            });

            describe("canCreateForFile", () => {
                test("returns true for supported typescript file extensions", () => {
                    const clientFactory = new LSPClientFactory(logger);

                    expect(clientFactory.canCreateForFile("/path/to/file.ts")).toBe(true);
                    expect(clientFactory.canCreateForFile("/path/to/file.tsx")).toBe(true);
                    expect(clientFactory.canCreateForFile("/path/to/file.js")).toBe(true);
                    expect(clientFactory.canCreateForFile("/path/to/file.jsx")).toBe(true);
                });

                test("returns false for unsupported file extensions", () => {
                    const clientFactory = new LSPClientFactory(logger);

                    expect(clientFactory.canCreateForFile("/path/to/file.txt")).toBe(false);
                    expect(clientFactory.canCreateForFile("/path/to/file.py")).toBe(false);
                    expect(clientFactory.canCreateForFile("/path/to/file.xyz")).toBe(false);
                    expect(clientFactory.canCreateForFile("/path/to/file")).toBe(false);
                });
            });

            describe("getOrCreateForFile", () => {
                test("returns undefined for unsupported file extension", async () => {
                    const clientFactory = new LSPClientFactory(logger);
                    const client = await clientFactory.getOrCreateForFile("/some/path/file.xyz", "/");
                    expect(client).toBe(undefined);
                });

                test("returns undefined when no project root is found", async () => {
                    // Create a temporary file without any project root files
                    const filePath = join(tempDir, "test.ts");
                    await writeFile(filePath, "const x = 1;");

                    const clientFactory = new LSPClientFactory(logger);
                    const client = await clientFactory.getOrCreateForFile(filePath, tempDir);
                    expect(client).toBe(undefined);
                });

                test("creates new client for typescript file in project", async () => {
                    // Create a minimal TypeScript project structure
                    await createTypeScriptProject(tempDir);

                    const filePath = join(tempDir, "test.ts");
                    await writeFile(filePath, goodTypeScriptCode[0]);

                    const clientFactory = new LSPClientFactory(logger);
                    const client = await clientFactory.getOrCreateForFile(filePath, tempDir);

                    try {
                        expect(client).toBeDefined();
                        expect(client!.initialized).toBe(true);
                        expect(client!.running).toBe(true);
                    } finally {
                        if (client) {
                            await client.shutdown();
                        }
                    }
                }, 3000);

                test("returns existing client for same project", async () => {
                    // Create a minimal TypeScript project structure
                    await createTypeScriptProject(tempDir);

                    const filePath1 = join(tempDir, "test1.ts");
                    const filePath2 = join(tempDir, "test2.ts");
                    await writeFile(filePath1, goodTypeScriptCode[0]);
                    await writeFile(filePath2, goodTypeScriptCode[1]);

                    const clientFactory = new LSPClientFactory(logger);
                    const client1 = await clientFactory.getOrCreateForFile(filePath1, tempDir);
                    const client2 = await clientFactory.getOrCreateForFile(filePath2, tempDir);

                    try {
                        expect(client1).toBeDefined();
                        expect(client2).toBeDefined();
                        expect(client1).toBe(client2); // Should be the same instance
                        expect(clientFactory.numCachedClients).toBe(1);
                    } finally {
                        if (client1) {
                            await client1.shutdown();
                        }
                    }
                }, 3000);

                test("creates separate clients for different projects", async () => {
                    // Create two separate temporary projects
                    const tempDir1 = await mkdtemp(join(tmpdir(), `lsp-project1-`));
                    const tempDir2 = await mkdtemp(join(tmpdir(), `lsp-project2-`));
                    const resolvedTempDir1 = await realpath(tempDir1);
                    const resolvedTempDir2 = await realpath(tempDir2);

                    await createTypeScriptProject(resolvedTempDir1);
                    await createTypeScriptProject(resolvedTempDir2);

                    const filePath1 = join(resolvedTempDir1, "test1.ts");
                    const filePath2 = join(resolvedTempDir2, "test2.ts");
                    await writeFile(filePath1, goodTypeScriptCode[0]);
                    await writeFile(filePath2, goodTypeScriptCode[1]);

                    const clientFactory = new LSPClientFactory(logger);
                    const client1 = await clientFactory.getOrCreateForFile(filePath1, resolvedTempDir1);
                    const client2 = await clientFactory.getOrCreateForFile(filePath2, resolvedTempDir2);

                    try {
                        expect(client1).toBeDefined();
                        expect(client2).toBeDefined();
                        expect(client1).not.toBe(client2); // Should be different instances
                        expect(clientFactory.numCachedClients).toBe(2);
                    } finally {
                        if (client1) {
                            await client1.shutdown();
                        }
                        if (client2) {
                            await client2.shutdown();
                        }
                        // Clean up the extra temp directories
                        await rm(tempDir1, { recursive: true, force: true });
                        await rm(tempDir2, { recursive: true, force: true });
                    }
                }, 5000);

                test("finds project root in parent directory", async () => {
                    // Create a minimal TypeScript project structure
                    await createTypeScriptProject(tempDir);

                    // Create nested directories
                    const nestedDir = join(tempDir, "src", "components");
                    await mkdir(nestedDir, { recursive: true });

                    const filePath = join(nestedDir, "Component.tsx");
                    await writeFile(filePath, goodTypeScriptCode[0]);

                    const clientFactory = new LSPClientFactory(logger);
                    const client = await clientFactory.getOrCreateForFile(filePath, tempDir);

                    try {
                        expect(client).toBeDefined();
                        expect(client!.initialized).toBe(true);
                        expect(client!.running).toBe(true);
                    } finally {
                        if (client) {
                            await client.shutdown();
                        }
                    }
                }, 3000);

                test("supports different typescript file extensions", async () => {
                    await createTypeScriptProject(tempDir);

                    const extensions = [".ts", ".tsx", ".js", ".jsx"];
                    const clients: (LSPClient | undefined)[] = [];

                    const clientFactory = new LSPClientFactory(logger);
                    try {
                        for (const ext of extensions) {
                            const filePath = join(tempDir, `test${ext}`);
                            await writeFile(filePath, goodTypeScriptCode[0]);

                            const client = await clientFactory.getOrCreateForFile(filePath, tempDir);
                            clients.push(client);
                            expect(client).toBeDefined();
                        }

                        // All should return the same client instance since they're in the same project
                        const firstClient = clients[0];
                        for (const client of clients) {
                            expect(client).toBe(firstClient);
                        }

                        expect(clientFactory.numCachedClients).toBe(1);
                    } finally {
                        for (const client of clients) {
                            if (client) {
                                await client.shutdown();
                                break; // Only need to shutdown once since they're all the same instance
                            }
                        }
                    }
                }, 3000);

                test("created clients are removed from factory cache on dispose", async () => {
                    // Create a minimal TypeScript project structure
                    await createTypeScriptProject(tempDir);

                    const filePath = join(tempDir, "test.ts");
                    await writeFile(filePath, goodTypeScriptCode[0]);

                    const clientFactory = new LSPClientFactory(logger);
                    const client = await clientFactory.getOrCreateForFile(filePath, tempDir);

                    expect(client).toBeDefined();
                    expect(clientFactory.numCachedClients).toBe(1);

                    await client?.shutdown();

                    expect(clientFactory.numCachedClients).toBe(0);
                });
            });
        });
    });
});

const goodTypeScriptCode = [
    `export function goodbye(name: string): string {
    return \`Goodbye, \${name}!\`;
}`,
    `export function hello(name: string): string {
    return \`Hello, \${name}!\`;
}`,
];

const badTypeScriptCode = [
    `function greet(name: string): string {
    const message: number = "Hello, " + name; // Type error: string assigned to number
    console.log(undeclaredVariable); // Error: undeclared variable
    return message;
}

// Another error: calling function with wrong type
greet(123);
`,
    `function quickTest(): void {
    const x: string = 123; // Type error
    console.log(undefinedVar); // Undefined variable
}`,
];

async function shutdownAndVerifyCleanExit(client: LSPClient): Promise<void> {
    const exitCode = await client.shutdown();
    expect(exitCode).toBe(0);
    expect(client.initialized).toBe(false);
    expect(client.running).toBe(false);
}

async function createAndStartClient(dir: string): Promise<LSPClient> {
    const client = new LSPClient(dir, typescriptLSPConfig, logger);
    await client.start();
    return client;
}

async function writeToFile(
    dir: string,
    fileName: string,
    content: string,
    client: LSPClient,
): Promise<{
    filePath: string;
    fileUri: string;
    clientOpenOrUpdateFile: () => Promise<"opened" | "updated">;
}> {
    const filePath = join(dir, fileName);
    const fileUri = `file://${filePath}`;
    await writeFile(filePath, content);

    return {
        filePath: filePath,
        fileUri: fileUri,
        clientOpenOrUpdateFile: async () => {
            return await client.openOrUpdateDocument(fileUri, content);
        },
    };
}

function verifyFilesMentionedInDiagnostics(
    allDiagnostics: { uri: string; diagnostics: Diagnostic[] }[],
    expectedFileUris: string[],
): void {
    const diagnosticUris = new Set(allDiagnostics.map((d) => d.uri));
    for (const expectedUri of expectedFileUris) {
        expect(diagnosticUris.has(expectedUri)).toBe(true);
    }
}

/**
 * Find the current project's root directory by walking up from the current file
 */
async function findProjectRoot(): Promise<string> {
    let currentDir = dirname(import.meta.url.replace("file://", ""));

    // Walk up the directory tree until we find package.json
    while (currentDir !== "/" && currentDir !== ".") {
        try {
            // Check if package.json exists at this level
            const packageJsonPath = join(currentDir, "package.json");
            await access(packageJsonPath, constants.F_OK);

            const packageJsonContent = await readFile(packageJsonPath, "utf-8");
            const packageJson = JSON.parse(packageJsonContent);

            // Verify this looks like our project by checking for typescript in dependencies
            const allDeps = {
                ...packageJson.dependencies,
                ...packageJson.devDependencies,
            };
            if (allDeps.typescript) {
                return currentDir;
            }
        } catch {
            // Continue searching up
        }

        // Move up one directory
        currentDir = dirname(currentDir);
    }

    throw new Error("Could not find project root with TypeScript dependency");
}

/**
 * Cache for TypeScript project template
 */
let cachedTypeScriptProjectPath: string | null = null;

/**
 * Create or get cached TypeScript project template
 */
async function getOrCreateCachedTypeScriptProject(): Promise<string> {
    if (cachedTypeScriptProjectPath) {
        return cachedTypeScriptProjectPath;
    }

    // Create a new cached project
    cachedTypeScriptProjectPath = await mkdtemp(join(tmpdir(), "lsp-cached-ts-project-"));

    const currentProjectRoot = await findProjectRoot();
    const currentPackageJsonPath = join(currentProjectRoot, "package.json");

    const currentPackageJson = JSON.parse(await readFile(currentPackageJsonPath, "utf-8"));
    const allDeps = {
        ...currentPackageJson.dependencies,
        ...currentPackageJson.devDependencies,
    };

    const typescriptVersion = allDeps.typescript || "^5.0.0";
    const typesNodeVersion = allDeps["@types/node"] || "^20.0.0";

    // Write the package.json with the same typescript version
    await writeFile(
        join(cachedTypeScriptProjectPath, "package.json"),
        JSON.stringify(
            {
                name: "test-project",
                version: "1.0.0",
                devDependencies: {
                    typescript: typescriptVersion,
                    "@types/node": typesNodeVersion,
                },
            },
            null,
            2,
        ),
    );

    // Create a more comprehensive tsconfig.json
    await writeFile(
        join(cachedTypeScriptProjectPath, "tsconfig.json"),
        JSON.stringify(
            {
                compilerOptions: {
                    target: "ES2020",
                    module: "commonjs",
                    strict: true,
                    esModuleInterop: true,
                    skipLibCheck: true,
                    forceConsistentCasingInFileNames: true,
                    moduleResolution: "node",
                    allowSyntheticDefaultImports: true,
                },
                include: ["**/*.ts"],
                exclude: ["node_modules"],
            },
            null,
            2,
        ),
    );

    // Copy this project's typescript folder to the cached project's node_modules folder
    const sourceTypeScriptPath = join(currentProjectRoot, "node_modules", "typescript");
    const targetNodeModulesPath = join(cachedTypeScriptProjectPath, "node_modules");
    const targetTypeScriptPath = join(targetNodeModulesPath, "typescript");

    // Create node_modules directory
    await mkdir(targetNodeModulesPath, { recursive: true });

    // Copy typescript folder recursively
    await copyDirectory(sourceTypeScriptPath, targetTypeScriptPath);

    // Also copy @types/node if it exists
    try {
        const sourceTypesNodePath = join(currentProjectRoot, "node_modules", "@types", "node");
        await access(sourceTypesNodePath, constants.F_OK);

        const targetTypesPath = join(targetNodeModulesPath, "@types");
        const targetTypesNodePath = join(targetTypesPath, "node");

        await mkdir(targetTypesPath, { recursive: true });

        await copyDirectory(sourceTypesNodePath, targetTypesNodePath);
    } catch {
        // @types/node not found, that's okay
    }

    return cachedTypeScriptProjectPath;
}

async function createTypeScriptProject(rootDir: string): Promise<void> {
    // Get or create the cached template project
    const templatePath = await getOrCreateCachedTypeScriptProject();

    // Copy the template project to the target directory using Node.js fs functions for cross-platform compatibility
    await copyDirectory(templatePath, rootDir);
}

async function copyDirectory(src: string, dest: string): Promise<void> {
    await cp(src, dest, { recursive: true, force: true });
}
