/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { mkdir, rm, writeFile } from "fs/promises";
import { tmpdir } from "os";
import { join } from "path";
import { afterEach, beforeEach, expect, test } from "vitest";
import { getBlockedRequests } from "../src/firewall";

const TEST_DIR_FILEBASE = "firewall-test";
const TEST_DIR = join(tmpdir(), TEST_DIR_FILEBASE);

async function retry(message: string, fn: () => Promise<void>, maxTries: number = 100, delay: number = 100) {
    let failedAttempts = 0;
    while (true) {
        try {
            await fn();
            return;
        } catch (error: unknown) {
            failedAttempts++;
            if (failedAttempts >= maxTries) {
                throw new Error(`Failed to ${message} after ${maxTries} attempts\n${error}`);
            }
            await new Promise((resolve) => setTimeout(resolve, delay));
        }
    }
}

let originalEnvVar: string | undefined;

describe("firewall", () => {
    beforeEach(async () => {
        // Create test directory
        await mkdir(TEST_DIR, { recursive: true });

        // Store original environment variable
        originalEnvVar = process.env["COPILOT_AGENT_FIREWALL_LOG_FILE"];
    });

    afterEach(async () => {
        // Restore original environment variable
        if (originalEnvVar !== undefined) {
            process.env["COPILOT_AGENT_FIREWALL_LOG_FILE"] = originalEnvVar;
        } else {
            delete process.env["COPILOT_AGENT_FIREWALL_LOG_FILE"];
        }

        // Clean up test directory
        await retry(`delete ${TEST_DIR} directory`, async () => {
            await rm(TEST_DIR, { recursive: true });
        });
    });

    test("getBlockedRequests returns empty array when no firewall log file env var is set", async () => {
        delete process.env["COPILOT_AGENT_FIREWALL_LOG_FILE"];

        const result = await getBlockedRequests();

        expect(result).toEqual([]);
    });

    test("getBlockedRequests returns empty array when firewall log file does not exist", async () => {
        const nonExistentFile = join(TEST_DIR, "nonexistent.log");
        process.env["COPILOT_AGENT_FIREWALL_LOG_FILE"] = nonExistentFile;

        await expect(getBlockedRequests()).rejects.toThrow();
    });

    test("getBlockedRequests parses valid blocked requests", async () => {
        const logFile = join(TEST_DIR, "firewall.log");
        const logContent = [
            '{"blocked":true,"because":"DomainBlocked","blockedAt":"2024-01-01T10:00:00Z","cmd":"curl","domains":"example.com","hasBeenRedirected":false,"ip":"*******","originalIp":"*******","port":"443","ruleSourceComment":"test rule","url":"https://example.com"}',
            '{"blocked":true,"because":"IPBlocked","blockedAt":"2024-01-01T10:01:00Z","cmd":"wget","domains":"malicious.com","hasBeenRedirected":false,"ip":"*******","originalIp":"*******","port":"80","ruleSourceComment":"malicious site","url":"http://malicious.com"}',
        ].join("\n");

        await writeFile(logFile, logContent);
        process.env["COPILOT_AGENT_FIREWALL_LOG_FILE"] = logFile;

        const result = await getBlockedRequests();

        expect(result).toHaveLength(2);
        expect(result[0]).toEqual({
            because: "DomainBlocked",
            blockedAt: "2024-01-01T10:00:00Z",
            cmd: "curl",
            domains: "example.com",
            hasBeenRedirected: false,
            ip: "*******",
            originalIp: "*******",
            port: "443",
            ruleSourceComment: "test rule",
            url: "https://example.com",
        });
        expect(result[1]).toEqual({
            because: "IPBlocked",
            blockedAt: "2024-01-01T10:01:00Z",
            cmd: "wget",
            domains: "malicious.com",
            hasBeenRedirected: false,
            ip: "*******",
            originalIp: "*******",
            port: "80",
            ruleSourceComment: "malicious site",
            url: "http://malicious.com",
        });
    });

    test("getBlockedRequests filters out non-blocked requests", async () => {
        const logFile = join(TEST_DIR, "firewall.log");
        const logContent = [
            '{"blocked":true,"because":"DomainBlocked","blockedAt":"2024-01-01T10:00:00Z","cmd":"curl","domains":"example.com","hasBeenRedirected":false,"ip":"*******","originalIp":"*******","port":"443","ruleSourceComment":"test rule","url":"https://example.com"}',
            '{"blocked":false,"because":"Allowed","blockedAt":"2024-01-01T10:01:00Z","cmd":"curl","domains":"allowed.com","hasBeenRedirected":false,"ip":"**********","originalIp":"**********","port":"443","ruleSourceComment":"allowed rule","url":"https://allowed.com"}',
        ].join("\n");

        await writeFile(logFile, logContent);
        process.env["COPILOT_AGENT_FIREWALL_LOG_FILE"] = logFile;

        const result = await getBlockedRequests();

        expect(result).toHaveLength(1);
        expect(result[0].domains).toBe("example.com");
    });

    test("getBlockedRequests filters out IPv6AlwaysBlocked requests", async () => {
        const logFile = join(TEST_DIR, "firewall.log");
        const logContent = [
            '{"blocked":true,"because":"DomainBlocked","blockedAt":"2024-01-01T10:00:00Z","cmd":"curl","domains":"example.com","hasBeenRedirected":false,"ip":"*******","originalIp":"*******","port":"443","ruleSourceComment":"test rule","url":"https://example.com"}',
            '{"blocked":true,"because":"IPv6AlwaysBlocked","blockedAt":"2024-01-01T10:01:00Z","cmd":"curl","domains":"ipv6site.com","hasBeenRedirected":false,"ip":"::1","originalIp":"::1","port":"443","ruleSourceComment":"ipv6 rule","url":"https://ipv6site.com"}',
        ].join("\n");

        await writeFile(logFile, logContent);
        process.env["COPILOT_AGENT_FIREWALL_LOG_FILE"] = logFile;

        const result = await getBlockedRequests();

        expect(result).toHaveLength(1);
        expect(result[0].domains).toBe("example.com");
    });

    test("getBlockedRequests filters out redirected requests", async () => {
        const logFile = join(TEST_DIR, "firewall.log");
        const logContent = [
            '{"blocked":true,"because":"DomainBlocked","blockedAt":"2024-01-01T10:00:00Z","cmd":"curl","domains":"example.com","hasBeenRedirected":false,"ip":"*******","originalIp":"*******","port":"443","ruleSourceComment":"test rule","url":"https://example.com"}',
            '{"blocked":true,"because":"DomainBlocked","blockedAt":"2024-01-01T10:01:00Z","cmd":"curl","domains":"redirected.com","hasBeenRedirected":true,"ip":"*******","originalIp":"*******","port":"443","ruleSourceComment":"redirect rule","url":"https://redirected.com"}',
        ].join("\n");

        await writeFile(logFile, logContent);
        process.env["COPILOT_AGENT_FIREWALL_LOG_FILE"] = logFile;

        const result = await getBlockedRequests();

        expect(result).toHaveLength(1);
        expect(result[0].domains).toBe("example.com");
    });

    test("getBlockedRequests cleans up root process commands", async () => {
        const logFile = join(TEST_DIR, "firewall.log");
        const logContent = [
            '{"blocked":true,"because":"DomainBlocked","blockedAt":"2024-01-01T10:00:00Z","cmd":"root process (pid 0)","domains":"example.com","hasBeenRedirected":false,"ip":"*******","originalIp":"*******","port":"443","ruleSourceComment":"test rule","url":"https://example.com"}',
            '{"blocked":true,"because":"DomainBlocked","blockedAt":"2024-01-01T10:01:00Z","cmd":"process with pid 1234 no longer exists","domains":"test.com","hasBeenRedirected":false,"ip":"*******","originalIp":"*******","port":"443","ruleSourceComment":"test rule","url":"https://test.com"}',
        ].join("\n");

        await writeFile(logFile, logContent);
        process.env["COPILOT_AGENT_FIREWALL_LOG_FILE"] = logFile;

        const result = await getBlockedRequests();

        expect(result).toHaveLength(2);
        expect(result[0].cmd).toBe("");
        expect(result[1].cmd).toBe("");
    });

    test("getBlockedRequests ignores invalid JSON lines", async () => {
        const logFile = join(TEST_DIR, "firewall.log");
        const logContent = [
            '{"blocked":true,"because":"DomainBlocked","blockedAt":"2024-01-01T10:00:00Z","cmd":"curl","domains":"example.com","hasBeenRedirected":false,"ip":"*******","originalIp":"*******","port":"443","ruleSourceComment":"test rule","url":"https://example.com"}',
            "invalid json line",
            '{"blocked":true,"because":"IPBlocked","blockedAt":"2024-01-01T10:01:00Z","cmd":"wget","domains":"test.com","hasBeenRedirected":false,"ip":"*******","originalIp":"*******","port":"80","ruleSourceComment":"test rule","url":"http://test.com"}',
        ].join("\n");

        await writeFile(logFile, logContent);
        process.env["COPILOT_AGENT_FIREWALL_LOG_FILE"] = logFile;

        const result = await getBlockedRequests();

        expect(result).toHaveLength(2);
        expect(result[0].domains).toBe("example.com");
        expect(result[1].domains).toBe("test.com");
    });

    test("getBlockedRequests ignores empty lines", async () => {
        const logFile = join(TEST_DIR, "firewall.log");
        const logContent = [
            '{"blocked":true,"because":"DomainBlocked","blockedAt":"2024-01-01T10:00:00Z","cmd":"curl","domains":"example.com","hasBeenRedirected":false,"ip":"*******","originalIp":"*******","port":"443","ruleSourceComment":"test rule","url":"https://example.com"}',
            "",
            "   ",
            '{"blocked":true,"because":"IPBlocked","blockedAt":"2024-01-01T10:01:00Z","cmd":"wget","domains":"test.com","hasBeenRedirected":false,"ip":"*******","originalIp":"*******","port":"80","ruleSourceComment":"test rule","url":"http://test.com"}',
        ].join("\n");

        await writeFile(logFile, logContent);
        process.env["COPILOT_AGENT_FIREWALL_LOG_FILE"] = logFile;

        const result = await getBlockedRequests();

        expect(result).toHaveLength(2);
        expect(result[0].domains).toBe("example.com");
        expect(result[1].domains).toBe("test.com");
    });

    test("getBlockedRequests deduplicates identical requests", async () => {
        const logFile = join(TEST_DIR, "firewall.log");
        const duplicateRequest =
            '{"blocked":true,"because":"DomainBlocked","blockedAt":"2024-01-01T10:00:00Z","cmd":"curl","domains":"example.com","hasBeenRedirected":false,"ip":"*******","originalIp":"*******","port":"443","ruleSourceComment":"test rule","url":"https://example.com"}';
        const logContent = [
            duplicateRequest,
            duplicateRequest,
            '{"blocked":true,"because":"IPBlocked","blockedAt":"2024-01-01T10:01:00Z","cmd":"wget","domains":"test.com","hasBeenRedirected":false,"ip":"*******","originalIp":"*******","port":"80","ruleSourceComment":"test rule","url":"http://test.com"}',
        ].join("\n");

        await writeFile(logFile, logContent);
        process.env["COPILOT_AGENT_FIREWALL_LOG_FILE"] = logFile;

        const result = await getBlockedRequests();

        expect(result).toHaveLength(2);
        expect(result[0].domains).toBe("example.com");
        expect(result[1].domains).toBe("test.com");
    });

    test("getBlockedRequests handles empty log file", async () => {
        const logFile = join(TEST_DIR, "firewall.log");
        await writeFile(logFile, "");
        process.env["COPILOT_AGENT_FIREWALL_LOG_FILE"] = logFile;

        const result = await getBlockedRequests();

        expect(result).toEqual([]);
    });

    test("getBlockedRequests handles file with only whitespace", async () => {
        const logFile = join(TEST_DIR, "firewall.log");
        await writeFile(logFile, "   \n  \n\t\n   ");
        process.env["COPILOT_AGENT_FIREWALL_LOG_FILE"] = logFile;

        const result = await getBlockedRequests();

        expect(result).toEqual([]);
    });
});
