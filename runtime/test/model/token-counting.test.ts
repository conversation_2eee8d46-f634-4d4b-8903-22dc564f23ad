/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import { ChatCompletionContentPart, ChatCompletionMessageParam } from "openai/resources.js";
import {
    countImagePartTokens,
    countTokensInChatCompletionMessage,
    countTokensInChatCompletionMessages,
} from "../../src/model/tokenCounting.js";

describe("token counting", () => {
    describe("for images", () => {
        test("gpt-4o", async () => {
            const message: ChatCompletionMessageParam = {
                role: "user",
                content: [
                    { type: "text", text: "Hello" },
                    {
                        type: "image_url",
                        image_url: { url: "https://example.com/image.png" },
                    },
                ],
            };
            const modelName = "gpt-4o";
            const tokenCount = countTokensInChatCompletionMessage(message, modelName);
            expect(tokenCount).toBeGreaterThan(0);
            expect(tokenCount).toBeLessThan(100);
        });

        test("claude-sonnet-4", async () => {
            const message: ChatCompletionMessageParam = {
                role: "user",
                content: [
                    { type: "text", text: "Hello" },
                    {
                        type: "image_url",
                        image_url: { url: "https://example.com/image.png" },
                    },
                ],
            };
            const modelName = "claude-sonnet-4";
            const tokenCount = countTokensInChatCompletionMessage(message, modelName);
            expect(tokenCount).toBeGreaterThan(750);
            expect(tokenCount).toBeLessThan(1000);
        });
    });
    describe("OpenAI Examples", () => {
        // From https://github.com/openai/openai-cookbook/blob/main/examples/How_to_count_tokens_with_tiktoken.ipynb
        const expectedTokenCounts: { [modelName: string]: number } = {
            "gpt-3.5-turbo": 129,
            "gpt-4": 129,
            "gpt-4o": 124,
            "gpt-4o-mini": 124,
            // should count the same as gpt-4o since that's our default model for token counting
            "claude-sonnet-4": 124,
        };

        const messages: ChatCompletionMessageParam[] = [
            {
                role: "system",
                content:
                    "You are a helpful, pattern-following assistant that translates corporate jargon into plain English.",
            },
            {
                role: "system",
                name: "example_user",
                content: "New synergies will help drive top-line growth.",
            },
            {
                role: "system",
                name: "example_assistant",
                content: "Things working well together will increase revenue.",
            },
            {
                role: "system",
                name: "example_user",
                content:
                    "Let's circle back when we have more bandwidth to touch base on opportunities for increased leverage.",
            },
            {
                role: "system",
                name: "example_assistant",
                content: "Let's talk later when we're less busy about how to do better.",
            },
            {
                role: "user",
                content: "This late pivot means we don't have time to boil the ocean for the client deliverable.",
            },
        ];
        for (const [modelName, expectedTokenCount] of Object.entries(expectedTokenCounts)) {
            describe(`${modelName}`, () => {
                test(`basic test`, async () => {
                    const tokenCount = countTokensInChatCompletionMessages(messages, modelName);
                    expect(tokenCount).toBe(expectedTokenCount);
                });
                test(`plus an image`, async () => {
                    const imageMessagePart: ChatCompletionContentPart = {
                        type: "image_url",
                        image_url: { url: "https://example.com/image.png" },
                    };
                    const tokensFromImagePart = countImagePartTokens(imageMessagePart, modelName);

                    const tokenCount = countTokensInChatCompletionMessages(
                        [...messages, { role: "user", content: [imageMessagePart] }],
                        modelName,
                    );
                    // expected + tokens for the image part + 1 for the role + 3 for the message
                    expect(tokenCount).toBe(expectedTokenCount + tokensFromImagePart + 4);
                });
            });
        }
    });
});
