/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { APIError } from "openai";
import type { ChatCompletion, ChatCompletionMessageParam } from "openai/resources/chat/completions";
import { afterEach, beforeEach, describe, expect, test, vi } from "vitest";
import { ChatCompletionClient } from "../../src/model/capi/chat-completion-client";
import { CAPIError } from "../../src/model/capi/copilot-client";
import { BasicTruncator } from "../../src/model/capi/truncateContextWindow";
import type { Model, OpenAIClientProvider } from "../../src/model/capi/types";
import { GetCompletionWithToolsOptions } from "../../src/model/client";
import { AssistantMessageEvent, Event, ToolMessageEvent } from "../../src/model/event";
import { ConsoleLogger } from "../../src/runner/logger/console";
import { getOrInitSettings } from "../../src/settings/factory";
import { RuntimeSettings } from "../../src/settings/types";
import type { Tool, ToolResultExpanded } from "../../src/tools";
import { inflatePreRecordedCompletion } from "../preRecordedCompletions";

const truncator = new BasicTruncator();
const truncatorPreRequestSpy = vi.spyOn(truncator, "preRequest");

// Mock OpenAI client
const mockCreate = vi.fn();
const mockListModels = vi.fn();

const mockOpenAIClient = {
    chat: {
        completions: {
            create: mockCreate,
        },
    },
    listModels: mockListModels,
    setInitiatorHeader: vi.fn(),
};

// Mock provider that returns our mocked OpenAI client
const mockProvider: OpenAIClientProvider = {
    createClient: vi.fn().mockResolvedValue(mockOpenAIClient),
};

function createMockResponseHeaders(): Headers {
    const headers = new Headers();
    headers.set("x-github-request-id", "fake");
    return headers;
}
const mockResponseHeaders = createMockResponseHeaders();

function createMockCreateReturnValue(chatCompletionResponse: ChatCompletion, reqId: string = "req-test") {
    return {
        withResponse: vi.fn().mockResolvedValue({
            response: { headers: mockResponseHeaders },
            data: chatCompletionResponse,
            request_id: reqId,
        }),
    };
}

function createMockAPIError(status: number, message: string, requestId?: string): APIError {
    const error = new APIError(status, { message }, message, {
        "content-type": "application/json",
        "x-github-request-id": `github-${requestId}`,
    }) as APIError;
    if (requestId) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (error as any).request_id = requestId;
    }
    return error;
}

function createMockToolCallResponse(
    toolCallId: string,
    toolName: string,
    toolArgs: string,
    overrides: Partial<ChatCompletion> = {},
): ChatCompletion {
    return inflatePreRecordedCompletion({
        id: "chatcmpl-tool-call",
        choices: [
            {
                index: 0,
                message: {
                    role: "assistant",
                    content: null,
                    refusal: null,
                    tool_calls: [
                        {
                            id: toolCallId,
                            type: "function",
                            function: {
                                name: toolName,
                                arguments: toolArgs,
                            },
                        },
                    ],
                },
                finish_reason: "tool_calls",
                logprobs: null,
            },
        ],
        usage: { prompt_tokens: 15, completion_tokens: 25, total_tokens: 40 },
        ...overrides,
    });
}

async function getCompletionWithTools(
    client: ChatCompletionClient,
    systemMessage: string,
    messages: ChatCompletionMessageParam[],
    tools: Tool[] = [],
    options?: GetCompletionWithToolsOptions,
): Promise<{
    events: Event[];
    finalTurnCount: number;
    error?: { obj: unknown; str: string };
}> {
    let finalTurnCount = 0;
    const events: Event[] = [];
    const generator = client.getCompletionWithTools(systemMessage, messages, tools, options);
    let error: { obj: unknown; str: string } | undefined = undefined;

    const eventsPromise = (async () => {
        try {
            for await (const event of generator) {
                events.push(event);
                if (event.kind === "message" || event.kind === "response") {
                    finalTurnCount = Math.max(finalTurnCount, event.turn ?? 0);
                }
                if (event.kind === "model_call_failure") {
                    // - Wait a bit before advancing timers so the client has time to get to its await timeout.
                    // - Don't await this promise, as the client code might not continue until this iteration of the loop is done.

                    // eslint-disable-next-line @typescript-eslint/no-floating-promises
                    new Promise((resolve) => setTimeout(resolve, 1000)).then(async () => {
                        // Advance timers to simulate time passing for retries
                        await vi.advanceTimersByTimeAsync(7200);
                    });
                }

                // Simulate time passing between emits
                await vi.advanceTimersByTimeAsync(1000);
            }
        } catch (err) {
            error = {
                obj: err,
                str: `${err}`,
            };
        }
    })();

    await eventsPromise;

    return { events: events, finalTurnCount: finalTurnCount, error: error };
}

describe("ChatCompletionClient", () => {
    let client: ChatCompletionClient;
    let logger: ConsoleLogger;
    let settings: RuntimeSettings;

    beforeEach(async () => {
        // Reset all mocks
        vi.clearAllMocks();

        // Explicitly reset mockCreate implementation
        mockCreate.mockReset();
        mockListModels.mockReset();

        // Mock timers to speed up retry delays
        vi.useFakeTimers();

        // Setup default mocks
        logger = new ConsoleLogger();
        settings = await getOrInitSettings();

        // Default model info
        const defaultModel: Model = {
            id: "test-model",
            name: "test-model",
            capabilities: {
                supports: { vision: false },
                limits: {
                    max_prompt_tokens: 4000,
                    max_context_window_tokens: 8000,
                },
            },
        };

        mockListModels.mockResolvedValue([defaultModel]);

        client = new ChatCompletionClient(mockProvider, settings, logger, {
            model: "test-model",
            retryPolicy: {
                maxRetries: 3,
            },
        });
    });

    afterEach(() => {
        vi.useRealTimers();
        vi.clearAllMocks();
    });

    describe("successful responses", () => {
        test("should handle simple text response", async () => {
            // Arrange
            const mockResponse = inflatePreRecordedCompletion({
                type: "stop-message",
                content: "Hello, this is a test response!",
            });

            mockCreate.mockReturnValue(createMockCreateReturnValue(mockResponse, "req-test"));

            // Act
            const result = await getCompletionWithTools(
                client,
                "You are a helpful assistant.",
                [{ role: "user", content: "Hello!" }],
                [],
                {
                    processors: { preRequest: [truncator] },
                },
            );
            const events = result.events;

            // Assert
            expect(events).toHaveLength(7);
            expect(events[0].kind).toBe("turn_started");
            expect(events[1].kind).toBe("image_processing");
            expect(events[2].kind).toBe("history_truncated");
            expect(events[3].kind).toBe("model_call_success");
            expect(events[4].kind).toBe("session_log");
            expect(events[5].kind).toBe("response");
            expect(events[6].kind).toBe("turn_ended");

            const responseEvent = events.find((e) => e.kind === "response");
            expect(responseEvent?.response.content).toBe("Hello, this is a test response!");
        });
    });

    describe("jit instructions", () => {
        test("should include jit instructions in the request", async () => {
            // Arrange
            const mockResponse = inflatePreRecordedCompletion({
                type: "stop-message",
                content: "Response with JIT instructions!",
            });

            mockCreate.mockReturnValue(createMockCreateReturnValue(mockResponse, "req-jit-test"));

            const getJitInstructions = vi.fn().mockReturnValue("These are just-in-time instructions");

            // Act
            const result = await getCompletionWithTools(
                client,
                "You are a helpful assistant.",
                [{ role: "user", content: "Test message" }],
                [],
                { getJitInstructions },
            );
            const events = result.events;

            // Assert
            expect(mockCreate).toHaveBeenCalledTimes(1);
            expect(getJitInstructions).toHaveBeenCalled();

            const createArgs = mockCreate.mock.calls[0][0];

            // Verify jitInstructions were included in messages as the last message
            expect(createArgs.messages).toEqual([
                {
                    role: "system",
                    content: "You are a helpful assistant.",
                    copilot_cache_control: { type: "ephemeral" },
                },
                { role: "user", content: "Test message" },
                {
                    role: "user",
                    content: "These are just-in-time instructions",
                    copilot_cache_control: { type: "ephemeral" },
                },
            ]);

            // Verify response was processed normally
            const responseEvent = events.find((e) => e.kind === "response");
            expect(responseEvent?.response.content).toBe("Response with JIT instructions!");
        });

        test("should not insert jit instructions again upon retrying", async () => {
            // Arrange
            const apiError = createMockAPIError(500, "Internal Server Error", "req-error-test");

            // First call fails, second succeeds
            mockCreate
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockRejectedValue(apiError),
                })
                .mockReturnValueOnce(
                    createMockCreateReturnValue(
                        inflatePreRecordedCompletion({
                            type: "stop-message",
                            content: "Success after retry!",
                        }),
                        "req-retry-test",
                    ),
                );

            const getJitInstructions = vi.fn().mockReturnValue("These are just-in-time instructions");

            const retryClient = new ChatCompletionClient(mockProvider, settings, logger, {
                model: "test-model",
                retryPolicy: {
                    maxRetries: 3,
                },
            });

            // Act
            const result = await getCompletionWithTools(
                retryClient,
                "You are a helpful assistant.",
                [{ role: "user", content: "Test message" }],
                [],
                { getJitInstructions },
            );
            const events = result.events;

            // Assert
            expect(mockCreate).toHaveBeenCalledTimes(2);
            expect(getJitInstructions).toHaveBeenCalledTimes(1);

            const createArgs = mockCreate.mock.calls[1][0];

            // Verify response was processed normally
            const responseEvent = events.find((e) => e.kind === "response");
            expect(responseEvent?.response.content).toBe("Success after retry!");

            // Verify jitInstructions were included in messages as the last message
            expect(createArgs.messages).toEqual([
                {
                    role: "system",
                    content: "You are a helpful assistant.",
                    copilot_cache_control: { type: "ephemeral" },
                },
                { role: "user", content: "Test message" },
                {
                    role: "user",
                    content: "These are just-in-time instructions",
                    copilot_cache_control: { type: "ephemeral" },
                },
            ]);
        });

        test("should not include jit instructions if caller stops returning them", async () => {
            // Arrange
            const testTool: Tool = {
                name: "test_tool",
                description: "A test tool",
                input_schema: {
                    type: "object",
                    properties: {
                        message: { type: "string" },
                    },
                },
                callback: vi.fn().mockResolvedValue("Tool executed successfully!"),
            };

            // First response: tool call
            const toolCallResponse = createMockToolCallResponse(
                "call_123",
                "test_tool",
                '{"message": "Hello from tool!"}',
            );

            // Second response: final response after tool execution
            const finalResponse = inflatePreRecordedCompletion({
                type: "stop-message",
                content: "I used the tool and got the result!",
            });

            mockCreate
                .mockReturnValueOnce(createMockCreateReturnValue(toolCallResponse, "req-tool-call"))
                .mockReturnValueOnce(createMockCreateReturnValue(finalResponse, "req-final"));

            const getJitInstructions = vi
                .fn()
                .mockReturnValueOnce("These are just-in-time instructions")
                .mockReturnValueOnce(undefined);

            // Act
            const result = await getCompletionWithTools(
                client,
                "You are a helpful assistant.",
                [{ role: "user", content: "Test message" }],
                [testTool],
                { getJitInstructions },
            );
            const events = result.events;

            // Assert
            expect(mockCreate).toHaveBeenCalledTimes(2);
            expect(getJitInstructions).toHaveBeenCalledTimes(2);

            const createArgs = mockCreate.mock.calls[1][0];

            // Verify jitInstructions were included in messages as the last message
            expect(createArgs.messages).toEqual([
                {
                    role: "system",
                    content: "You are a helpful assistant.",
                    copilot_cache_control: { type: "ephemeral" },
                },
                { role: "user", content: "Test message" },
                {
                    role: "user",
                    content: "These are just-in-time instructions",
                },
                {
                    role: "assistant",
                    content: null,
                    refusal: null,
                    tool_calls: [
                        {
                            id: "call_123",
                            type: "function",
                            function: {
                                name: "test_tool",
                                arguments: '{"message": "Hello from tool!"}',
                            },
                        },
                    ],
                },
                {
                    role: "tool",
                    tool_call_id: "call_123",
                    content: "Tool executed successfully!",
                    copilot_cache_control: { type: "ephemeral" },
                },
            ]);

            // Verify response was processed normally
            const responseEvent = events.find((e) => e.kind === "response");
            expect(responseEvent?.response.content).toBe("I used the tool and got the result!");
        });

        test("should include jit instructions if caller keeps returning them", async () => {
            // Arrange
            const testTool: Tool = {
                name: "test_tool",
                description: "A test tool",
                input_schema: {
                    type: "object",
                    properties: {
                        message: { type: "string" },
                    },
                },
                callback: vi.fn().mockResolvedValue("Tool executed successfully!"),
            };

            // First response: tool call
            const toolCallResponse = createMockToolCallResponse(
                "call_123",
                "test_tool",
                '{"message": "Hello from tool!"}',
            );

            // Second response: final response after tool execution
            const finalResponse = inflatePreRecordedCompletion({
                type: "stop-message",
                content: "I used the tool and got the result!",
            });

            mockCreate
                .mockReturnValueOnce(createMockCreateReturnValue(toolCallResponse, "req-tool-call"))
                .mockReturnValueOnce(createMockCreateReturnValue(finalResponse, "req-final"));

            const getJitInstructions = vi.fn().mockReturnValue("These are just-in-time instructions");

            // Act
            const result = await getCompletionWithTools(
                client,
                "You are a helpful assistant.",
                [{ role: "user", content: "Test message" }],
                [testTool],
                { getJitInstructions },
            );
            const events = result.events;

            // Assert
            expect(mockCreate).toHaveBeenCalledTimes(2);
            expect(getJitInstructions).toHaveBeenCalledTimes(2);

            const createArgs = mockCreate.mock.calls[1][0];

            // Verify jitInstructions were included in messages as the last message
            expect(createArgs.messages).toEqual([
                {
                    role: "system",
                    content: "You are a helpful assistant.",
                    copilot_cache_control: { type: "ephemeral" },
                },
                { role: "user", content: "Test message" },
                {
                    role: "user",
                    content: "These are just-in-time instructions",
                },
                {
                    role: "assistant",
                    content: null,
                    refusal: null,
                    tool_calls: [
                        {
                            id: "call_123",
                            type: "function",
                            function: {
                                name: "test_tool",
                                arguments: '{"message": "Hello from tool!"}',
                            },
                        },
                    ],
                },
                {
                    role: "tool",
                    tool_call_id: "call_123",
                    content: "Tool executed successfully!",
                },
                {
                    role: "user",
                    content: "These are just-in-time instructions",
                    copilot_cache_control: { type: "ephemeral" },
                },
            ]);

            // Verify response was processed normally
            const responseEvent = events.find((e) => e.kind === "response");
            expect(responseEvent?.response.content).toBe("I used the tool and got the result!");
        });
    });

    describe("error handling", () => {
        beforeEach(() => {
            mockOpenAIClient.setInitiatorHeader.mockClear();
        });

        test("should handle 500 errors with retry", async () => {
            // Arrange
            const apiError = createMockAPIError(500, "Internal Server Error", "req-error-test");

            // First call fails, second succeeds
            mockCreate
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockRejectedValue(apiError),
                })
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockResolvedValue({
                        response: { headers: mockResponseHeaders },
                        data: inflatePreRecordedCompletion({
                            type: "stop-message",
                            content: "Success after retry!",
                        }),
                        request_id: "req-retry-test",
                    }),
                });

            const retryClient = new ChatCompletionClient(mockProvider, settings, logger, {
                model: "test-model",
                retryPolicy: {
                    maxRetries: 3,
                    errorCodesToRetry: [500], // Allow retrying 500 errors,
                },
            });

            // Act
            const result = await getCompletionWithTools(
                retryClient,
                "You are a helpful assistant.",
                [{ role: "user", content: "Test retry" }],
                [],
            );
            const events = result.events;

            // Assert - should eventually succeed and not call setInitiatorHeader
            expect(events.length).toBeGreaterThan(0);
            const responseEvent = events.find((e) => e.kind === "response");
            expect(responseEvent?.response.content).toBe("Success after retry!");
            expect(mockCreate).toHaveBeenCalledTimes(2);
            expect(mockOpenAIClient.setInitiatorHeader).not.toHaveBeenCalled();
        });

        test("should handle 429 rate limit errors with retry-after header", async () => {
            // Arrange
            const rateLimitError = new APIError(429, { message: "Rate limit exceeded" }, "Rate limit exceeded", {
                "retry-after": "2",
                "content-type": "application/json",
                "x-github-request-id": "github-req-rate-limit",
            }) as APIError;
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            (rateLimitError as any).request_id = "req-rate-limit";

            // First call fails with rate limit, second succeeds
            mockCreate
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockRejectedValue(rateLimitError),
                })
                .mockReturnValueOnce(
                    createMockCreateReturnValue(
                        inflatePreRecordedCompletion({
                            type: "stop-message",
                            content: "Success after rate limit!",
                        }),
                        "req-rate-limit-success",
                    ),
                );

            // Create a client that allows retrying 429 errors
            const retryClient = new ChatCompletionClient(mockProvider, settings, logger, {
                model: "test-model", // 429s should be retried by default
            });

            // Act
            const result = await getCompletionWithTools(
                retryClient,
                "You are a helpful assistant.",
                [{ role: "user", content: "Test rate limit" }],
                [],
            );
            const events = result.events;

            // Assert
            expect(events.length).toBeGreaterThan(0);
            const responseEvent = events.find((e) => e.kind === "response");
            expect(responseEvent?.response.content).toBe("Success after rate limit!");
            expect(mockCreate).toHaveBeenCalledTimes(2);
            expect(mockOpenAIClient.setInitiatorHeader).not.toHaveBeenCalled();
        });

        test("should handle 429 rate limit errors with default retry", async () => {
            // Arrange
            const rateLimitError = createMockAPIError(429, "Rate limit exceeded", "req-rate-limit-default");

            // First call fails with rate limit (no retry-after header), second succeeds
            mockCreate
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockRejectedValue(rateLimitError),
                })
                .mockReturnValueOnce(
                    createMockCreateReturnValue(
                        inflatePreRecordedCompletion({
                            type: "stop-message",
                            content: "Success after default rate limit retry!",
                        }),
                        "req-rate-limit-default-success",
                    ),
                );

            // Create a client that allows retrying 429 errors
            const retryClient = new ChatCompletionClient(mockProvider, settings, logger, {
                model: "test-model", // 429s should be retried by default
            });

            // Act
            const result = await getCompletionWithTools(
                retryClient,
                "You are a helpful assistant.",
                [{ role: "user", content: "Test default rate limit" }],
                [],
            );
            const events = result.events;

            // Assert
            expect(events.length).toBeGreaterThan(0);
            const responseEvent = events.find((e) => e.kind === "response");
            expect(responseEvent?.response.content).toBe("Success after default rate limit retry!");
            expect(mockCreate).toHaveBeenCalledTimes(2);
            expect(mockOpenAIClient.setInitiatorHeader).not.toHaveBeenCalled();
        });

        test("should handle 503 Service Unavailable errors with default retry", async () => {
            // Arrange
            const serviceUnavailableError = createMockAPIError(
                503,
                "Service Unavailable",
                "req-service-unavailable-default",
            );

            // First call fails with service unavailable (no retry-after header), second succeeds
            mockCreate
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockRejectedValue(serviceUnavailableError),
                })
                .mockReturnValueOnce(
                    createMockCreateReturnValue(
                        inflatePreRecordedCompletion({
                            type: "stop-message",
                            content: "Success after default rate limit retry!",
                        }),
                        "req-service-unavailable-default-success",
                    ),
                );

            // Create a client that allows retrying 503 errors
            const retryClient = new ChatCompletionClient(mockProvider, settings, logger, {
                model: "test-model", // 503s should be retried by default
            });

            // Act
            const result = await getCompletionWithTools(
                retryClient,
                "You are a helpful assistant.",
                [{ role: "user", content: "Test default service unavailable" }],
                [],
            );
            const events = result.events;

            // Assert
            expect(events.length).toBeGreaterThan(0);
            const responseEvent = events.find((e) => e.kind === "response");
            expect(responseEvent?.response.content).toBe("Success after default rate limit retry!");
            expect(mockCreate).toHaveBeenCalledTimes(2);
            expect(mockOpenAIClient.setInitiatorHeader).not.toHaveBeenCalled();
        });

        test("should handle 500 Internal Server Error errors with default retry", async () => {
            // Arrange
            const internalServerError = createMockAPIError(
                500,
                "Internal Server Error",
                "req-internal-server-error-default",
            );

            // First call fails with internal server error (no retry-after header), second succeeds
            mockCreate
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockRejectedValue(internalServerError),
                })
                .mockReturnValueOnce(
                    createMockCreateReturnValue(
                        inflatePreRecordedCompletion({
                            type: "stop-message",
                            content: "Success after default rate limit retry!",
                        }),
                        "req-internal-server-error-default-success",
                    ),
                );

            const retryClient = new ChatCompletionClient(mockProvider, settings, logger, {
                model: "test-model", // 500s should be retried by default
            });

            // Act
            const result = await getCompletionWithTools(
                retryClient,
                "You are a helpful assistant.",
                [
                    {
                        role: "user",
                        content: "Test default internal server error",
                    },
                ],
                [],
            );
            const events = result.events;

            // Assert
            expect(events.length).toBeGreaterThan(0);
            const responseEvent = events.find((e) => e.kind === "response");
            expect(responseEvent?.response.content).toBe("Success after default rate limit retry!");
            expect(mockCreate).toHaveBeenCalledTimes(2);
            expect(mockOpenAIClient.setInitiatorHeader).not.toHaveBeenCalled();
        });

        test("should handle 400 token limit errors by at least retrying", async () => {
            // Arrange
            const tokenLimitError = createMockAPIError(
                400,
                "prompt token count of 5000 exceeds the limit of 4000",
                "req-token-limit",
            );

            // First call fails with token limit, second succeeds with truncated context
            mockCreate
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockRejectedValue(tokenLimitError),
                })
                .mockReturnValueOnce(
                    createMockCreateReturnValue(
                        inflatePreRecordedCompletion({
                            type: "stop-message",
                            content: "Success with truncated context!",
                        }),
                        "req-token-limit-success",
                    ),
                );

            // Act
            const result = await getCompletionWithTools(
                client,
                "You are a helpful assistant.",
                [{ role: "user", content: "Test token limit" }],
                [],
            );
            const events = result.events;

            // Assert
            expect(events.length).toBeGreaterThan(0);
            const responseEvent = events.find((e) => e.kind === "response");
            expect(responseEvent?.response.content).toBe("Success with truncated context!");
            expect(mockCreate).toHaveBeenCalledTimes(2);
            expect(mockOpenAIClient.setInitiatorHeader).not.toHaveBeenCalled();
        });

        test("should handle 402 payment required errors by retrying with agent initiator", async () => {
            // Arrange
            const paymentError = createMockAPIError(402, "Payment Required", "req-payment-required");

            // First call fails with 402, second succeeds
            mockCreate
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockRejectedValue(paymentError),
                })
                .mockReturnValueOnce(
                    createMockCreateReturnValue(
                        inflatePreRecordedCompletion({
                            type: "stop-message",
                            content: "Success after payment required!",
                        }),
                        "req-payment-success",
                    ),
                );

            // Act
            const result = await getCompletionWithTools(
                client,
                "You are a helpful assistant.",
                [{ role: "user", content: "Test payment required" }],
                [],
            );
            const events = result.events;

            // Assert
            expect(events.length).toBeGreaterThan(0);
            const responseEvent = events.find((e) => e.kind === "response");
            expect(responseEvent?.response.content).toBe("Success after payment required!");
            expect(mockCreate).toHaveBeenCalledTimes(2);
            expect(mockOpenAIClient.setInitiatorHeader).toHaveBeenCalledWith("agent");
        });

        test("should throw after max retries on 500 errors", async () => {
            // Create a client with shorter retry policy for this test
            const shortRetryClient = new ChatCompletionClient(mockProvider, settings, logger, {
                model: "test-model",
                retryPolicy: {
                    maxRetries: 2,
                    rateLimitRetryPolicy: {
                        defaultRetryAfterSeconds: 1,
                        maxRetryAfterSeconds: 1, // Short max to force giving up quickly
                    },
                },
            });

            // Arrange
            const apiError = createMockAPIError(500, "Persistent Server Error", "req-persistent-error");

            mockCreate.mockReturnValue({
                withResponse: vi.fn().mockRejectedValue(apiError),
            });

            // Act
            const result = await getCompletionWithTools(
                shortRetryClient,
                "You are a helpful assistant.",
                [{ role: "user", content: "This will fail" }],
                [],
            );
            const error = result.error?.obj;

            // Assert
            expect(error).toBeInstanceOf(CAPIError);
            expect((error as CAPIError).name).toBe("CAPIError");
            expect((error as CAPIError).ghRequestId).toBe("github-req-persistent-error");
            expect(error).toEqual(
                expect.objectContaining({
                    message: expect.stringContaining("Persistent Server Error"),
                }),
            );
            expect(mockOpenAIClient.setInitiatorHeader).not.toHaveBeenCalled();
        });

        test("should handle errors with non-standard codes in errorCodesToRetry option with retry", async () => {
            // Arrange
            const apiError = createMockAPIError(418, "Internal Server Error", "req-error-test");

            // First call fails, second succeeds
            mockCreate
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockRejectedValue(apiError),
                })
                .mockReturnValueOnce(
                    createMockCreateReturnValue(
                        inflatePreRecordedCompletion({
                            type: "stop-message",
                            content: "Success after retry!",
                        }),
                        "req-retry-test",
                    ),
                );

            const retryClient = new ChatCompletionClient(mockProvider, settings, logger, {
                model: "test-model",
                retryPolicy: {
                    maxRetries: 3,
                    errorCodesToRetry: [418], // Allow retrying 418 errors
                },
            });

            // Act
            const result = await getCompletionWithTools(
                retryClient,
                "You are a helpful assistant.",
                [{ role: "user", content: "Test retry" }],
                [],
            );
            const events = result.events;

            // Assert - should eventually succeed and not call setInitiatorHeader
            expect(events.length).toBeGreaterThan(0);
            const responseEvent = events.find((e) => e.kind === "response");
            expect(responseEvent?.response.content).toBe("Success after retry!");
            expect(mockCreate).toHaveBeenCalledTimes(2);
            expect(mockOpenAIClient.setInitiatorHeader).not.toHaveBeenCalled();
        });

        test("should not handle errors with non-standard codes not in errorCodesToRetry option with retry", async () => {
            // Create a client with shorter retry policy for this test
            const shortRetryClient = new ChatCompletionClient(mockProvider, settings, logger, {
                model: "test-model",
                retryPolicy: {
                    maxRetries: 2,
                    rateLimitRetryPolicy: {
                        defaultRetryAfterSeconds: 1,
                        maxRetryAfterSeconds: 1, // Short max to force giving up quickly
                    },
                },
            });

            // Arrange
            const apiError = createMockAPIError(418, "Persistent Server Error", "req-persistent-error");

            mockCreate.mockReturnValue({
                withResponse: vi.fn().mockRejectedValue(apiError),
            });

            // Act
            const result = await getCompletionWithTools(
                shortRetryClient,
                "You are a helpful assistant.",
                [{ role: "user", content: "This will fail" }],
                [],
            );
            const error = result.error?.obj;

            // Assert
            expect(error).toBeInstanceOf(CAPIError);
            expect((error as CAPIError).name).toBe("CAPIError");
            expect((error as CAPIError).ghRequestId).toBe("github-req-persistent-error");
            expect(error).toEqual(
                expect.objectContaining({
                    message: expect.stringContaining("Persistent Server Error"),
                }),
            );
            expect(mockOpenAIClient.setInitiatorHeader).not.toHaveBeenCalled();
        });
    });

    describe("tool calling", () => {
        test("should handle tool calls and responses", async () => {
            // Arrange
            const testTool: Tool = {
                name: "test_tool",
                description: "A test tool",
                input_schema: {
                    type: "object",
                    properties: {
                        message: { type: "string" },
                    },
                },
                callback: vi.fn().mockResolvedValue("Tool executed successfully!"),
            };

            // First response: tool call
            const toolCallResponse = createMockToolCallResponse(
                "call_123",
                "test_tool",
                '{"message": "Hello from tool!"}',
            );

            // Second response: final response after tool execution
            const finalResponse = inflatePreRecordedCompletion({
                type: "stop-message",
                content: "I used the tool and got the result!",
            });

            mockCreate
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockResolvedValue({
                        response: { headers: mockResponseHeaders },
                        data: toolCallResponse,
                        request_id: "req-tool-call",
                    }),
                })
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockResolvedValue({
                        response: { headers: mockResponseHeaders },
                        data: finalResponse,
                        request_id: "req-final",
                    }),
                });

            // Act
            const result = await getCompletionWithTools(
                client,
                "You are a helpful assistant.",
                [{ role: "user", content: "Use the test tool" }],
                [testTool],
            );
            const events = result.events;

            // Assert
            expect(events.length).toBeGreaterThan(4); // At least: session_log, message (assistant), message (tool), session_log, response, telemetry

            // Check that we got the tool call message
            const assistantMessage = events.find((e) => e.kind === "message" && e.message.role === "assistant") as
                | AssistantMessageEvent
                | undefined;
            expect(assistantMessage).toBeDefined();
            expect(assistantMessage?.message.tool_calls).toHaveLength(1);

            // Check that we got the tool response message
            const toolMessage = events.find((e) => e.kind === "message" && e.message.role === "tool") as
                | ToolMessageEvent
                | undefined;
            expect(toolMessage).toBeDefined();
            expect(toolMessage?.message.content).toBe("Tool executed successfully!");

            // Check that the final response was received
            const responseEvent = events.find((e) => e.kind === "response");
            expect(responseEvent?.response.content).toBe("I used the tool and got the result!");

            // Check that the tool callback was called with correct arguments
            expect(testTool.callback).toHaveBeenCalledWith({ message: "Hello from tool!" }, expect.any(Object));
        });

        test("should handle tool call with invalid JSON arguments", async () => {
            // Arrange
            const testTool: Tool = {
                name: "test_tool",
                description: "A test tool",
                input_schema: {
                    type: "object",
                    properties: {
                        message: { type: "string" },
                    },
                },
                callback: vi.fn().mockResolvedValue("Tool executed successfully!"),
            };

            // Tool call with invalid JSON
            const toolCallResponse = createMockToolCallResponse(
                "call_456",
                "test_tool",
                '{"message": "Hello from tool!", invalid}', // Invalid JSON
            );

            // Final response after tool execution
            const finalResponse = inflatePreRecordedCompletion({
                type: "stop-message",
                content: "I handled the invalid JSON gracefully!",
            });

            mockCreate
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockResolvedValue({
                        response: { headers: mockResponseHeaders },
                        data: toolCallResponse,
                        request_id: "req-tool-invalid",
                    }),
                })
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockResolvedValue({
                        response: { headers: mockResponseHeaders },
                        data: finalResponse,
                        request_id: "req-final-invalid",
                    }),
                });

            // Act
            const result = await getCompletionWithTools(
                client,
                "You are a helpful assistant.",
                [
                    {
                        role: "user",
                        content: "Use the test tool with invalid JSON",
                    },
                ],
                [testTool],
            );
            const events = result.events;

            // Assert
            expect(events.length).toBeGreaterThan(4);

            // Check that we got a tool message with error about invalid JSON
            const toolMessage = events.find((e) => e.kind === "message" && e.message.role === "tool") as
                | ToolMessageEvent
                | undefined;
            expect(toolMessage).toBeDefined();
            expect(toolMessage?.message.content).toContain("not valid JSON");
            expect(toolMessage?.message.content).toContain("test_tool");

            // Tool callback should not have been called due to invalid JSON
            expect(testTool.callback).not.toHaveBeenCalled();

            // Should still get a final response
            const responseEvent = events.find((e) => e.kind === "response");
            expect(responseEvent?.response.content).toBe("I handled the invalid JSON gracefully!");
        });

        test("should handle tool that does not exist", async () => {
            // Arrange
            const existingTool: Tool = {
                name: "existing_tool",
                description: "An existing tool",
                input_schema: { type: "object" },
                callback: vi.fn().mockResolvedValue("Should not be called"),
            };

            // Tool call for non-existent tool
            const toolCallResponse = createMockToolCallResponse(
                "call_789",
                "non_existent_tool",
                '{"message": "Hello"}',
            );

            // Final response
            const finalResponse = inflatePreRecordedCompletion({
                type: "stop-message",
                content: "I understand the tool does not exist.",
            });

            mockCreate
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockResolvedValue({
                        response: { headers: mockResponseHeaders },
                        data: toolCallResponse,
                        request_id: "req-tool-missing",
                    }),
                })
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockResolvedValue({
                        response: { headers: mockResponseHeaders },
                        data: finalResponse,
                        request_id: "req-final-missing",
                    }),
                });

            // Act
            const result = await getCompletionWithTools(
                client,
                "You are a helpful assistant.",
                [{ role: "user", content: "Use a non-existent tool" }],
                [existingTool],
            );
            const events = result.events;

            // Assert
            expect(events.length).toBeGreaterThan(4);

            // Check that we got a tool message with error about missing tool
            const toolMessage = events.find((e) => e.kind === "message" && e.message.role === "tool") as
                | ToolMessageEvent
                | undefined;
            expect(toolMessage).toBeDefined();
            expect(toolMessage?.message.content).toContain("Tool 'non_existent_tool' does not exist");
            expect(toolMessage?.message.content).toContain("existing_tool");

            // Existing tool should not have been called
            expect(existingTool.callback).not.toHaveBeenCalled();

            // Should still get a final response
            const responseEvent = events.find((e) => e.kind === "response");
            expect(responseEvent?.response.content).toBe("I understand the tool does not exist.");
        });

        test("should handle multiple tool calls in single response", async () => {
            // Arrange
            const testTool1: Tool = {
                name: "test_tool_1",
                description: "First test tool",
                input_schema: {
                    type: "object",
                    properties: { message: { type: "string" } },
                },
                callback: vi.fn().mockResolvedValue("Tool 1 executed!"),
            };
            const testTool2: Tool = {
                name: "test_tool_2",
                description: "Second test tool",
                input_schema: {
                    type: "object",
                    properties: { data: { type: "string" } },
                },
                callback: vi.fn().mockResolvedValue("Tool 2 executed!"),
            };

            const multiToolResponse = inflatePreRecordedCompletion({
                type: "tool-calls",
                toolsToCall: [
                    { tool: testTool1, args: { message: "First call" } },
                    { tool: testTool2, args: { data: "Second call" } },
                ],
            });

            const finalResponse = inflatePreRecordedCompletion({
                type: "stop-message",
                content: "Used both tools!",
            });

            mockCreate
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockResolvedValue({
                        response: { headers: mockResponseHeaders },
                        data: multiToolResponse,
                    }),
                })
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockResolvedValue({
                        response: { headers: mockResponseHeaders },
                        data: finalResponse,
                    }),
                });

            // Act
            const result = await getCompletionWithTools(
                client,
                "System",
                [{ role: "user", content: "Use both tools" }],
                [testTool1, testTool2],
            );
            const events = result.events;

            // Assert
            const toolMessages = events.filter((e) => e.kind === "message" && e.message.role === "tool");
            expect(toolMessages).toHaveLength(2);
            expect(testTool1.callback).toHaveBeenCalledWith({ message: "First call" }, expect.any(Object));
            expect(testTool2.callback).toHaveBeenCalledWith({ data: "Second call" }, expect.any(Object));
        });

        test("should handle multiple choices with tool calls", async () => {
            const testTool1: Tool = {
                name: "test_tool_1",
                description: "First test tool",
                input_schema: {
                    type: "object",
                    properties: { message: { type: "string" } },
                },
                callback: vi.fn().mockResolvedValue("Tool 1 executed!"),
            };

            const testTool2: Tool = {
                name: "test_tool_2",
                description: "Second test tool",
                input_schema: {
                    type: "object",
                    properties: { data: { type: "string" } },
                },
                callback: vi.fn().mockResolvedValue("Tool 2 executed!"),
            };

            const multiChoiceResponse = inflatePreRecordedCompletion([
                {
                    type: "tool-calls",
                    toolsToCall: [
                        {
                            tool: testTool1,
                            args: { message: "First tool call" },
                        },
                    ],
                },
                {
                    type: "tool-calls",
                    toolsToCall: [{ tool: testTool2, args: { data: "Second tool call" } }],
                },
            ]);

            const finalResponse = inflatePreRecordedCompletion({
                type: "stop-message",
                content: "Processed both tools!",
            });

            mockCreate
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockResolvedValue({
                        response: { headers: mockResponseHeaders },
                        data: multiChoiceResponse,
                    }),
                })
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockResolvedValue({
                        response: { headers: mockResponseHeaders },
                        data: finalResponse,
                    }),
                });

            // Act
            const result = await getCompletionWithTools(
                client,
                "System",
                [{ role: "user", content: "Test multiple choices" }],
                [testTool1, testTool2],
            );
            const events = result.events;

            // Assert
            // Should process both choices with different tools
            const toolMessages = events.filter((e) => e.kind === "message" && e.message.role === "tool");
            expect(toolMessages).toHaveLength(2);
            expect(testTool1.callback).toHaveBeenCalledTimes(1);
            expect(testTool1.callback).toHaveBeenCalledWith({ message: "First tool call" }, expect.any(Object));
            expect(testTool2.callback).toHaveBeenCalledTimes(1);
            expect(testTool2.callback).toHaveBeenCalledWith({ data: "Second tool call" }, expect.any(Object));

            // Final response should be received
            const responseEvent = events.find((e) => e.kind === "response");
            expect(responseEvent?.response.content).toBe("Processed both tools!");
        });

        test("should handle mixed text and tool call choices", async () => {
            const testTool: Tool = {
                name: "test_tool",
                description: "Test tool",
                input_schema: {
                    type: "object",
                    properties: { message: { type: "string" } },
                },
                callback: vi.fn().mockResolvedValue("Tool executed!"),
            };

            const mixedResponse = inflatePreRecordedCompletion([
                { type: "stop-message", content: "Direct text response" },
                {
                    type: "tool-calls",
                    toolsToCall: [{ tool: testTool, args: { message: "Tool choice" } }],
                },
            ]);

            const finalResponse = inflatePreRecordedCompletion({
                type: "stop-message",
                content: "Used first choice!",
            });

            mockCreate
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockResolvedValue({
                        response: { headers: mockResponseHeaders },
                        data: mixedResponse,
                    }),
                })
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockResolvedValue({
                        response: { headers: mockResponseHeaders },
                        data: finalResponse,
                    }),
                });

            // Act
            const result = await getCompletionWithTools(
                client,
                "System",
                [{ role: "user", content: "Test mixed responses" }],
                [testTool],
            );
            const events = result.events;

            // Assert
            // Should process both choices
            const assistantMessages = events.filter(
                (e) => e.kind === "message" && e.message.role === "assistant",
            ) as AssistantMessageEvent[];
            expect(assistantMessages).toHaveLength(2); // One for text, one for tool call

            // Check direct text response was processed
            const textMessage = assistantMessages.find((m) => m.message.content === "Direct text response");
            expect(textMessage).toBeDefined();

            // Check tool call was processed
            const toolCallMessage = assistantMessages.find((m) => m.message.tool_calls);
            expect(toolCallMessage).toBeDefined();
            expect(toolCallMessage?.message.tool_calls).toBeDefined();

            const toolMessage = events.find((e) => e.kind === "message" && e.message.role === "tool") as
                | ToolMessageEvent
                | undefined;
            expect(toolMessage).toBeDefined();
            expect(toolMessage?.message.content).toBe("Tool executed!");

            expect(testTool.callback).toHaveBeenCalledWith({ message: "Tool choice" }, expect.any(Object));

            // Final response should be received
            const responseEvent = events.find((e) => e.kind === "response");
            expect(responseEvent?.response.content).toBe("Used first choice!");
        });

        test("should handle tool executions which throw", async () => {
            const error = new Error("Tool execution failed");
            const testTool: Tool = {
                name: "test_tool",
                description: "Test tool",
                input_schema: {
                    type: "object",
                    properties: { message: { type: "string" } },
                },
                callback: vi.fn().mockRejectedValue(error),
            };

            const mixedResponse = inflatePreRecordedCompletion([
                { type: "stop-message", content: "Direct text response" },
                {
                    type: "tool-calls",
                    toolsToCall: [{ tool: testTool, args: { message: "Tool choice" } }],
                },
            ]);

            const finalResponse = inflatePreRecordedCompletion({
                type: "stop-message",
                content: "Used first choice!",
            });

            mockCreate
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockResolvedValue({
                        response: { headers: mockResponseHeaders },
                        data: mixedResponse,
                    }),
                })
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockResolvedValue({
                        response: { headers: mockResponseHeaders },
                        data: finalResponse,
                    }),
                });

            // Act
            const result = await getCompletionWithTools(
                client,
                "System",
                [{ role: "user", content: "Test mixed responses" }],
                [testTool],
            );
            const events = result.events;

            // Assert
            // Should process both choices
            const assistantMessages = events.filter(
                (e) => e.kind === "message" && e.message.role === "assistant",
            ) as AssistantMessageEvent[];
            expect(assistantMessages).toHaveLength(2); // One for text, one for tool call

            // Check direct text response was processed
            const textMessage = assistantMessages.find((m) => m.message.content === "Direct text response");
            expect(textMessage).toBeDefined();

            // Check tool call was processed
            const toolCallMessage = assistantMessages.find((m) => m.message.tool_calls);
            expect(toolCallMessage).toBeDefined();
            expect(toolCallMessage?.message.tool_calls).toBeDefined();

            const toolMessage = events.find((e) => e.kind === "message" && e.message.role === "tool") as
                | ToolMessageEvent
                | undefined;
            expect(toolMessage).toBeDefined();
            expect(toolMessage?.message.content).toBe(
                `Failed to execute \`test_tool\` tool with arguments: {"message":"Tool choice"} due to error: ${error}`,
            );

            expect(testTool.callback).toHaveBeenCalledWith({ message: "Tool choice" }, expect.any(Object));

            // Final response should be received
            const responseEvent = events.find((e) => e.kind === "response");
            expect(responseEvent?.response.content).toBe("Used first choice!");
        });

        const missingFieldModifiersAndExpectedError = [
            {
                modifier: (newMixedResponse: ChatCompletion) => {
                    // Empty name
                    newMixedResponse.choices[1].message.tool_calls![0]!.function.name = "";
                    return newMixedResponse;
                },
                expectedError: "Tool name is missing",
            },
            {
                modifier: (newMixedResponse: ChatCompletion) => {
                    // Empty arguments
                    newMixedResponse.choices[1].message.tool_calls![0]!.function.arguments = "";
                    return newMixedResponse;
                },
                expectedError: "Tool call arguments are missing",
            },
            {
                modifier: (newMixedResponse: ChatCompletion) => {
                    // Missing tool call id
                    newMixedResponse.choices[1].message.tool_calls![0]!.id = "";
                    return newMixedResponse;
                },
                expectedError: "Tool call id is missing",
            },
        ];
        for (const modifierAndExpectedError of missingFieldModifiersAndExpectedError) {
            // Arrange
            const testTool: Tool = {
                name: "test_tool",
                description: "Test tool",
                input_schema: {
                    type: "object",
                    properties: { message: { type: "string" } },
                },
                callback: vi.fn().mockResolvedValue("Tool executed successfully!"),
            };

            const mixedResponse = inflatePreRecordedCompletion([
                { type: "stop-message", content: "Direct text response" },
                {
                    type: "tool-calls",
                    toolsToCall: [{ tool: testTool, args: { message: "Tool choice" } }],
                },
            ]);

            const finalResponse = inflatePreRecordedCompletion({
                type: "stop-message",
                content: "Used first choice!",
            });

            test(`should handle tool calls with missing/empty fields - ${modifierAndExpectedError.expectedError}`, async () => {
                // Modify the mixed response to trigger the error
                const modifiedMixedResponse = modifierAndExpectedError.modifier(
                    JSON.parse(JSON.stringify(mixedResponse)),
                );
                mockCreate
                    .mockReturnValueOnce({
                        withResponse: vi.fn().mockResolvedValue({
                            response: { headers: mockResponseHeaders },
                            data: modifiedMixedResponse,
                        }),
                    })
                    .mockReturnValueOnce({
                        withResponse: vi.fn().mockResolvedValue({
                            response: { headers: mockResponseHeaders },
                            data: finalResponse,
                        }),
                    });

                // Act
                const result = await getCompletionWithTools(
                    client,
                    "System",
                    [{ role: "user", content: "Test mixed responses" }],
                    [testTool],
                );
                const events = result.events;
                const error = result.error?.str;

                // Assert
                // Should process both choices
                const assistantMessages = events.filter(
                    (e) => e.kind === "message" && e.message.role === "assistant",
                ) as AssistantMessageEvent[];
                expect(assistantMessages).toHaveLength(2); // One for text, one for tool call

                // Check direct text response was processed
                const textMessage = assistantMessages.find((m) => m.message.content === "Direct text response");
                expect(textMessage).toBeDefined();

                // Check tool call was processed
                const toolCallMessage = assistantMessages.find((m) => m.message.tool_calls);
                expect(toolCallMessage).toBeDefined();
                expect(toolCallMessage?.message.tool_calls).toBeDefined();

                expect(testTool.callback).toHaveBeenCalledTimes(0);

                // Final error should be received
                expect(error).toContain(modifierAndExpectedError.expectedError);
            });
        }

        test("should halt processing when tool result is rejected", async () => {
            // Arrange
            const testTool: Tool = {
                name: "test_tool",
                description: "A test tool",
                input_schema: {
                    type: "object",
                    properties: {
                        message: { type: "string" },
                    },
                },
                callback: vi.fn().mockResolvedValue({
                    textResultForLlm:
                        "The tool call required confirmation from the user, but the user rejected it. Await further instructions.",
                    resultType: "rejected",
                    error: "Tool call was rejected by user",
                    sessionLog: "<error>Tool call was rejected by user</error>",
                    toolTelemetry: {},
                } as ToolResultExpanded),
            };

            // First response: tool call
            const toolCallResponse = createMockToolCallResponse(
                "call_123",
                "test_tool",
                '{"message": "Hello from tool!"}',
            );

            // Second response should not be needed since processing halts
            const shouldNotBeCalledResponse = inflatePreRecordedCompletion({
                type: "stop-message",
                content: "This should not be reached!",
            });

            mockCreate
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockResolvedValue({
                        response: { headers: mockResponseHeaders },
                        data: toolCallResponse,
                        request_id: "req-tool-call",
                    }),
                })
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockResolvedValue({
                        response: { headers: mockResponseHeaders },
                        data: shouldNotBeCalledResponse,
                        request_id: "req-should-not-be-called",
                    }),
                });

            // Act
            const result = await getCompletionWithTools(
                client,
                "You are a helpful assistant.",
                [{ role: "user", content: "Use the test tool" }],
                [testTool],
            );
            const events = result.events;

            // Assert
            // Check that we got the tool call message
            const assistantMessage = events.find((e) => e.kind === "message" && e.message.role === "assistant") as
                | AssistantMessageEvent
                | undefined;
            expect(assistantMessage).toBeDefined();
            expect(assistantMessage?.message.tool_calls).toHaveLength(1);

            // Check that we got the tool response message with rejection
            const toolMessage = events.find((e) => e.kind === "message" && e.message.role === "tool") as
                | ToolMessageEvent
                | undefined;
            expect(toolMessage).toBeDefined();
            expect(toolMessage?.message.content).toBe(
                "The tool call required confirmation from the user, but the user rejected it. Await further instructions.",
            );

            // Check that NO final response was received (processing should halt)
            const responseEvent = events.find((e) => e.kind === "response");
            expect(responseEvent).toBeUndefined();

            // Check that the tool callback was called with correct arguments
            expect(testTool.callback).toHaveBeenCalledWith({ message: "Hello from tool!" }, expect.any(Object));

            // Verify the second mock (shouldNotBeCalledResponse) was never used
            expect(mockCreate).toHaveBeenCalledTimes(1);
        });

        test("should halt processing after first rejected tool in multiple tool calls", async () => {
            // Arrange
            const acceptedTool: Tool = {
                name: "accepted_tool",
                description: "A tool that succeeds",
                input_schema: {
                    type: "object",
                    properties: {
                        message: { type: "string" },
                    },
                },
                callback: vi.fn().mockResolvedValue({
                    textResultForLlm: "Tool executed successfully!",
                    resultType: "success",
                    sessionLog: "Tool executed successfully!",
                    toolTelemetry: {},
                } as ToolResultExpanded),
            };

            const rejectedTool: Tool = {
                name: "rejected_tool",
                description: "A tool that gets rejected",
                input_schema: {
                    type: "object",
                    properties: {
                        message: { type: "string" },
                    },
                },
                callback: vi.fn().mockResolvedValue({
                    textResultForLlm:
                        "The user rejected an earlier tool call and does not want to take this action right now. Await further instructions.",
                    resultType: "rejected",
                    error: "Tool call was rejected by user",
                    sessionLog: "<error>Tool call was rejected by user</error>",
                    toolTelemetry: {},
                } as ToolResultExpanded),
            };

            // Response with multiple tool calls
            const multipleToolCallsResponse = inflatePreRecordedCompletion({
                id: "chatcmpl-multiple-tools",
                choices: [
                    {
                        index: 0,
                        message: {
                            role: "assistant",
                            content: null,
                            refusal: null,
                            tool_calls: [
                                {
                                    id: "call_accepted",
                                    type: "function",
                                    function: {
                                        name: "accepted_tool",
                                        arguments: '{"message": "This should work"}',
                                    },
                                },
                                {
                                    id: "call_rejected",
                                    type: "function",
                                    function: {
                                        name: "rejected_tool",
                                        arguments: '{"message": "This will be rejected"}',
                                    },
                                },
                            ],
                        },
                        finish_reason: "tool_calls",
                        logprobs: null,
                    },
                ],
                usage: {
                    prompt_tokens: 20,
                    completion_tokens: 40,
                    total_tokens: 60,
                },
            });

            mockCreate.mockReturnValueOnce({
                withResponse: vi.fn().mockResolvedValue({
                    response: { headers: mockResponseHeaders },
                    data: multipleToolCallsResponse,
                    request_id: "req-multiple-tools",
                }),
            });

            // Act
            const result = await getCompletionWithTools(
                client,
                "You are a helpful assistant.",
                [{ role: "user", content: "Use both tools" }],
                [acceptedTool, rejectedTool],
            );
            const events = result.events;

            // Assert
            // Check that we got the tool call message
            const assistantMessage = events.find((e) => e.kind === "message" && e.message.role === "assistant") as
                | AssistantMessageEvent
                | undefined;
            expect(assistantMessage).toBeDefined();
            expect(assistantMessage?.message.tool_calls).toHaveLength(2);

            // Check that we got tool response messages
            const toolMessages = events.filter(
                (e) => e.kind === "message" && e.message.role === "tool",
            ) as AssistantMessageEvent[];
            expect(toolMessages).toHaveLength(2);

            // Check that both tools were called
            expect(acceptedTool.callback).toHaveBeenCalled();
            expect(rejectedTool.callback).toHaveBeenCalled();

            // Check that NO final response was received (processing should halt after rejection)
            const responseEvent = events.find((e) => e.kind === "response");
            expect(responseEvent).toBeUndefined();

            // Verify only one model call was made (no follow-up after rejection)
            expect(mockCreate).toHaveBeenCalledTimes(1);
        });

        test("should process all tools in the same turn even when one is rejected", async () => {
            // Arrange
            const firstTool: Tool = {
                name: "first_tool",
                description: "A tool that gets rejected",
                input_schema: {
                    type: "object",
                    properties: {
                        message: { type: "string" },
                    },
                },
                callback: vi.fn().mockResolvedValue({
                    textResultForLlm:
                        "The tool call required confirmation from the user, but the user rejected it. Await further instructions.",
                    resultType: "rejected",
                    error: "Tool call was rejected by user",
                    sessionLog: "<error>Tool call was rejected by user</error>",
                    toolTelemetry: {},
                } as ToolResultExpanded),
            };

            const secondTool: Tool = {
                name: "second_tool",
                description: "A tool that succeeds",
                input_schema: {
                    type: "object",
                    properties: {
                        message: { type: "string" },
                    },
                },
                callback: vi.fn().mockResolvedValue({
                    textResultForLlm: "Second tool executed successfully!",
                    resultType: "success",
                    sessionLog: "Second tool executed successfully!",
                    toolTelemetry: {},
                } as ToolResultExpanded),
            };

            const thirdTool: Tool = {
                name: "third_tool",
                description: "A tool that also succeeds",
                input_schema: {
                    type: "object",
                    properties: {
                        message: { type: "string" },
                    },
                },
                callback: vi.fn().mockResolvedValue({
                    textResultForLlm: "Third tool executed successfully!",
                    resultType: "success",
                    sessionLog: "Third tool executed successfully!",
                    toolTelemetry: {},
                } as ToolResultExpanded),
            };

            // Response with multiple tool calls in a single response
            const multipleToolCallsResponse = inflatePreRecordedCompletion({
                id: "chatcmpl-multiple-tools-same-turn",
                choices: [
                    {
                        index: 0,
                        message: {
                            role: "assistant",
                            content: null,
                            refusal: null,
                            tool_calls: [
                                {
                                    id: "call_first",
                                    type: "function",
                                    function: {
                                        name: "first_tool",
                                        arguments: '{"message": "This will be rejected"}',
                                    },
                                },
                                {
                                    id: "call_second",
                                    type: "function",
                                    function: {
                                        name: "second_tool",
                                        arguments: '{"message": "This should work"}',
                                    },
                                },
                                {
                                    id: "call_third",
                                    type: "function",
                                    function: {
                                        name: "third_tool",
                                        arguments: '{"message": "This should also work"}',
                                    },
                                },
                            ],
                        },
                        finish_reason: "tool_calls",
                        logprobs: null,
                    },
                ],
                usage: {
                    prompt_tokens: 30,
                    completion_tokens: 60,
                    total_tokens: 90,
                },
            });

            // Mock only one response since processing should halt after rejection
            mockCreate.mockReturnValueOnce({
                withResponse: vi.fn().mockResolvedValue({
                    response: { headers: mockResponseHeaders },
                    data: multipleToolCallsResponse,
                    request_id: "req-multiple-tools-same-turn",
                }),
            });

            // Act
            const result = await getCompletionWithTools(
                client,
                "You are a helpful assistant.",
                [{ role: "user", content: "Use all three tools" }],
                [firstTool, secondTool, thirdTool],
            );
            const events = result.events;

            // Assert
            // Check that we got the tool call message with all three tool calls
            const assistantMessage = events.find((e) => e.kind === "message" && e.message.role === "assistant") as
                | AssistantMessageEvent
                | undefined;
            expect(assistantMessage).toBeDefined();
            expect(assistantMessage?.message.tool_calls).toHaveLength(3);

            // Check that ALL THREE tools were called (inner loop completes)
            expect(firstTool.callback).toHaveBeenCalledWith({ message: "This will be rejected" }, expect.any(Object));
            expect(secondTool.callback).toHaveBeenCalledWith({ message: "This should work" }, expect.any(Object));
            expect(thirdTool.callback).toHaveBeenCalledWith({ message: "This should also work" }, expect.any(Object));

            // Check that we got tool response messages for all three tools
            const toolMessages = events.filter(
                (e) => e.kind === "message" && e.message.role === "tool",
            ) as ToolMessageEvent[];
            expect(toolMessages).toHaveLength(3);

            // Verify the content of each tool message
            const firstToolMessage = toolMessages.find((m) => m.message.tool_call_id === "call_first");
            expect(firstToolMessage?.message.content).toBe(
                "The tool call required confirmation from the user, but the user rejected it. Await further instructions.",
            );

            const secondToolMessage = toolMessages.find((m) => m.message.tool_call_id === "call_second");
            expect(secondToolMessage?.message.content).toBe("Second tool executed successfully!");

            const thirdToolMessage = toolMessages.find((m) => m.message.tool_call_id === "call_third");
            expect(thirdToolMessage?.message.content).toBe("Third tool executed successfully!");

            // Check that NO final response was received (outer loop halts due to userHalted=true)
            const responseEvent = events.find((e) => e.kind === "response");
            expect(responseEvent).toBeUndefined();

            // Verify only one model call was made (no follow-up after rejection)
            expect(mockCreate).toHaveBeenCalledTimes(1);
        });
    });

    describe("parallel tool calling", () => {
        test("should execute multiple tool calls in parallel when executeToolsInParallel is true", async () => {
            const executionOrder: string[] = [];

            const tool1: Tool = {
                name: "parallel_tool_1",
                description: "Parallel tool 1",
                input_schema: {
                    type: "object",
                    properties: { foo: { type: "string" } },
                },
                callback: vi.fn().mockImplementation(async () => {
                    executionOrder.push("tool1_start");
                    // Simulate async work without timers
                    await Promise.resolve();
                    executionOrder.push("tool1_end");
                    return "Tool 1 done";
                }),
            };
            const tool2: Tool = {
                name: "parallel_tool_2",
                description: "Parallel tool 2",
                input_schema: {
                    type: "object",
                    properties: { bar: { type: "string" } },
                },
                callback: vi.fn().mockImplementation(async () => {
                    executionOrder.push("tool2_start");
                    // Simulate async work without timers
                    await Promise.resolve();
                    executionOrder.push("tool2_end");
                    return "Tool 2 done";
                }),
            };

            const multiToolResponse = inflatePreRecordedCompletion({
                type: "tool-calls",
                toolsToCall: [
                    { tool: tool1, args: { foo: "abc" } },
                    { tool: tool2, args: { bar: "xyz" } },
                ],
            });
            const finalResponse = inflatePreRecordedCompletion({
                type: "stop-message",
                content: "Parallel tools finished!",
            });

            mockCreate
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockResolvedValue({
                        response: { headers: mockResponseHeaders },
                        data: multiToolResponse,
                    }),
                })
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockResolvedValue({
                        response: { headers: mockResponseHeaders },
                        data: finalResponse,
                    }),
                });

            const result = await getCompletionWithTools(
                client,
                "System",
                [{ role: "user", content: "Run parallel tools" }],
                [tool1, tool2],
                { executeToolsInParallel: true },
            );
            const events = result.events;

            expect(tool1.callback).toHaveBeenCalled();
            expect(tool2.callback).toHaveBeenCalled();
            expect(executionOrder).toContain("tool1_start");
            expect(executionOrder).toContain("tool2_start");
            expect(executionOrder).toContain("tool1_end");
            expect(executionOrder).toContain("tool2_end");

            // In parallel execution, both tools should start before either completes
            // This verifies that Promise.all is used rather than sequential execution
            expect(executionOrder).toEqual(["tool1_start", "tool2_start", "tool1_end", "tool2_end"]);

            // Both tool messages should be present
            const toolMessages = events.filter(
                (e) => e.kind === "message" && e.message.role === "tool",
            ) as ToolMessageEvent[];
            expect(toolMessages).toHaveLength(2);
            expect(toolMessages.map((m) => m.message.content)).toContain("Tool 1 done");
            expect(toolMessages.map((m) => m.message.content)).toContain("Tool 2 done");

            // Verify tool_call_id is correct and unique for each tool
            const tool1Message = toolMessages.find((m) => m.message.content === "Tool 1 done");
            const tool2Message = toolMessages.find((m) => m.message.content === "Tool 2 done");
            expect(tool1Message?.message.tool_call_id).toBe("tool-call-parallel_tool_1-0");
            expect(tool2Message?.message.tool_call_id).toBe("tool-call-parallel_tool_2-1");

            // Verify each tool_call_id is unique
            const toolCallIds = toolMessages.map((m) => m.message.tool_call_id);
            expect(new Set(toolCallIds)).toHaveLength(toolCallIds.length);

            // Final response
            const responseEvent = events.find((e) => e.kind === "response");
            expect(responseEvent?.response.content).toBe("Parallel tools finished!");
        });

        test("should execute multiple tool calls sequentially when executeToolsInParallel is false", async () => {
            const executionOrder: string[] = [];

            const tool1: Tool = {
                name: "sequential_tool_1",
                description: "Sequential tool 1",
                input_schema: {
                    type: "object",
                    properties: { foo: { type: "string" } },
                },
                callback: vi.fn().mockImplementation(async () => {
                    executionOrder.push("tool1_start");
                    await Promise.resolve();
                    executionOrder.push("tool1_end");
                    return "Tool 1 done";
                }),
            };
            const tool2: Tool = {
                name: "sequential_tool_2",
                description: "Sequential tool 2",
                input_schema: {
                    type: "object",
                    properties: { bar: { type: "string" } },
                },
                callback: vi.fn().mockImplementation(async () => {
                    executionOrder.push("tool2_start");
                    await Promise.resolve();
                    executionOrder.push("tool2_end");
                    return "Tool 2 done";
                }),
            };

            const multiToolResponse = inflatePreRecordedCompletion({
                type: "tool-calls",
                toolsToCall: [
                    { tool: tool1, args: { foo: "abc" } },
                    { tool: tool2, args: { bar: "xyz" } },
                ],
            });
            const finalResponse = inflatePreRecordedCompletion({
                type: "stop-message",
                content: "Sequential tools finished!",
            });

            mockCreate
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockResolvedValue({
                        response: { headers: mockResponseHeaders },
                        data: multiToolResponse,
                    }),
                })
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockResolvedValue({
                        response: { headers: mockResponseHeaders },
                        data: finalResponse,
                    }),
                });

            const result = await getCompletionWithTools(
                client,
                "System",
                [{ role: "user", content: "Run sequential tools" }],
                [tool1, tool2],
                { executeToolsInParallel: false },
            );
            const events = result.events;

            expect(tool1.callback).toHaveBeenCalled();
            expect(tool2.callback).toHaveBeenCalled();
            // In sequential execution, tool1 must complete before tool2 starts
            expect(executionOrder).toEqual(["tool1_start", "tool1_end", "tool2_start", "tool2_end"]);

            // Both tool messages should be present
            const toolMessages = events.filter(
                (e) => e.kind === "message" && e.message.role === "tool",
            ) as ToolMessageEvent[];
            expect(toolMessages).toHaveLength(2);
            expect(toolMessages.map((m) => m.message.content)).toContain("Tool 1 done");
            expect(toolMessages.map((m) => m.message.content)).toContain("Tool 2 done");

            // Verify tool_call_id is correct and unique for each tool
            const tool1Message = toolMessages.find((m) => m.message.content === "Tool 1 done");
            const tool2Message = toolMessages.find((m) => m.message.content === "Tool 2 done");
            expect(tool1Message?.message.tool_call_id).toBe("tool-call-sequential_tool_1-0");
            expect(tool2Message?.message.tool_call_id).toBe("tool-call-sequential_tool_2-1");

            // Verify each tool_call_id is unique
            const toolCallIds = toolMessages.map((m) => m.message.tool_call_id);
            expect(new Set(toolCallIds)).toHaveLength(toolCallIds.length);

            // Final response
            const responseEvent = events.find((e) => e.kind === "response");
            expect(responseEvent?.response.content).toBe("Sequential tools finished!");
        });

        test("should execute tools sequentially by default when executeToolsInParallel is not specified", async () => {
            const executionOrder: string[] = [];

            const tool1: Tool = {
                name: "default_tool_1",
                description: "Default tool 1",
                input_schema: {
                    type: "object",
                    properties: { foo: { type: "string" } },
                },
                callback: vi.fn().mockImplementation(async () => {
                    executionOrder.push("tool1_start");
                    await Promise.resolve();
                    executionOrder.push("tool1_end");
                    return "Tool 1 done";
                }),
            };
            const tool2: Tool = {
                name: "default_tool_2",
                description: "Default tool 2",
                input_schema: {
                    type: "object",
                    properties: { bar: { type: "string" } },
                },
                callback: vi.fn().mockImplementation(async () => {
                    executionOrder.push("tool2_start");
                    await Promise.resolve();
                    executionOrder.push("tool2_end");
                    return "Tool 2 done";
                }),
            };

            const multiToolResponse = inflatePreRecordedCompletion({
                type: "tool-calls",
                toolsToCall: [
                    { tool: tool1, args: { foo: "abc" } },
                    { tool: tool2, args: { bar: "xyz" } },
                ],
            });
            const finalResponse = inflatePreRecordedCompletion({
                type: "stop-message",
                content: "Default tools finished!",
            });

            mockCreate
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockResolvedValue({
                        response: { headers: mockResponseHeaders },
                        data: multiToolResponse,
                    }),
                })
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockResolvedValue({
                        response: { headers: mockResponseHeaders },
                        data: finalResponse,
                    }),
                });

            // No executeToolsInParallel option specified - should default to sequential
            const result = await getCompletionWithTools(
                client,
                "System",
                [{ role: "user", content: "Run default tools" }],
                [tool1, tool2],
            );
            const events = result.events;

            expect(tool1.callback).toHaveBeenCalled();
            expect(tool2.callback).toHaveBeenCalled();
            // Should execute sequentially by default
            expect(executionOrder).toEqual(["tool1_start", "tool1_end", "tool2_start", "tool2_end"]);

            // Both tool messages should be present
            const toolMessages = events.filter(
                (e) => e.kind === "message" && e.message.role === "tool",
            ) as ToolMessageEvent[];
            expect(toolMessages).toHaveLength(2);

            // Verify tool_call_id is correct and unique for each tool
            const tool1Message = toolMessages.find((m) => m.message.content === "Tool 1 done");
            const tool2Message = toolMessages.find((m) => m.message.content === "Tool 2 done");
            expect(tool1Message?.message.tool_call_id).toBe("tool-call-default_tool_1-0");
            expect(tool2Message?.message.tool_call_id).toBe("tool-call-default_tool_2-1");

            // Verify each tool_call_id is unique
            const toolCallIds = toolMessages.map((m) => m.message.tool_call_id);
            expect(new Set(toolCallIds)).toHaveLength(toolCallIds.length);

            // Final response
            const responseEvent = events.find((e) => e.kind === "response");
            expect(responseEvent?.response.content).toBe("Default tools finished!");
        });

        test("should handle errors during parallel tool execution", async () => {
            const executionOrder: string[] = [];
            const error = new Error("Tool execution failed");

            const failingTool: Tool = {
                name: "failing_tool",
                description: "A tool that fails",
                input_schema: {
                    type: "object",
                    properties: { message: { type: "string" } },
                },
                callback: vi.fn().mockImplementation(async () => {
                    executionOrder.push("failing_tool_start");
                    await Promise.resolve();
                    executionOrder.push("failing_tool_error");
                    throw error;
                }),
            };

            const successTool: Tool = {
                name: "success_tool",
                description: "A tool that succeeds",
                input_schema: {
                    type: "object",
                    properties: { data: { type: "string" } },
                },
                callback: vi.fn().mockImplementation(async () => {
                    executionOrder.push("success_tool_start");
                    await Promise.resolve();
                    executionOrder.push("success_tool_end");
                    return "Tool executed successfully!";
                }),
            };

            const multiToolResponse = inflatePreRecordedCompletion({
                type: "tool-calls",
                toolsToCall: [
                    { tool: failingTool, args: { message: "This will fail" } },
                    { tool: successTool, args: { data: "This will succeed" } },
                ],
            });

            const finalResponse = inflatePreRecordedCompletion({
                type: "stop-message",
                content: "Handled tool execution results!",
            });

            mockCreate
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockResolvedValue({
                        response: { headers: mockResponseHeaders },
                        data: multiToolResponse,
                    }),
                })
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockResolvedValue({
                        response: { headers: mockResponseHeaders },
                        data: finalResponse,
                    }),
                });

            const result = await getCompletionWithTools(
                client,
                "System",
                [{ role: "user", content: "Use both tools in parallel" }],
                [failingTool, successTool],
                { executeToolsInParallel: true },
            );
            const events = result.events;

            expect(failingTool.callback).toHaveBeenCalled();
            expect(successTool.callback).toHaveBeenCalled();

            // Both tools should have started (parallel execution)
            expect(executionOrder).toContain("failing_tool_start");
            expect(executionOrder).toContain("success_tool_start");
            expect(executionOrder).toContain("failing_tool_error");
            expect(executionOrder).toContain("success_tool_end");

            // Both tool messages should be present
            const toolMessages = events.filter(
                (e) => e.kind === "message" && e.message.role === "tool",
            ) as ToolMessageEvent[];
            expect(toolMessages).toHaveLength(2);

            // Find the error message
            const errorMessage = toolMessages.find(
                (m) => typeof m.message.content === "string" && m.message.content.includes("Failed to execute"),
            );
            expect(errorMessage).toBeDefined();
            expect(errorMessage?.message.content).toContain("failing_tool");
            expect(errorMessage?.message.content).toContain("Tool execution failed");
            expect(errorMessage?.message.tool_call_id).toBe("tool-call-failing_tool-0");

            // Find the success message
            const successMessage = toolMessages.find((m) => m.message.content === "Tool executed successfully!");
            expect(successMessage).toBeDefined();
            expect(successMessage?.message.tool_call_id).toBe("tool-call-success_tool-1");

            // Verify each tool_call_id is unique
            const toolCallIds = toolMessages.map((m) => m.message.tool_call_id);
            expect(new Set(toolCallIds)).toHaveLength(toolCallIds.length);

            // Final response should be received
            const responseEvent = events.find((e) => e.kind === "response");
            expect(responseEvent?.response.content).toBe("Handled tool execution results!");
        });

        test("should handle single tool call with parallel execution enabled", async () => {
            const testTool: Tool = {
                name: "single_tool",
                description: "A single test tool",
                input_schema: {
                    type: "object",
                    properties: { message: { type: "string" } },
                },
                callback: vi.fn().mockResolvedValue("Single tool executed!"),
            };

            const singleToolResponse = inflatePreRecordedCompletion({
                type: "tool-calls",
                toolsToCall: [{ tool: testTool, args: { message: "Single call" } }],
            });

            const finalResponse = inflatePreRecordedCompletion({
                type: "stop-message",
                content: "Used single tool!",
            });

            mockCreate
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockResolvedValue({
                        response: { headers: mockResponseHeaders },
                        data: singleToolResponse,
                    }),
                })
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockResolvedValue({
                        response: { headers: mockResponseHeaders },
                        data: finalResponse,
                    }),
                });

            const result = await getCompletionWithTools(
                client,
                "System",
                [{ role: "user", content: "Use single tool" }],
                [testTool],
                { executeToolsInParallel: true },
            );
            const events = result.events;

            const toolMessages = events.filter((e) => e.kind === "message" && e.message.role === "tool");
            expect(toolMessages).toHaveLength(1);
            expect(testTool.callback).toHaveBeenCalledWith({ message: "Single call" }, expect.any(Object));

            // Verify tool_call_id is correct
            const toolMessage = toolMessages[0] as ToolMessageEvent;
            expect(toolMessage.message.tool_call_id).toBe("tool-call-single_tool-0");

            // Final response should be received
            const responseEvent = events.find((e) => e.kind === "response");
            expect(responseEvent?.response.content).toBe("Used single tool!");
        });
    });

    describe("optional max_prompt_tokens", () => {
        test("should fallback to max_context_window_tokens when max_prompt_tokens is undefined", async () => {
            // Arrange - Create a model without max_prompt_tokens
            const modelWithoutPromptTokens: Model = {
                id: "test-model-no-prompt-tokens",
                name: "test-model-no-prompt-tokens",
                capabilities: {
                    supports: { vision: false },
                    limits: {
                        // max_prompt_tokens is undefined (optional)
                        max_context_window_tokens: 16000,
                    },
                },
            };

            mockListModels.mockResolvedValueOnce([modelWithoutPromptTokens]);

            const clientWithoutPromptTokens = new ChatCompletionClient(mockProvider, settings, logger, {
                model: "test-model-no-prompt-tokens",
                retryPolicy: {
                    maxRetries: 3,
                },
            });

            const simpleResponse = inflatePreRecordedCompletion({
                type: "stop-message",
                content: "Response using fallback token limit!",
            });

            mockCreate.mockReturnValueOnce({
                withResponse: vi.fn().mockResolvedValue({
                    response: { headers: mockResponseHeaders },
                    data: simpleResponse,
                    request_id: "req-fallback-test",
                }),
            });

            // Act
            const result = await getCompletionWithTools(
                clientWithoutPromptTokens,
                "You are a helpful assistant.",
                [{ role: "user", content: "Test fallback" }],
                [],
            );
            const events = result.events;

            // Assert
            expect(events.length).toBeGreaterThan(0);
            const responseEvent = events.find((e) => e.kind === "response");
            expect(responseEvent?.response.content).toBe("Response using fallback token limit!");
            expect(mockCreate).toHaveBeenCalledTimes(1);
        });

        test("should fallback to max_context_window_tokens when max_prompt_tokens is 0", async () => {
            // Arrange - Create a model with max_prompt_tokens set to 0
            const modelWithZeroPromptTokens: Model = {
                id: "test-model-zero-prompt-tokens",
                name: "test-model-zero-prompt-tokens",
                capabilities: {
                    supports: { vision: false },
                    limits: {
                        max_prompt_tokens: 0, // Explicitly set to 0
                        max_context_window_tokens: 16000,
                    },
                },
            };

            mockListModels.mockResolvedValueOnce([modelWithZeroPromptTokens]);

            const clientWithZeroPromptTokens = new ChatCompletionClient(mockProvider, settings, logger, {
                model: "test-model-zero-prompt-tokens",
                retryPolicy: {
                    maxRetries: 3,
                },
            });

            const simpleResponse = inflatePreRecordedCompletion({
                type: "stop-message",
                content: "Response using fallback from zero token limit!",
            });

            mockCreate.mockReturnValueOnce({
                withResponse: vi.fn().mockResolvedValue({
                    response: { headers: mockResponseHeaders },
                    data: simpleResponse,
                    request_id: "req-zero-fallback-test",
                }),
            });

            // Act
            const result = await getCompletionWithTools(
                clientWithZeroPromptTokens,
                "You are a helpful assistant.",
                [{ role: "user", content: "Test zero fallback" }],
                [],
            );
            const events = result.events;

            // Assert
            expect(events.length).toBeGreaterThan(0);
            const responseEvent = events.find((e) => e.kind === "response");
            expect(responseEvent?.response.content).toBe("Response using fallback from zero token limit!");
            expect(mockCreate).toHaveBeenCalledTimes(1);
        });
    });

    describe("turn counting", () => {
        test("should return correct turn count", async () => {
            // Arrange
            const mockResponse = inflatePreRecordedCompletion({
                type: "stop-message",
                content: "Hello, this is a test response!",
            });

            mockCreate.mockReturnValue({
                withResponse: vi.fn().mockResolvedValue({
                    response: { headers: mockResponseHeaders },
                    data: mockResponse,
                    request_id: "req-test",
                }),
            });

            // Act
            const result = await getCompletionWithTools(client, "You are a helpful assistant.", [
                { role: "user", content: "Hello!" },
            ]);
            const finalTurnCount = result.finalTurnCount;

            // Assert
            expect(finalTurnCount).toBe(0);
        });

        test("should correctly resume turn count", async () => {
            const mockResponse = inflatePreRecordedCompletion({
                type: "stop-message",
                content: "Hello, this is a test response!",
            });

            mockCreate.mockReturnValue({
                withResponse: vi.fn().mockResolvedValue({
                    response: { headers: mockResponseHeaders },
                    data: mockResponse,
                    request_id: "req-test",
                }),
            });

            const resultOne = await getCompletionWithTools(client, "You are a helpful assistant.", [
                { role: "user", content: "Hello!" },
            ]);
            const eventsOne = resultOne.events;
            const finalTurnCountOne = resultOne.finalTurnCount;

            const responseEvent = eventsOne.find((e) => e.kind === "response");
            expect(responseEvent?.response.content).toBe(mockResponse.choices[0].message.content);
            expect(finalTurnCountOne).toBe(0);

            const resultTwo = await getCompletionWithTools(
                client,
                "You are a helpful assistant.",
                [{ role: "user", content: "Hello!" }, ...(responseEvent?.response ? [responseEvent.response] : [])],
                undefined,
                { initialTurnCount: finalTurnCountOne },
            );
            const eventsTwo = resultTwo.events;
            const finalTurnCountTwo = resultTwo.finalTurnCount;

            const responseEventTwo = eventsTwo.find((e) => e.kind === "response");
            expect(responseEventTwo?.response.content).toBe(mockResponse.choices[0].message.content);
            expect(finalTurnCountTwo).toBe(1);
        });
    });

    describe(`token counting`, () => {
        test("truncates non-user messages to fit within token limits", async () => {
            // Arrange - Create a model with a very low token limit to force truncation
            const lowLimitModel: Model = {
                id: "test-model-low-limit",
                name: "test-model-low-limit",
                capabilities: {
                    supports: { vision: false },
                    limits: {
                        max_prompt_tokens: 10, // Very low limit to force truncation
                        max_context_window_tokens: 50,
                    },
                },
            };

            mockListModels.mockResolvedValue([lowLimitModel]);

            // Use a fresh client to it grabs the mock model
            const clientWithLowLimitModel = new ChatCompletionClient(mockProvider, settings, logger, {
                model: "test-model-low-limit",
                retryPolicy: {
                    maxRetries: 3,
                },
            });

            // Mock a successful response after truncation
            mockCreate.mockReturnValue(
                createMockCreateReturnValue(
                    inflatePreRecordedCompletion({
                        type: "stop-message",
                        content: "Response after truncation!",
                    }),
                    "req-truncation-test",
                ),
            );

            // Create messages that will exceed the token limit
            const initialMessages: ChatCompletionMessageParam[] = [
                {
                    role: "user",
                    content: "This is the first user message that should be preserved.",
                },
                {
                    role: "assistant",
                    content:
                        "This is an assistant response that should be removed when truncating to save tokens. This message is long to increase token count.",
                },
                {
                    role: "user",
                    content: "This is another user message that should always be preserved.",
                },
                {
                    role: "assistant",
                    content:
                        "This is another assistant response that should be removed when truncating. This is also long to increase tokens.",
                },
                {
                    role: "user",
                    content: "This is the final user message that should be preserved.",
                },
            ];

            // Act
            const result = await getCompletionWithTools(
                clientWithLowLimitModel,
                "You are a helpful assistant.",
                initialMessages,
                [],
                {
                    processors: { preRequest: [truncator] },
                },
            );
            const events = result.events;

            // Assert
            expect(events.length).toBeGreaterThan(0);
            const responseEvent = events.find((e) => e.kind === "response");
            expect(responseEvent?.response.content).toBe("Response after truncation!");

            // Verify that the chat completion client performed truncation
            expect(mockCreate).toHaveBeenCalledTimes(1);
            const callArgs = mockCreate.mock.calls[0][0];
            expect(callArgs.messages).toBeDefined();

            // The client should have truncated some messages to fit within token limits
            // User and system messages should be preserved, but some assistant messages may be removed
            const sentMessages = callArgs.messages;
            const userMessages = sentMessages.filter((m: ChatCompletionMessageParam) => m.role === "user");
            const systemMessages = sentMessages.filter((m: ChatCompletionMessageParam) => m.role === "system");
            const assistantMessages = sentMessages.filter((m: ChatCompletionMessageParam) => m.role === "assistant");

            // All user messages should be preserved
            expect(userMessages.length).toBe(3);
            // System message should be preserved
            expect(systemMessages.length).toBe(1);
            // Assistant messages should be truncated
            expect(assistantMessages.length).toBeLessThan(initialMessages.filter((m) => m.role === "assistant").length);
        });

        test("adjusts token limit if token limit error indicates a new limit", async () => {
            // Arrange - Create a model with a very low token limit to force truncation
            const specificLimitModel: Model = {
                id: "test-model-specific-limit",
                name: "test-model-specific-limit",
                capabilities: {
                    supports: { vision: false },
                    limits: {
                        max_prompt_tokens: 4000,
                        max_context_window_tokens: 6000,
                    },
                },
            };

            mockListModels.mockResolvedValue([specificLimitModel]);

            // Use a fresh client to it grabs the mock model
            const clientWithSpecificLimitModel = new ChatCompletionClient(mockProvider, settings, logger, {
                model: "test-model-specific-limit",
                retryPolicy: {
                    maxRetries: 3,
                },
            });

            // Create a mock error indicating a LOWER token limit than the model's initial limit
            // The error says the limit is 3000, but our model was supposed to have 4000
            const tokenLimitError = createMockAPIError(
                400,
                "prompt token count of 3500 exceeds the limit of 3000",
                "req-token-limit",
            );

            // Mock the first call to fail with token limit error, second to succeed
            mockCreate
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockRejectedValue(tokenLimitError),
                })
                .mockReturnValueOnce(
                    createMockCreateReturnValue(
                        inflatePreRecordedCompletion({
                            type: "stop-message",
                            content: "Success after token limit adjustment!",
                        }),
                        "req-token-limit-success",
                    ),
                );

            // Create messages that will be long enough to trigger the token limit logic
            const longMessage =
                "This is a long message that will help demonstrate token limit adjustment behavior. ".repeat(20);
            const testMessages: ChatCompletionMessageParam[] = [
                { role: "user", content: longMessage },
                { role: "assistant", content: "Previous response" },
                {
                    role: "user",
                    content: "Test message that causes token limit error",
                },
            ];

            // Act
            const result = await getCompletionWithTools(
                clientWithSpecificLimitModel,
                "You are a helpful assistant.",
                testMessages,
                [],
                {
                    processors: { preRequest: [truncator] },
                },
            );
            const events = result.events;

            // Assert - Verify the operation succeeded after retry
            expect(events.length).toBeGreaterThan(0);
            const responseEvent = events.find((e) => e.kind === "response");
            expect(responseEvent?.response.content).toBe("Success after token limit adjustment!");

            // Verify that we made two API calls - first failed, second succeeded
            expect(mockCreate).toHaveBeenCalledTimes(2);

            // Verify that a model call failure event was generated for the first failed attempt
            const failureEvent = events.find((e) => e.kind === "model_call_failure");
            expect(failureEvent).toBeDefined();
            expect(failureEvent?.modelCall?.status).toBe(400);
            expect(failureEvent?.modelCall?.error).toContain("prompt token count of 3500 exceeds the limit of 3000");

            // Verify that truncateContextWindow was called with the correct token limits
            expect(truncatorPreRequestSpy).toHaveBeenCalledTimes(2);

            // First call should use the model's initial token limit of 4000
            const firstCall = truncatorPreRequestSpy.mock.calls[0];
            expect(firstCall[0].tokenLimit).toBe(4000);

            // Second call should use the adjusted token limit of 3000 (from the error message)
            const secondCall = truncatorPreRequestSpy.mock.calls[1];
            expect(secondCall[0].tokenLimit).toBe(3000);
        });

        test("adjusts token buffer if token limit error indicates the same limit", async () => {
            // Arrange - Mock the model to have a specific token limit
            const specificLimitModel: Model = {
                id: "test-model-specific-limit",
                name: "test-model-specific-limit",
                capabilities: {
                    supports: { vision: false },
                    limits: {
                        max_prompt_tokens: 50000,
                        max_context_window_tokens: 50000,
                    },
                },
            };

            mockListModels.mockResolvedValue([specificLimitModel]);

            // Use a fresh client to it grabs the mock model
            const clientWithSpecificLimitModel = new ChatCompletionClient(mockProvider, settings, logger, {
                model: "test-model-specific-limit",
                retryPolicy: {
                    maxRetries: 3,
                },
            });

            // Create an error indicating the same limit as the model definition
            const sameTokenLimitError = createMockAPIError(
                400,
                "prompt token count of 48000 exceeds the limit of 50000",
                "req-token-buffer",
            );

            // Mock the first call to fail with token limit error, second to succeed
            mockCreate
                .mockReturnValueOnce({
                    withResponse: vi.fn().mockRejectedValue(sameTokenLimitError),
                })
                .mockReturnValueOnce(
                    createMockCreateReturnValue(
                        inflatePreRecordedCompletion({
                            type: "stop-message",
                            content: "Success after buffer adjustment!",
                        }),
                        "req-buffer-success",
                    ),
                );

            // Act
            const result = await getCompletionWithTools(
                clientWithSpecificLimitModel,
                "You are a helpful assistant.",
                [
                    {
                        role: "user",
                        content: "Test message that causes token buffer adjustment",
                    },
                ],
                [],
                {
                    processors: { preRequest: [truncator] },
                },
            );
            const events = result.events;

            // Assert
            expect(events.length).toBeGreaterThan(0);
            const responseEvent = events.find((e) => e.kind === "response");
            expect(responseEvent?.response.content).toBe("Success after buffer adjustment!");
            expect(mockCreate).toHaveBeenCalledTimes(2);

            // Verify that the chat completion client adjusted its token buffer and retried
            const secondCallArgs = mockCreate.mock.calls[1][0];
            expect(secondCallArgs.messages).toBeDefined();

            // Both calls should have been made (first failed, second succeeded)
            const firstCallArgs = mockCreate.mock.calls[0][0];
            expect(secondCallArgs).toBeDefined();
            expect(firstCallArgs).toBeDefined();

            // Verify that truncateContextWindow was called with the correct token limits
            expect(truncatorPreRequestSpy).toHaveBeenCalledTimes(2);

            // First call should use the model's initial token limit of 50000
            const firstCall = truncatorPreRequestSpy.mock.calls[0];
            expect(firstCall[0].tokenLimit).toBe(50000);

            // Second call should use something less since the buffer should have been adjusted
            const secondCall = truncatorPreRequestSpy.mock.calls[1];
            expect(secondCall[0].tokenLimit).toBeLessThan(50000);
        });

        test("reaches max token limit buffer", async () => {
            // Arrange - Mock the model to have a specific token limit
            const specificLimitModel: Model = {
                id: "test-model-specific-limit",
                name: "test-model-specific-limit",
                capabilities: {
                    supports: { vision: false },
                    limits: {
                        max_prompt_tokens: 50000,
                        max_context_window_tokens: 50000,
                    },
                },
            };

            mockListModels.mockResolvedValue([specificLimitModel]);

            // Use a fresh client to it grabs the mock model
            const clientWithSpecificLimitModel = new ChatCompletionClient(mockProvider, settings, logger, {
                model: "test-model-specific-limit",
                retryPolicy: {
                    maxRetries: 5,
                },
            });

            // Create an error indicating the same limit as the model definition
            const sameTokenLimitError = createMockAPIError(
                400,
                "prompt token count of 48000 exceeds the limit of 50000",
                "req-token-buffer",
            );

            // Keep returning token limit error no matter what
            mockCreate.mockReturnValue({
                withResponse: vi.fn().mockRejectedValue(sameTokenLimitError),
            });

            // Act
            const result = await getCompletionWithTools(
                clientWithSpecificLimitModel,
                "You are a helpful assistant.",
                [
                    {
                        role: "user",
                        content: "Test message that causes token buffer adjustment",
                    },
                ],
                [],
                {
                    processors: { preRequest: [truncator] },
                },
            );
            const events = result.events;

            // Assert
            expect(events.length).toBeGreaterThan(0);

            // Final call should have used a token limit of half the model's limit
            const finalCall = truncatorPreRequestSpy.mock.calls[truncatorPreRequestSpy.mock.calls.length - 1];
            expect(Math.floor(finalCall[0].tokenLimit)).toBe(25000);
        });
    });

    test("should always emit session_log event even when tool result sessionLog is null", async () => {
        // Arrange
        const testTool: Tool = {
            name: "test_tool_with_null_session_log",
            description: "A test tool that returns null sessionLog",
            input_schema: {
                type: "object",
                properties: {
                    message: { type: "string" },
                },
            },
            callback: vi.fn().mockResolvedValue({
                textResultForLlm: "Tool executed successfully!",
                resultType: "success",
                sessionLog: null, // This is the key test case - null sessionLog
                toolTelemetry: {},
            } as unknown as ToolResultExpanded),
        };

        // First response: tool call
        const toolCallResponse = createMockToolCallResponse(
            "call_123",
            "test_tool_with_null_session_log",
            '{"message": "Hello from tool!"}',
        );

        // Second response: final response after tool execution
        const finalResponse = inflatePreRecordedCompletion({
            type: "stop-message",
            content: "I used the tool and got the result!",
        });

        mockCreate
            .mockReturnValueOnce({
                withResponse: vi.fn().mockResolvedValue({
                    response: { headers: mockResponseHeaders },
                    data: toolCallResponse,
                    request_id: "req-tool-call",
                }),
            })
            .mockReturnValueOnce({
                withResponse: vi.fn().mockResolvedValue({
                    response: { headers: mockResponseHeaders },
                    data: finalResponse,
                    request_id: "req-final",
                }),
            });

        // Act
        const result = await getCompletionWithTools(
            client,
            "You are a helpful assistant.",
            [{ role: "user", content: "Use the test tool" }],
            [testTool],
        );
        const events = result.events;

        // Assert
        expect(events.length).toBeGreaterThan(4); // At least: session_log, message (assistant), message (tool), session_log, response, telemetry

        // Check that the tool callback was called with correct arguments
        expect(testTool.callback).toHaveBeenCalledWith({ message: "Hello from tool!" }, expect.any(Object));

        // Check that we got the tool response message
        const toolMessage = events.find((e) => e.kind === "message" && e.message.role === "tool") as
            | ToolMessageEvent
            | undefined;
        expect(toolMessage).toBeDefined();
        expect(toolMessage?.message.content).toBe("Tool executed successfully!");

        // Check that the final response was received
        const responseEvent = events.find((e) => e.kind === "response");
        expect(responseEvent?.response.content).toBe("I used the tool and got the result!");
    });
});
