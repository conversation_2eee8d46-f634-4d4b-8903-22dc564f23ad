/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import { describe, expect, test } from "vitest";
import { AIClient as CAPIAIClient } from "../../src/model/capi/ai";
import { ConsoleLogger } from "../../src/runner/logger/console";

describe("ai-tests", async () => {
    test.each([
        {
            settings: {
                api: {
                    copilot: {
                        token: "token",
                        integrationId: "integrationId",
                        sessionId: "sessionId",
                    },
                },
            },
            expectedHeaderValue: "sessionId",
        },
        {
            settings: {
                api: {
                    copilot: {
                        token: "token",
                        integrationId: "integrationId",
                    },
                },
                service: {
                    instance: {
                        id: "jobId",
                    },
                },
            },
            expectedHeaderValue: "jobId",
        },
    ])(
        "should use agent session id as x-interaction-id header",
        { timeout: 10000 },
        async ({ settings, expectedHeaderValue }) => {
            let didReceiveRequest = false;

            global.fetch = vi.fn((_: string | URL | globalThis.Request, init?: RequestInit | undefined) => {
                didReceiveRequest = true;

                expect(init?.headers).toBeDefined();
                expect(typeof init?.headers).toBe("object");
                expect(init?.headers as Record<string, string>).toHaveProperty("X-Interaction-Id");
                expect((init?.headers as Record<string, string>)["X-Interaction-Id"]).toEqual(expectedHeaderValue);

                // Mock the first API call in getCompletionWithTools (getModel)
                return Promise.resolve(new Response(JSON.stringify({ data: [] })));
            });

            try {
                const client = new CAPIAIClient(settings, new ConsoleLogger(), {
                    retryPolicy: { maxRetries: 0 },
                });
                const result = await client.getCompletionWithTools(
                    "You are a helpful assistant.",
                    [
                        {
                            role: "user",
                            content: "Tell me a brief history of Padawan, not the one in Star Wars.",
                        },
                    ],
                    [],
                );
                for await (const _ of result) {
                    // Intentionally empty - we just need to consume the async iterator to so we can assert things
                    // about the underling request.
                }
            } catch {
                // Ignore Errors, we just want to assert the headers
            }

            expect(didReceiveRequest).toBeTruthy();
        },
    );
});
