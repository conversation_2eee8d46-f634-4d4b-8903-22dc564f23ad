/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import {
    ChatCompletionAssistantMessageParam,
    ChatCompletionMessageParam,
    ChatCompletionMessageToolCall,
    ChatCompletionSystemMessageParam,
    ChatCompletionToolMessageParam,
    ChatCompletionUserMessageParam,
} from "openai/resources";
import { v4 as uuidv4 } from "uuid";
import { AIClient as CAPIAIClient } from "../../src/model/capi/ai";
import { truncateContextWindow } from "../../src/model/capi/truncateContextWindow";
import { ConsoleLogger } from "../../src/runner/logger/console";
import { getOrInitSettings } from "../../src/settings/factory";

const DEFAULT_TIMEOUT_MS = 10 * 60 * 2000;

describe.runIf(process.env.NIGHTLY_TEST_CI === "true")("capi-tests", () => {
    // add an entry for every character in the control characters range
    // 0x00-0x08, 0x0E-0x1F, and 0x7F-0x9F in the prompts below as '<char> helloworld'
    // /[\u0000-\u0008\u000E-\u001F\u007F-\u009F]/g
    function generateControlCharacters(): string[] {
        const start = 0x00;
        const end = 0x009f;

        const characters: string[] = [];

        for (let code = start; code <= end; code++) {
            const char = String.fromCharCode(code);
            // eslint-disable-next-line no-control-regex
            const isInRange = /[\u0000-\u0008\u000E-\u001F\u007F-\u009F]/g.test(char);
            if (isInRange) {
                // Add the character and a sample string
                characters.push(`${String.fromCharCode(code)} helloworld`);
            }
        }

        return characters;
    }

    test(
        "unicode-control-characters",
        async () => {
            const settings = await getOrInitSettings();
            const logger = new ConsoleLogger();
            const client = new CAPIAIClient(settings, logger);
            const controlCharacters = generateControlCharacters();
            const prompt = controlCharacters.join("\n");
            const result = await client.getCompletionWithTools(
                "You are a helpful assistant. Echo back the prompt you receive.",
                [{ role: "user", content: prompt }],
                [],
            );

            const validateResponse = (content: string) => {
                const helloWorldCount = (content.match(/helloworld/g) || []).length;
                expect(helloWorldCount).toBe(controlCharacters.length);
                console.log(content);
            };

            // no 500s or 400s, we should get the "helloworld" back
            let receivedHelloWorld = false;
            for await (const event of result) {
                if (event.kind === "response" && event.response.content) {
                    validateResponse(event.response.content);
                    receivedHelloWorld = true;
                } else if (
                    event.kind === "message" &&
                    event.message.content &&
                    typeof event.message.content === "string"
                ) {
                    validateResponse(event.message.content);
                    receivedHelloWorld = true;
                } else {
                    console.log(JSON.stringify(event));
                }
            }
            expect(receivedHelloWorld).toBe(true);
        },
        DEFAULT_TIMEOUT_MS,
    );
});

describe.skipIf(process.env.DISABLE_CAPI_TESTS === "true")("truncate-context-window-tests", () => {
    const loremIpsum =
        "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.";

    const userMessage: ChatCompletionUserMessageParam = {
        role: "user",
        content: loremIpsum,
    };

    const systemMessage: ChatCompletionSystemMessageParam = {
        role: "system",
        content: loremIpsum,
    };

    const assistantMessageWithoutToolCall: ChatCompletionAssistantMessageParam = {
        role: "assistant",
        content: loremIpsum,
    };

    const assistantMessageWithToolCalls: ChatCompletionAssistantMessageParam = {
        role: "assistant",
        content: loremIpsum,
        tool_calls: [
            { id: 1 } as unknown as ChatCompletionMessageToolCall,
            { id: 2 } as unknown as ChatCompletionMessageToolCall,
            { id: 3 } as unknown as ChatCompletionMessageToolCall,
        ],
    };

    const toolResponseMessages = assistantMessageWithToolCalls
        .tool_calls!.map((toolCall) => toolCall.id)
        .map(
            (id) =>
                ({
                    content: loremIpsum,
                    role: "tool",
                    tool_call_id: id,
                }) satisfies ChatCompletionToolMessageParam,
        );

    test("should not truncate when not necessary", async () => {
        const messages: ChatCompletionMessageParam[] = [
            systemMessage,
            userMessage,
            assistantMessageWithoutToolCall,
            userMessage,
        ];
        const originalMessagesLength = messages.length;

        const truncateContextWindowResult = await truncateContextWindow(
            messages,
            [],
            "just use whatever is default token counting model",
            100_000,
        );

        expect(messages.length).toBe(originalMessagesLength);
        expect(truncateContextWindowResult.tokensRemovedDuringTruncation).toBe(0);
    });

    test("should not remove system message or user messages when truncating", async () => {
        const messages: ChatCompletionMessageParam[] = [
            systemMessage,
            userMessage,
            assistantMessageWithoutToolCall,
            userMessage,
        ];

        const truncateContextWindowResult = await truncateContextWindow(
            messages,
            [],
            "just use whatever is default token counting model",
            1,
        );

        expect(messages.length).toBe(3);
        expect(truncateContextWindowResult.tokensRemovedDuringTruncation).toBeGreaterThan(0);
    });

    test("should not remove tool response messages without a corresponding assistant message with the tool call", async () => {
        const messages: ChatCompletionMessageParam[] = [systemMessage, userMessage, ...toolResponseMessages];
        const originalMessagesLength = messages.length;

        const truncateContextWindowResult = await truncateContextWindow(
            messages,
            [],
            "just use whatever is default token counting model",
            10,
        );

        expect(messages.length).toBe(originalMessagesLength);
        expect(truncateContextWindowResult.tokensRemovedDuringTruncation).toBe(0);
    });

    test("should remove tool response messages with a corresponding assistant message with the tool call", async () => {
        const messages: ChatCompletionMessageParam[] = [
            systemMessage,
            userMessage,
            assistantMessageWithToolCalls,
            ...toolResponseMessages,
        ];

        const truncateContextWindowResult = await truncateContextWindow(
            messages,
            [],
            "just use whatever is default token counting model",
            10,
        );

        expect(messages.length).toBe(2);
        expect(truncateContextWindowResult.tokensRemovedDuringTruncation).toBeGreaterThan(0);
    });
});

describe.skipIf(process.env.DISABLE_CAPI_TESTS === "true")("images", () => {
    beforeEach(() => {
        // Mock is already set up
    });

    afterEach(() => {
        vi.restoreAllMocks();
    });

    test("model supports images", { timeout: DEFAULT_TIMEOUT_MS }, async () => {
        const logger = new ConsoleLogger();
        process.env.GITHUB_USER_ID = "1885174";
        const settings = await getOrInitSettings();

        settings.featureFlags = {};
        settings.featureFlags["copilot_swe_agent_vision"] = true;
        settings.service ||= {};
        settings.service.instance ||= {};
        settings.service.instance.id ||= `sweagent-tests-${uuidv4()}`;
        const client = new CAPIAIClient(settings, logger, {
            model: "claude-sonnet-4",
        });

        const result = await client.getCompletionWithTools(
            "You are a helpful assistant.",
            [
                { role: "user", content: "What text is in this image?" },
                {
                    role: "user",
                    content: [
                        {
                            type: "image_url",
                            image_url: {
                                url: "https://github.com/user-attachments/assets/9300859f-46c9-4d16-80b0-c9fd0f38adb3",
                            },
                        },
                    ],
                },
            ],
            [],
        );

        for await (const event of result) {
            console.log(event);
            if (event.kind == "response") {
                expect(event.response.content?.toLowerCase()).toContain("hello");
                return;
            }
        }

        expect.fail("Expected a response");
    });
});

describe.skipIf(process.env.DISABLE_CAPI_TESTS === "true")("record failed model calls", () => {
    test("should record failed model calls", { timeout: DEFAULT_TIMEOUT_MS }, async () => {
        const logger = new ConsoleLogger();
        process.env.GITHUB_USER_ID = "1885174";
        const settings = await getOrInitSettings();

        settings.featureFlags = {};
        settings.featureFlags["copilot_swe_agent_vision"] = true;
        settings.service ||= {};
        settings.service.instance ||= {};
        settings.service.instance.id ||= `sweagent-tests-${uuidv4()}`;
        const client = new CAPIAIClient(settings, logger, {
            model: "claude-sonnet-4",
        });

        const result = await client.getCompletionWithTools(
            "You are a helpful assistant.",
            [
                {
                    role: "user",
                    content: "What text is in this image?",
                },
                {
                    role: "user",
                    content: [
                        {
                            type: "image_url",
                            image_url: {
                                // URL doesn't exist, should trigger a failure
                                url: "https://github.com/user-attachments/assets/9300859f-46c9-4d16-80b0-c9fd0f38ad",
                            },
                        },
                    ],
                },
            ],
            [],
        );

        for await (const event of result) {
            console.log(event);
            if (event.kind === "model_call_failure") {
                expect(event.modelCall.request_id).toBeDefined();
                expect(event.modelCall.status).toBeDefined();
                expect(event.modelCall.error).toBeDefined();
                return;
            }
        }

        expect.fail("Expected a response");
    });
});
