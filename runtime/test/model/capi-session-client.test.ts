/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { APIError } from "openai";
import { ChatCompletionChunk } from "openai/resources";
import { test } from "vitest";
import * as ghFetchHelpers from "../../src/helpers/gh-fetch-helpers";
import { SecretFilter } from "../../src/helpers/SecretFilter";
import { CAPIError } from "../../src/model/capi/copilot-client";
import { createCopilotSessionsClient, sessionErrorMessages } from "../../src/model/capi/sessions-client";
import { RunnerLogger } from "../../src/runner";
import { ConsoleLogger } from "../../src/runner/logger/console";
import { RuntimeSettings } from "../../src/settings";
import { secretEnvVarNames } from "../../src/settings/environment-settings";

function injectSecretEnvVarKeysIntoSecretFilterViaRunner(keys: string[]) {
    SecretFilter.getInstance().setRunner({
        type: "GITHUB_ACTIONS",
        prepareEnvironment: async () => {
            /* no-op */
        },
        exec: undefined!,
        git: undefined!,
        logger: new ConsoleLogger(),
        get sensitiveKeys() {
            return keys;
        },
        decoder: undefined!,
    });
}

function resetSecretFilterRunner() {
    SecretFilter.getInstance().setRunner({
        type: "GITHUB_ACTIONS",
        prepareEnvironment: async () => {
            /* no-op */
        },
        exec: undefined!,
        git: undefined!,
        logger: new ConsoleLogger(),
        get sensitiveKeys() {
            return [];
        },
        decoder: undefined!,
    });
}

describe("CAPI session client", () => {
    const settings: RuntimeSettings = {
        api: {
            copilot: {
                useSessions: true,
                token: "token",
                integrationId: "integrationId",
                sessionId: "sessionId",
            },
        },
    };

    afterEach(() => {
        resetSecretFilterRunner();
    });

    describe("log", async () => {
        test("should send logs to the CAPI endpoint", async () => {
            const client = await createCopilotSessionsClient(settings, new ConsoleLogger());

            let didReceiveRequest = false;

            global.fetch = vi.fn((input: string | URL | globalThis.Request, init?: RequestInit | undefined) => {
                didReceiveRequest = true;

                expect(input).toBe("https://api.githubcopilot.com/agents/sessions/sessionId/logs");
                expect(init?.method).toBe("PUT");
                expect(init?.headers).toEqual({
                    "Copilot-Integration-Id": settings.api?.copilot?.integrationId,
                    Authorization: `Bearer ${settings.api?.copilot?.token}`,
                });

                const expectedContent = "hello";
                const body = JSON.parse((init?.body as string).slice(5) ?? "{}");
                expect(body.choices[0].delta.content).toBe(expectedContent);

                return Promise.resolve(new Response("{}"));
            });

            await client.log({
                created: 0,
                id: "id",
                model: "model",
                object: "chat.completion.chunk",
                choices: [
                    {
                        delta: {
                            content: "hello",
                        },
                        index: 0,
                        finish_reason: null,
                    },
                ],
            });

            expect(didReceiveRequest).toBeTruthy();
            expect(global.fetch).toHaveBeenCalledTimes(1);
        });

        test("should redact the values of the provided sensitiveKeys", async () => {
            injectSecretEnvVarKeysIntoSecretFilterViaRunner([...secretEnvVarNames, "INJECTED_1", "INJECTED_2"]);
            const client = await createCopilotSessionsClient(settings, new ConsoleLogger());

            let didReceiveRequest = false;

            // Add in some secrets to the environment
            vi.stubEnv(secretEnvVarNames[0], "KNOWN_1");
            vi.stubEnv("INJECTED_1", "INJECTED_1");
            vi.stubEnv("INJECTED_2", "INJECTED_2");

            global.fetch = vi.fn((input: string | URL | globalThis.Request, init?: RequestInit | undefined) => {
                didReceiveRequest = true;

                expect(input).toBe("https://api.githubcopilot.com/agents/sessions/sessionId/logs");
                expect(init?.method).toBe("PUT");
                expect(init?.headers).toEqual({
                    "Copilot-Integration-Id": settings.api?.copilot?.integrationId,
                    Authorization: `Bearer ${settings.api?.copilot?.token}`,
                });

                const expectedContent = "Known 1: ******. Injected 1: ******. Injected 2: ******. Known base64: ******";
                const body = JSON.parse((init?.body as string).slice(5) ?? "{}");
                expect(body.choices[0].delta.content).toBe(expectedContent);

                return Promise.resolve(new Response("{}"));
            });

            await client.log({
                created: 0,
                id: "id",
                model: "model",
                object: "chat.completion.chunk",
                choices: [
                    {
                        delta: {
                            content: `Known 1: KNOWN_1. Injected 1: INJECTED_1. Injected 2: INJECTED_2. Known base64: ${Buffer.from("KNOWN_1").toString("base64")}`,
                        },
                        index: 0,
                        finish_reason: null,
                    },
                ],
            });

            expect(didReceiveRequest).toBeTruthy();
            expect(global.fetch).toHaveBeenCalledTimes(1);
        });

        test("should redact patterns for known sensitive strings", async () => {
            const client = await createCopilotSessionsClient(settings, new ConsoleLogger());

            let didReceiveRequest = false;

            global.fetch = vi.fn((input: string | URL | globalThis.Request, init?: RequestInit | undefined) => {
                didReceiveRequest = true;

                expect(input).toBe("https://api.githubcopilot.com/agents/sessions/sessionId/logs");
                expect(init?.method).toBe("PUT");
                expect(init?.headers).toEqual({
                    "Copilot-Integration-Id": settings.api?.copilot?.integrationId,
                    Authorization: `Bearer ${settings.api?.copilot?.token}`,
                });
                const expectedContent = "Authorization: ****** GitHub Token: ******";
                const body = JSON.parse((init?.body as string).slice(5) ?? "{}");
                expect(body.choices[0].delta.content).toBe(expectedContent);

                return Promise.resolve(new Response("{}"));
            });

            await client.log({
                created: 0,
                id: "id",
                model: "model",
                object: "chat.completion.chunk",
                choices: [
                    {
                        delta: {
                            content: `Authorization: Bearer token. GitHub Token: ghp_${"0".repeat(36)}`,
                        },
                        index: 0,
                        finish_reason: null,
                    },
                ],
            });

            expect(didReceiveRequest).toBeTruthy();
            expect(global.fetch).toHaveBeenCalledTimes(1);
        });

        test("should redact tool call arguments by parsing JSON and redacting string/number fields", async () => {
            injectSecretEnvVarKeysIntoSecretFilterViaRunner(["SECRET_TOKEN", "SECRET_NUMBER"]);
            const client = await createCopilotSessionsClient(settings, new ConsoleLogger());

            let didReceiveRequest = false;

            // Add a secret to the environment
            vi.stubEnv("SECRET_TOKEN", "my-secret-value");
            vi.stubEnv("SECRET_NUMBER", "67890");

            global.fetch = vi.fn((_input: string | URL | globalThis.Request, init?: RequestInit | undefined) => {
                didReceiveRequest = true;

                const body = JSON.parse((init?.body as string).slice(5) ?? "{}");
                const toolCall = body.choices[0].delta.tool_calls[0];
                const parsedArgs = JSON.parse(toolCall.function.arguments);

                // Check that string fields containing secrets are redacted
                expect(parsedArgs.secretParam).toBe("******");
                expect(parsedArgs.normalString).toBe("normal-value");
                expect(parsedArgs.sensitiveString).toBe("******"); // Contains JWT pattern
                // Check that number fields are only redacted if they contain patterns
                expect(parsedArgs.normalNumber).toBe(12345);
                expect(parsedArgs.secretNumber).toBe("******");
                // Check that other types are preserved
                expect(parsedArgs.boolParam).toBe(true);
                expect(parsedArgs.nullParam).toBe(null);
                expect(parsedArgs.objectParam.nestedString).toBe("******");
                expect(parsedArgs.objectParam.nestedNormalString).toBe("safe-value");
                expect(parsedArgs.objectParam.nestedBool).toBe(false);
                expect(parsedArgs.arrayParam).toEqual(["******", "safe-value", true, 12345, "******", "******"]);

                return Promise.resolve(new Response("{}"));
            });

            await client.log({
                created: 0,
                id: "id",
                model: "model",
                object: "chat.completion.chunk",
                choices: [
                    {
                        delta: {
                            tool_calls: [
                                {
                                    index: 0,
                                    id: "call_123",
                                    type: "function",
                                    function: {
                                        name: "test_function",
                                        arguments: JSON.stringify({
                                            secretParam: "my-secret-value",
                                            normalString: "normal-value",
                                            sensitiveString: "eyJhbGciOiJIUzI1NiJ9", // JWT pattern
                                            normalNumber: 12345,
                                            secretNumber: 67890,
                                            boolParam: true,
                                            nullParam: null,
                                            objectParam: {
                                                nestedString: "my-secret-value",
                                                nestedNormalString: "safe-value",
                                                nestedBool: false,
                                            },
                                            arrayParam: [
                                                "my-secret-value",
                                                "safe-value",
                                                true,
                                                12345,
                                                67890,
                                                "eyJhbGciOiJIUzI1NiJ9",
                                            ],
                                        }),
                                    },
                                },
                            ],
                        },
                        index: 0,
                        finish_reason: null,
                    },
                ],
            });

            expect(didReceiveRequest).toBeTruthy();
            expect(global.fetch).toHaveBeenCalledTimes(1);
        });

        test("should fall back to string redaction when tool call arguments are not valid JSON", async () => {
            injectSecretEnvVarKeysIntoSecretFilterViaRunner(["SECRET_TOKEN", "SECRET_NUMBER"]);
            const client = await createCopilotSessionsClient(settings, new ConsoleLogger());

            let didReceiveRequest = false;

            // Add a secret to the environment
            vi.stubEnv("SECRET_TOKEN", "my-secret-value");
            vi.stubEnv("SECRET_NUMBER", "67890");

            global.fetch = vi.fn((_input: string | URL | globalThis.Request, init?: RequestInit | undefined) => {
                didReceiveRequest = true;

                const body = JSON.parse((init?.body as string).slice(5) ?? "{}");
                const toolCall = body.choices[0].delta.tool_calls[0];

                // When JSON parsing fails, the entire string should be redacted
                expect(toolCall.function.arguments).toBe("invalid json with ****** in it");

                return Promise.resolve(new Response("{}"));
            });

            await client.log({
                created: 0,
                id: "id",
                model: "model",
                object: "chat.completion.chunk",
                choices: [
                    {
                        delta: {
                            tool_calls: [
                                {
                                    index: 0,
                                    id: "call_123",
                                    type: "function",
                                    function: {
                                        name: "test_function",
                                        arguments: "invalid json with my-secret-value in it",
                                    },
                                },
                            ],
                        },
                        index: 0,
                        finish_reason: null,
                    },
                ],
            });

            expect(didReceiveRequest).toBeTruthy();
            expect(global.fetch).toHaveBeenCalledTimes(1);
        });

        test("should redact JWT tokens in tool call arguments when parsed as JSON", async () => {
            const client = await createCopilotSessionsClient(settings, new ConsoleLogger());

            let didReceiveRequest = false;

            global.fetch = vi.fn((_input: string | URL | globalThis.Request, init?: RequestInit | undefined) => {
                didReceiveRequest = true;

                const body = JSON.parse((init?.body as string).slice(5) ?? "{}");
                const toolCall = body.choices[0].delta.tool_calls[0];
                const parsedArgs = JSON.parse(toolCall.function.arguments);

                // JWT token should be redacted
                expect(parsedArgs.token).toBe("******");
                expect(parsedArgs.regularString).toBe("normal-value");

                return Promise.resolve(new Response("{}"));
            });

            await client.log({
                created: 0,
                id: "id",
                model: "model",
                object: "chat.completion.chunk",
                choices: [
                    {
                        delta: {
                            tool_calls: [
                                {
                                    index: 0,
                                    id: "call_123",
                                    type: "function",
                                    function: {
                                        name: "test_function",
                                        arguments: JSON.stringify({
                                            token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
                                            regularString: "normal-value",
                                        }),
                                    },
                                },
                            ],
                        },
                        index: 0,
                        finish_reason: null,
                    },
                ],
            });

            expect(didReceiveRequest).toBeTruthy();
            expect(global.fetch).toHaveBeenCalledTimes(1);
        });

        test("should handle edge cases in tool call arguments correctly", async () => {
            const client = await createCopilotSessionsClient(settings, new ConsoleLogger());

            let didReceiveRequest = false;

            global.fetch = vi.fn((_input: string | URL | globalThis.Request, init?: RequestInit | undefined) => {
                didReceiveRequest = true;

                const body = JSON.parse((init?.body as string).slice(5) ?? "{}");
                const toolCall = body.choices[0].delta.tool_calls[0];
                const parsedArgs = JSON.parse(toolCall.function.arguments);

                // Verify edge cases are handled correctly
                expect(parsedArgs.emptyString).toBe("");
                expect(parsedArgs.zeroNumber).toBe(0);
                expect(parsedArgs.negativeNumber).toBe(-123);
                expect(parsedArgs.floatNumber).toBe(3.14);
                expect(parsedArgs.emptyArray).toEqual([]);
                expect(parsedArgs.emptyObject).toEqual({});

                return Promise.resolve(new Response("{}"));
            });

            await client.log({
                created: 0,
                id: "id",
                model: "model",
                object: "chat.completion.chunk",
                choices: [
                    {
                        delta: {
                            tool_calls: [
                                {
                                    index: 0,
                                    id: "call_123",
                                    type: "function",
                                    function: {
                                        name: "test_function",
                                        arguments: JSON.stringify({
                                            emptyString: "",
                                            zeroNumber: 0,
                                            negativeNumber: -123,
                                            floatNumber: 3.14,
                                            emptyArray: [],
                                            emptyObject: {},
                                        }),
                                    },
                                },
                            ],
                        },
                        index: 0,
                        finish_reason: null,
                    },
                ],
            });

            expect(didReceiveRequest).toBeTruthy();
            expect(global.fetch).toHaveBeenCalledTimes(1);
        });

        test("should throw if log endpoint returns non-OK response", async () => {
            const client = await createCopilotSessionsClient(settings, new ConsoleLogger());

            vi.spyOn(ghFetchHelpers, "ghFetch").mockImplementation(async (..._args) => {
                // Return a mocked Response object or your custom logic
                return new Response("mocked body", {
                    status: 500,
                    headers: { "x-github-request-id": "test-request-id" },
                });
            });

            const logData: ChatCompletionChunk = {
                created: 0,
                id: "id",
                model: "model",
                object: "chat.completion.chunk",
                choices: [
                    {
                        delta: {
                            content: "test log",
                        },
                        index: 0,
                        finish_reason: null,
                    },
                ],
            };

            await expect(client.log(logData)).rejects.toThrow("Request ID test-request-id failed): 500");
            expect(ghFetchHelpers.ghFetch).toHaveBeenCalledTimes(1);
        });
    });

    test("should throw if error endpoint returns non-OK response", async () => {
        const client = await createCopilotSessionsClient(settings, new ConsoleLogger());

        vi.spyOn(ghFetchHelpers, "ghFetch").mockImplementation(async (..._args) => {
            // Return a mocked Response object or your custom logic
            return new Response("mocked body", {
                status: 500,
                headers: { "x-github-request-id": "test-request-id" },
            });
        });

        const mockError = new Error("mock error", {
            cause: new APIError(403, "Unauthorized", undefined, {
                "x-request-id": "test-request-id",
            }),
        });
        await expect(client.error(mockError)).rejects.toThrow(
            "Error completing session (Request ID: test-request-id): 500",
        );
        expect(ghFetchHelpers.ghFetch).toHaveBeenCalledTimes(1);
    });

    describe("error", async () => {
        test.each([
            // 429 APIError without retry-after header
            {
                error: new APIError(429, undefined, undefined, undefined),
                expectedContent: sessionErrorMessages.RateLimitExceededBase,
            },
            // 429 CAPIError without retry-after header - verify inheritence is working
            {
                error: CAPIError.fromAPIError(new APIError(429, undefined, undefined, undefined)),
                expectedContent: sessionErrorMessages.RateLimitExceededBase,
            },
            // 429 APIError with retry-after header under 60 seconds
            {
                error: new APIError(429, undefined, undefined, {
                    "retry-after": "55",
                }),
                expectedContent: sessionErrorMessages.RateLimitExceededBase + " Please try again in 1 minute.",
            },
            // 429 APIError with retry-after header between 1 minute and 1 hour
            {
                error: new APIError(429, undefined, undefined, {
                    "retry-after": "230",
                }),
                expectedContent: sessionErrorMessages.RateLimitExceededBase + " Please try again in 4 minutes.",
            },
            // 429 APIError with retry-after header over 1 hour
            {
                error: new APIError(429, undefined, undefined, {
                    "retry-after": "7300",
                }),
                expectedContent: sessionErrorMessages.RateLimitExceededBase + " Please try again in 3 hours.",
            },
            // 429 APIError with invalid retry-after header
            {
                error: new APIError(429, undefined, undefined, {
                    "retry-after": "foo",
                }),
                expectedContent: sessionErrorMessages.RateLimitExceededBase,
            },
            // Error with 429 APIError as cause without retry-after header
            {
                error: Error("Failed to get response from the AI model; retried", {
                    cause: new APIError(429, undefined, undefined, undefined),
                }),
                expectedContent: sessionErrorMessages.RateLimitExceededBase,
            },
            // Error with 429 APIError as cause with retry-after header
            {
                error: Error("Failed to get response from the AI model; retried", {
                    cause: new APIError(429, undefined, undefined, {
                        "retry-after": "60",
                    }),
                }),
                expectedContent: sessionErrorMessages.RateLimitExceededBase + " Please try again in 1 minute.",
            },
            // Error with double-nested 429 APIError as cause without retry-after header
            {
                error: new Error("rate limited?", {
                    cause: new Error("rate limited really?", {
                        cause: new APIError(429, undefined, undefined, undefined),
                    }),
                }),
                expectedContent: sessionErrorMessages.RateLimitExceededBase,
            },
            // Error with double-nested 429 APIError as cause with retry-after header
            {
                error: new Error("rate limited?", {
                    cause: new Error("rate limited really?", {
                        cause: new APIError(429, undefined, undefined, {
                            "retry-after": "60",
                        }),
                    }),
                }),
                expectedContent: sessionErrorMessages.RateLimitExceededBase + " Please try again in 1 minute.",
            },
            {
                error: new APIError(422, "Unprocessable Entity", undefined, {
                    "x-github-request-id": "githubRequestId",
                }),
                expectedContent: `${sessionErrorMessages.RAIError} ${sessionErrorMessages.ProblemPersistsWithRequestId} \`githubRequestId\`.`,
            },
            {
                error: new APIError(422, "Unprocessable Entity", undefined, {}),
                expectedContent: `${sessionErrorMessages.RAIError} ${sessionErrorMessages.ProblemPersists}`,
            },
            {
                error: new APIError(
                    503,
                    "unavailable: the request was rate limited by the upstream provider",
                    undefined,
                    { "x-github-request-id": "githubRequestId" },
                ),
                expectedContent: `${sessionErrorMessages.UpstreamFailure} ${sessionErrorMessages.ProblemPersistsWithRequestId} \`githubRequestId\`.`,
            },
            {
                error: new APIError(
                    503,
                    "unavailable: the request was rate limited by the upstream provider",
                    undefined,
                    {},
                ),
                expectedContent: `${sessionErrorMessages.UpstreamFailure} ${sessionErrorMessages.ProblemPersists}`,
            },
            {
                error: new Error("something catastrophic happened in runtime"),
                expectedContent: null,
            },
            {
                error: new Error("something catastrophic happened in runtime", {
                    cause: "foo",
                }),
                expectedContent: null,
            },
            // Error with GitHub request ID uses the GitHub request ID in the message
            {
                error: new Error("Failed to get response from the AI model; retried", {
                    cause: new APIError(500, undefined, undefined, {
                        "x-github-request-id": "githubRequestId",
                    }),
                }),
                expectedContent: `${sessionErrorMessages.InternalError} ${sessionErrorMessages.ProblemPersistsWithRequestId} \`githubRequestId\`.`,
            },
            // Error with non-GitHub request ID uses the non-GitHub request ID in the message
            {
                error: new Error("Failed to get response from the AI model; retried", {
                    cause: new APIError(500, undefined, undefined, {
                        "x-request-id": "requestId",
                    }),
                }),
                expectedContent: `${sessionErrorMessages.InternalError} ${sessionErrorMessages.ProblemPersistsWithRequestId} \`requestId\`.`,
            },
            // Error with GitHub and non-GitHub request ID uses the GitHub request ID in the message
            {
                error: new Error("Failed to get response from the AI model; retried", {
                    cause: new APIError(500, undefined, undefined, {
                        "x-github-request-id": "githubRequestId",
                        "x-request-id": "requestId",
                    }),
                }),
                expectedContent: `${sessionErrorMessages.InternalError} ${sessionErrorMessages.ProblemPersistsWithRequestId} \`githubRequestId\`.`,
            },
        ])(
            "should update session object with an error message based on the error",
            async ({ error, expectedContent }) => {
                const client = await createCopilotSessionsClient(settings, new ConsoleLogger());

                let didReceiveRequest = false;

                global.fetch = vi.fn((input: string | URL | globalThis.Request, init?: RequestInit | undefined) => {
                    didReceiveRequest = true;

                    expect(input).toBe("https://api.githubcopilot.com/agents/sessions/sessionId");
                    expect(init?.method).toBe("PUT");
                    expect(init?.headers).toEqual({
                        "Copilot-Integration-Id": settings.api?.copilot?.integrationId,
                        Authorization: `Bearer ${settings.api?.copilot?.token}`,
                        "Content-Type": "application/json",
                        Accept: "application/json",
                    });
                    const body = JSON.parse(init?.body as string);
                    expect(body.error.message).toContain(expectedContent);
                    expect(body.error.message).toContain(sessionErrorMessages.TryAgainInstructions);

                    return Promise.resolve(new Response("{}"));
                });

                await client.error(error as Error);

                if (expectedContent !== null) {
                    expect(didReceiveRequest).toBeTruthy();
                    expect(global.fetch).toHaveBeenCalledTimes(1);
                } else {
                    expect(didReceiveRequest).toBeFalsy();
                    expect(global.fetch).not.toHaveBeenCalled();
                }
            },
        );
    });

    describe("session full handling", () => {
        test("should handle 413 response in error method by disabling logging", async () => {
            const client = await createCopilotSessionsClient(settings, new ConsoleLogger());
            let requestCount = 0;

            vi.spyOn(ghFetchHelpers, "ghFetch").mockImplementation(async (..._args) => {
                requestCount++;
                if (requestCount === 1) {
                    // First request (error method) returns 413
                    return new Response("", {
                        status: 413,
                        headers: {
                            "x-github-request-id": "test-request-id",
                        },
                    });
                } else if (requestCount === 2) {
                    // Second request (logSessionFull attempt) returns success
                    return new Response("{}", { status: 200 });
                }
                return new Response("", { status: 200 });
            });

            const mockError = new APIError(429, undefined, undefined, {
                "retry-after": "foo",
            });

            await client.error(mockError);

            // Should have made exactly 2 requests (error attempt + logSessionFull)
            expect(ghFetchHelpers.ghFetch).toHaveBeenCalledTimes(2);

            // Subsequent error calls should be no-ops
            await client.error(mockError);
            expect(ghFetchHelpers.ghFetch).toHaveBeenCalledTimes(2);
        });

        test("should handle 413 response in log method by disabling logging", async () => {
            const client = await createCopilotSessionsClient(settings, new ConsoleLogger());
            let requestCount = 0;

            vi.spyOn(ghFetchHelpers, "ghFetch").mockImplementation(async (..._args) => {
                requestCount++;
                if (requestCount === 1) {
                    // First request (log method) returns 413
                    return new Response("", {
                        status: 413,
                        headers: {
                            "x-github-request-id": "test-request-id",
                        },
                    });
                } else if (requestCount === 2) {
                    // Second request (logSessionFull attempt) returns success
                    return new Response("{}", { status: 200 });
                }
                return new Response("", { status: 200 });
            });

            const logData: ChatCompletionChunk = {
                created: 0,
                id: "id",
                model: "model",
                object: "chat.completion.chunk",
                choices: [
                    {
                        delta: {
                            content: "test log",
                        },
                        index: 0,
                        finish_reason: null,
                    },
                ],
            };

            await client.log(logData);

            // Should have made exactly 2 requests (log attempt + logSessionFull)
            expect(ghFetchHelpers.ghFetch).toHaveBeenCalledTimes(2);

            // Subsequent log calls should be no-ops
            await client.log(logData);
            expect(ghFetchHelpers.ghFetch).toHaveBeenCalledTimes(2);
        });

        test("should handle failure to log session full message", async () => {
            const mockLogger = {
                debug: vi.fn(),
                error: vi.fn(),
                info: vi.fn(),
                warn: vi.fn(),
            };

            const client = await createCopilotSessionsClient(settings, mockLogger as unknown as RunnerLogger);

            vi.spyOn(ghFetchHelpers, "ghFetch").mockImplementation(async (..._args) => {
                return new Response("", {
                    status: 413,
                    headers: { "x-github-request-id": "test-request-id" },
                });
            });

            const mockError = new APIError(429, undefined, undefined, {
                "retry-after": "foo",
            });

            await client.error(mockError);

            // Should have attempted to log error message
            expect(mockLogger.error).toHaveBeenCalled();

            // Subsequent calls should be no-ops
            await client.error(mockError);
            expect(ghFetchHelpers.ghFetch).toHaveBeenCalledTimes(2); // Initial error + failed logSessionFull
        });
    });

    afterEach(() => {
        vi.unstubAllEnvs();
        vi.unstubAllGlobals();
        vi.restoreAllMocks();
    });
});
