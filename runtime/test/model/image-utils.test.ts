/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import {
    convertPathToAbsolutePath,
    extractRepositoryImageUrls,
    extractRepositoryInsights,
    getAttachmentFromGuid,
    isImageInRepoPath,
    matchAllImageReferences,
} from "../../src/model/capi/image-utils";
import { ImageReference } from "../../src/model/capi/types";
import { ConsoleLogger } from "../../src/runner/logger/console";
import { demandGitHubToken } from "../../src/settings/demand";
import { getOrInitSettings } from "../../src/settings/factory";

describe("test matching image references", () => {
    const logger = new ConsoleLogger();

    test("should extract image references correctly", () => {
        const result: ImageReference[] = matchAllImageReferences(
            "What text is in this image? ![alt text](https://github.com/user-attachments/assets/9300859f-46c9-4d16-80b0-c9fd0f38adb3)",
            "https://github.com",
            false,
            logger,
        );
        expect(result.length).equal(1);
        expect(result[0].guid).equal("9300859f-46c9-4d16-80b0-c9fd0f38adb3");
        expect(result[0].url).equal("https://github.com/user-attachments/assets/9300859f-46c9-4d16-80b0-c9fd0f38adb3");
    });

    test("should extract image tag correctly", () => {
        const result: ImageReference[] = matchAllImageReferences(
            'What text is in this image? <img src="https://github.com/user-attachments/assets/9300859f-46c9-4d16-80b0-c9fd0f38adb3">',
            "https://github.com",
            false,
            logger,
        );
        expect(result.length).equal(1);
        expect(result[0].guid).equal("9300859f-46c9-4d16-80b0-c9fd0f38adb3");
        expect(result[0].url).equal("https://github.com/user-attachments/assets/9300859f-46c9-4d16-80b0-c9fd0f38adb3");
    });

    test("should return empty guid for empty reference", () => {
        const result = matchAllImageReferences("", "", false, logger);
        expect(result.length).equal(0);
    });
});

describe.skipIf(process.env.DISABLE_CAPI_TESTS === "true")("image repo utility methods", async () => {
    const settings = await getOrInitSettings();

    settings.github = {
        serverUrl: "https://github.com",
    };

    const serverUrl = settings.github.serverUrl ?? "https://github.com";

    describe("isRelativePath", () => {
        it("returns true for simple relative image paths", () => {
            expect(isImageInRepoPath("images/photo.png")).toBe(true);
        });

        it('returns true for simple relative image paths beginning with "./"', () => {
            expect(isImageInRepoPath("./images/photo.png")).toBe(true);
        });

        it("returns false for non-image paths", () => {
            expect(isImageInRepoPath("docs/readme.md")).toBe(false);
            expect(isImageInRepoPath("archive.zip")).toBe(false);
        });

        it("returns false for absolute paths", () => {
            expect(isImageInRepoPath("https://github.com/org/repo/main/image.png")).toBe(false);
        });
    });

    describe("extractRepositoryInsights", () => {
        it('parses JSON array after "Here are relevant code snippets from the repository that might help with this task:"', () => {
            const input =
                'Some text\nHere are relevant code snippets from the repository that might help with this task: [{"foo":"bar"}]\nMore text';
            const result = extractRepositoryInsights(input);
            expect(result).toEqual([{ foo: "bar" }]);
        });

        it("returns null when no insights tag present", () => {
            const input = "No insights here";
            expect(extractRepositoryInsights(input)).toBeNull();
        });

        it("returns null on malformed JSON", () => {
            const input =
                "Here are relevant code snippets from the repository that might help with this task: [invalid json]";
            expect(extractRepositoryInsights(input)).toBeNull();
        });
    });

    describe("convertRelativePathToAbsolutePath", () => {
        it("constructs correct URLs for a given repoInsight and imagePath", () => {
            const repoInsight = {
                url: "https://github.com/org/repo",
                ref: "refs/heads/main",
                path: "folder/file.json",
            };
            const imagePath = "assets/img.png";
            const uiUrl = convertPathToAbsolutePath(repoInsight, imagePath, serverUrl);
            expect(uiUrl).toBe("https://github.com/org/repo/blob/main/folder/assets/img.png");
        });

        it("constructs correct URLs using complex relative path", () => {
            const repoInsight = {
                url: "https://github.com/org/repo",
                ref: "refs/heads/main",
                path: "folder/file.json",
            };
            const imagePath = "assets/../img.png";
            const uiUrl = convertPathToAbsolutePath(repoInsight, imagePath, serverUrl);
            expect(uiUrl).toBe("https://github.com/org/repo/blob/main/folder/img.png");
        });

        it("constructs correct URLs using basic relative path", () => {
            const repoInsight = {
                url: "https://github.com/org/repo",
                ref: "refs/heads/main",
                path: "file.json",
            };
            const imagePath = "img.png";
            const uiUrl = convertPathToAbsolutePath(repoInsight, imagePath, serverUrl);
            expect(uiUrl).toBe("https://github.com/org/repo/blob/main/img.png");
        });

        it("constructs correct URLs using absolute path", () => {
            const repoInsight = {
                url: "https://github.com/org/repo",
                ref: "refs/heads/main",
                path: "folder/file.json",
            };
            const imagePath = "/assets/img.png";
            const uiUrl = convertPathToAbsolutePath(repoInsight, imagePath, serverUrl);
            expect(uiUrl).toBe("https://github.com/org/repo/blob/main/assets/img.png");
        });
    });

    describe("extractRepositoryImageUrls", () => {
        const logger = new ConsoleLogger();
        it("returns empty array when maxImagesToResolve is <= 0", () => {
            const messages = [{ role: "user", content: "No images here" }];
            expect(extractRepositoryImageUrls(messages, 0, serverUrl, logger)).toEqual([]);
            expect(extractRepositoryImageUrls(messages, -5, serverUrl, logger)).toEqual([]);
        });

        it("extracts and converts markdown image references up to the limit", () => {
            const insightJson = JSON.stringify([
                {
                    url: "https://github.com/user/repo",
                    ref: "refs/heads/main",
                    path: "folder/insight.json",
                    contents: "![rel](images/pic.png) and ![abs](https://example.com/img.jpg)",
                },
            ]);
            const content = `Here are relevant code snippets from the repository that might help with this task: ${insightJson}`;
            const messages = [{ role: "user", content }];

            // allow up to 2 images
            const result = extractRepositoryImageUrls(messages, 2, serverUrl, logger);
            expect(result).toHaveLength(1);
            expect(result[0].url).toBe("https://github.com/user/repo/blob/main/folder/images/pic.png");
        });

        it("respects the maxImagesToResolve limit", () => {
            const insightJson = JSON.stringify([
                {
                    url: "https://github.com/user/repo",
                    ref: "refs/heads/main",
                    path: "folder/insight.json",
                    contents: "![one](a.png) ![two](b.png) ![three](c.png)",
                },
            ]);
            const content = `Here are relevant code snippets from the repository that might help with this task: ${insightJson}`;
            const messages = [{ role: "user", content }];

            // only allow 1 image
            const result = extractRepositoryImageUrls(messages, 1, serverUrl, logger);
            expect(result).toHaveLength(1);
            expect(result[0].url).toContain("/blob/main/folder/a.png");
        });

        it("returns empty array when no markdown images present", () => {
            const insightJson = JSON.stringify([
                {
                    url: "x",
                    ref: "r",
                    path: "p",
                    contents: "no images here",
                },
            ]);
            const messages = [
                {
                    role: "user",
                    content: `Here are relevant code snippets from the repository that might help with this task: ${insightJson}`,
                },
            ];
            expect(extractRepositoryImageUrls(messages, 5, serverUrl, logger)).toEqual([]);
        });

        it("extracts and converts absolute path image references", () => {
            const insightJson = JSON.stringify([
                {
                    url: "https://github.com/user/repo",
                    ref: "refs/heads/main",
                    path: "folder/insight.json",
                    contents: "![abs path](/assets/logo.png) and ![another](/docs/images/diagram.svg)",
                },
            ]);
            const content = `Here are relevant code snippets from the repository that might help with this task: ${insightJson}`;
            const messages = [{ role: "user", content }];

            const result = extractRepositoryImageUrls(messages, 5, serverUrl, logger);
            expect(result).toHaveLength(2);
            expect(result[0].url).toBe("https://github.com/user/repo/blob/main/assets/logo.png");
            expect(result[1].url).toBe("https://github.com/user/repo/blob/main/docs/images/diagram.svg");
        });

        it("skips non-github image references", () => {
            const insightJson = JSON.stringify([
                {
                    url: "https://github.com/user/repo",
                    ref: "refs/heads/main",
                    path: "folder/insight.json",
                    contents:
                        "It should also be verified that an invoice for the transaction was created as well:\\n\\n![image](https://user-images.githubusercontent.com/16449156/211853789-95e1ec0f-8e97-4e84-97df-84d8260e261b.png)",
                },
            ]);
            const content = `Here are relevant code snippets from the repository that might help with this task: ${insightJson}`;
            const messages = [{ role: "user", content }];

            const result = extractRepositoryImageUrls(messages, 5, serverUrl, logger);
            expect(result).toHaveLength(0);
        });
    });
});

describe.runIf(process.env.NIGHTLY_TEST_CI === "true")("getAttachmentFromGuid", () => {
    it("returns correct size when both fetches succeed", async () => {
        const logger = new ConsoleLogger();
        process.env.GITHUB_USER_ID = "1885174";
        const settings = await getOrInitSettings();
        settings.github = {
            serverUrl: "https://github.com",
            token: process.env.GITHUB_TOKEN,
        };

        settings.featureFlags = {};
        settings.featureFlags["copilot_swe_agent_vision"] = true;

        const imageGuid = "19912acc-2f92-441b-a47b-4f32f97d004a";
        const attachment = await getAttachmentFromGuid(imageGuid, demandGitHubToken(settings), logger);
        expect(attachment.size).toEqual(2091165);
    });
});
