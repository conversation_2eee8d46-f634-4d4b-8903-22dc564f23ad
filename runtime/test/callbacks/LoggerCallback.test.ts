/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import { beforeEach, expect, test, vi } from "vitest";
import { LoggerCallback } from "../../src/callbacks/LoggerCallback";

// Mock the firewall module
vi.mock("../../src/firewall", () => ({
    getBlockedRequests: vi.fn().mockResolvedValue([]),
}));

let logOutput: string[];
let mockLog: (msg: string) => void;
let callback: LoggerCallback;

beforeEach(() => {
    logOutput = [];
    mockLog = vi.fn((msg: string) => {
        logOutput.push(msg);
    });
    callback = new LoggerCallback(mockLog);
});

test("handles assistant message with string content", async () => {
    await callback.progress({
        kind: "message",
        message: {
            role: "assistant",
            content: "Hello, this is a test message",
        },
    });

    expect(logOutput).toHaveLength(1);
    expect(logOutput[0]).toContain("copilot:");
    expect(logOutput[0]).toContain("Hello, this is a test message");
});

test("handles assistant message with reasoning_text", async () => {
    await callback.progress({
        kind: "message",
        message: {
            role: "assistant",
            content: "Main content",
            reasoning_text: "This is my reasoning",
        },
    });

    expect(logOutput).toHaveLength(2);
    expect(logOutput[0]).toContain("Main content");
    expect(logOutput[1]).toContain("This is my reasoning");
});

test("handles assistant message with array content", async () => {
    await callback.progress({
        kind: "message",
        message: {
            role: "assistant",
            content: [
                { type: "text", text: "First part" },
                { type: "text", text: "Second part" },
            ],
        },
    });

    expect(logOutput).toHaveLength(1);
    expect(logOutput[0]).toContain("copilot:");
    expect(logOutput[0]).toContain("First part");
    expect(logOutput[0]).toContain("Second part");
});

test("handles tool calls and results", async () => {
    // First, send a tool call
    await callback.progress({
        kind: "message",
        message: {
            role: "assistant",
            tool_calls: [
                {
                    id: "test-tool-id",
                    type: "function",
                    function: {
                        name: "test_function",
                        arguments: JSON.stringify({
                            param1: "value1",
                            param2: "value2",
                        }),
                    },
                },
            ],
        },
    });

    // Then send the tool result
    await callback.progress({
        kind: "message",
        message: {
            role: "tool",
            tool_call_id: "test-tool-id",
            content: "Function executed successfully",
        },
    });

    expect(logOutput).toHaveLength(6);
    expect(logOutput[0]).toContain("function:");
    expect(logOutput[1]).toContain("name: test_function");
    expect(logOutput[2]).toContain("args:");
    expect(logOutput[3]).toContain("param1: value1");
    expect(logOutput[4]).toContain("param2: value2");
    expect(logOutput[5]).toContain("result: Function executed successfully");
});

test("handles tool call with malformed arguments", async () => {
    // First, send a tool call with malformed JSON
    await callback.progress({
        kind: "message",
        message: {
            role: "assistant",
            tool_calls: [
                {
                    id: "test-tool-id-malformed",
                    type: "function",
                    function: {
                        name: "test_function",
                        arguments: "invalid json {",
                    },
                },
            ],
        },
    });

    // Then send the tool result
    await callback.progress({
        kind: "message",
        message: {
            role: "tool",
            tool_call_id: "test-tool-id-malformed",
            content: "Function result",
        },
    });

    expect(logOutput).toHaveLength(5);
    expect(logOutput[0]).toContain("function:");
    expect(logOutput[1]).toContain("name: test_function");
    expect(logOutput[2]).toContain("args:");
    expect(logOutput[3]).toContain("functionArguments: invalid json {");
    expect(logOutput[4]).toContain("result: Function result");
});

test("handles tool call with no arguments", async () => {
    await callback.progress({
        kind: "message",
        message: {
            role: "assistant",
            tool_calls: [
                {
                    id: "test-tool-id-no-args",
                    type: "function",
                    function: {
                        name: "simple_function",
                        arguments: "{}",
                    },
                },
            ],
        },
    });

    await callback.progress({
        kind: "message",
        message: {
            role: "tool",
            tool_call_id: "test-tool-id-no-args",
            content: "Simple result",
        },
    });

    expect(logOutput).toHaveLength(3);
    expect(logOutput[0]).toContain("function:");
    expect(logOutput[1]).toContain("name: simple_function");
    expect(logOutput[2]).toContain("result: Simple result");
});

test("handles tool result with array content", async () => {
    await callback.progress({
        kind: "message",
        message: {
            role: "assistant",
            tool_calls: [
                {
                    id: "test-tool-array",
                    type: "function",
                    function: {
                        name: "array_function",
                        arguments: "{}",
                    },
                },
            ],
        },
    });

    await callback.progress({
        kind: "message",
        message: {
            role: "tool",
            tool_call_id: "test-tool-array",
            content: [{ type: "text", text: "Array result" }],
        },
    });

    expect(logOutput).toHaveLength(3);
    expect(logOutput[0]).toContain("function:");
    expect(logOutput[1]).toContain("name: array_function");
    expect(logOutput[2]).toContain('result: [{"type":"text","text":"Array result"}]');
});

test("ignores log events", async () => {
    await callback.progress({
        kind: "log",
        message: "This should be ignored",
    });

    expect(logOutput).toHaveLength(0);
});

test("ignores user messages", async () => {
    await callback.progress({
        kind: "message",
        message: {
            role: "user",
            content: "This user message should be ignored",
        },
    });

    expect(logOutput).toHaveLength(0);
});

test("ignores response events", async () => {
    await callback.progress({
        kind: "response",
        response: {
            role: "assistant",
            content: "This response should be ignored",
            refusal: null,
        },
    });

    expect(logOutput).toHaveLength(0);
});

test("handles model call failure events", async () => {
    await callback.progress({
        kind: "model_call_failure",
        turn: 1,
        modelCallDurationMs: 1000,
        modelCall: {
            model: "test-model",
            error: "Model call failed",
        },
    });

    // Should not log anything for model call failures
    expect(logOutput).toHaveLength(0);
});

test("displays generic message events", async () => {
    await callback.progress({
        kind: "message",
        message: {
            role: "system",
            content: "System message",
        },
    });

    expect(logOutput).toHaveLength(1);
    expect(logOutput[0]).toContain("copilot-event:");
    expect(logOutput[0]).toContain("System message");
});

test("handles partialResult", async () => {
    await callback.partialResult({
        branchName: "feature-branch",
        message: "Work in progress",
    });

    expect(logOutput).toHaveLength(1);
    expect(logOutput[0]).toContain("copilot-update:");
    expect(logOutput[0]).toContain("feature-branch: Work in progress");
});

test("handles commentReply", async () => {
    await callback.commentReply({
        comment_id: 123,
        message: "Reply to comment",
    });

    expect(logOutput).toHaveLength(1);
    expect(logOutput[0]).toContain("copilot-reply:");
    expect(logOutput[0]).toContain("123: Reply to comment");
});

test("handles object result", async () => {
    const result = {
        diff: "- old line\n+ new line",
        branchName: "main",
        prTitle: "Update code",
        prDescription: "This PR updates the code",
    };

    await callback.result(result);

    expect(logOutput).toHaveLength(1);
    expect(logOutput[0]).toContain("copilot-result:");
    expect(logOutput[0]).toContain('"diff": "- old line\\n+ new line"');
    expect(logOutput[0]).toContain('"branchName": "main"');
    expect(logOutput[0]).toContain('"prTitle": "Update code"');
    expect(logOutput[0]).toContain('"prDescription": "This PR updates the code"');
});

test("handles object result with blockedRequests", async () => {
    const result = {
        diff: "- old line\n+ new line",
        branchName: "main",
        prTitle: "Update code",
        prDescription: "This PR updates the code",
        blockedRequests: [
            {
                because: "security policy",
                blockedAt: "2024-01-01T00:00:00Z",
                cmd: "curl",
                domains: "blocked.com",
                hasBeenRedirected: false,
                ip: "***********",
                originalIp: "***********",
                port: "443",
                ruleSourceComment: "Test rule",
                url: "http://blocked.com",
            },
        ],
    };

    await callback.result(result);

    expect(logOutput).toHaveLength(1);
    expect(logOutput[0]).toContain("copilot-result:");
    expect(logOutput[0]).toContain('"diff": "- old line\\n+ new line"');
    // Should not contain blockedRequests in the output
    expect(logOutput[0]).not.toContain("blockedRequests");
});

test("handles error event", async () => {
    const error = {
        text: "Something went wrong",
        name: "TestError",
        stack: "Error stack trace",
    };

    await callback.error(error);

    expect(logOutput).toHaveLength(1);
    expect(logOutput[0]).toContain("ERROR:");
    expect(logOutput[0]).toContain("Something went wrong");
    expect(logOutput[0]).toContain("TestError");
});

test("handles error event with blockedRequests", async () => {
    const error = {
        text: "Something went wrong",
        name: "TestError",
        blockedRequests: [
            {
                because: "security policy",
                blockedAt: "2024-01-01T00:00:00Z",
                cmd: "curl",
                domains: "blocked.com",
                hasBeenRedirected: false,
                ip: "***********",
                originalIp: "***********",
                port: "443",
                ruleSourceComment: "Test rule",
                url: "http://blocked.com",
            },
        ],
    };

    await callback.error(error);

    expect(logOutput).toHaveLength(1);
    expect(logOutput[0]).toContain("ERROR:");
    expect(logOutput[0]).toContain("Something went wrong");
    // Should not contain blockedRequests in the output
    expect(logOutput[0]).not.toContain("blockedRequests");
});

test("handles multiple tool calls in sequence", async () => {
    // First tool call
    await callback.progress({
        kind: "message",
        message: {
            role: "assistant",
            tool_calls: [
                {
                    id: "tool-1",
                    type: "function",
                    function: {
                        name: "function_one",
                        arguments: '{"arg": "value1"}',
                    },
                },
            ],
        },
    });

    await callback.progress({
        kind: "message",
        message: {
            role: "tool",
            tool_call_id: "tool-1",
            content: "Result 1",
        },
    });

    // Second tool call
    await callback.progress({
        kind: "message",
        message: {
            role: "assistant",
            tool_calls: [
                {
                    id: "tool-2",
                    type: "function",
                    function: {
                        name: "function_two",
                        arguments: '{"arg": "value2"}',
                    },
                },
            ],
        },
    });

    await callback.progress({
        kind: "message",
        message: {
            role: "tool",
            tool_call_id: "tool-2",
            content: "Result 2",
        },
    });

    expect(logOutput).toHaveLength(10);
    // First function call should be at indices 0-4
    expect(logOutput[0]).toContain("function:");
    expect(logOutput[1]).toContain("name: function_one");
    expect(logOutput[4]).toContain("Result 1");
    // Second function call should be at indices 5-9
    expect(logOutput[5]).toContain("function:");
    expect(logOutput[6]).toContain("name: function_two");
    expect(logOutput[9]).toContain("Result 2");
});

test("handles tool result for unknown tool call id", async () => {
    // Send tool result without corresponding tool call
    await callback.progress({
        kind: "message",
        message: {
            role: "tool",
            tool_call_id: "unknown-id",
            content: "Orphaned result",
        },
    });

    // Should not log anything since there's no matching tool call
    expect(logOutput).toHaveLength(0);
});

test("handles complex function arguments with nested objects", async () => {
    await callback.progress({
        kind: "message",
        message: {
            role: "assistant",
            tool_calls: [
                {
                    id: "complex-tool",
                    type: "function",
                    function: {
                        name: "complex_function",
                        arguments: JSON.stringify({
                            simple: "value",
                            nested: {
                                inner: "data",
                                array: [1, 2, 3],
                            },
                            list: ["a", "b", "c"],
                        }),
                    },
                },
            ],
        },
    });

    await callback.progress({
        kind: "message",
        message: {
            role: "tool",
            tool_call_id: "complex-tool",
            content: "Complex result",
        },
    });

    expect(logOutput).toHaveLength(7);
    expect(logOutput[0]).toContain("function:");
    expect(logOutput[1]).toContain("name: complex_function");
    expect(logOutput[2]).toContain("args:");
    // The arguments will be sorted alphabetically by key
    const allContent = logOutput.join(" ");
    expect(allContent).toContain("simple: value");
    expect(allContent).toContain("nested:");
    expect(allContent).toContain("list:");
    expect(logOutput[6]).toContain("Complex result");
});
