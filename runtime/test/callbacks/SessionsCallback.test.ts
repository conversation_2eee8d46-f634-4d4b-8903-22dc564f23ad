/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { ChatCompletionChunk } from "openai/resources.mjs";
import { beforeEach, expect, MockInstance, test, vi } from "vitest";
import { SessionsCallback } from "../../src/callbacks/SessionsCallback";
import * as openAiHelpers from "../../src/helpers/openAiHelpers";
import { completionToChunk } from "../../src/helpers/openAiHelpers";
import { NoopCopilotSessionsClient } from "../../src/model/capi/sessions-client";
import type { ModelCallSuccessEvent, ToolExecutionEvent } from "../../src/model/event";
import { RunnerLogger } from "../../src/runner";
import type { ToolResultExpanded } from "../../src/tools";
import type { AgentCallbackProgressEvent } from "../../src/types";
import { inflatePreRecordedCompletion } from "../preRecordedCompletions";

let sessionClient: NoopCopilotSessionsClient;
let logger: RunnerLogger;
let callback: SessionsCallback;
let sessionLogSpy: MockInstance<(_logs: ChatCompletionChunk) => Promise<void>>;
let warningSpy: ReturnType<typeof vi.spyOn>;
let createChunkSpy: MockInstance<
    (sessionLog: string, completionChunk: ChatCompletionChunk, toolCallId: string) => ChatCompletionChunk
>;

beforeEach(() => {
    sessionClient = new NoopCopilotSessionsClient();
    logger = {
        warning: vi.fn(),
        debug: vi.fn(),
        info: vi.fn(),
        error: vi.fn(),
    } as unknown as RunnerLogger;
    callback = new SessionsCallback(sessionClient, logger);

    sessionLogSpy = vi.spyOn(sessionClient, "log");
    warningSpy = vi.spyOn(logger, "warning");
    createChunkSpy = vi.spyOn(openAiHelpers, "createChunkWithSessionLogToolResult");
});

test("handles model call success event", async () => {
    const completion = completionToChunk(
        inflatePreRecordedCompletion({
            type: "stop-message",
            content: "Hello, world!",
        }),
    );

    const event: ModelCallSuccessEvent = {
        kind: "model_call_success",
        turn: 1,
        modelCallDurationMs: 1000,
        modelCall: {
            model: "test-model",
        },
        responseChunk: completion,
        responseUsage: {
            prompt_tokens: 100,
            completion_tokens: 50,
            total_tokens: 150,
        },
    };

    await callback.progress(event);

    expect(sessionLogSpy).toHaveBeenCalledTimes(1);
    const sessionLoggedChunk = sessionLogSpy.mock.calls[0][0] as ChatCompletionChunk;
    expect(sessionLoggedChunk.object).toBe("chat.completion.chunk");
    expect(sessionLoggedChunk.choices[0].delta.content).toBe("Hello, world!");
});

test("handles model call success event with tool calls", async () => {
    const completion = completionToChunk(
        inflatePreRecordedCompletion({
            type: "tool-calls",
            toolsToCall: [{ tool: "test_function", args: { param: "value" } }],
            content: "I'll call a function",
        }),
    );

    const event: ModelCallSuccessEvent = {
        kind: "model_call_success",
        turn: 1,
        modelCallDurationMs: 1000,
        modelCall: {
            model: "test-model",
        },
        responseChunk: completion,
        responseUsage: {
            prompt_tokens: 100,
            completion_tokens: 50,
            total_tokens: 150,
        },
    };

    await callback.progress(event);

    expect(sessionLogSpy).toHaveBeenCalledTimes(1);
    const sessionLoggedChunk = sessionLogSpy.mock.calls[0][0] as ChatCompletionChunk;
    expect(sessionLoggedChunk.object).toBe("chat.completion.chunk");
    expect(sessionLoggedChunk.choices[0].delta.tool_calls).toBeDefined();
    expect(sessionLoggedChunk.choices[0].delta.tool_calls![0]?.function?.name).toBe("test_function");
});

test("handles tool execution event with success result", async () => {
    // First, set up a model call with tool calls
    const completion = completionToChunk(
        inflatePreRecordedCompletion({
            type: "tool-calls",
            toolsToCall: [{ tool: "test_function", args: { param: "value" } }],
        }),
    );

    const modelEvent: ModelCallSuccessEvent = {
        kind: "model_call_success",
        turn: 1,
        modelCallDurationMs: 1000,
        modelCall: {
            model: "test-model",
        },
        responseChunk: completion,
        responseUsage: {
            prompt_tokens: 100,
            completion_tokens: 50,
            total_tokens: 150,
        },
    };

    await callback.progress(modelEvent);

    // Now handle the tool execution result
    const toolResult: ToolResultExpanded = {
        textResultForLlm: "Function executed successfully",
        resultType: "success",
        toolTelemetry: {
            properties: {},
            metrics: {},
        },
        sessionLog: "Tool output: Function executed successfully",
    };

    const toolCallId = completion.choices[0].delta.tool_calls![0].id;
    if (!toolCallId) {
        return expect(false, "Tool call ID should not be undefined");
    }
    const toolEvent: ToolExecutionEvent = {
        kind: "tool_execution",
        turn: 1,
        toolCallId,
        toolResult,
        durationMs: 500,
    };

    await callback.progress(toolEvent);

    expect(sessionLogSpy).toHaveBeenCalledTimes(2);
    const toolResultChunk = sessionLogSpy.mock.calls[1][0] as ChatCompletionChunk;
    expect(toolResultChunk.choices[0].delta.tool_calls![0]?.id).toBe(toolCallId);

    // Verify the complex session log logic: success case with sessionLog should use sessionLog
    expect(createChunkSpy).toHaveBeenCalledWith(
        // Should use sessionLog
        "Tool output: Function executed successfully",
        completion,
        toolCallId,
    );
});

test("handles tool execution event with failure result", async () => {
    const completion = completionToChunk(
        inflatePreRecordedCompletion({
            type: "tool-calls",
            toolsToCall: [{ tool: "failing_function", args: {} }],
        }),
    );

    const modelEvent: ModelCallSuccessEvent = {
        kind: "model_call_success",
        turn: 1,
        modelCallDurationMs: 1000,
        modelCall: {
            model: "test-model",
        },
        responseChunk: completion,
        responseUsage: {
            prompt_tokens: 100,
            completion_tokens: 50,
            total_tokens: 150,
        },
    };

    await callback.progress(modelEvent);

    const toolResult: ToolResultExpanded = {
        textResultForLlm: "Function failed",
        resultType: "failure",
        error: "Something went wrong",
        toolTelemetry: {
            properties: {},
            metrics: {},
        },
    };

    const toolCallId = completion.choices[0].delta.tool_calls![0].id;
    if (!toolCallId) {
        return expect(false, "Tool call ID should not be undefined");
    }
    const toolEvent: ToolExecutionEvent = {
        kind: "tool_execution",
        turn: 1,
        toolCallId,
        toolResult,
        durationMs: 500,
    };

    await callback.progress(toolEvent);

    expect(sessionLogSpy).toHaveBeenCalledTimes(2);
    const toolResultChunk = sessionLogSpy.mock.calls[1][0] as ChatCompletionChunk;
    expect(toolResultChunk.choices[0].delta.tool_calls![0]?.id).toBe(toolCallId);

    // Verify the complex session log logic: failure case with error should wrap error in <error> tags
    expect(createChunkSpy).toHaveBeenCalledWith(
        // Should wrap error in tags
        "<error>Something went wrong</error>",
        completion,
        toolCallId,
    );
});

test("handles tool execution event with failure result and no error", async () => {
    const completion = completionToChunk(
        inflatePreRecordedCompletion({
            type: "tool-calls",
            toolsToCall: [{ tool: "failing_function", args: {} }],
        }),
    );

    const modelEvent: ModelCallSuccessEvent = {
        kind: "model_call_success",
        turn: 1,
        modelCallDurationMs: 1000,
        modelCall: {
            model: "test-model",
        },
        responseChunk: completion,
        responseUsage: {
            prompt_tokens: 100,
            completion_tokens: 50,
            total_tokens: 150,
        },
    };

    await callback.progress(modelEvent);

    const toolResult: ToolResultExpanded = {
        textResultForLlm: "Function failed with details",
        resultType: "failure",
        toolTelemetry: {
            properties: {},
            metrics: {},
        },
    };

    const toolCallId = completion.choices[0].delta.tool_calls![0].id;
    if (!toolCallId) {
        return expect(false, "Tool call ID should not be undefined");
    }
    const toolEvent: ToolExecutionEvent = {
        kind: "tool_execution",
        turn: 1,
        toolCallId,
        toolResult,
        durationMs: 500,
    };

    await callback.progress(toolEvent);

    expect(sessionLogSpy).toHaveBeenCalledTimes(2);

    // Verify the complex session log logic: failure case without error should use textResultForLlm
    expect(createChunkSpy).toHaveBeenCalledWith(
        // Should fall back to textResultForLlm
        "Function failed with details",
        completion,
        toolCallId,
    );
});

test("handles tool execution event with failure result that has sessionLog but no error", async () => {
    const completion = completionToChunk(
        inflatePreRecordedCompletion({
            type: "tool-calls",
            toolsToCall: [{ tool: "failing_function", args: {} }],
        }),
    );

    const modelEvent: ModelCallSuccessEvent = {
        kind: "model_call_success",
        turn: 1,
        modelCallDurationMs: 1000,
        modelCall: {
            model: "test-model",
        },
        responseChunk: completion,
        responseUsage: {
            prompt_tokens: 100,
            completion_tokens: 50,
            total_tokens: 150,
        },
    };

    await callback.progress(modelEvent);

    const toolResult: ToolResultExpanded = {
        textResultForLlm: "Function failed text for LLM",
        resultType: "failure",
        // Has sessionLog but no error
        sessionLog: "Function failed session log",
        toolTelemetry: {
            properties: {},
            metrics: {},
        },
    };

    const toolCallId = completion.choices[0].delta.tool_calls![0].id;
    if (!toolCallId) {
        return expect(false, "Tool call ID should not be undefined");
    }
    const toolEvent: ToolExecutionEvent = {
        kind: "tool_execution",
        turn: 1,
        toolCallId,
        toolResult,
        durationMs: 500,
    };

    await callback.progress(toolEvent);

    expect(sessionLogSpy).toHaveBeenCalledTimes(2);

    // Verify the complex session log logic: failure case without error should use sessionLog (not textResultForLlm)
    expect(createChunkSpy).toHaveBeenCalledWith(
        // Should use sessionLog since no error present
        "Function failed session log",
        completion,
        toolCallId,
    );
});

test("handles tool execution event with failure result that has both sessionLog and error", async () => {
    const completion = completionToChunk(
        inflatePreRecordedCompletion({
            type: "tool-calls",
            toolsToCall: [{ tool: "failing_function", args: {} }],
        }),
    );

    const modelEvent: ModelCallSuccessEvent = {
        kind: "model_call_success",
        turn: 1,
        modelCallDurationMs: 1000,
        modelCall: {
            model: "test-model",
        },
        responseChunk: completion,
        responseUsage: {
            prompt_tokens: 100,
            completion_tokens: 50,
            total_tokens: 150,
        },
    };

    await callback.progress(modelEvent);

    const toolResult: ToolResultExpanded = {
        textResultForLlm: "Function failed text for LLM",
        resultType: "failure",
        // Has sessionLog
        sessionLog: "Function failed session log",
        // Also has error
        error: "Critical failure occurred",
        toolTelemetry: {
            properties: {},
            metrics: {},
        },
    };

    const toolCallId = completion.choices[0].delta.tool_calls![0].id;
    if (!toolCallId) {
        return expect(false, "Tool call ID should not be undefined");
    }
    const toolEvent: ToolExecutionEvent = {
        kind: "tool_execution",
        turn: 1,
        toolCallId,
        toolResult,
        durationMs: 500,
    };

    await callback.progress(toolEvent);

    expect(sessionLogSpy).toHaveBeenCalledTimes(2);

    // Verify the complex session log logic: failure case with error should prioritize error over sessionLog
    expect(createChunkSpy).toHaveBeenCalledWith(
        // Should wrap error in tags, ignoring sessionLog
        "<error>Critical failure occurred</error>",
        completion,
        toolCallId,
    );
});

test("handles tool execution event with session log fallback", async () => {
    const completion = completionToChunk(
        inflatePreRecordedCompletion({
            type: "tool-calls",
            toolsToCall: [{ tool: "test_function", args: {} }],
        }),
    );

    const modelEvent: ModelCallSuccessEvent = {
        kind: "model_call_success",
        turn: 1,
        modelCallDurationMs: 1000,
        modelCall: {
            model: "test-model",
        },
        responseChunk: completion,
        responseUsage: {
            prompt_tokens: 100,
            completion_tokens: 50,
            total_tokens: 150,
        },
    };

    await callback.progress(modelEvent);

    // Tool result without sessionLog should fall back to textResultForLlm
    const toolResult: ToolResultExpanded = {
        textResultForLlm: "Function result for LLM",
        resultType: "success",
        toolTelemetry: {
            properties: {},
            metrics: {},
        },
    };

    const toolCallId = completion.choices[0].delta.tool_calls![0].id;
    if (!toolCallId) {
        return expect(false, "Tool call ID should not be undefined");
    }
    const toolEvent: ToolExecutionEvent = {
        kind: "tool_execution",
        turn: 1,
        toolCallId,
        toolResult,
        durationMs: 500,
    };

    await callback.progress(toolEvent);

    expect(sessionLogSpy).toHaveBeenCalledTimes(2);

    // Verify the complex session log logic: success case without sessionLog should fall back to textResultForLlm
    expect(createChunkSpy).toHaveBeenCalledWith(
        // Should fall back to textResultForLlm when sessionLog is undefined
        "Function result for LLM",
        completion,
        toolCallId,
    );
});

test("handles tool execution event for unknown tool call id", async () => {
    const toolResult: ToolResultExpanded = {
        textResultForLlm: "Function result",
        resultType: "success",
        toolTelemetry: {
            properties: {},
            metrics: {},
        },
    };

    const toolEvent: ToolExecutionEvent = {
        kind: "tool_execution",
        turn: 1,
        toolCallId: "unknown-tool-call-id",
        toolResult,
        durationMs: 500,
    };

    await callback.progress(toolEvent);

    expect(warningSpy).toHaveBeenCalledWith("No completion found for tool call ID: unknown-tool-call-id");
    expect(sessionLogSpy).not.toHaveBeenCalled();
});

test("removes tool call from map after processing", async () => {
    const completion = completionToChunk(
        inflatePreRecordedCompletion({
            type: "tool-calls",
            toolsToCall: [{ tool: "test_function", args: {} }],
        }),
    );

    const modelEvent: ModelCallSuccessEvent = {
        kind: "model_call_success",
        turn: 1,
        modelCallDurationMs: 1000,
        modelCall: {
            model: "test-model",
        },
        responseChunk: completion,
        responseUsage: {
            prompt_tokens: 100,
            completion_tokens: 50,
            total_tokens: 150,
        },
    };

    await callback.progress(modelEvent);

    const toolResult: ToolResultExpanded = {
        textResultForLlm: "Function result",
        resultType: "success",
        toolTelemetry: {
            properties: {},
            metrics: {},
        },
    };

    const toolCallId = completion.choices[0].delta.tool_calls![0].id;
    if (!toolCallId) {
        return expect(false, "Tool call ID should not be undefined");
    }
    const toolEvent: ToolExecutionEvent = {
        kind: "tool_execution",
        turn: 1,
        toolCallId,
        toolResult,
        durationMs: 500,
    };

    await callback.progress(toolEvent);

    // Try to process the same tool call again
    await callback.progress(toolEvent);

    expect(warningSpy).toHaveBeenCalledWith(`No completion found for tool call ID: ${toolCallId}`);
    // Only the first two calls
    expect(sessionLogSpy).toHaveBeenCalledTimes(2);
});

test("handles multiple tool calls in same completion", async () => {
    const completion = completionToChunk(
        inflatePreRecordedCompletion({
            type: "tool-calls",
            toolsToCall: [
                { tool: "function_one", args: { param: "value1" } },
                { tool: "function_two", args: { param: "value2" } },
            ],
        }),
    );

    const modelEvent: ModelCallSuccessEvent = {
        kind: "model_call_success",
        turn: 1,
        modelCallDurationMs: 1000,
        modelCall: {
            model: "test-model",
        },
        responseChunk: completion,
        responseUsage: {
            prompt_tokens: 100,
            completion_tokens: 50,
            total_tokens: 150,
        },
    };

    await callback.progress(modelEvent);

    // Process results for both tool calls
    const toolResult1: ToolResultExpanded = {
        textResultForLlm: "Result 1",
        resultType: "success",
        toolTelemetry: { properties: {}, metrics: {} },
    };

    const toolResult2: ToolResultExpanded = {
        textResultForLlm: "Result 2",
        resultType: "success",
        toolTelemetry: { properties: {}, metrics: {} },
    };

    const toolCallId1 = completion.choices[0].delta.tool_calls![0].id;
    if (!toolCallId1) {
        return expect(false, "Tool call ID should not be undefined");
    }
    const toolCallId2 = completion.choices[0].delta.tool_calls![1].id;
    if (!toolCallId2) {
        return expect(false, "Tool call ID should not be undefined");
    }

    await callback.progress({
        kind: "tool_execution",
        turn: 1,
        toolCallId: toolCallId1,
        toolResult: toolResult1,
        durationMs: 500,
    });

    await callback.progress({
        kind: "tool_execution",
        turn: 1,
        toolCallId: toolCallId2,
        toolResult: toolResult2,
        durationMs: 600,
    });

    // Model call + 2 tool results
    expect(sessionLogSpy).toHaveBeenCalledTimes(3);
});

test("ignores log events", async () => {
    const logEvent: AgentCallbackProgressEvent = {
        kind: "log",
        message: "This is a log message",
    };

    await callback.progress(logEvent);

    expect(sessionLogSpy).not.toHaveBeenCalled();
});

test("ignores user message events", async () => {
    const userMessageEvent: AgentCallbackProgressEvent = {
        kind: "message",
        message: {
            role: "user",
            content: "User message",
        },
    };

    await callback.progress(userMessageEvent);

    expect(sessionLogSpy).not.toHaveBeenCalled();
});

test("ignores response events", async () => {
    const responseEvent: AgentCallbackProgressEvent = {
        kind: "response",
        response: {
            role: "assistant",
            content: "Assistant response",
            refusal: null,
        },
    };

    await callback.progress(responseEvent);

    expect(sessionLogSpy).not.toHaveBeenCalled();
});

test("ignores telemetry events", async () => {
    const telemetryEvent: AgentCallbackProgressEvent = {
        kind: "telemetry",
        telemetry: {
            event: "test-event",
            properties: {},
            restrictedProperties: {},
            metrics: {},
        },
    };

    await callback.progress(telemetryEvent);

    expect(sessionLogSpy).not.toHaveBeenCalled();
});

test("partialResult is a noop", async () => {
    const result = await callback.partialResult({
        branchName: "test-branch",
        message: "Test message",
    });

    expect(result).toBeUndefined();
    expect(sessionLogSpy).not.toHaveBeenCalled();
});

test("commentReply is a noop", async () => {
    const result = await callback.commentReply({
        comment_id: 123,
        message: "Test reply",
    });

    expect(result).toBeUndefined();
    expect(sessionLogSpy).not.toHaveBeenCalled();
});

test("result is a noop", async () => {
    const result = await callback.result({
        diff: "test diff",
        branchName: "test-branch",
        prTitle: "Test PR",
        prDescription: "Test description",
    });

    expect(result).toBeUndefined();
    expect(sessionLogSpy).not.toHaveBeenCalled();
});

test("error is a noop", async () => {
    const result = await callback.error({
        text: "Test error",
        name: "TestError",
    });

    expect(result).toBeUndefined();
    expect(sessionLogSpy).not.toHaveBeenCalled();
});
