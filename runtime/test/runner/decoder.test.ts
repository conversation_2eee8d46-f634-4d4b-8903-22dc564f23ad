/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import { readFileSync } from "fs";
import { join as pathJoin } from "path";
import { StandaloneRunner } from "../../src/runner/standalone";

["base64", "gzipped"].forEach((scenario) => {
    it(`should handle ${scenario} problem statement`, () => {
        const filePath = pathJoin(__dirname, `decoder-problem-${scenario.replaceAll(" ", "-")}.txt`);
        testFile(filePath);
    });
});

function testFile(filePath: string) {
    const runner = new StandaloneRunner();
    const fileContents = readFileSync(filePath, "utf8");
    const decoded = runner.decoder.decode(fileContents);
    // Expected length of decoded string is 670000 characters
    expect(decoded.length).toBe(670000);

    // Check that a specific string occurs 10000 times
    const targetString = "problem";
    const occurrences = (decoded.match(new RegExp(targetString, "g")) || []).length;
    expect(occurrences).toBe(10000);
}
