/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import * as fs from "fs";
import * as path from "path";
import { beforeEach, expect, test } from "vitest";
import { TrajectoryFileCallback } from "../../src/callbacks/TrajectoryFileCallback";

const TEST_OUTPUT_DIR = path.join(__dirname, "../../.test-output");
const TEST_TRAJECTORY_FILE = path.join(TEST_OUTPUT_DIR, "test-trajectory.xml");

beforeEach(() => {
    // Set up test directory
    if (!fs.existsSync(TEST_OUTPUT_DIR)) {
        fs.mkdirSync(TEST_OUTPUT_DIR, { recursive: true });
    }
    // Clean up test file from previous runs
    if (fs.existsSync(TEST_TRAJECTORY_FILE)) {
        fs.unlinkSync(TEST_TRAJECTORY_FILE);
    }
});

const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

test("CLI callback saves trajectory to file", async () => {
    const callback = new TrajectoryFileCallback(TEST_TRAJECTORY_FILE);

    // Test tool call
    await callback.progress({
        kind: "message",
        message: {
            role: "assistant",
            reasoning_text: "tool reasoning",
            tool_calls: [
                {
                    id: "test-id",
                    type: "function",
                    function: {
                        name: "test_function",
                        arguments: JSON.stringify({
                            param1: "value1",
                            param2: "value2",
                        }),
                    },
                },
            ],
        },
    });

    // Test tool result
    await callback.progress({
        kind: "message",
        message: {
            role: "tool",
            tool_call_id: "test-id",
            content: "test result",
        },
    });

    // Test assistant message
    await callback.progress({
        kind: "message",
        message: {
            role: "assistant",
            content: "test message",
            reasoning_text: "content reasoning",
        },
    });

    // Close the file and wait a moment to ensure it's flushed
    await callback.result({
        prTitle: "Test PR Title",
        prDescription: "Test PR Description",
        diff: "Test diff content",
        branchName: "test-branch",
    });
    await delay(100); // Short delay to ensure file is closed

    try {
        // Read and validate the trajectory file
        expect(fs.existsSync(TEST_TRAJECTORY_FILE)).toBeTruthy();
        const content = fs.readFileSync(TEST_TRAJECTORY_FILE, "utf-8");

        // Check for function call
        expect(content).toContain("<function_calls>");
        expect(content).toContain('<invoke name="test_function">');
        expect(content).toContain('<parameter name="param1">value1</parameter>');
        expect(content).toContain('<parameter name="param2">value2</parameter>');

        // Check for function result
        expect(content).toContain("<function_results>");
        expect(content).toContain("test result");

        // Check for message
        expect(content).toContain("test message");
        expect(content).toContain("tool reasoning");
        expect(content).toContain("content reasoning");
    } finally {
        // Clean up test file
        if (fs.existsSync(TEST_TRAJECTORY_FILE)) {
            fs.unlinkSync(TEST_TRAJECTORY_FILE);
        }
    }
});
