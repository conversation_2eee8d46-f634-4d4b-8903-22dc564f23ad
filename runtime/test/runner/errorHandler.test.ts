/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import { ExecException } from "child_process";
import { randomUUID } from "crypto";
import { mkdtempSync } from "fs";
import { APIError } from "openai";
import { tmpdir } from "os";
import { join as pathJoin } from "path";
import { SWEBenchVerifiedProblem } from "../../src";
import { AgentUpdate<PERSON>ontentMap, Compound<PERSON><PERSON>back, IAgentCallback } from "../../src/callbacks/callback";
import { LoggerCallback } from "../../src/callbacks/LoggerCallback";
import { ErrorHandler, JobError } from "../../src/errors";
import { CAPIError } from "../../src/model/capi/copilot-client";
import { getSessionError, NoopCopilotSessionsClient } from "../../src/model/capi/sessions-client";
import { LogLevel, RunnerLogger } from "../../src/runner";
import { DefaultExec } from "../../src/runner/exec/default";
import { GitError } from "../../src/runner/git";
import { DefaultGitHandler } from "../../src/runner/git/default";
import { NoopGitHandler, GitHandlerCall } from "../../src/runner/git/noop";
import { getOrInitSettings } from "../../src/settings/factory";
import { AgentCallbackErrorEvent } from "../../src/types";

process.env.GITHUB_SERVER_URL = process.env.GITHUB_SERVER_URL ? process.env.GITHUB_SERVER_URL : "https://github.com";
process.env.COPILOT_AGENT_COMMIT_LOGIN = process.env.COPILOT_AGENT_COMMIT_LOGIN
    ? process.env.COPILOT_AGENT_COMMIT_LOGIN
    : "CloudColonel";
process.env.COPILOT_AGENT_COMMIT_EMAIL = process.env.COPILOT_AGENT_COMMIT_EMAIL
    ? process.env.COPILOT_AGENT_COMMIT_EMAIL
    : "<EMAIL>";
process.env.GITHUB_TOKEN = process.env.GITHUB_TOKEN ? process.env.GITHUB_TOKEN : "ghu_notreallyatoken";

let output = "";

class OutputLogger implements RunnerLogger {
    log(message: string): void {
        output += message + "\n";
    }
    debug(message: string): void {
        output += "[DEBUG] " + message + "\n";
    }
    info(message: string): void {
        output += "[INFO] " + message + "\n";
    }
    notice(message: string | Error): void {
        output += "[NOTICE] " + (message instanceof Error ? message.message : message) + "\n";
    }
    warning(message: string | Error): void {
        output += "[WARNING] " + (message instanceof Error ? message.message : message) + "\n";
    }
    error(message: string | Error): void {
        output += "[ERROR] " + (message instanceof Error ? message.message : message) + "\n";
    }
    startGroup(name: string, _level?: LogLevel): void {
        output += "[GROUP START] " + name + "\n";
    }
    endGroup(_level?: LogLevel): void {
        output += "[GROUP END]\n";
    }
    isDebug(): boolean {
        return false;
    }
}
const outputLogger = new OutputLogger();
const exec = new DefaultExec(outputLogger);

let errorForSession: Error | null = null;
let sessionErrorObject: { message: string; code: string } | null = null;
let sessionNonCompletionContent = "";
class TestCopilotSessionsClient extends NoopCopilotSessionsClient {
    public async error(_error: Error): Promise<void> {
        errorForSession = _error;
        sessionErrorObject = getSessionError(_error);
    }
    async logNonCompletionContent(content: string): Promise<void> {
        sessionNonCompletionContent += content;
    }
}
const sessionClient = new TestCopilotSessionsClient();

type RecordedType = { kind: keyof AgentUpdateContentMap; content: unknown };
let recorded: RecordedType[] = [];
const recordAllUpdatesCallback: IAgentCallback = {
    progress: function (content): Promise<void> {
        recorded.push({ kind: "progress", content });
        return Promise.resolve();
    },
    partialResult: function (content): Promise<void> {
        recorded.push({ kind: "partialResult", content });
        return Promise.resolve();
    },
    commentReply: function (reply): Promise<void> {
        recorded.push({ kind: "commentReply", content: reply });
        return Promise.resolve();
    },
    result: function (result): Promise<void> {
        recorded.push({ kind: "result", content: result });
        return Promise.resolve();
    },
    error: function (error): Promise<void> {
        recorded.push({ kind: "error", content: error });
        return Promise.resolve();
    },
};

const callback = new CompoundCallback()
    .addCallback(new LoggerCallback(outputLogger.log.bind(outputLogger)))
    .addCallback(recordAllUpdatesCallback);

// Helper function to assert that recorded content is an error event
function asErrorEvent(recorded: RecordedType): AgentCallbackErrorEvent {
    expect(recorded.kind).toBe("error");
    return recorded.content as AgentCallbackErrorEvent;
}

// Helper function to assert and return typed git handler call data
function expectGitCall<T extends GitHandlerCall[0]>(
    gitHandler: NoopGitHandler,
    index: number,
    expectedMethod: T,
): Extract<GitHandlerCall, [T, unknown]>[1] {
    expect(gitHandler.calls[index][0]).toBe(expectedMethod);
    return gitHandler.calls[index][1] as Extract<GitHandlerCall, [T, unknown]>[1];
}

// Note: This is expected to fail, so we don't need to skip this during CI.
const tmpDir = mkdtempSync(pathJoin(tmpdir(), "sweagentd-error-handler-test-"));
const branchName = randomUUID();
const problem = {
    repo: "test-repo",
    branchName: branchName,
    repoLocation: tmpDir,
    base_commit: "commit",
    issue_number: 1,
    problem_statement: "Do a thing",
    job_id: "job-id",
    push: true,
    callback: callback,
} satisfies SWEBenchVerifiedProblem;

describe("handles errors", () => {
    beforeEach(() => {
        output = "";
        recorded = [];
        errorForSession = null;
        sessionNonCompletionContent = "";
    });

    it("should handle a simple error", async () => {
        const settings = await getOrInitSettings();
        const gitHandler = new NoopGitHandler();
        const errorHandler = new ErrorHandler(settings, outputLogger, problem, gitHandler, sessionClient);
        const err = new Error("Test error");
        await errorHandler.handleError(err);
        expect(output).toContain("[ERROR] Error Test error");
        expect(recorded).toBeDefined();
        const errorEvent = asErrorEvent(recorded[0]);
        expect(errorEvent.name).toBe("Error");
        expect(errorEvent.text).toBe("Test error");
    });

    it("should push changes before error occurred", async () => {
        const settings = await getOrInitSettings();
        const gitHandler = new NoopGitHandler();
        const errorHandler = new ErrorHandler(settings, outputLogger, problem, gitHandler, sessionClient);
        const err = new Error("Test error");
        await errorHandler.handleError(err);
        expect(output).toContain("[ERROR] Error Test error");
        expect(recorded).toBeDefined();
        const errorEvent = asErrorEvent(recorded[0]);
        expect(errorEvent.name).toBe("Error");
        expect(errorEvent.text).toBe("Test error");
        expect(gitHandler.calls.length).toBeGreaterThan(0);
        const callData = expectGitCall(gitHandler, 0, "commitAndPushChanges");
        expect(callData.branchName).toBe(problem.branchName);
        expect(callData.repoLocation).toBe(problem.repoLocation);
        expect(callData.commitMessage).toBe("Changes before error encountered");
        expect(callData.noVerify).toBe(true);
    });

    it("should handle error with time unit", async () => {
        const settings = await getOrInitSettings();
        const gitHandler = new NoopGitHandler();
        const errorHandler = new ErrorHandler(settings, outputLogger, problem, gitHandler, sessionClient);
        const err = new Error("Test error: 1 second, 2 mins, 3 hours and will repeat 4 times");
        await errorHandler.handleError(err);
        expect(output).toContain("[ERROR] Error Test error: 1 second, 2 mins, 3 hours");
        expect(recorded).toBeDefined();
        const errorEvent = asErrorEvent(recorded[0]);
        expect(errorEvent.name).toBe("Error");
        // Since text is used to classify errors, the numbers should have been removed
        expect(errorEvent.text).toBe("Test error: n second, n mins, n hours and will repeat n times");
        expect(gitHandler.calls.length).toBeGreaterThan(0);
        const callData = expectGitCall(gitHandler, 0, "commitAndPushChanges");
        expect(callData.branchName).toBe(problem.branchName);
        expect(callData.repoLocation).toBe(problem.repoLocation);
        expect(callData.commitMessage).toBe("Changes before error encountered");
        expect(callData.noVerify).toBe(true);
    });

    it("should handle an ExecException", async () => {
        const settings = await getOrInitSettings();
        const gitHandler = new NoopGitHandler();
        const errorHandler = new ErrorHandler(settings, outputLogger, problem, gitHandler, sessionClient);
        let execException: ExecException | undefined;
        try {
            await exec.execReturn(
                "random-command-that-should-fail",
                ["arg-should-filter", "arg-should-stay", "arg-should-filter-2"],
                { shell: "bash", silent: true },
                ["arg-should-filter", "arg-should-filter-2"],
            );
        } catch (err) {
            execException = err as ExecException;
        }
        expect(execException).toBeDefined();
        await errorHandler.handleError(execException);
        const expectedMessage = `Command failed with exit code 127: random-command-that-should-fail REDACTED arg-should-stay REDACTED`;
        const expectedStdOut = "";
        const expectedStdErr = `random-command-that-should-fail: command not found`;
        expect(output).toContain(expectedMessage);
        expect(output).toContain(expectedStdErr);
        expect(recorded).toBeDefined();
        const errorEvent = asErrorEvent(recorded[0]);
        expect(errorEvent.cmd).toContain("arg-should-stay");
        expect(errorEvent.cmd).not.toContain("arg-should-filter");
        expect(errorEvent.cmd).not.toContain("arg-should-filter-2");
        expect(errorEvent.name).toBe("Error");
        expect(errorEvent.text).toBe(expectedMessage);
        expect(errorEvent.stdout ?? "").toContain(expectedStdOut);
        expect(errorEvent.stderr ?? "").toContain(expectedStdErr);
        expect(errorEvent.skipReport).toBe(false);
        expect(errorForSession).toBeDefined();
        expect(errorForSession?.message).toContain(expectedMessage);
        expect(sessionNonCompletionContent).toContain(
            "<error>$ random-command-that-should-fail REDACTED arg-should-stay REDACTED\n",
        );
        expect(sessionErrorObject).toBeNull();
        expect(gitHandler.calls.length).toBeGreaterThan(0);
        const callData = expectGitCall(gitHandler, 0, "commitAndPushChanges");
        expect(callData.branchName).toBe(problem.branchName);
        expect(callData.repoLocation).toBe(problem.repoLocation);
        expect(callData.commitMessage).toBe("Changes before error encountered");
        expect(callData.noVerify).toBe(true);
    });

    it("should handle an GitException", async () => {
        const settings = await getOrInitSettings();
        const gitHandler = new DefaultGitHandler(outputLogger, exec);
        const errorHandler = new ErrorHandler(settings, outputLogger, problem, gitHandler, sessionClient);
        let gitError: GitError | undefined;
        try {
            await gitHandler.cloneRepo(settings, "repo-that-should-be-filtered", tmpDir, branchName, 1, "main");
        } catch (err) {
            gitError = err as GitError;
        }
        expect(gitError).toBeDefined();
        // Test w/o falling back to pushing since we'll test that part later
        await errorHandler.handleError(gitError);
        const expectedMessage = `unknown git error: Command failed with exit code 128: git clone REDACTED REDACTED`;
        const expectedStdOut = "";
        const expectedStdErr = `Cloning into '${tmpDir}'...\nremote: Not Found\nfatal: repository 'https://github.com/repo-that-should-be-filtered/' not found`;
        expect(output).toContain(expectedMessage);
        expect(output).toContain(expectedStdErr);
        expect(recorded).toBeDefined();
        const errorEvent0 = asErrorEvent(recorded[0]);
        expect(errorEvent0.cmd).toContain("clone");
        expect(errorEvent0.cmd).not.toContain(branchName);
        expect(errorEvent0.cmd).not.toContain(tmpDir);
        expect(errorEvent0.name).toBe("GitError");
        expect(errorEvent0.text).toBe(expectedMessage);
        expect(errorEvent0.stdout ?? "").toContain(expectedStdOut);
        expect(errorEvent0.stderr ?? "").toContain(expectedStdErr);
        expect(errorEvent0.skipReport).toBe(false);
        expect(sessionNonCompletionContent).toContain("<error>$ git clone REDACTED REDACTED\n");
        const errorEvent1 = asErrorEvent(recorded[1]);
        expect(errorEvent1.cmd).toContain("checkout");
        expect(errorEvent1.cmd).not.toContain(branchName);
        expect(errorEvent1.cmd).not.toContain(tmpDir);
        expect(errorEvent1.name).toBe("GitError");
        expect(errorEvent1.skipReport).toBe(false);
        expect(sessionNonCompletionContent).toContain("<error>$ git checkout REDACTED\n"); // This is also expected due to the attempt commit/push --no-verify
        expect(sessionErrorObject?.message).toBe(
            "Copilot encountered an unknown Git error. If the problem persists, please contact GitHub Support. To retry, leave a comment on this pull request asking Copilot to try again.",
        );
        expect(errorForSession).toBeDefined();
        expect(errorForSession?.message).toContain(expectedMessage);
    });

    it(`should include fields an error's cause if the cause is a CAPI error`, async () => {
        const settings = await getOrInitSettings();
        const gitHandler = new NoopGitHandler();
        const errorHandler = new ErrorHandler(settings, outputLogger, problem, gitHandler, sessionClient);
        const capiError = CAPIError.fromAPIError(
            new APIError(500, new Error("Model not found"), "The requested model does not exist", {
                "x-github-request-id": "ghr_12345",
                "x-request-id": "req_67890",
            }),
        );

        // Create a test object with additional properties that should be ignored
        const capiErrorWithExtraProps = capiError as CAPIError & {
            cmd: string;
            stdout: string;
            stderr: string;
            signal: string;
        };
        capiErrorWithExtraProps.cmd = "This should not be picked up by the error handler";
        capiErrorWithExtraProps.stdout = "This should not be picked up by the error handler";
        capiErrorWithExtraProps.stderr = "This should not be picked up by the error handler";
        capiErrorWithExtraProps.signal = "This should not be picked up by the error handler";

        const err = new Error("Test error", { cause: capiError });
        await errorHandler.handleError(err);
        expect(output).toContain("[ERROR] Error Test error");
        expect(recorded).toBeDefined();
        const errorEvent = asErrorEvent(recorded[0]);
        expect(errorEvent.name).toBe("Error");
        expect(errorEvent.text).toBe("Test error");
        expect(errorEvent.request_id).toBe(capiError?.request_id);
        expect(errorEvent.ghRequestId).toBe(capiError?.ghRequestId);
        expect(errorEvent.code).toBe(capiError?.code);

        expect(errorEvent.cmd).toBe(undefined);
        expect(errorEvent.stdout).toBe(undefined);
        expect(errorEvent.stderr).toBe(undefined);
        expect(errorEvent.signal).toBe(undefined);

        expect(sessionErrorObject).toBeDefined();
        expect(sessionErrorObject?.code).toBe(new JobError("capi", "500").toString());
    });
});
