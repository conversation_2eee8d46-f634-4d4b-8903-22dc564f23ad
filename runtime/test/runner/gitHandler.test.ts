/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import { randomUUID } from "crypto";
import { chmodSync, existsSync, mkdirSync, mkdtempSync, rmSync, writeFileSync } from "fs";
import { tmpdir } from "os";
import { join as pathJoin } from "path";
import { afterAll, beforeAll, beforeEach, describe, expect, it } from "vitest";
import { RunnerExec } from "../../src/runner/exec";
import { GitError, GitErrorType, GitHandler } from "../../src/runner/git";
import { bashCredHelper } from "../../src/runner/git/default";
import { RunnerLogger } from "../../src/runner/logger";
import { StandaloneRunner } from "../../src/runner/standalone";
import { initSettings, RuntimeSettings } from "../../src/settings";

let settings: RuntimeSettings;
let runner: StandaloneRunner;
let exec: RunnerExec;
let gitHandler: GitHandler;
let logger: RunnerLogger;
let tmpDir: string;
let branchName: string;
let testRepo: string;
let testRepoUrl: string;
let testRepoUrlNoToken: string;

process.env.COPILOT_AGENT_COMMIT_LOGIN = process.env.COPILOT_AGENT_COMMIT_LOGIN
    ? process.env.COPILOT_AGENT_COMMIT_LOGIN
    : "CloudColonel";
process.env.COPILOT_AGENT_COMMIT_EMAIL = process.env.COPILOT_AGENT_COMMIT_EMAIL
    ? process.env.COPILOT_AGENT_COMMIT_EMAIL
    : "<EMAIL>";

// This method is used to set up different tests
async function cloneForTest(depth?: number): Promise<void> {
    await exec.execReturn(
        "git",
        depth ? ["clone", "--depth", depth.toString(), testRepoUrl, tmpDir] : ["clone", testRepoUrl, tmpDir],
        { silent: true },
    );
    await exec.exec("git", ["config", "user.name", settings.github?.user?.name ?? "CloudColonel"], {
        cwd: tmpDir,
        silent: true,
    });
    await exec.exec(
        "git",
        ["config", "user.email", settings.github?.user?.email ?? "<EMAIL>"],
        { cwd: tmpDir, silent: true },
    );
}

async function checkGitConfig(): Promise<void> {
    const { stdout: config } = await exec.execReturn("git", ["config", "list", "--local"], {
        cwd: tmpDir,
        silent: true,
    });
    expect(config).toContain(`remote.origin.url=${testRepoUrlNoToken}`);
    expect(config).toContain(`user.name=${process.env.COPILOT_AGENT_COMMIT_LOGIN}`);
    expect(config).toContain(`user.email=${process.env.COPILOT_AGENT_COMMIT_EMAIL}`);
    expect(config).toContain(`credential.username=${process.env.COPILOT_AGENT_COMMIT_LOGIN}`);
    expect(config).toContain(`credential.helper=${bashCredHelper}`);
}

describe.skipIf(
    process.env.DISABLE_GIT_HANDLER_TESTS === "true" ||
        !process.env.GIT_HANDLER_TEST_GITHUB_TOKEN ||
        process.env.GIT_HANDLER_TEST_GITHUB_TOKEN === "",
)(
    "git",
    async () => {
        beforeAll(async () => {
            if (!process.env.GIT_HANDLER_TEST_GITHUB_TOKEN || process.env.GIT_HANDLER_TEST_GITHUB_TOKEN === "") {
                return;
            }
            process.env.GITHUB_TOKEN = process.env.GIT_HANDLER_TEST_GITHUB_TOKEN;
            settings = await initSettings();
            runner = new StandaloneRunner();
            exec = runner.exec;
            gitHandler = runner.git;
            logger = runner.logger;

            const token = settings.github?.token;
            if (!token) {
                throw new Error("GitHub token not defined via settings");
            }

            testRepo = process.env.GIT_HANDLER_TEST_REPO ?? "ghcpd/runtime-git-test-repo";
            testRepoUrl = `https://${settings.github?.user?.name ?? "CloudColonel"}:${token}@${settings.github?.host ?? "github.com"}/${testRepo}.git`;
            testRepoUrlNoToken = `https://${settings.github?.host ?? "github.com"}/${testRepo}`;

            tmpDir = mkdtempSync(pathJoin(tmpdir(), "sweagentd-git-handler-test-"));
            branchName = `test-branch-${randomUUID().toString()}`;

            logger.info(`\n** Creating test branch ${branchName} with 5 commits **`);
            // Set up the test branch
            await cloneForTest(1);
            await exec.execReturn("git", ["branch", branchName], {
                cwd: tmpDir,
                silent: true,
            });
            await exec.execReturn("git", ["checkout", branchName], {
                cwd: tmpDir,
                silent: true,
            });
            // Add 5 commits
            for (let i = 0; i < 5; i++) {
                writeFileSync(pathJoin(tmpDir, `test-file-${i}.txt`), "This is a test file");
                await exec.execReturn("git", ["add", `test-file-${i}.txt`], {
                    cwd: tmpDir,
                    silent: true,
                });
                await exec.execReturn("git", ["commit", "-m", `Add test file ${i}`], { cwd: tmpDir, silent: true });
                await exec.execReturn("git", ["push", "origin", branchName], {
                    cwd: tmpDir,
                    silent: true,
                });
            }
            // Clean out tmpDir for tests
            rmSync(tmpDir, { recursive: true });
            mkdirSync(tmpDir);
        });

        beforeEach(() => {
            if (existsSync(tmpDir)) {
                rmSync(tmpDir, { recursive: true });
                mkdirSync(tmpDir);
            }
        });

        afterAll(async () => {
            logger.info(`\n** Cleaning up branch ${branchName} and temporary directory ${tmpDir} **`);
            // Create a temporary git directory for cleanup operations
            const cleanupDir = mkdtempSync(pathJoin(tmpdir(), "sweagentd-git-cleanup-"));
            try {
                // Clone repo to cleanup directory for git operations
                await exec.execReturn("git", ["clone", "--depth", "1", testRepoUrl, cleanupDir], { silent: true });
                await exec.exec("git", ["config", "user.name", settings.github?.user?.name ?? "CloudColonel"], {
                    cwd: cleanupDir,
                    silent: true,
                });
                await exec.exec(
                    "git",
                    [
                        "config",
                        "user.email",
                        settings.github?.user?.email ?? "<EMAIL>",
                    ],
                    { cwd: cleanupDir, silent: true },
                );

                // Delete the test branch
                for (let i = 0; i <= 12; i++) {
                    const branch = i > 0 ? branchName + "-" + i : branchName;
                    try {
                        const { stdout } = await exec.execReturn("git", ["ls-remote", "--heads", "origin", branch], {
                            cwd: cleanupDir,
                            silent: true,
                        });
                        if (stdout.toString().trim().length > 0) {
                            await exec.execReturn("git", ["push", "origin", "--delete", branch], {
                                cwd: cleanupDir,
                                silent: true,
                            });
                        }
                    } catch (error) {
                        // Branch doesn't exist or other error, continue with cleanup
                        logger.debug(`Error cleaning up branch ${branch}: ${error}`);
                    }
                }
            } finally {
                // Clean up both directories
                if (existsSync(tmpDir)) {
                    rmSync(tmpDir, { recursive: true });
                }
                if (existsSync(cleanupDir)) {
                    rmSync(cleanupDir, { recursive: true });
                }
            }
        }, 20 * 1000);

        describe("clone", async () => {
            it("should clone depth of 2 by default", async () => {
                logger.info(`\n** Testing ${testRepo} in ${tmpDir} **`);
                await gitHandler.cloneRepo(settings!, testRepo, tmpDir, branchName);
                const { stdout } = await exec.execReturn("git", ["rev-list", "--count", "HEAD"], {
                    cwd: tmpDir,
                    silent: true,
                });
                expect(stdout.trim()).toBe("2");
                await checkGitConfig();
            });

            it("should support different clone depth", async () => {
                logger.info(`\n** Testing ${testRepo} in ${tmpDir} and with depth 3 **`);
                await gitHandler.cloneRepo(settings, testRepo, tmpDir, branchName, 3);
                const { stdout } = await exec.execReturn("git", ["rev-list", "--count", "HEAD"], {
                    cwd: tmpDir,
                    silent: true,
                });
                // Actual cloned depth should be commit count + 1 to get the commit before the PR was created
                expect(stdout.trim()).toBe("4");
                await checkGitConfig();
            });

            it("should create branch from ref", async () => {
                logger.info(`\n** Testing ${testRepo} in ${tmpDir} and checking out ${branchName + "-1"} **`);
                await gitHandler.cloneRepo(settings, testRepo, tmpDir, branchName + "-1", 2, "main");
                const { stdout } = await exec.execReturn("git", ["rev-parse", "--abbrev-ref", "HEAD"], {
                    cwd: tmpDir,
                    silent: true,
                });
                expect(stdout.trim()).toBe(branchName + "-1");
                await checkGitConfig();
            });
        });

        describe("existing clone", () => {
            it("should checkout existing local branch", async () => {
                // Set up local branch
                await cloneForTest();
                await exec.execReturn("git", ["branch", branchName + "-2"], {
                    cwd: tmpDir,
                    silent: true,
                });
                await exec.execReturn("git", ["push", "origin", branchName + "-2"], { cwd: tmpDir, silent: true });
                await exec.execReturn("git", ["checkout", branchName], {
                    cwd: tmpDir,
                    silent: true,
                });

                // Run actual test
                logger.info(`\n** Testing ${testRepo} in ${tmpDir} and checking out ${branchName + "-2"} **`);
                await gitHandler.cloneRepo(settings, testRepo, tmpDir, branchName + "-2");
                const { stdout } = await exec.execReturn("git", ["rev-parse", "--abbrev-ref", "HEAD"], {
                    cwd: tmpDir,
                    silent: true,
                });
                expect(stdout.trim()).toBe(branchName + "-2");
                await checkGitConfig();
            });

            it("should pull existing remote branch", async () => {
                // Set up remote branch, make sure it isn't local
                await cloneForTest();
                await exec.execReturn("git", ["checkout", "-b", branchName + "-3"], { cwd: tmpDir, silent: true });
                await exec.execReturn("git", ["push", "origin", branchName + "-3"], { cwd: tmpDir, silent: true });
                rmSync(tmpDir, { recursive: true });
                mkdirSync(tmpDir);
                await cloneForTest(1);

                // Run actual test
                logger.info(`\n** Testing ${testRepo} in ${tmpDir} and checking out ${branchName + "-3"} **`);
                await gitHandler.cloneRepo(settings, testRepo, tmpDir, branchName + "-3");
                const { stdout } = await exec.execReturn("git", ["rev-parse", "--abbrev-ref", "HEAD"], {
                    cwd: tmpDir,
                    silent: true,
                });
                expect(stdout.trim()).toBe(branchName + "-3");
                await checkGitConfig();
            });

            it("should create branch from ref (already cloned)", async () => {
                // Set up local branch
                await cloneForTest(1);

                // Run actual test
                logger.info(`\n** Testing ${testRepo} in ${tmpDir} and checking out ${branchName + "-4"} **`);
                const { stdout: ref } = await exec.execReturn("git", ["rev-parse", "HEAD"], {
                    cwd: tmpDir,
                    silent: true,
                });
                await gitHandler.cloneRepo(settings, testRepo, tmpDir, branchName + "-4", 2, ref.trim());
                const { stdout } = await exec.execReturn("git", ["rev-parse", "--abbrev-ref", "HEAD"], {
                    cwd: tmpDir,
                    silent: true,
                });
                expect(stdout.trim()).toBe(branchName + "-4");
                await checkGitConfig();
            });

            it("should set git config on existing branch", async () => {
                // Set up local branch
                await cloneForTest();
                await exec.execReturn("git", ["branch", branchName + "-7"], {
                    cwd: tmpDir,
                    silent: true,
                });
                await exec.execReturn("git", ["push", "origin", branchName + "-7"], { cwd: tmpDir, silent: true });
                await exec.execReturn("git", ["checkout", branchName + "-7"], {
                    cwd: tmpDir,
                    silent: true,
                });

                // Run actual test
                logger.info(`\n** Testing ${testRepo} in ${tmpDir} and checking config for ${branchName + "-7"} **`);
                await gitHandler.cloneRepo(settings, testRepo, tmpDir, branchName + "-7");
                const { stdout } = await exec.execReturn("git", ["rev-parse", "--abbrev-ref", "HEAD"], {
                    cwd: tmpDir,
                    silent: true,
                });
                expect(stdout.trim()).toBe(branchName + "-7");
                await checkGitConfig();
            });
        });

        describe("commit and push", () => {
            it("should push", async () => {
                // Set up local branch
                const testFilePath = pathJoin(tmpDir, `push-me-${randomUUID().toString()}.txt`);
                await cloneForTest();
                await exec.execReturn("git", ["checkout", "-b", branchName + "-5"], { cwd: tmpDir, silent: true });
                writeFileSync(testFilePath, "This is a test file");

                // Run actual test
                logger.info(`\n** Testing ${testRepo} in ${tmpDir} and pushing ${branchName + "-5"} **`);
                await gitHandler.commitAndPushChanges(settings, branchName + "-5", tmpDir, "Add push-me.txt", false);
                rmSync(tmpDir, { recursive: true });
                mkdirSync(tmpDir);
                await cloneForTest();
                await exec.execReturn("git", ["checkout", branchName + "-5"], {
                    cwd: tmpDir,
                    silent: true,
                });
                expect(existsSync(testFilePath)).toBeTruthy();
            });

            it("should push with --no-verify", async () => {
                // Set up local branch
                const testFilePath = pathJoin(tmpDir, `push-me-${randomUUID().toString()}.txt`);
                await cloneForTest();
                await exec.execReturn("git", ["checkout", "-b", branchName + "-11"], { cwd: tmpDir, silent: true });
                writeFileSync(testFilePath, "This is a test file");
                // Add git hooks that will always fail to verify things work as expected
                writeFileSync(
                    pathJoin(tmpDir, ".git", "hooks", "pre-commit"),
                    '#!/bin/sh\necho "This is a pre-commit hook that fails"\nexit 1',
                );
                chmodSync(pathJoin(tmpDir, ".git", "hooks", "pre-commit"), 0o755);
                writeFileSync(
                    pathJoin(tmpDir, ".git", "hooks", "pre-push"),
                    '#!/bin/sh\necho "This is a pre-push hook that fails"\nexit 1',
                );
                chmodSync(pathJoin(tmpDir, ".git", "hooks", "pre-push"), 0o755);

                // Run actual test
                logger.info(
                    `\n** Testing ${testRepo} in ${tmpDir} and pushing ${branchName + "-11"} with --no-verify **`,
                );
                // Should fail
                let passed = false;
                try {
                    await gitHandler.commitAndPushChanges(
                        settings,
                        branchName + "-11",
                        tmpDir,
                        "Add push-me.txt",
                        false,
                        false,
                    );
                } catch {
                    passed = true;
                }
                expect(passed).toBeTruthy();
                // Now commit and push with --no-verify
                await gitHandler.commitAndPushChanges(
                    settings,
                    branchName + "-11",
                    tmpDir,
                    "Add push-me.txt",
                    false,
                    true,
                );
                // Verify it actually pushed
                rmSync(tmpDir, { recursive: true });
                mkdirSync(tmpDir);
                await cloneForTest();
                await exec.execReturn("git", ["checkout", branchName + "-11"], {
                    cwd: tmpDir,
                    silent: true,
                });
                expect(existsSync(testFilePath)).toBeTruthy();
            });

            it.each([{ push: false }, { push: true }])(
                "should commit and add co-author to commit message",
                async ({ push }) => {
                    // Set up local branch
                    const testFilePath = pathJoin(tmpDir, `co-author-${randomUUID().toString()}.txt`);
                    await cloneForTest();
                    await exec.execReturn("git", ["checkout", "-b", branchName + "-8"], { cwd: tmpDir, silent: true });
                    writeFileSync(testFilePath, "This is a test file");

                    // Add actor info to settings
                    settings.github = {
                        ...settings.github,
                        user: {
                            ...settings.github?.user,
                            actorId: 12345,
                            actorLogin: "test-user",
                        },
                    };

                    // Run actual test
                    const gitAction = push ? gitHandler.commitAndPushChanges : gitHandler.commitChanges;
                    await gitAction.bind(gitHandler)(
                        settings,
                        branchName + "-8",
                        tmpDir,
                        "Add co-author test file",
                        false,
                    );

                    // Check the commit message
                    const { stdout } = await exec.execReturn("git", ["log", "-1", "--format=%B"], {
                        cwd: tmpDir,
                        silent: true,
                    });
                    expect(stdout).toContain("Co-authored-by: test-user <<EMAIL>>");
                },
            );

            it.each([{ push: false }, { push: true }])(
                "should commit and add co-author to commit message",
                async ({ push }) => {
                    // Set up local branch
                    const testFilePath = pathJoin(tmpDir, `co-author-${randomUUID().toString()}.txt`);
                    await cloneForTest();
                    await exec.execReturn("git", ["checkout", "-b", branchName + "-8"], { cwd: tmpDir, silent: true });
                    writeFileSync(testFilePath, "This is a test file");

                    // Add actor info to settings
                    settings.github = {
                        ...settings.github,
                        user: {
                            ...settings.github?.user,
                            actorId: 12345,
                            actorLogin: "test-user",
                        },
                    };

                    // Run actual test
                    const gitAction = push ? gitHandler.commitAndPushChanges : gitHandler.commitChanges;
                    await gitAction.bind(gitHandler)(
                        settings,
                        branchName + "-8",
                        tmpDir,
                        "Add co-author test file",
                        false,
                    );

                    // Check the commit message
                    const { stdout } = await exec.execReturn("git", ["log", "-1", "--format=%B"], {
                        cwd: tmpDir,
                        silent: true,
                    });
                    expect(stdout).toContain("Co-authored-by: test-user <<EMAIL>>");
                },
            );

            it("should push with rebase", async () => {
                // Set up local branch
                const testFilePath1 = pathJoin(tmpDir, `push-me-${randomUUID().toString()}.txt`);
                const testFilePath2 = pathJoin(tmpDir, `push-me-${randomUUID().toString()}.txt`);
                await cloneForTest();
                await exec.execReturn("git", ["checkout", "-b", branchName + "-6"], { cwd: tmpDir, silent: true });
                writeFileSync(testFilePath1, "This is a test file");
                await exec.execReturn("git", ["add", testFilePath1], {
                    cwd: tmpDir,
                    silent: true,
                });
                await exec.execReturn("git", ["commit", "-m", "Add " + testFilePath1], { cwd: tmpDir, silent: true });
                await exec.execReturn("git", ["push", "origin", branchName + "-6"], { cwd: tmpDir, silent: true });
                await exec.execReturn("git", ["reset", "--hard", "HEAD~1"], {
                    cwd: tmpDir,
                    silent: true,
                });
                writeFileSync(testFilePath2, "This is a test file");

                // Run actual test
                logger.info(`\n** Testing ${testRepo} in ${tmpDir} and pushing ${branchName + "-6"} **`);
                await gitHandler.commitAndPushChanges(settings, branchName + "-6", tmpDir, "More push-me.txt", false);
                rmSync(tmpDir, { recursive: true });
                mkdirSync(tmpDir);
                await cloneForTest();
                await exec.execReturn("git", ["checkout", branchName + "-6"], {
                    cwd: tmpDir,
                    silent: true,
                });
                expect(existsSync(testFilePath1)).toBeTruthy();
                expect(existsSync(testFilePath2)).toBeTruthy();
            });

            it("should return formatted command output", async () => {
                // Set up local branch
                const testFilePath = pathJoin(tmpDir, `output-${randomUUID().toString()}.txt`);
                await cloneForTest();
                await exec.execReturn("git", ["checkout", "-b", branchName + "-9"], { cwd: tmpDir, silent: true });
                writeFileSync(testFilePath, "This is a test file");

                // Run actual test
                const output = await gitHandler.commitAndPushChanges(
                    settings,
                    branchName + "-9",
                    tmpDir,
                    "Test command output",
                    false,
                );

                // Verify output format
                expect(output).toContain("$ git add .");
                expect(output).toContain("$ git commit -m");
                expect(output).toContain("$ git push -v origin");
                expect(output.split("\n").length).toBeGreaterThan(3); // Should have commands and their output
            });
        });

        describe("getChangedPaths", () => {
            it("should return changed file paths between commits", async () => {
                // Set up local branch with multiple commits
                const testFile1 = pathJoin(tmpDir, `changed-file-1-${randomUUID().toString()}.txt`);
                const testFile2 = pathJoin(tmpDir, `changed-file-2-${randomUUID().toString()}.txt`);
                const testFile3 = pathJoin(tmpDir, `unchanged-file-${randomUUID().toString()}.txt`);

                await cloneForTest();
                await exec.execReturn("git", ["checkout", "-b", branchName + "-12"], { cwd: tmpDir, silent: true });

                // Create initial commit with all files
                writeFileSync(testFile1, "Initial content 1");
                writeFileSync(testFile2, "Initial content 2");
                writeFileSync(testFile3, "Unchanged content");
                await exec.execReturn("git", ["add", "."], {
                    cwd: tmpDir,
                    silent: true,
                });
                await exec.execReturn("git", ["commit", "-m", "Initial commit with all files"], {
                    cwd: tmpDir,
                    silent: true,
                });
                const { stdout: baseCommit } = await exec.execReturn("git", ["rev-parse", "HEAD"], {
                    cwd: tmpDir,
                    silent: true,
                });

                // Make changes to only some files
                writeFileSync(testFile1, "Modified content 1");
                writeFileSync(testFile2, "Modified content 2");
                // testFile3 remains unchanged
                await exec.execReturn("git", ["add", "."], {
                    cwd: tmpDir,
                    silent: true,
                });
                await exec.execReturn("git", ["commit", "-m", "Modify some files"], { cwd: tmpDir, silent: true });
                const { stdout: recentCommit } = await exec.execReturn("git", ["rev-parse", "HEAD"], {
                    cwd: tmpDir,
                    silent: true,
                });

                // Test getChangedPaths
                const changedPaths = await gitHandler.getChangedPaths(tmpDir, recentCommit.trim(), baseCommit.trim());

                // Verify results
                expect(changedPaths).toHaveLength(2);
                expect(changedPaths).toContain(testFile1.replace(tmpDir + "/", ""));
                expect(changedPaths).toContain(testFile2.replace(tmpDir + "/", ""));
                expect(changedPaths).not.toContain(testFile3.replace(tmpDir + "/", ""));
            });

            it("should return empty array when no changes between commits", async () => {
                await cloneForTest();
                await exec.execReturn("git", ["checkout", "-b", branchName + "-13"], { cwd: tmpDir, silent: true });

                // Get the same commit hash twice
                const { stdout: commitHash } = await exec.execReturn("git", ["rev-parse", "HEAD"], {
                    cwd: tmpDir,
                    silent: true,
                });

                // Test getChangedPaths with same commit
                const changedPaths = await gitHandler.getChangedPaths(tmpDir, commitHash.trim(), commitHash.trim());

                // Should return empty array
                expect(changedPaths).toHaveLength(0);
            });

            it("should handle file deletions and additions", async () => {
                const testFileToDelete = pathJoin(tmpDir, `file-to-delete-${randomUUID().toString()}.txt`);
                const testFileToAdd = pathJoin(tmpDir, `file-to-add-${randomUUID().toString()}.txt`);

                await cloneForTest();
                await exec.execReturn("git", ["checkout", "-b", branchName + "-14"], { cwd: tmpDir, silent: true });

                // Create initial commit with file to delete
                writeFileSync(testFileToDelete, "This file will be deleted");
                await exec.execReturn("git", ["add", "."], {
                    cwd: tmpDir,
                    silent: true,
                });
                await exec.execReturn("git", ["commit", "-m", "Add file to delete"], { cwd: tmpDir, silent: true });
                const { stdout: baseCommit } = await exec.execReturn("git", ["rev-parse", "HEAD"], {
                    cwd: tmpDir,
                    silent: true,
                });

                // Delete one file and add another
                rmSync(testFileToDelete);
                writeFileSync(testFileToAdd, "This is a new file");
                await exec.execReturn("git", ["add", "."], {
                    cwd: tmpDir,
                    silent: true,
                });
                await exec.execReturn("git", ["commit", "-m", "Delete and add files"], { cwd: tmpDir, silent: true });
                const { stdout: recentCommit } = await exec.execReturn("git", ["rev-parse", "HEAD"], {
                    cwd: tmpDir,
                    silent: true,
                });

                // Test getChangedPaths
                const changedPaths = await gitHandler.getChangedPaths(tmpDir, recentCommit.trim(), baseCommit.trim());

                // Verify both deleted and added files are in the result
                expect(changedPaths).toHaveLength(2);
                expect(changedPaths).toContain(testFileToDelete.replace(tmpDir + "/", ""));
                expect(changedPaths).toContain(testFileToAdd.replace(tmpDir + "/", ""));
            });
        });

        describe("error handling", async () => {
            it("should fail if it git resets, but not report to sentry", async () => {
                // Set up local branch
                const testFilePath1 = pathJoin(tmpDir, `push-me-${randomUUID().toString()}.txt`);
                const testFilePath2 = pathJoin(tmpDir, `push-me-${randomUUID().toString()}.txt`);
                const testFilePath3 = pathJoin(tmpDir, `push-me-${randomUUID().toString()}.txt`);
                await cloneForTest(1);
                await exec.execReturn("git", ["checkout", "-b", branchName + "-10"], { cwd: tmpDir, silent: true });
                writeFileSync(testFilePath1, "This is a test file");
                // Simulate a reset where remote is ahead of local, but the agent committed to create a problem
                await exec.execReturn("git", ["add", testFilePath1], {
                    cwd: tmpDir,
                    silent: true,
                });
                await exec.execReturn("git", ["commit", "-m", "Add " + testFilePath1], { cwd: tmpDir, silent: true });
                await exec.execReturn("git", ["push", "origin", branchName + "-10"], { cwd: tmpDir, silent: true });
                await exec.execReturn("git", ["reset", "--hard", "HEAD~1"], {
                    cwd: tmpDir,
                    silent: true,
                });
                writeFileSync(testFilePath2, "This is a test file");
                await exec.execReturn("git", ["add", testFilePath2], {
                    cwd: tmpDir,
                    silent: true,
                });
                await exec.execReturn("git", ["commit", "-m", "Add " + testFilePath2], { cwd: tmpDir, silent: true });
                writeFileSync(testFilePath3, "This is a test file");

                // Run actual test
                let err: unknown = undefined;
                try {
                    await gitHandler.commitAndPushChanges(
                        settings,
                        branchName + "-10",
                        tmpDir,
                        "Test command output",
                        false,
                    );
                } catch (ex: unknown) {
                    err = ex;
                }
                expect(err).toBeDefined();
                expect(err).instanceOf(GitError);
                expect((err as GitError).errorType).toBe(GitErrorType.RebaseFailed);
                expect((err as GitError).skipReport).toBeTruthy();
            });

            it("should fail on unhandled exception and report to sentry", async () => {
                // Clone to a non-existent location
                const folder = pathJoin(tmpdir(), `sweagentd-git-handler-test-${randomUUID().toString()}`);
                // Run actual test
                let err: unknown = undefined;
                try {
                    // Should cause an unhandled error since this is not a git folder and the branch does not exist
                    await gitHandler.commitAndPushChanges(
                        settings,
                        branchName + "-does-not-exist",
                        folder,
                        "Test command output",
                        false,
                    );
                } catch (ex: unknown) {
                    err = ex;
                }
                expect(err).toBeDefined();
                expect(err).instanceOf(GitError);
                expect((err as GitError).errorType).toBe(GitErrorType.Unknown);
                expect((err as GitError).skipReport).toBeFalsy();
            });
        });
    },
    {
        timeout: 10 * 1000, // 10 seconds for each test
    },
);
