/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { afterAll, beforeAll, beforeEach, describe, expect, it, vi } from "vitest";
import { LogLevel } from "../../src/runner/logger";
import { ConsoleLogger } from "../../src/runner/logger/console";
import { initSettings } from "../../src/settings";
import { clearCurrentSettings, setCurrentSettings } from "../../src/settings/current";
import { secretEnvVarNames } from "../../src/settings/environment-settings";

let loggerOutput = "";

const consoleLogMock = vi.spyOn(console, "log").mockImplementation((message: string | Error) => {
    loggerOutput += message.toString() + "\n";
});
const consoleDebugMock = vi.spyOn(console, "debug").mockImplementation((message: string | Error) => {
    loggerOutput += message.toString() + "\n";
});
const consoleErrorMock = vi.spyOn(console, "error").mockImplementation((message: string | Error) => {
    loggerOutput += message.toString() + "\n";
});
const consoleWarnMock = vi.spyOn(console, "warn").mockImplementation((message: string | Error) => {
    loggerOutput += message.toString() + "\n";
});
const consoleInfoMock = vi.spyOn(console, "info").mockImplementation((message: string | Error) => {
    loggerOutput += message.toString() + "\n";
});

// Mock the actions logger by sending to console.log which itself is mocked
vi.mock("@actions/core", () => {
    return {
        debug: vi.fn((message: string) => {
            console.log(message);
        }),
        error: vi.fn((message: string) => {
            console.log(message);
        }),
        warning: vi.fn((message: string) => {
            console.log(message);
        }),
        info: vi.fn((message: string) => {
            console.log(message);
        }),
        notice: vi.fn((message: string) => {
            console.log(message);
        }),
    };
});

afterAll(() => {
    consoleLogMock.mockReset();
    consoleDebugMock.mockReset();
    consoleErrorMock.mockReset();
    consoleWarnMock.mockReset();
    consoleInfoMock.mockReset();
    vi.resetAllMocks();
});

beforeEach(() => {
    loggerOutput = "";
});

async function doTest(logger: ConsoleLogger, secretsToReplace: string[]) {
    const allSecrets = secretsToReplace.join("");
    logger.debug(allSecrets);
    logger.debug("debug");
    logger.info(allSecrets);
    logger.info("info");
    logger.warning(allSecrets);
    logger.warning("warning");
    logger.warning(new Error(allSecrets));
    logger.warning(new Error("warning-error"));
    logger.error(allSecrets);
    logger.error("error");
    logger.error(new Error(allSecrets));
    logger.error(new Error("error-error"));
    logger.notice(allSecrets);
    logger.notice("notice");
    logger.notice(new Error(allSecrets));
    logger.notice(new Error("notice-error"));

    // Verify secrets do not appear in the resulting output
    expect(loggerOutput.trim()).not.toBe("");
    for (const secret of secretsToReplace) {
        expect(loggerOutput).not.toContain(secret);
    }
    // Make sure it does contain the non-secrets
    for (const message of [
        "debug",
        "info",
        "warning",
        "warning-error",
        "error",
        "error-error",
        "notice",
        "notice-error",
    ]) {
        expect(loggerOutput).toContain(message);
    }
}

describe("filter environment secrets from logs", () => {
    const originalEnvVars = new Map<string, string | undefined>();
    const secretsToReplace: string[] = [];

    // Update vars to values we can verify
    beforeAll(async () => {
        // Set up fake env vars
        for (const name of secretEnvVarNames) {
            originalEnvVars.set(name, process.env[name]);
            const secretString = `~~*${name}*~~`;
            process.env[name] = secretString;
            secretsToReplace.push(secretString);
        }
        originalEnvVars.set("COPILOT_AGENT_DEBUG", process.env.COPILOT_AGENT_DEBUG);
        originalEnvVars.set("COPILOT_AGENT_RUNNER_TYPE", process.env.COPILOT_AGENT_RUNNER_TYPE);
        process.env.COPILOT_AGENT_DEBUG = "true";
        process.env.COPILOT_AGENT_RUNNER_TYPE = "STANDALONE";

        // Initialize settings out of environment
        clearCurrentSettings();
        await initSettings();
    });

    // Restore original vars and functionality of console.log
    afterAll(async () => {
        for (const [name, value] of originalEnvVars) {
            if (value) {
                process.env[name] = value;
            } else {
                delete process.env[name];
            }
        }
        // Reset the settings to their original state
        clearCurrentSettings();
        await initSettings();
    });

    it("should filter console logs", async () => {
        const logger = new ConsoleLogger(LogLevel.All);
        await doTest(logger, secretsToReplace);
    });
});

describe("filter settings secrets from logs", () => {
    const secretsToReplace: string[] = [
        "~~*github.token*~~",
        "~~*api.aipSweAgent.token*~~",
        "~~*api.anthropic.key*~~",
        "~~*api.copilot.hmacKey*~~",
        "~~*api.copilot.token*~~",
    ];
    const originalEnvVars = new Map<string, string | undefined>();

    // Update vars to values we can verify
    beforeAll(async () => {
        originalEnvVars.set("COPILOT_AGENT_RUNNER_TYPE", process.env.COPILOT_AGENT_RUNNER_TYPE);
        originalEnvVars.set("COPILOT_AGENT_DEBUG", process.env.COPILOT_AGENT_DEBUG);
        process.env.COPILOT_AGENT_DEBUG = "true";
        process.env.COPILOT_AGENT_RUNNER_TYPE = "STANDALONE";
    });

    // Restore original vars and functionality of console.log
    afterAll(async () => {
        // Reset the settings to their original state
        clearCurrentSettings();
        await initSettings();
    });

    it("should filter console logs", async () => {
        // Initialize settings out of environment
        clearCurrentSettings();
        setCurrentSettings({
            github: {
                token: "~~*github.token*~~",
            },
            api: {
                aipSweAgent: {
                    token: "~~*api.aipSweAgent.token*~~",
                },
                anthropic: {
                    key: "~~*api.anthropic.key*~~",
                },
                copilot: {
                    hmacKey: "~~*api.copilot.hmacKey*~~",
                    token: "~~*api.copilot.token*~~",
                },
            },
        });
        const logger = new ConsoleLogger(LogLevel.All);
        await doTest(logger, secretsToReplace);
    });
});
