/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import { ActionsRunner } from "../../src/runner/actions";

describe("ActionsRunner#sensitiveKeys", () => {
    test("should include all sensitive keys", () => {
        vi.stubEnv("COPILOT_AGENT_INJECTED_SECRET_NAMES", "INJECTED_1,INJECTED_2");
        vi.stubEnv("INJECTED_1", "injected_1");
        vi.stubEnv("INJECTED_2", "injected_2");

        const runner = new ActionsRunner();
        expect(runner.sensitiveKeys).toEqual([
            "GITHUB_COPILOT_API_TOKEN",
            "GITHUB_COPILOT_INTEGRATION_ID",
            "COPILOT_JOB_NONCE",
            "GITHUB_TOKEN",
            "CAPI_HMAC_KEY",
            "CAPI_HMAC_KEY_OVERRIDE",
            "ANTHROPIC_API_KEY",
            "AIP_SWE_AGENT_TOKEN",
            "CAPI_AZURE_KEY_VAULT_URI",
            "GITHUB_MCP_SERVER_TOKEN",
            "OPENAI_BASE_URL",
            "OPENAI_API_KEY",
            "COPILOT_AGENT_REQUEST_HEADERS",
            "AZURE_OPENAI_API_KEY",
            "AZURE_OPENAI_API_ENDPOINT",
            "AZURE_OPENAI_KEY_VAULT_URI",
            "AZURE_OPENAI_KEY_VAULT_SECRET_NAME",
            "BLACKBIRD_AUTH_METIS_API_KEY",
            "BLACKBIRD_AUTH_MODEL_BASED_RETRIEVAL_TOKEN",
            "INJECTED_1",
            "INJECTED_2",
        ]);
    });

    afterEach(() => {
        vi.unstubAllEnvs();
    });
});
