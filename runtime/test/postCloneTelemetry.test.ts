/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { join as pathJoin } from "path";
import { beforeEach, describe, expect, it } from "vitest";
import type { IAgentCallback } from "../src/callbacks/callback";
import type { SWEBenchVerifiedProblem } from "../src";
import { emitPostCloneTelemetry } from "../src/postCloneTelemetry";
import type { RuntimeSettings } from "../src/settings";
import type { AgentCallbackProgressEvent } from "../src/types";

describe("postCloneTelemetry", () => {
    let mockCallback: IAgentCallback;
    let eventsSentToMockCallback: AgentCallbackProgressEvent[];

    beforeEach(() => {
        // Set up mock callback like in telemetry.test.ts
        eventsSentToMockCallback = [];
        mockCallback = {
            progress: (event: AgentCallbackProgressEvent) => {
                eventsSentToMockCallback.push(event);
            },
        } as IAgentCallback;
    });

    describe("emitPostCloneTelemetry", () => {
        it("should emit telemetry for a repo without any instructions", async () => {
            const repoPath = pathJoin(__dirname, "helpers", "repository-clone-with-copilot-setup-steps-yml");
            const problem: SWEBenchVerifiedProblem = {
                repo: "test/repo",
                base_commit: "abc123",
                issue_number: 42,
                problem_statement: "Test problem",
                job_id: "test-job-123",
                branchName: "test-branch",
                repoLocation: repoPath,
                callback: mockCallback,
            };

            const settings: RuntimeSettings = {
                timeoutMs: 300000,
                startTimeMs: Date.now() - 60000,
            };

            await emitPostCloneTelemetry(problem, settings);

            expect(eventsSentToMockCallback).toHaveLength(1);
            const telemetryEvent = eventsSentToMockCallback[0];

            expect(telemetryEvent.kind).toBe("telemetry");
            if (telemetryEvent.kind === "telemetry") {
                expect(telemetryEvent.telemetry.event).toBe("post_clone");

                // Check properties
                expect(telemetryEvent.telemetry.properties.hasCopilotInstructions).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasCopilotSetupSteps).toBe("true"); // This repo has setup steps
                expect(telemetryEvent.telemetry.properties.hasCodexCustomInstructions).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasClaudeCustomInstructions).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasGeminiCustomInstructions).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasAtLeastOneGitHubInstructionsMd).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasClaudeHooks).toBe("false");

                // Check metrics
                expect(telemetryEvent.telemetry.metrics.nowMs).toBeTypeOf("number");
                expect(telemetryEvent.telemetry.metrics.runtimeTimeoutMs).toBe(300000);
                expect(telemetryEvent.telemetry.metrics.runtimeStartTimeMs).toBeTypeOf("number");

                // Check restrictedProperties is empty
                expect(telemetryEvent.telemetry.restrictedProperties).toEqual({});
            }
        });

        it("should emit telemetry for a repo with copilot instructions", async () => {
            const repoPath = pathJoin(__dirname, "helpers", "repository-clone-with-copilot-instructions");
            const problem: SWEBenchVerifiedProblem = {
                repo: "test/repo",
                base_commit: "abc123",
                issue_number: 42,
                problem_statement: "Test problem",
                job_id: "test-job-123",
                branchName: "test-branch",
                repoLocation: repoPath,
                callback: mockCallback,
            };

            const settings: RuntimeSettings = {
                timeoutMs: undefined,
                startTimeMs: undefined,
            };

            await emitPostCloneTelemetry(problem, settings);

            expect(eventsSentToMockCallback).toHaveLength(1);
            const telemetryEvent = eventsSentToMockCallback[0];

            expect(telemetryEvent.kind).toBe("telemetry");
            if (telemetryEvent.kind === "telemetry") {
                expect(telemetryEvent.telemetry.event).toBe("post_clone");

                // Check properties - this repo should have copilot instructions
                expect(telemetryEvent.telemetry.properties.hasCopilotInstructions).toBe("true");
                expect(telemetryEvent.telemetry.properties.hasCopilotSetupSteps).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasCodexCustomInstructions).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasClaudeCustomInstructions).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasGeminiCustomInstructions).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasAtLeastOneGitHubInstructionsMd).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasClaudeHooks).toBe("false");

                // Check metrics when no timeout/start time
                expect(telemetryEvent.telemetry.metrics.nowMs).toBeTypeOf("number");
                expect(telemetryEvent.telemetry.metrics.runtimeTimeoutMs).toBeUndefined();
                expect(telemetryEvent.telemetry.metrics.runtimeStartTimeMs).toBeUndefined();
            }
        });

        it("should emit telemetry for a repo with codex custom instructions", async () => {
            const repoPath = pathJoin(__dirname, "helpers", "repository-clone-with-codex-custom-instructions");
            const problem: SWEBenchVerifiedProblem = {
                repo: "test/repo",
                base_commit: "abc123",
                issue_number: 42,
                problem_statement: "Test problem",
                job_id: "test-job-123",
                branchName: "test-branch",
                repoLocation: repoPath,
                callback: mockCallback,
            };

            const settings: RuntimeSettings = {};

            await emitPostCloneTelemetry(problem, settings);

            expect(eventsSentToMockCallback).toHaveLength(1);
            const telemetryEvent = eventsSentToMockCallback[0];

            expect(telemetryEvent.kind).toBe("telemetry");
            if (telemetryEvent.kind === "telemetry") {
                expect(telemetryEvent.telemetry.properties.hasCopilotInstructions).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasCopilotSetupSteps).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasCodexCustomInstructions).toBe("true");
                expect(telemetryEvent.telemetry.properties.hasClaudeCustomInstructions).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasGeminiCustomInstructions).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasAtLeastOneGitHubInstructionsMd).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasClaudeHooks).toBe("false");
            }
        });

        it("should emit telemetry for a repo with claude custom instructions", async () => {
            const repoPath = pathJoin(__dirname, "helpers", "repository-clone-with-claude-custom-instructions");
            const problem: SWEBenchVerifiedProblem = {
                repo: "test/repo",
                base_commit: "abc123",
                issue_number: 42,
                problem_statement: "Test problem",
                job_id: "test-job-123",
                branchName: "test-branch",
                repoLocation: repoPath,
                callback: mockCallback,
            };

            const settings: RuntimeSettings = {};

            await emitPostCloneTelemetry(problem, settings);

            expect(eventsSentToMockCallback).toHaveLength(1);
            const telemetryEvent = eventsSentToMockCallback[0];

            expect(telemetryEvent.kind).toBe("telemetry");
            if (telemetryEvent.kind === "telemetry") {
                expect(telemetryEvent.telemetry.properties.hasCopilotInstructions).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasCopilotSetupSteps).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasCodexCustomInstructions).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasClaudeCustomInstructions).toBe("true");
                expect(telemetryEvent.telemetry.properties.hasGeminiCustomInstructions).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasAtLeastOneGitHubInstructionsMd).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasClaudeHooks).toBe("false");
            }
        });

        it("should emit telemetry for a repo with gemini custom instructions", async () => {
            const repoPath = pathJoin(__dirname, "helpers", "repository-clone-with-gemini-custom-instructions");
            const problem: SWEBenchVerifiedProblem = {
                repo: "test/repo",
                base_commit: "abc123",
                issue_number: 42,
                problem_statement: "Test problem",
                job_id: "test-job-123",
                branchName: "test-branch",
                repoLocation: repoPath,
                callback: mockCallback,
            };

            const settings: RuntimeSettings = {};

            await emitPostCloneTelemetry(problem, settings);

            expect(eventsSentToMockCallback).toHaveLength(1);
            const telemetryEvent = eventsSentToMockCallback[0];

            expect(telemetryEvent.kind).toBe("telemetry");
            if (telemetryEvent.kind === "telemetry") {
                expect(telemetryEvent.telemetry.properties.hasCopilotInstructions).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasCopilotSetupSteps).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasCodexCustomInstructions).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasClaudeCustomInstructions).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasGeminiCustomInstructions).toBe("true");
                expect(telemetryEvent.telemetry.properties.hasAtLeastOneGitHubInstructionsMd).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasClaudeHooks).toBe("false");
            }
        });

        it("should emit telemetry for a repo with github instructions", async () => {
            const repoPath = pathJoin(__dirname, "helpers", "repository-clone-with-github-instructions");
            const problem: SWEBenchVerifiedProblem = {
                repo: "test/repo",
                base_commit: "abc123",
                issue_number: 42,
                problem_statement: "Test problem",
                job_id: "test-job-123",
                branchName: "test-branch",
                repoLocation: repoPath,
                callback: mockCallback,
            };

            const settings: RuntimeSettings = {};

            await emitPostCloneTelemetry(problem, settings);

            expect(eventsSentToMockCallback).toHaveLength(1);
            const telemetryEvent = eventsSentToMockCallback[0];

            expect(telemetryEvent.kind).toBe("telemetry");
            if (telemetryEvent.kind === "telemetry") {
                expect(telemetryEvent.telemetry.properties.hasCopilotInstructions).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasCopilotSetupSteps).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasCodexCustomInstructions).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasClaudeCustomInstructions).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasGeminiCustomInstructions).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasAtLeastOneGitHubInstructionsMd).toBe("true");
                expect(telemetryEvent.telemetry.properties.hasClaudeHooks).toBe("false");
            }
        });

        it("should emit telemetry for a repo with mixed instructions (copilot + vscode)", async () => {
            const repoPath = pathJoin(__dirname, "helpers", "repository-clone-with-copilot-and-vscode-instructions");
            const problem: SWEBenchVerifiedProblem = {
                repo: "test/repo",
                base_commit: "abc123",
                issue_number: 42,
                problem_statement: "Test problem",
                job_id: "test-job-123",
                branchName: "test-branch",
                repoLocation: repoPath,
                callback: mockCallback,
            };

            const settings: RuntimeSettings = {
                timeoutMs: 600000,
                startTimeMs: Date.now() - 120000,
            };

            await emitPostCloneTelemetry(problem, settings);

            expect(eventsSentToMockCallback).toHaveLength(1);
            const telemetryEvent = eventsSentToMockCallback[0];

            expect(telemetryEvent.kind).toBe("telemetry");
            if (telemetryEvent.kind === "telemetry") {
                // This repo should have both copilot instructions and github instructions
                expect(telemetryEvent.telemetry.properties.hasCopilotInstructions).toBe("true");
                expect(telemetryEvent.telemetry.properties.hasCopilotSetupSteps).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasCodexCustomInstructions).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasClaudeCustomInstructions).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasGeminiCustomInstructions).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasAtLeastOneGitHubInstructionsMd).toBe("true");
                expect(telemetryEvent.telemetry.properties.hasClaudeHooks).toBe("false");

                // Check metrics with specific values
                expect(telemetryEvent.telemetry.metrics.runtimeTimeoutMs).toBe(600000);
                expect(telemetryEvent.telemetry.metrics.runtimeStartTimeMs).toBeTypeOf("number");
            }
        });

        it("should handle non-existent repo path gracefully", async () => {
            const repoPath = pathJoin(__dirname, "helpers", "non-existent-repo");
            const problem: SWEBenchVerifiedProblem = {
                repo: "test/repo",
                base_commit: "abc123",
                issue_number: 42,
                problem_statement: "Test problem",
                job_id: "test-job-123",
                branchName: "test-branch",
                repoLocation: repoPath,
                callback: mockCallback,
            };

            const settings: RuntimeSettings = {};

            await emitPostCloneTelemetry(problem, settings);

            expect(eventsSentToMockCallback).toHaveLength(1);
            const telemetryEvent = eventsSentToMockCallback[0];

            expect(telemetryEvent.kind).toBe("telemetry");
            if (telemetryEvent.kind === "telemetry") {
                // All should be false for non-existent repo
                expect(telemetryEvent.telemetry.properties.hasCopilotInstructions).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasCopilotSetupSteps).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasCodexCustomInstructions).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasClaudeCustomInstructions).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasGeminiCustomInstructions).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasAtLeastOneGitHubInstructionsMd).toBe("false");
                expect(telemetryEvent.telemetry.properties.hasClaudeHooks).toBe("false");
            }
        });

        it("should generate correct timestamp in nowMs metric", async () => {
            const repoPath = pathJoin(__dirname, "helpers", "repository-clone-with-copilot-instructions");
            const problem: SWEBenchVerifiedProblem = {
                repo: "test/repo",
                base_commit: "abc123",
                issue_number: 42,
                problem_statement: "Test problem",
                job_id: "test-job-123",
                branchName: "test-branch",
                repoLocation: repoPath,
                callback: mockCallback,
            };

            const settings: RuntimeSettings = {};

            const beforeCall = Date.now();
            await emitPostCloneTelemetry(problem, settings);
            const afterCall = Date.now();

            expect(eventsSentToMockCallback).toHaveLength(1);
            const telemetryEvent = eventsSentToMockCallback[0];

            expect(telemetryEvent.kind).toBe("telemetry");
            if (telemetryEvent.kind === "telemetry") {
                // The nowMs should be between when we called the function
                expect(telemetryEvent.telemetry.metrics.nowMs).toBeGreaterThanOrEqual(beforeCall);
                expect(telemetryEvent.telemetry.metrics.nowMs).toBeLessThanOrEqual(afterCall);
            }
        });

        it("should call callback.progress exactly once", async () => {
            const repoPath = pathJoin(__dirname, "helpers", "repository-clone-with-copilot-instructions");
            const problem: SWEBenchVerifiedProblem = {
                repo: "test/repo",
                base_commit: "abc123",
                issue_number: 42,
                problem_statement: "Test problem",
                job_id: "test-job-123",
                branchName: "test-branch",
                repoLocation: repoPath,
                callback: mockCallback,
            };

            const settings: RuntimeSettings = {};

            await emitPostCloneTelemetry(problem, settings);

            // Should be called exactly once
            expect(eventsSentToMockCallback).toHaveLength(1);
        });
    });
});
