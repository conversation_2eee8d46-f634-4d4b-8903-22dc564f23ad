/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import http from "http";
import { AddressInfo } from "net";
import { afterEach, describe, expect, it, vi } from "vitest";
import { newRecordCallback } from "../src/record-callback";
import { Runner } from "../src/runner";

describe("newRecordCallback", () => {
    let server: http.Server;
    let url: string;
    let mockLogger: {
        error: (message: string) => void;
        warning: (message: string) => void;
    };
    let mockRunner: Runner;

    beforeEach(() => {
        mockLogger = {
            error: vi.fn(),
            warning: vi.fn(),
        };

        mockRunner = {
            type: "STANDALONE",
            logger: mockLogger,
        } as unknown as Runner;
    });

    afterEach(() => {
        if (server) {
            server.close();
        }
        vi.restoreAllMocks();
    });

    it("logs the X-GitHub-Request-Id header on failure when present", async () => {
        const requestId = "test-request-id-123";

        // Set up a test server that returns a 403 response with the header
        server = http.createServer((_req, res) => {
            res.writeHead(403, { "X-GitHub-Request-ID": requestId });
            res.end("Forbidden");
        });

        // Start the server on a random available port
        await new Promise<void>((resolve) => {
            server.listen(0, "127.0.0.1", () => resolve());
        });

        const address = server.address() as AddressInfo;
        url = `http://127.0.0.1:${address.port}`;

        // Create the callback function
        const callback = newRecordCallback(
            url,
            mockRunner,
            "owner/repo",
            123,
            "repo",
            "owner",
            456,
            "job-id",
            "fix",
            {},
        );

        // Trigger the callback
        await callback("progress", { kind: "log", message: "test" });

        // Verify the error message was logged with the request ID
        expect(mockLogger.error).toHaveBeenCalledWith(expect.stringContaining(`request ID: ${requestId}`));
    });

    it("logs logs unknown when the GitHub-Request-ID header is not present", async () => {
        // Set up a test server that returns a 403 response without the header
        server = http.createServer((_req, res) => {
            res.writeHead(403);
            res.end("Forbidden");
        });

        // Start the server on a random available port
        await new Promise<void>((resolve) => {
            server.listen(0, "127.0.0.1", () => resolve());
        });

        const address = server.address() as AddressInfo;
        url = `http://127.0.0.1:${address.port}`;

        // Create the callback function
        const callback = newRecordCallback(
            url,
            mockRunner,
            "owner/repo",
            123,
            "repo",
            "owner",
            456,
            "job-id",
            "fix",
            {},
        );

        // Trigger the callback
        await callback("progress", { kind: "log", message: "test" });

        // Verify the error message was logged with the request ID
        expect(mockLogger.error).toHaveBeenCalledWith(expect.stringContaining(`request ID: unknown`));
    });
});
