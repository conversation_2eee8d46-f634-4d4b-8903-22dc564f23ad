/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

/**
 * Test to verify CLI /history show and /history clear slash commands work correctly
 */

import fs from "fs";
import { render } from "ink-testing-library";
import os from "os";
import path from "path";
import React from "react";
import { v4 as uuidv4 } from "uuid";
import { afterEach, beforeEach, describe, expect, test, vi } from "vitest";
import type { ConversationEntry } from "../../src/cli/conversationHistory";
import {
    initializeTestIsolation,
    restoreTestIsolation,
    verifyHistoryEmpty,
    type TestIsolationState,
} from "./test-isolation-helpers";

// Mock the CLI module below getCompletionWithTools - use real conversation history but mock LLM calls
const createMockCLIInstance = () => ({
    getCompletionWithTools: vi.fn().mockImplementation(async function* (_systemMessage, messages) {
        // Get the last user message to determine what to respond with
        const lastUserMessage = messages[messages.length - 1];
        const query = lastUserMessage?.content?.toLowerCase() || "";

        let response = "";

        if (query.includes("penguin")) {
            response =
                "A penguin is a flightless bird that lives primarily in the Southern Hemisphere. They are excellent swimmers and have distinctive black and white coloring.";
        } else if (query.includes("dolphin")) {
            response =
                "A dolphin is a highly intelligent marine mammal known for its playful behavior, echolocation abilities, and social nature. They are part of the cetacean family.";
        } else {
            response = "I can help you with information about various topics. What would you like to know?";
        }

        yield {
            kind: "message",
            message: {
                role: "assistant",
                content: response,
            },
        };
        yield {
            kind: "response",
            response: {
                content: response,
            },
        };
    }),
    shutdown: vi.fn().mockResolvedValue(undefined),
});

vi.mock("../../src/cli", () => ({
    CLI: {
        createFromOptions: vi.fn().mockImplementation(() => Promise.resolve(createMockCLIInstance())),
    },
}));

// Mock the agents/prompts module
vi.mock("../../src/agents/prompts", () => ({
    systemMessage: vi.fn().mockResolvedValue("You are a helpful assistant."),
    cliSystemMessage: vi.fn().mockResolvedValue("You are a helpful CLI assistant."),
}));

// Mock child_process
vi.mock("child_process", () => ({
    spawn: vi.fn().mockReturnValue({
        on: vi.fn(),
        stderr: {
            on: vi.fn(),
        },
        kill: vi.fn(),
    }),
}));

// DO NOT mock conversation history - we want to test the real implementation

const DEFAULT_TIMEOUT_MS = 30000; // 30 seconds for mocked LLM calls

describe("CLI History Slash Commands Test", () => {
    let tempDir: string;
    let isolationState: TestIsolationState;

    beforeEach(async () => {
        // Initialize complete test isolation
        isolationState = await initializeTestIsolation();

        // Verify we start with empty history
        await verifyHistoryEmpty();

        // Ensure the real copilot directory exists
        const realCopilotDir = path.join(os.homedir(), ".copilot");
        if (!fs.existsSync(realCopilotDir)) {
            fs.mkdirSync(realCopilotDir, { recursive: true });
        }

        // Setup temp directory for cleanup tracking
        tempDir = path.join(os.tmpdir(), "cli-history-slash-commands-test", uuidv4());
        fs.mkdirSync(tempDir, { recursive: true });

        // Get HMAC key from environment variable
        let hmacKey = process.env.COPILOT_HMAC_KEY;

        // Get integration ID from environment variable, with fallback to hardcoded value
        let integrationId = process.env.COPILOT_INTEGRATION_ID || "copilot-developer-dev";

        // If HMAC key not in environment, try to read from the backed up config
        if (!hmacKey && isolationState.configBackup) {
            try {
                const lines = isolationState.configBackup.split("\n");
                for (const line of lines) {
                    if (line.includes("=")) {
                        const [key, ...valueParts] = line.split("=");
                        const value = valueParts.join("=").trim();
                        const trimmedKey = key.trim();
                        if (trimmedKey === "COPILOT_HMAC_KEY" && !hmacKey) {
                            hmacKey = value;
                        }
                        if (trimmedKey === "COPILOT_INTEGRATION_ID" && !integrationId) {
                            integrationId = value;
                        }
                    }
                }
            } catch {
                // Error reading config - continue with test
            }
        }

        // Fail test if we don't have the HMAC key
        if (!hmacKey) {
            throw new Error("No HMAC key found in environment or config file");
        }

        const configContent = `COPILOT_HMAC_KEY=${hmacKey}\nCOPILOT_INTEGRATION_ID=${integrationId}`;
        fs.writeFileSync(isolationState.realConfigPath, configContent);

        // Don't mock homedir - let the real conversation history manager work with the real home directory
        // We're using backup/restore to safely manage the real history
        // vi.spyOn(os, 'homedir').mockReturnValue(tempDir);
    });

    afterEach(async () => {
        // Restore complete test isolation
        await restoreTestIsolation(isolationState);

        // Clean up temp directory
        if (fs.existsSync(tempDir)) {
            fs.rmSync(tempDir, { recursive: true, force: true });
        }
        vi.restoreAllMocks();
    });

    test(
        "validates /history show and /history clear slash commands work correctly",
        async () => {
            const { default: App } = await import("../../src/cli/app");

            const { lastFrame, stdin } = render(React.createElement(App));

            // Wait for initialization
            await new Promise((resolve) => setTimeout(resolve, 500));

            // Test 1: /history show with empty history
            stdin.write("/history show");
            await new Promise((resolve) => setTimeout(resolve, 500));
            stdin.write("\r");
            await new Promise((resolve) => setTimeout(resolve, 2000)); // Wait longer for slash command processing

            let currentFrame = lastFrame();
            expect(currentFrame).toBeDefined();

            // Check if the command was processed (should show the result, not still in input)
            if (currentFrame!.includes("│ > /history show")) {
                // Command is still in input, wait longer
                await new Promise((resolve) => setTimeout(resolve, 2000));
                currentFrame = lastFrame();
            }

            expect(currentFrame!).toContain("No conversation history found");

            // Test 2: Add some conversation history by executing a query
            stdin.write("what is a penguin");
            await new Promise((resolve) => setTimeout(resolve, 500));
            stdin.write("\r");

            // Wait for mocked LLM response and history to be saved
            await new Promise((resolve) => setTimeout(resolve, 3000));

            // Verify the query executed successfully
            currentFrame = lastFrame();
            expect(currentFrame).toBeDefined();
            expect(currentFrame!).toContain("penguin");

            // Test 3: /history show with one entry
            stdin.write("/history show");
            await new Promise((resolve) => setTimeout(resolve, 500));
            stdin.write("\r");
            await new Promise((resolve) => setTimeout(resolve, 2000));

            currentFrame = lastFrame();
            expect(currentFrame).toBeDefined();
            expect(currentFrame!).toContain("Recent conversation history:");
            expect(currentFrame!).toContain("what is a penguin");
            expect(currentFrame!).toContain("penguin"); // Should show part of the response

            // Test 4: Add another query to have multiple entries
            stdin.write("what is a dolphin");
            await new Promise((resolve) => setTimeout(resolve, 500));
            stdin.write("\r");
            await new Promise((resolve) => setTimeout(resolve, 3000)); // Wait for mocked response and history save

            // Verify second query executed
            currentFrame = lastFrame();
            expect(currentFrame).toBeDefined();
            expect(currentFrame!).toContain("dolphin");

            // Test 5: /history show with multiple entries
            stdin.write("/history show");
            await new Promise((resolve) => setTimeout(resolve, 500));
            stdin.write("\r");
            await new Promise((resolve) => setTimeout(resolve, 2000));

            currentFrame = lastFrame();
            expect(currentFrame).toBeDefined();
            expect(currentFrame!).toContain("Recent conversation history:");
            expect(currentFrame!).toContain("what is a penguin");
            expect(currentFrame!).toContain("what is a dolphin");

            // Verify history file contains both entries
            const realHistoryPath = path.join(os.homedir(), ".copilot", "conversation-history.json");
            expect(fs.existsSync(realHistoryPath)).toBe(true);
            const historyBeforeClear = JSON.parse(fs.readFileSync(realHistoryPath, "utf8"));

            const penguinEntries = historyBeforeClear.entries.filter(
                (entry: ConversationEntry) => entry.userMessage && entry.userMessage.includes("penguin"),
            );
            const dolphinEntries = historyBeforeClear.entries.filter(
                (entry: ConversationEntry) => entry.userMessage && entry.userMessage.includes("dolphin"),
            );

            expect(penguinEntries.length).toBeGreaterThanOrEqual(1);
            expect(dolphinEntries.length).toBeGreaterThanOrEqual(1);

            // Test 6: /history clear
            stdin.write("/history clear");
            await new Promise((resolve) => setTimeout(resolve, 500));
            stdin.write("\r");
            await new Promise((resolve) => setTimeout(resolve, 2000));

            currentFrame = lastFrame();
            expect(currentFrame).toBeDefined();
            expect(currentFrame!).toContain("Conversation history cleared");

            // Test 7: Verify history is actually cleared
            const { conversationHistoryManager } = await import("../../src/cli/conversationHistory");
            const clearedHistory = conversationHistoryManager.getDisplayHistory();
            expect(clearedHistory.length).toBe(0);

            // Also verify the history file is cleared
            if (fs.existsSync(realHistoryPath)) {
                const clearedHistoryFile = JSON.parse(fs.readFileSync(realHistoryPath, "utf8"));
                expect(clearedHistoryFile.entries.length).toBe(0);
            }

            // Test 8: /history show after clear should show empty
            stdin.write("/history show");
            await new Promise((resolve) => setTimeout(resolve, 500));
            stdin.write("\r");
            await new Promise((resolve) => setTimeout(resolve, 2000));

            currentFrame = lastFrame();
            expect(currentFrame).toBeDefined();
            expect(currentFrame!).toContain("No conversation history found");

            // Test validates that:
            // ✅ /history show works with empty history
            // ✅ /history show displays conversation entries correctly
            // ✅ /history show shows multiple entries
            // ✅ /history clear removes all conversation history
            // ✅ /history commands use real conversation history implementation
        },
        DEFAULT_TIMEOUT_MS,
    );
});
