/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import fs from "fs";
import { render } from "ink-testing-library";
import os from "os";
import path from "path";
import React from "react";
import { v4 as uuidv4 } from "uuid";
import { afterEach, beforeEach, describe, expect, test, vi } from "vitest";
import {
    getTestConfiguration,
    initializeTestIsolation,
    restoreTestIsolation,
    TestIsolationState,
} from "./test-isolation-helpers";

describe("Slash Command Picker Test", () => {
    let tempDir: string;
    let testCopilotDir: string;
    let originalCwd: string;
    let isolationState: TestIsolationState;

    beforeEach(async () => {
        // Store original working directory
        originalCwd = process.cwd();

        // Initialize complete test isolation
        isolationState = await initializeTestIsolation();

        // Create test directories
        tempDir = path.join(os.tmpdir(), "cli-clear-test", uuidv4());
        testCopilotDir = path.join(tempDir, ".copilot");
        fs.mkdirSync(tempDir, { recursive: true });
        fs.mkdirSync(testCopilotDir, { recursive: true });

        // Ensure the real copilot directory exists
        const realCopilotDir = path.join(os.homedir(), ".copilot");
        if (!fs.existsSync(realCopilotDir)) {
            fs.mkdirSync(realCopilotDir, { recursive: true });
        }

        // Get configuration with proper fallback logic
        const { hmacKey, integrationId } = getTestConfiguration();

        const configContent = `COPILOT_HMAC_KEY=${hmacKey}\nCOPILOT_INTEGRATION_ID=${integrationId}`;
        fs.writeFileSync(isolationState.realConfigPath, configContent);
    });

    afterEach(async () => {
        // Restore original working directory
        try {
            process.chdir(originalCwd);
        } catch {
            // If we can't change back, that's ok for tests
        }

        // Restore complete test isolation
        await restoreTestIsolation(isolationState);

        // Clean up temp directory
        if (fs.existsSync(tempDir)) {
            fs.rmSync(tempDir, { recursive: true, force: true });
        }
        vi.restoreAllMocks();
    });

    test("slash command picker works", async () => {
        // Initialize app
        const { default: App } = await import("../../src/cli/app");
        const { lastFrame, stdin } = render(React.createElement(App));
        await new Promise((resolve) => setTimeout(resolve, 500));

        // Verify slash command picker isn't initially visible
        expect(lastFrame()).not.toContain("/clear");

        // Type "/" to trigger slash command picker
        let currentOutput = await typeAndGetOutput("/");
        expect(currentOutput).toContain("/clear");
        expect(currentOutput).toContain("/cwd");

        // It also shows as a placeholder
        expect(currentOutput).toContain("│ > /clear");

        // You can move down to some item in the list
        // Here we press "down arrow" repeatedly until we find /mcp
        for (let count = 0; count < 10 && currentOutput!.indexOf("│ > /mcp") < 0; count++) {
            currentOutput = await typeAndGetOutput("\x1b[B");
        }
        expect(currentOutput).toContain("│ > /mcp");

        // You can tab to autocomplete, including after partially typing
        // the command name. Note that the values we're asserting should
        // be present include the autocompletion text.
        expect(await typeAndGetOutput("mc")).toContain("│ > /mcp");
        expect(await typeAndGetOutput("\t")).toContain("│ > /mcp ");
        expect(await typeAndGetOutput("hello")).toContain("│ > /mcp hello");

        async function typeAndGetOutput(input: string): Promise<string | undefined> {
            stdin.write(input);
            await new Promise((resolve) => setTimeout(resolve, 200));
            return lastFrame();
        }
    });
});
