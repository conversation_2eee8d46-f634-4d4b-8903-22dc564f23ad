/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

/**
 * Test to verify CLI /clear slash command works correctly
 */

import { describe, test, expect, vi, beforeEach, afterEach } from "vitest";
import { render } from "ink-testing-library";
import React from "react";
import fs from "fs";
import os from "os";
import path from "path";
import { v4 as uuidv4 } from "uuid";
import {
    initializeTestIsolation,
    restoreTestIsolation,
    verifyHistoryEmpty,
    getTestConfiguration,
    type TestIsolationState,
} from "./test-isolation-helpers";
import { conversationHistoryManager } from "../../src/cli/conversationHistory";

// Mock the CLI module below getCompletionWithTools - use real conversation history but mock LLM calls
const createMockCLIInstance = () => ({
    getCompletionWithTools: vi.fn().mockImplementation(async function* (_systemMessage, _messages) {
        // For /clear command tests, we don't need LLM responses, just mock a basic response
        yield {
            kind: "message",
            message: {
                role: "assistant",
                content: "This is a mock response for testing.",
            },
        };
        yield {
            kind: "response",
            response: {
                content: "This is a mock response for testing.",
            },
        };
    }),
    shutdown: vi.fn().mockResolvedValue(undefined),
});

vi.mock("../../src/cli", () => ({
    CLI: {
        createFromOptions: vi.fn().mockImplementation(() => Promise.resolve(createMockCLIInstance())),
    },
}));

// Mock the agents/prompts module
vi.mock("../../src/agents/prompts", () => ({
    systemMessage: vi.fn().mockResolvedValue("You are a helpful assistant."),
    cliSystemMessage: vi.fn().mockResolvedValue("You are a helpful CLI assistant."),
}));

// Mock child_process
vi.mock("child_process", () => ({
    spawn: vi.fn().mockReturnValue({
        on: vi.fn(),
        stderr: {
            on: vi.fn(),
        },
        kill: vi.fn(),
    }),
}));

// DO NOT mock conversation history - we want to test the real implementation

const DEFAULT_TIMEOUT_MS = 30000; // 30 seconds for mocked LLM calls

describe("CLI /clear Slash Command Test", () => {
    let tempDir: string;
    let testCopilotDir: string;
    let originalCwd: string;
    let isolationState: TestIsolationState;
    let originalEnvVars: { [key: string]: string | undefined };

    beforeEach(async () => {
        // Store original working directory
        originalCwd = process.cwd();

        // Store original environment variables that /clear command clears
        originalEnvVars = {
            COPILOT_AGENT_JOB_ID: process.env.COPILOT_AGENT_JOB_ID,
            COPILOT_JOB_NONCE: process.env.COPILOT_JOB_NONCE,
            GITHUB_RUN_ID: process.env.GITHUB_RUN_ID,
            GITHUB_JOB: process.env.GITHUB_JOB,
            GITHUB_WORKFLOW: process.env.GITHUB_WORKFLOW,
        };

        // Initialize complete test isolation
        isolationState = await initializeTestIsolation();

        // Verify we start with empty history
        await verifyHistoryEmpty();

        // Create test directories
        tempDir = path.join(os.tmpdir(), "cli-clear-test", uuidv4());
        testCopilotDir = path.join(tempDir, ".copilot");
        fs.mkdirSync(tempDir, { recursive: true });
        fs.mkdirSync(testCopilotDir, { recursive: true });

        // Ensure the real copilot directory exists
        const realCopilotDir = path.join(os.homedir(), ".copilot");
        if (!fs.existsSync(realCopilotDir)) {
            fs.mkdirSync(realCopilotDir, { recursive: true });
        }

        // Get configuration with proper fallback logic
        const { hmacKey, integrationId } = getTestConfiguration();

        const configContent = `COPILOT_HMAC_KEY=${hmacKey}\nCOPILOT_INTEGRATION_ID=${integrationId}`;
        fs.writeFileSync(isolationState.realConfigPath, configContent);
    });

    afterEach(async () => {
        // Restore original working directory
        try {
            process.chdir(originalCwd);
        } catch {
            // If we can't change back, that's ok for tests
        }

        // Restore original environment variables
        Object.keys(originalEnvVars).forEach((key) => {
            if (originalEnvVars[key] === undefined) {
                delete process.env[key];
            } else {
                process.env[key] = originalEnvVars[key];
            }
        });

        // Restore complete test isolation
        await restoreTestIsolation(isolationState);

        // Clean up temp directory
        if (fs.existsSync(tempDir)) {
            fs.rmSync(tempDir, { recursive: true, force: true });
        }
        vi.restoreAllMocks();
    });

    test(
        "validates /clear command clears session and shows confirmation message",
        async () => {
            const { default: App } = await import("../../src/cli/app");

            const { lastFrame, stdin } = render(React.createElement(App));

            // Wait for initialization
            await new Promise((resolve) => setTimeout(resolve, 500));

            // First, add some conversation history by sending a message
            stdin.write("test message for history");
            await new Promise((resolve) => setTimeout(resolve, 500));
            stdin.write("\r");
            await new Promise((resolve) => setTimeout(resolve, 4000)); // Wait for response and history recording

            // Verify history was recorded by waiting and checking
            let historyBeforeClear;
            let attempts = 0;
            do {
                await new Promise((resolve) => setTimeout(resolve, 1000));
                historyBeforeClear = conversationHistoryManager.getDisplayHistory();
                attempts++;
            } while (historyBeforeClear.length === 0 && attempts < 5);

            expect(historyBeforeClear.length).toBeGreaterThan(0);

            // Test: /clear command should clear everything
            stdin.write("/clear");
            await new Promise((resolve) => setTimeout(resolve, 500));
            stdin.write("\r");
            await new Promise((resolve) => setTimeout(resolve, 1000)); // Wait for slash command processing

            const currentFrame = lastFrame();
            expect(currentFrame).toBeDefined();

            // Check if the command was processed (should show the result, not still in input)
            if (currentFrame!.includes("│ > /clear")) {
                // Command is still in input, wait longer
                await new Promise((resolve) => setTimeout(resolve, 2000));
            }

            // The /clear command clears messages, so we might not see the confirmation in the UI
            // Instead, verify that the history was actually cleared and UI is reset
            const historyAfterClear = conversationHistoryManager.getDisplayHistory();
            expect(historyAfterClear.length).toBe(0);

            // Check that the UI is back to initial state (clean)
            expect(currentFrame!).toContain("Welcome to Copilot");
            expect(currentFrame!).not.toContain("test message for history");
        },
        DEFAULT_TIMEOUT_MS,
    );

    test(
        "validates /clear command deletes session files",
        async () => {
            const { default: App } = await import("../../src/cli/app");

            // Create some test files that should be deleted
            const copilotDir = path.join(os.homedir(), ".copilot");
            const historyFile = path.join(copilotDir, "conversation-history.json");
            const logFile = path.join(copilotDir, "cli.log");
            const configFile = path.join(process.cwd(), "cpd.local.settings.yaml");
            const oldHistoryFile = path.join(os.homedir(), ".conversation-history.json");
            const localHistoryFile = path.join(process.cwd(), ".conversation-history.json");

            // Create test files
            fs.mkdirSync(copilotDir, { recursive: true });
            fs.writeFileSync(historyFile, '{"test": "data"}');
            fs.writeFileSync(logFile, "test log data");
            fs.writeFileSync(configFile, "test config data");
            fs.writeFileSync(oldHistoryFile, '{"old": "data"}');
            fs.writeFileSync(localHistoryFile, '{"local": "data"}');

            // Verify files exist before clearing
            expect(fs.existsSync(historyFile)).toBe(true);
            expect(fs.existsSync(logFile)).toBe(true);
            expect(fs.existsSync(configFile)).toBe(true);
            expect(fs.existsSync(oldHistoryFile)).toBe(true);
            expect(fs.existsSync(localHistoryFile)).toBe(true);

            const { lastFrame, stdin } = render(React.createElement(App));

            // Wait for initialization
            await new Promise((resolve) => setTimeout(resolve, 500));

            // Execute /clear command
            stdin.write("/clear");
            await new Promise((resolve) => setTimeout(resolve, 500));
            stdin.write("\r");
            await new Promise((resolve) => setTimeout(resolve, 2000)); // Wait for slash command processing

            const currentFrame = lastFrame();
            expect(currentFrame).toBeDefined();

            // Check if the command was processed
            if (currentFrame!.includes("│ > /clear")) {
                // Command is still in input, wait longer
                await new Promise((resolve) => setTimeout(resolve, 2000));
            }

            // Verify files were deleted
            expect(fs.existsSync(historyFile)).toBe(false);
            expect(fs.existsSync(logFile)).toBe(false);
            expect(fs.existsSync(configFile)).toBe(false);
            expect(fs.existsSync(oldHistoryFile)).toBe(false);
            expect(fs.existsSync(localHistoryFile)).toBe(false);
        },
        DEFAULT_TIMEOUT_MS,
    );

    test(
        "validates /clear command clears environment variables",
        async () => {
            const { default: App } = await import("../../src/cli/app");

            // Set some test environment variables that should be cleared
            process.env.COPILOT_AGENT_JOB_ID = "test-job-id";
            process.env.COPILOT_JOB_NONCE = "test-nonce";
            process.env.GITHUB_RUN_ID = "test-run-id";
            process.env.GITHUB_JOB = "test-job";
            process.env.GITHUB_WORKFLOW = "test-workflow";

            // Verify environment variables are set
            expect(process.env.COPILOT_AGENT_JOB_ID).toBe("test-job-id");
            expect(process.env.COPILOT_JOB_NONCE).toBe("test-nonce");
            expect(process.env.GITHUB_RUN_ID).toBe("test-run-id");
            expect(process.env.GITHUB_JOB).toBe("test-job");
            expect(process.env.GITHUB_WORKFLOW).toBe("test-workflow");

            const { lastFrame, stdin } = render(React.createElement(App));

            // Wait for initialization
            await new Promise((resolve) => setTimeout(resolve, 500));

            // Execute /clear command
            stdin.write("/clear");
            await new Promise((resolve) => setTimeout(resolve, 500));
            stdin.write("\r");
            await new Promise((resolve) => setTimeout(resolve, 2000)); // Wait for slash command processing

            const currentFrame = lastFrame();
            expect(currentFrame).toBeDefined();

            // Check if the command was processed
            if (currentFrame!.includes("│ > /clear")) {
                // Command is still in input, wait longer
                await new Promise((resolve) => setTimeout(resolve, 2000));
            }

            // Verify environment variables were cleared
            expect(process.env.COPILOT_AGENT_JOB_ID).toBeUndefined();
            expect(process.env.COPILOT_JOB_NONCE).toBeUndefined();
            expect(process.env.GITHUB_RUN_ID).toBeUndefined();
            expect(process.env.GITHUB_JOB).toBeUndefined();
            expect(process.env.GITHUB_WORKFLOW).toBeUndefined();
        },
        DEFAULT_TIMEOUT_MS,
    );

    test(
        "validates /clear command handles file deletion errors gracefully",
        async () => {
            const { default: App } = await import("../../src/cli/app");

            const { lastFrame, stdin } = render(React.createElement(App));

            // Wait for initialization
            await new Promise((resolve) => setTimeout(resolve, 500));

            // Execute /clear command - it should work even if some files can't be deleted
            stdin.write("/clear");
            await new Promise((resolve) => setTimeout(resolve, 500));
            stdin.write("\r");
            await new Promise((resolve) => setTimeout(resolve, 2000)); // Wait for slash command processing

            const currentFrame = lastFrame();
            expect(currentFrame).toBeDefined();

            // Check if the command was processed
            if (currentFrame!.includes("│ > /clear")) {
                // Command is still in input, wait longer
                await new Promise((resolve) => setTimeout(resolve, 2000));
            }

            // The /clear command should work even if some files couldn't be deleted
            // Verify the UI is back to clean state
            const finalFrame = lastFrame();
            expect(finalFrame!).toContain("Welcome to Copilot");

            // Verify history was cleared (the main functionality still works)
            const historyAfterClear = conversationHistoryManager.getDisplayHistory();
            expect(historyAfterClear.length).toBe(0);
        },
        DEFAULT_TIMEOUT_MS,
    );

    test(
        'validates /clear alternative syntax "clear" (without slash) works',
        async () => {
            const { default: App } = await import("../../src/cli/app");

            const { lastFrame, stdin } = render(React.createElement(App));

            // Wait for initialization
            await new Promise((resolve) => setTimeout(resolve, 500));

            // For this test, let's just verify that "clear" without slash is handled
            // as a slash command and results in a clean UI state
            // (We don't need history for this test since it's about command recognition)

            // Test: "clear" command (without slash) should work
            stdin.write("clear");
            await new Promise((resolve) => setTimeout(resolve, 500));
            stdin.write("\r");
            await new Promise((resolve) => setTimeout(resolve, 2000)); // Wait for slash command processing

            const currentFrame = lastFrame();
            expect(currentFrame).toBeDefined();

            // Check if the command was processed
            if (currentFrame!.includes("│ > clear")) {
                // Command is still in input, wait longer
                await new Promise((resolve) => setTimeout(resolve, 2000));
            }

            // Verify that the "clear" command was recognized and processed
            // The UI should be in clean state (the command was processed as a slash command)
            const finalFrame = lastFrame();
            expect(finalFrame!).toContain("Welcome to Copilot");

            // Verify history is empty (clear command should work)
            const historyAfterClear = conversationHistoryManager.getDisplayHistory();
            expect(historyAfterClear.length).toBe(0);
        },
        DEFAULT_TIMEOUT_MS,
    );
});
