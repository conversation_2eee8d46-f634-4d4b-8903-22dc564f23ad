/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

/**
 * Test to verify CLI /cwd slash command works correctly
 */

import { describe, test, expect, vi, beforeEach, afterEach } from "vitest";
import { render } from "ink-testing-library";
import React from "react";
import fs from "fs";
import os from "os";
import path from "path";
import { v4 as uuidv4 } from "uuid";
import {
    initializeTestIsolation,
    restoreTestIsolation,
    verifyHistoryEmpty,
    type TestIsolationState,
} from "./test-isolation-helpers";

// Mock the CLI module below getCompletionWithTools - use real conversation history but mock LLM calls
const createMockCLIInstance = () => ({
    getCompletionWithTools: vi.fn().mockImplementation(async function* (_systemMessage, _messages) {
        // For /cwd command tests, we don't need LLM responses, just mock a basic response
        yield {
            kind: "message",
            message: {
                role: "assistant",
                content: "This is a mock response for testing.",
            },
        };
        yield {
            kind: "response",
            response: {
                content: "This is a mock response for testing.",
            },
        };
    }),
    shutdown: vi.fn().mockResolvedValue(undefined),
});

vi.mock("../../src/cli", () => ({
    CLI: {
        createFromOptions: vi.fn().mockImplementation(() => Promise.resolve(createMockCLIInstance())),
    },
}));

// Mock the agents/prompts module
vi.mock("../../src/agents/prompts", () => ({
    systemMessage: vi.fn().mockResolvedValue("You are a helpful assistant."),
    cliSystemMessage: vi.fn().mockResolvedValue("You are a helpful CLI assistant."),
}));

// Mock child_process
vi.mock("child_process", () => ({
    spawn: vi.fn().mockReturnValue({
        on: vi.fn(),
        stderr: {
            on: vi.fn(),
        },
        kill: vi.fn(),
    }),
}));

// DO NOT mock conversation history - we want to test the real implementation

const DEFAULT_TIMEOUT_MS = 30000; // 30 seconds for mocked LLM calls

// eslint-disable-next-line no-control-regex
const ANSI_CONTROL_REGEX = /\x1B\[[0-9;]*m/g;

describe("CLI /cwd Slash Command Test", () => {
    let tempDir: string;
    let testWorkingDir: string;
    let originalCwd: string;
    let isolationState: TestIsolationState;

    beforeEach(async () => {
        // Store original working directory
        originalCwd = process.cwd();

        // Initialize complete test isolation
        isolationState = await initializeTestIsolation();

        // Verify we start with empty history
        await verifyHistoryEmpty();

        // Create test directories using current working directory to avoid symlinks
        tempDir = path.join(process.cwd(), "tmp-test-cwd", uuidv4());
        testWorkingDir = path.join(tempDir, "test-working-dir");
        fs.mkdirSync(tempDir, { recursive: true });
        fs.mkdirSync(testWorkingDir, { recursive: true });

        // Ensure the real copilot directory exists
        const realCopilotDir = path.join(os.homedir(), ".copilot");
        if (!fs.existsSync(realCopilotDir)) {
            fs.mkdirSync(realCopilotDir, { recursive: true });
        }

        // Get HMAC key from environment variable
        let hmacKey = process.env.COPILOT_HMAC_KEY;

        // Get integration ID from environment variable, with fallback to hardcoded value
        let integrationId = process.env.COPILOT_INTEGRATION_ID || "copilot-developer-dev";

        // If HMAC key not in environment, try to read from the backed up config
        if (!hmacKey && isolationState.configBackup) {
            try {
                const lines = isolationState.configBackup.split("\n");
                for (const line of lines) {
                    if (line.includes("=")) {
                        const [key, ...valueParts] = line.split("=");
                        const value = valueParts.join("=").trim();
                        const trimmedKey = key.trim();
                        if (trimmedKey === "COPILOT_HMAC_KEY" && !hmacKey) {
                            hmacKey = value;
                        }
                        if (trimmedKey === "COPILOT_INTEGRATION_ID" && !integrationId) {
                            integrationId = value;
                        }
                    }
                }
            } catch {
                // Error reading config - continue with test
            }
        }

        // Fail test if we don't have the HMAC key
        if (!hmacKey) {
            throw new Error("No HMAC key found in environment or config file");
        }

        const configContent = `COPILOT_HMAC_KEY=${hmacKey}\nCOPILOT_INTEGRATION_ID=${integrationId}`;
        fs.writeFileSync(isolationState.realConfigPath, configContent);
    });

    afterEach(async () => {
        // Restore original working directory
        try {
            process.chdir(originalCwd);
        } catch {
            // If we can't change back, that's ok for tests
        }

        // Restore complete test isolation
        await restoreTestIsolation(isolationState);

        // Clean up temp directory
        if (fs.existsSync(tempDir)) {
            fs.rmSync(tempDir, { recursive: true, force: true });
        }
        vi.restoreAllMocks();
    });

    test(
        "validates /cwd command shows current working directory",
        async () => {
            const { default: App } = await import("../../src/cli/app");

            const { lastFrame, stdin } = render(React.createElement(App));

            // Wait for initialization
            await new Promise((resolve) => setTimeout(resolve, 500));

            // Test: /cwd with no arguments should show current directory
            stdin.write("/cwd");
            await new Promise((resolve) => setTimeout(resolve, 500));
            stdin.write("\r");
            await new Promise((resolve) => setTimeout(resolve, 2000)); // Wait for slash command processing

            const currentFrame = lastFrame();
            expect(currentFrame).toBeDefined();

            // Check if the command was processed (should show the result, not still in input)
            if (currentFrame!.includes("│ > /cwd")) {
                // Command is still in input, wait longer
                await new Promise((resolve) => setTimeout(resolve, 2000));
            }

            // Should show current working directory
            const finalFrame = lastFrame();
            expect(finalFrame!).toContain("Current working directory:");
            expect(finalFrame!).toContain(process.cwd()); // Should show the actual current directory
        },
        DEFAULT_TIMEOUT_MS,
    );

    test(
        "validates /cwd command changes working directory",
        async () => {
            const { default: App } = await import("../../src/cli/app");

            const { lastFrame, stdin } = render(React.createElement(App));

            // Wait for initialization
            await new Promise((resolve) => setTimeout(resolve, 500));

            // Test: /cwd with directory argument should change directory
            stdin.write(`/cwd ${testWorkingDir}`);
            await new Promise((resolve) => setTimeout(resolve, 500));
            stdin.write("\r");
            await new Promise((resolve) => setTimeout(resolve, 2000)); // Wait for slash command processing

            let currentFrame = lastFrame();
            expect(currentFrame).toBeDefined();

            // Check if the command was processed
            if (currentFrame!.includes(`│ > /cwd ${testWorkingDir}`)) {
                // Command is still in input, wait longer
                await new Promise((resolve) => setTimeout(resolve, 2000));
            }

            // Should show changed working directory message
            const changedFrame = lastFrame();
            expect(changedFrame!).toContain("Changed working directory to:");
            // Remove formatting and check if the path components are present (handling line wrapping)
            const cleanedFrame = changedFrame!.replace(ANSI_CONTROL_REGEX, "").replace(/\s+/g, "");
            const expectedPath = fs.realpathSync(testWorkingDir).replace(/\s+/g, "");
            expect(cleanedFrame).toContain(expectedPath);

            // Verify that the process working directory actually changed
            expect(process.cwd()).toBe(testWorkingDir);

            // Test: Show current directory after change
            stdin.write("/cwd");
            await new Promise((resolve) => setTimeout(resolve, 500));
            stdin.write("\r");
            await new Promise((resolve) => setTimeout(resolve, 2000));

            currentFrame = lastFrame();
            expect(currentFrame!).toContain("Current working directory:");
            // Remove formatting and check if the path components are present (handling line wrapping)
            const cleanedCurrentFrame = currentFrame!.replace(ANSI_CONTROL_REGEX, "").replace(/\s+/g, "");
            const expectedCurrentPath = fs.realpathSync(testWorkingDir).replace(/\s+/g, "");
            expect(cleanedCurrentFrame).toContain(expectedCurrentPath);
        },
        DEFAULT_TIMEOUT_MS,
    );

    test(
        "validates /cwd command handles invalid directory",
        async () => {
            const { default: App } = await import("../../src/cli/app");

            const { lastFrame, stdin } = render(React.createElement(App));

            // Wait for initialization
            await new Promise((resolve) => setTimeout(resolve, 500));

            // Test: /cwd with non-existent directory should show error
            const nonExistentDir = path.join(tempDir, "non-existent-directory");
            stdin.write(`/cwd ${nonExistentDir}`);
            await new Promise((resolve) => setTimeout(resolve, 500));
            stdin.write("\r");
            await new Promise((resolve) => setTimeout(resolve, 2000)); // Wait for slash command processing

            const currentFrame = lastFrame();
            expect(currentFrame).toBeDefined();

            // Check if the command was processed
            if (currentFrame!.includes(`│ > /cwd ${nonExistentDir}`)) {
                // Command is still in input, wait longer
                await new Promise((resolve) => setTimeout(resolve, 2000));
            }

            // Should show error message
            const errorFrame = lastFrame();
            expect(errorFrame!).toContain("Failed to change directory:");
            expect(errorFrame!).toContain("ENOENT"); // Should contain error about directory not existing

            // Verify that the process working directory didn't change
            expect(process.cwd()).toBe(originalCwd); // Should still be in the original directory
        },
        DEFAULT_TIMEOUT_MS,
    );

    test(
        "validates /cwd command handles directory with spaces",
        async () => {
            const { default: App } = await import("../../src/cli/app");

            const { lastFrame, stdin } = render(React.createElement(App));

            // Wait for initialization
            await new Promise((resolve) => setTimeout(resolve, 500));

            // Create a directory with spaces in the name
            const dirWithSpaces = path.join(tempDir, "directory with spaces");
            fs.mkdirSync(dirWithSpaces, { recursive: true });

            // Test: /cwd with directory containing spaces
            stdin.write(`/cwd ${dirWithSpaces}`);
            await new Promise((resolve) => setTimeout(resolve, 500));
            stdin.write("\r");
            await new Promise((resolve) => setTimeout(resolve, 2000)); // Wait for slash command processing

            const currentFrame = lastFrame();
            expect(currentFrame).toBeDefined();

            // Check if the command was processed
            if (currentFrame!.includes(`│ > /cwd ${dirWithSpaces}`)) {
                // Command is still in input, wait longer
                await new Promise((resolve) => setTimeout(resolve, 2000));
            }

            // Should show changed working directory message
            const spacesFrame = lastFrame();
            expect(spacesFrame!).toContain("Changed working directory to:");
            // Remove formatting and check if the path components are present (handling line wrapping)
            const cleanedSpacesFrame = spacesFrame!.replace(ANSI_CONTROL_REGEX, "").replace(/\s+/g, "");
            const expectedSpacesPath = fs.realpathSync(dirWithSpaces).replace(/\s+/g, "");
            expect(cleanedSpacesFrame).toContain(expectedSpacesPath);

            // Verify that the process working directory actually changed
            expect(process.cwd()).toBe(dirWithSpaces);
        },
        DEFAULT_TIMEOUT_MS,
    );

    test(
        "validates /cwd command handles relative paths",
        async () => {
            const { default: App } = await import("../../src/cli/app");

            const { lastFrame, stdin } = render(React.createElement(App));

            // Wait for initialization
            await new Promise((resolve) => setTimeout(resolve, 500));

            // Create a subdirectory in the current working directory
            const subDir = "test-subdir";
            const fullSubDirPath = path.join(process.cwd(), subDir);
            fs.mkdirSync(fullSubDirPath, { recursive: true });

            // Test: /cwd with relative path
            stdin.write(`/cwd ${subDir}`);
            await new Promise((resolve) => setTimeout(resolve, 500));
            stdin.write("\r");
            await new Promise((resolve) => setTimeout(resolve, 2000)); // Wait for slash command processing

            const currentFrame = lastFrame();
            expect(currentFrame).toBeDefined();

            // Check if the command was processed
            if (currentFrame!.includes(`│ > /cwd ${subDir}`)) {
                // Command is still in input, wait longer
                await new Promise((resolve) => setTimeout(resolve, 2000));
            }

            // Should show changed working directory message
            const relativeFrame = lastFrame();
            expect(relativeFrame!).toContain("Changed working directory to:");
            expect(relativeFrame!).toContain(fs.realpathSync(fullSubDirPath));

            // Verify that the process working directory actually changed
            expect(process.cwd()).toBe(fullSubDirPath);

            // Clean up the created subdirectory
            try {
                process.chdir(originalCwd);
                fs.rmSync(fullSubDirPath, { recursive: true, force: true });
            } catch {
                // Cleanup failed, but test can still pass
            }
        },
        DEFAULT_TIMEOUT_MS,
    );
});
