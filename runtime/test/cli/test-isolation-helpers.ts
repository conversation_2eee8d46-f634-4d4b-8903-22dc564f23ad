/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import fs from "fs";
import os from "os";
import path from "path";

/**
 * Helper functions for ensuring complete test isolation between CLI tests
 */

/**
 * Get configuration values for CLI tests with proper fallback from environment variables to config file
 */
export function getTestConfiguration(): {
    hmacKey: string;
    integrationId: string;
} {
    // First try environment variables (for CI)
    let hmacKey = process.env.COPILOT_HMAC_KEY;
    let integrationId =
        process.env.COPILOT_INTEGRATION_ID || process.env.GITHUB_COPILOT_INTEGRATION_ID || "copilot-developer-dev";

    // If HMAC key not in environment, try to read from existing config file (for local dev)
    if (!hmacKey) {
        const existingConfigPath = path.join(os.homedir(), ".copilot", "config");
        if (fs.existsSync(existingConfigPath)) {
            try {
                const existingConfig = fs.readFileSync(existingConfigPath, "utf8");
                const lines = existingConfig.split("\n");
                for (const line of lines) {
                    if (line.includes("=")) {
                        const [key, ...valueParts] = line.split("=");
                        const value = valueParts.join("=").trim(); // Handle values with = in them
                        const trimmedKey = key.trim();
                        if (trimmedKey === "COPILOT_HMAC_KEY" && !hmacKey) {
                            hmacKey = value;
                        }
                        if (trimmedKey === "COPILOT_INTEGRATION_ID" && integrationId === "copilot-developer-dev") {
                            integrationId = value;
                        }
                    }
                }
            } catch (error) {
                console.log("Error reading existing config file:", error);
            }
        }
    }

    // Fail if we don't have the HMAC key
    if (!hmacKey) {
        throw new Error(
            "No HMAC key found in environment or config file. Set COPILOT_HMAC_KEY environment variable or create ~/.copilot/config file.",
        );
    }

    return { hmacKey, integrationId };
}

export interface TestIsolationState {
    historyBackup: string | null;
    configBackup: string | null;
    realHistoryPath: string;
    realConfigPath: string;
}

/**
 * Initialize test isolation by backing up current state and clearing everything
 */
export async function initializeTestIsolation(): Promise<TestIsolationState> {
    // Longer wait to prevent conflicts with other tests
    await new Promise((resolve) => setTimeout(resolve, 500));

    const realCopilotDir = path.join(os.homedir(), ".copilot");
    const realHistoryPath = path.join(realCopilotDir, "conversation-history.json");
    const realConfigPath = path.join(realCopilotDir, "config");

    // Backup existing files
    const historyBackup = fs.existsSync(realHistoryPath) ? fs.readFileSync(realHistoryPath, "utf8") : null;
    const configBackup = fs.existsSync(realConfigPath) ? fs.readFileSync(realConfigPath, "utf8") : null;

    // Force clear everything - multiple approaches to ensure complete cleanup
    // First, aggressively clear the file system
    if (fs.existsSync(realHistoryPath)) {
        try {
            fs.unlinkSync(realHistoryPath);
        } catch (_error) {
            // File might be locked, try to overwrite instead
            fs.writeFileSync(realHistoryPath, JSON.stringify({ entries: [] }, null, 2), "utf8");
        }
    }

    // Clear the directory-level cache of the module to force fresh import
    try {
        delete require.cache[require.resolve("../../src/cli/conversationHistory")];
    } catch (_error) {
        // Module might not be in cache yet, that's fine
    }

    // Clear via conversation history manager (force fresh import)
    const { conversationHistoryManager } = await import("../../src/cli/conversationHistory");
    conversationHistoryManager.clearHistory();

    // Verify everything is completely clear with more robust checking
    let attempts = 0;
    while (attempts < 15) {
        const verifyEmpty = conversationHistoryManager.getDisplayHistory();
        const fileExists = fs.existsSync(realHistoryPath);
        let fileIsEmpty = true;

        if (fileExists) {
            try {
                const content = fs.readFileSync(realHistoryPath, "utf8");
                const parsed = JSON.parse(content);
                fileIsEmpty = !parsed.entries || parsed.entries.length === 0;
            } catch (_error) {
                // File is corrupt or empty, that's fine
                fileIsEmpty = true;
            }
        }

        if (verifyEmpty.length === 0 && fileIsEmpty) {
            break;
        }

        // Still not clear, force it again more aggressively
        if (fs.existsSync(realHistoryPath)) {
            try {
                fs.unlinkSync(realHistoryPath);
            } catch (_error) {
                // File might be locked, overwrite instead
                fs.writeFileSync(realHistoryPath, JSON.stringify({ entries: [] }, null, 2), "utf8");
            }
        }
        conversationHistoryManager.clearHistory();

        // Also try clearing the cache again
        try {
            delete require.cache[require.resolve("../../src/cli/conversationHistory")];
        } catch (_error) {
            // Module might not be in cache, that's fine
        }

        await new Promise((resolve) => setTimeout(resolve, 100));
        attempts++;
    }

    // Final verification with more detailed error
    const finalCheck = conversationHistoryManager.getDisplayHistory();
    if (finalCheck.length > 0) {
        console.error(`History entries still present:`, finalCheck);

        // Final desperate attempt - write empty file directly
        fs.writeFileSync(realHistoryPath, JSON.stringify({ entries: [] }, null, 2), "utf8");
        conversationHistoryManager.clearHistory();

        // Check one more time
        const lastCheck = conversationHistoryManager.getDisplayHistory();
        if (lastCheck.length > 0) {
            throw new Error(
                `Failed to clear conversation history after ${attempts} attempts. Still has ${lastCheck.length} entries: ${JSON.stringify(lastCheck, null, 2)}`,
            );
        }
    }

    // Extended wait after clearing to ensure all operations complete
    await new Promise((resolve) => setTimeout(resolve, 500));

    return {
        historyBackup,
        configBackup,
        realHistoryPath,
        realConfigPath,
    };
}

/**
 * Restore test isolation by restoring backed up state
 */
export async function restoreTestIsolation(state: TestIsolationState): Promise<void> {
    // Wait before cleanup to ensure test operations complete
    await new Promise((resolve) => setTimeout(resolve, 500));

    // Clear the module cache again before restoring
    try {
        delete require.cache[require.resolve("../../src/cli/conversationHistory")];
    } catch (_error) {
        // Module might not be in cache, that's fine
    }

    // Force clear current state first
    if (fs.existsSync(state.realHistoryPath)) {
        try {
            fs.unlinkSync(state.realHistoryPath);
        } catch (_error) {
            // File might be locked, overwrite instead
            fs.writeFileSync(state.realHistoryPath, JSON.stringify({ entries: [] }, null, 2), "utf8");
        }
    }

    // Restore history file
    if (state.historyBackup !== null) {
        fs.writeFileSync(state.realHistoryPath, state.historyBackup, "utf8");
    } else if (fs.existsSync(state.realHistoryPath)) {
        fs.unlinkSync(state.realHistoryPath);
    }

    // Restore config file
    if (state.configBackup !== null) {
        fs.writeFileSync(state.realConfigPath, state.configBackup, "utf8");
    } else if (fs.existsSync(state.realConfigPath)) {
        fs.unlinkSync(state.realConfigPath);
    }

    // Also restore via conversation history manager
    const { conversationHistoryManager } = await import("../../src/cli/conversationHistory");
    if (state.historyBackup !== null) {
        conversationHistoryManager.restoreHistory(state.historyBackup);
    } else {
        conversationHistoryManager.clearHistory();
    }

    // Extended wait to ensure all cleanup completes before next test
    await new Promise((resolve) => setTimeout(resolve, 500));
}

/**
 * Verify that conversation history is completely empty
 */
export async function verifyHistoryEmpty(): Promise<void> {
    // Clear cache and get fresh instance
    try {
        delete require.cache[require.resolve("../../src/cli/conversationHistory")];
    } catch (_error) {
        // Module might not be in cache, that's fine
    }
    const { conversationHistoryManager } = await import("../../src/cli/conversationHistory");

    const history = conversationHistoryManager.getDisplayHistory();

    if (history.length > 0) {
        throw new Error(
            `Expected empty conversation history but found ${history.length} entries: ${JSON.stringify(history, null, 2)}`,
        );
    }

    const realHistoryPath = path.join(os.homedir(), ".copilot", "conversation-history.json");
    if (fs.existsSync(realHistoryPath)) {
        try {
            const fileContent = fs.readFileSync(realHistoryPath, "utf8");
            const fileData = JSON.parse(fileContent);
            if (fileData.entries && fileData.entries.length > 0) {
                throw new Error(
                    `Expected empty history file but found ${fileData.entries.length} entries in file: ${JSON.stringify(fileData.entries, null, 2)}`,
                );
            }
        } catch (_parseError) {
            // If we can't parse the file, that's also a problem unless it's empty
            const fileContent = fs.readFileSync(realHistoryPath, "utf8").trim();
            if (fileContent && fileContent !== "{}" && fileContent !== '{"entries":[]}') {
                throw new Error(`History file exists but is not empty or parseable: ${fileContent}`);
            }
        }
    }
}

/**
 * Wait for conversation history to be recorded after LLM response
 */
export async function waitForHistoryRecording(expectedCount: number, maxWaitTime: number = 8000): Promise<void> {
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
        // Clear cache to get fresh data
        try {
            delete require.cache[require.resolve("../../src/cli/conversationHistory")];
        } catch (_error) {
            // Module might not be in cache, that's fine
        }
        const { conversationHistoryManager } = await import("../../src/cli/conversationHistory");
        const history = conversationHistoryManager.getDisplayHistory();

        if (history.length >= expectedCount) {
            return;
        }

        await new Promise((resolve) => setTimeout(resolve, 200));
    }

    // Final check with detailed error
    try {
        delete require.cache[require.resolve("../../src/cli/conversationHistory")];
    } catch (_error) {
        // Module might not be in cache, that's fine
    }
    const { conversationHistoryManager } = await import("../../src/cli/conversationHistory");
    const finalHistory = conversationHistoryManager.getDisplayHistory();

    throw new Error(
        `Timeout waiting for history recording. Expected ${expectedCount} entries, but only found ${finalHistory.length} after ${maxWaitTime}ms. Current history: ${JSON.stringify(finalHistory, null, 2)}`,
    );
}
