/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import { render } from "ink-testing-library";
import React from "react";
import { ToolConfirmation } from "../../../src/cli/components/toolConfirmation";
import { ToolIntention } from "../../../src/tools";

describe("ToolConfirmation Component", () => {
    let mockOnConfirm: ReturnType<typeof vi.fn>;

    beforeEach(() => {
        mockOnConfirm = vi.fn();
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    describe("Exec intention rendering", () => {
        it("should render exec intention with command", () => {
            const intention: ToolIntention = {
                type: "exec",
                title: "Run test command",
                command: "echo 'hello world'",
            };

            const { lastFrame } = render(<ToolConfirmation intention={intention} onConfirm={mockOnConfirm} />);

            const output = lastFrame();
            expect(output).toContain("Run test command:");
            expect(output).toContain("echo 'hello world'");
            expect(output).toContain("Do you want to run this command?");
            expect(output).toContain("Yes");
            expect(output).toContain("No, and tell Copilot what to do differently");
        });
    });

    describe("Edit intention rendering", () => {
        it("should render edit intention with file name and diff", () => {
            const intention: ToolIntention = {
                type: "edit",
                title: "Update configuration",
                fileName: "config.json",
                diff: `--- a/config.json
+++ b/config.json
@@ -1,3 +1,4 @@
 {
   "name": "app",
-  "version": "1.0.0"
+  "version": "1.0.1",
+  "description": "My app"
 }`,
            };

            const { lastFrame } = render(<ToolConfirmation intention={intention} onConfirm={mockOnConfirm} />);

            const output = lastFrame();
            expect(output).toContain("Update configuration:");
            expect(output).toContain("config.json");
            expect(output).toContain("Do you want to edit this file?");
            expect(output).toContain("Yes");
            expect(output).toContain("No, and tell Copilot what to do differently");
        });

        it("should render simple diff correctly", () => {
            const intention: ToolIntention = {
                type: "edit",
                title: "Fix typo",
                fileName: "readme.txt",
                diff: `--- a/readme.txt
+++ b/readme.txt
@@ -1,1 +1,1 @@
-Hello wrold
+Hello world`,
            };

            const { lastFrame } = render(<ToolConfirmation intention={intention} onConfirm={mockOnConfirm} />);

            const output = lastFrame();
            expect(output).toContain("Fix typo:");
            expect(output).toContain("readme.txt");
            expect(output).toContain("Hello wrold"); // removed line
            expect(output).toContain("Hello world"); // added line
        });

        it("should handle empty diff", () => {
            const intention: ToolIntention = {
                type: "edit",
                title: "Empty change",
                fileName: "test.txt",
                diff: "",
            };

            const { lastFrame } = render(<ToolConfirmation intention={intention} onConfirm={mockOnConfirm} />);

            const output = lastFrame();
            expect(output).toContain("Empty change:");
            expect(output).toContain("test.txt");
            expect(output).toContain("Do you want to edit this file?");
        });
    });

    describe("MCP intention rendering", () => {
        it("should render mcp intention with tool name and arguments", () => {
            const intention: ToolIntention = {
                type: "mcp",
                toolName: "file_reader",
                args: { path: "/home/<USER>/document.txt", encoding: "utf-8" },
            };

            const { lastFrame } = render(<ToolConfirmation intention={intention} onConfirm={mockOnConfirm} />);

            const output = lastFrame();
            expect(output).toContain("file_reader:");
            // Check for JSON delimiters
            expect(output).toContain("{");
            expect(output).toContain("}");
            // Check for expected argument values
            expect(output).toContain("/home/<USER>/document.txt");
            expect(output).toContain("utf-8");
            expect(output).toContain("Do you want to use this tool?");
            expect(output).toContain("Yes");
            expect(output).toContain("No, and tell Copilot what to do differently");
        });

        it("should render mcp intention with complex arguments", () => {
            const intention: ToolIntention = {
                type: "mcp",
                toolName: "database_query",
                args: { query: "SELECT * FROM users WHERE age > 18", limit: 100, options: { include_metadata: true } },
            };

            const { lastFrame } = render(<ToolConfirmation intention={intention} onConfirm={mockOnConfirm} />);

            const output = lastFrame();
            expect(output).toContain("database_query:");
            // Check for JSON delimiters
            expect(output).toContain("{");
            expect(output).toContain("}");
            // Check for expected argument values
            expect(output).toContain("SELECT * FROM users WHERE age > 18");
            expect(output).toContain("100");
            expect(output).toContain("include_metadata");
            expect(output).toContain("true");
            expect(output).toContain("Do you want to use this tool?");
            expect(output).toContain("Yes");
            expect(output).toContain("No, and tell Copilot what to do differently");
        });

        it("should handle empty arguments", () => {
            const intention: ToolIntention = {
                type: "mcp",
                toolName: "status_check",
                args: {},
            };

            const { lastFrame } = render(<ToolConfirmation intention={intention} onConfirm={mockOnConfirm} />);

            const output = lastFrame();
            expect(output).toContain("status_check:");
            // Check for empty JSON object
            expect(output).toContain("{}");
            expect(output).toContain("Do you want to use this tool?");
            expect(output).toContain("Yes");
            expect(output).toContain("No, and tell Copilot what to do differently");
        });
    });

    describe("Diff component", () => {
        it("should render git diff with proper syntax highlighting structure", () => {
            const intention: ToolIntention = {
                type: "edit",
                title: "Test diff",
                fileName: "test.js",
                diff: `diff --git a/test.js b/test.js
index 1234567..abcdefg 100644
--- a/test.js
+++ b/test.js
@@ -1,3 +1,4 @@
 function hello() {
-  console.log("hello");
+  console.log("hello world");
+  return true;
 }`,
            };

            const { lastFrame } = render(<ToolConfirmation intention={intention} onConfirm={mockOnConfirm} />);

            const output = lastFrame();
            // Check for diff header elements
            expect(output).toContain("diff --git");
            expect(output).toContain("test.js");
            expect(output).toContain("@@");

            // Check for proper line numbering in diff output
            // Line numbers should be formatted as "  1 | " (3 chars padded, then " | ")
            // Unchanged line: function hello() should be at line 1
            expect(output).toMatch(/\s*1\s+\|\s+function hello\(\)/);

            // Removed line: console.log("hello"); should be at line 2
            expect(output).toMatch(/\s*2\s+\|\s+-\s*console\.log\("hello"\);/);

            // Added line: console.log("hello world"); should be at line 2 (new numbering)
            expect(output).toMatch(/\s*2\s+\|\s+\+\s*console\.log\("hello world"\);/);

            // Added line: return true; should be at line 3 (new numbering)
            expect(output).toMatch(/\s*3\s+\|\s+\+\s*return true;/);

            // Unchanged line: } should be at line 4 (new numbering)
            expect(output).toMatch(/\s*4\s+\|\s+}/);
        });
    });
});
