/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { afterEach, describe, expect, it, vi } from "vitest";
import { render } from "ink-testing-library";
import React from "react";

// Import the component directly
import { ToolCallResultMessage } from "../../../src/cli/components/messagesDisplay";

describe("ToolCallResultMessage Component", () => {
    afterEach(() => {
        vi.clearAllMocks();
    });

    describe("Success result type", () => {
        it("should render success with expand option when collapsed", () => {
            const { lastFrame } = render(
                <ToolCallResultMessage
                    callId="test-call-id"
                    name="test_tool"
                    intentionSummary="Testing something"
                    result={{
                        type: "success",
                        log: "Tool executed successfully",
                    }}
                    expand={false}
                />,
            );

            const output = lastFrame();
            expect(output).toContain("● test_tool: Testing something");
            expect(output).toContain("Press ctrl+r to expand");
            expect(output).not.toContain("Tool executed successfully");
        });

        it("should render success with log content when expanded", () => {
            const { lastFrame } = render(
                <ToolCallResultMessage
                    callId="test-call-id"
                    name="test_tool"
                    intentionSummary="Testing something"
                    result={{
                        type: "success",
                        log: "Tool executed successfully",
                    }}
                    expand={true}
                />,
            );

            const output = lastFrame();
            expect(output).toContain("● test_tool: Testing something");
            expect(output).toContain("Tool executed successfully");
            expect(output).toContain("Press ctrl+r to collapse");
            expect(output).not.toContain("Press ctrl+r to expand");
        });

        it("should render success without intention summary", () => {
            const { lastFrame } = render(
                <ToolCallResultMessage
                    callId="test-call-id"
                    name="test_tool"
                    intentionSummary={null}
                    result={{
                        type: "success",
                        log: "Tool executed successfully",
                    }}
                    expand={false}
                />,
            );

            const output = lastFrame();
            expect(output).toContain("● test_tool");
            expect(output).not.toContain("test_tool:");
            expect(output).toContain("Press ctrl+r to expand");
        });
    });

    describe("Failure result type", () => {
        it("should render failure with log always visible", () => {
            const { lastFrame } = render(
                <ToolCallResultMessage
                    callId="test-call-id"
                    name="test_tool"
                    intentionSummary="Testing something"
                    result={{
                        type: "failure",
                        log: "Tool execution failed with error",
                    }}
                    expand={false}
                />,
            );

            const output = lastFrame();
            expect(output).toContain("● test_tool: Testing something");
            expect(output).toContain("Tool execution failed with error");
            expect(output).not.toContain("Press ctrl+r to collapse");
            expect(output).not.toContain("Press ctrl+r to expand");
        });

        it("should render failure with log content when expanded (same as collapsed)", () => {
            const { lastFrame } = render(
                <ToolCallResultMessage
                    callId="test-call-id"
                    name="test_tool"
                    intentionSummary="Testing something"
                    result={{
                        type: "failure",
                        log: "Tool execution failed with error",
                    }}
                    expand={true}
                />,
            );

            const output = lastFrame();
            expect(output).toContain("● test_tool: Testing something");
            expect(output).toContain("Tool execution failed with error");
            // Failure messages are always shown and not toggleable
            expect(output).not.toContain("Press ctrl+r to collapse");
            expect(output).not.toContain("Press ctrl+r to expand");
        });

        it("should render failure without intention summary", () => {
            const { lastFrame } = render(
                <ToolCallResultMessage
                    callId="test-call-id"
                    name="test_tool"
                    intentionSummary={null}
                    result={{ type: "failure", log: "Tool execution failed" }}
                    expand={false}
                />,
            );

            const output = lastFrame();
            expect(output).toContain("● test_tool");
            expect(output).not.toContain("test_tool:");
            expect(output).toContain("Tool execution failed");
        });
    });

    describe("Rejected result type", () => {
        it("should render rejected with cancelled message", () => {
            const { lastFrame } = render(
                <ToolCallResultMessage
                    callId="test-call-id"
                    name="test_tool"
                    intentionSummary="Testing something"
                    result={{ type: "rejected" }}
                    expand={false}
                />,
            );

            const output = lastFrame();
            expect(output).toContain("● test_tool: Testing something");
            expect(output).toContain("Cancelled by you.");
            expect(output).not.toContain("Press ctrl+r to collapse");
            expect(output).not.toContain("Press ctrl+r to expand");
        });

        it("should render rejected with cancelled message when expanded (same as collapsed)", () => {
            const { lastFrame } = render(
                <ToolCallResultMessage
                    callId="test-call-id"
                    name="test_tool"
                    intentionSummary="Testing something"
                    result={{ type: "rejected" }}
                    expand={true}
                />,
            );

            const output = lastFrame();
            expect(output).toContain("● test_tool: Testing something");
            expect(output).toContain("Cancelled by you.");
            // Rejected messages are always shown and not toggleable
            expect(output).not.toContain("Press ctrl+r to collapse");
            expect(output).not.toContain("Press ctrl+r to expand");
        });

        it("should render rejected without intention summary", () => {
            const { lastFrame } = render(
                <ToolCallResultMessage
                    callId="test-call-id"
                    name="test_tool"
                    intentionSummary={null}
                    result={{ type: "rejected" }}
                    expand={false}
                />,
            );

            const output = lastFrame();
            expect(output).toContain("● test_tool");
            expect(output).not.toContain("test_tool:");
            expect(output).toContain("Cancelled by you.");
        });
    });

    describe("Edge cases", () => {
        it("should handle empty tool name", () => {
            const { lastFrame } = render(
                <ToolCallResultMessage
                    callId="test-call-id"
                    name=""
                    intentionSummary="Testing something"
                    result={{ type: "success", log: "Success message" }}
                    expand={false}
                />,
            );

            const output = lastFrame();
            expect(output).toContain("●");
            expect(output).toContain("Testing something");
        });

        it("should handle empty intention summary", () => {
            const { lastFrame } = render(
                <ToolCallResultMessage
                    callId="test-call-id"
                    name="test_tool"
                    intentionSummary=""
                    result={{ type: "success", log: "Success message" }}
                    expand={false}
                />,
            );

            const output = lastFrame();
            // Empty string is falsy, so it should render without the colon
            expect(output).toContain("● test_tool");
            expect(output).not.toContain("test_tool:");
            expect(output).toContain("Press ctrl+r to expand");
        });

        it("should handle null intention summary", () => {
            const { lastFrame } = render(
                <ToolCallResultMessage
                    callId="test-call-id"
                    name="test_tool"
                    intentionSummary={null}
                    result={{ type: "success", log: "Success message" }}
                    expand={false}
                />,
            );

            const output = lastFrame();
            expect(output).toContain("● test_tool");
            expect(output).not.toContain("test_tool:");
        });

        it("should handle empty log messages", () => {
            const { lastFrame } = render(
                <ToolCallResultMessage
                    callId="test-call-id"
                    name="test_tool"
                    intentionSummary="Testing something"
                    result={{ type: "success", log: "" }}
                    expand={true}
                />,
            );

            const output = lastFrame();
            expect(output).toContain("● test_tool: Testing something");
            // With empty log string, should show "No Content" and no toggle
            expect(output).toContain("No Content");
            expect(output).not.toContain("Press ctrl+r to collapse");
            expect(output).not.toContain("Press ctrl+r to expand");
        });

        it("should handle whitespace-only log messages", () => {
            const { lastFrame } = render(
                <ToolCallResultMessage
                    callId="test-call-id"
                    name="test_tool"
                    intentionSummary="Testing something"
                    result={{ type: "success", log: "   \n\t  " }}
                    expand={true}
                />,
            );

            const output = lastFrame();
            expect(output).toContain("● test_tool: Testing something");
            // With whitespace-only log string, should show "No Content" and no toggle
            expect(output).toContain("No Content");
            expect(output).not.toContain("Press ctrl+r to collapse");
            expect(output).not.toContain("Press ctrl+r to expand");
        });

        it("should handle multiline log messages", () => {
            const multilineLog = `Line 1
Line 2
Line 3`;

            const { lastFrame } = render(
                <ToolCallResultMessage
                    callId="test-call-id"
                    name="test_tool"
                    intentionSummary="Testing multiline"
                    result={{ type: "success", log: multilineLog }}
                    expand={true}
                />,
            );

            const output = lastFrame();
            expect(output).toContain("● test_tool: Testing multiline");

            // Verify that the multiline content appears correctly with proper indentation
            expect(output).toContain("  Line 1\n  Line 2\n  Line 3");

            expect(output).toContain("Press ctrl+r to collapse");
        });
    });

    describe("Icon and text color behavior", () => {
        it("should use success colors for success result type", () => {
            const { lastFrame } = render(
                <ToolCallResultMessage
                    callId="test-call-id"
                    name="test_tool"
                    intentionSummary="Testing colors"
                    result={{ type: "success", log: "Success message" }}
                    expand={true}
                />,
            );

            // The exact color testing would require checking rendered properties,
            // but we can verify the structure is correct
            const output = lastFrame();
            expect(output).toContain("● test_tool: Testing colors");
            expect(output).toContain("Success message");
        });

        it("should use error colors for failure result type", () => {
            const { lastFrame } = render(
                <ToolCallResultMessage
                    callId="test-call-id"
                    name="test_tool"
                    intentionSummary="Testing colors"
                    result={{ type: "failure", log: "Failure message" }}
                    expand={true}
                />,
            );

            const output = lastFrame();
            expect(output).toContain("● test_tool: Testing colors");
            expect(output).toContain("Failure message");
        });

        it("should use error colors for rejected result type", () => {
            const { lastFrame } = render(
                <ToolCallResultMessage
                    callId="test-call-id"
                    name="test_tool"
                    intentionSummary="Testing colors"
                    result={{ type: "rejected" }}
                    expand={true}
                />,
            );

            const output = lastFrame();
            expect(output).toContain("● test_tool: Testing colors");
            expect(output).toContain("Cancelled by you.");
        });
    });
});
