/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { describe, expect, it } from "vitest";
import { render } from "ink-testing-library";
import React from "react";
import { Box } from "ink";
import SlashCommandPicker from "../../../src/cli/components/slashCommandPicker";
import { SlashCommandDescription } from "../../../src/cli/commands/slashCommands";

describe("SlashCommandPicker Component", () => {
    // Basic rendering tests:
    it("should render as many commands as fit into maxRows", () => {
        const commands: SlashCommandDescription[] = [
            { name: "/cmd1", help: "First command" },
            { name: "/cmd2", args: "[arg]", help: "Second command with args" },
            { name: "/cmd3", help: "Third command" },
        ];

        const outputLines = render(<SlashCommandPicker options={commands} selectedIndex={0} maxRows={2} />)
            .lastFrame()!
            .split("\n")
            .filter((l) => l);

        // Limits output length to maxRows=2
        expect(outputLines.length).toBe(2);
        expect(outputLines[0]).toMatch(/\/cmd1\s+First command/);
        expect(outputLines[1]).toMatch(/\/cmd2 \[arg\]\s+Second command with args/);
    });

    it("should handle maxRows greater than available commands", () => {
        const commands: SlashCommandDescription[] = [
            { name: "/cmd1", help: "First command" },
            { name: "/cmd2", help: "Second command" },
        ];

        const outputLines = render(<SlashCommandPicker options={commands} selectedIndex={0} maxRows={5} />)
            .lastFrame()!
            .split("\n")
            .filter((l) => l);

        expect(outputLines.length).toBe(2);
        expect(outputLines[0]).toMatch(/\/cmd1\s+First command/);
        expect(outputLines[1]).toMatch(/\/cmd2\s+Second command/);
    });

    it("should format command names and help text in separate columns", () => {
        const commands: SlashCommandDescription[] = [
            { name: "/short", help: "Short help" },
            {
                name: "/verylongcommandname",
                args: "[arg]",
                help: "Long command help text",
            },
        ];

        const outputLines = render(<SlashCommandPicker options={commands} selectedIndex={0} maxRows={2} />)
            .lastFrame()!
            .split("\n")
            .filter((l) => l);

        // Both lines should have consistent column alignment
        expect(outputLines.length).toBe(2);
        const line0HelpPos = outputLines[0].indexOf("Short help");
        const line1HelpPos = outputLines[1].indexOf("Long command help text");
        expect(line0HelpPos).toBe(line1HelpPos);
    });

    it("should handle long command names and help text gracefully (within fixed width)", () => {
        const commands: SlashCommandDescription[] = [
            {
                name: "/extremelylongcommandnamethatmightoverflow",
                args: "[verylongarg]",
                help: "Greetings! This is extremely long help text that should be handled gracefully without breaking the layout. Goodbye!",
            },
        ];
        const simulateWidth = 60;
        const outputLines = render(
            <Box width={simulateWidth}>
                <SlashCommandPicker options={commands} selectedIndex={0} maxRows={1} />
            </Box>,
        )
            .lastFrame()!
            .split("\n")
            .filter((l) => l);

        // There will be only one line. Some of both "command name" and "help" will be
        // displayed, but we're not going to assert exactly how much of each since that's
        // up to ink's layout system.
        expect(outputLines.length).toBe(1);
        expect(outputLines[0].length).toBeLessThanOrEqual(simulateWidth);
        expect(outputLines[0]).toContain("/extremelylong");
        expect(outputLines[0]).toContain("Greetings!");
        expect(outputLines[0]).not.toContain("Goodbye!");
    });

    it("should scroll up/down when selected index is beyond visible area", () => {
        const commands: SlashCommandDescription[] = [
            { name: "/cmd0", help: "Command 0" },
            { name: "/cmd1", help: "Command 1" },
            { name: "/cmd2", help: "Command 2" },
            { name: "/cmd3", help: "Command 3" },
        ];

        // Start by rendering from the top
        const { lastFrame, rerender } = render(<SlashCommandPicker options={commands} selectedIndex={0} maxRows={2} />);

        // Now play out the following sequence of user actions
        const script = [
            // Still on screen, so scroll position unchanged
            { selectedIndex: 1, expectedCommands: ["/cmd0", "/cmd1"] },

            // Exceeded scroll bounds, so scrolls down
            { selectedIndex: 2, expectedCommands: ["/cmd1", "/cmd2"] },
            { selectedIndex: 3, expectedCommands: ["/cmd2", "/cmd3"] },

            // Still on screen, so scroll position unchanged
            { selectedIndex: 2, expectedCommands: ["/cmd2", "/cmd3"] },

            // Exceeded scroll bounds, so scrolls up
            { selectedIndex: 1, expectedCommands: ["/cmd1", "/cmd2"] },
        ];

        script.forEach((step) => {
            rerender(<SlashCommandPicker options={commands} selectedIndex={step.selectedIndex} maxRows={2} />);
            const outputLines = lastFrame()!
                .split("\n")
                .filter((l) => l);
            expect(outputLines.length).toBe(step.expectedCommands.length);
            step.expectedCommands.forEach((expectedCommand, index) =>
                expect(outputLines[index]).toContain(expectedCommand),
            );
        });
    });
});
