/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

/**
 * Test to verify CLI startup works correctly with HMAC key from either user config or environment
 */

import fs from "fs";
import os from "os";
import path from "path";
import { v4 as uuidv4 } from "uuid";
import { exec, ExecException } from "child_process";
import { promisify } from "util";
import { getTestConfiguration } from "./test-isolation-helpers";

const execAsync = promisify(exec);

const DEFAULT_TIMEOUT_MS = 2 * 60 * 1000; // 2 minutes

describe("CLI startup tests", () => {
    test(
        "CLI -p parameter executes prompt and returns response",
        async () => {
            const tempDir = path.join(os.tmpdir(), "cli-test", uuidv4());
            const copilotDir = path.join(tempDir, ".copilot");
            const configPath = path.join(copilotDir, "config");

            try {
                // Create temporary directory structure
                fs.mkdirSync(tempDir, { recursive: true });
                fs.mkdirSync(copilotDir, { recursive: true });

                // Create config with HMAC key and integration ID in key=value format (as used by CLI)
                const { hmacKey, integrationId } = getTestConfiguration();

                const configContent = `COPILOT_HMAC_KEY=${hmacKey}\nCOPILOT_INTEGRATION_ID=${integrationId}`;
                fs.writeFileSync(configPath, configContent);

                // Path to the built CLI
                const cliPath = path.join(process.cwd(), "dist-cli", "index.js");

                // Ensure CLI is built
                if (!fs.existsSync(cliPath)) {
                    throw new Error('CLI not built. Run "npm run build:cli" first.');
                }

                try {
                    // Test the CLI with -p parameter
                    const { stdout, stderr } = await execAsync(
                        `HOME="${tempDir}" USERPROFILE="${tempDir}" node "${cliPath}" -p "What is 2+2?"`,
                        {
                            timeout: 30000, // 30 seconds timeout
                            env: {
                                ...process.env,
                                HOME: tempDir,
                                USERPROFILE: tempDir,
                            },
                        },
                    );

                    // Verify that we got a response
                    expect(stdout).toBeTruthy();
                    expect(stdout.trim().length).toBeGreaterThan(0);

                    // Verify the response makes sense for the math question
                    expect(stdout.toLowerCase()).toMatch(/4|four/);

                    console.log("CLI output:", stdout);
                    console.log("CLI stderr:", stderr);
                } catch (error: unknown) {
                    // Log error details and fail the test
                    const execError = error as ExecException;
                    console.log("CLI execution error:", execError.message);
                    console.log("stdout:", execError.stdout);
                    console.log("stderr:", execError.stderr);

                    // Re-throw the error to fail the test
                    throw error;
                }
            } finally {
                // Clean up
                if (fs.existsSync(tempDir)) {
                    fs.rmSync(tempDir, { recursive: true, force: true });
                }
            }
        },
        DEFAULT_TIMEOUT_MS,
    );
});
