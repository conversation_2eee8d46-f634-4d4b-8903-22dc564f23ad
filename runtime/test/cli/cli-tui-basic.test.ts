/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

/**
 * Test to verify CLI TUI renders correctly and handles basic queries
 */

import fs from "fs";
import { render } from "ink-testing-library";
import os from "os";
import path from "path";
import React from "react";
import { v4 as uuidv4 } from "uuid";
import { afterEach, beforeEach, describe, expect, test, vi } from "vitest";
import { getTestConfiguration } from "./test-isolation-helpers";

// Set up a global variable to store the temp directory for this test
let globalTempDir: string;

// Mock os.homedir globally to always return the current test temp directory
vi.mock("os", async () => {
    const actualOs = await vi.importActual("os");
    return {
        ...actualOs,
        homedir: () => globalTempDir || os.homedir(),
    };
});

// Mock the CLI module to avoid actual execution
const createMockCLIInstance = () => ({
    getCompletionWithTools: vi.fn().mockImplementation(async function* (_systemMessage, messages) {
        // Get the last user message to determine what to respond with
        const lastUserMessage = messages[messages.length - 1];
        const query = lastUserMessage?.content?.toLowerCase() || "";

        let response = "";

        if (query.includes("squid")) {
            response =
                "A squid is a marine cephalopod mollusk with a soft body, eight arms, and two tentacles. They are known for their intelligence, ability to change color, and their unique propulsion system using water jets.";
        } else if (query.includes("clam")) {
            response =
                "A clam is a bivalve mollusk with a shell consisting of two hinged parts. They are filter feeders that live in marine and freshwater environments, burying themselves in sand or mud.";
        } else if (query.includes("animals") && query.includes("discussed")) {
            // This should reference the conversation history passed in messages
            const previousMessages = messages.slice(0, -1); // All messages except the current one
            const mentionedAnimals = [];

            for (const msg of previousMessages) {
                if (msg.role === "user") {
                    if (msg.content?.toLowerCase().includes("squid")) {
                        mentionedAnimals.push("squid");
                    }
                    if (msg.content?.toLowerCase().includes("clam")) {
                        mentionedAnimals.push("clam");
                    }
                }
            }

            if (mentionedAnimals.length > 0) {
                response = `Based on our conversation, we have discussed these animals: ${mentionedAnimals.join(" and ")}. They are both fascinating marine creatures with different characteristics.`;
            } else {
                response = "I don't see any animals mentioned in our conversation yet.";
            }
        } else {
            response = "I can help you with information about various topics. What would you like to know?";
        }

        yield {
            kind: "message",
            message: {
                role: "assistant",
                content: response,
            },
        };
        yield {
            kind: "response",
            response: {
                content: response,
            },
        };
    }),
    shutdown: vi.fn().mockResolvedValue(undefined),
});

vi.mock("../../src/cli", () => ({
    CLI: {
        createFromOptions: vi.fn().mockImplementation(() => Promise.resolve(createMockCLIInstance())),
    },
}));

// Mock the agents/prompts module
vi.mock("../../src/agents/prompts", () => ({
    systemMessage: vi.fn().mockResolvedValue("You are a helpful assistant."),
    cliSystemMessage: vi.fn().mockResolvedValue("You are a helpful CLI assistant."),
}));

// Don't mock filesystem operations - we need real file access for config loading

// Mock child_process
vi.mock("child_process", () => ({
    spawn: vi.fn().mockReturnValue({
        on: vi.fn(),
        stderr: {
            on: vi.fn(),
        },
        kill: vi.fn(),
    }),
}));

// Don't mock conversation history - let the real implementation run in basic TUI tests

const DEFAULT_TIMEOUT_MS = 30000; // 30 seconds

describe("CLI TUI Basic Test", () => {
    let tempDir: string;
    let copilotDir: string;
    let configPath: string;

    beforeEach(() => {
        // Setup temporary directory structure
        tempDir = path.join(os.tmpdir(), "cli-tui-basic-test", uuidv4());
        copilotDir = path.join(tempDir, ".copilot");
        configPath = path.join(copilotDir, "config");

        // Set the global temp directory for the mock
        globalTempDir = tempDir;

        // Create temporary directory structure
        fs.mkdirSync(tempDir, { recursive: true });
        fs.mkdirSync(copilotDir, { recursive: true });

        // Get configuration with proper fallback logic
        const { hmacKey, integrationId } = getTestConfiguration();

        const configContent = `COPILOT_HMAC_KEY=${hmacKey}\nCOPILOT_INTEGRATION_ID=${integrationId}`;
        fs.writeFileSync(configPath, configContent);

        // Clear CLI-specific mocks but not the global os mock
        vi.clearAllMocks();
    });

    afterEach(() => {
        // Clean up
        if (fs.existsSync(tempDir)) {
            fs.rmSync(tempDir, { recursive: true, force: true });
        }
        // Clear the global temp directory
        globalTempDir = "";
        vi.restoreAllMocks();
    });

    test(
        'renders CLI TUI, sends "what is a squid" message, and validates non-error result',
        async () => {
            // Clear module cache to ensure fresh import with mocks
            try {
                delete require.cache[require.resolve("../../src/cli/app")];
            } catch {
                // Module might not be in cache yet
            }

            // Import App component after mocks are set up
            const { default: App } = await import("../../src/cli/app");

            // Render the App component
            const { lastFrame, stdin } = render(React.createElement(App));

            // Wait a bit for the component to initialize
            await new Promise((resolve) => setTimeout(resolve, 100));

            // Verify the initial UI is rendered correctly
            expect(lastFrame()).toContain("Welcome to Copilot");
            expect(lastFrame()).toContain("cwd:");
            expect(lastFrame()).toContain("> "); // Input prompt
            expect(lastFrame()).toContain("Type ? for help");

            // Send the squid query to the textbox
            stdin.write("what is a squid");

            // Wait for the input to be processed
            await new Promise((resolve) => setTimeout(resolve, 100));

            // Verify the input appears in the textbox
            expect(lastFrame()).toContain("what is a squid");

            // Submit the query by pressing Enter
            stdin.write("\r"); // Enter key

            // Wait for the response to be processed - longer timeout for async operations
            await new Promise((resolve) => setTimeout(resolve, 2000));

            // Get the final frame content
            const finalFrame = lastFrame();

            console.log("Final CLI TUI output:", finalFrame);

            // Validate that the result is not an error
            expect(finalFrame).not.toContain("[error]");
            expect(finalFrame).not.toContain("Execution failed");
            expect(finalFrame).not.toContain("Error:");

            // Validate that the user query is shown in the new format
            expect(finalFrame).toContain("> what is a squid");

            // Validate that the assistant responded with bullet points
            expect(finalFrame).toContain("●");

            // Validate that we got a response about squids - check the actual response
            expect(finalFrame).toContain("squid");
            expect(finalFrame).toContain("cephalopod");
            expect(finalFrame).toContain("mollusk");
        },
        DEFAULT_TIMEOUT_MS,
    );

    test("handles input focus and cursor positioning correctly", async () => {
        // Clear module cache to ensure fresh import with mocks
        try {
            delete require.cache[require.resolve("../../src/cli/app")];
        } catch {
            // Module might not be in cache yet
        }

        const { default: App } = await import("../../src/cli/app");

        const { lastFrame, stdin } = render(React.createElement(App));

        // Wait for initialization
        await new Promise((resolve) => setTimeout(resolve, 100));

        // Type characters one by one to test cursor positioning
        stdin.write("w");
        await new Promise((resolve) => setTimeout(resolve, 50));

        stdin.write("h");
        await new Promise((resolve) => setTimeout(resolve, 50));

        stdin.write("a");
        await new Promise((resolve) => setTimeout(resolve, 50));

        stdin.write("t");
        await new Promise((resolve) => setTimeout(resolve, 50));

        // Verify the text appears correctly
        expect(lastFrame()).toContain("what");

        // Test backspace
        stdin.write("\x08"); // Backspace
        await new Promise((resolve) => setTimeout(resolve, 50));

        // Should show 'wha' after backspace
        expect(lastFrame()).toContain("wha");

        // Continue typing
        stdin.write("t is a squid");
        await new Promise((resolve) => setTimeout(resolve, 100));

        // Final text should be complete
        expect(lastFrame()).toContain("what is a squid");
    });

    test("validates textbox handles special keys correctly", async () => {
        // Clear module cache to ensure fresh import with mocks
        try {
            delete require.cache[require.resolve("../../src/cli/app")];
        } catch {
            // Module might not be in cache yet
        }

        const { default: App } = await import("../../src/cli/app");

        const { lastFrame, stdin } = render(React.createElement(App));

        // Wait for initialization
        await new Promise((resolve) => setTimeout(resolve, 100));

        // Type some text
        stdin.write("test input");
        await new Promise((resolve) => setTimeout(resolve, 100));

        // Verify text appears
        expect(lastFrame()).toContain("test input");

        // Test left arrow key navigation
        stdin.write("\x1b[D"); // Left arrow
        await new Promise((resolve) => setTimeout(resolve, 50));

        // Test right arrow key navigation
        stdin.write("\x1b[C"); // Right arrow
        await new Promise((resolve) => setTimeout(resolve, 50));

        // Test escape key (should clear input when not processing)
        stdin.write("\x1b"); // Escape
        await new Promise((resolve) => setTimeout(resolve, 100));

        // After escape, input should be cleared
        const frameAfterEscape = lastFrame();
        expect(frameAfterEscape).not.toContain("test input");

        // Type again to verify input works after escape
        stdin.write("new text");
        await new Promise((resolve) => setTimeout(resolve, 100));

        expect(lastFrame()).toContain("new text");
    });
});
