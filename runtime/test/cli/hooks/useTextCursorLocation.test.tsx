/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
// @vitest-environment jsdom
import { act } from "react";
import { useTextCursorLocation } from "../../../src/cli/hooks/useTextCursorLocation";
import { renderHook } from "@testing-library/react";
import { Key } from "../../../src/cli/hooks/useInput";

function createParsedKeyPress(): Key {
    return {
        upArrow: false,
        downArrow: false,
        leftArrow: false,
        rightArrow: false,
        pageDown: false,
        pageUp: false,
        return: false,
        escape: false,
        backspace: false,
        delete: false,
        tab: false,
        ctrl: false,
        meta: false,
        shift: false,
        home: false,
        end: false,
    };
}

describe("useTextCursorLocation Hook", () => {
    it("should initialize to an empty string and 0 cursor position", async () => {
        const { result } = renderHook(() => useTextCursorLocation());

        expect(result.current.text).toBe("");
        expect(result.current.cursorPosition).toBe(0);
    });

    it("setText: updates text and resets cursor to end", async () => {
        const { result } = renderHook(() => useTextCursorLocation());

        act(() => {
            result.current.setText("Hello");
            result.current.setText("World!");
        });

        expect(result.current.text).toBe("World!");
        expect(result.current.cursorPosition).toBe(6); // Cursor at end of new text
    });

    it("setCursorPosition: sets cursor to specified position", async () => {
        const { result } = renderHook(() => useTextCursorLocation());

        act(() => {
            result.current.setText("Hello");
            result.current.setCursorPosition(2);
        });

        expect(result.current.cursorPosition).toBe(2);
    });

    it("setCursorPosition: sets cursor to specified position", async () => {
        const { result } = renderHook(() => useTextCursorLocation());

        act(() => {
            result.current.setText("Hello");
            result.current.setCursorPosition(2);
        });

        expect(result.current.cursorPosition).toBe(2);
    });

    it("insertInput: appends input at cursor position", async () => {
        const { result } = renderHook(() => useTextCursorLocation());

        act(() => {
            result.current.setText("Hello");
            result.current.setCursorPosition(0); // Move cursor to end of "Hello"
        });

        expect(result.current.text).toBe("Hello");
        expect(result.current.cursorPosition).toBe(0);

        act(() => {
            result.current.insertInput("Goodbye");
        });

        expect(result.current.text).toBe("GoodbyeHello");
        expect(result.current.cursorPosition).toBe(7);
    });

    it("backspace: removes character before cursor", async () => {
        const { result } = renderHook(() => useTextCursorLocation());

        act(() => {
            result.current.setText("Howdy");
        });

        expect(result.current.text).toBe("Howdy");
        expect(result.current.cursorPosition).toBe(5);

        act(() => {
            result.current.backspace();
        });

        expect(result.current.text).toBe("Howd");
        expect(result.current.cursorPosition).toBe(4);
    });

    it("forwardDelete removes character in front of cursor", async () => {
        const { result } = renderHook(() => useTextCursorLocation());

        act(() => {
            result.current.setText("Hello");
            result.current.setCursorPosition(2);
        });

        expect(result.current.text).toBe("Hello");
        expect(result.current.cursorPosition).toBe(2);

        act(() => {
            result.current.forwardDelete();
        });

        expect(result.current.text).toBe("Helo");
        expect(result.current.cursorPosition).toBe(2);
    });

    it("clear: resets text and cursor position", async () => {
        const { result } = renderHook(() => useTextCursorLocation());

        act(() => {
            result.current.setText("Hello");
            result.current.clear();
        });

        expect(result.current.text).toBe("");
        expect(result.current.cursorPosition).toBe(0);
    });

    it("moveRight: moves cursor one to right", async () => {
        const { result } = renderHook(() => useTextCursorLocation());

        act(() => {
            result.current.setText("Hello");
            result.current.setCursorPosition(2);
        });

        expect(result.current.cursorPosition).toBe(2);

        act(() => {
            result.current.moveRight();
        });

        expect(result.current.cursorPosition).toBe(3);
    });

    it("moveLeft: moves cursor on to left", async () => {
        const { result } = renderHook(() => useTextCursorLocation());

        act(() => {
            result.current.setText("Hello");
        });

        expect(result.current.cursorPosition).toBe(5);

        act(() => {
            result.current.moveLeft();
        });

        expect(result.current.cursorPosition).toBe(4);
    });

    it("moveRight at end does not change position", async () => {
        const { result } = renderHook(() => useTextCursorLocation());

        act(() => {
            result.current.setText("Hello");
        });

        expect(result.current.cursorPosition).toBe(5); // At end of text

        act(() => {
            result.current.moveRight();
        });

        expect(result.current.cursorPosition).toBe(5); // Still at end
    });

    it("moveLeft at start does not change position", async () => {
        const { result } = renderHook(() => useTextCursorLocation());

        act(() => {
            result.current.setText("Hello");
            result.current.setCursorPosition(0); // At start
        });

        expect(result.current.cursorPosition).toBe(0); // Still at start

        act(() => {
            result.current.moveLeft();
        });

        expect(result.current.cursorPosition).toBe(0); // Still at start
    });

    it("handleCommonKeyPress: handles home", async () => {
        const { result } = renderHook(() => useTextCursorLocation());

        act(() => {
            result.current.setText("Hello");
            const key = createParsedKeyPress();
            key.home = true;
            result.current.handleCommonKeyPress("", key);
        });

        expect(result.current.cursorPosition).toBe(0);
    });

    it("handleCommonKeyPress: handles end", async () => {
        const { result } = renderHook(() => useTextCursorLocation());

        act(() => {
            result.current.setText("Hello");
            result.current.setCursorPosition(0);
        });

        expect(result.current.cursorPosition).toBe(0);

        act(() => {
            const key = createParsedKeyPress();
            key.end = true;
            result.current.handleCommonKeyPress("", key);
        });

        expect(result.current.cursorPosition).toBe(5);
    });

    it("backspaceWord: deletes word before cursor", async () => {
        const { result } = renderHook(() => useTextCursorLocation());

        act(() => {
            result.current.setText("Hello World");
        });

        expect(result.current.text).toBe("Hello World");
        expect(result.current.cursorPosition).toBe(11);

        act(() => {
            result.current.backspaceWord();
        });

        expect(result.current.cursorPosition).toBe(6);
        expect(result.current.text).toBe("Hello ");
    });

    it("forwardDeleteWord: deletes word after cursor", async () => {
        const { result } = renderHook(() => useTextCursorLocation());

        act(() => {
            result.current.setText("Hello World");
            result.current.setCursorPosition(0); // Move cursor to start
        });

        expect(result.current.text).toBe("Hello World");
        expect(result.current.cursorPosition).toBe(0);

        act(() => {
            result.current.forwardDeleteWord();
        });

        expect(result.current.cursorPosition).toBe(0);
        expect(result.current.text).toBe(" World");
    });

    it("moveWordLeft: moves cursor to start of previous word", async () => {
        const { result } = renderHook(() => useTextCursorLocation());

        act(() => {
            result.current.setText("Hello beautiful world");
        });

        expect(result.current.cursorPosition).toBe(21);

        act(() => {
            result.current.moveWordLeft();
        });

        expect(result.current.cursorPosition).toBe(16); // Start of "world"

        act(() => {
            result.current.moveWordLeft();
        });

        expect(result.current.cursorPosition).toBe(6); // Start of "beautiful"
    });

    it("moveWordRight: moves cursor to start of next word", async () => {
        const { result } = renderHook(() => useTextCursorLocation());

        act(() => {
            result.current.setText("Hello beautiful world");
            result.current.setCursorPosition(0);
        });

        expect(result.current.cursorPosition).toBe(0);

        act(() => {
            result.current.moveWordRight();
        });

        expect(result.current.cursorPosition).toBe(6); // Start of "beautiful"

        act(() => {
            result.current.moveWordRight();
        });

        expect(result.current.cursorPosition).toBe(16); // Start of "world"
    });

    it("handleCommonKeyPress: handles ctrl+backspace for word deletion", async () => {
        const { result } = renderHook(() => useTextCursorLocation());

        act(() => {
            result.current.setText("Hello World");
        });

        expect(result.current.cursorPosition).toBe(11);

        act(() => {
            const key = createParsedKeyPress();
            key.ctrl = true;
            key.backspace = true;
            result.current.handleCommonKeyPress("", key);
        });

        expect(result.current.text).toBe("Hello ");
        expect(result.current.cursorPosition).toBe(6);
    });

    it("handleCommonKeyPress: handles meta+backspace for word deletion", async () => {
        const { result } = renderHook(() => useTextCursorLocation());

        act(() => {
            result.current.setText("Hello World");
        });

        expect(result.current.cursorPosition).toBe(11);

        act(() => {
            const key = createParsedKeyPress();
            key.meta = true;
            key.backspace = true;
            result.current.handleCommonKeyPress("", key);
        });

        expect(result.current.text).toBe("Hello ");
        expect(result.current.cursorPosition).toBe(6);
    });

    it("handleCommonKeyPress: handles ctrl+w for word deletion", async () => {
        const { result } = renderHook(() => useTextCursorLocation());

        act(() => {
            result.current.setText("Hello World");
        });

        expect(result.current.cursorPosition).toBe(11);

        act(() => {
            const key = createParsedKeyPress();
            key.ctrl = true;
            result.current.handleCommonKeyPress("w", key);
        });

        expect(result.current.text).toBe("Hello ");
        expect(result.current.cursorPosition).toBe(6);
    });

    it("handleCommonKeyPress: handles ctrl+delete for forward word deletion", async () => {
        const { result } = renderHook(() => useTextCursorLocation());

        act(() => {
            result.current.setText("Hello World");
            result.current.setCursorPosition(0);
        });

        expect(result.current.cursorPosition).toBe(0);

        act(() => {
            const key = createParsedKeyPress();
            key.ctrl = true;
            key.delete = true;
            result.current.handleCommonKeyPress("", key);
        });

        expect(result.current.text).toBe(" World");
        expect(result.current.cursorPosition).toBe(0);
    });

    it("handleCommonKeyPress: handles ctrl+left arrow for word navigation", async () => {
        const { result } = renderHook(() => useTextCursorLocation());

        act(() => {
            result.current.setText("Hello World");
        });

        expect(result.current.cursorPosition).toBe(11);

        act(() => {
            const key = createParsedKeyPress();
            key.ctrl = true;
            key.leftArrow = true;
            result.current.handleCommonKeyPress("", key);
        });

        expect(result.current.cursorPosition).toBe(6); // Start of "World"
    });

    it("handleCommonKeyPress: handles meta+b for word navigation left", async () => {
        const { result } = renderHook(() => useTextCursorLocation());

        act(() => {
            result.current.setText("Hello World");
        });

        expect(result.current.cursorPosition).toBe(11);

        act(() => {
            const key = createParsedKeyPress();
            key.meta = true;
            result.current.handleCommonKeyPress("b", key);
        });

        expect(result.current.cursorPosition).toBe(6); // Start of "World"
    });

    it("handleCommonKeyPress: handles meta+f for word navigation right", async () => {
        const { result } = renderHook(() => useTextCursorLocation());

        act(() => {
            result.current.setText("Hello World");
            result.current.setCursorPosition(0);
        });

        expect(result.current.cursorPosition).toBe(0);

        act(() => {
            const key = createParsedKeyPress();
            key.meta = true;
            result.current.handleCommonKeyPress("f", key);
        });

        expect(result.current.cursorPosition).toBe(6); // Start of "World"
    });

    it("handleCommonKeyPress: handles ctrl+a for home", async () => {
        const { result } = renderHook(() => useTextCursorLocation());

        act(() => {
            result.current.setText("Hello World");
        });

        expect(result.current.cursorPosition).toBe(11);

        act(() => {
            const key = createParsedKeyPress();
            key.ctrl = true;
            result.current.handleCommonKeyPress("a", key);
        });

        expect(result.current.cursorPosition).toBe(0);
    });

    it("handleCommonKeyPress: handles ctrl+e for end", async () => {
        const { result } = renderHook(() => useTextCursorLocation());

        act(() => {
            result.current.setText("Hello World");
            result.current.setCursorPosition(0);
        });

        expect(result.current.cursorPosition).toBe(0);

        act(() => {
            const key = createParsedKeyPress();
            key.ctrl = true;
            result.current.handleCommonKeyPress("e", key);
        });

        expect(result.current.cursorPosition).toBe(11);
    });

    it("handleCommonKeyPress: handles ctrl+h for backspace", async () => {
        const { result } = renderHook(() => useTextCursorLocation());

        act(() => {
            result.current.setText("Hello");
        });

        expect(result.current.cursorPosition).toBe(5);

        act(() => {
            const key = createParsedKeyPress();
            key.ctrl = true;
            result.current.handleCommonKeyPress("h", key);
        });

        expect(result.current.text).toBe("Hell");
        expect(result.current.cursorPosition).toBe(4);
    });

    it("handleCommonKeyPress: handles ctrl+d for forward delete", async () => {
        const { result } = renderHook(() => useTextCursorLocation());

        act(() => {
            result.current.setText("Hello");
            result.current.setCursorPosition(2);
        });

        expect(result.current.cursorPosition).toBe(2);

        act(() => {
            const key = createParsedKeyPress();
            key.ctrl = true;
            result.current.handleCommonKeyPress("d", key);
        });

        expect(result.current.text).toBe("Helo");
        expect(result.current.cursorPosition).toBe(2);
    });

    it("backspaceWord: handles multiple words and whitespace correctly", async () => {
        const { result } = renderHook(() => useTextCursorLocation());

        act(() => {
            result.current.setText("Hello   beautiful   world");
            result.current.setCursorPosition(13); // In the middle of "beautiful"
        });

        act(() => {
            result.current.backspaceWord();
        });

        expect(result.current.text).toBe("Hello   iful   world");
        expect(result.current.cursorPosition).toBe(8);

        act(() => {
            result.current.backspaceWord();
        });

        expect(result.current.text).toBe("iful   world");
        expect(result.current.cursorPosition).toBe(0);
    });

    it("forwardDeleteWord: handles multiple words and whitespace correctly", async () => {
        const { result } = renderHook(() => useTextCursorLocation());

        act(() => {
            result.current.setText("Hello   beautiful   world");
            result.current.setCursorPosition(8); // Start of "beautiful"
        });

        act(() => {
            result.current.forwardDeleteWord();
        });

        expect(result.current.text).toBe("Hello      world");
        expect(result.current.cursorPosition).toBe(8);
    });

    it("word operations: handle edge cases at start and end", async () => {
        const { result } = renderHook(() => useTextCursorLocation());

        act(() => {
            result.current.setText("Hello World");
            result.current.setCursorPosition(0);
        });

        // moveWordLeft at start should not change position
        act(() => {
            result.current.moveWordLeft();
        });

        expect(result.current.cursorPosition).toBe(0);

        // backspaceWord at start should not change anything
        act(() => {
            result.current.backspaceWord();
        });

        expect(result.current.text).toBe("Hello World");
        expect(result.current.cursorPosition).toBe(0);

        // Move to end
        act(() => {
            result.current.setCursorPosition(11);
        });

        // moveWordRight at end should not change position
        act(() => {
            result.current.moveWordRight();
        });

        expect(result.current.cursorPosition).toBe(11);

        // forwardDeleteWord at end should not change anything
        act(() => {
            result.current.forwardDeleteWord();
        });

        expect(result.current.text).toBe("Hello World");
        expect(result.current.cursorPosition).toBe(11);
    });

    it("handleCommonKeyPress: Input should not be inserted if part of a shortcut (ctrl/meta/shift key pressed)", async () => {
        const { result } = renderHook(() => useTextCursorLocation());

        act(() => {
            result.current.setText("Hello World");
        });

        expect(result.current.cursorPosition).toBe(11);

        act(() => {
            const key = createParsedKeyPress();
            key.ctrl = true;
            result.current.handleCommonKeyPress("r", key);
        });

        result.current.setText("Hello World");
        expect(result.current.cursorPosition).toBe(11);

        act(() => {
            const key = createParsedKeyPress();
            key.meta = true;
            result.current.handleCommonKeyPress("r", key);
        });

        result.current.setText("Hello World");
        expect(result.current.cursorPosition).toBe(11);
    });
});
