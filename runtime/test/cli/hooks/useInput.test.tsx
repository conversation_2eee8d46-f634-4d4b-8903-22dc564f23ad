/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import { Text } from "ink";
import useInput from "../../../src/cli/hooks/useInput";
import React, { useState } from "react";
import { render } from "ink-testing-library";

const UseInputFixture = () => {
    // State to capture user input
    const [value, setValue] = useState("");
    useInput((input, key) => {
        let output = input || "";
        if (key.ctrl) {
            output += "ctrl";
        }
        if (key.meta) {
            output += "meta";
        }

        if (key.shift) {
            output += "shift";
        }

        if (key.tab) {
            output += "tab";
        }

        if (key.escape) {
            output += "escape";
        }

        if (key.upArrow) {
            output += "upArrow";
        }

        if (key.downArrow) {
            output += "downArrow";
        }

        if (key.leftArrow) {
            output += "leftArrow";
        }

        if (key.rightArrow) {
            output += "rightArrow";
        }

        if (key.backspace) {
            output += "backspace";
        }

        if (key.delete) {
            output += "delete";
        }

        if (key.return) {
            output += "return";
        }

        if (key.home) {
            output += "home";
        }

        if (key.end) {
            output += "end";
        }

        setValue(output);
    });

    return <Text>{value}</Text>;
};

describe("Use Input Hook", () => {
    it("useInput - handle lowercase character", async () => {
        const { stdin, lastFrame } = render(<UseInputFixture />);
        await delay(100);
        stdin.write("q");
        await delay(100);
        expect(lastFrame()).toContain("q");
    });

    it("useInput - handle uppercase character", async () => {
        const { stdin, lastFrame } = render(<UseInputFixture />);
        await delay(100);
        stdin.write("Q");
        await delay(100);
        expect(lastFrame()).toContain("shift");
        expect(lastFrame()).toContain("Q");
    });

    it("useInput - handle return key", async () => {
        const { stdin, lastFrame } = render(<UseInputFixture />);
        await delay(100);
        stdin.write("\r");
        await delay(100);
        expect(lastFrame()).toContain("return");
    });

    it("useInput - handle a pasted return key (ignores it)", async () => {
        const { stdin, lastFrame } = render(<UseInputFixture />);
        await delay(100);
        stdin.write("\r test");
        await delay(100);
        expect(lastFrame()).toContain("test");
    });

    it("useInput - handles the backspace key", async () => {
        const { stdin, lastFrame } = render(<UseInputFixture />);
        await delay(100);
        stdin.write("\b");
        await delay(100);
        expect(lastFrame()).toContain("backspace");

        await delay(100);
        stdin.write("\x7f");
        await delay(100);
        expect(lastFrame()).toContain("backspace");

        await delay(100);
        stdin.write("\x1b\b");
        await delay(100);
        expect(lastFrame()).toContain("backspace");
    });

    it("useInput - handles remove key as the delete key", async () => {
        const { stdin, lastFrame } = render(<UseInputFixture />);
        await delay(100);
        stdin.write("\x1b\x7f");
        await delay(100);
        expect(lastFrame()).toContain("delete");

        await delay(100);
        stdin.write("\u001B[3~");
        await delay(100);
        expect(lastFrame()).toContain("delete");
    });

    it("useInput - handles arrow keys", async () => {
        const { stdin, lastFrame } = render(<UseInputFixture />);
        await delay(100);
        stdin.write("\u001B[D");
        await delay(100);
        expect(lastFrame()).toContain("leftArrow");

        await delay(100);
        stdin.write("\u001B[C");
        await delay(100);
        expect(lastFrame()).toContain("rightArrow");

        await delay(100);
        stdin.write("\u001B[B");
        await delay(100);
        expect(lastFrame()).toContain("downArrow");

        await delay(100);
        stdin.write("\u001B[A");
        await delay(100);
        expect(lastFrame()).toContain("upArrow");
    });

    it("useInput - handles escape", async () => {
        const { stdin, lastFrame } = render(<UseInputFixture />);
        await delay(100);
        stdin.write("\u001B");
        await delay(100);
        expect(lastFrame()).toContain("escape");
    });

    it("useInput - handles ctrl", async () => {
        const { stdin, lastFrame } = render(<UseInputFixture />);
        await delay(100);
        stdin.write("\u0006");
        await delay(100);
        expect(lastFrame()).toContain("ctrl");
    });

    it("useInput - handles meta", async () => {
        const { stdin, lastFrame } = render(<UseInputFixture />);
        await delay(100);
        stdin.write("\u001Bm");
        await delay(100);
        expect(lastFrame()).toContain("meta");
    });

    it("useInput - handles shift + tab", async () => {
        const { stdin, lastFrame } = render(<UseInputFixture />);
        await delay(100);
        stdin.write("\u001B[Z");
        await delay(100);
        expect(lastFrame()).toContain("shift");
        expect(lastFrame()).toContain("tab");
    });

    it("useInput - handles tab", async () => {
        const { stdin, lastFrame } = render(<UseInputFixture />);
        await delay(100);
        stdin.write("\t");
        await delay(100);
        expect(lastFrame()).toContain("tab");
    });

    it("useInput - handles home", async () => {
        const { stdin, lastFrame } = render(<UseInputFixture />);
        await delay(100);
        stdin.write("\x1B\x5B\x48");
        await delay(100);
        expect(lastFrame()).toContain("home");
    });

    it("useInput - handles end", async () => {
        const { stdin, lastFrame } = render(<UseInputFixture />);
        await delay(100);
        stdin.write("\x1B\x5B\x46");
        await delay(100);
        expect(lastFrame()).toContain("end");
    });
});

function delay(ms: number) {
    return new Promise((resolve) => setTimeout(resolve, ms));
}
