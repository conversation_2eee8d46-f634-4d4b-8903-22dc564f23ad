/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { afterEach, beforeEach, describe, expect, test, vi } from "vitest";
import { redactedValue, SecretFilter } from "../src/helpers/SecretFilter";
import { Runner, RunnerLogger } from "../src/runner";
import { RuntimeSettings } from "../src/settings";
import {
    addError,
    addFeatureFlags,
    FEATURE_FLAGS_TELEMETRY_PROPERTY_NAME,
    isTelemetryEvent,
    redactTelemetry,
    TelemetryEvent,
} from "../src/telemetry";

// Mock the settings modules that SecretFilter depends on
vi.mock("../src/settings/current", () => ({
    getCurrentSettings: vi.fn(),
    hasCurrentSettings: vi.fn(),
}));

vi.mock("../src/settings/types", () => ({
    getSettingsSecretVals: vi.fn(),
}));

vi.mock("../src/helpers/environment-variables", () => ({
    getNotEmptyStringEnvVar: vi.fn(),
}));

import { getNotEmptyStringEnvVar } from "../src/helpers/environment-variables";
import { getCurrentSettings, hasCurrentSettings } from "../src/settings/current";
import { getSettingsSecretVals } from "../src/settings/types";

describe("telemetry", () => {
    let mockRunner: Runner;
    let mockLogger: RunnerLogger;
    let secretFilter: SecretFilter;

    beforeEach(() => {
        // Reset all mocks
        vi.resetAllMocks();

        // Create mock logger
        mockLogger = {
            info: vi.fn(),
            error: vi.fn(),
            warn: vi.fn(),
            debug: vi.fn(),
        } as unknown as RunnerLogger;

        // Create mock runner
        mockRunner = {
            logger: mockLogger,
            sensitiveKeys: ["SENSITIVE_KEY_1", "API_TOKEN"],
        } as unknown as Runner;

        // Get SecretFilter instance and configure it
        secretFilter = SecretFilter.getInstance();
        secretFilter.setRunner(mockRunner);

        // Setup default mock returns
        vi.mocked(hasCurrentSettings).mockReturnValue(false);
        vi.mocked(getNotEmptyStringEnvVar).mockReturnValue(undefined);
        vi.mocked(getSettingsSecretVals).mockReturnValue([]);
    });

    afterEach(() => {
        vi.resetAllMocks();
    });

    describe("redactTelemetry", () => {
        test("should redact secrets from properties using real SecretFilter", () => {
            const secretValue = "my-secret-token-123";
            const normalValue = "normal-value";

            vi.mocked(getNotEmptyStringEnvVar).mockImplementation((varName: string) => {
                if (varName === "SENSITIVE_KEY_1") {
                    return secretValue;
                }
                return undefined;
            });

            const event = {
                telemetry: {
                    properties: {
                        normalProp: normalValue,
                        secretProp: `Contains ${secretValue} which should be redacted`,
                        anotherProp: "just normal text",
                    },
                    restrictedProperties: {},
                    metrics: {},
                },
            };

            redactTelemetry(event);

            expect(event.telemetry.properties.normalProp).toBe(normalValue);
            expect(event.telemetry.properties.secretProp).toBe(`Contains ${redactedValue} which should be redacted`);
            expect(event.telemetry.properties.anotherProp).toBe("just normal text");
        });

        test("should redact secrets from restrictedProperties using real SecretFilter", () => {
            const secretValue = "api-key-secret";

            vi.mocked(getNotEmptyStringEnvVar).mockImplementation((varName: string) => {
                if (varName === "API_TOKEN") {
                    return secretValue;
                }
                return undefined;
            });

            const configObject = { token: secretValue, timeout: 30 };
            const expectedConfigObject = { token: redactedValue, timeout: 30 };

            const event = {
                telemetry: {
                    properties: {},
                    restrictedProperties: {
                        apiKey: secretValue,
                        config: JSON.stringify(configObject),
                    },
                    metrics: {},
                },
            };

            redactTelemetry(event);

            expect(event.telemetry.restrictedProperties.apiKey).toBe(redactedValue);
            expect(event.telemetry.restrictedProperties.config).toBe(JSON.stringify(expectedConfigObject));
        });

        test("should redact JWT tokens from telemetry properties", () => {
            const jwtToken =
                "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiYWRtaW4iOnRydWV9.TJVA95OrM7E2cBab30RMHrHDcEfxjoYZgeFONFh7HgQ";

            const event = {
                telemetry: {
                    properties: {
                        authHeader: `Bearer ${jwtToken}`,
                        logMessage: `Authentication successful with token ${jwtToken}`,
                    },
                    restrictedProperties: {},
                    metrics: {},
                },
            };

            redactTelemetry(event);

            expect(event.telemetry.properties.authHeader).toBe(`${redactedValue}`);
            expect(event.telemetry.properties.logMessage).toBe(`Authentication successful with token ${redactedValue}`);
        });

        test("should redact Bearer tokens from telemetry properties", () => {
            const event = {
                telemetry: {
                    properties: {
                        curlCommand: `curl -H "Authorization: Bearer abc123def456"`,
                        httpRequest: "Bearer token123",
                    },
                    restrictedProperties: {},
                    metrics: {},
                },
            };

            redactTelemetry(event);

            expect(event.telemetry.properties.curlCommand).toBe(`curl -H "Authorization: ${redactedValue}"`);
            expect(event.telemetry.properties.httpRequest).toBe(redactedValue);
        });

        test("should redact password patterns from telemetry properties", () => {
            const event = {
                telemetry: {
                    properties: {
                        connectionString: "Server=localhost;Database=test;Password=*********;",
                        command: "mysql -u user -Pwd=mypassword123 database",
                    },
                    restrictedProperties: {},
                    metrics: {},
                },
            };

            redactTelemetry(event);

            expect(event.telemetry.properties.connectionString).toBe(
                `Server=localhost;Database=test;${redactedValue};`,
            );
            expect(event.telemetry.properties.command).toBe(`mysql -u user -${redactedValue} database`);
        });

        test("should redact URI credentials from telemetry properties", () => {
            const event = {
                telemetry: {
                    properties: {
                        dbUrl: "mysql://user:password123@localhost/db",
                        gitUrl: "https://username:<EMAIL>/repo.git",
                    },
                    restrictedProperties: {},
                    metrics: {},
                },
            };

            redactTelemetry(event);

            expect(event.telemetry.properties.dbUrl).toBe(`${redactedValue}localhost/db`);
            expect(event.telemetry.properties.gitUrl).toBe(`${redactedValue}github.com/repo.git`);
        });

        test("should redact base64 encoded secrets", () => {
            const plainSecret = "plain-secret-value";
            const base64Secret = Buffer.from(plainSecret, "utf8").toString("base64");

            vi.mocked(getNotEmptyStringEnvVar).mockImplementation((varName: string) => {
                if (varName === "SENSITIVE_KEY_1") {
                    return plainSecret;
                }
                return undefined;
            });

            const event = {
                telemetry: {
                    properties: {
                        encodedSecret: `Config value: ${base64Secret}`,
                        plainSecret: `Config value: ${plainSecret}`,
                    },
                    restrictedProperties: {},
                    metrics: {},
                },
            };

            redactTelemetry(event);

            expect(event.telemetry.properties.encodedSecret).toBe(`Config value: ${redactedValue}`);
            expect(event.telemetry.properties.plainSecret).toBe(`Config value: ${redactedValue}`);
        });

        test("should handle JSON strings in properties correctly", () => {
            const secretValue = "json-secret-456";

            vi.mocked(getNotEmptyStringEnvVar).mockImplementation((varName: string) => {
                if (varName === "API_TOKEN") {
                    return secretValue;
                }
                return undefined;
            });

            const jsonString = JSON.stringify({
                username: "user1",
                apiKey: secretValue,
                config: {
                    token: secretValue,
                    timeout: 30,
                },
            });

            const event = {
                telemetry: {
                    properties: {
                        requestBody: jsonString,
                    },
                    restrictedProperties: {},
                    metrics: {},
                },
            };

            redactTelemetry(event);

            const parsedResult = JSON.parse(event.telemetry.properties.requestBody!);
            expect(parsedResult.username).toBe("user1");
            expect(parsedResult.apiKey).toBe(redactedValue);
            expect(parsedResult.config.token).toBe(redactedValue);
            expect(parsedResult.config.timeout).toBe(30);
        });

        test("should handle settings secrets when available", () => {
            const settingsSecret = "settings-secret-789";

            vi.mocked(hasCurrentSettings).mockReturnValue(true);
            vi.mocked(getCurrentSettings).mockReturnValue({});
            vi.mocked(getSettingsSecretVals).mockReturnValue([settingsSecret]);

            const event = {
                telemetry: {
                    properties: {
                        configValue: `Setting value: ${settingsSecret}`,
                    },
                    restrictedProperties: {
                        internalConfig: settingsSecret,
                    },
                    metrics: {},
                },
            };

            redactTelemetry(event);

            expect(event.telemetry.properties.configValue).toBe(`Setting value: ${redactedValue}`);
            expect(event.telemetry.restrictedProperties.internalConfig).toBe(redactedValue);
        });

        test("should handle undefined and empty properties gracefully", () => {
            const event = {
                telemetry: {
                    properties: {
                        definedProp: "value",
                        undefinedProp: undefined,
                        emptyProp: "",
                    },
                    restrictedProperties: {
                        restrictedDefined: "restricted-value",
                        restrictedUndefined: undefined,
                    },
                    metrics: {},
                },
            };

            // Should not throw an error
            expect(() => redactTelemetry(event)).not.toThrow();

            expect(event.telemetry.properties.definedProp).toBe("value");
            expect(event.telemetry.properties.undefinedProp).toBe(undefined);
            expect(event.telemetry.properties.emptyProp).toBe("");
            expect(event.telemetry.restrictedProperties.restrictedDefined).toBe("restricted-value");
            expect(event.telemetry.restrictedProperties.restrictedUndefined).toBe(undefined);
        });

        test("should redact multiple different types of secrets in same event", () => {
            const envSecret = "env-secret-value";
            const jwtToken =
                "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiYWRtaW4iOnRydWV9.TJVA95OrM7E2cBab30RMHrHDcEfxjoYZgeFONFh7HgQ";

            vi.mocked(hasCurrentSettings).mockReturnValue(true);
            vi.mocked(getCurrentSettings).mockReturnValue({});
            vi.mocked(getSettingsSecretVals).mockReturnValue(["setting-secret"]);
            vi.mocked(getNotEmptyStringEnvVar).mockImplementation((varName: string) => {
                if (varName === "SENSITIVE_KEY_1") {
                    return envSecret;
                }
                return undefined;
            });

            const event = {
                telemetry: {
                    properties: {
                        mixedSecrets: `Env: ${envSecret}, JWT: ${jwtToken}, Bearer: Bearer token123, Setting: setting-secret`,
                        connectionString: "Server=localhost;Password=*********;Database=test",
                    },
                    restrictedProperties: {
                        credentials: `mysql://user:pass@localhost/db with ${envSecret}`,
                    },
                    metrics: {},
                },
            };

            redactTelemetry(event);

            // Fix the expected result based on actual regex behavior
            expect(event.telemetry.properties.mixedSecrets).toBe(
                `Env: ${redactedValue}, JWT: ${redactedValue} Bearer: ${redactedValue} Setting: ${redactedValue}`,
            );
            expect(event.telemetry.properties.connectionString).toBe(`Server=localhost;${redactedValue};Database=test`);
            expect(event.telemetry.restrictedProperties.credentials).toBe(
                `${redactedValue}localhost/db with ${redactedValue}`,
            );
        });

        test("should not modify metrics", () => {
            const event = {
                telemetry: {
                    properties: {},
                    restrictedProperties: {},
                    metrics: {
                        duration: 1234,
                        count: 42,
                        errorCount: undefined,
                    },
                },
            };

            redactTelemetry(event);

            expect(event.telemetry.metrics.duration).toBe(1234);
            expect(event.telemetry.metrics.count).toBe(42);
            expect(event.telemetry.metrics.errorCount).toBe(undefined);
        });
    });

    describe("addError", () => {
        test("should add error message to restrictedProperties with default property name", () => {
            const event = {
                telemetry: {
                    properties: {},
                    restrictedProperties: {} as Record<string, string | undefined>,
                    metrics: {},
                },
            };

            const error = new Error("Something went wrong");

            addError(event, error);

            expect(event.telemetry.restrictedProperties.error).toBe("Error: Something went wrong");
        });

        test("should add error message with custom property name", () => {
            const event = {
                telemetry: {
                    properties: {},
                    restrictedProperties: {} as Record<string, string | undefined>,
                    metrics: {},
                },
            };

            const error = new Error("Custom error");

            addError(event, error, "customError");

            expect(event.telemetry.restrictedProperties.customError).toBe("Error: Custom error");
        });

        test("should include error cause when present", () => {
            const event = {
                telemetry: {
                    properties: {},
                    restrictedProperties: {} as Record<string, string | undefined>,
                    metrics: {},
                },
            };

            const causeError = new Error("Root cause");
            const error = new Error("Main error");
            error.cause = causeError;

            addError(event, error);

            expect(event.telemetry.restrictedProperties.error).toBe("Error: Main error (Cause: Error: Root cause)");
        });

        test("should handle non-Error objects", () => {
            const event = {
                telemetry: {
                    properties: {},
                    restrictedProperties: {} as Record<string, string | undefined>,
                    metrics: {},
                },
            };

            addError(event, "String error");

            expect(event.telemetry.restrictedProperties.error).toBe("String error");

            addError(event, 404, "statusCode");

            expect(event.telemetry.restrictedProperties.statusCode).toBe("404");
        });

        test("should handle null and undefined errors", () => {
            const event = {
                telemetry: {
                    properties: {},
                    restrictedProperties: {} as Record<string, string | undefined>,
                    metrics: {},
                },
            };

            addError(event, null);
            expect(event.telemetry.restrictedProperties.error).toBe("null");

            addError(event, undefined, "undefinedError");
            expect(event.telemetry.restrictedProperties.undefinedError).toBe("undefined");
        });
    });

    describe("addFeatureFlags", () => {
        test("should add feature flags to properties as JSON string", () => {
            const event = {
                telemetry: {
                    properties: {} as Record<string, string | undefined>,
                    restrictedProperties: {},
                    metrics: {},
                },
            };

            const settings: RuntimeSettings = {
                featureFlags: {
                    enableNewFeature: true,
                    experimentalMode: false,
                    debugLogging: true,
                },
            };

            addFeatureFlags(event, settings);

            expect(event.telemetry.properties[FEATURE_FLAGS_TELEMETRY_PROPERTY_NAME]).toBe(
                JSON.stringify({
                    enableNewFeature: true,
                    experimentalMode: false,
                    debugLogging: true,
                }),
            );
        });

        test("should handle empty feature flags", () => {
            const event = {
                telemetry: {
                    properties: {} as Record<string, string | undefined>,
                    restrictedProperties: {},
                    metrics: {},
                },
            };

            const settings: RuntimeSettings = {
                featureFlags: {},
            };

            addFeatureFlags(event, settings);

            expect(event.telemetry.properties[FEATURE_FLAGS_TELEMETRY_PROPERTY_NAME]).toBe(JSON.stringify({}));
        });

        test("should handle undefined feature flags", () => {
            const event = {
                telemetry: {
                    properties: {} as Record<string, string | undefined>,
                    restrictedProperties: {},
                    metrics: {},
                },
            };

            const settings: RuntimeSettings = {};

            addFeatureFlags(event, settings);

            expect(event.telemetry.properties[FEATURE_FLAGS_TELEMETRY_PROPERTY_NAME]).toBe(JSON.stringify({}));
        });
    });

    describe("isTelemetryEvent", () => {
        test("should return true for valid telemetry events", () => {
            const telemetryEvent: TelemetryEvent = {
                kind: "telemetry",
                telemetry: {
                    event: "test-event",
                    properties: {},
                    restrictedProperties: {},
                    metrics: {},
                },
            };

            expect(isTelemetryEvent(telemetryEvent)).toBe(true);
        });

        test("should return false for non-telemetry events", () => {
            const nonTelemetryEvent = {
                kind: "other",
                data: "some data",
            };

            expect(isTelemetryEvent(nonTelemetryEvent)).toBe(false);
        });

        test("should return false for objects without kind property", () => {
            const noKindEvent = {
                telemetry: {
                    event: "test-event",
                    properties: {},
                    restrictedProperties: {},
                    metrics: {},
                },
            };

            expect(isTelemetryEvent(noKindEvent)).toBe(false);
        });

        test("should return false for null and undefined", () => {
            expect(isTelemetryEvent(null)).toBe(false);
            expect(isTelemetryEvent(undefined)).toBe(false);
        });

        test("should return false for primitive types", () => {
            expect(isTelemetryEvent("string")).toBe(false);
            expect(isTelemetryEvent(123)).toBe(false);
            expect(isTelemetryEvent(true)).toBe(false);
        });
    });
});
