/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import { describe, expect, it, beforeAll, afterAll } from "vitest";
import { TrajectoryFormatter } from "../../src/trajectory/TrajectoryFormatter";
import { ChatCompletionMessageToolCall } from "openai/resources";
import { clearCurrentSettings } from "../../src/settings/current";
import { initSettings } from "../../src/settings";
import { secretEnvVarNames } from "../../src/settings/environment-settings";

describe("TrajectoryFormatter secret filtering", () => {
    const formatter = new TrajectoryFormatter();
    const originalEnvVars = new Map<string, string | undefined>();
    const secrets: string[] = [];

    // Set up environment secrets before tests
    beforeAll(async () => {
        // Save original environment variables and set fake secrets
        for (const name of secretEnvVarNames) {
            originalEnvVars.set(name, process.env[name]);
            const secretValue = `SECRET_${name}_VALUE`;
            process.env[name] = secretValue;
            secrets.push(secretValue);
        }

        // Set required environment variables for testing
        originalEnvVars.set("COPILOT_AGENT_DEBUG", process.env.COPILOT_AGENT_DEBUG);
        originalEnvVars.set("COPILOT_AGENT_RUNNER_TYPE", process.env.COPILOT_AGENT_RUNNER_TYPE);
        process.env.COPILOT_AGENT_DEBUG = "true";
        process.env.COPILOT_AGENT_RUNNER_TYPE = "STANDALONE";

        // Initialize settings from environment
        clearCurrentSettings();
        await initSettings();
    });

    // Restore original environment variables after tests
    afterAll(async () => {
        for (const [name, value] of originalEnvVars) {
            if (value) {
                process.env[name] = value;
            } else {
                delete process.env[name];
            }
        }

        // Reset settings to original state
        clearCurrentSettings();
        await initSettings();
    });

    describe("formatToolCall", () => {
        it("should filter secrets from tool call parameters", () => {
            // Create a tool call with secrets in parameters
            const toolCall: ChatCompletionMessageToolCall = {
                id: "test-id",
                type: "function",
                function: {
                    name: "test_tool",
                    arguments: JSON.stringify({
                        param1: "normal value",
                        param2: secrets[0],
                        param3: `This contains a secret: ${secrets[1]}`,
                    }),
                },
            };

            // Format the tool call
            const result = formatter.formatToolCall(toolCall);

            // Check that the result contains expected values
            expect(result).toContain("test_tool");
            expect(result).toContain("normal value");

            // Check that secrets are filtered
            for (const secret of secrets) {
                expect(result).not.toContain(secret);
            }

            // Check that secrets are replaced with asterisks
            expect(result).toContain("******");
        });
    });

    describe("formatToolResult", () => {
        it("should filter secrets from tool results", () => {
            // Create a tool result with secrets
            const toolResult = `Here is some output with secrets: ${secrets[0]} and ${secrets[1]}`;

            // Format the tool result
            const result = formatter.formatToolResult(toolResult);

            // Check that result contains expected structure
            expect(result.startsWith("<function_results>")).toBe(true);
            expect(result.endsWith("</function_results>")).toBe(true);
            expect(result).toContain("Here is some output with secrets:");

            // Check that secrets are filtered
            for (const secret of secrets) {
                expect(result).not.toContain(secret);
            }

            // Check that secrets are replaced with asterisks
            expect(result).toContain("******");
        });

        it("should handle empty tool results", () => {
            // Test with empty result
            const emptyResult = formatter.formatToolResult("");

            // Should have placeholder text
            expect(emptyResult).toContain("Tool ran without output or errors");
        });
    });

    describe("formatMessage", () => {
        it("should filter secrets from assistant messages", () => {
            // Create a message with secrets
            const role = "assistant";
            const content = `This is a response that contains secrets: ${secrets[0]} and ${secrets[1]}`;

            // Format the message
            const result = formatter.formatMessage(role, content);

            // Check that the message contains expected content
            expect(result).toContain("This is a response that contains secrets:");

            // Check that secrets are filtered
            for (const secret of secrets) {
                expect(result).not.toContain(secret);
            }

            // Check that secrets are replaced with asterisks
            expect(result).toContain("******");
        });

        it("should handle empty messages", () => {
            // Test with empty content
            const emptyResult = formatter.formatMessage("assistant", "");

            // Should return empty string for empty content
            expect(emptyResult).toBe("");
        });
    });
});
