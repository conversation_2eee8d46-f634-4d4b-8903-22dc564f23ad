/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { writeFileSync } from "fs";
import { tmpdir } from "os";
import { join } from "path";
import { beforeEach, describe, expect, it } from "vitest";
import { EditsTracker, PathEditsTracking } from "../../src/helpers/EditsTracker";

describe("EditsTracker", () => {
    let tracker: EditsTracker;
    const tmpFilePath = join(tmpdir(), "test-file.not-safe-totally-fake");

    beforeEach(() => {
        tracker = new EditsTracker();
        writeFileSync(tmpFilePath, "This is a test file for EditsTracker.");
    });

    describe("trackEdit", () => {
        it("should track the first edit for a file", () => {
            const result = tracker.trackEdit(__filename, "edit_command", "success");
            expect(result).toBeDefined();
        });

        it("should track edits for existing files", () => {
            const result = tracker.trackEdit(__filename, "edit_command", "success");

            expect(result).toBeDefined();
            expect(typeof result).toBe("string");
        });

        it("should track multiple edits for the same file", () => {
            const key1 = tracker.trackEdit(__filename, "edit_command", "success");
            const key2 = tracker.trackEdit(__filename, "another_command", "failure");

            // Should return the same key for the same path
            expect(key1).toBe(key2);

            const tracking = tracker.getTrackedEditsForPath(key1!);
            expect(tracking?.edits).toHaveLength(2);
        });

        it("should handle directory paths correctly", () => {
            const result = tracker.trackEdit(__dirname, "mkdir", "success");

            expect(result).toBeDefined();

            const tracking = tracker.getTrackedEditsForPath(result!);
            expect(tracking?.fileExtension).toBe("directory");
        });
    });

    describe("edit tracking statistics", () => {
        it("should correctly count successful and failed edits", () => {
            const key = tracker.trackEdit(__filename, "command1", "success");
            tracker.trackEdit(__filename, "command2", "failure");
            tracker.trackEdit(__filename, "command3", "success");
            tracker.trackEdit(__filename, "command4", "failure");

            const tracking = tracker.getTrackedEditsForPath(key!);
            expect(tracking?.numSuccessfulEdits).toBe(2);
            expect(tracking?.numFailedEdits).toBe(2);
        });

        it("should track the last edit result correctly", () => {
            const key = tracker.trackEdit(__filename, "command1", "success");
            let tracking = tracker.getTrackedEditsForPath(key!);
            expect(tracking?.wasLastEditSuccessful).toBe(true);

            tracker.trackEdit(__filename, "command2", "failure");
            tracking = tracker.getTrackedEditsForPath(key!);
            expect(tracking?.wasLastEditSuccessful).toBe(false);
        });

        it("should count edits by command correctly", () => {
            const key = tracker.trackEdit(__filename, "edit", "success");
            tracker.trackEdit(__filename, "edit", "failure");
            tracker.trackEdit(__filename, "delete", "success");
            tracker.trackEdit(__filename, "edit", "success");

            const tracking = tracker.getTrackedEditsForPath(key!);
            expect(tracking?.numEditsByCommand["edit"]).toBe(3);
            expect(tracking?.numEditsByCommand["delete"]).toBe(1);
        });
    });

    describe("failure sequence tracking", () => {
        it("should track failure sequences correctly", () => {
            const key = tracker.trackEdit(__filename, "command1", "success");
            tracker.trackEdit(__filename, "command2", "failure");
            tracker.trackEdit(__filename, "command3", "failure");
            tracker.trackEdit(__filename, "command4", "failure");
            tracker.trackEdit(__filename, "command5", "success");

            const tracking = tracker.getTrackedEditsForPath(key!);
            expect(tracking).toBeDefined();
            const edits = tracking!.edits;

            // First edit, no prior failures
            expect(edits[0].lenPriorFailureSequence).toBe(0);
            // Previous was success
            expect(edits[1].lenPriorFailureSequence).toBe(0);
            // One prior failure
            expect(edits[2].lenPriorFailureSequence).toBe(1);
            // Two prior failures
            expect(edits[3].lenPriorFailureSequence).toBe(2);
            // Three prior failures (not reset for success)
            expect(edits[4].lenPriorFailureSequence).toBe(3);
        });

        it("should reset failure sequence count after success", () => {
            const key = tracker.trackEdit(__filename, "command1", "failure");
            tracker.trackEdit(__filename, "command2", "failure");
            tracker.trackEdit(__filename, "command3", "success");
            tracker.trackEdit(__filename, "command4", "failure");

            const tracking = tracker.getTrackedEditsForPath(key!);
            expect(tracking).toBeDefined();
            const edits = tracking!.edits;

            // Should reset after success
            expect(edits[3].lenPriorFailureSequence).toBe(0);
        });
    });

    describe("file extension tracking", () => {
        it("should track file extensions correctly for files with safe extensions", () => {
            const key = tracker.trackEdit(__filename, "edit", "success");
            const tracking = tracker.getTrackedEditsForPath(key!);
            expect(tracking?.fileExtension).toBe(".ts");
        });

        it("should handle files with unsafe extensions", () => {
            const key = tracker.trackEdit(tmpFilePath, "edit", "success");
            const tracking = tracker.getTrackedEditsForPath(key!);
            expect(tracking?.fileExtension).toBe("not-safe");
        });

        it("should track directories with special extension", () => {
            const key = tracker.trackEdit(__dirname, "mkdir", "success");
            const tracking = tracker.getTrackedEditsForPath(key!);
            expect(tracking?.fileExtension).toBe("directory");
        });
    });

    describe("getTrackedEditsForPath", () => {
        it("should return undefined for non-existent keys", () => {
            const tracking = tracker.getTrackedEditsForPath("non-existent-key");
            expect(tracking).toBeUndefined();
        });

        it("should return a deep copy of the tracking data", () => {
            const key = tracker.trackEdit(__filename, "edit", "success");
            const tracking1 = tracker.getTrackedEditsForPath(key!);
            const tracking2 = tracker.getTrackedEditsForPath(key!);

            // Should be different objects (deep copy)
            expect(tracking1).not.toBe(tracking2);
            expect(tracking1?.edits).not.toBe(tracking2?.edits);

            // But should have same content
            expect(tracking1).toEqual(tracking2);
        });
    });

    describe("getTrackedEdits", () => {
        it("should return all tracked edits", () => {
            const key1 = tracker.trackEdit(__filename, "edit", "success");
            const key2 = tracker.trackEdit(__dirname, "mkdir", "success");

            const allTracked = tracker.getTrackedEdits();

            expect(Object.keys(allTracked)).toHaveLength(2);
            expect(allTracked[key1!]).toBeDefined();
            expect(allTracked[key2!]).toBeDefined();
        });

        it("should return a deep copy of all tracking data", () => {
            tracker.trackEdit(__filename, "edit", "success");

            const tracked1 = tracker.getTrackedEdits();
            const tracked2 = tracker.getTrackedEdits();

            expect(tracked1).not.toBe(tracked2);
            expect(tracked1).toEqual(tracked2);
        });
    });

    describe("getTrackedEditsJsonArrString", () => {
        it("should return valid JSON array string", () => {
            const tracked1 = tracker.trackEdit(__filename, "edit", "success");
            const tracked2 = tracker.trackEdit(__dirname, "mkdir", "failure");

            const jsonString = tracker.getTrackedEditsJsonArrString();

            // Should be valid JSON
            expect(() => JSON.parse(jsonString)).not.toThrow();

            const parsed = JSON.parse(jsonString);
            expect(Array.isArray(parsed)).toBe(true);
            expect(parsed).toHaveLength(2);

            // Each item should have hashedPath property
            expect(parsed[0]).toHaveProperty("hashedPath");
            expect(parsed[1]).toHaveProperty("hashedPath");

            // Should contain the tracking data
            const firstItem = parsed.find(
                (item: PathEditsTracking & { hashedPath: string }) => item.hashedPath === tracked1,
            );
            const secondItem = parsed.find(
                (item: PathEditsTracking & { hashedPath: string }) => item.hashedPath === tracked2,
            );

            expect(firstItem).toBeDefined();
            expect(secondItem).toBeDefined();
            expect(firstItem.edits).toHaveLength(1);
            expect(secondItem.edits).toHaveLength(1);
        });

        it("should return empty array JSON when no edits tracked", () => {
            const jsonString = tracker.getTrackedEditsJsonArrString();
            expect(jsonString).toBe("[]");
            expect(() => JSON.parse(jsonString)).not.toThrow();

            const parsed = JSON.parse(jsonString);
            expect(Array.isArray(parsed)).toBe(true);
            expect(parsed).toHaveLength(0);
        });

        it("should include all tracking properties with hashedPath", () => {
            const key = tracker.trackEdit(__filename, "edit", "success");
            tracker.trackEdit(__filename, "delete", "failure");

            const jsonString = tracker.getTrackedEditsJsonArrString();
            const parsed = JSON.parse(jsonString);

            expect(parsed).toHaveLength(1);

            const item = parsed[0];
            expect(item).toHaveProperty("hashedPath", key);
            expect(item).toHaveProperty("edits");
            expect(item).toHaveProperty("numEditsByCommand");
            expect(item).toHaveProperty("numFailedEdits", 1);
            expect(item).toHaveProperty("numSuccessfulEdits", 1);
            expect(item).toHaveProperty("wasLastEditSuccessful", false);
            expect(item).toHaveProperty("fileExtension", ".ts");
            expect(item.edits).toHaveLength(2);
        });
    });

    describe("multiple files tracking", () => {
        it("should track edits to different files independently", () => {
            const key1 = tracker.trackEdit(__filename, "edit", "success");
            const key2 = tracker.trackEdit(__dirname, "mkdir", "failure");

            tracker.trackEdit(__filename, "delete", "failure");
            tracker.trackEdit(__dirname, "create", "success");

            const tracking1 = tracker.getTrackedEditsForPath(key1!);
            const tracking2 = tracker.getTrackedEditsForPath(key2!);

            expect(tracking1?.edits).toHaveLength(2);
            expect(tracking2?.edits).toHaveLength(2);

            expect(tracking1?.numSuccessfulEdits).toBe(1);
            expect(tracking1?.numFailedEdits).toBe(1);

            expect(tracking2?.numSuccessfulEdits).toBe(1);
            expect(tracking2?.numFailedEdits).toBe(1);
        });
    });

    describe("edge cases", () => {
        it("should handle tracking for the same path multiple times with different commands", () => {
            const commands = ["edit", "save", "delete", "create", "move"];
            const key = tracker.trackEdit(__filename, commands[0], "success");

            for (let i = 1; i < commands.length; i++) {
                const result = tracker.trackEdit(__filename, commands[i], i % 2 === 0 ? "success" : "failure");
                // Should always return the same key
                expect(result).toBe(key);
            }

            const tracking = tracker.getTrackedEditsForPath(key!);
            expect(tracking?.edits).toHaveLength(commands.length);
            expect(Object.keys(tracking?.numEditsByCommand || {}).length).toBe(commands.length);
        });

        it("should properly track edit details in the correct order", () => {
            const key = tracker.trackEdit(__filename, "first", "success");
            tracker.trackEdit(__filename, "second", "failure");
            tracker.trackEdit(__filename, "third", "success");

            const tracking = tracker.getTrackedEditsForPath(key!);
            expect(tracking).toBeDefined();
            const edits = tracking!.edits;

            expect(edits[0].command).toBe("first");
            expect(edits[0].result).toBe("success");
            expect(edits[1].command).toBe("second");
            expect(edits[1].result).toBe("failure");
            expect(edits[2].command).toBe("third");
            expect(edits[2].result).toBe("success");
        });
    });

    afterEach(() => {
        try {
            writeFileSync(tmpFilePath, "");
        } catch (e) {
            console.error(`Failed to clear test file: ${tmpFilePath}`, e);
        }
    });
});
