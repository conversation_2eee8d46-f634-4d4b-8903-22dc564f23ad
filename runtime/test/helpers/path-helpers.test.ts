/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import { describe, it, expect } from "vitest";
import * as path from "path";
import { getAbsolutePath, findGitRoot } from "../../src/helpers/path-helpers";

describe("getAbsolutePath", () => {
    it("returns absolute paths unchanged", () => {
        const absolutePath = path.resolve("/home/<USER>/project");
        expect(getAbsolutePath(absolutePath)).toBe(absolutePath);
    });

    it("converts relative paths to absolute", () => {
        const relativePath = "./test/dir";
        const absolutePath = path.resolve(relativePath);
        expect(getAbsolutePath(relativePath)).toBe(absolutePath);
    });

    it("handles parent directory notation", () => {
        const relativePath = "../dir";
        const absolutePath = path.resolve(relativePath);
        expect(getAbsolutePath(relativePath)).toBe(absolutePath);
    });

    it("handles tilde in path", () => {
        const tildePathInput = "~/dir";
        const tildePath = getAbsolutePath(tildePathInput);
        // Should not contain tilde anymore
        expect(tildePath.includes("~")).toBe(false);
        // Should be absolute
        expect(path.isAbsolute(tildePath)).toBe(true);
    });

    it("normalizes paths with .. in them", () => {
        const weirdPath = "dir1/dir2/../dir3";
        const normalizedPath = path.resolve("dir1/dir3");
        expect(getAbsolutePath(weirdPath)).toBe(normalizedPath);
    });
});

describe("findGitRoot", () => {
    it("returns the current working directory if no start path is provided and no .git is found", () => {
        // Since we can't guarantee there's no .git in the test environment,
        // we'll test with a non-existent path
        const nonExistentPath = "/tmp/definitely-not-a-git-repo";
        const result = findGitRoot(nonExistentPath);
        expect(result.gitRoot).toBe(nonExistentPath);
        expect(result.found).toBe(false);
    });

    it("returns the provided start path if no .git directory is found", () => {
        const testPath = "/tmp/some/deep/path/without/git";
        const result = findGitRoot(testPath);
        expect(result.gitRoot).toBe(testPath);
        expect(result.found).toBe(false);
    });

    it("should return a valid absolute path", () => {
        const result = findGitRoot();
        expect(path.isAbsolute(result.gitRoot)).toBe(true);
    });
});
