/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { describe, expect } from "vitest";
import { deepJsonParse, jsonStringifyHashed } from "../../src/helpers/json-helpers";

describe("json-helpers", () => {
    describe("deepJsonParse", () => {
        it("should parse a JSON string", () => {
            const input = '{"x":1}';
            const expectedOutput = { x: 1 };
            const result = deepJsonParse(input);
            expect(result).toEqual(expectedOutput);
        });

        it("should parse a JSON string that has been stringified multiple times", () => {
            const input = '"\\"{\\\\\\"x\\\\\\":1}\\""';
            const expectedOutput = { x: 1 };
            const result = deepJsonParse(input);
            expect(result).toEqual(expectedOutput);
        });

        it("should throw an error if the final parse fails", () => {
            const input = '"\\"{\\\\\\"x\\\\\\":falsey}\\""';
            expect(() => deepJsonParse(input)).toThrow();
        });

        it("should throw an error if the original text is not a valid JSON string", () => {
            const input = "falsey";
            expect(() => deepJsonParse(input)).toThrow();
        });
    });

    describe("jsonStringifyHashed", () => {
        it("should hash all string values in a flat object", () => {
            const input = { a: "foo", b: "bar" };
            const result = JSON.parse(jsonStringifyHashed(input));
            expect(typeof result.a).toBe("string");
            expect(typeof result.b).toBe("string");
            expect(result.a).not.toBe("foo");
            expect(result.b).not.toBe("bar");
            expect(result.a).not.toBe(result.b);
        });

        it("should hash non-string values by stringifying them", () => {
            const input = { a: 123, b: true, c: null, d: [1, 2], e: { x: 1 } };
            const result = JSON.parse(jsonStringifyHashed(input));
            expect(Object.keys(result)).toEqual(["a", "b", "c", "d", "e"]);
            for (const key of Object.keys(result)) {
                expect(typeof result[key]).toBe("string");
            }
        });

        it("should produce the same hash for equivalent objects", () => {
            const obj1 = { x: 1, y: "test" };
            const obj2 = { x: 1, y: "test" };
            expect(jsonStringifyHashed(obj1)).toBe(jsonStringifyHashed(obj2));
        });

        it("should produce different hashes for different values", () => {
            const obj1 = { x: 1 };
            const obj2 = { x: 2 };
            expect(jsonStringifyHashed(obj1)).not.toBe(jsonStringifyHashed(obj2));
        });
    });
});
