/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { afterEach, beforeEach, describe, expect, test, vi } from "vitest";
import { getNotEmptyStringEnvVar } from "../../src/helpers/environment-variables";

describe("environment-variables", () => {
    describe("getNotEmptyStringEnvVar", () => {
        beforeEach(() => {
            // Clear all environment variable stubs before each test
            vi.unstubAllEnvs();
        });

        afterEach(() => {
            // Clean up after each test
            vi.unstubAllEnvs();
        });

        test("should return the value when environment variable exists and is not empty", () => {
            const envVarName = "TEST_VAR";
            const expectedValue = "test-value";

            // Stub the environment variable
            vi.stubEnv(envVarName, expectedValue);

            const result = getNotEmptyStringEnvVar(envVarName);

            expect(result).toBe(expectedValue);
        });

        test("should return undefined when environment variable does not exist", () => {
            const envVarName = "NON_EXISTENT_VAR";

            // Don't stub the variable, so it doesn't exist

            const result = getNotEmptyStringEnvVar(envVarName);

            expect(result).toBeUndefined();
        });

        test("should return undefined when environment variable is empty string", () => {
            const envVarName = "EMPTY_VAR";

            // Stub with empty string
            vi.stubEnv(envVarName, "");

            const result = getNotEmptyStringEnvVar(envVarName);

            expect(result).toBeUndefined();
        });

        test("should return undefined when environment variable contains only whitespace", () => {
            const envVarName = "WHITESPACE_VAR";

            // Stub with whitespace
            vi.stubEnv(envVarName, "   ");

            const result = getNotEmptyStringEnvVar(envVarName);

            expect(result).toBeUndefined();
        });

        test("should return undefined when environment variable contains only tabs and newlines", () => {
            const envVarName = "TABS_NEWLINES_VAR";

            // Stub with tabs and newlines
            vi.stubEnv(envVarName, "\t\n\r");

            const result = getNotEmptyStringEnvVar(envVarName);

            expect(result).toBeUndefined();
        });

        test("should return the value when environment variable has leading/trailing whitespace but contains content", () => {
            const envVarName = "PADDED_VAR";
            const paddedValue = "  test-value  ";

            // Stub with padded value
            vi.stubEnv(envVarName, paddedValue);

            const result = getNotEmptyStringEnvVar(envVarName);

            // Should return original value, not trimmed
            expect(result).toBe(paddedValue);
        });

        test("should return the value when environment variable contains special characters", () => {
            const envVarName = "SPECIAL_CHARS_VAR";
            const specialValue = "test@#$%^&*()value";

            // Stub with special characters
            vi.stubEnv(envVarName, specialValue);

            const result = getNotEmptyStringEnvVar(envVarName);

            expect(result).toBe(specialValue);
        });

        test("should return the value when environment variable contains numbers", () => {
            const envVarName = "NUMERIC_VAR";
            const numericValue = "12345";

            // Stub with numeric value
            vi.stubEnv(envVarName, numericValue);

            const result = getNotEmptyStringEnvVar(envVarName);

            expect(result).toBe(numericValue);
        });

        test("should return the value when environment variable contains zero", () => {
            const envVarName = "ZERO_VAR";
            const zeroValue = "0";

            // Stub with zero value
            vi.stubEnv(envVarName, zeroValue);

            const result = getNotEmptyStringEnvVar(envVarName);

            expect(result).toBe(zeroValue);
        });

        test("should handle multiple different environment variables independently", () => {
            // Stub multiple environment variables
            vi.stubEnv("VAR1", "value1");
            vi.stubEnv("VAR2", "");
            vi.stubEnv("VAR3", "value3");
            // VAR4 is intentionally not stubbed

            expect(getNotEmptyStringEnvVar("VAR1")).toBe("value1");
            expect(getNotEmptyStringEnvVar("VAR2")).toBeUndefined();
            expect(getNotEmptyStringEnvVar("VAR3")).toBe("value3");
            expect(getNotEmptyStringEnvVar("VAR4")).toBeUndefined();
        });
    });
});
