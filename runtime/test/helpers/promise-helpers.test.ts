/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import { executeInParallelAndYieldAsCompleted, executeWithTimeout } from "../../src/model/capi/promise-helpers";

describe("promise-helpers", () => {
    describe("executeInParallelAndYieldAsCompleted", () => {
        it("should yield results as they complete", async () => {
            const delays = [100, 50, 150];
            const tasks = delays.map(
                (delay, index) => () =>
                    new Promise<string>((resolve) => setTimeout(() => resolve(`task-${index}`), delay)),
            );

            const results: string[] = [];
            for await (const result of executeInParallelAndYieldAsCompleted(tasks)) {
                results.push(result);
            }

            expect(results).toHaveLength(3);
            expect(results).toContain("task-0");
            expect(results).toContain("task-1");
            expect(results).toContain("task-2");
            // task-1 should complete first (50ms), then task-0 (100ms), then task-2 (150ms)
            expect(results[0]).toBe("task-1");
            expect(results[1]).toBe("task-0");
            expect(results[2]).toBe("task-2");
        });

        it("should handle empty task array", async () => {
            const results: unknown[] = [];
            for await (const result of executeInParallelAndYieldAsCompleted([])) {
                results.push(result);
            }

            expect(results).toHaveLength(0);
        });

        it("should handle single task", async () => {
            const tasks = [() => Promise.resolve("single-task")];

            const results: string[] = [];
            for await (const result of executeInParallelAndYieldAsCompleted(tasks)) {
                results.push(result);
            }

            expect(results).toEqual(["single-task"]);
        });

        it("should not return duplicate results", async () => {
            const tasks = [
                () => new Promise<string>((resolve) => setTimeout(() => resolve("fast"), 10)),
                () => new Promise<string>((resolve) => setTimeout(() => resolve("medium"), 50)),
                () => new Promise<string>((resolve) => setTimeout(() => resolve("slow"), 100)),
            ];

            const results: string[] = [];
            for await (const result of executeInParallelAndYieldAsCompleted(tasks)) {
                results.push(result);
            }

            expect(results).toHaveLength(3);
            expect(new Set(results).size).toBe(3); // All results should be unique
        });

        it("should handle tasks that resolve immediately", async () => {
            const tasks = [
                () => Promise.resolve("immediate-1"),
                () => Promise.resolve("immediate-2"),
                () => Promise.resolve("immediate-3"),
            ];

            const results: string[] = [];
            for await (const result of executeInParallelAndYieldAsCompleted(tasks)) {
                results.push(result);
            }

            expect(results).toHaveLength(3);
            expect(results).toContain("immediate-1");
            expect(results).toContain("immediate-2");
            expect(results).toContain("immediate-3");
        });

        it("should handle mixed timing scenarios", async () => {
            const tasks = [
                () => Promise.resolve("immediate"),
                () => new Promise<string>((resolve) => setTimeout(() => resolve("delayed"), 30)),
                () => Promise.resolve("also-immediate"),
            ];

            const results: string[] = [];
            const startTime = Date.now();
            for await (const result of executeInParallelAndYieldAsCompleted(tasks)) {
                results.push(result);
            }
            const endTime = Date.now();

            expect(results).toHaveLength(3);
            expect(results).toContain("immediate");
            expect(results).toContain("delayed");
            expect(results).toContain("also-immediate");
            expect(endTime - startTime).toBeGreaterThanOrEqual(25); // Should wait for delayed task
        });

        it("should handle task factories that return rejected promises", async () => {
            const tasks = [
                () => Promise.resolve("success"),
                () => Promise.reject(new Error("failure")),
                () => new Promise<string>((resolve) => setTimeout(() => resolve("delayed-success"), 20)),
            ];

            const results: string[] = [];
            const errors: Error[] = [];

            try {
                for await (const result of executeInParallelAndYieldAsCompleted(tasks)) {
                    results.push(result);
                }
            } catch (error) {
                errors.push(error as Error);
            }

            // The generator should handle rejections gracefully
            // Note: The current implementation doesn't handle rejections, so this test
            // documents the current behavior. You may want to modify the implementation
            // to handle rejections if needed.
            // Since the current implementation doesn't handle rejections gracefully,
            // we expect either no results due to the rejection stopping iteration,
            // or the rejection to be thrown
            expect(errors.length).toBeGreaterThan(0);
            expect(results.length).toBeLessThan(3);
        });

        it("should execute all tasks in parallel", async () => {
            const executionOrder: number[] = [];
            const tasks = [
                () =>
                    new Promise<string>((resolve) => {
                        executionOrder.push(1);
                        setTimeout(() => resolve("task-1"), 50);
                    }),
                () =>
                    new Promise<string>((resolve) => {
                        executionOrder.push(2);
                        setTimeout(() => resolve("task-2"), 30);
                    }),
                () =>
                    new Promise<string>((resolve) => {
                        executionOrder.push(3);
                        setTimeout(() => resolve("task-3"), 40);
                    }),
            ];

            const results: string[] = [];
            for await (const result of executeInParallelAndYieldAsCompleted(tasks)) {
                results.push(result);
            }

            // All tasks should start immediately (parallel execution) in same order
            expect(executionOrder).toEqual([1, 2, 3]);

            // Results should be yielded in the order they complete
            expect(results).toHaveLength(3);
            expect(results[0]).toBe("task-2");
            expect(results[1]).toBe("task-3");
            expect(results[2]).toBe("task-1");
        });
    });

    describe("executeWithTimeout", () => {
        it("should resolve with promise result when promise resolves before timeout", async () => {
            const promise = new Promise<string>((resolve) => setTimeout(() => resolve("success"), 50));

            const result = await executeWithTimeout(promise, 100);

            expect(result).toBe("success");
        });

        it("should return timedOut object when promise takes longer than timeout", async () => {
            const promise = new Promise<string>((resolve) => setTimeout(() => resolve("too-late"), 100));

            const result = await executeWithTimeout(promise, 50);

            expect(result).toEqual({ timedOut: true });
        });

        it("should handle promises that resolve immediately", async () => {
            const promise = Promise.resolve("immediate");

            const result = await executeWithTimeout(promise, 100);

            expect(result).toBe("immediate");
        });

        it("should propagate errors if promise rejects before timeout resolves", async () => {
            const error = new Error("Promise error");
            const promise = new Promise<string>((_, reject) => setTimeout(() => reject(error), 50));

            await expect(executeWithTimeout(promise, 100)).rejects.toThrow("Promise error");
        });

        it("should not propagate errors if promise rejects before timeout resolves", async () => {
            const error = new Error("Promise error");
            const promise = new Promise<string>((_, reject) => setTimeout(() => reject(error), 150));

            const result = await executeWithTimeout(promise, 50);

            expect(result).toEqual({ timedOut: true });
        });

        it("should handle zero timeout", async () => {
            const promise = new Promise<string>((resolve) => setTimeout(() => resolve("delayed"), 10));

            const result = await executeWithTimeout(promise, 0);

            expect(result).toEqual({ timedOut: true });
        });

        it("should handle negative timeout", async () => {
            const promise = new Promise<string>((resolve) => setTimeout(() => resolve("delayed"), 10));

            const result = await executeWithTimeout(promise, -100);

            expect(result).toEqual({ timedOut: true });
        });

        it("should return custom timedOut value when provided", async () => {
            const promise = new Promise<string>((resolve) => setTimeout(() => resolve("too-late"), 100));

            const customTimedOutValue = {
                error: "Operation timed out",
                code: 408,
            };
            const result = await executeWithTimeout(promise, 50, customTimedOutValue);

            expect(result).toEqual(customTimedOutValue);
        });

        it("should return custom timedOut value even when promise resolves after timeout", async () => {
            const promise = new Promise<string>((resolve) => setTimeout(() => resolve("delayed-success"), 100));

            const customTimedOutValue = "TIMEOUT_ERROR";
            const result = await executeWithTimeout(promise, 25, customTimedOutValue);

            expect(result).toBe(customTimedOutValue);
        });

        it("should reject with custom timedOut value when onTimeout is 'reject'", async () => {
            const promise = new Promise<string>((resolve) => setTimeout(() => resolve("too-late"), 100));

            const customTimedOutValue = {
                error: "Operation timed out",
                code: 408,
            };

            await expect(executeWithTimeout(promise, 50, customTimedOutValue, "reject")).rejects.toEqual(
                customTimedOutValue,
            );
        });

        it("should resolve with custom timedOut value when onTimeout is 'resolve'", async () => {
            const promise = new Promise<string>((resolve) => setTimeout(() => resolve("too-late"), 100));

            const customTimedOutValue = {
                error: "Operation timed out",
                code: 408,
            };

            const result = await executeWithTimeout(promise, 50, customTimedOutValue, "resolve");

            expect(result).toEqual(customTimedOutValue);
        });

        it("should default to 'resolve' behavior when onTimeout is not specified", async () => {
            const promise = new Promise<string>((resolve) => setTimeout(() => resolve("too-late"), 100));

            const customTimedOutValue = "TIMEOUT";

            const result = await executeWithTimeout(promise, 50, customTimedOutValue);

            expect(result).toBe(customTimedOutValue);
        });
    });
});
