/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import http from "http";
import { AddressInfo } from "net";
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import { ghFetch } from "../../src/helpers/gh-fetch-helpers";
import { RunnerLogger } from "../../src/runner";

async function realisticFetchError(): Promise<Error> {
    try {
        await fetch("http://thisisnotarealwebsite-padawan.com");
    } catch (error) {
        return error as Error;
    }
    throw new Error("Failed to simulate realistic fetch error");
}

describe("ghFetch", () => {
    let server: http.Server;
    let url: string;
    let mockLogger: RunnerLogger;

    beforeEach(async () => {
        mockLogger = {
            debug: vi.fn(),
            info: vi.fn(),
            warning: vi.fn(),
            error: vi.fn(),
        } as unknown as RunnerLogger;

        // Set up a test server
        server = http.createServer((_req, res) => {
            res.writeHead(200);
            res.end("OK");
        });

        // Start the server on a random available port
        await new Promise<void>((resolve) => {
            server.listen(0, "127.0.0.1", () => resolve());
        });

        const address = server.address() as AddressInfo;
        url = `http://127.0.0.1:${address.port}`;
    });

    afterEach(() => {
        if (server) {
            server.close();
        }
        vi.restoreAllMocks();
    });

    it("returns the response immediately when successful", async () => {
        const response = await ghFetch(url, { method: "GET" }, mockLogger);

        expect(response.ok).toBe(true);
        expect(response.status).toBe(200);
        expect(mockLogger.warning).not.toHaveBeenCalled();
    });

    it("retries on 429 rate limit responses and respects retry-after header", async () => {
        // Mock global fetch instead of using a real server
        vi.useFakeTimers();

        // Create mock responses for the sequence
        const mockResponseError = new Response(null, {
            status: 429,
            headers: { "Retry-After": "2" },
        });

        const mockResponseSuccess = new Response("Success", {
            status: 200,
        });

        // Create fetch spy that returns different responses
        const fetchSpy = vi
            .spyOn(global, "fetch")
            .mockResolvedValueOnce(mockResponseError)
            .mockResolvedValueOnce(mockResponseSuccess);

        // Call ghFetch
        const fetchPromise = ghFetch("http://example.com", { method: "GET" }, mockLogger);

        // Advance timers to complete first fetch and get to setTimeout
        await vi.runOnlyPendingTimersAsync();

        // Verify logger was called with retry message
        expect(mockLogger.warning).toHaveBeenCalledWith(expect.stringContaining("Rate limited (429)"));
        expect(mockLogger.warning).toHaveBeenCalledWith(expect.stringContaining("Retrying in 2 seconds"));

        // Advance time to complete the retry
        await vi.advanceTimersByTimeAsync(2000);

        // Get the final response
        const response = await fetchPromise;

        // Verify fetch was called twice
        expect(fetchSpy).toHaveBeenCalledTimes(2);
        expect(response.status).toBe(200);

        vi.useRealTimers();
    });

    it("uses default retry delay when retry-after header is missing", async () => {
        // Mock global fetch
        vi.useFakeTimers();

        // Create mock responses for the sequence
        const mockResponseError = new Response(null, {
            status: 429,
            // No retry-after header
        });

        const mockResponseSuccess = new Response("Success", {
            status: 200,
        });

        // Create fetch spy that returns different responses
        const fetchSpy = vi
            .spyOn(global, "fetch")
            .mockResolvedValueOnce(mockResponseError)
            .mockResolvedValueOnce(mockResponseSuccess);

        // Call ghFetch
        const fetchPromise = ghFetch("http://example.com", { method: "GET" }, mockLogger);

        // Advance timers to complete first fetch and get to setTimeout
        await vi.runOnlyPendingTimersAsync();

        // Verify logger was called with default retry delay message (5 seconds)
        expect(mockLogger.warning).toHaveBeenCalledWith(expect.stringContaining("Retrying in 5 seconds"));

        // Advance time to complete the retry
        await vi.advanceTimersByTimeAsync(5000);

        // Get the final response
        const response = await fetchPromise;

        // Verify fetch was called twice
        expect(fetchSpy).toHaveBeenCalledTimes(2);
        expect(response.status).toBe(200);

        vi.useRealTimers();
    });

    it("allows custom retry options", async () => {
        // Mock global fetch
        vi.useFakeTimers();

        // Create mock responses for the sequence
        const mockResponseError = new Response(null, {
            status: 429,
            // No retry-after header
        });

        const mockResponseSuccess = new Response("Success", {
            status: 200,
        });

        // Create fetch spy that returns different responses
        const fetchSpy = vi
            .spyOn(global, "fetch")
            .mockResolvedValueOnce(mockResponseError)
            .mockResolvedValueOnce(mockResponseSuccess);

        // Custom retry options
        const customRetryOptions = {
            maxRetries: 3,
            defaultRetryDelaySeconds: 2,
        };

        // Call ghFetch with custom options
        const fetchPromise = ghFetch(
            "http://example.com",
            { method: "GET" },
            mockLogger,
            "Mock API",
            customRetryOptions,
        );

        // Advance timers to complete first fetch and get to setTimeout
        await vi.runOnlyPendingTimersAsync();

        // Verify logger was called with custom retry delay
        expect(mockLogger.warning).toHaveBeenCalledWith(expect.stringContaining("Retrying in 2 seconds"));
        expect(mockLogger.warning).toHaveBeenCalledWith(expect.stringContaining("Attempt 1/3"));

        // Advance time to complete the retry
        await vi.advanceTimersByTimeAsync(2000);

        // Get the final response
        const response = await fetchPromise;

        // Verify fetch was called twice
        expect(fetchSpy).toHaveBeenCalledTimes(2);
        expect(response.status).toBe(200);

        vi.useRealTimers();
    });

    it("returns error response after maximum retries exceeded", async () => {
        // Mock global fetch
        vi.useFakeTimers();

        // Create mock responses - always 429
        const mockResponseError = new Response(null, {
            status: 429,
            headers: { "X-GitHub-Request-ID": "test-id" },
        });

        // Create fetch spy that always returns error
        const fetchSpy = vi.spyOn(global, "fetch").mockResolvedValue(mockResponseError);

        // Use small max retries to make the test faster
        const customRetryOptions = {
            maxRetries: 2,
            defaultRetryDelaySeconds: 1,
        };

        // Call ghFetch
        const fetchPromise = ghFetch(
            "http://example.com",
            { method: "GET" },
            mockLogger,
            "Mock API",
            customRetryOptions,
        );

        // Process all retries
        for (let i = 0; i < customRetryOptions.maxRetries; i++) {
            await vi.runOnlyPendingTimersAsync(); // Process the fetch and logger calls
            expect(mockLogger.warning).toHaveBeenCalledWith(
                expect.stringContaining(`Attempt ${i + 1}/${customRetryOptions.maxRetries}`),
            );
            await vi.advanceTimersByTimeAsync(1000); // Wait for retry delay
        }

        // Get the final response
        const response = await fetchPromise;

        // Verify fetch was called maxRetries + 1 times (initial + retries)
        expect(fetchSpy).toHaveBeenCalledTimes(customRetryOptions.maxRetries + 1);
        expect(response.status).toBe(429);
        expect(mockLogger.warning).toHaveBeenCalledTimes(customRetryOptions.maxRetries);

        vi.useRealTimers();
    });

    it("retries on fetch errors and eventually succeeds", async () => {
        // Mock global fetch
        vi.useFakeTimers();

        const networkError = await realisticFetchError();

        const mockResponseSuccess = new Response("Success", {
            status: 200,
        });

        // Create fetch spy that throws error first, then succeeds
        const fetchSpy = vi
            .spyOn(global, "fetch")
            .mockRejectedValueOnce(networkError)
            .mockResolvedValueOnce(mockResponseSuccess);

        // Use small max retries to make the test faster
        const customRetryOptions = {
            maxRetries: 2,
            defaultRetryDelaySeconds: 1,
        };

        // Call ghFetch
        const fetchPromise = ghFetch(
            "http://example.com",
            { method: "GET" },
            mockLogger,
            "Mock API",
            customRetryOptions,
        );

        // Advance timers to complete first fetch (which throws) and get to setTimeout
        await vi.runOnlyPendingTimersAsync();

        // Verify error was logged with expected content
        expect(mockLogger.error).toHaveBeenCalledWith(expect.stringContaining("ENOTFOUND"));
        expect(mockLogger.error).toHaveBeenCalledWith(expect.stringContaining("thisisnotarealwebsite-padawan.com"));

        // Advance time to complete the retry
        await vi.advanceTimersByTimeAsync(1000);

        // Get the final response
        const response = await fetchPromise;

        // Verify fetch was called twice (initial error + retry success)
        expect(fetchSpy).toHaveBeenCalledTimes(2);
        expect(response.status).toBe(200);

        vi.useRealTimers();
    });

    it("throws error after maximum retries exceeded on fetch errors", async () => {
        // Mock global fetch
        vi.useFakeTimers();

        const networkError = await realisticFetchError();

        // Create fetch spy that always throws error
        const fetchSpy = vi.spyOn(global, "fetch").mockRejectedValue(networkError);

        // Use small max retries to make the test faster
        const customRetryOptions = {
            maxRetries: 2,
            defaultRetryDelaySeconds: 1,
        };

        // Handle unhandled rejections during this test
        const originalHandler = process.listeners("unhandledRejection");
        process.removeAllListeners("unhandledRejection");
        process.on("unhandledRejection", () => {
            // Ignore unhandled rejections during this test
        });

        // Call ghFetch and expect it to throw
        const fetchPromise = ghFetch(
            "http://example.com",
            { method: "GET" },
            mockLogger,
            "Mock API",
            customRetryOptions,
        );

        // Process all retries
        for (let i = 0; i < customRetryOptions.maxRetries; i++) {
            await vi.runOnlyPendingTimersAsync(); // Process the fetch error and logger calls
            expect(mockLogger.error).toHaveBeenCalledWith(expect.stringContaining("ENOTFOUND"));
            expect(mockLogger.error).toHaveBeenCalledWith(expect.stringContaining("thisisnotarealwebsite-padawan.com"));
            await vi.advanceTimersByTimeAsync(1000); // Wait for retry delay
        }

        // Final attempt - need to process the last fetch error and check it throws
        await vi.runOnlyPendingTimersAsync(); // Process the final fetch error
        await expect(fetchPromise).rejects.toThrow(networkError);

        // Verify fetch was called maxRetries + 1 times (initial + retries)
        expect(fetchSpy).toHaveBeenCalledTimes(customRetryOptions.maxRetries + 1);
        expect(mockLogger.error).toHaveBeenCalledTimes(customRetryOptions.maxRetries + 1);

        // Restore original handlers
        process.removeAllListeners("unhandledRejection");
        originalHandler.forEach((handler) => process.on("unhandledRejection", handler));

        vi.useRealTimers();
    });

    it("throws error immediately on first attempt when maxRetries is 0", async () => {
        const networkError = await realisticFetchError();

        // Create fetch spy that throws error
        const fetchSpy = vi.spyOn(global, "fetch").mockRejectedValue(networkError);

        // Use maxRetries: 0 to disable retries
        const customRetryOptions = {
            maxRetries: 0,
        };

        // Call ghFetch and expect it to throw immediately
        await expect(
            ghFetch("http://example.com", { method: "GET" }, mockLogger, "Mock API", customRetryOptions),
        ).rejects.toThrow(networkError);

        // Verify fetch was called only once
        expect(fetchSpy).toHaveBeenCalledTimes(1);
        expect(mockLogger.error).toHaveBeenCalledWith(expect.stringContaining("ENOTFOUND"));
        expect(mockLogger.error).toHaveBeenCalledWith(expect.stringContaining("thisisnotarealwebsite-padawan.com"));
    });
});
