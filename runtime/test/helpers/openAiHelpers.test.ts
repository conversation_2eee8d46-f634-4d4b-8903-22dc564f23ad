/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import type { ChatCompletionChunk } from "openai/resources/chat/completions";
import { describe, expect } from "vitest";
import { createChunkWithSessionLogToolResult } from "../../src/helpers/openAiHelpers";

describe("openAiHelpers", () => {
    describe("createChunkWithSessionLogToolResult", () => {
        it("should only convert choice with tool calls", () => {
            const mockCompletion: ChatCompletionChunk = {
                id: "test-id",
                created: 1234567890,
                model: "test-model",
                choices: [
                    {
                        index: 0,
                        delta: {
                            role: "assistant",
                            content: "OK, let me think what I can do with bash!",
                            refusal: "false",
                        },
                        finish_reason: "tool_calls",
                        logprobs: null,
                    },
                    {
                        index: 0,
                        delta: {
                            role: "assistant",
                            content: "",
                            refusal: "false",
                            tool_calls: [
                                {
                                    id: "tool-id",
                                    type: "function",
                                    function: {
                                        name: "bash",
                                        arguments: 'echo "Hello World"',
                                    },
                                    index: 0,
                                },
                            ],
                        },
                        finish_reason: "tool_calls",
                        logprobs: null,
                    },
                ],
                object: "chat.completion.chunk",
            };

            const chunk = createChunkWithSessionLogToolResult("Hello World", mockCompletion, "tool-id");

            // expect only the second choice to be converted since only it has tool calls
            expect(chunk).toEqual({
                ...mockCompletion,
                created: chunk.created,
                object: "chat.completion.chunk",
                choices: [
                    {
                        finish_reason: "tool_calls",
                        delta: {
                            content: "Hello World",
                            tool_calls: [
                                {
                                    ...mockCompletion.choices[1].delta.tool_calls?.[0],
                                    index: 0,
                                },
                            ],
                        },
                        index: 0,
                    },
                ],
            });
        });
    });
    it("should only create choice with tool call that has matching tool call ID", () => {
        const mockCompletion: ChatCompletionChunk = {
            id: "test-id",
            created: 1234567890,
            model: "test-model",
            choices: [
                {
                    index: 0,
                    delta: {
                        role: "assistant",
                        content: "OK, let me think what I can do with bash!",
                        refusal: "false",
                    },
                    finish_reason: "tool_calls",
                    logprobs: null,
                },
                {
                    index: 0,
                    delta: {
                        role: "assistant",
                        content: "",
                        refusal: "false",
                        tool_calls: [
                            {
                                id: "tool-id-1",
                                type: "function",
                                function: {
                                    name: "bash",
                                    arguments: 'echo "Hello World"',
                                },
                                index: 0,
                            },
                        ],
                    },
                    finish_reason: "tool_calls",
                    logprobs: null,
                },
                {
                    index: 0,
                    delta: {
                        role: "assistant",
                        content: "",
                        refusal: "false",
                        tool_calls: [
                            {
                                id: "tool-id-2",
                                type: "function",
                                function: {
                                    name: "bash",
                                    arguments: 'echo "Goodbye World"',
                                },
                                index: 0,
                            },
                        ],
                    },
                    finish_reason: "tool_calls",
                    logprobs: null,
                },
            ],
            object: "chat.completion.chunk",
        };

        const chunk = createChunkWithSessionLogToolResult("Goodbye World", mockCompletion, "tool-id-2");

        // expect only the second choice should have the session content added
        expect(chunk).toEqual({
            ...mockCompletion,
            created: chunk.created,
            object: "chat.completion.chunk",
            choices: [
                {
                    finish_reason: "tool_calls",
                    delta: {
                        content: "Goodbye World",
                        tool_calls: [
                            {
                                ...mockCompletion.choices[2].delta?.tool_calls?.[0],
                                index: 0,
                            },
                        ],
                    },
                    index: 0,
                },
            ],
        });
    });
});
