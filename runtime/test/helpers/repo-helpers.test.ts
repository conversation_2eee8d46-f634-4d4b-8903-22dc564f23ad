/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import { join as pathJoin } from "path";
import { describe, expect, it } from "vitest";
import {
    hasAtLeastOneGitHubInstructionsMd,
    hasClaudeCustomInstructions,
    hasClaudeHooks,
    hasClaudeSettings,
    hasCodexCustomInstructions,
    hasCopilotInstructions,
    hasCopilotSetupSteps,
    hasGeminiCustomInstructions,
    readCustomInstructions,
    readVSCodeInstructions,
} from "../../src/helpers/repo-helpers";

describe("repoHelpers", () => {
    describe("hasCopilotInstructions", () => {
        it("should return { exists: false, path: undefined } for repos without copilot instructions", () => {
            const repoPath = pathJoin(__dirname, "repository-clone-with-copilot-setup-steps-yml");
            expect(hasCopilotInstructions(repoPath)).toEqual({
                exists: false,
                path: undefined,
            });
        });

        it("should return { exists: true, path: string } for repos with copilot instructions", () => {
            const repoPath = pathJoin(__dirname, "repository-clone-with-copilot-instructions");
            const result = hasCopilotInstructions(repoPath);
            expect(result.exists).toBe(true);
            expect(result.path).toBe(pathJoin(repoPath, ".github", "copilot-instructions.md"));
        });
    });

    describe("hasCopilotSetupSteps", () => {
        it("should return false for repos without copilot setup steps", () => {
            const repoPath = pathJoin(__dirname, "repository-clone-with-copilot-instructions");
            expect(hasCopilotSetupSteps(repoPath)).toBe(false);
        });

        it("should return true for repos with copilot-setup-steps.yml", () => {
            const repoPath = pathJoin(__dirname, "repository-clone-with-copilot-setup-steps-yml");
            expect(hasCopilotSetupSteps(repoPath)).toBe(true);
        });

        it("should return true for repos with copilot-setup-steps.yaml", () => {
            const repoPath = pathJoin(__dirname, "repository-clone-with-copilot-setup-steps-yaml");
            expect(hasCopilotSetupSteps(repoPath)).toBe(true);
        });
    });

    describe("hasCodexCustomInstructions", () => {
        it("should return { exists: false, path: undefined } for repos without Codex custom instructions", () => {
            const repoPath = pathJoin(__dirname, "repository-clone-with-copilot-instructions");
            expect(hasCodexCustomInstructions(repoPath)).toEqual({
                exists: false,
                path: undefined,
            });
        });

        it("should return { exists: true, path: string } for repos with Codex custom instructions, even if the filename has mixed case", () => {
            const repoPath = pathJoin(__dirname, "repository-clone-with-codex-custom-instructions");
            const result = hasCodexCustomInstructions(repoPath);
            expect(result.exists).toBe(true);
            expect(result.path).toBe(pathJoin(repoPath, "AGENTS.md"));
        });
    });

    describe("hasClaudeCustomInstructions", () => {
        it("should return { exists: false, path: undefined } for repos without Claude custom instructions", () => {
            const repoPath = pathJoin(__dirname, "repository-clone-with-copilot-instructions");
            expect(hasClaudeCustomInstructions(repoPath)).toEqual({
                exists: false,
                path: undefined,
            });
        });

        it("should return { exists: true, path: string } for repos with Claude custom instructions, even if the filename has mixed case", () => {
            const repoPath = pathJoin(__dirname, "repository-clone-with-claude-custom-instructions");
            const result = hasClaudeCustomInstructions(repoPath);
            expect(result.exists).toBe(true);
            expect(result.path).toBe(pathJoin(repoPath, "CLAUDE.md"));
        });
    });

    describe("hasGeminiCustomInstructions", () => {
        it("should return { exists: false, path: undefined } for repos without Gemini custom instructions", () => {
            const repoPath = pathJoin(__dirname, "repository-clone-with-copilot-instructions");
            expect(hasGeminiCustomInstructions(repoPath)).toEqual({
                exists: false,
                path: undefined,
            });
        });

        it("should return { exists: true, path: string } for repos with Gemini custom instructions", () => {
            const repoPath = pathJoin(__dirname, "repository-clone-with-gemini-custom-instructions");
            const result = hasGeminiCustomInstructions(repoPath);
            expect(result.exists).toBe(true);
            expect(result.path).toBe(pathJoin(repoPath, "GEMINI.md"));
        });
    });

    describe("hasAtLeastOneGitHubInstructionsMd", () => {
        it("should return false for repos without .github/instructions directory", () => {
            const repoPath = pathJoin(__dirname, "repository-clone-with-copilot-instructions");
            expect(hasAtLeastOneGitHubInstructionsMd(repoPath)).toBe(false);
        });

        it("should return false for repos with .github/instructions directory but no .instructions.md files", async () => {
            // Create a test directory with instructions folder but no .instructions.md files
            const testDir = "/tmp/test-instructions-no-files";
            await import("fs").then((fs) =>
                fs.promises.mkdir(pathJoin(testDir, ".github", "instructions"), { recursive: true }),
            );
            await import("fs").then((fs) =>
                fs.promises.writeFile(
                    pathJoin(testDir, ".github", "instructions", "readme.md"),
                    "Not an instructions file",
                ),
            );

            const result = hasAtLeastOneGitHubInstructionsMd(testDir);
            expect(result).toBe(false);

            // Clean up
            await import("fs").then((fs) => fs.promises.rm(testDir, { recursive: true, force: true }));
        });

        it("should return true for repos with at least one .instructions.md file", () => {
            const repoPath = pathJoin(__dirname, "repository-clone-with-github-instructions");
            expect(hasAtLeastOneGitHubInstructionsMd(repoPath)).toBe(true);
        });

        it("should return true for repos with .instructions.md files in subdirectories", async () => {
            // Create a test directory with nested .instructions.md files
            const testDir = "/tmp/test-instructions-nested";
            await import("fs").then((fs) =>
                fs.promises.mkdir(pathJoin(testDir, ".github", "instructions", "nested"), { recursive: true }),
            );
            await import("fs").then((fs) =>
                fs.promises.writeFile(
                    pathJoin(testDir, ".github", "instructions", "nested", "test.instructions.md"),
                    "Nested instructions",
                ),
            );

            const result = hasAtLeastOneGitHubInstructionsMd(testDir);
            expect(result).toBe(true);

            // Clean up
            await import("fs").then((fs) => fs.promises.rm(testDir, { recursive: true, force: true }));
        });

        it("should return false when directory reading fails or does not exist", () => {
            const repoPath = "/non/existent/path";
            expect(hasAtLeastOneGitHubInstructionsMd(repoPath)).toBe(false);
        });
    });

    describe("hasClaudeSettings", () => {
        it("should return { exists: false, path: undefined } for repos without Claude settings", () => {
            const repoPath = pathJoin(__dirname, "repository-clone-with-copilot-instructions");
            expect(hasClaudeSettings(repoPath)).toEqual({
                exists: false,
                path: undefined,
            });
        });

        it("should return { exists: true, path: string } for repos with Claude settings", async () => {
            // Create a test directory with Claude settings
            const testDir = "/tmp/test-claude-settings";
            await import("fs").then((fs) =>
                fs.promises.mkdir(pathJoin(testDir, ".claude"), {
                    recursive: true,
                }),
            );
            await import("fs").then((fs) =>
                fs.promises.writeFile(pathJoin(testDir, ".claude", "settings.json"), '{"test": "value"}'),
            );

            const result = hasClaudeSettings(testDir);
            expect(result.exists).toBe(true);
            expect(result.path).toBe(pathJoin(testDir, ".claude", "settings.json"));

            // Clean up
            await import("fs").then((fs) => fs.promises.rm(testDir, { recursive: true, force: true }));
        });
    });

    describe("hasClaudeHooks", () => {
        it("should return false for repos without Claude settings", () => {
            const repoPath = pathJoin(__dirname, "repository-clone-with-copilot-instructions");
            expect(hasClaudeHooks(repoPath)).toBe(false);
        });

        it("should return false for repos with Claude settings but no hooks", async () => {
            // Create a test directory with Claude settings but no hooks
            const testDir = "/tmp/test-claude-no-hooks";
            const settingsContent = {
                model: "claude-3-5-sonnet-20241022",
                maxTokens: 4096,
                temperature: 0.7,
            };

            await import("fs").then((fs) =>
                fs.promises.mkdir(pathJoin(testDir, ".claude"), {
                    recursive: true,
                }),
            );
            await import("fs").then((fs) =>
                fs.promises.writeFile(
                    pathJoin(testDir, ".claude", "settings.json"),
                    JSON.stringify(settingsContent, null, 2),
                ),
            );

            const result = hasClaudeHooks(testDir);
            expect(result).toBe(false);

            // Clean up
            await import("fs").then((fs) => fs.promises.rm(testDir, { recursive: true, force: true }));
        });

        it("should return false for repos with Claude settings but empty hooks object", async () => {
            // Create a test directory with Claude settings but empty hooks
            const testDir = "/tmp/test-claude-empty-hooks";
            const settingsContent = {
                model: "claude-3-5-sonnet-20241022",
                hooks: {},
            };

            await import("fs").then((fs) =>
                fs.promises.mkdir(pathJoin(testDir, ".claude"), {
                    recursive: true,
                }),
            );
            await import("fs").then((fs) =>
                fs.promises.writeFile(
                    pathJoin(testDir, ".claude", "settings.json"),
                    JSON.stringify(settingsContent, null, 2),
                ),
            );

            const result = hasClaudeHooks(testDir);
            expect(result).toBe(false);

            // Clean up
            await import("fs").then((fs) => fs.promises.rm(testDir, { recursive: true, force: true }));
        });

        it("should return false for repos with Claude settings but hooks with empty arrays", async () => {
            // Create a test directory with Claude settings but hooks with empty arrays
            const testDir = "/tmp/test-claude-empty-hook-arrays";
            const settingsContent = {
                model: "claude-3-5-sonnet-20241022",
                hooks: {
                    PreToolUse: [],
                    PostToolUse: [],
                },
            };

            await import("fs").then((fs) =>
                fs.promises.mkdir(pathJoin(testDir, ".claude"), {
                    recursive: true,
                }),
            );
            await import("fs").then((fs) =>
                fs.promises.writeFile(
                    pathJoin(testDir, ".claude", "settings.json"),
                    JSON.stringify(settingsContent, null, 2),
                ),
            );

            const result = hasClaudeHooks(testDir);
            expect(result).toBe(false);

            // Clean up
            await import("fs").then((fs) => fs.promises.rm(testDir, { recursive: true, force: true }));
        });

        it("should return true for repos with Claude settings and PreToolUse hooks", async () => {
            // Create a test directory with Claude settings and PreToolUse hooks
            const testDir = "/tmp/test-claude-pretooluse-hooks";
            const settingsContent = {
                model: "claude-3-5-sonnet-20241022",
                hooks: {
                    PreToolUse: [
                        {
                            matcher: "Bash",
                            hooks: [
                                {
                                    type: "command",
                                    command:
                                        'jq -r \'"\\(.tool_input.command) - \\(.tool_input.description // "No description")"\' >> ~/.claude/bash-command-log.txt',
                                },
                            ],
                        },
                    ],
                },
            };

            await import("fs").then((fs) =>
                fs.promises.mkdir(pathJoin(testDir, ".claude"), {
                    recursive: true,
                }),
            );
            await import("fs").then((fs) =>
                fs.promises.writeFile(
                    pathJoin(testDir, ".claude", "settings.json"),
                    JSON.stringify(settingsContent, null, 2),
                ),
            );

            const result = hasClaudeHooks(testDir);
            expect(result).toBe(true);

            // Clean up
            await import("fs").then((fs) => fs.promises.rm(testDir, { recursive: true, force: true }));
        });

        it("should return true for repos with Claude settings and PostToolUse hooks", async () => {
            // Create a test directory with Claude settings and PostToolUse hooks
            const testDir = "/tmp/test-claude-posttooluse-hooks";
            const settingsContent = {
                model: "claude-3-5-sonnet-20241022",
                hooks: {
                    PostToolUse: [
                        {
                            matcher: "Bash",
                            hooks: [
                                {
                                    type: "command",
                                    command:
                                        'jq -r \'"\\(.tool_input.command) - \\(.tool_input.description // "No description")"\' >> ~/.claude/bash-command-log.txt',
                                },
                            ],
                        },
                    ],
                },
            };

            await import("fs").then((fs) =>
                fs.promises.mkdir(pathJoin(testDir, ".claude"), {
                    recursive: true,
                }),
            );
            await import("fs").then((fs) =>
                fs.promises.writeFile(
                    pathJoin(testDir, ".claude", "settings.json"),
                    JSON.stringify(settingsContent, null, 2),
                ),
            );

            const result = hasClaudeHooks(testDir);
            expect(result).toBe(true);

            // Clean up
            await import("fs").then((fs) => fs.promises.rm(testDir, { recursive: true, force: true }));
        });

        it("should return true for repos with Claude settings and Notification hooks", async () => {
            // Create a test directory with Claude settings and Notification hooks
            const testDir = "/tmp/test-claude-notification-hooks";
            const settingsContent = {
                model: "claude-3-5-sonnet-20241022",
                hooks: {
                    Notification: [
                        {
                            matcher: "Bash",
                            hooks: [
                                {
                                    type: "command",
                                    command:
                                        'jq -r \'"\\(.tool_input.command) - \\(.tool_input.description // "No description")"\' >> ~/.claude/bash-command-log.txt',
                                },
                            ],
                        },
                    ],
                },
            };

            await import("fs").then((fs) =>
                fs.promises.mkdir(pathJoin(testDir, ".claude"), {
                    recursive: true,
                }),
            );
            await import("fs").then((fs) =>
                fs.promises.writeFile(
                    pathJoin(testDir, ".claude", "settings.json"),
                    JSON.stringify(settingsContent, null, 2),
                ),
            );

            const result = hasClaudeHooks(testDir);
            expect(result).toBe(true);

            // Clean up
            await import("fs").then((fs) => fs.promises.rm(testDir, { recursive: true, force: true }));
        });

        it("should return true for repos with Claude settings and Stop hooks", async () => {
            // Create a test directory with Claude settings and Stop hooks
            const testDir = "/tmp/test-claude-stop-hooks";
            const settingsContent = {
                model: "claude-3-5-sonnet-20241022",
                hooks: {
                    Stop: [
                        {
                            matcher: "Bash",
                            hooks: [
                                {
                                    type: "command",
                                    command:
                                        'jq -r \'"\\(.tool_input.command) - \\(.tool_input.description // "No description")"\' >> ~/.claude/bash-command-log.txt',
                                },
                            ],
                        },
                    ],
                },
            };

            await import("fs").then((fs) =>
                fs.promises.mkdir(pathJoin(testDir, ".claude"), {
                    recursive: true,
                }),
            );
            await import("fs").then((fs) =>
                fs.promises.writeFile(
                    pathJoin(testDir, ".claude", "settings.json"),
                    JSON.stringify(settingsContent, null, 2),
                ),
            );

            const result = hasClaudeHooks(testDir);
            expect(result).toBe(true);

            // Clean up
            await import("fs").then((fs) => fs.promises.rm(testDir, { recursive: true, force: true }));
        });

        it("should return false for repos with Claude settings but hooks is not an object", async () => {
            // Create a test directory with Claude settings but hooks is not an object
            const testDir = "/tmp/test-claude-hooks-not-object";
            const settingsContent = {
                model: "claude-3-5-sonnet-20241022",
                hooks: "not an object",
            };

            await import("fs").then((fs) =>
                fs.promises.mkdir(pathJoin(testDir, ".claude"), {
                    recursive: true,
                }),
            );
            await import("fs").then((fs) =>
                fs.promises.writeFile(
                    pathJoin(testDir, ".claude", "settings.json"),
                    JSON.stringify(settingsContent, null, 2),
                ),
            );

            const result = hasClaudeHooks(testDir);
            expect(result).toBe(false);

            // Clean up
            await import("fs").then((fs) => fs.promises.rm(testDir, { recursive: true, force: true }));
        });

        it("should return false for repos with Claude settings but hook values are not arrays", async () => {
            // Create a test directory with Claude settings but hook values are not arrays
            const testDir = "/tmp/test-claude-hooks-not-arrays";
            const settingsContent = {
                model: "claude-3-5-sonnet-20241022",
                hooks: {
                    PreToolUse: "not an array",
                    PostToolUse: 123,
                },
            };

            await import("fs").then((fs) =>
                fs.promises.mkdir(pathJoin(testDir, ".claude"), {
                    recursive: true,
                }),
            );
            await import("fs").then((fs) =>
                fs.promises.writeFile(
                    pathJoin(testDir, ".claude", "settings.json"),
                    JSON.stringify(settingsContent, null, 2),
                ),
            );

            const result = hasClaudeHooks(testDir);
            expect(result).toBe(false);

            // Clean up
            await import("fs").then((fs) => fs.promises.rm(testDir, { recursive: true, force: true }));
        });

        it("should return false for repos with invalid JSON in Claude settings", async () => {
            // Create a test directory with invalid JSON
            const testDir = "/tmp/test-claude-hooks-invalid-json";
            await import("fs").then((fs) =>
                fs.promises.mkdir(pathJoin(testDir, ".claude"), {
                    recursive: true,
                }),
            );
            await import("fs").then((fs) =>
                fs.promises.writeFile(pathJoin(testDir, ".claude", "settings.json"), "{ invalid json content"),
            );

            const result = hasClaudeHooks(testDir);
            expect(result).toBe(false);

            // Clean up
            await import("fs").then((fs) => fs.promises.rm(testDir, { recursive: true, force: true }));
        });

        it("should return false for repos with Claude settings but unrecognized hook types only", async () => {
            // Create a test directory with unrecognized hook types
            const testDir = "/tmp/test-claude-unrecognized-hooks";
            const settingsContent = {
                model: "claude-3-5-sonnet-20241022",
                hooks: {
                    UnrecognizedHook: [
                        {
                            matcher: "Bash",
                            hooks: [
                                {
                                    type: "command",
                                    command:
                                        'jq -r \'"\\(.tool_input.command) - \\(.tool_input.description // "No description")"\' >> ~/.claude/bash-command-log.txt',
                                },
                            ],
                        },
                    ],
                },
            };

            await import("fs").then((fs) =>
                fs.promises.mkdir(pathJoin(testDir, ".claude"), {
                    recursive: true,
                }),
            );
            await import("fs").then((fs) =>
                fs.promises.writeFile(
                    pathJoin(testDir, ".claude", "settings.json"),
                    JSON.stringify(settingsContent, null, 2),
                ),
            );

            const result = hasClaudeHooks(testDir);
            expect(result).toBe(false);

            // Clean up
            await import("fs").then((fs) => fs.promises.rm(testDir, { recursive: true, force: true }));
        });
    });

    describe("readCustomInstructions", () => {
        describe("when Copilot instructions exist", () => {
            it("should read Copilot instructions and ignore fallbacks", async () => {
                const repoPath = pathJoin(__dirname, "repository-clone-with-copilot-instructions");
                const result = await readCustomInstructions(repoPath);
                expect(result).toEqual({
                    content: "Hello world!\n",
                    source: "copilot",
                    sourcePath: ".github/copilot-instructions.md",
                });
            });
        });

        describe("when only Claude instructions exist", () => {
            it("should read Claude instructions as fallback", async () => {
                const repoPath = pathJoin(__dirname, "repository-clone-with-claude-custom-instructions");
                const result = await readCustomInstructions(repoPath);
                expect(result).toEqual({
                    content: "Claude instructions test content\n",
                    source: "claude",
                    sourcePath: "CLAUDE.md",
                });
            });
        });

        describe("when only Codex instructions exist", () => {
            it("should read Codex instructions as fallback", async () => {
                const repoPath = pathJoin(__dirname, "repository-clone-with-codex-custom-instructions");
                const result = await readCustomInstructions(repoPath);
                expect(result).toEqual({
                    content: "Codex instructions test content\n",
                    source: "codex",
                    sourcePath: "AGENTS.md",
                });
            });
        });

        describe("when only Gemini instructions exist", () => {
            it("should read Gemini instructions as fallback", async () => {
                const repoPath = pathJoin(__dirname, "repository-clone-with-gemini-custom-instructions");
                const result = await readCustomInstructions(repoPath);
                expect(result).toEqual({
                    content: "Gemini instructions test content\n",
                    source: "gemini",
                    sourcePath: "GEMINI.md",
                });
            });
        });

        describe("when no custom instructions exist", () => {
            it("should return undefined", async () => {
                const repoPath = pathJoin(__dirname, "repository-clone-with-copilot-setup-steps-yml");
                const result = await readCustomInstructions(repoPath);
                expect(result).toBeUndefined();
            });
        });

        describe("when vscode custom instructions exist", () => {
            it("should read Copilot instructions as primary and VS Code instructions as additional", async () => {
                const repoPath = pathJoin(__dirname, "repository-clone-with-copilot-and-vscode-instructions");
                const result = await readCustomInstructions(repoPath);

                expect(result?.source).toBe("copilot");
                expect(result?.content).toContain("Primary Copilot instructions for the repository");
                expect(result?.additionalInstructions?.source).toBe("vscode");
                expect(result?.additionalInstructions?.content).toContain("TypeScript specific instructions");
                expect(result?.additionalInstructions?.content).toContain("| *.ts |");
                expect(result?.additionalInstructions?.content).toContain("| *.js |");
            });

            it("When only VS Code instructions exist", async () => {
                const repoPath = pathJoin(__dirname, "repository-clone-with-github-instructions");
                const result = await readCustomInstructions(repoPath);
                expect(result?.source).toBe("vscode");
                expect(result?.sourcePath).toBe(".github/instructions");
                expect(result?.content).toBeDefined();
            });

            it("should return undefined for repos without VS Code instructions", async () => {
                const repoPath = pathJoin(__dirname, "repository-clone-with-copilot-instructions");
                const result = await readVSCodeInstructions(repoPath);
                expect(result).toBeUndefined();
            });

            it("should read and process VS Code instructions with applyTo metadata", async () => {
                const repoPath = pathJoin(__dirname, "repository-clone-with-copilot-and-vscode-instructions");
                const result = await readVSCodeInstructions(repoPath);

                expect(result?.source).toBe("vscode");
                expect(result?.sourcePath).toBe(".github/instructions");
                expect(result?.content).toContain("TypeScript specific instructions");
                expect(result?.content).toContain("| *.ts |");
                expect(result?.content).toContain("| *.js |");
                expect(result?.content).toContain("javascript.instructions.md");
                expect(result?.content).toContain("typescript.instructions.md");
            });
        });

        describe("fallback priority", () => {
            it("should prefer Claude over Codex when both exist but no Copilot instructions", async () => {
                // Create a test directory with both Claude and Codex instructions
                const testDir = "/tmp/test-fallback-priority";
                await import("fs").then((fs) => fs.promises.mkdir(testDir, { recursive: true }));

                // Write different content to each file
                await import("fs").then((fs) =>
                    fs.promises.writeFile(pathJoin(testDir, "CLAUDE.md"), "Claude instructions"),
                );
                await import("fs").then((fs) =>
                    fs.promises.writeFile(pathJoin(testDir, "AGENTS.md"), "Codex instructions"),
                );

                const result = await readCustomInstructions(testDir);
                expect(result).toEqual({
                    content: "Claude instructions",
                    source: "claude",
                    sourcePath: "CLAUDE.md",
                });

                // Clean up
                await import("fs").then((fs) => fs.promises.rm(testDir, { recursive: true, force: true }));
            });

            it("should prefer Codex over Gemini when both exist but no Copilot or Claude instructions", async () => {
                // Create a test directory with both Codex and Gemini instructions
                const testDir = "/tmp/test-fallback-priority-codex-gemini";
                await import("fs").then((fs) => fs.promises.mkdir(testDir, { recursive: true }));

                // Write different content to each file
                await import("fs").then((fs) =>
                    fs.promises.writeFile(pathJoin(testDir, "AGENTS.md"), "Codex instructions"),
                );
                await import("fs").then((fs) =>
                    fs.promises.writeFile(pathJoin(testDir, "GEMINI.md"), "Gemini instructions"),
                );

                const result = await readCustomInstructions(testDir);
                expect(result).toEqual({
                    content: "Codex instructions",
                    source: "codex",
                    sourcePath: "AGENTS.md",
                });

                // Clean up
                await import("fs").then((fs) => fs.promises.rm(testDir, { recursive: true, force: true }));
            });
        });
    });
});
