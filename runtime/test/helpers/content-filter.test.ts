/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import { readFileSync } from "fs";
import { join as pathJoin } from "path";
import { describe, expect, it } from "vitest";
import {
    applyContentFilter,
    ContentFilterMode,
    filterAllJsonFields,
    filterContent,
    filterMarkdown,
    getContentFilterMode,
} from "../../src/helpers/content-filter";

describe("Content Filter", () => {
    describe("filterMarkdown", () => {
        it("should return empty string if input is empty", () => {
            expect(filterMarkdown("")).toBe("");
            expect(filterMarkdown(undefined as unknown as string)).toBe(undefined);
        });

        it("should remove HTML comments outside code blocks", () => {
            const input = "before <!-- comment --> after";
            const expected = "before  after";
            expect(filterMarkdown(input)).toBe(expected);
        });

        it("should preserve HTML comments inside code blocks", () => {
            const input = "before ```html\n<!-- comment -->\n``` after";
            const expected = "before ```html\n<!-- comment -->\n``` after";
            const filtered = filterMarkdown(input);
            expect(filtered).toBe(expected);
        });

        it("should filter disallowed HTML tags", () => {
            const input = 'before <script>alert("xss")</script> after';
            const expected = "before  after";
            const filtered = filterMarkdown(input);
            expect(filtered).toBe(expected);
        });

        it("should keep allowed HTML tags", () => {
            const input = "before <b>bold</b> and <i>italic</i> after";
            const expected = "before <b>bold</b> and <i>italic</i> after";
            const filtered = filterMarkdown(input);
            expect(filtered).toBe(expected);
        });

        it("should allow custom XML tags if specified", () => {
            const input = `<comment_old><author>@helen</author>Please re-test using the existing get-date.sh, auditSchedule.json, and services.csv files</comment_old>`;
            const expected = input;
            const filtered = filterMarkdown(input, ["comment_old", "author"]);
            expect(filtered).toBe(expected);
        });

        it("should filter attributes on allowed HTML tags", () => {
            const input = 'before <a href="https://example.com" onclick="alert(1)">link</a> after';
            const expected = 'before <a href="https://example.com">link</a> after';
            const filtered = filterMarkdown(input);
            expect(filtered).toBe(expected);
        });

        it("should handle complex content with code blocks", () => {
            let input = `
Here is some text with <!-\u{E0070}- comme\u{E0070}nt --> and <scri\u{E0070}pt>alert(1)\u{E0070}</script> and \u{E0070}\`\u{E0070}<sc\u{E0070}ript>alert(2)</script>\`.

\`\`\`html
<div onc\u{E0070}\u{E0070}lick="alert('xss')">
    <!-- This comment should be preserved -->
    <s\u{E0070}cript>ale\u{E0070}rt("This scri\u{E0070}pt tag should be preserved in the code block")</script>
</div>
\`\`\u{E0070}\u{E0070}\`
\u{E0070}😅\u{E0070} \u{E0070}🏃🏻‍♀️\u{E0070}
<img src="I am trying to get away with something here">
<img src="someprotocol://tricked-you">
More te\u{E0070}xt with <img src="https://gist.github.com/assets/image.jpg" onload="alert(1)" alt="image"> here.
`;
            const expected = `
Here is some text with  and  and \`<script>alert(2)</script>\`.

\`\`\`html
<div onclick="alert('xss')">
    <!-- This comment should be preserved -->
    <script>alert("This script tag should be preserved in the code block")</script>
</div>
\`\`\`
😅 🏃🏻‍♀️
<img>
<img>
More text with <img src="https://gist.github.com/assets/image.jpg" alt="image"> here.
`;
            for (let i = 0; i < 100; i++) {
                const index = Math.floor(Math.random() * input.length - 1);
                input = input.slice(0, index) + "\u{E0025}" + input.slice(index);
            }
            const filtered = filterMarkdown(input);
            expect(filtered).toBe(expected);
        });

        it("should remove invisible Unicode characters in the tag range", () => {
            // Create a string with invisible characters (U+E0001, U+E0020, 0xE004A)
            const invisibleChar1 = "\u{E0001}";
            const invisibleChar2 = "\u{E0020}";
            const invisibleChar3 = "\u{E004A}";
            const invisibleChar4 = "\u{E0070}";

            const input = `He${invisibleChar1}llo Wo${invisibleChar2}r${invisibleChar3}l${invisibleChar4}d!`;
            const expected = "Hello World!";
            const filtered = filterMarkdown(input);
            expect(filtered).toBe(expected);
        });

        it("should handle smuggled content in copilot-instructions.md", () => {
            const instructionsWithDirective = readFileSync(
                pathJoin(__dirname, "copilot-instructions-with-hidden-directive.md"),
                "utf-8",
            );
            const instructionsWithoutDirective = readFileSync(
                pathJoin(__dirname, "copilot-instructions-wo-hidden-directive.md"),
                "utf-8",
            );
            const filtered = filterMarkdown(instructionsWithDirective.toString());
            expect(filtered).toBe(instructionsWithoutDirective.toString());
        });
    });

    describe("filterContent", () => {
        it("should filter hidden characters", () => {
            const input = "before <!-- comment --> `<notfiltered></notfiltered>` \u{E0001}after";
            const expected = "before <!-- comment --> `<notfiltered></notfiltered>` after";
            expect(filterContent(input, ContentFilterMode.HiddenCharacters)).toBe(expected);
        });
        it("should filter markdown", () => {
            const input = "before <!-- comment --> `<notfiltered></notfiltered>` \u{E0001}after";
            const expected = "before `<notfiltered></notfiltered>` after";
            expect(filterContent(input, ContentFilterMode.Markdown)).toBe(expected);
        });
        it("should not filter", () => {
            const input = "before <!-- comment --> `<notfiltered></notfiltered>` \u{E0001}after";
            expect(filterContent(input, ContentFilterMode.None)).toBe(input);
        });
        it("should default to markdown filtering", () => {
            const input = "before <!-- comment --> `<notfiltered></notfiltered>` \u{E0001}after";
            const expected = "before `<notfiltered></notfiltered>` after";
            expect(filterContent(input)).toBe(expected);
        });
    });

    describe("getContentFilterMode", () => {
        it("should return HiddenCharacters for empty filter mode", () => {
            expect(getContentFilterMode("")).toBe(ContentFilterMode.HiddenCharacters);
        });

        it("should return HiddenCharacters for non-valid filter mode", () => {
            expect(getContentFilterMode("random")).toBe(ContentFilterMode.HiddenCharacters);
        });

        it("should return Markdown for non-valid filter mode", () => {
            expect(getContentFilterMode("markdown")).toBe(ContentFilterMode.Markdown);
        });

        it("should handle weird casing", () => {
            expect(getContentFilterMode("MaRKdown")).toBe(ContentFilterMode.Markdown);
        });

        it("should return None for non-valid filter mode", () => {
            expect(getContentFilterMode("none")).toBe(ContentFilterMode.None);
        });
    });

    describe("filterAllJsonFields", () => {
        it("should filter nested string fields markdown", () => {
            const input = {
                text: "before <!-- comment --> after",
                nested: {
                    text: "nested before <!-- comment --> nested after",
                },
            };
            const expected = {
                text: "before  after",
                nested: {
                    text: "nested before  nested after",
                },
            };
            expect(filterAllJsonFields(input, ContentFilterMode.Markdown)).toEqual(expected);
        });

        it("should filter nested string fields hidden characters", () => {
            const input = {
                text: "before \u{E0001} after",
                nested: {
                    text: "nested before \u{E0001} nested after",
                },
            };
            const expected = {
                text: "before  after",
                nested: {
                    text: "nested before  nested after",
                },
            };
            expect(filterAllJsonFields(input, ContentFilterMode.HiddenCharacters)).toEqual(expected);
        });

        it("should not filter nested string fields when mode is None", () => {
            const input = {
                text: "before <!-- comment --> after",
                nested: {
                    text: "nested before <!-- comment --> nested after",
                },
            };
            const expected = {
                text: "before <!-- comment --> after",
                nested: {
                    text: "nested before <!-- comment --> nested after",
                },
            };
            expect(filterAllJsonFields(input, ContentFilterMode.None)).toEqual(expected);
        });

        it("should handle empty objects", () => {
            const input = {};
            const expected = {};
            expect(filterAllJsonFields(input, ContentFilterMode.Markdown)).toEqual(expected);
        });

        it("should handle undefined", () => {
            const input = undefined;
            const expected = undefined;
            expect(filterAllJsonFields(input, ContentFilterMode.Markdown)).toEqual(expected);
        });

        it("should handle mixed content with arrays and objects", () => {
            const input = {
                items: [
                    { text: "before <!-- comment --> after" },
                    { text: "nested before <!-- comment --> nested after" },
                ],
                info: { text: "info before <!-- comment --> info after" },
            };
            const expected = {
                items: [{ text: "before  after" }, { text: "nested before  nested after" }],
                info: { text: "info before  info after" },
            };
            expect(filterAllJsonFields(input, ContentFilterMode.Markdown)).toEqual(expected);
        });
    });

    describe("applyContentFilter", () => {
        it("should apply content filter based on mode", () => {
            const inputMarkdown = "before <!-- comment --> after";
            const inputHiddenChars = "before \u{E0001} after";
            const expectedMarkdown = "before  after";
            const expectedHiddenCharacters = "before  after";

            expect(applyContentFilter(inputMarkdown, ContentFilterMode.Markdown)).toBe(expectedMarkdown);
            expect(applyContentFilter(inputHiddenChars, ContentFilterMode.HiddenCharacters)).toBe(
                expectedHiddenCharacters,
            );
            expect(applyContentFilter(inputMarkdown, ContentFilterMode.None)).toBe(inputMarkdown);
        });

        it("should handle edge cases", () => {
            const inputEmpty = "";
            const inputUndefined = undefined;

            expect(applyContentFilter(inputEmpty, ContentFilterMode.Markdown)).toBe(inputEmpty);
            expect(applyContentFilter(inputUndefined as unknown as string, ContentFilterMode.HiddenCharacters)).toBe(
                inputUndefined,
            );
        });
    });
});
