/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { afterEach, beforeEach, describe, expect, test, vi } from "vitest";
import { redactedValue, SecretFilter } from "../../src/helpers/SecretFilter";
import { Runner, RunnerLogger } from "../../src/runner";

// Mock the settings modules
vi.mock("../../src/settings/current", () => ({
    getCurrentSettings: vi.fn(),
    hasCurrentSettings: vi.fn(),
}));

vi.mock("../../src/settings/types", () => ({
    getSettingsSecretVals: vi.fn(),
}));

vi.mock("../../src/helpers/environment-variables", () => ({
    getNotEmptyStringEnvVar: vi.fn(),
}));

import { getNotEmptyStringEnvVar } from "../../src/helpers/environment-variables";
import { getCurrentSettings, hasCurrentSettings } from "../../src/settings/current";
import { getSettingsSecretVals, RuntimeSettings } from "../../src/settings/types";

describe("SecretFilter", () => {
    let secretFilter: SecretFilter;
    let mockRunner: Runner;
    let mockLogger: RunnerLogger;

    beforeEach(() => {
        // Reset all mocks
        vi.resetAllMocks();

        // Create mock logger
        mockLogger = {
            info: vi.fn(),
            error: vi.fn(),
            warn: vi.fn(),
            debug: vi.fn(),
        } as unknown as RunnerLogger;

        // Create mock runner
        mockRunner = {
            logger: mockLogger,
            sensitiveKeys: ["SENSITIVE_KEY_1", "SENSITIVE_KEY_2"],
        } as unknown as Runner;

        // Get a fresh instance of SecretFilter
        secretFilter = SecretFilter.getInstance();
        secretFilter.setRunner(mockRunner);

        // Setup default mock returns
        vi.mocked(hasCurrentSettings).mockReturnValue(false);
        vi.mocked(getNotEmptyStringEnvVar).mockReturnValue(undefined);
        vi.mocked(getSettingsSecretVals).mockReturnValue([]);
    });

    afterEach(() => {
        vi.resetAllMocks();
    });

    describe("filterSecrets", () => {
        test("should redact some well known secret patterns (hard to test as git policies prevent committing things that look real)", () => {
            const jwtInput =
                "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiYWRtaW4iOnRydWV9.TJVA95OrM7E2cBab30RMHrHDcEfxjoYZgeFONFh7HgQ";
            const jwtResult = secretFilter.filterSecrets(jwtInput);
            // JWT token should be completely redacted
            expect(jwtResult).toBe(redactedValue);

            const bearerInput = `curl -H "Authorization: Bearer abc123def456"`;
            const bearerResult = secretFilter.filterSecrets(bearerInput);
            // Bearer token should be redacted
            expect(bearerResult).toBe(`curl -H "Authorization: ${redactedValue}"`);

            const passwordInput = "Server=localhost;Database=test;Password=*********;";
            const passwordResult = secretFilter.filterSecrets(passwordInput);
            // Password should be redacted
            expect(passwordResult).toBe(`Server=localhost;Database=test;${redactedValue};`);

            // URI pattern replaces the entire scheme://user:pass@ part with ${redactedValue}
            const uriInput = "mysql://user:password123@localhost/db";
            const uriResult = secretFilter.filterSecrets(uriInput);
            // URI credentials should be redacted
            expect(uriResult).toBe(`${redactedValue}localhost/db`);
        });

        test("should redact environment variable secrets", () => {
            const secretValue = "my-secret-value";
            vi.mocked(getNotEmptyStringEnvVar).mockImplementation((varName: string) => {
                if (varName === "SENSITIVE_KEY_1") {
                    return secretValue;
                }
                return undefined;
            });

            const input = `This contains ${secretValue} which should be redacted`;

            const result = secretFilter.filterSecrets(input);

            expect(result).toBe(`This contains ${redactedValue} which should be redacted`);
        });

        test("should redact base64 encoded secrets", () => {
            const secretValue = "my-secret-value";
            const base64Secret = Buffer.from(secretValue, "utf8").toString("base64");
            vi.mocked(getNotEmptyStringEnvVar).mockImplementation((varName: string) => {
                if (varName === "SENSITIVE_KEY_1") {
                    return secretValue;
                }
                return undefined;
            });

            const input = `This contains ${base64Secret} which is base64 encoded secret`;

            const result = secretFilter.filterSecrets(input);

            expect(result).toBe(`This contains ${redactedValue} which is base64 encoded secret`);
        });

        test("should redact settings secrets", () => {
            const settingsSecret = "settings-secret-123";
            vi.mocked(hasCurrentSettings).mockReturnValue(true);
            vi.mocked(getCurrentSettings).mockReturnValue({} as RuntimeSettings);
            vi.mocked(getSettingsSecretVals).mockReturnValue([settingsSecret]);

            const input = `Configuration value: ${settingsSecret}`;

            const result = secretFilter.filterSecrets(input);

            expect(result).toBe(`Configuration value: ${redactedValue}`);
        });

        test("should handle Error objects", () => {
            const secretValue = "error-secret";
            vi.mocked(getNotEmptyStringEnvVar).mockImplementation((varName: string) => {
                if (varName === "SENSITIVE_KEY_1") {
                    return secretValue;
                }
                return undefined;
            });

            const error = new Error(`Database connection failed with password: ${secretValue}`);

            const result = secretFilter.filterSecrets(error);

            expect(result).toBeInstanceOf(Error);
            expect((result as Error).message).toBe(`Database connection failed with password: ${redactedValue}`);
        });

        test("should redact multiple secrets in the same string", () => {
            const secret1 = "first-secret";
            const secret2 = "second-secret";
            vi.mocked(getNotEmptyStringEnvVar).mockImplementation((varName: string) => {
                if (varName === "SENSITIVE_KEY_1") {
                    return secret1;
                }
                if (varName === "SENSITIVE_KEY_2") {
                    return secret2;
                }
                return undefined;
            });

            const input = `Config has ${secret1} and ${secret2} plus Bearer token123`;

            const result = secretFilter.filterSecrets(input);

            expect(result).toBe(`Config has ${redactedValue} and ${redactedValue} plus ${redactedValue}`);
        });

        test("should handle empty string", () => {
            const result = secretFilter.filterSecrets("");

            expect(result).toBe("");
        });

        test("should handle very long strings with secrets", () => {
            const secret = "long-secret";
            vi.mocked(getNotEmptyStringEnvVar).mockImplementation((varName: string) => {
                if (varName === "SENSITIVE_KEY_1") {
                    return secret;
                }
                return undefined;
            });

            const longString = "x".repeat(1000) + secret + "y".repeat(1000);

            const result = secretFilter.filterSecrets(longString);

            expect(result).toBe("x".repeat(1000) + redactedValue + "y".repeat(1000));
        });
    });

    describe("filterSecretsFromJsonString", () => {
        test("should redact secrets in valid JSON string", () => {
            const secretValue = "json-secret-123";
            vi.mocked(getNotEmptyStringEnvVar).mockImplementation((varName: string) => {
                if (varName === "SENSITIVE_KEY_1") {
                    return secretValue;
                }
                return undefined;
            });

            const jsonString = JSON.stringify({
                username: "user1",
                password: secretValue,
                config: {
                    apiKey: secretValue,
                },
            });

            const result = secretFilter.filterSecretsFromJsonString(jsonString);

            const parsed = JSON.parse(result);
            expect(parsed.username).toBe("user1");
            expect(parsed.password).toBe(redactedValue);
            expect(parsed.config.apiKey).toBe(redactedValue);
        });

        test("should fall back to string filtering for invalid JSON", () => {
            const secretValue = "invalid-json-secret";
            vi.mocked(getNotEmptyStringEnvVar).mockImplementation((varName: string) => {
                if (varName === "SENSITIVE_KEY_1") {
                    return secretValue;
                }
                return undefined;
            });

            const invalidJson = `{ "incomplete": "${secretValue}`;

            const result = secretFilter.filterSecretsFromJsonString(invalidJson);

            expect(result).toBe(`{ "incomplete": "${redactedValue}`);
        });
    });

    describe("filterSecretsFromObj", () => {
        test("should redact secrets in nested objects", () => {
            const secretValue = "obj-secret-456";
            vi.mocked(getNotEmptyStringEnvVar).mockImplementation((varName: string) => {
                if (varName === "SENSITIVE_KEY_1") {
                    return secretValue;
                }
                return undefined;
            });

            const obj = {
                user: "testuser",
                password: secretValue,
                settings: {
                    apiKey: secretValue,
                    timeout: 30,
                },
                tags: ["tag1", secretValue, "tag3"],
            };

            const result = secretFilter.filterSecretsFromObj(obj);

            expect(result.user).toBe("testuser");
            expect(result.password).toBe(redactedValue);
            expect(result.settings.apiKey).toBe(redactedValue);
            expect(result.settings.timeout).toBe(30);
            expect(result.tags).toEqual(["tag1", redactedValue, "tag3"]);
        });

        test("should handle null and undefined values", () => {
            expect(secretFilter.filterSecretsFromObj(null)).toBe(null);
            expect(secretFilter.filterSecretsFromObj(undefined)).toBe(undefined);
        });

        test("should handle numbers that might be secrets", () => {
            const secretNumber = "123456";
            vi.mocked(getNotEmptyStringEnvVar).mockImplementation((varName: string) => {
                if (varName === "SENSITIVE_KEY_1") {
                    return secretNumber;
                }
                return undefined;
            });

            const obj = { port: 123456, count: 789 };

            const result = secretFilter.filterSecretsFromObj(obj);

            // Number containing secret becomes redacted string
            expect(result.port).toBe(redactedValue);
            // Normal number stays as number
            expect(result.count).toBe(789);
        });

        test("should handle arrays", () => {
            const secretValue = "array-secret";
            vi.mocked(getNotEmptyStringEnvVar).mockImplementation((varName: string) => {
                if (varName === "SENSITIVE_KEY_1") {
                    return secretValue;
                }
                return undefined;
            });

            const arr = ["normal", secretValue, { key: secretValue }];

            const result = secretFilter.filterSecretsFromObj(arr) as unknown[];

            expect(result[0]).toBe("normal");
            expect(result[1]).toBe(redactedValue);
            expect(result[2]).toEqual({ key: redactedValue });
        });

        test("should handle edge cases with empty arrays and objects", () => {
            expect(secretFilter.filterSecretsFromObj([])).toEqual([]);
            expect(secretFilter.filterSecretsFromObj({})).toEqual({});
        });

        test("should handle deeply nested objects", () => {
            const secretValue = "deep-nested-secret";
            vi.mocked(getNotEmptyStringEnvVar).mockImplementation((varName: string) => {
                if (varName === "SENSITIVE_KEY_1") {
                    return secretValue;
                }
                return undefined;
            });

            const obj = {
                level1: {
                    level2: {
                        level3: {
                            secret: secretValue,
                            normal: "value",
                        },
                        array: [{ item: secretValue }, { item: "normal" }],
                    },
                },
            };

            const result = secretFilter.filterSecretsFromObj(obj);

            expect(result.level1.level2.level3.secret).toBe(redactedValue);
            expect(result.level1.level2.level3.normal).toBe("value");
            expect(result.level1.level2.array[0].item).toBe(redactedValue);
            expect(result.level1.level2.array[1].item).toBe("normal");
        });

        test("should handle boolean and other primitive types", () => {
            const obj = {
                boolValue: true,
                nullValue: null,
                undefinedValue: undefined,
                numberValue: 42,
                stringValue: "normal",
            };

            const result = secretFilter.filterSecretsFromObj(obj);

            expect(result.boolValue).toBe(true);
            expect(result.nullValue).toBe(null);
            expect(result.undefinedValue).toBe(undefined);
            expect(result.numberValue).toBe(42);
            expect(result.stringValue).toBe("normal");
        });
    });

    describe("singleton pattern", () => {
        test("should return the same instance", () => {
            const instance1 = SecretFilter.getInstance();
            const instance2 = SecretFilter.getInstance();

            expect(instance1).toBe(instance2);
        });
    });

    describe("setRunner", () => {
        test("should reset cached secret values when runner is set", () => {
            const newMockRunner = {
                logger: mockLogger,
                sensitiveKeys: ["NEW_SENSITIVE_KEY"],
            } as unknown as Runner;

            secretFilter.setRunner(newMockRunner);

            // This is tested indirectly by ensuring new sensitive keys are picked up
            vi.mocked(getNotEmptyStringEnvVar).mockImplementation((varName: string) => {
                if (varName === "NEW_SENSITIVE_KEY") {
                    return "new-secret";
                }
                return undefined;
            });

            const result = secretFilter.filterSecrets("This contains new-secret");
            expect(result).toBe(`This contains ${redactedValue}`);
        });
    });
});
