/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { describe, expect, it } from "vitest";
import { findAndReplaceOne, formatBytes } from "../../src/helpers/string-helpers";

describe("string-helpers", () => {
    describe("findAndReplaceOne", () => {
        it("should use exact match when possible", () => {
            const text = "Hello\nWorld\nTest";
            const oldStr = "World\n";
            const newStr = "Planet\n";
            const result = findAndReplaceOne(text, oldStr, newStr);
            expect(result.type).toBe("exact");
            expect(result.text).toBe("Hello\nPlanet\nTest");
        });

        it("should fallback to fuzzy match when exact fails", () => {
            const text = "Hello  \nWorld  \nTest";
            const oldStr = "Hello\nWorld\n";
            const newStr = "Hi\nPlanet\n";
            const result = findAndReplaceOne(text, oldStr, newStr);
            expect(result.type).toBe("fuzzy");
            expect(result.text).toBe("Hi\nPlanet\nTest");
        });

        it("should handle no matches gracefully", () => {
            const text = "Hello\nWorld\nTest";
            const oldStr = "NotFound";
            const newStr = "Replace";
            const result = findAndReplaceOne(text, oldStr, newStr);
            expect(result.type).toBe("none");
            expect(result.text).toBe(text);
        });

        it("should error on multiple exact matches", () => {
            const text = "Hello\nHello\nTest";
            const oldStr = "Hello";
            const newStr = "Hi";
            const result = findAndReplaceOne(text, oldStr, newStr);
            expect(result.type).toBe("multiple");
            expect(result.text).toBe(text);
        });

        it("should error on multiple fuzzy matches", () => {
            const text = "Hello  \nHello  \nTest";
            const oldStr = "Hello";
            const newStr = "Hi";
            const result = findAndReplaceOne(text, oldStr, newStr);
            expect(result.type).toBe("multiple");
            expect(result.text).toBe(text);
        });

        it("should preserve formatting in non-replaced lines", () => {
            const text = "Keep  \nWorld  \nThis  ";
            const oldStr = "World";
            const newStr = "Planet";
            const result = findAndReplaceOne(text, oldStr, newStr);
            expect(result.type).toBe("exact");
            expect(result.text).toBe("Keep  \nPlanet  \nThis  ");
        });
    });

    describe("formatBytes", () => {
        it("should format bytes correctly with default precision", () => {
            expect(formatBytes(0)).toBe("0 B");
            expect(formatBytes(500)).toBe("500 B");
            expect(formatBytes(1024)).toBe("1.00 KiB");
            expect(formatBytes(1536)).toBe("1.50 KiB");
            expect(formatBytes(1048576)).toBe("1.00 MiB");
            expect(formatBytes(1073741824)).toBe("1.00 GiB");
            expect(formatBytes(1099511627776)).toBe("1.00 TiB");
        });

        it("should apply custom precision when specified", () => {
            expect(formatBytes(1500, 0)).toBe("1 KiB");
            expect(formatBytes(1500, 1)).toBe("1.5 KiB");
            expect(formatBytes(1500, 3)).toBe("1.465 KiB");
            expect(formatBytes(1048576, 4)).toBe("1.0000 MiB");
        });

        it("should use 0 precision for bytes", () => {
            expect(formatBytes(42, 3)).toBe("42 B");
            expect(formatBytes(999, 5)).toBe("999 B");
        });

        it("should handle very large numbers", () => {
            // 1 ZiB (2^70 bytes)
            expect(formatBytes(1180591620717411303424)).toBe("1.00 ZiB");
            // 1 YiB (2^80 bytes)
            expect(formatBytes(1208925819614629174706176)).toBe("1.00 YiB");
        });
    });
});
