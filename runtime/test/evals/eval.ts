/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

/**
 * The tests in this file are non-determinc evals to connstrain the behaviour of the agent.
 * They may not all pass all the time, but should pass "most" of the time.
 * They should be skipped in PR/main CI, but will be run in nightlies.
 */

import { Client as McpClient } from "@modelcontextprotocol/sdk/client/index.js";
import { StdioClientTransport, StdioServerParameters } from "@modelcontextprotocol/sdk/client/stdio.js";
import { exec } from "child_process";
import fs from "fs";
import { ChatCompletion, ChatCompletionMessageToolCall } from "openai/resources";
import os from "os";
import path from "path";
import { v4 as uuidv4 } from "uuid";
import { TestContext } from "vitest";
import { getAgent, getClient, PRDetails, SweAgent, SweAgentAction, SweAgentKind } from "../../src/agents/sweagent";
import { CompoundCallback, IAgentCallback } from "../../src/callbacks/callback";
import { LoggerCallback } from "../../src/callbacks/LoggerCallback";
import { PlaybackAIClient as PlaybackCAPIAIClient } from "../../src/model/capi/ai";
import { Client, ClientOptions } from "../../src/model/client";
import { DefaultExec } from "../../src/runner/exec/default";
import { ConsoleLogger } from "../../src/runner/logger/console";
import { RuntimeSettings } from "../../src/settings";
import { getOrInitSettings } from "../../src/settings/factory";
import { ClientInfo } from "../../src/tools/mcp-transport";
import {
    AgentCallbackCommentReplyEvent,
    AgentCallbackErrorEvent,
    AgentCallbackPartialResultEvent,
    AgentCallbackProgressEvent,
    AgentCallbackResultEvent,
} from "../../src/types";

export const DEFAULT_TIMEOUT_MS = 55 * 60 * 1000; // 55 minutes to match the production timeout

// Add feature flags here to enable them for all evals.
const FEATURE_FLAGS = {
    copilot_swe_agent_parallel_tool_execution: true,
    copilot_swe_agent_sync_pr_title_description: true,
};

export function execCommand(command: string, cwd: string): Promise<string> {
    return new Promise((resolve, reject) => {
        // Expand stdout buffer. We sometimes see "stdout maxBuffer exceeded" errors here.
        exec(command, { cwd, maxBuffer: 1024 * 1024 * 5 }, (error, stdout, stderr) => {
            if (error) {
                reject(`Error executing '${command}': ${stderr || error.message}`);
            } else {
                resolve(stdout);
            }
        });
    });
}

function gitInit(cwd: string): Promise<string> {
    return execCommand('git init && git add . && git commit --allow-empty -m "Initial commit"', cwd);
}

export function gitDiff(cwd: string, baseCommit: string, filePath?: string): Promise<string> {
    const args = filePath ? ` -- ${filePath}` : "";
    return execCommand(`git diff ${baseCommit.trim()}${args}`, cwd);
}

/**  gitClone clones the given repository (in the form of "<owner>/<repo>") into `cwd`.
 *
 * If `EVAL_TEST_GITHUB_TOKEN_<OWNER>` is set, it will be used as an access token for the clone. If unset,
 * and `EVAL_TEST_GITHUB_TOKEN` is set, it will be used instead. Otherwise, git clone with run without any
 * auth information and the expectation is that the repo is public or git has been configured to use a
 * credential helper that will supply authentication information (true on local developer machines).
 *
 * @param cwd The directory to clone into.
 * @param repo The repository to clone, in the form of "owner/repo".
 * @returns A promise that resolves to the output of the clone operation.
 */
function gitClone(cwd: string, repo: string): Promise<string> {
    const orgUpper = repo.split("/")[0].toUpperCase();
    const ORG_TOKEN_KEY = `EVAL_TEST_GITHUB_TOKEN_${orgUpper}`;
    const DEFAULT_TOKEN_KEY = `EVAL_TEST_GITHUB_TOKEN`;
    let token: string | undefined = undefined;

    if (process.env[ORG_TOKEN_KEY]) {
        console.log(`Using token from ${ORG_TOKEN_KEY}`);
        token = process.env[ORG_TOKEN_KEY];
    } else if (process.env[DEFAULT_TOKEN_KEY]) {
        console.log(`Using token from ${DEFAULT_TOKEN_KEY}`);
        token = process.env[DEFAULT_TOKEN_KEY];
    }

    const login = token ? `x-access-token:${token}@` : "";
    return execCommand(`git clone https://${login}github.com/${repo} .`, cwd);
}

function gitCheckout(cwd: string, commit: string, branchName: string): Promise<string> {
    return execCommand(`git checkout -b ${branchName} ${commit}`, cwd);
}

function gitCheckoutBranchAndReset(cwd: string, branchName: string, commit: string): Promise<string> {
    return execCommand(`git checkout ${branchName} && git reset --hard ${commit}`, cwd);
}

async function getCurrentBranch(cwd: string): Promise<string> {
    return (await execCommand("git branch --show-current", cwd)).trim();
}

function getCurrentCommit(cwd: string): Promise<string> {
    return execCommand("git rev-parse HEAD", cwd);
}

export async function applyPatch(cwd: string, diff: string): Promise<boolean> {
    const tempFile = path.join(os.tmpdir(), `sweagent-eval-diff-${uuidv4()}.patch`);
    fs.writeFileSync(tempFile, diff);

    try {
        await execCommand(`git apply --binary ${tempFile}`, cwd);
        return true;
    } catch (error) {
        throw new Error(`Error applying patch: ${error}`);
    } finally {
        // Clean up the temporary file
        fs.unlinkSync(tempFile);
    }
}
function reportChecks(checks: EvaluationAsserts) {
    // Separate successful and failed checks
    const successfulChecks: EvaluationResult[] = [];
    const failedChecks: EvaluationResult[] = [];

    // Process all checks
    for (const [, check] of checks.entries()) {
        if (check.assertion === true) {
            successfulChecks.push(check);
        } else {
            failedChecks.push(check);
        }
    }

    // Display successful checks
    console.groupCollapsed(
        "%c  %cSuccessful Checks",
        "background-color: green; margin-right: 10px",
        "background-color: transparent",
    );
    console.groupCollapsed();
    console.table(
        successfulChecks.map((check) => ({
            Check: check.name,
            Result: "Success",
        })),
    );
    console.groupEnd();
    console.groupEnd();

    // Display failed checks
    if (failedChecks.length > 0) {
        console.groupCollapsed(
            "%c  %cFailed Checks",
            "background-color: red; margin-right: 10px",
            "background-color: transparent",
        );
        console.groupCollapsed();
        console.table(
            failedChecks.map((check) => ({
                Check: check.name,
                Result: "Failed",
                Actual: check.actual ?? "",
            })),
        );
        console.groupEnd();
        console.groupEnd();

        throw new Error(
            `Failed ${failedChecks.length} checks.\n` +
                failedChecks.map((check) => `- ${check.name} (Actual: ${check.actual})`).join("\n"),
        );
    }
}

export function searchDirectory(currentPath: string, check: (file: string) => boolean | null): boolean {
    const files = fs.readdirSync(currentPath);

    for (const file of files) {
        const filePath = path.join(currentPath, file);
        const stats = fs.statSync(filePath);

        const completed = check(filePath);
        if (completed !== null) {
            return completed;
        }

        // Skip node_modules directories to avoid deep recursion
        if (file === "node_modules" || file.startsWith(".")) {
            continue;
        }

        // If it's a directory, search inside it
        if (stats.isDirectory()) {
            if (searchDirectory(filePath, check)) {
                return true;
            }
        }
    }

    return false; // Return false only after all files have been processed and no conditions for early termination were met.
}

export function getModifiedFilesInDiff(diff: string): string[] {
    const diffLines = diff.split("\n");
    // look for all +++ b/ lines to find all files modified
    const modifiedFiles = diffLines
        .map((line) => {
            const match = line.match(/^\+\+\+ b\/(.*)/);
            if (match && match[1]) {
                return match[1];
            }
            return "";
        })
        .filter((line) => line !== "");

    return modifiedFiles;
}

export type EvaluationResult = {
    name: string;
    assertion: boolean;
    actual?: unknown;
};

export type EvaluationAsserts = Map<string, EvaluationResult>;

export interface AssertionArgs {
    context: TestContext;
    repoPath: string;
    diff: string;
    partialResultEvents: AgentCallbackPartialResultEvent[];
    progressEvents: AgentCallbackProgressEvent[];
    commentReplyEvents: AgentCallbackCommentReplyEvent[];
    toolCallMessages: ChatCompletionMessageToolCall[];
    result: PRDetails;
    checks: EvaluationAsserts;
}

export interface TestCase {
    // A name for the test case
    name: string;
    // A description of the test case
    testDescription?: string;
    // Repository in owner/repo format or an object with filenames and contents (and optionally, a file mode)
    repo: string | Record<string, string | { content: string; fileMode: number }>;
    // The commit hash to check out
    commit: string | null;
    // The branch to use for resetting to a specific commit.
    branch?: string;
    // The issue/PR number for tracking the test case
    issueNumber?: number;
    // The full problem description - this will be passed as the prompt to the agent
    problemDescription: string;
    // The agent kind to use - defaults to "sweagent-capi"
    agent?: SweAgentKind;
    // The model to use - defaults to the default model for the agent
    model?: string;
    // client options for the model
    clientOptions?: ClientOptions;
    // A list of pre-recorded chat completion responses that define what the agent should receive when it calls whatever LLM API it is using.
    preRecordedCompletions?: (testFolder: string) => ChatCompletion[];
    // The action to run on the agent - defaults to 'createPRAgent'
    actionName?: keyof SweAgent;
    // A function that will be called after the agent runs to make assertions about the results
    assertions: (args: AssertionArgs) => Promise<void>;
    // An environment variable that can be set to skip the test case
    skipEnvVar?: string;
    // An environment variable that can be set to run the test.  If provided, the test will not
    // run unless the environment variable is set to 'true'.
    runIfEnvVar?: string;
    // A custom function which can be used to skip a test case. If it returns "true", the test will be skipped,
    // if it returns a non empty string, the test will be skipped and the string will be used as the reason.
    skipIf?: () => Promise<boolean | string>;
    // Enable default assertions. If undefined, defaults to true, meaning default assertions will be executed.
    // Default assertions are disabled only when explicitly set to false.
    runDefaultAssertions?: boolean;
    // A set of MCP servers to start and provide to the agent. The key is the name of the server and the value
    // are the parameters used to start the server.
    mcpServers?: Record<string, StdioServerParameters>;
    // A callback which can be used to apply custom settings to the agent before it runs.
    configureSettings?: (
        settings: RuntimeSettings & {
            readonly featureFlags: Record<string, boolean>;
        },
    ) => Promise<void>;
    // An optional adjustment to the start time of the test, in milliseconds.
    // This can simulate a delay in the start of the agent's execution, which can be useful for testing timeouts.
    startTimeAdjustmentMs?: number;
}

export async function runTest(test: TestCase, context: TestContext) {
    if (test.runIfEnvVar && process.env[test.runIfEnvVar] !== "true") {
        context.skip(`Skipping because '${test.runIfEnvVar}' is not set`);
        return;
    } else if (test.skipEnvVar && process.env[test.skipEnvVar] === "true") {
        context.skip(`Skipping because '${test.skipEnvVar}' is set`);
        return;
    } else if (test.skipIf) {
        const skip = await test.skipIf();
        if (skip === true) {
            context.skip(`Skipping because skipIf returned true`);
            return;
        } else if (typeof skip === "string" && skip) {
            context.skip(`Skipping because skipIf returned: ${skip}`);
            return;
        }
    }
    const testFolder = path.join(os.tmpdir(), "sweagent-evals", uuidv4());

    const transports: StdioClientTransport[] = [];
    const clients: ClientInfo[] = [];

    for (const [name, server] of Object.entries(test.mcpServers || {})) {
        const transport = new StdioClientTransport(server);
        const client = new McpClient(
            {
                name: "github-copilot-developer",
                version: "1.0.0",
            },
            {
                capabilities: {
                    experimental: undefined,
                    roots: undefined,
                    sampling: undefined,
                },
            },
        );

        await client.connect(transport);
        transports.push(transport);
        clients.push({
            clientName: name,
            mcpClient: client,
        });
    }

    // Create the directory for the test
    fs.mkdirSync(testFolder, { recursive: true });

    try {
        fs.mkdirSync(testFolder, { recursive: true });
        await innerTest(context, testFolder, test, clients);
    } finally {
        fs.rmSync(testFolder, { recursive: true, force: true });

        for (const transport of transports) {
            await transport.close();
        }
    }
}

async function innerTest(context: TestContext, testFolder: string, test: TestCase, clients: ClientInfo[]) {
    let settings = {
        ...(await getOrInitSettings()),
        featureFlags: { ...FEATURE_FLAGS },
    };
    settings.onlineEvaluation = {
        disableOnlineEvaluation: true,
        enableOnlineEvaluationOutputFile: false,
    };
    settings.service ||= {};
    settings.service.instance ||= {};
    settings.service.instance.id ||= `sweagent-evals-${uuidv4()}`;

    // Always tell the runtime it has as long as the test itself has
    settings.timeoutMs = DEFAULT_TIMEOUT_MS;

    if (test.configureSettings) {
        settings = JSON.parse(JSON.stringify(settings)); // Clone settings to avoid mutating the original
        await test.configureSettings(settings);
    }

    if (settings.timeoutMs) {
        settings.startTimeMs = Date.now() - (test.startTimeAdjustmentMs ?? 0);
    }

    const logger = new ConsoleLogger();
    const exec = new DefaultExec(logger);
    const agentKind = test.agent ?? (process.env.EVAL_AGENT_KIND as SweAgentKind);
    const model = test.model ?? process.env.EVAL_MODEL;
    const options = { ...test.clientOptions, model };

    const logRoot = process.env.EVAL_LOG_ROOT ?? path.join(os.tmpdir(), "sweagent-evals");
    const testOutputFolder = path.join(logRoot, test.name.replace(/[^a-zA-Z0-9_-]/g, "_"), `${Date.now()}`);

    if (!fs.existsSync(testOutputFolder)) {
        fs.mkdirSync(testOutputFolder, { recursive: true });
    }
    console.log(`Test outputs folder for ${test.name}: ${testOutputFolder}`);

    const logStream = fs.createWriteStream(path.join(testOutputFolder, "cpd.log"), { flags: "a" });
    const evalCallback: IAgentCallback = {
        progress: function (content: AgentCallbackProgressEvent): Promise<void> {
            progressEvents.push(content);
            if (
                content.kind === "message" &&
                content.message.role === "assistant" &&
                content.message.tool_calls &&
                content.message.tool_calls.length > 0
            ) {
                toolCallMessages.push(...content.message.tool_calls);
            }
            return Promise.resolve();
        },
        partialResult: function (content: AgentCallbackPartialResultEvent): Promise<void> {
            partialResultEvents.push(content);
            return Promise.resolve();
        },
        commentReply: function (reply: AgentCallbackCommentReplyEvent): Promise<void> {
            commentReplyEvents.push(reply);
            return Promise.resolve();
        },
        result: function (_result: AgentCallbackResultEvent): Promise<void> {
            // no-op, but an expected kind.
            return Promise.resolve();
        },
        error: function (error: AgentCallbackErrorEvent): Promise<void> {
            throw error;
        },
    };

    const callback = new CompoundCallback().addCallback(evalCallback).addCallback(
        new LoggerCallback((msg: string) => {
            logStream.write(msg + "\n");
            console.log(msg);
        }),
    );

    const preRecordedCompletions = test.preRecordedCompletions ? test.preRecordedCompletions(testFolder) : [];
    const client =
        preRecordedCompletions.length === 0
            ? undefined
            : getEvalClient(preRecordedCompletions, settings, logger, agentKind, options);
    const agent = getAgent(settings, logger, exec, agentKind, options, clients, callback, client);
    const actionName = test.actionName ?? "createPRAgent";
    const action: SweAgentAction<PRDetails> = (agent[actionName] as SweAgentAction<PRDetails>).bind(agent);

    console.log(`Running ${test.name}`);
    console.log(`Agent: ${agentKind}`);
    console.log(`Model: ${model}`);

    fs.writeFileSync(
        path.join(testOutputFolder, "test.json"),
        JSON.stringify({ ...test, agent: agentKind, model }, null, 2),
    );

    let branchName;
    let baseCommit;
    if (typeof test.repo === "string") {
        if (!test.commit) {
            throw new Error("No commit specified");
        }
        branchName = `fix-${Date.now()}`;
        baseCommit = test.commit;
        await gitClone(testFolder, test.repo);
        if (test.branch) {
            // If a branch is specified, check it out
            await gitCheckoutBranchAndReset(testFolder, test.branch, test.commit);
        }

        // still checkout to a new branch to avoid conflicts
        await gitCheckout(testFolder, test.commit, branchName);
    } else {
        for (const [filename, content] of Object.entries(test.repo)) {
            const filePath = path.join(testFolder, filename);
            const dir = path.dirname(filePath);
            fs.mkdirSync(dir, { recursive: true });

            if (typeof content === "object") {
                const mode = content.fileMode ?? 0o644;
                fs.writeFileSync(filePath, content.content);
                fs.chmodSync(filePath, mode);
            } else {
                fs.writeFileSync(filePath, content);
            }
        }
        await gitInit(testFolder);
        branchName = await getCurrentBranch(testFolder);
        baseCommit = await getCurrentCommit(testFolder);
    }

    const progressEvents: AgentCallbackProgressEvent[] = [];
    const partialResultEvents: AgentCallbackPartialResultEvent[] = [];
    const commentReplyEvents: AgentCallbackCommentReplyEvent[] = [];
    const toolCallMessages: ChatCompletionMessageToolCall[] = [];

    const repo = typeof test.repo === "string" ? test.repo : "testorg/testrepo";

    const outPromise = action(testFolder, test.problemDescription, repo, test.issueNumber ?? 0, branchName);

    const evalTimedoutResult = "EVAL_TIMED_OUT";
    let out: PRDetails | string;
    if (!!settings.timeoutMs && !!settings.startTimeMs) {
        let timeoutId;
        const timeoutPromise = new Promise<PRDetails | string>((resolve) => {
            timeoutId = setTimeout(() => {
                resolve(evalTimedoutResult);
            }, settings.timeoutMs);
        });
        out = await Promise.race([outPromise, timeoutPromise]);
        clearTimeout(timeoutId);
    } else {
        out = await outPromise;
    }

    logStream.end();

    const checks: EvaluationAsserts = new Map();
    const diff = await gitDiff(testFolder, baseCommit);

    // add to output
    fs.writeFileSync(
        path.join(testOutputFolder, "featureFlags.json"),
        JSON.stringify(settings.featureFlags ?? {}, null, 2),
    );
    fs.writeFileSync(path.join(testOutputFolder, "output.txt"), JSON.stringify(out, null, 2));
    fs.writeFileSync(path.join(testOutputFolder, "diff.txt"), diff);
    fs.writeFileSync(path.join(testOutputFolder, "progress.json"), JSON.stringify(progressEvents, null, 2));
    fs.writeFileSync(path.join(testOutputFolder, "partial-results.json"), JSON.stringify(partialResultEvents, null, 2));
    fs.writeFileSync(path.join(testOutputFolder, "comment-replies.json"), JSON.stringify(commentReplyEvents, null, 2));
    fs.writeFileSync(path.join(testOutputFolder, "tool-calls.json"), JSON.stringify(toolCallMessages, null, 2));

    if (typeof out === "object") {
        await test.assertions({
            context,
            repoPath: testFolder,
            diff,
            progressEvents,
            partialResultEvents,
            commentReplyEvents,
            toolCallMessages,
            result: out,
            checks,
        });

        if (test.runDefaultAssertions ?? true) {
            checkPartialResults(checks, actionName, partialResultEvents);
            checkResult(checks, actionName, out, test.issueNumber);
        }
        console.log(`[${test.name}]${test.testDescription ? ` - ${test.testDescription}` : ""}:`);
    } else {
        console.log(`[${test.name}]${test.testDescription ? ` - ${test.testDescription}` : ""}: ${out}`);
        check(checks, `result not received: ${out}`, false, out);
    }

    fs.writeFileSync(path.join(testOutputFolder, "checks.json"), JSON.stringify(Array.from(checks.values()), null, 2));
    reportChecks(checks);
}

export const check = (checks: EvaluationAsserts, name: string, assertion: boolean, actual?: unknown) => {
    checks.set(name, { name, assertion, actual });
};

const checkPartialResults = (
    checks: EvaluationAsserts,
    action: keyof SweAgent,
    partialResultEvents: AgentCallbackPartialResultEvent[],
) => {
    const expectedCount = action === "createPRAgent" ? 2 : 1;

    // The agent should have reported partial progress at least once
    check(
        checks,
        "partial results events >= " + expectedCount,
        partialResultEvents.length >= expectedCount,
        partialResultEvents.length,
    );

    // The agent should have used a markdown task list in its first progress report
    let markdownTaskList = true;
    for (const partialResult of partialResultEvents) {
        if (!partialResult.message.includes("- [")) {
            markdownTaskList = false;
        }
    }

    check(checks, "partial results events uses markdown task list", markdownTaskList);

    if (action === "createPRAgent") {
        if (partialResultEvents.length === 0) {
            check(checks, "partial results events should not be empty", false);
            return;
        }

        const { incompleteTaskCount: firstIncompleteTasksCount } = getTasks(partialResultEvents[0]);

        check(checks, "first partial result should have incomplete tasks", firstIncompleteTasksCount > 0);
        // The first partial result may include completed tasks related to initial planning or exploration.
        // For example, tasks like "Analyze repository structure" or "Identify key files" might be marked as completed.

        const { incompleteTaskCount: lastIncompleteTasksCount, completedTaskCount: lastCompletedTasksCount } = getTasks(
            partialResultEvents[partialResultEvents.length - 1],
        );
        check(checks, "last partial result should not have incomplete tasks", lastIncompleteTasksCount === 0);
        check(checks, "last partial result should have completed tasks", lastCompletedTasksCount > 0);
    }
};

function checkResult(checks: EvaluationAsserts, action: keyof SweAgent, out: PRDetails, issueNumber: number = 0) {
    if (action === "createPRAgent") {
        const prDetails = out as PRDetails;
        const prDescription = prDetails.description;
        check(checks, "PR has title", prDetails.title.length > 0);
        check(checks, "PR description has body", prDescription.length > 0);
        if (issueNumber !== 0) {
            check(checks, `PR description has Fixes #${issueNumber}`, prDescription.endsWith(`Fixes #${issueNumber}.`));
        }
    }
}

export function getTasks(from: AgentCallbackPartialResultEvent): {
    incompleteTaskCount: number;
    completedTaskCount: number;
} {
    // look for all - [ ] tasks
    const incompleteTasks = from.message.match(/- \[ \].*/g);
    const completedTasks = from.message.match(/- \[x\].*/g);

    return {
        incompleteTaskCount: incompleteTasks?.length ?? 0,
        completedTaskCount: completedTasks?.length ?? 0,
    };
}

function getEvalClient(
    preRecordedCompletions: ChatCompletion[],
    settings: RuntimeSettings,
    logger: ConsoleLogger,
    agent?: SweAgentKind,
    clientOptions?: ClientOptions,
): Client {
    switch (agent) {
        case "sweagent-aip":
            return getClient(settings, logger, agent, clientOptions);
        case "sweagent-anthropic":
            return getClient(settings, logger, agent, clientOptions);
        case "sweagent-capi":
            return new PlaybackCAPIAIClient(preRecordedCompletions, settings, logger, clientOptions);
        case "sweagent-openai":
            return getClient(settings, logger, agent, clientOptions);
        default:
            // Default to CAPI with Sonnet
            return new PlaybackCAPIAIClient(preRecordedCompletions, settings, logger, clientOptions);
    }
}
