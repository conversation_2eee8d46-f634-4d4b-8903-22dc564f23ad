/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

/**
 * The tests in this file are non-deterministic evals to constrain the behaviour of the agent.
 * They may not all pass all the time, but should pass "most" of the time.
 * They should be skipped in PR/main CI, but will be run in nightlies.
 */
import { readFileSync } from "fs";
import { BASH_TOOL_NAME, REPLY_TO_COMMENT_TOOL_NAME, STOP_BASH_TOOL_NAME } from "../../src/tools";
import { DEFAULT_TIMEOUT_MS, TestCase, check, runTest } from "./eval";

// --isolated so that we can run multiple instances without an error.
const PLAYWRIGHT_MCP_SERVERS = {
    playwright: {
        command: "npx",
        args: ["@playwright/mcp@latest", "--isolated"],
    },
};

const testCases: TestCase[] = [
    {
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: `Uses scaffolding tools to create a new React application`,
        repo: {},
        commit: null,
        mcpServers: PLAYWRIGHT_MCP_SERVERS,
        problemDescription: readFileSync("test/evals/problem_statements/webDevE2E-react-app.md", "utf-8"),
        configureSettings: async (settings) => {
            settings.featureFlags.copilot_swe_agent_vision = true;
            settings.tools = settings.tools || {};
            settings.tools.bash = settings.tools.bash || {};
            settings.tools.bash.defaultTimeout = 5;
        },
        assertions: async ({ checks, toolCallMessages }) => {
            const createReactAppCommandMessages = toolCallMessages.filter(
                (messsage) =>
                    messsage.function.name === BASH_TOOL_NAME &&
                    messsage.function.arguments.includes("npx create-react-app"),
            );
            const createReactAppCommandParameters =
                createReactAppCommandMessages?.length > 0
                    ? JSON.parse(createReactAppCommandMessages[0].function.arguments)
                    : [];

            const stoppedCreateReactApp = toolCallMessages.some(
                (message) =>
                    message.function.name === STOP_BASH_TOOL_NAME &&
                    message.function.arguments.includes(createReactAppCommandParameters.sessionId),
            );

            const startedDevServer = toolCallMessages.some(
                (message) =>
                    message.function.name === BASH_TOOL_NAME && message.function.arguments.includes("npm start"),
            );

            const tookAScreenshot = toolCallMessages.some(
                (message) =>
                    message.function.name === "playwright-browser_take_screenshot" &&
                    (message.function.arguments.includes(".png") || message.function.arguments.includes(".jpg")),
            );

            // For the test to succeed we have to have started create-react-app just once and not stopped it.
            check(
                checks,
                "create-react-app should be used to scaffold the application",
                createReactAppCommandMessages.length === 1,
            );
            check(checks, "did not abandon the create-react-app approach", !stoppedCreateReactApp);
            check(checks, "started the dev server", startedDevServer);
            check(checks, "took a screenshot of the running application", tookAScreenshot);
        },
    },
    {
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "Uses playwright to resolve a bug based on repro steps",
        repo: "ghcpd/copilot-chat",
        commit: "031328e04ca2a758d9875b7019c5b332ab48c0b3",
        mcpServers: PLAYWRIGHT_MCP_SERVERS,
        problemDescription: readFileSync("test/evals/problem_statements/webDevE2E-auth-bug.md", "utf-8"),
        configureSettings: async (settings) => {
            settings.featureFlags.copilot_swe_agent_vision = true;
            settings.tools = settings.tools || {};
            settings.tools.bash = settings.tools.bash || {};
            settings.tools.bash.defaultTimeout = 5;
        },
        assertions: async ({ checks, toolCallMessages }) => {
            const navigatedToPage = toolCallMessages.some(
                (message) => message.function.name === "playwright-browser_navigate",
            );

            const enteredPassword = toolCallMessages.some(
                (message) =>
                    message.function.name === "playwright-browser_type" &&
                    message.function.arguments.includes("Password"),
            );

            const clickedLoginButton = toolCallMessages.some(
                (message) =>
                    message.function.name === "playwright-browser_click" &&
                    message.function.arguments.includes("Login"),
            );

            const tookAScreenshot = toolCallMessages.some(
                (message) =>
                    message.function.name === "playwright-browser_take_screenshot" &&
                    (message.function.arguments.includes(".png") || message.function.arguments.includes(".jpg")),
            );

            // Ensure that the model used Playwright MCP to exercise the scenario when debugging the issue.
            check(checks, "Navigated to a web page", navigatedToPage);
            check(checks, "Entered password in the password textbox", enteredPassword);
            check(checks, "Clicked the login button", clickedLoginButton);
            check(checks, "Took a screenshot of the running application", tookAScreenshot);
        },
    },
    {
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "Uses playwright to take screenshots in comment reply",
        repo: "ghcpd/copilot-chat",

        // Commit based on https://github.com/ghcpd/copilot-chat/pull/89 to simulate a comment reply.
        commit: "eef5a57af98b9ae9d4c9c98bf504323baeed43d3",
        mcpServers: PLAYWRIGHT_MCP_SERVERS,
        problemDescription: readFileSync("test/evals/problem_statements/webDevE2E-comment-reply.md", "utf-8"),
        actionName: "respondToPRCommentAgent",
        configureSettings: async (settings) => {
            settings.featureFlags.copilot_swe_agent_vision = true;
            settings.tools = settings.tools || {};
            settings.tools.bash = settings.tools.bash || {};
            settings.tools.bash.defaultTimeout = 5;
        },
        assertions: async ({ checks, toolCallMessages }) => {
            const tookAScreenshot = toolCallMessages.some(
                (message) =>
                    message.function.name === "playwright-mcp-server-browser_take_screenshot" &&
                    (message.function.arguments.includes(".png") || message.function.arguments.includes(".jpg")),
            );

            const replyToComment = toolCallMessages.some(
                (message) =>
                    message.function.name === REPLY_TO_COMMENT_TOOL_NAME &&
                    (message.function.arguments.includes(".png") || message.function.arguments.includes(".jpg")),
            );

            // Ensure that the model used Playwright MCP to exercise the scenario when debugging the issue.
            check(checks, "Took a screenshot of the running application", tookAScreenshot);
            check(checks, "Replied to the comment with a screenshot", replyToComment);
        },
    },
    {
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "Does not include screenshots in comment reply when there are no UI changes",
        repo: "ghcpd/copilot-chat",

        // Commit based on https://github.com/ghcpd/copilot-chat/pull/89 to simulate a comment reply.
        commit: "eef5a57af98b9ae9d4c9c98bf504323baeed43d3",
        mcpServers: PLAYWRIGHT_MCP_SERVERS,
        problemDescription: readFileSync("test/evals/problem_statements/webDevE2E-comment-reply-non-ui.md", "utf-8"),
        actionName: "respondToPRCommentAgent",
        configureSettings: async (settings) => {
            settings.featureFlags.copilot_swe_agent_vision = true;
            settings.tools = settings.tools || {};
            settings.tools.bash = settings.tools.bash || {};
            settings.tools.bash.defaultTimeout = 5;
        },
        assertions: async ({ checks, toolCallMessages }) => {
            const replyToCommentWithoutScreenshot = toolCallMessages.some(
                (message) =>
                    message.function.name === REPLY_TO_COMMENT_TOOL_NAME &&
                    !message.function.arguments.includes(".png") &&
                    !message.function.arguments.includes(".jpg"),
            );

            // Ensure that no screenshot was posted since the changes were not UI related.
            check(checks, "Replied to the comment without a screenshot", replyToCommentWithoutScreenshot);
        },
    },
];

describe("web dev E2E evals", () => {
    test.concurrent.for(testCases)("$name", { timeout: DEFAULT_TIMEOUT_MS }, runTest);
});
