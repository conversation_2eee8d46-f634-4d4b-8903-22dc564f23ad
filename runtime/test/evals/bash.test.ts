/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

/**
 * The tests in this file are non-deterministic evals to constrain the behaviour of the agent.
 * They may not all pass all the time, but should pass "most" of the time.
 * They should be skipped in PR/main CI, but will be run in nightlies.
 */
import { readFileSync } from "fs";
import { BASH_TOOL_NAME, WRITE_BASH_TOOL_NAME } from "../../src/tools";
import { DEFAULT_TIMEOUT_MS, TestCase, check, runTest } from "./eval";

const repo = {
    "test.sh": {
        fileMode: 0o755,
        content: `#!/bin/bash
echo "Running a long command, this may take up to 15 seconds"
sleep 10
echo "Finished, everything is ok"`,
    },
};

// Repo is initially empty.
const ticTacToeRepo = {};

const testCases: TestCase[] = [
    {
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: `Uses timeout parameter of ${BASH_TOOL_NAME} for long running commands`,
        repo: repo,
        commit: null,
        problemDescription: readFileSync("test/evals/problem_statements/bash-timeout.md", "utf-8"),
        configureSettings: async (settings) => {
            settings.tools = settings.tools || {};
            settings.tools.bash = settings.tools.bash || {};
            settings.tools.bash.defaultTimeout = 5;
        },
        assertions: async ({ checks, toolCallMessages }) => {
            let usedTimeout = false;
            let usedAsNonAsync = false;
            let args = null;
            for (const toolCall of toolCallMessages) {
                if (toolCall.function.name == BASH_TOOL_NAME) {
                    try {
                        args = JSON.parse(toolCall.function.arguments);
                        if (
                            typeof args === "object" &&
                            typeof args.command === "string" &&
                            (args.command as string).indexOf("test.sh") > 0 &&
                            typeof args.timeout === "number" &&
                            args.timeout > 10
                        ) {
                            usedTimeout = true;
                            break;
                        }
                        usedAsNonAsync = args?.async === false || args?.async === undefined;
                    } catch {
                        // ignore JSON parse errors
                    }
                }
            }

            check(
                checks,
                "bash tool should use timeout parameter greater than 10 when running test.sh",
                usedTimeout,
                args,
            );
            check(checks, "used non-async long running command", usedAsNonAsync, args);
        },
    },
    {
        // This test case is designed to check if the agent can use interactive bash features, which
        // can be used to send keyboard input to a running process for avoiding getting stuck behind
        // interactive prompts and for manually validating changes to console apps that aren't covered
        // by tests.
        //
        // We specifically instruct the agent to make the AI player respond at random so it's impossible
        // for the LLM to know the moves in advance so it's forced to use the interactive features instead
        // piping a fixed script of moves into standard input.
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: `Uses interactive bash features for scenarios that require user input`,
        repo: ticTacToeRepo,
        commit: null,
        problemDescription: readFileSync("test/evals/problem_statements/bash-tictactoe.md", "utf-8"),
        configureSettings: async (settings) => {
            settings.tools = settings.tools || {};
            settings.tools.bash = settings.tools.bash || {};
            settings.tools.bash.defaultTimeout = 5;
        },
        assertions: async ({ checks, toolCallMessages }) => {
            let usedWriteBash = false;
            let validPositionParametersCount = 0;
            let positionParameters = "";

            for (const toolCall of toolCallMessages) {
                if (toolCall.function.name == WRITE_BASH_TOOL_NAME) {
                    usedWriteBash = true;

                    // Format is [line],[column]
                    const parameters = JSON.parse(toolCall.function.arguments)?.input.split(",");

                    // Ensure each position is a valid integer.
                    validPositionParametersCount +=
                        Number.isInteger(parseInt(parameters[0])) && Number.isInteger(parseInt(parameters[1])) ? 1 : 0;

                    // Collect the position parameters for later validation.
                    positionParameters += `(${parameters[0]}, ${parameters[1]})\n`;
                }
            }

            check(checks, "write_bash tool should be used to send keyboard input when required.", usedWriteBash);
            check(
                checks,
                "write_bash tool sends valid input",
                validPositionParametersCount >= 3,
                `validPositionParametersCount: ${validPositionParametersCount},\nposition parameters:\n${positionParameters}`,
            );
        },
    },
    {
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: `Uses async parameter of ${BASH_TOOL_NAME} to run an application in the background`,
        repo: {},
        commit: null,
        problemDescription: readFileSync("test/evals/problem_statements/bash-identityservice.md", "utf-8"),
        configureSettings: async (settings) => {
            settings.tools = settings.tools || {};
            settings.tools.bash = settings.tools.bash || {};
            settings.tools.bash.defaultTimeout = 5;
        },
        assertions: async ({ checks, toolCallMessages }) => {
            let usedAsyncBash = false;
            let usedCurl = false;
            let hasSessionId = false;
            for (const toolCall of toolCallMessages) {
                if (toolCall.function.name === BASH_TOOL_NAME) {
                    const parameters = JSON.parse(toolCall.function.arguments);

                    usedAsyncBash ||= parameters?.async ?? false;
                    usedCurl ||= parameters?.command?.includes("curl") ?? false;
                    hasSessionId = parameters?.sessionId !== undefined && parameters?.sessionId !== null;
                }
            }

            check(checks, "a command should be run in the background via the 'async' parameter", usedAsyncBash);
            check(checks, "curl should be used to exercise the server", usedCurl);
            check(checks, "sessionId should be set to allow the agent to read later", hasSessionId);
        },
    },
    {
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: `Disables pager when running Git commands via ${BASH_TOOL_NAME}`,
        repo: "ghcpd/react-router-test-app",
        commit: "7575e5e121e285cc44d51cfd6a6c0f1f0aa06185",
        problemDescription: readFileSync("test/evals/problem_statements/bash-git-disablespager.md", "utf-8"),
        configureSettings: async (settings) => {
            settings.tools = settings.tools || {};
            settings.tools.bash = settings.tools.bash || {};
            settings.tools.bash.defaultTimeout = 5;
        },
        assertions: async ({ checks, toolCallMessages }) => {
            const ranGitWithNoPager = toolCallMessages.some(
                (toolCallMessage) =>
                    toolCallMessage.function.name === BASH_TOOL_NAME &&
                    toolCallMessage.function.arguments.includes("git") &&
                    toolCallMessage.function.arguments.includes("--no-pager"),
            );

            // Ensures that the pager is disabled when running Git commands so that we don't take a multi-minute hit
            // waiting for the command to 'timeout' before giving the model a chance to offer input.
            check(checks, "the 'Git' command should be run with the pager disabled", ranGitWithNoPager);
        },
    },
];

describe("bash evals", () => {
    test.concurrent.for(testCases)("$name", { timeout: DEFAULT_TIMEOUT_MS }, runTest);
});
