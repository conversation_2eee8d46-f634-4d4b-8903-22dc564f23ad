/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

/**
 * The tests in this file are client-model-e2e evals to ensure that model works as expected in a very simple text file scenario.
 * They should pass all the time.
 */

import fs from "fs";
import path from "path";
import { PRDetails, SweAgentKind } from "../../src/agents/sweagent";
import { ClientOptions } from "../../src/model/client";
import { DEFAULT_TIMEOUT_MS, TestCase, check, runTest } from "./eval";

const clientModelMatrix: {
    action: SweAgentKind;
    model: string | undefined;
    skipEnvVar: string | undefined;
    options?: ClientOptions;
}[] = [
    {
        action: "sweagent-capi",
        model: undefined,
        skipEnvVar: "DISABLE_CAPI_TESTS",
    },
    {
        action: "sweagent-capi",
        model: "oswe",
        skipEnvVar: "DISABLE_CAPI_TESTS",
    },
    { action: "sweagent-aip", model: undefined, skipEnvVar: "TEST_CI" },
    {
        action: "sweagent-anthropic",
        model: "claude-3-7-sonnet-20250219",
        skipEnvVar: "TEST_CI",
        options: { thinkingMode: true },
    },
    {
        action: "sweagent-openai",
        model: process.env.AZURE_OPENAI_MODEL,
        skipEnvVar: "TEST_CI",
        options: { thinkingMode: true },
    },
];

const createFixTest = (
    agent: SweAgentKind,
    model: string | undefined,
    skipEnvVar: string | undefined,
    options?: ClientOptions,
): TestCase => ({
    skipEnvVar,
    name: `create [${agent.replace("sweagent-", "")}:${model}]`,
    testDescription: `createPRAgent with agent ${agent} and model ${model}`,
    agent,
    model,
    clientOptions: options,
    issueNumber: 42,
    actionName: "createPRAgent",
    repo: {},
    commit: null,
    problemDescription: `----
*This section details on the original issue you should resolve*

<issue_title>
Add a README.md file with header '# Hello World'
</issue_title>

<issue_description>

</issue_description>

## Comments on the Issue (you are @copilot in this section)

<comments>

</comments>
`,
    assertions: async ({ repoPath, result, checks }) => {
        // Verify the results in the file system
        console.log(`PR Commit: ${result.title}\n\n${result.description}`);
        const readMeFile = path.join(repoPath, "README.md");

        const readMeFileExists = fs.existsSync(readMeFile);
        if (readMeFileExists) {
            const readMeContent = fs.readFileSync(readMeFile, "utf-8");
            check(
                checks,
                `README.md content check to be "# Hello World"`,
                readMeContent.trim() === "# Hello World",
                readMeContent.trim(),
            );
        } else {
            check(checks, 'README.md content is "# Hello World"', false, "file does not exist");
        }

        const prDetails = result as PRDetails;
        const prDescription = prDetails.description;
        check(checks, "PR has title", prDetails.title.length > 0);
        check(checks, "PR description has body", prDescription.length > 0);

        check(checks, 'title contains "Hello World"', result.title.includes("Hello World"));
    },
    runDefaultAssertions: false,
});

const createFixCommentTest = (
    agent: SweAgentKind,
    model: string | undefined,
    skipEnvVar: string | undefined,
    options?: ClientOptions,
): TestCase => ({
    skipEnvVar,
    name: `respond [${agent.replace("sweagent-", "")}:${model}]`,
    testDescription: `respondToPRCommentAgent with agent ${agent} and model ${model}`,
    agent,
    model,
    clientOptions: options,
    issueNumber: 42,
    actionName: "respondToPRCommentAgent",
    repo: {
        "README.md": "# Hello World",
    },
    commit: null,
    problemDescription: `----
*This section includes details on the pull request with code changes you have already submitted to fix the problem above.*

<pr_title>
Add README.md with 'Hello World' header
</pr_title>

<pr_description>

This PR adds a README.md file to the repository with the header '# Hello World'.

The README.md file provides a simple entry point for users visiting the repository, with a clear title that follows standard markdown formatting.

</pr_description>

## Comments on the PR (you are @copilot in this section)

<comments>

<pr_comments>
<comment_new>
<comment_id>12345</comment_id>
<author>@me</author>
Change the README.md file header to be "# Hello Universe" instead.
</comment_new>
</pr_comments>

</comments>

----
The last **1** git commits in this branch are the changes you have made so far. Use those as your change commit history.

`,
    assertions: async ({ result, repoPath, commentReplyEvents, checks }) => {
        // Verify the results in the file system
        console.log("commentReplyEvents:");
        console.log(JSON.stringify(commentReplyEvents), null, 2);
        console.log(`PR Commit: ${result.title}\n\n${result.description}`);

        const readMeFile = path.join(repoPath, "README.md");

        const fileExists = fs.existsSync(readMeFile);

        if (fileExists) {
            const readMeContent = fs.readFileSync(readMeFile, "utf-8");
            check(
                checks,
                `README.md content check to be "# Hello Universe"`,
                readMeContent.trim() === "# Hello Universe",
                readMeContent.trim(),
            );
        } else {
            check(checks, 'README.md content is "# Hello Universe"', false, "file does not exist");
        }

        check(checks, "commentReplyEvents.length == 1", commentReplyEvents.length == 1);
    },
    runDefaultAssertions: false,
});

const testCases = (
    agent: SweAgentKind,
    model: string | undefined,
    skipEnvVar: string | undefined,
    options?: ClientOptions,
): TestCase[] => [
    createFixTest(agent, model, skipEnvVar, options),
    createFixCommentTest(agent, model, skipEnvVar, options),
];

describe("client-e2e tests", () => {
    test.concurrent.for([...clientModelMatrix.flatMap((m) => testCases(m.action, m.model, m.skipEnvVar, m.options))])(
        "$name",
        { timeout: DEFAULT_TIMEOUT_MS },
        runTest,
    );
});
