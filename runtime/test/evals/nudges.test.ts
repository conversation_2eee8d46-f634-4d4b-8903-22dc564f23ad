/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

/**
 * The tests in this file are non-deterministic evals to constrain the behaviour of the agent.
 * They may not all pass all the time, but should pass "most" of the time.
 * They should be skipped in PR/main CI, but will be run in nightlies.
 */
import { GetCompletionWithToolsTurnTelemetryEvent } from "../../src/agents/telemetry";
import { isToolMessageEvent, isUserMessageEvent, UserMessageEvent } from "../../src/model/event";
import { check, DEFAULT_TIMEOUT_MS, getTasks, runTest, TestCase } from "./eval";

const testCases: TestCase[] = [
    {
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "Wraps up work when prompted to do so",
        repo: {
            "README.md": "Tic-Tac-Toe App",
        },
        commit: null,
        runDefaultAssertions: false,
        startTimeAdjustmentMs: 1000 * 60 * 49,
        problemDescription: `----
        *This section details on the original issue you should resolve*

        <issue_title>
        Tic-Tac-Toe Web & CLI App
        </issue_title>

        <issue_description>
        Implement a combination web & CLI app that lets users play tic-tac-toe.

        Web App Requirements:
        - Games should be able to be created, joined, or spectated from the home page.
        - Once a game is created, the initial player should have access to a join and spectate code on the game page.
        - The second player should be able to join the game using the join code.
        - Other people should be able to spectate the game using the spectate code.
        - Once the game is over, the spectate code should allow someone to play back the game turn by turn.
        - There should be a leaderboard of who wins more often, Xs, Os, or draws.
        - There should be a page with a whimsical and fantastical, but also fictional (since the real history is lost to time), history of tic-tac-toe.
        - There should be an admin page with a:
           - List of all games, their status, and how many spectators have watched the games.
           - A button to reset the leaderboard.
           - A button to end all active games.
        - All pages should have a dark and light mode.

        CLI App Requirements:
        - Sharing code from packages which make up the backend game logic, also implement a CLI app that lets users play tic-tac-toe.
        - Users should take turn on the command line, and the CLI should display the current state of the board after each turn.
        - Output and vibe of the CLI app should be friendly and cheeky.

        Technical requirements:
        - Create the web app backend in go, sharing code with the CLI app.
        - Create the CLI app in go, sharing code with the web app backend.
        - Create the web app frontend in React (using TypeScript).
        - Tests are only required for the go code.
        - Ensure tests are long and arduous to run. The longer the test suite, the better the go code is tested.
        - Fill out the README.md as you complete your work. The README.md should include detailed instructions on how to:
           - Run the web app backend and frontend
           - Play a game via the CLI app
           - Run the automated tests
        - Add full and complete markdown documentation for the API endpoints.
        - Add a NoHackersPlz.md file which begs people to not hack the site. Make this file very long, go through multiple iterations of it, and make it very detailed.
        </issue_description>

        ## Comments on the Issue (you are @copilot in this section)

        <comments>

        </comments>
        `,
        configureSettings: async (settings) => {
            settings.timeoutMs = 1000 * 60 * 60;
        },
        assertions: async ({ partialResultEvents, progressEvents, checks }) => {
            // Ensure at least one task is incomplete.
            const tasks = getTasks(partialResultEvents[partialResultEvents.length - 1]);
            check(checks, "There are incomplete tasks", tasks.incompleteTaskCount > 0);

            // Ensure at least one JIT instruction was used, but no more than the total we have.
            const jitInstructionsAddedCount = progressEvents
                .filter(
                    (e): e is GetCompletionWithToolsTurnTelemetryEvent =>
                        e.kind === "telemetry" && e.telemetry.event === "get_completion_with_tools_turn",
                )
                .map((e) => e.telemetry.metrics.jitInstructionsAdded || 0)
                .reduce((acc, curr) => acc + (curr || 0), 0);
            check(checks, "At least one JIT instruction was used", jitInstructionsAddedCount > 0);

            const jitInstructionsProgressEvents = progressEvents
                .filter((e) => e.kind === "message" && isUserMessageEvent(e))
                .filter((e) => e.source === "jit-instruction");

            // Ensure there were a matching number of JIT instruction progress events emitted
            const jitInstructionsProgressEventsCount = jitInstructionsProgressEvents.length;
            check(
                checks,
                "The number of JIT instruction progress events matches the number of JIT instructions added",
                jitInstructionsProgressEventsCount === jitInstructionsAddedCount,
            );

            // Ensure there are a limited number of tool message events after the final JIT instruction
            const lastJitInstructionEvent = jitInstructionsProgressEvents.at(-1);
            if (lastJitInstructionEvent) {
                const lastJitInstructionIndex = progressEvents.lastIndexOf(lastJitInstructionEvent);
                const toolMessagesAfterLastJit = progressEvents
                    .slice(lastJitInstructionIndex + 1)
                    .filter((e) => e.kind === "message" && isToolMessageEvent(e)).length;
                check(
                    checks,
                    "There are no more than 3 tool message events after the final JIT instruction",
                    toolMessagesAfterLastJit <= 3,
                );
            }
        },
    },
    {
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "Agent Uses Remind Where To Do Work JIT Instruction",
        repo: {
            "README.md": "Sentiment Analysis CLI",
        },
        commit: null,
        startTimeAdjustmentMs: 1000 * 60 * 31,
        problemDescription: `----
        *This section details on the original issue you should resolve*

        <issue_title>
        Sentiment Analysis CLI
        </issue_title>

        <issue_description>
        Implement a command-line interface (CLI) for sentiment analysis.

        This CLI should take in a text input and return the sentiment of the text (positive, negative, or neutral). Implement the sentiment analysis using a
        heuristic approach, such as counting positive and negative words.

        <comments>

        </comments>
        `,
        configureSettings: async (settings) => {
            settings.timeoutMs = 1000 * 60 * 60;
        },
        assertions: async ({ progressEvents, checks }) => {
            // Ensure at only one JIT instruction was used (the agent shouldn't get to the point of needing its timeout JIT instruction).
            const jitInstructionsAddedCount = progressEvents
                .filter(
                    (e): e is GetCompletionWithToolsTurnTelemetryEvent =>
                        e.kind === "telemetry" && e.telemetry.event === "get_completion_with_tools_turn",
                )
                .map((e) => e.telemetry.metrics.jitInstructionsAdded || 0)
                .reduce((acc, curr) => acc + (curr || 0), 0);
            check(checks, "One JIT instruction was used", jitInstructionsAddedCount === 1);

            // Ensure there were a matching number of JIT instruction progress events emitted
            const jitInstructionsProgressCount = progressEvents
                .filter((e): e is UserMessageEvent => e.kind === "message" && e.message.role === "user")
                .filter((e) => e.source === "jit-instruction").length;
            check(
                checks,
                "The number of JIT instruction progress events matches the number of JIT instructions added",
                jitInstructionsProgressCount === jitInstructionsAddedCount,
            );
        },
    },
];

describe("timeout evals", () => {
    test.concurrent.for(testCases)("$name", { timeout: DEFAULT_TIMEOUT_MS }, runTest);
});
