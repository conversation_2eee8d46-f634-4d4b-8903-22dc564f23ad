/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

/**
 * The tests in this file are non-deterministic evals to constrain the behaviour of the agent.
 * They may not all pass all the time, but should pass "most" of the time.
 * They should be skipped in PR/main CI, but will be run in nightlies.
 */

import { readFileSync } from "fs";
import path from "path";
import { BASH_TOOL_NAME, STOP_BASH_TOOL_NAME, WRITE_BASH_TOOL_NAME } from "../../src/tools";
import { check, DEFAULT_TIMEOUT_MS, gitDiff, runTest, searchDirectory, TestCase } from "./eval";

// to generate the problem statement, use the following command:
// `go run cmd/psg/main.go -nwo=<repo> -number=<issue/pr_number>` e.g. `go run cmd/psg/main.go -nwo=github/sweagentd -number=1308`
// NOTE: below problem statements have been edited slightly for the test scenario being covered based on the repo/issue
//
// NOTE: CI has been configured with a token that allows the tests to clone public repositories, as well as any private repositories
// in the `ghcpd` organization. In addition, the tests have access to the following repositories in the github organization:
//
// - github/accessibility-governance
// - github/availability-findings
// - github/governor
// - github/wiz-therapy
//
// Via a fine grained access token which grant read only access to just the above repositories. If you want to add additional tests based
// on other private repositories in the GitHub organization, you will need to generate a new fine grained access token that grants access
// to the above repositories and whatever other repositories you want to test against, and then work with a repository admin to update the
// `EVAL_TEST_GITHUB_TOKEN_GITHUB` secret in actions.
//
// When run locally, the tests will use `git clone` to clone the repository, using whatever credentials you have configured.
const testCases: TestCase[] = [
    {
        // Long running test, only run in nightly test CI
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "cdias dogfood feedback 1",
        testDescription: "no tests added if there is no test setup in repo",
        repo: "ghcpd/vscode-settingsview",
        // TODO: also add test after adding copilot-instructions.md in '8a7bfab99d8ae5da2ca46db46d0283ccc993b420'?
        commit: "5f80eacc56cb2c0f3feb56887cc77f2208e16dea",
        problemDescription: readFileSync("test/evals/problem_statements/cdias-dogfood-feedback-1.md", "utf-8"),
        assertions: async ({ toolCallMessages, diff, checks }) => {
            // Expect to build
            const lintBuildOrTestCommands = toolCallMessages.filter((e) => {
                const args = e.function.arguments;
                return (
                    args.includes("npm run lint") || args.includes("npm run compile") || args.includes("npm run test")
                );
            });
            check(checks, "lintBuildOrTestCommands.length > 0", lintBuildOrTestCommands.length > 0);

            // Expect to *not* add tests because there are no tests in the repo
            check(checks, "diff does not include .test.ts", !diff.includes(".test.ts"), diff);
        },
    },
    {
        // Long running test, only run in nightly test CI
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "helen dogfood feedback 1",
        testDescription: "undo changes and reply to all in-line comments",
        repo: "github/accessibility-governance",
        commit: "c4646745f9349a22af7d729bbfa6ed8a8b0641a8",
        issueNumber: 2034,
        // the tests for this repo require tokens and auth, so for the test, we'll just disable them
        problemDescription:
            readFileSync("test/evals/problem_statements/helen-dogfood-feedback-1.md", "utf-8") +
            "\n\nDo not run any tests after addressing feedback. I've already run them.",
        actionName: "respondToPRCommentAgent",
        assertions: async ({ repoPath, partialResultEvents, commentReplyEvents, toolCallMessages, checks }) => {
            // files to undo and count
            const undoMap = new Map<string, number>();
            undoMap.set("bin/get-date.sh", 0);
            undoMap.set("data/services.csv", 0);
            undoMap.set("data/auditSchedule.json", 0);

            const undoActionTaken = toolCallMessages.filter((e) => {
                const args = e.function.arguments;
                const checkout = args.includes("git checkout ");

                // only undo these 3 files
                if (checkout) {
                    let undoVerified = false;
                    for (const [file, count] of undoMap) {
                        if (args.includes(file)) {
                            undoMap.set(file, count + 1);
                            undoVerified = true;
                        }
                    }

                    // only return true if it's one of the 3 files
                    return undoVerified;
                }

                return false;
            });

            check(checks, "undoActionTaken > 1", undoActionTaken.length > 0);
            check(checks, "undo bin/get-date.sh", undoMap.get("bin/get-date.sh")! > 0);
            check(checks, "undo data/services.csv", undoMap.get("data/services.csv")! > 0);
            check(checks, "undo data/auditSchedule.json", undoMap.get("data/auditSchedule.json")! > 0);

            // check diff for the files that were undone
            const diffGetDate = await gitDiff(repoPath, "ea50654", "bin/get-date.sh");
            const diffServices = await gitDiff(repoPath, "ea50654", "data/services.csv");
            const diffAuditSchedule = await gitDiff(repoPath, "ea50654", "data/auditSchedule.json");

            // check if the diff is empty
            check(checks, "diff bin/get-date.sh is empty", diffGetDate === "");
            check(checks, "diff data/services.csv is empty", diffServices === "");
            check(checks, "diff data/auditSchedule.json is empty", diffAuditSchedule === "");

            // reply to all 3 in-line comments
            check(checks, "commentReplyEvents.length == 3", commentReplyEvents.length == 3, commentReplyEvents.length);

            // Expect the agent to report partial progress at least once to commit the changes
            check(checks, "report_progress count > 0", partialResultEvents.length > 0);
        },
    },
    {
        // Long running test, only run in nightly test CI
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "helen dogfood feedback 2",
        testDescription:
            "undo changes, respond to pr comment in addition to in-line comment, but not old comments even if they are related to changes",
        repo: "github/accessibility-governance",
        commit: "d40bbc370a3b730c1a8711d55a82c4416ac2d5af",
        issueNumber: 2034,
        // the tests for this repo require tokens and auth, so for the test, we'll just disable them
        problemDescription:
            readFileSync("test/evals/problem_statements/helen-dogfood-feedback-2.md", "utf-8") +
            "\n\nDo not run any tests after addressing feedback. I've already run them.",
        actionName: "respondToPRCommentAgent",
        assertions: async ({ repoPath, partialResultEvents, commentReplyEvents, checks }) => {
            // check diff for the files that were undone
            const diffGetDate = await gitDiff(repoPath, "ea50654", "bin/get-date.sh");
            const diffServices = await gitDiff(repoPath, "ea50654", "data/services.csv");
            const diffAuditSchedule = await gitDiff(repoPath, "ea50654", "data/auditSchedule.json");

            // check if the diff is empty
            check(checks, "diff bin/get-date.sh is empty", diffGetDate === "");
            check(checks, "diff data/services.csv is empty", diffServices === "");
            check(checks, "diff data/auditSchedule.json is empty", diffAuditSchedule === "");

            // llm tends to recreate the file to revert it sometimes and diff shows the creation as diff
            try {
                const thirdPartiesFile = readFileSync(path.join(repoPath, "third-parties.json"), "utf-8");
                check(checks, "third-parties.json is reverted", thirdPartiesFile.trim() == "{}");
            } catch {
                check(checks, "third-parties.json is reverted", false, "file does not exist");
            }

            check(checks, "commentReplyEvents.length == 2", commentReplyEvents.length == 2, commentReplyEvents.length);

            // reply to 1 inline comment
            check(
                checks,
                "third-parties.json uses comment_id=2025344781",
                commentReplyEvents.find((reply) => reply.comment_id === 2025344781) !== undefined,
            );
            check(
                checks,
                "response to pr comment uses comment_id=2025326126",
                commentReplyEvents.find((reply) => reply.comment_id === 2025326126) !== undefined,
            );

            // Expect the agent to report partial progress at least once to commit the changes
            check(checks, "report_progress count > 0", partialResultEvents.length > 0);
        },
    },
    {
        // Long running test, only run in nightly test CI
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "tspascoal dogfood feedback 1",
        testDescription: "node_modules is gitignored and yeoman is non-interactively used",
        repo: {},
        commit: null,
        problemDescription: readFileSync("test/evals/problem_statements/tspascoal-dogfood-feedback-1.md", "utf-8"),
        assertions: async ({ repoPath, toolCallMessages, checks }) => {
            const hasGitIgnore = searchDirectory(repoPath, (filePath) => {
                // Check if this is the .gitignore file
                if (path.basename(filePath) === ".gitignore") {
                    // Check if it contains a line with 'node_modules'
                    const content = readFileSync(filePath, "utf-8");
                    return content.split("\n").some((line) => line.trim()?.includes("node_modules"));
                }

                return null;
            });

            const calledYo = toolCallMessages.some(
                (message) => message.function.name === BASH_TOOL_NAME && message.function.arguments.includes("yo"),
            );

            const writeBashCount = toolCallMessages.reduce(
                (count, message) => (message.function.name === WRITE_BASH_TOOL_NAME ? count + 1 : count),
                0,
            );

            const usedEnterKey = toolCallMessages.some(
                (message) =>
                    message.function.name === WRITE_BASH_TOOL_NAME && message.function.arguments.includes("{enter}"),
            );

            const usedStopBash = toolCallMessages.some((message) => message.function.name === STOP_BASH_TOOL_NAME);

            check(checks, "has .gitignore with node_modules", hasGitIgnore);
            check(checks, "called yeoman tool to generate the repo skeleton", calledYo);
            check(checks, "sent keyboard input enough times to make it through yeoman", writeBashCount > 6);
            check(checks, "used enter key to confirm prompts", usedEnterKey);
            check(checks, "did not give up on using yeoman and manually create the project", !usedStopBash);
        },
    },
    {
        // Long running test, only run in nightly test CI
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "srt1 dogfood feedback 1",
        testDescription: "only reply to in-line comments",
        repo: "github/availability-findings",
        commit: "c29ef144ece5fbd7b62c6966612f5f1737e5410e",
        issueNumber: 30,
        // the tests for this repo require tokens and auth, so for the test, we'll just disable them
        problemDescription: readFileSync("test/evals/problem_statements/srt32-dogfood-feedback-1.md", "utf-8"),
        actionName: "respondToPRCommentAgent",
        assertions: async ({ commentReplyEvents, checks }) => {
            // LLM could decide to do this in one step, or per file call

            // reply to all 3 in-line comments
            check(checks, "should reply to in-line comment", commentReplyEvents.length == 1, commentReplyEvents.length);
        },
    },
    {
        // Long running test, only run in nightly test CI
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "colbywilliams dogfood feedback 1",
        testDescription:
            "multiple file edits for comment, only reply to new in-line comments even if there are related old comments",
        repo: "ghcpd/copilot-developer",
        commit: "2e817366fcb6fc3e034a25cb0103a6d4f1b184fd",
        issueNumber: 233,
        // the tests for this repo require tokens and auth, so for the test, we'll just disable them
        problemDescription: readFileSync("test/evals/problem_statements/colbywilliams-dogfood-feedback-1.md", "utf-8"),
        actionName: "respondToPRCommentAgent",
        assertions: async ({ commentReplyEvents, checks }) => {
            // reply to all 3 in-line comments
            check(checks, "should reply to pr comment", commentReplyEvents.length == 1, commentReplyEvents.length);
            check(
                checks,
                "comment_id should be 2050057743",
                commentReplyEvents.find((reply) => reply.comment_id === 2050057743) !== undefined,
            );
        },
    },
    {
        // Long running test, only run in nightly test CI
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "dgavedissian dogfood feedback 1",
        testDescription: "undo changes and code change for multiple in-line comments on different files",
        repo: "github/governor",
        commit: "c100c2257955b6d5eeb187ad1d191d9bdcc38423",
        issueNumber: 241,
        // the tests for this repo require tokens and auth, so for the test, we'll just disable them
        problemDescription:
            readFileSync("test/evals/problem_statements/dgavedissian-dogfood-feedback-1.md", "utf-8") +
            "\n\nDo not run any tests after addressing feedback. I've already run them.",
        actionName: "respondToPRCommentAgent",
        assertions: async ({ repoPath, commentReplyEvents, checks }) => {
            // check diff for the files that were undone
            const diffGo = await gitDiff(
                repoPath,
                "7c23aeb93c8183540e6e5904b3bdbcf9d76e7231",
                "config/versions/github/git",
            );
            const diffGitMon = await gitDiff(
                repoPath,
                "7c23aeb93c8183540e6e5904b3bdbcf9d76e7231",
                "config/versions/github/gitmon",
            );

            check(checks, "diff config/versions/github/git is empty", diffGo === "");
            check(checks, "diff config/versions/github/gitmon is empty", diffGitMon === "");

            // reply to 1 inline comment
            check(
                checks,
                "should reply to all in-line comment",
                commentReplyEvents.length == 5,
                commentReplyEvents.length,
            );
        },
    },
    {
        // Long running test, only run in nightly test CI
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "lichao127 dogfood feedback 1",
        testDescription: "comment should be treated as undo on previous commit, and reply to all in-line comments",
        repo: "github/wiz-therapy",
        commit: "071f28d65be66590c791884d554047cab8bfbb67",
        issueNumber: 510,
        // the tests for this repo require tokens and auth, so for the test, we'll just disable them
        problemDescription:
            readFileSync("test/evals/problem_statements/lichao127-dogfood-feedback-1.md", "utf-8") +
            "\n\nDo not run any tests after addressing feedback. I've already run them.",
        actionName: "respondToPRCommentAgent",
        assertions: async ({ repoPath, commentReplyEvents, checks }) => {
            // LLM could decide to do this in one step, or per file call
            // check diff for the files that were undone
            const diffPipfile = await gitDiff(repoPath, "071f28d~1", "Pipfile");

            check(checks, "diff Pipfile is empty", diffPipfile === "");

            // reply to 1 inline comment
            check(
                checks,
                "should reply to all in-line comment",
                commentReplyEvents.length == 1,
                commentReplyEvents.length,
            );
            check(
                checks,
                "comment_id should be 2045757368",
                commentReplyEvents.find((reply) => reply.comment_id === 2045757368) !== undefined,
            );
        },
    },
    {
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "no cross-repo dogfood feedback",
        testDescription: "no cross-repo support",
        repo: "ghcpd/cli",
        commit: "main",
        problemDescription: `Fix issue #47 in the 'ghcpd/pulumi' repository"`,
        runDefaultAssertions: false,
        assertions: async ({ progressEvents, diff, checks }) => {
            check(checks, "no diff", diff.trim() === "");
            check(checks, "at least one progress event", progressEvents.length >= 1);
        },
    },
];

describe("dogfood", () => {
    test.concurrent.for(testCases)("$name", { timeout: DEFAULT_TIMEOUT_MS }, runTest);
});
