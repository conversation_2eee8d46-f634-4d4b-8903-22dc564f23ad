/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import * as fs from "fs";
import * as path from "path";
import { v4 as uuidv4 } from "uuid";
import { describe, expect, test } from "vitest";
import { AIClient as CAPIAIClient } from "../../src/model/capi/ai";
import { ConsoleLogger } from "../../src/runner/logger/console";
import { getOrInitSettings } from "../../src/settings/factory";
import { DEFAULT_TIMEOUT_MS } from "./eval";

import { Tool, ToolResult } from "../../src/tools";

describe.runIf(process.env.NIGHTLY_TEST_CI === "true")("images", () => {
    // Mock GitHub MCP server for repository image tests
    const createMockGitHubMCPTool = (): Tool | null => {
        if (process.env.MOCK_GITHUB_MCP_TOOL === "false") {
            return null;
        }

        return {
            name: "github-mcp-server-get_file_contents",
            description: "Mock GitHub MCP tool for testing",
            input_schema: {
                type: "object",
                properties: {
                    owner: { type: "string" },
                    repo: { type: "string" },
                    path: { type: "string" },
                    branch: { type: "string" },
                },
                required: ["owner", "repo", "path"],
            },
            callback: async (input: {
                owner: string;
                repo: string;
                path: string;
                branch?: string;
            }): Promise<ToolResult> => {
                const { owner, repo, path: filePath } = input;
                console.log(`Mock MCP tool called with: owner=${owner}, repo=${repo}, path=${filePath}`);

                try {
                    if (filePath === "src/images/autumn-forest-nature-scenery-mountain-45-8K.jpg") {
                        console.log("Mock: Loading large image fixture for autumn-forest image");
                        const largeImageBase64 = await readFileToString(
                            path.join(__dirname, "../fixtures", "image_as_base_64_exceeding_max_dim.txt"),
                        );
                        console.log(`Mock: Loaded large image, length: ${largeImageBase64.length}`);
                        const result = {
                            textResultForLlm: "successfully downloaded binary file",
                            binaryResultForLlm: [
                                {
                                    data: largeImageBase64,
                                    mimeType: "image/jpeg",
                                    type: "image",
                                },
                            ],
                            resultType: "success" as const,
                            toolTelemetry: {},
                        };
                        console.log("Mock: Returning large image result");
                        return result;
                    }

                    if (filePath === "docs/architecture.png") {
                        console.log("Mock: Loading normal image fixture for architecture.png");
                        const normalImageBase64 = await readFileToString(
                            path.join(__dirname, "../fixtures", "image_as_base64.txt"),
                        );
                        console.log(`Mock: Loaded normal image, length: ${normalImageBase64.length}`);
                        const result = {
                            textResultForLlm: "successfully downloaded binary file",
                            binaryResultForLlm: [
                                {
                                    data: normalImageBase64,
                                    mimeType: "image/png",
                                    type: "image",
                                },
                            ],
                            resultType: "success" as const,
                            toolTelemetry: {},
                        };
                        console.log("Mock: Returning normal image result");
                        return result;
                    }

                    console.log(`Mock: File not found for path: ${filePath}`);
                    return {
                        textResultForLlm: JSON.stringify({
                            error: "File not found",
                        }),
                        resultType: "failure" as const,
                        error: "File not found",
                        toolTelemetry: {},
                    };
                } catch (error) {
                    console.error(`Mock: Error in callback: ${error}`);
                    return {
                        textResultForLlm: JSON.stringify({
                            error: `Mock error: ${error}`,
                        }),
                        resultType: "failure" as const,
                        error: `Mock error: ${error}`,
                        toolTelemetry: {},
                    };
                }
            },
        };
    };

    test("agent expands <img> tags", { timeout: DEFAULT_TIMEOUT_MS }, async () => {
        const logger = new ConsoleLogger();
        process.env.GITHUB_USER_ID = "1885174";
        const settings = await getOrInitSettings();
        settings.github = {
            serverUrl: "https://github.com",
            token: process.env.EVAL_TEST_VISION,
        };
        settings.api = {
            copilot: {
                token: process.env.EVAL_TEST_VISION,
                integrationId: process.env.GITHUB_COPILOT_INTEGRATION_ID,
            },
        };

        settings.featureFlags ||= {};
        settings.featureFlags["copilot_swe_agent_vision"] = true;
        settings.service ||= {};
        settings.service.instance ||= {};
        settings.service.instance.id ||= `sweagent-tests-${uuidv4()}`;
        const client = new CAPIAIClient(settings, logger, {
            model: process.env.EVAL_MODEL,
        });

        const result = await client.getCompletionWithTools(
            "You are a helpful assistant.",
            [
                {
                    role: "user",
                    content: `What text is in this image? <img src="https://github.com/user-attachments/assets/9300859f-46c9-4d16-80b0-c9fd0f38adb3">`,
                },
            ],
            [],
        );
        for await (const event of result) {
            if (event.kind == "response") {
                expect(event.response.content?.toLowerCase()).toContain("hello");
                return;
            }
        }

        expect.fail("Expected a response");
    });

    test("agent expands markdown image tags", { timeout: DEFAULT_TIMEOUT_MS }, async () => {
        const logger = new ConsoleLogger();
        process.env.GITHUB_USER_ID = "1885174";
        const settings = await getOrInitSettings();

        settings.featureFlags ||= {};
        settings.featureFlags["copilot_swe_agent_vision"] = true;

        settings.github = {
            serverUrl: "https://github.com",
            token: process.env.EVAL_TEST_VISION,
        };
        settings.api = {
            copilot: {
                token: process.env.EVAL_TEST_VISION,
                integrationId: process.env.GITHUB_COPILOT_INTEGRATION_ID,
            },
        };
        settings.service ||= {};
        settings.service.instance ||= {};
        settings.service.instance.id ||= `sweagent-tests-${uuidv4()}`;
        const client = new CAPIAIClient(settings, logger, {
            model: process.env.EVAL_MODEL,
        });

        const result = await client.getCompletionWithTools(
            "You are a helpful assistant.",
            [
                {
                    role: "user",
                    content: `What text is in this image? ![alt text](https://github.com/user-attachments/assets/9300859f-46c9-4d16-80b0-c9fd0f38adb3)`,
                },
            ],
            [],
        );
        for await (const event of result) {
            if (event.kind == "response") {
                expect(event.response.content?.toLowerCase()).toContain("hello");
                return;
            }
        }

        expect.fail("Expected a response");
    });

    test(
        "agent calls take screenshot tool and converts base64 image to image url",
        { timeout: DEFAULT_TIMEOUT_MS },
        async () => {
            const logger = new ConsoleLogger();
            process.env.GITHUB_USER_ID = "1885174";
            const settings = await getOrInitSettings();

            settings.github = {
                serverUrl: "https://github.com",
                token: process.env.EVAL_TEST_VISION,
                repo: {
                    id: 1004572951, // ghcpd/runtime-vision-test-repo
                },
            };
            settings.api = {
                copilot: {
                    token: process.env.EVAL_TEST_VISION,
                    integrationId: process.env.GITHUB_COPILOT_INTEGRATION_ID,
                },
            };

            settings.featureFlags ||= {};
            settings.featureFlags["copilot_swe_agent_vision"] = true;

            const client = new CAPIAIClient(settings, logger, {
                model: process.env.EVAL_MODEL,
            });
            const filePath = path.join(__dirname, "../fixtures", "image_as_base64.txt");
            const base64Image = await readFileToString(filePath);

            const tools = [
                {
                    name: "browser_navigate",
                    description:
                        "Navigate to a page. You can use this to navigate to a page before taking a screenshot.",
                    input_schema: undefined,
                    callback: async (_input: unknown): Promise<ToolResult> => {
                        return {
                            textResultForLlm: "<html><body><img src='img.png'/></body></html>",
                            resultType: "success",
                            toolTelemetry: {},
                        };
                    },
                },
                {
                    name: "browser_take_screenshot",
                    description:
                        "Take a screenshot of the current page. You can't perform actions based on the screenshot, use browser_snapshot for actions.",
                    input_schema: undefined,
                    callback: async (_input: unknown): Promise<ToolResult> => {
                        return {
                            textResultForLlm: "screenshot",
                            binaryResultForLlm: [
                                {
                                    data: base64Image,
                                    mimeType: "image/png",
                                    type: "image",
                                },
                            ],
                            resultType: "success",
                            toolTelemetry: {},
                        };
                    },
                },
            ];

            const result = await client.getCompletionWithTools(
                "You are a helpful assistant.",
                [
                    {
                        role: "user",
                        content: `Can you take a screenshot of the https://some-test-page.org/ page, include it in PR description and describe what is there?`,
                    },
                ],
                tools,
            );

            for await (const event of result) {
                if (event.kind === "message" && event.message.role === "user" && Array.isArray(event.message.content)) {
                    expect(event.message.content.length).toBeGreaterThan(0);
                    expect(event.message.content[0].type).toBe("text");
                    expect(event.message.content[1].type).toBe("image_url");
                }
                if (event.kind == "response") {
                    expect(event.response.content?.toLowerCase()).toContain("hello");
                    expect(event.response.content?.toLowerCase()).toContain(
                        "https://github.com/user-attachments/assets/",
                    );
                    return;
                }
            }

            expect.fail("Expected a response");
        },
    );

    test(
        "agent calls take screenshot tool and converts base64 image to image url - image exceeding limits",
        { timeout: DEFAULT_TIMEOUT_MS },
        async () => {
            const logger = new ConsoleLogger();
            process.env.GITHUB_USER_ID = "1885174";
            const settings = await getOrInitSettings();

            settings.github = {
                serverUrl: "https://github.com",
                token: process.env.EVAL_TEST_VISION,
                repo: {
                    id: 1004572951, // ghcpd/runtime-vision-test-repo
                },
            };
            settings.api = {
                copilot: {
                    token: process.env.EVAL_TEST_VISION,
                    integrationId: process.env.GITHUB_COPILOT_INTEGRATION_ID,
                },
            };

            settings.featureFlags ||= {};
            settings.featureFlags["copilot_swe_agent_vision"] = true;

            const client = new CAPIAIClient(settings, logger, {
                model: process.env.EVAL_MODEL,
            });
            const filePath = path.join(__dirname, "../fixtures", "image_as_base_64_exceeding_max_dim.txt");
            const base64Image = await readFileToString(filePath);

            const tools = [
                {
                    name: "browser_take_screenshot",
                    description:
                        "Take a screenshot of the current page. You can't perform actions based on the screenshot, use browser_snapshot for actions.",
                    input_schema: undefined,
                    callback: async (_input: unknown): Promise<ToolResult> => {
                        return {
                            textResultForLlm: "screenshot",
                            binaryResultForLlm: [
                                {
                                    data: base64Image,
                                    mimeType: "image/png",
                                    type: "image",
                                },
                            ],
                            resultType: "success",
                            toolTelemetry: {},
                        };
                    },
                },
            ];

            const result = await client.getCompletionWithTools(
                "You are a helpful assistant.",
                [
                    {
                        role: "user",
                        content: `Can you take a screenshot of the https://some-test-page.org/ page, include it in PR description and describe what is there?`,
                    },
                ],
                tools,
            );
            for await (const event of result) {
                if (event.kind === "message" && event.message.role === "user" && Array.isArray(event.message.content)) {
                    expect(event.message.content.length).toBeGreaterThan(0);
                    expect(event.message.content[0].type).toBe("text");
                    expect(event.message.content[1].type).toBe("image_url");
                }
                if (event.kind == "response") {
                    expect(event.response.content?.toLowerCase()).not.toContain("hello");
                    expect(event.response.content?.toLowerCase()).not.toContain(
                        "https://github.com/user-attachments/assets/",
                    );
                    return;
                }
            }

            expect.fail("Expected a response");
        },
    );

    test("agent expands markdown image tags - image exceeds dimensions", { timeout: DEFAULT_TIMEOUT_MS }, async () => {
        const logger = new ConsoleLogger();
        process.env.GITHUB_USER_ID = "1885174";
        const settings = await getOrInitSettings();

        settings.featureFlags ||= {};
        settings.featureFlags["copilot_swe_agent_vision"] = true;

        settings.github = {
            serverUrl: "https://github.com",
            token: process.env.EVAL_TEST_VISION,
        };
        settings.api = {
            copilot: {
                token: process.env.EVAL_TEST_VISION,
                integrationId: process.env.GITHUB_COPILOT_INTEGRATION_ID,
            },
        };
        settings.service ||= {};
        settings.service.instance ||= {};
        settings.service.instance.id ||= `sweagent-tests-${uuidv4()}`;
        const client = new CAPIAIClient(settings, logger, {
            model: process.env.EVAL_MODEL,
        });

        const result = await client.getCompletionWithTools(
            "You are a helpful assistant.",
            [
                {
                    role: "user",
                    content: `What text is in this image? ![alt text](https://github.com/user-attachments/assets/d514ebf0-c284-44aa-be7d-f7ea834674bd)`,
                },
            ],
            [],
        );
        for await (const event of result) {
            if (event.kind == "response") {
                expect(event.response.content?.toLowerCase()).not.toContain("hello");
                return;
            }
        }

        expect.fail("Expected a response");
    });

    test("agent builds image url from relative path", { timeout: DEFAULT_TIMEOUT_MS }, async () => {
        const logger = new ConsoleLogger();

        const settings = await getOrInitSettings();
        settings.github = {
            serverUrl: "https://github.com",
            token: process.env.EVAL_TEST_VISION,
        };
        settings.api = {
            copilot: {
                token: process.env.EVAL_TEST_VISION,
                integrationId: process.env.GITHUB_COPILOT_INTEGRATION_ID,
            },
        };
        process.env.GITHUB_USER_ID = "1885174";
        process.env.COPILOT_MCP_ENABLED = "true";

        settings.featureFlags ||= {};
        settings.featureFlags["copilot_swe_agent_resolve_repo_images"] = true;
        settings.featureFlags["copilot_swe_agent_vision"] = true;

        const client = new CAPIAIClient(settings, logger, {
            model: process.env.EVAL_MODEL,
        });

        const testInsights =
            '{\n  "contents": "File: `docs/architecture.md`\\n```markdown\\n# Architecture of Padawan\\n...\\n![Architecture Diagram](./architecture.png)\\n\\n## Hydro events\\n...\\n```",\n  "languageName": "Markdown",\n  "path": "docs/architecture.md",\n  "range": {\n    "start": 3,\n    "end": 60\n  },\n  "ref": "refs/heads/main",\n  "type": "snippet",\n  "url": "https://github.com/github/sweagentd/blob/37ca958a4d5fd5f9cd8d28b819da29ff59bbebb5/docs/architecture.md#L3-L60"\n}';

        const result = await client.getCompletionWithTools(
            "You are a helpful assistant.",
            [
                {
                    role: "user",
                    content: `
                        What text is in the images in this repository? Make sure you echo back the repository insights and anything else I give you exactly as I am providing them as well.

                        Here are relevant code snippets from the repository that might help with this task: [${testInsights}]
                    `,
                },
            ],
            [createMockGitHubMCPTool()].filter((tool) => tool !== null) as Tool[],
        );

        for await (const event of result) {
            if (event.kind == "response") {
                console.log(event.response.content?.toLowerCase());
                expect(event.response.content).toBeDefined();
                expect(event.response.content?.toLowerCase()).toContain("computeusage");
                return;
            }
        }

        expect.fail("Expected a response");
    });

    test(
        "agent builds image url from repo path - image exceeds dimensions",
        { timeout: DEFAULT_TIMEOUT_MS },
        async () => {
            const logger = new ConsoleLogger();

            const settings = await getOrInitSettings();
            settings.github = {
                serverUrl: "https://github.com",
                token: process.env.EVAL_TEST_VISION,
            };
            settings.api = {
                copilot: {
                    token: process.env.EVAL_TEST_VISION,
                    integrationId: process.env.GITHUB_COPILOT_INTEGRATION_ID,
                },
            };
            process.env.GITHUB_USER_ID = "1885174";

            process.env.COPILOT_MCP_ENABLED = "true";

            settings.featureFlags ||= {};
            settings.featureFlags["copilot_swe_agent_resolve_repo_images"] = true;
            settings.featureFlags["copilot_swe_agent_vision"] = true;

            const client = new CAPIAIClient(settings, logger, {
                model: process.env.EVAL_MODEL,
            });

            const testInsights =
                '{\n  "contents": "File: `docs/architecture.md`\\n```markdown\\n# Architecture of Padawan\\n\\n![Large Image](https://github.com/ghcpd/react-spa/blob/main/src/images/autumn-forest-nature-scenery-mountain-45-8K.jpg)\\n\\n## Hydro events\\n...\\n```File: `docs/architecture.md`\\n```markdown\\n# Architecture of Padawan\\n...\\n## Runtime\\n...\\n```",\n  "languageName": "Markdown",\n  "path": "docs/architecture.md",\n  "range": {\n    "start": 3,\n    "end": 60\n  },\n  "ref": "refs/heads/main",\n  "type": "snippet",\n  "url": "https://github.com/github/sweagentd/blob/37ca958a4d5fd5f9cd8d28b819da29ff59bbebb5/docs/architecture.md#L3-L60"\n}';

            const result = await client.getCompletionWithTools(
                "You are a helpful assistant.",
                [
                    {
                        role: "user",
                        content: `
                        What colours you see in the images in this repository? Make sure you echo back the repository insights and anything else I give you exactly as I am providing them as well. Don't tell me unless you can actually see the colours, don't just guess. It is absolutely critical that you do not guess.

                        Here are relevant code snippets from the repository that might help with this task: [${testInsights}]
                    `,
                    },
                ],
                [createMockGitHubMCPTool()].filter((tool) => tool !== null) as Tool[],
            );

            for await (const event of result) {
                if (event.kind === "message" && event.message.role === "user" && Array.isArray(event.message.content)) {
                    for (const content of event.message.content) {
                        if (content.type === "image_url") {
                            console.log(`Found image URL in message: ${content.image_url.url}`);
                            if (content.image_url.url.includes("autumn-forest-nature-scenery-mountain-45-8K.jpg")) {
                                console.log(`Oversized image URL found in CAPI message (should be filtered)`);
                                expect.fail(
                                    `Oversized image ${content.image_url.url} was not filtered out and reached the LLM`,
                                );
                            }
                        }
                    }
                }

                if (event.kind == "response") {
                    expect(event.response.content?.toLowerCase()).not.toContain("green");
                    console.log(`Oversized image properly filtered - no color descriptions found`);
                    return;
                }
            }

            expect.fail("Expected a response");
        },
    );

    async function readFileToString(filePath: string): Promise<string> {
        try {
            const data = await fs.promises.readFile(filePath, "utf8");
            return data;
        } catch (error) {
            console.error("Error reading file:", error);
            throw error;
        }
    }
});
