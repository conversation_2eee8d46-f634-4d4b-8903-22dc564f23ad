/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import fs from "fs";
import path from "path";
import { v4 as uuidv4 } from "uuid";
import { PRDetails } from "../../src/agents/sweagent";
import { ContentFilterMode } from "../../src/helpers/content-filter";
import { check, DEFAULT_TIMEOUT_MS, runTest, TestCase } from "./eval";

const testRepo = "ghcpd/react-spa";

const problemDescription_takeScreenshot = `---
*This section details on the original issue you should resolve*

<issue_title>
Add new fields to React SPA App
</issue_title>

<issue_description>
Currently React SPA app has one input field. Add a second field prompting a user to input the city they live in.

When user adds their name and a city show the text that greets the user by name and gives some random compliment to the city they live in. You can pre-generate 10-15 phrases and pick randomly one of them.

At the end start server locally and take a screenshot of how the page looks like and include it with in the PR description.
</issue_description>

## Comments on the Issue (you are @copilot in this section)

<comments>

</comments>
`;
const problemDescription_describeUserAttachedImage = `---
----
*This section details on the original issue you should resolve*

<issue_title>
Add picture description
</issue_title>

<issue_description>

Add a description to the image attached to this issue. The description should be added to the image.md file in the root of the repository.

![Image](https://github.com/user-attachments/assets/91a56ab4-cce5-47de-8544-e9f9308b9b97)
</issue_description>

## Comments on the Issue (you are @copilot in this section)

<comments>

</comments>
`;
const testCases: TestCase[] = [
    {
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: `agent runs React SPA app and takes a screenshot`,
        repo: testRepo,
        commit: "4feaac57663133c29815fc495459535b4113b81c",
        problemDescription: problemDescription_takeScreenshot,
        mcpServers: {
            playwright: {
                command: "npx",
                args: ["@playwright/mcp@latest"],
            },
        },
        configureSettings: async (settings) => {
            settings.featureFlags.copilot_swe_agent_vision = true;
            settings.tools = settings.tools || {};
            settings.service ||= {};
            settings.service.instance ||= {};
            settings.service.instance.id ||= `sweagent-tests-${uuidv4()}`;
            settings.api ||= {};
            settings.api.copilot ||= {};
            settings.api.copilot.token = process.env.EVAL_TEST_VISION;
        },
        assertions: async ({ checks, toolCallMessages }) => {
            let calledBrowserTakeScreenshot = false;

            for (const toolCall of toolCallMessages) {
                if (toolCall.function.name == "playwright-browser_take_screenshot") {
                    try {
                        const args = JSON.parse(toolCall.function.arguments);
                        if (
                            typeof args === "object" &&
                            args.filename &&
                            (args.filename.endsWith(".png") || args.filename.endsWith(".jpg"))
                        ) {
                            calledBrowserTakeScreenshot = true;
                            break;
                        }
                    } catch {
                        // ignore JSON parse errors
                    }
                }
            }

            check(
                checks,
                "playwright-browser_take_screenshot is called with valid file extension",
                calledBrowserTakeScreenshot,
            );
        },
    },
    {
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: `recognizes user attached image and describes it`,
        repo: "ghcpd/runtime-vision-test-repo",
        commit: "c87cbd7bd7002e37f858aa79c01c2bbc367590b7",
        problemDescription: problemDescription_describeUserAttachedImage,
        configureSettings: async (settings) => {
            settings.featureFlags.copilot_swe_agent_vision = true;
            settings.featureFlags.sweagentd_blackbird_initial_search = true;
            settings.tools = settings.tools || {};
            settings.service ||= {};
            settings.service.instance ||= {};
            settings.service.instance.id ||= `sweagent-tests-${uuidv4()}`;
            settings.problem = {
                contentFilterMode: ContentFilterMode.Markdown,
            };
            settings.api ||= {};
            settings.api.copilot ||= {};
            settings.api.copilot.token = process.env.EVAL_TEST_VISION;
            settings.api.copilot.hmacKey = "";
        },
        assertions: async ({ repoPath, result, checks }) => {
            console.log(`PR Commit: ${(result as PRDetails).title}\n\n${(result as PRDetails).description}`);
            const imageFile = path.join(repoPath, "image.md");

            const imageFileExists = fs.existsSync(imageFile);
            if (imageFileExists) {
                const imageContent = fs.readFileSync(imageFile, "utf-8");
                check(
                    checks,
                    `image.md contains image description`,
                    imageContent.toLowerCase().includes("hello"),
                    imageContent.trim(),
                );
            } else {
                check(checks, "image.md contains image description", false, "file does not exist");
            }
        },
    },
];

describe("vision evals", () => {
    test.concurrent.for(testCases)("$name", { timeout: DEFAULT_TIMEOUT_MS }, runTest);
});
