/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

/**
 * The tests in this file are non-deterministic evals to constrain the behaviour of the agent.
 * They may not all pass all the time, but should pass "most" of the time.
 * They should be skipped in PR/main CI, but will be run in nightlies.
 */
import { getDefaultEnvironment } from "@modelcontextprotocol/sdk/client/stdio.js";
import { execSync } from "child_process";
import { existsSync } from "fs";
import { lookpath } from "lookpath";
import { DEFAULT_TIMEOUT_MS, TestCase, check, runTest } from "./eval";

const testCases: TestCase[] = [
    {
        runIfEnvVar: "NIGHTLY_TEST_CI",
        skipIf: async () => {
            if (!(await lookpath("docker"))) {
                return "docker not found in PATH";
            }

            if (!process.env.GITHUB_MCP_ACCESS_TOKEN && !process.env.GITHUB_TOKEN) {
                return "GITHUB_MCP_ACCESS_TOKEN or GITHUB_TOKEN not set";
            }

            try {
                execSync("docker pull ghcr.io/github/github-mcp-server:latest");
            } catch (error) {
                return `Failed to pull github-mcp-server image: ${error}`;
            }

            return false;
        },
        name: "Uses github mcp server to fetch issues.",
        repo: {
            "README.md": "",
        },
        commit: null,
        problemDescription: `----
*This section details on the original issue you should resolve*

<issue_title>
Summarize the discussion from github/sweagentd#1612
</issue_title>

<issue_description>
Create a TASK.md file summarizing the discussion from github/sweagentd#1612.

Use the **get_issue** tool to fetch the issue details.

If you can't access the issue details, do nothing, DO NOT CREATE the file with dummy content.
</issue_description>

## Comments on the Issue (you are @copilot in this section)

<comments>

</comments>
`,
        mcpServers: {
            "github-mcp-server": {
                command: "docker",
                args: [
                    "run",
                    "-i",
                    "--rm",
                    "-e",
                    "GITHUB_PERSONAL_ACCESS_TOKEN",
                    "ghcr.io/github/github-mcp-server:latest",
                    "stdio",
                    "--read-only",
                ],
                env: {
                    ...getDefaultEnvironment(),
                    GITHUB_PERSONAL_ACCESS_TOKEN: process.env.GITHUB_MCP_ACCESS_TOKEN || process.env.GITHUB_TOKEN!,
                },
            },
        },
        assertions: async ({ repoPath, checks, toolCallMessages }) => {
            let calledGetIssue = false;

            for (const toolCall of toolCallMessages) {
                if (toolCall.function.name == "github-mcp-server-get_issue") {
                    try {
                        const args = JSON.parse(toolCall.function.arguments);
                        if (
                            typeof args === "object" &&
                            args?.owner === "github" &&
                            args?.repo === "sweagentd" &&
                            args?.issue_number === 1612
                        ) {
                            calledGetIssue = true;
                            break;
                        }
                    } catch {
                        // ignore JSON parse errors
                    }
                }
            }

            check(checks, "get_issue should be called", calledGetIssue);
            check(checks, "TASK.md should have been created", existsSync(`${repoPath}/TASK.md`));
        },
    },
];

describe("mcp integrations", () => {
    test.concurrent.for(testCases)("$name", { timeout: DEFAULT_TIMEOUT_MS }, runTest);
});
