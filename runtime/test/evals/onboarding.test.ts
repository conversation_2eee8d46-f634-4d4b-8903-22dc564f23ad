/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

/**
 * The tests in this file are non-deterministic evals to constrain the behaviour of the agent.
 * They may not all pass all the time, but should pass "most" of the time.
 * They should be skipped in PR/main CI, but will be run in nightlies.
 */
import { readFileSync } from "fs";
import path from "path";
import { DEFAULT_TIMEOUT_MS, TestCase, check, runTest } from "./eval";

// --isolated so that we can run multiple instances without an error.
const PLAYWRIGHT_MCP_SERVERS = {
    "playwright-mcp-server": {
        command: "npx",
        args: ["@playwright/mcp@latest", "--isolated"],
    },
};

const testCases: TestCase[] = [
    {
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "Uses the onboarding work item to generate custom instructions for the repository",
        repo: "ghcpd/copilot-chat",
        commit: "031328e04ca2a758d9875b7019c5b332ab48c0b3",
        mcpServers: PLAYWRIGHT_MCP_SERVERS,
        problemDescription: readFileSync(
            path.join(__dirname, "../../../internal/github/onboarding_prompt.md"),
            "utf-8",
        ),
        configureSettings: async (settings) => {
            settings.featureFlags.copilot_swe_agent_vision = true;
            settings.tools = settings.tools || {};
            settings.tools.bash = settings.tools.bash || {};
            settings.tools.bash.defaultTimeout = 5;
        },
        assertions: async ({ checks, toolCallMessages }) => {
            const customInstructionsEdits = toolCallMessages.filter(
                (message) =>
                    message.function.name === "str_replace_editor" &&
                    message.function.arguments.includes(".github/copilot-instructions.md"),
            );
            const instructionsIncludeBuild = customInstructionsEdits.some((message) =>
                message.function.arguments.includes("npm run build"),
            );
            const instructionsIncludeTest = customInstructionsEdits.some(
                (message) =>
                    message.function.arguments.includes("npm test") ||
                    message.function.arguments.includes("npm run test"),
            );

            check(checks, "Creates custom instructions during onboarding", customInstructionsEdits.length > 0);
            check(checks, "Custom instructions include build", instructionsIncludeBuild);
            check(checks, "Custom instructions include testing", instructionsIncludeTest);
        },
    },
    {
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "Presses through build difficulties during onboarding and validates the build instructions work",
        repo: "ghcpd/eshop",
        commit: "826ab847d80add515b5b80739c110a62a6365708",
        mcpServers: PLAYWRIGHT_MCP_SERVERS,
        problemDescription: readFileSync(
            path.join(__dirname, "../../../internal/github/onboarding_prompt.md"),
            "utf-8",
        ),
        configureSettings: async (settings) => {
            settings.featureFlags.copilot_swe_agent_vision = true;
            settings.tools = settings.tools || {};
            settings.tools.bash = settings.tools.bash || {};
            settings.tools.bash.defaultTimeout = 5;
        },
        assertions: async ({ checks, toolCallMessages }) => {
            const customInstructionsEdits = toolCallMessages.filter(
                (message) =>
                    message.function.name === "str_replace_editor" &&
                    message.function.arguments.includes(".github/copilot-instructions.md"),
            );
            const instructionsIncludeBuild = customInstructionsEdits.some((message) =>
                message.function.arguments.includes("dotnet build"),
            );
            const instructionsIncludeTest = customInstructionsEdits.some((message) =>
                message.function.arguments.includes("dotnet test"),
            );

            const validatedRunningApp = toolCallMessages.some(
                (message) => message.function.name === "bash" && message.function.arguments.includes("dotnet run"),
            );

            const usedPlaywright = toolCallMessages.some((message) => message.function.name.includes("playwright"));

            check(checks, "Creates custom instructions during onboarding", customInstructionsEdits.length > 0);
            check(checks, "Custom instructions include build", instructionsIncludeBuild);
            check(checks, "Custom instructions include testing", instructionsIncludeTest);
            check(checks, "Successfully runs the application", validatedRunningApp);
            check(checks, "Uses Playwright to validate the application", usedPlaywright);
        },
    },
];

describe("onboarding evals", () => {
    test.concurrent.for(testCases)("$name", { timeout: DEFAULT_TIMEOUT_MS }, runTest);
});
