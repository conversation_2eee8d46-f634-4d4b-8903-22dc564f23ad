/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

/**
 * The tests in this file are non-deterministic evals to constrain the behaviour of the agent.
 * They may not all pass all the time, but should pass "most" of the time.
 * They should be skipped in PR/main CI, but will be run in nightlies.
 */
import { DEFAULT_TIMEOUT_MS, TestCase, check, runTest } from "./eval";

const createTest = (
    params: Pick<TestCase, "name" | "runIfEnvVar" | "assertions" | "configureSettings"> & {
        comments: string;
    },
): TestCase => ({
    runIfEnvVar: params.runIfEnvVar,
    name: params.name,
    assertions: params.assertions,
    configureSettings: params.configureSettings,
    runDefaultAssertions: false,
    issueNumber: 42,
    actionName: "respondToPRCommentAgent",
    repo: {
        "README.md": "hello world",
    },
    commit: null,
    problemDescription: `----
*This section includes details on the pull request with code changes you have already submitted to fix the problem above.*

<pr_title>
Add README.md with hello world content
</pr_title>

<pr_description>

Added a README.md file to the root directory of the repository with the simple content "hello world" as requested. This provides a basic introduction to the project that can be expanded in the future.

Fixes #42.

</pr_description>

## Comments on the PR (you are @copilot in this section)

<comments>
${params.comments}
</comments>

----
The last **1** git commits in this branch are the changes you have made so far. Use those as your change commit history.

`,
});

const testCases: TestCase[] = [
    createTest({
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "pr_comment: explain only",
        comments: `
<pr_comments>
<comment_new>
<comment_id>1234509876</comment_id>
<author>@me</author>
What changes did you make to the code?
</comment_new>
`,
        assertions: async ({ result, commentReplyEvents, diff, partialResultEvents, checks }) => {
            console.log("diff", diff);
            console.log("partialResultEvents", JSON.stringify(partialResultEvents, null, 2));
            console.log("commentReplyEvents", JSON.stringify(commentReplyEvents, null, 2));
            check(checks, "No changes made to the repo", diff === "", diff);
            check(
                checks,
                "replied to comment 1234509876",
                commentReplyEvents.find((e) => e.comment_id === 1234509876) !== undefined,
            );
            check(checks, "no partial results", partialResultEvents.length === 0, partialResultEvents.length);

            check(checks, "no title updated", !result.title, result.title);
            check(checks, "no description updated", !result.description, result.description);
        },
    }),
    createTest({
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "pr_reviews: explain only",
        comments: `
            <pr_reviews>
            <comment_thread>
            <file>README.md:2</file>
            <comment_new>
            <comment_id>1234509876</comment_id>
            <author>@me</author>
            What changes did you make to the code?
            </comment_new>
            </comment_thread>
            </pr_reviews>
            `,
        assertions: async ({ result, commentReplyEvents, diff, partialResultEvents, checks }) => {
            console.log("diff", diff);
            console.log("partialResultEvents", JSON.stringify(partialResultEvents, null, 2));
            console.log("commentReplyEvents", JSON.stringify(commentReplyEvents, null, 2));
            check(checks, "No changes made to the repo", diff === "", diff);
            check(
                checks,
                "replied to comment 1234509876",
                commentReplyEvents.find((e) => e.comment_id === 1234509876) !== undefined,
            );
            check(checks, "no partial results", partialResultEvents.length === 0);

            check(checks, "no title updated", !result.title, result.title);
            check(checks, "no description updated", !result.description, result.description);
        },
    }),
    createTest({
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "pr_comment: old and new comments",
        comments: `
            <pr_comments>
            <comment_old>
            <author>@me</author>
            What changes did you make to the code?
            </comment_old>
            </comment_new>
            <pr_comments>
            <comment_new>
            <comment_id>1234509876</comment_id>
            <author>@me</author>
            Change the README.md file content to be "hello universe" instead.
            </comment_new>
            `,
        assertions: async ({ result, commentReplyEvents, diff, partialResultEvents, checks }) => {
            console.log("diff", diff);
            console.log("partialResultEvents", JSON.stringify(partialResultEvents, null, 2));
            console.log("commentReplyEvents", JSON.stringify(commentReplyEvents, null, 2));
            check(
                checks,
                "README.md content is 'hello universe'",
                diff.includes("hello universe") && diff.includes("README.md"),
                diff,
            );
            check(checks, "only one comment reply", commentReplyEvents.length === 1, commentReplyEvents.length);
            check(
                checks,
                "replied to comment 1234509876",
                commentReplyEvents.find((e) => e.comment_id === 1234509876) !== undefined,
            );
            check(checks, "partial results > 0", partialResultEvents.length > 0);

            check(checks, 'title contains "universe"', !!result.title?.includes("universe"), result.title);
            check(
                checks,
                'description contains "universe"',
                !!result.description?.includes("universe"),
                result.description,
            );
            check(checks, 'title does not contain "world"', !result.title?.includes("world"), result.title);
            check(
                checks,
                'description does not contain "world"',
                !result.description?.includes("world"),
                result.description,
            );
        },
    }),
    createTest({
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "pr_comment: old review comment and new pr comment",
        comments: `
            <pr_reviews>
            <comment_thread>
            <file>README.md:2</file>
            <original_commit>c464674</original_commit>
            <comment_old>
            <author>@me</author>
            What changes did you make to the code?
            </comment_old>
            </comment_thread>
            </pr_reviews>

            <pr_comments>
            <comment_new>
            <comment_id>1234509876</comment_id>
            <author>@me</author>
            Change the README.md file content to be "hello universe" instead.
            </comment_new>
            `,
        assertions: async ({ result, commentReplyEvents, diff, partialResultEvents, checks }) => {
            console.log("diff", diff);
            console.log("partialResultEvents", JSON.stringify(partialResultEvents, null, 2));
            console.log("commentReplyEvents", JSON.stringify(commentReplyEvents, null, 2));
            check(
                checks,
                "README.md content is 'hello universe'",
                diff.includes("hello universe") && diff.includes("README.md"),
                diff,
            );
            check(checks, "only one comment reply", commentReplyEvents.length === 1, commentReplyEvents.length);
            check(
                checks,
                "replied to comment 1234509876",
                commentReplyEvents.find((e) => e.comment_id === 1234509876) !== undefined,
            );
            check(checks, "partial results > 0", partialResultEvents.length > 0);

            check(checks, 'title contains "universe"', !!result.title?.includes("universe"), result.title);
            check(
                checks,
                'description contains "universe"',
                !!result.description?.includes("universe"),
                result.description,
            );
            check(checks, 'title does not contain "world"', !result.title?.includes("world"), result.title);
            check(
                checks,
                'description does not contain "world"',
                !result.description?.includes("world"),
                result.description,
            );
        },
    }),
    createTest({
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "pr_comment and pr_reviews comments",
        comments: `
            <pr_reviews>
            <comment_thread>
            <file>README.md:2</file>
            <original_commit>c464674</original_commit>
            <comment_new>
            <comment_id>1234509876</comment_id>
            <author>@me</author>
            What changes did you make to the code?
            </comment_new>
            </comment_thread>
            </pr_reviews>

            <pr_comments>
            <comment_new>
            <comment_id>1234509879</comment_id>
            <author>@me</author>
            Change the README.md file content to be "hello universe" instead.
            </comment_new>
            `,
        assertions: async ({ result, commentReplyEvents, diff, partialResultEvents, checks }) => {
            console.log("diff", diff);
            console.log("partialResultEvents", JSON.stringify(partialResultEvents, null, 2));
            console.log("commentReplyEvents", JSON.stringify(commentReplyEvents, null, 2));
            check(
                checks,
                "README.md content is 'hello universe'",
                diff.includes("hello universe") && diff.includes("README.md"),
                diff,
            );
            check(checks, "both comment reply", commentReplyEvents.length === 2, commentReplyEvents.length);
            check(
                checks,
                "replied to comment 1234509876",
                commentReplyEvents.find((e) => e.comment_id === 1234509876) !== undefined,
            );
            check(
                checks,
                "replied to comment 1234509879",
                commentReplyEvents.find((e) => e.comment_id === 1234509879) !== undefined,
            );
            check(checks, "partial results > 0", partialResultEvents.length > 0);

            check(checks, 'title contains "universe"', !!result.title?.includes("universe"), result.title);
            check(
                checks,
                'description contains "universe"',
                !!result.description?.includes("universe"),
                result.description,
            );
            check(checks, 'title does not contain "world"', !result.title?.includes("world"), result.title);
            check(
                checks,
                'description does not contain "world"',
                !result.description?.includes("world"),
                result.description,
            );
        },
    }),
    createTest({
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "pr_reviews: >1 new comments; disabled copilot_swe_agent_sync_pr_title_description",
        comments: `
            <pr_reviews>
            <comment_thread>
            <file>README.md:2</file>

            <comment_new>
            <comment_id>1234509876</comment_id>
            <author>@me</author>
            What changes did you make to the code?
            </comment_new>

            <comment_new>
            <comment_id>1234509879</comment_id>
            <author>@me</author>
            Change the README.md file content to be "hello universe" instead.
            </comment_new>

            </comment_thread>
            </pr_reviews>
            `,
        configureSettings: async (settings) => {
            // remove copilot_swe_agent_sync_pr_title_description from featureFlags
            settings.featureFlags.copilot_swe_agent_sync_pr_title_description = false;
        },
        assertions: async ({ result, commentReplyEvents, diff, partialResultEvents, checks }) => {
            console.log("diff", diff);
            console.log("partialResultEvents", JSON.stringify(partialResultEvents, null, 2));
            console.log("commentReplyEvents", JSON.stringify(commentReplyEvents, null, 2));
            check(
                checks,
                "README.md content is 'hello universe'",
                diff.includes("hello universe") && diff.includes("README.md"),
                diff,
            );
            check(checks, "to both comments reply", commentReplyEvents.length === 2, commentReplyEvents.length);
            check(
                checks,
                "replied to comment 1234509876",
                commentReplyEvents.find((e) => e.comment_id === 1234509876) !== undefined,
            );
            check(
                checks,
                "replied to comment 1234509879",
                commentReplyEvents.find((e) => e.comment_id === 1234509879) !== undefined,
            );
            check(checks, "partial results > 0", partialResultEvents.length > 0);

            // PR title and description should be updated if the feature flag is enabled
            check(checks, "no title updated", !result.title, result.title);
            check(checks, "no description updated", !result.description, result.description);
        },
    }),
    createTest({
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "pr_comments: >1 new comments",
        comments: `
            <pr_comments>
            <comment_new>
            <comment_id>1234509876</comment_id>
            <author>@me</author>
            What changes did you make to the code?
            </comment_new>

            <comment_new>
            <comment_id>1234509879</comment_id>
            <author>@me</author>
            Change the README.md file content to be "hello universe" instead.
            </comment_new>

            </pr_comments>
            `,
        assertions: async ({ result, commentReplyEvents, diff, partialResultEvents, checks }) => {
            console.log("diff", diff);
            console.log("partialResultEvents", JSON.stringify(partialResultEvents, null, 2));
            console.log("commentReplyEvents", JSON.stringify(commentReplyEvents, null, 2));
            check(
                checks,
                "README.md content is 'hello universe'",
                diff.includes("hello universe") && diff.includes("README.md"),
                diff,
            );
            check(checks, "to both comments reply", commentReplyEvents.length === 2, commentReplyEvents.length);
            check(
                checks,
                "replied to comment 1234509876",
                commentReplyEvents.find((e) => e.comment_id === 1234509876) !== undefined,
            );
            check(
                checks,
                "replied to comment 1234509879",
                commentReplyEvents.find((e) => e.comment_id === 1234509879) !== undefined,
            );
            check(checks, "partial results > 0", partialResultEvents.length > 0);

            check(checks, 'title contains "universe"', !!result.title?.includes("universe"), result.title);
            check(
                checks,
                'description contains "universe"',
                !!result.description?.includes("universe"),
                result.description,
            );
            check(checks, 'title does not contain "world"', !result.title?.includes("world"), result.title);
            check(
                checks,
                'description does not contain "world"',
                !result.description?.includes("world"),
                result.description,
            );
        },
    }),
    createTest({
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "pr_reviews: no action or response needed",
        comments: `
            <pr_reviews>
            <comment_thread>
            <file>README.md:2</file>

            <comment_old>
            <comment_id>1234009876</comment_id>
            <author>@me</author>
            What changes did you make to the code?
            </comment_old>

            <comment_old>
            <comment_id>1234009878</comment_id>
            <author>@copilot</author>
            I added README.md with hello world content.
            </comment_old>

            <comment_new>
            <comment_id>1234009879</comment_id>
            <author>@me</author>
            Thanks!
            </comment_new>

            </comment_thread>
            </pr_reviews>
            `,
        assertions: async ({ result, commentReplyEvents, diff, partialResultEvents, checks }) => {
            console.log("diff", diff);
            console.log("partialResultEvents", JSON.stringify(partialResultEvents, null, 2));
            console.log("commentReplyEvents", JSON.stringify(commentReplyEvents, null, 2));
            check(checks, "No changes made to the repo", diff === "", diff);
            check(checks, "no comment reply", commentReplyEvents.length === 0, commentReplyEvents.length);
            check(checks, "no partial events", partialResultEvents.length === 0, partialResultEvents.length);

            check(checks, "no title updated", !result.title, result.title);
            check(checks, "no description updated", !result.description, result.description);
        },
    }),
    createTest({
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "pr_reviews: action only needed for one comment",
        comments: `
            <pr_reviews>
            <comment_thread>
            <file>README.md:2</file>

            <comment_old>
            <author>@me</author>
            What changes did you make to the code?
            </comment_old>

            <comment_old>
            <author>@copilot</author>
            I added README.md with hello world content.
            </comment_old>

            <comment_new>
            <comment_id>1234509879</comment_id>
            <author>@me</author>
            Thanks!
            </comment_new>
            </comment_thread>
            </pr_reviews>

            </pr_comments>
            <comment_new>
            <comment_id>1234509899</comment_id>
            <author>@me</author>
            Can you also change the README.md file content to be "hello universe" instead?
            </comment_new>
            </pr_comments>
            `,
        assertions: async ({ result, commentReplyEvents, diff, partialResultEvents, checks }) => {
            console.log("diff", diff);
            console.log("partialResultEvents", JSON.stringify(partialResultEvents, null, 2));
            console.log("commentReplyEvents", JSON.stringify(commentReplyEvents, null, 2));
            check(
                checks,
                "README.md content is 'hello universe'",
                diff.includes("hello universe") && diff.includes("README.md"),
                diff,
            );
            check(checks, "1 comment reply", commentReplyEvents.length === 1, commentReplyEvents.length);
            check(
                checks,
                "replied to comment 1234509899",
                commentReplyEvents.find((e) => e.comment_id === 1234509899) !== undefined,
            );
            check(checks, "1 partial result", partialResultEvents.length === 1, partialResultEvents.length);

            check(checks, 'title contains "universe"', !!result.title?.includes("universe"), result.title);
            check(
                checks,
                'description contains "universe"',
                !!result.description?.includes("universe"),
                result.description,
            );
            check(checks, 'title does not contain "world"', !result.title?.includes("world"), result.title);
            check(
                checks,
                'description does not contain "world"',
                !result.description?.includes("world"),
                result.description,
            );
        },
    }),
    createTest({
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "pr_comments: no action or response needed",
        comments: `
            <pr_comments>
            <comment_new>
            <comment_id>1234509876</comment_id>
            <author>@me</author>
            nice work!
            </comment_new>
            </pr_comments>
            `,
        assertions: async ({ result, commentReplyEvents, diff, partialResultEvents, checks }) => {
            console.log("diff", diff);
            console.log("partialResultEvents", JSON.stringify(partialResultEvents, null, 2));
            console.log("commentReplyEvents", JSON.stringify(commentReplyEvents, null, 2));
            check(checks, "No changes made to the repo", diff === "", diff);
            check(checks, "no comment reply", commentReplyEvents.length === 0, commentReplyEvents.length);
            check(checks, "no partial events", partialResultEvents.length === 0, partialResultEvents.length);

            check(checks, "no title updated", !result.title, result.title);
            check(checks, "no description updated", !result.description, result.description);
        },
    }),
    createTest({
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "pr_comments: comment only needed for one comment",
        comments: `
            <pr_comments>
            <comment_new>
            <comment_id>1234509876</comment_id>
            <author>@me</author>
            nice work!
            </comment_new>

            <comment_new>
            <comment_id>1234509879</comment_id>
            <author>@me</author>
            Can you also change the README.md file content to be "hello universe" instead? Thanks!
            </comment_new>
            </pr_comments>
            `,
        assertions: async ({ result, commentReplyEvents, diff, partialResultEvents, checks }) => {
            console.log("diff", diff);
            console.log("partialResultEvents", JSON.stringify(partialResultEvents, null, 2));
            console.log("commentReplyEvents", JSON.stringify(commentReplyEvents, null, 2));
            check(
                checks,
                "README.md content is 'hello universe'",
                diff.includes("hello universe") && diff.includes("README.md"),
                diff,
            );
            check(checks, "1 comment reply", commentReplyEvents.length === 1, commentReplyEvents.length);
            check(
                checks,
                "replied to comment 1234509879",
                commentReplyEvents.find((e) => e.comment_id === 1234509879) !== undefined,
            );
            check(checks, "1 partial result", partialResultEvents.length === 1, partialResultEvents.length);

            check(checks, 'title contains "universe"', !!result.title?.includes("universe"), result.title);
            check(
                checks,
                'description contains "universe"',
                !!result.description?.includes("universe"),
                result.description,
            );
            check(checks, 'title does not contain "world"', !result.title?.includes("world"), result.title);
            check(
                checks,
                'description does not contain "world"',
                !result.description?.includes("world"),
                result.description,
            );
        },
    }),
    createTest({
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "pr_reviews: comment to another user",
        comments: `
            <pr_reviews>
            <comment_thread>
            <file>README.md:2</file>
            <comment_new>
            <comment_id>1234509876</comment_id>
            <author>@me</author>
            Hey @johndoe, does this look good to you?
            </comment_new>
            </comment_thread>
            </pr_reviews>
            `,
        assertions: async ({ result, commentReplyEvents, diff, partialResultEvents, checks }) => {
            console.log("diff", diff);
            console.log("partialResultEvents", JSON.stringify(partialResultEvents, null, 2));
            console.log("commentReplyEvents", JSON.stringify(commentReplyEvents, null, 2));
            check(checks, "No changes made to the repo", diff === "", diff);
            check(checks, "no comment reply", commentReplyEvents.length === 0, commentReplyEvents.length);
            check(checks, "no partial events", partialResultEvents.length === 0, partialResultEvents.length);

            check(checks, "no title updated", !result.title, result.title);
            check(checks, "no description updated", !result.description, result.description);
        },
    }),
    createTest({
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "pr_reviews: comment to another user and copilot",
        comments: `
            <pr_reviews>
            <comment_thread>
            <file>README.md:2</file>
            <comment_new>
            <comment_id>1234509876</comment_id>
            <author>@me</author>
            Hey @johndoe, does this look good to you?
            </comment_new>
            <comment_new>
            <comment_id>1234509877</comment_id>
            <author>@me</author>
            Hey @copilot, Can you also change the README.md file content to be "hello universe" instead?
            </comment_new>
            </comment_thread>
            </pr_reviews>
            `,
        assertions: async ({ result, commentReplyEvents, diff, partialResultEvents, checks }) => {
            console.log("diff", diff);
            console.log("partialResultEvents", JSON.stringify(partialResultEvents, null, 2));
            console.log("commentReplyEvents", JSON.stringify(commentReplyEvents, null, 2));
            check(
                checks,
                "README.md content is 'hello universe'",
                diff.includes("hello universe") && diff.includes("README.md"),
                diff,
            );
            check(checks, "1 comment reply", commentReplyEvents.length === 1, commentReplyEvents.length);
            check(
                checks,
                "replied to comment 1234509877",
                commentReplyEvents.find((e) => e.comment_id === 1234509877) !== undefined,
            );
            check(checks, "1 partial result", partialResultEvents.length === 1, partialResultEvents.length);

            check(checks, 'title contains "universe"', !!result.title?.includes("universe"), result.title);
            check(
                checks,
                'description contains "universe"',
                !!result.description?.includes("universe"),
                result.description,
            );
            check(checks, 'title does not contain "world"', !result.title?.includes("world"), result.title);
            check(
                checks,
                'description does not contain "world"',
                !result.description?.includes("world"),
                result.description,
            );
        },
    }),
    createTest({
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "pr_comments: comment to another user",
        comments: `
            <pr_comments>
            <comment_new>
            <comment_id>1234509876</comment_id>
            <author>@me</author>
            Hey @doejohn, does this look good to you?
            </comment_new>
            </pr_comments>
            `,
        assertions: async ({ result, commentReplyEvents, diff, partialResultEvents, checks }) => {
            console.log("diff", diff);
            console.log("partialResultEvents", JSON.stringify(partialResultEvents, null, 2));
            console.log("commentReplyEvents", JSON.stringify(commentReplyEvents, null, 2));
            check(checks, "No changes made to the repo", diff === "", diff);
            check(checks, "no comment reply", commentReplyEvents.length === 0, commentReplyEvents.length);
            check(checks, "no partial events", partialResultEvents.length === 0, partialResultEvents.length);

            check(checks, "no title updated", !result.title, result.title);
            check(checks, "no description updated", !result.description, result.description);
        },
    }),
    createTest({
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "pr_comments: ask clarification",
        // TODO: Need a better test case for this - LLM makes an assumption on action needed here.
        comments: `
            <pr_comments>
            <comment_new>
            <comment_id>1234509876</comment_id>
            <author>@me</author>
            Can you change
            </comment_new>
            </pr_comments>
            `,
        assertions: async ({ result, commentReplyEvents, diff, partialResultEvents, checks }) => {
            console.log("diff", diff);
            console.log("partialResultEvents", JSON.stringify(partialResultEvents, null, 2));
            console.log("commentReplyEvents", JSON.stringify(commentReplyEvents, null, 2));
            check(checks, "No changes made to the repo", diff === "", diff);
            check(checks, "question comment reply", commentReplyEvents.length === 1, commentReplyEvents.length);
            check(
                checks,
                "replied to comment 1234509876",
                commentReplyEvents.find((e) => e.comment_id === 1234509876) !== undefined,
            );
            check(
                checks,
                "comment reply is a question",
                commentReplyEvents.find((e) => e.comment_id === 1234509876)?.message.includes("?") === true,
            );
            check(checks, "no partial events", partialResultEvents.length === 0, partialResultEvents.length);

            check(checks, "no title updated", !result.title, result.title);
            check(checks, "no description updated", !result.description, result.description);
        },
    }),
];

describe("basic commentReply evals", () => {
    test.concurrent.for(testCases)("$name", { timeout: DEFAULT_TIMEOUT_MS }, runTest);
});
