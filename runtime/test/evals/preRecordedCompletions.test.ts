/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { readFileSync } from "fs";
import OpenAI from "openai";
import { join } from "path";
import { ReportProgressInput, StrReplaceEditorArgs } from "../../src/tools";
import { inflatePreRecordedCompletion } from "../preRecordedCompletions";
import { check, DEFAULT_TIMEOUT_MS, runTest, TestCase } from "./eval";

const numPreRecordedToolCalls = 4;

const desiredReadmeContent = `## Sentiment Analysis CLI Tool

A simple Python-based sentiment analysis CLI tool using a "contains words" heuristic.

### Usage

\`\`\`bash
# Analyze sentiment for a text
python3 sentiment.py "I love this product"
# Output: positive
\`\`\`
`;

let returnedPreRecordedCompletions: OpenAI.Chat.Completions.ChatCompletion[] | undefined = undefined;

const testCases: TestCase[] = [
    {
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "Pre-recorded completions functionality works",
        repo: {
            "README.md": "Sentiment Analysis CLI",
        },
        commit: null,
        problemDescription: `----
        *This section details on the original issue you should resolve*

        <issue_title>
        Sentiment Analysis CLI
        </issue_title>

        <issue_description>
        We're going to create a command-line interface (CLI) for sentiment analysis.

        This CLI will take in text input and return the sentiment of the text (positive, negative, or neutral). The sentiment analysis will use a
        heuristic approach, such as counting positive and negative words.

        For now, we simply want the README to be initialized with a basic description of what we're going to build in order to help guide our development.

        <comments>

        </comments>
        `,
        runDefaultAssertions: false,
        preRecordedCompletions: (testLocation) => {
            const preRecordedCompletions = [
                inflatePreRecordedCompletion({
                    type: "tool-calls",
                    content: "Let's start by exploring the repository structure.",
                    toolsToCall: [
                        {
                            tool: "str_replace_editor",
                            args: {
                                command: "view",
                                path: testLocation,
                                view_range: undefined,
                            } satisfies StrReplaceEditorArgs,
                        },
                    ],
                }),
                inflatePreRecordedCompletion({
                    type: "tool-calls",
                    content:
                        "It looks like there's already a README and that's all there is. I should view it to see what it currently contains.",
                    toolsToCall: [
                        {
                            tool: "str_replace_editor",
                            args: {
                                command: "view",
                                path: join(testLocation, "README.md"),
                                view_range: undefined,
                            } satisfies StrReplaceEditorArgs,
                        },
                    ],
                }),
                inflatePreRecordedCompletion({
                    type: "tool-calls",
                    content:
                        "Ok, now I'll add some reasonable content to the README.md to help guide future development.",
                    toolsToCall: [
                        {
                            tool: "str_replace_editor",
                            args: {
                                command: "str_replace",
                                path: join(testLocation, "README.md"),
                                old_str: "Sentiment Analysis CLI",
                                new_str: desiredReadmeContent,
                            } satisfies StrReplaceEditorArgs,
                        },
                    ],
                }),
                inflatePreRecordedCompletion({
                    type: "tool-calls",
                    content: "Now I'll report the progress with the PR details.",
                    toolsToCall: [
                        {
                            tool: "report_progress",
                            args: {
                                commitMessage: "Initialize README with sentiment analysis CLI description",
                                prDescription: `- [x] Initialize README.md with basic description of sentiment analysis CLI tool
- [x] Add usage example showing expected CLI interface
- [x] Document that tool will use heuristic approach for sentiment analysis
- [x] Specify expected output format (positive, negative, neutral)`,
                            } satisfies ReportProgressInput,
                        },
                    ],
                }),
                inflatePreRecordedCompletion({
                    type: "stop-message",
                    content:
                        "The README.md has been updated with a basic description of the sentiment analysis CLI tool. My work here is done!",
                }),
            ];

            const toolCallsInPreRecordedCompletions = preRecordedCompletions.filter((completion) =>
                completion.choices.find((c) => c.message.tool_calls !== undefined && c.message.tool_calls.length > 0),
            );
            if (toolCallsInPreRecordedCompletions.length !== numPreRecordedToolCalls) {
                throw new Error(
                    `Expected ${numPreRecordedToolCalls} tool calls in pre-recorded completions, but found ${toolCallsInPreRecordedCompletions.length}`,
                );
            }

            returnedPreRecordedCompletions = preRecordedCompletions;

            return returnedPreRecordedCompletions;
        },
        assertions: async ({ repoPath, checks, toolCallMessages }) => {
            check(checks, "Pre-recorded completions were returned", returnedPreRecordedCompletions !== undefined);

            // The LLM sometimes likes to have a thought before producing a PR description.
            const nonThoughtToolCallMessages = toolCallMessages.filter((m) => m.function.name !== "think");

            check(
                checks,
                "Number of tool calls matches our pre-recorded completions",
                nonThoughtToolCallMessages.length === numPreRecordedToolCalls,
            );

            const finalReadmeContent = readFileSync(join(repoPath, "README.md"), "utf-8");
            check(
                checks,
                "README.md was updated with the expected content",
                finalReadmeContent === desiredReadmeContent,
            );
        },
    },
];

describe("timeout evals", () => {
    test.concurrent.for(testCases)("$name", { timeout: DEFAULT_TIMEOUT_MS }, runTest);
});
