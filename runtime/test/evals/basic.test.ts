/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

/**
 * The tests in this file are non-deterministic evals to constrain the behaviour of the agent.
 * They may not all pass all the time, but should pass "most" of the time.
 * They should be skipped in PR/main CI, but will be run in nightlies.
 */
import { readFileSync } from "fs";
import fs from "fs/promises";
import { isToolMessageEvent } from "../../src/model/event";
import { StrReplaceEditorArgs } from "../../src/tools";
import { DEFAULT_TIMEOUT_MS, TestCase, applyPatch, check, execCommand, getModifiedFilesInDiff, runTest } from "./eval";

const testCases: TestCase[] = [
    {
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "Does call str_replace to patch, not create",
        repo: "ghcpd/react-router-test-app",
        commit: "7575e5e121e285cc44d51cfd6a6c0f1f0aa06185",
        problemDescription: `----
*This section details on the original issue you should resolve*

<issue_title>
Change the home page title and description
</issue_title>

<issue_description>
The Home page title should say "Copilot for Developers" and Description should say "Copilot for Developers is a new way to build and ship software faster.".
</issue_description>

## Comments on the Issue (you are @copilot in this section)

<comments>

</comments>
`,
        assertions: async ({ repoPath, checks, toolCallMessages }) => {
            let usedRepoBuildCount = 0;
            let usedDepInstallCount = 0;
            let usedBuildBeforeReplace = false;
            let usedBuildAfterReplace = false;
            let usedStrReplaceCount = 0;
            let usedCreateCount = 0;

            for (const toolCall of toolCallMessages) {
                if (toolCall.function.name === "bash") {
                    // Check if the command is a build command
                    const args = toolCall.function.arguments;
                    if (args.includes("npm run build")) {
                        usedRepoBuildCount++;
                        if (usedStrReplaceCount === 0) {
                            usedBuildBeforeReplace = true;
                        } else {
                            usedBuildAfterReplace = true;
                        }
                    }

                    if (args.includes("npm install")) {
                        usedDepInstallCount++;
                    }
                }

                // function: {arguments: '{"command":"str_replace","path":"/tmp/sweag…nd ship software faster.\\" },\\n  ];\\n}"}', name: 'str_replace_editor'}

                if (toolCall.function.name == "str_replace_editor") {
                    const args = toolCall.function.arguments;
                    try {
                        const argsObj = JSON.parse(args);
                        if (argsObj?.command === "str_replace" && argsObj?.path?.endsWith("app/routes/home.tsx")) {
                            usedStrReplaceCount++;
                        }

                        // shouldn't be creating anything
                        if (argsObj?.command === "create") {
                            usedCreateCount++;
                        }
                    } catch {
                        // Ignore JSON parse errors
                    }
                }
            }

            check(checks, "Does use dependency install", usedDepInstallCount > 0, `Used ${usedDepInstallCount} times`);
            check(
                checks,
                "Does use existing build to run a build more than once",
                usedRepoBuildCount > 1,
                `Used ${usedRepoBuildCount} times`,
            );
            check(checks, "Does use build before str_replace", usedBuildBeforeReplace);
            check(checks, "Does use build after str_replace", usedBuildAfterReplace);

            check(
                checks,
                "Does use str_replace to patch the file",
                usedStrReplaceCount > 0,
                `Used ${usedStrReplaceCount} times`,
            );
            check(
                checks,
                "Does not use str_replace to create a file",
                usedCreateCount === 0,
                `Used ${usedCreateCount} times`,
            );

            // file is updated with the new title and description
            try {
                const homeFile = `${repoPath}/app/routes/home.tsx`;
                const homeFileContent = await fs.readFile(homeFile);
                check(checks, "home.tsx file exists", homeFileContent !== undefined);
                check(
                    checks,
                    "home.tsx title is updated",
                    homeFileContent.toString().includes("Copilot for Developers"),
                    homeFileContent.toString(),
                );
                check(
                    checks,
                    "home.tsx description is updated",
                    homeFileContent
                        .toString()
                        .includes("Copilot for Developers is a new way to build and ship software faster."),
                    homeFileContent.toString(),
                );
                check(
                    checks,
                    "home.tsx old title and description are removed",
                    !homeFileContent.toString().includes("New React Router App") &&
                        !homeFileContent.toString().includes("Welcome to React Router"),
                    homeFileContent.toString(),
                );
            } catch {
                // Ignore file not found errors
                check(checks, "check for changes in home.tsx file", false);
            }

            // project is still buildable after the changes
            try {
                const buildOutput = await execCommand("npm run build", repoPath);
                check(
                    checks,
                    "project is still buildable after the changes",
                    !buildOutput.includes("Error") && !buildOutput.includes("Fail"),
                    buildOutput,
                );
            } catch (e) {
                check(
                    checks,
                    "project is still buildable after the changes",
                    false,
                    `Error running build command: ${e}`,
                );
            }
        },
    },
    {
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "Can create a test in existing tests if test suite is present",
        repo: "ghcpd/react-router-test-app",
        commit: "b6004cead80259ed8d6f8b7be104dd9b0a8a85f5",
        problemDescription: `----
*This section details on the original issue you should resolve*

<issue_title>
Provide example input and output for the prompt
</issue_title>

<issue_description>
Under the prompt input box, add side by side multi line input boxes that are in a collapsible display.

First input would take an input example that should be sent with the submit action.

The second input would be the how the problem output of the execution should look like. This should also be included in the submit action.
</issue_description>

## Comments on the Issue (you are @copilot in this section)

<comments>

</comments>
`,
        assertions: async ({ result, diff, checks, toolCallMessages }) => {
            console.log("Result:");
            console.log(JSON.stringify(result, null, 2));

            const modifiedFiles = getModifiedFilesInDiff(diff);

            check(
                checks,
                "test/PromptPlayground.test.tsx is changed",
                modifiedFiles.includes("test/PromptPlayground.test.tsx"),
                `Changed test files: ${modifiedFiles.join(", ")}`,
            );
            check(
                checks,
                "test/promptSlice.test.ts is changed",
                modifiedFiles.includes("test/promptSlice.test.ts"),
                `Changed test files: ${modifiedFiles.join(", ")}`,
            );
            check(
                checks,
                "test/store.test.ts is changed",
                modifiedFiles.includes("test/store.test.ts"),
                `Changed test files: ${modifiedFiles.join(", ")}`,
            );

            let usedTestCount = 0;

            for (const toolCall of toolCallMessages) {
                if (toolCall.function.name === "bash") {
                    // Check if the command is a build command
                    const args = toolCall.function.arguments;
                    if (args.includes("npm test") || args.includes("npm run test")) {
                        usedTestCount++;
                    }
                }
            }
            check(checks, "Does use test to validate", usedTestCount > 0, `Used ${usedTestCount} times`);
        },
    },
    {
        // uses a hard coded black bird insights directly in the problem statement
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "can address full test generation requested",
        testDescription: "All tests requested are generated and no unnecessary changes are made",
        repo: "github/sweagentd",
        commit: "ab92b4868c47c03b7510cd1b36a4f7e81bf828fe",
        branch: "copilot/fix-3010-2",
        issueNumber: 3010,
        // the tests for this repo require tokens and auth, so for the test, we'll just disable them
        problemDescription: readFileSync("test/evals/problem_statements/create-full-tests.md", "utf-8"),
        assertions: async ({ result, diff, checks }) => {
            console.log("Result:");
            console.log(JSON.stringify(result, null, 2));

            const modifiedFiles = getModifiedFilesInDiff(diff);

            // there should only be one test file modified
            check(
                checks,
                "runtime/test/model/capi-chat-completion-client.test.ts is the only file modified",
                modifiedFiles.length === 1 &&
                    modifiedFiles[0] === "runtime/test/model/capi-chat-completion-client.test.ts",
                `Modified files: ${modifiedFiles.join(", ")}`,
            );

            // look for completness of the test generation - test criteria in the problem statement
            const expectedWords = [
                "413",
                "copilot_swe_agent_vision",
                "data:image/png;base64",
                // 'truncation',
                "429",
                // "1P"
            ];

            for (const word of expectedWords) {
                check(checks, `diff contains test for ${word}`, diff.includes(word));
            }
        },
    },
    {
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "lukehoban full calclang function implementation",
        testDescription:
            "ensure that the agent implements evaluator as well as parser changes, and includes test coverage for both",
        repo: "ghcpd/calclang",
        commit: "480526ac16d881cbbb6b8466d9f852b2e5aac1f2",
        problemDescription: `----
*This section details on the original issue you should resolve*

<issue_title>
Add support for functions
</issue_title>

<issue_description>
Add support for function like \`FUN x ADD x 1 2\` which evaluates to \`3\`.
</issue_description>

## Comments on the Issue (you are @copilot in this section)

<comments>

</comments>`,
        assertions: async ({ repoPath, diff, checks }) => {
            const modifiedFiles = getModifiedFilesInDiff(diff);

            check(
                checks,
                "main.go is modified",
                modifiedFiles.includes("main.go"),
                `Modified files: ${modifiedFiles.join(", ")}`,
            );
            check(
                checks,
                "main_test.go is modified",
                modifiedFiles.includes("main_test.go"),
                `Modified files: ${modifiedFiles.join(", ")}`,
            );

            // patch the tests to validate the implementation was done correctly
            // we want to make sure that both the parser and evaluator are implemented
            const testPatch = `diff --git a/main_test.go b/main_test.go
index 9f68f3f..30801ab 100644
--- a/main_test.go
+++ b/main_test.go
@@ -22,3 +22,26 @@ func TestParse(t *testing.T) {
 		})
 	}
 }
+
+func TestEval(t *testing.T) {
+	tests := []struct {
+		input    string
+		expected int
+	}{
+		{"ADD 1 2", 3},
+		{"SUB 5 3", 2},
+		{"FUN x ADD x 1 2", 3}, // x=2, ADD 2 1 = 3
+		{"FUN y SUB y 3 5", 2}, // y=5, SUB 5 3 = 2
+	}
+
+	for _, tc := range tests {
+		t.Run(tc.input, func(t *testing.T) {
+			ast := Parse(tc.input)
+			if ast == nil {
+				t.Fatalf("Parse returned nil for input: %s", tc.input)
+			}
+			result := Eval(ast)
+			assert.Equal(t, tc.expected, result)
+		})
+	}
+}
`;

            const diffLines = diff.split("\n");

            // Check if the public contracts are updated
            // NOTE: this is slightly brittle, but we want to ensure that the Eval and Parse functions are touched on.
            check(
                checks,
                "updates Eval function",
                diffLines.some((line) => line.includes("func Eval(ast AST)")),
            );
            check(
                checks,
                "updates Parse function",
                diffLines.some((line) => line.includes("func Parse(input string)")),
            );

            const addedFUN = diffLines.filter((line) => line.includes('"FUN"'));
            check(
                checks,
                '"FUN" is added to parser',
                addedFUN.length > 0 && addedFUN.every((line) => line.startsWith("+")),
            );
            const addedEval = diffLines.filter((line) => line.includes("return Eval"));
            check(
                checks,
                "Eval updated to handle FUN",
                addedEval.length > 0 && addedEval.every((line) => line.startsWith("+")),
            );

            try {
                await execCommand(`git checkout 480526ac16d881cbbb6b8466d9f852b2e5aac1f2 -- main_test.go`, repoPath);
                await applyPatch(repoPath, testPatch);
                const testOutput = await execCommand("go test -v ./...", repoPath);
                check(checks, "go tests pass", !testOutput.includes("FAIL"), testOutput);
            } catch (error) {
                check(checks, "go tests pass", false, error);
            }
        },
    },
    {
        runIfEnvVar: "NIGHTLY_TEST_CI",
        name: "Promptly Starts Work in a Fresh Repo",
        repo: {
            "README.md": "# Connect Four Node",
        },
        commit: null,
        runDefaultAssertions: true,
        problemDescription: `----
*This section details on the original issue you should resolve*

<issue_title>
Implement Beginnings of Application
</issue_title>

<issue_description>
Begin our implementation of a CLI based version of Connect Four!

- This version of Connect Four should be played via the command line.
- As of right now, it should only involve one human player, who plays against an AI opponent, with the human always going first.
- That AI opponent should have a very basic strategy of choosing a random column to drop one of their pieces in (choosing another column if the chosen column is full).

Technical requirements:
- Implement in TypeScript/NodeJS
- Have strong test coverage of the codebase
- Have the README include clear instructions on how to use build and run
- Have tests which confirm the validity of the app usage examples
</issue_description>

## Comments on the Issue (you are @copilot in this section)

<comments>

</comments>
        `,
        configureSettings: async (settings) => {
            settings.timeoutMs = 1000 * 60 * 60;
        },
        assertions: async ({ progressEvents, checks }) => {
            const strReplaceEditorCommandsThatWriteChanges: StrReplaceEditorArgs["command"][] = [
                "str_replace",
                "create",
                "insert",
            ];

            // Check to make sure a file was edited in the first 10 turns
            const toolMessageEventsForStrReplaceEditor = progressEvents
                .filter((e) => e.kind === "message")
                .filter((e) => isToolMessageEvent(e))
                .filter(
                    (e) =>
                        e.telemetry?.properties.toolName === "str_replace_editor" &&
                        strReplaceEditorCommandsThatWriteChanges.includes(
                            e.telemetry.properties.command as StrReplaceEditorArgs["command"],
                        ),
                );
            const lowestTurnToolMessageEventForStrReplaceEditor = toolMessageEventsForStrReplaceEditor.reduce(
                (min, e) => (e.turn !== undefined && e.turn < min ? e.turn : min),
                Number.MAX_SAFE_INTEGER,
            );

            const limit = 15;
            check(
                checks,
                `A file was edited in the first ${limit} turns`,
                lowestTurnToolMessageEventForStrReplaceEditor <= limit,
                `Lowest turn for str_replace_editor tool message event was not < ${limit}, it was: ${lowestTurnToolMessageEventForStrReplaceEditor}`,
            );
        },
    },
];

describe("basic evals", () => {
    test.concurrent.for(testCases)("$name", { timeout: DEFAULT_TIMEOUT_MS }, runTest);
});
