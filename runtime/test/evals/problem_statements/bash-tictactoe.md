---

_This section details on the original issue you should resolve_

<issue_title>
Create a Tic Tac Toe console application
</issue_title>

<issue_description>
Implement an interactive command line Tic Tac Toe game using Node JS that allows one player to play against a simple computer player.

On launch the game should prompt the user each turn to input their move in the format "row,column" and then update the game board when the user hits enter. E.g. "1,2" for row 1 and column 2.

Then, the computer player should make its move by randomly selecting an empty cell on the board.
</issue_description>

## Comments on the Issue (you are @copilot in this section)

<comments>

</comments>
