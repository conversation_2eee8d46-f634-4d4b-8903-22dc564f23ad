---

_This section details on the original issue you should resolve_

<issue_title>
Feature Request: Support to see if a setting is changed from the default value
</issue_title>

<issue_description>
One of the nice things about VS Code's built-in settings editor is that you can see if a setting has been changed from the default, and you can easily reset it back to the default value. We should have a similar capability in the extension. When a setting is changed, mark it (somehow) that it is modified and provide a way for users to reset it back to its default value.
</issue_description>

## Comments on the Issue (you are @copilot in this section)

<comments>

</comments>
