---

_This section details on the original issue you should resolve_

<issue_title>
crea
</issue_title>

<issue_description>
Create a Visual Studio Code Extension that exposes a public command (for example to be called in tasks), the extension is to be created in Typescript and should use yeoman to scaffold the skeleton of the extension

This is a new extension, don't add functionality to the existing one. It should be created in windowsTokenVault folder

These are the extension characteristics

- The command should be called getGitHubTokenWindowsVault
- The extension should fetch from windows secure vault a parameter called GITHUB_TOKEN
  </issue_description>

## Comments on the Issue (you are @Copilot in this section)

<comments>

</comments>
