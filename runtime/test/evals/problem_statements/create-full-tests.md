---

_This section details on the original issue you should resolve_

<issue_title>
Add coverage to the mock capi-chat-completion-client.test.ts
</issue_title>

<issue_description>
Add tests to runtime/test/model/capi-chat-completion-client.test.ts
Current test coverage for ChatCompletionClient: **65.47% line coverage** (67.67% functions, 70.58% branches).

**Areas covered:** Text responses, error handling & retry logic (500/429 errors), tool calling workflows, token limit context truncation, tool validation (invalid JSON, non-existent tools), and event emission patterns.

**Areas not yet covered:** -

- **Image processing logic** (lines 562-747): Vision capabilities, image URL extraction, base64 image upload to GitHub attachments, image tag/markdown parsing
- **Advanced error scenarios**: 413 payload too large errors, image removal logic, network timeout handling
- **Model capability detection**: Default model fallback when listModels fails, vision support detection
- **Complex context management**: Advanced truncation edge cases, tool token budget allocation
- **Tool execution edge cases**: Binary result handling for LLM, tool callback option specifics, 1P vs 3P tool telemetry
- **Stream/chunk processing**: Session log generation from tool results
- **Retry policy variations**: Different retry-after header formats, backoff growth calculations
  </issue_description>

## Comments on the Issue (you are @copilot in this section)

<comments>

</comments>

<repository_context>
Here are relevant code snippets from the repository that might help with this task:
[
{
"contents": "File: `runtime/test/model/capi-chat-completion-client.test.ts`\n`typescript\n/*...*/\n            // Final response after tool execution\n            const finalResponse = createMockCompletionResponse({\n                id: 'chatcmpl-final',\n                choices: [{\n                    index: 0,\n                    message: {\n                        role: 'assistant',\n                        content: 'I handled the invalid JSON gracefully!',\n                        refusal: null\n                    },\n                    finish_reason: 'stop',\n                    logprobs: null\n                }]\n            });\n\n            mockCreate\n                .mockReturnValueOnce({\n                    withResponse: vi.fn().mockResolvedValue({\n                        data: toolCallResponse,\n                        request_id: 'req-tool-invalid'\n                    })\n                })\n                .mockReturnValueOnce({\n                    withResponse: vi.fn().mockResolvedValue({\n                        data: finalResponse,\n                        request_id: 'req-final-invalid'\n                    })\n                });\n\n            // Act\n            const events = await getCompletionWithTools(\n                client,\n                'You are a helpful assistant.',\n                [{ role: 'user', content: 'Use the test tool with invalid JSON' }],\n                [testTool]\n            );\n\n            // Assert\n            expect(events.length).toBeGreaterThan(4);\n            /*...*/\n`File: `runtime/test/model/capi-chat-completion-client.test.ts`\n`typescript\n/*...*/\n            const mockResponse = createMockCompletionResponse({\n                id: 'chatcmpl-test',\n                choices: [{\n                    index: 0,\n                    message: {\n                        role: 'assistant',\n                        content: 'Hello, this is a test response!',\n                        refusal: null\n                    },\n                    finish_reason: 'stop',\n                    logprobs: null\n                }]\n            });\n\n            mockCreate.mockReturnValue({\n                withResponse: vi.fn().mockResolvedValue({\n                    data: mockResponse,\n                    request_id: 'req-test'\n                })\n            });\n\n            // Act\n            const events = await getCompletionWithTools(\n                client,\n                'You are a helpful assistant.',\n                [{ role: 'user', content: 'Hello!' }]\n            );\n\n            // Assert\n            expect(events).toHaveLength(3); // session_log + response + telemetry\n            expect(events[0].kind).toBe('session_log');\n            expect(events[1].kind).toBe('response');\n            expect(events[2].kind).toBe('telemetry');\n\n            const responseEvent = events.find(e => e.kind === 'response');\n            /*...*/\n`File: `runtime/test/model/capi-chat-completion-client.test.ts`\n`typescript\n/*...*/\n                        data: finalResponse,\n                        request_id: 'req-final'\n                    })\n                });\n\n            // Act\n            const events = await getCompletionWithTools(\n                client,\n                'You are a helpful assistant.',\n                [{ role: 'user', content: 'Use the test tool' }],\n                [testTool]\n            );\n\n            // Assert\n            expect(events.length).toBeGreaterThan(4); // At least: session_log, message (assistant), message (tool), session_log, response, telemetry\n\n            // Check that we got the tool call message\n            const assistantMessage = events.find(e => e.kind === 'message' && e.message.role === 'assistant') as any;\n            expect(assistantMessage).toBeDefined();\n            expect(assistantMessage?.message.tool_calls).toHaveLength(1);\n\n            // Check that we got the tool response message  \n            const toolMessage = events.find(e => e.kind === 'message' && e.message.role === 'tool') as any;\n            expect(toolMessage).toBeDefined();\n            expect(toolMessage?.message.content).toBe('Tool executed successfully!');\n            /*...*/\n`",
"languageName": "TypeScript",
"path": "runtime/test/model/capi-chat-completion-client.test.ts",
"range": {
"start": 162,
"end": 738
},
"ref": "refs/heads/main",
"type": "snippet",
"url": "https://github.com/github/sweagentd/blob/ab92b4868c47c03b7510cd1b36a4f7e81bf828fe/runtime/test/model/capi-chat-completion-client.test.ts#L162-L738"
},
{
"contents": "File: `runtime/src/model/capi/chat-completion-client.ts`\n``typescript\n/*...*/\nexport class ChatCompletionClient implements Client {\n    /*...*/\n    public async* getCompletionWithTools(systemMessage: string, initialMessages: ChatCompletionMessageParam[], tools: Tool[], options?: GetCompletionWithToolsOptions): AsyncGenerator<Event> {\n        /*...*/\n                        this.logger.debug(`${i}: ${JSON.stringify(messages[i], null, 2)}`);\n                    }\n\n                    // Include the structured error if that's the cause of failures. Structured error help surface details to users via agent sessions\n                    // e.g. 429 errors being retried but eventually failed\n                    throw new Error(`Failed to get response from the AI model; retried ${retry - 1} times (total retry wait time: ${totalRetryWaitTime} seconds)`, { cause: lastAPIError });\n                }\n\n                yield { kind: \"session_log\", sessionLog: completionToChunk(response), modelCallDurationMs: turnTelemetry.metrics.modelCallDurationMs ?? 0, requestMessages: JSON.stringify(requestMessages) };\n\n                if (this.isToolCallResponse(response)) {\n                    /*...*/\n    }\n    /*...*/\n}\n/*...*/\n``",
"languageName": "TypeScript",
"path": "runtime/src/model/capi/chat-completion-client.ts",
"range": {
"start": 350,
"end": 360
},
"ref": "refs/heads/main",
"type": "snippet",
"url": "https://github.com/github/sweagentd/blob/ab92b4868c47c03b7510cd1b36a4f7e81bf828fe/runtime/src/model/capi/chat-completion-client.ts#L350-L360"
},
{
"contents": "File: `runtime/test/model/capi-ai.test.ts`\n``typescript\n/*...*/\n                    content: `What text is in this image? ![alt text](https://github.com/user-attachments/assets/9300859f-46c9-4d16-80b0-c9fd0f38adb3)`\n                },\n            ],\n            []\n        );\n        for await (const event of result) {\n            if (event.kind == \"response\") {\n                expect(event.response.content?.toLowerCase()).toContain(\"hello\");\n                return;\n            }\n        }\n\n        expect.fail(\"Expected a response\");\n    });\n\n    test('agent calls take screenshot tool and converts base64 image to image url', { timeout: DEFAULT_TIMEOUT_MS }, async () => {\n        const logger = new ConsoleLogger();\n        process.env.GITHUB_USER_ID = \"1885174\";\n        const settings = await getOrInitSettings();\n\n        settings.github = {\n            serverUrl: \"https://github.com\",\n            token: process.env.GITHUB_TOKEN,\n            repo: {\n                id: 3,\n            }\n        };\n\n        settings.featureFlags = {};\n        settings.featureFlags['copilot_swe_agent_vision'] = true;\n        /*...*/\n``File: `runtime/test/model/capi-ai.test.ts`\n`typescript\n/*...*/\n                expect(event.response.content?.toLowerCase()).not.toContain(\"https://github.com/user-attachments/assets/\");\n                return;\n            }\n        }\n\n        expect.fail(\"Expected a response\");\n    });\n\n    describe.skipIf(process.env.DISABLE_CAPI_TESTS === 'true')('image repo utility methods', async () => {\n        const settings = await getOrInitSettings();\n\n        settings.github = {\n            serverUrl: \"https://github.com\",\n        };\n\n        const serverUrl = settings.github.serverUrl ?? \"https://github.com\";\n\n        describe('isRelativePath', () => {\n            it('returns true for simple relative image paths', () => {\n                expect(isImageInRepoPath('images/photo.png')).toBe(true);\n            });\n\n            it('returns true for simple relative image paths beginning with \"./\"', () => {\n                expect(isImageInRepoPath('./images/photo.png')).toBe(true);\n            });\n\n            it('returns false for non-image paths', () => {\n                expect(isImageInRepoPath('docs/readme.md')).toBe(false);\n                expect(isImageInRepoPath('archive.zip')).toBe(false);\n            });\n            /*...*/\n`File: `runtime/test/model/capi-ai.test.ts`\n``typescript\n/*...*/\n                    content: `Can you take a screenshot of the https://some-test-page.org/ page, include it in PR description and describe what is there?`,\n                },\n            ],\n            tools\n        );\n\n        for await (const event of result) {\n            if (event.kind === \"message\" && event.message.role === \"user\" && Array.isArray(event.message.content)) {\n                expect(event.message.content.length).toBeGreaterThan(0);\n                expect(event.message.content[0].type).toBe(\"text\");\n                expect(event.message.content[1].type).toBe(\"image_url\");\n            }\n            if (event.kind == \"response\") {\n                expect(event.response.content?.toLowerCase()).toContain(\"hello\");\n                expect(event.response.content?.toLowerCase()).toContain(\"https://github.com/user-attachments/assets/\");\n                return;\n            }\n        }\n\n        expect.fail(\"Expected a response\");\n    });\n\n    test('agent calls take screenshot tool and converts base64 image to image url - image exceeding limits', { timeout: DEFAULT_TIMEOUT_MS }, async () => {\n        const logger = new ConsoleLogger();\n        /*...*/\n``",
"languageName": "TypeScript",
"path": "runtime/test/model/capi-ai.test.ts",
"range": {
"start": 321,
"end": 491
},
"ref": "refs/heads/main",
"type": "snippet",
"url": "https://github.com/github/sweagentd/blob/ab92b4868c47c03b7510cd1b36a4f7e81bf828fe/runtime/test/model/capi-ai.test.ts#L321-L491"
},
{
"contents": "File: `runtime/src/model/capi/chat-completion-client.ts`\n``typescript\n/*...*/\nexport class ChatCompletionClient implements Client {\n    /*...*/\n    public async* getCompletionWithTools(systemMessage: string, initialMessages: ChatCompletionMessageParam[], tools: Tool[], options?: GetCompletionWithToolsOptions): AsyncGenerator<Event> {\n        /*...*/\n                        // the rest we should rethrow like 400 etc.\n\n                        const errorToThrow = apiError || error;\n                        this.logger.error(`error`);\n                        this.logger.error(JSON.stringify(errorToThrow, null, 2));\n\n                        throw errorToThrow;\n                    } finally {\n                        this.logger.endGroup(LogLevel.Debug);\n                    }\n\n                } while (!response && retry++ < maxRetries);\n\n                if (!response) {\n                    // Log some recent messages to help with debugging\n                    this.logger.debug(`Recent messages prior to failure:`);\n                    for (let i = Math.max(0, messages.length - 6); i < messages.length; i++) {\n                        // CAPI error messages often reference messages at a specific index, so we print the index along with the message\n                        /*...*/\n    }\n    /*...*/\n}\n/*...*/\n``File: `runtime/src/model/capi/chat-completion-client.ts`\n``typescript\n/*...*/\nexport class ChatCompletionClient implements Client {\n    /*...*/\n    public async* getCompletionWithTools(systemMessage: string, initialMessages: ChatCompletionMessageParam[], tools: Tool[], options?: GetCompletionWithToolsOptions): AsyncGenerator<Event> {\n        /*...*/\n                        this.logger.debug(`${i}: ${JSON.stringify(messages[i], null, 2)}`);\n                    }\n\n                    // Include the structured error if that's the cause of failures. Structured error help surface details to users via agent sessions\n                    // e.g. 429 errors being retried but eventually failed\n                    throw new Error(`Failed to get response from the AI model; retried ${retry - 1} times (total retry wait time: ${totalRetryWaitTime} seconds)`, { cause: lastAPIError });\n                }\n\n                yield { kind: \"session_log\", sessionLog: completionToChunk(response), modelCallDurationMs: turnTelemetry.metrics.modelCallDurationMs ?? 0, requestMessages: JSON.stringify(requestMessages) };\n\n                if (this.isToolCallResponse(response)) {\n                    /*...*/\n    }\n    /*...*/\n}\n/*...*/\n``File: `runtime/src/model/capi/chat-completion-client.ts`\n``typescript\n/*...*/\nexport class ChatCompletionClient implements Client {\n    /*...*/\n    public async* getCompletionWithTools(systemMessage: string, initialMessages: ChatCompletionMessageParam[], tools: Tool[], options?: GetCompletionWithToolsOptions): AsyncGenerator<Event> {\n        /*...*/\n                                this.logger.debug(`Adjusted model token limit to ${modelTokenLimit}, token limit retry buffer to ${tokenLimitRetryBuffer}`);\n                            } else if (apiError.status === 413) {\n                                // Error 413 indicates that the request is too large - only known cause of this is that an attached image is too large\n                                // For today, we'll handle this by removing all of the images from the request\n                                // TODO: Improve handling of large image attachments (https://github.com/github/sweagentd/issues/2407)\n                                this.logger.debug(`Request is too large, trying to remove images from the request`);\n                        /*...*/\n    }\n    /*...*/\n}\n/*...*/\n``",
"languageName": "TypeScript",
"path": "runtime/src/model/capi/chat-completion-client.ts",
"range": {
"start": 291,
"end": 360
},
"ref": "refs/heads/main",
"type": "snippet",
"url": "https://github.com/github/sweagentd/blob/ab92b4868c47c03b7510cd1b36a4f7e81bf828fe/runtime/src/model/capi/chat-completion-client.ts#L291-L360"
},
{
"contents": "File: `runtime/src/model/capi/chat-completion-client.ts`\n``typescript\n/*...*/\nexport class ChatCompletionClient implements Client {\n    /*...*/\n    private async imageMessagesIfAllowed(messages: ChatCompletionMessageParam[], processedImageUrls: Set<string>): Promise<[ChatCompletionMessageParam[], TelemetryMetrics]> {\n        /*...*/\n                            imgUrls.push(...imgRefsSmallerThanMaxSize);\n                        } else if (content.type === 'image_url') {\n                            if (content.image_url.url.startsWith('data:')) {\n                                const [uploadResult, imageSize, error] = await this.extractAndUploadImageData(content, maxImageSize);\n\n                                if (error instanceof ImageSizeError) {\n                                    imagesRemovedDueToSize++;\n                                    // We might want to log it to to see what images are being removed due to size\n                                    // base64ImagesSize.push(imageSize);\n                                    continue;\n                                }\n                                if (uploadResult === \"\") {\n                                    this.logger.debug(`Skipping image upload for empty image data`);\n                                    continue;\n                                }\n                                imgUrls.push(uploadResult);\n                                base64ImagesSize.push(imageSize);\n                        /*...*/\n    }\n    /*...*/\n}\n/*...*/\n``File: `runtime/src/model/capi/chat-completion-client.ts`\n``typescript\n/*...*/\nexport class ChatCompletionClient implements Client {\n    /*...*/\n    private async uploadBase64ImageData(imageData: string, contentType: string, userToken: string): Promise<string | null> {\n        /*...*/\n                    this.logger.error(`Failed to upload image: ${error.message}`);\n                    if ((error as any).code === 'ECONNREFUSED') {\n                        this.logger.error('Connection refused. The server might be down or blocked.');\n                    } else if ((error as any).code === 'ETIMEDOUT') {\n                        this.logger.error('Connection timed out. Check network conditions or server load.');\n                    } else if (error.message.includes('request entity too large') ||\n                        error.message.includes('payload too large')) {\n                        this.logger.error('Request payload too large. Try reducing the image size.');\n                    }\n                } else if (typeof error === 'object' && error !== null && 'code' in error && 'message' in error) {\n                    this.logger.error(`Failed to upload image: ${(error as any).message}`);\n                /*...*/\n    }\n    /*...*/\n}\n/*...*/\n``File: `runtime/src/model/capi/chat-completion-client.ts`\n``typescript\n/*...*/\nexport class ChatCompletionClient implements Client {\n    /*...*/\n    private async imageMessagesIfAllowed(messages: ChatCompletionMessageParam[], processedImageUrls: Set<string>): Promise<[ChatCompletionMessageParam[], TelemetryMetrics]> {\n        /*...*/\n                        break;\n                    }\n\n                    if (processedImageUrls.has(imgUrl)) {\n                        this.logger.debug(`Skipping already processed repository image url: ${imgUrl}`);\n                        continue;\n                    }\n\n                    processedImageUrls.add(imgUrl);\n\n                    newImagesMessages.push({\n                        role: 'user',\n                        content: [\n                            { type: 'text', text: `Here is the image url:` },\n                            { type: 'image_url', image_url: { url: imgUrl } },\n                        ],\n                    });\n                    numImages++;\n                }\n            }\n        }\n\n        const imageMetrics: TelemetryMetrics = {\n            imagesExtractedCount: imagesExtractedCount,\n            base64ImagesCount: base64ImagesSize.length,\n            imagesRemovedDueToSize: imagesRemovedDueToSize\n        };\n        /*...*/\n    }\n    /*...*/\n}\n/*...*/\n``",
"languageName": "TypeScript",
"path": "runtime/src/model/capi/chat-completion-client.ts",
"range": {
"start": 681,
"end": 900
},
"ref": "refs/heads/main",
"type": "snippet",
"url": "https://github.com/github/sweagentd/blob/ab92b4868c47c03b7510cd1b36a4f7e81bf828fe/runtime/src/model/capi/chat-completion-client.ts#L681-L900"
},
{
"contents": "File: `runtime/src/model/capi/chat-completion-client.ts`\n`typescript\n/*...*/\nexport class ChatCompletionClient implements Client {\n    /*...*/\n    private async getModel(): Promise<Model> {\n        const model = this.clientOptions.model;\n        const defaultResult: Model = {\n            id: model,\n            name: model,\n            capabilities: {\n                supports: {\n                    vision: false,\n                },\n                limits: {\n                    max_prompt_tokens: 90_000,\n                    max_context_window_tokens: 128_000,\n                    vision: {\n                        max_prompt_image_size: 3_145_728,\n                        max_prompt_images: 1,\n                        supported_media_types: [\n                            \"image/jpeg\",\n                            \"image/png\",\n                            \"image/webp\"\n                        ]\n                    }\n                },\n            }\n        };\n\n        const client = await this.clientPromise;\n        const models = await client.listModels();\n        const matchingModel = models.filter((m) => m.id === model).at(0);\n\n        return matchingModel || defaultResult;\n    }\n    /*...*/\n}\n/*...*/\n`File: `runtime/src/model/capi/chat-completion-client.ts`\n`typescript\n/*...*/\nexport class ChatCompletionClient implements Client {\n    /*...*/\n    private async imageMessagesIfAllowed(messages: ChatCompletionMessageParam[], processedImageUrls: Set<string>): Promise<[ChatCompletionMessageParam[], TelemetryMetrics]> {\n        const capiModel = await this.modelPromise;\n        const newImagesMessages: ChatCompletionMessageParam[] = [];\n        const visionTelemetryMetrics: TelemetryMetrics = {};\n        let imagesRemovedDueToSize: number = 0;\n\n        if (!capiModel.capabilities.supports.vision || !this.settings.featureFlags?.['copilot_swe_agent_vision']) {\n            return [newImagesMessages, visionTelemetryMetrics];\n        }\n\n        // If there are <img> tags in the messages, embed as many as we are allowed\n        const numImagesAllowed = capiModel.capabilities.limits.vision?.max_prompt_images ?? 1;\n        const maxImageSize = capiModel.capabilities.limits.vision?.max_prompt_image_size ?? 3_145_728;\n        let numImages = 0;\n        /*...*/\n    }\n    /*...*/\n}\n/*...*/\n`File: `runtime/src/model/capi/chat-completion-client.ts`\n`typescript\n/*...*/\nexport class ChatCompletionClient implements Client {\n    protected readonly clientOptions: ClientOptionsRequired;\n\n    private readonly clientPromise: Promise<OpenAI & ClientExtensionMethods>;\n    private readonly modelPromise: Promise<Model>;\n\n    private agent: SweAgentKind = \"sweagent-capi\";\n\n    public get model(): string {\n        /*...*/\n    }\n\n    constructor(provider: OpenAIClientProvider, protected readonly settings: RuntimeSettings, protected readonly logger: RunnerLogger, clientOptions?: ClientOptions) {\n        /*...*/\n    }\n\n    private static initDefaultOptions(options?: ClientOptions): ClientOptionsRequired {\n        /*...*/\n    }\n\n    protected getCompletionOptions(tools?: Tool[], options?: GetCompletionWithToolsOptions): Omit<ChatCompletionCreateParamsNonStreaming, \"model\" | \"messages\"> {\n        /*...*/\n    }\n\n    public async* getCompletionWithTools(systemMessage: string, initialMessages: ChatCompletionMessageParam[], tools: Tool[], options?: GetCompletionWithToolsOptions): AsyncGenerator<Event> {\n        /*...*/\n    }\n    /*...*/\n}\n/*...*/\n`",
"languageName": "TypeScript",
"path": "runtime/src/model/capi/chat-completion-client.ts",
"range": {
"start": 44,
"end": 633
},
"ref": "refs/heads/main",
"type": "snippet",
"url": "https://github.com/github/sweagentd/blob/ab92b4868c47c03b7510cd1b36a4f7e81bf828fe/runtime/src/model/capi/chat-completion-client.ts#L44-L633"
},
{
"contents": "File: `runtime/src/model/tokenCounting.ts`\n`typescript\n/*...*/\nexport function countTokensInToolDefinitions(toolDefinitions: ChatCompletionTool[], modelName: string): number {\n    const toolDefinitionsString = JSON.stringify(toolDefinitions);\n    return countTokensInString(toolDefinitionsString, modelName);\n}\n\nexport function countImagePartTokens(_part: ChatCompletionContentPartImage, modelName: string): number {\n    if (modelName.startsWith(\"claude\")) {\n        // Images are counted as 750 tokens for Claude models\n        return 750;\n    }\n    return 0;\n}\n\n`File: `runtime/src/model/tokenCounting.ts`\n`typescript\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *--------------------------------------------------------------------------------------------*/\n\nimport { encodingForModel, Tiktoken, TiktokenModel } from \"js-tiktoken\";\nimport { ChatCompletionContentPartImage, ChatCompletionMessageParam, ChatCompletionTool } from \"openai/resources\";\n\nconst tokenizerCache = new Map<string, Tiktoken>();\n\n/**\n * If our token counting package doesn't support counting tokens for a model, we'll\n * default to using the token counting method for this model.\n * \n * This should be a good-enough approximation for most models, and only a problem if\n * we're pushing right up against context window limits.\n * \n * The current default of \"gpt-4o\" ultimately resolves to a o200k_base tokenizer.\n */\nconst defaultModelForTokenCounting = \"gpt-4o\";\n\nfunction initDefaultTokenizer(): Tiktoken {\n    /*...*/\n}\n\nfunction getTokenizer(modelName: string): Tiktoken {\n    /*...*/\n}\n\nexport function countTokensInString(text: string, modelName: string): number {\n    /*...*/\n}\n/*...*/\n`File: `runtime/src/model/tokenCounting.ts`\n`typescript\n/*...*/\nexport function countTokensInChatCompletionMessages(messages: ChatCompletionMessageParam[], modelName: string): number {\n    /*...*/\n}\n\n/**\n * Try to match logic in at least:\n * 1. https://github.com/github/copilot-api/blob/main/pkg/token/anthropic.go (we mainly use Anthropic models)\n * 2. https://github.com/github/copilot-api/blob/main/pkg/tokenizer/tokenizer.go\n * \n * Add logic/refactor/etc. for other models as needed.\n */\nexport function countTokensInChatCompletionMessage(message: ChatCompletionMessageParam, modelName: string): number {\n    /*...*/\n}\n\nexport function countTokensInToolDefinitions(toolDefinitions: ChatCompletionTool[], modelName: string): number {\n    /*...*/\n}\n\nexport function countImagePartTokens(_part: ChatCompletionContentPartImage, modelName: string): number {\n    /*...*/\n}\n\n`File: `runtime/src/model/tokenCounting.ts`\n`typescript\n/*...*/\n/**\n * Try to match logic in at least:\n * 1. https://github.com/github/copilot-api/blob/main/pkg/token/anthropic.go (we mainly use Anthropic models)\n * 2. https://github.com/github/copilot-api/blob/main/pkg/tokenizer/tokenizer.go\n * \n * Add logic/refactor/etc. for other models as needed.\n */\nexport function countTokensInChatCompletionMessage(message: ChatCompletionMessageParam, modelName: string): number {\n    /*...*/\n                    // just in case somebody forgets to update this code, we'll\n                    // just stringify any other types of parts and count\n                    // all of the tokens in that string.\n                    strContentToCount = JSON.stringify(part);\n                }\n                strTokens += countTokensInString(strContentToCount, modelName);\n            }\n        }\n    }\n    return strTokens + nonStrTokens;\n}\n/*...*/\n`",
"languageName": "TypeScript",
"path": "runtime/src/model/tokenCounting.ts",
"range": {
"start": 1,
"end": 126
},
"ref": "refs/heads/main",
"type": "snippet",
"url": "https://github.com/github/sweagentd/blob/ab92b4868c47c03b7510cd1b36a4f7e81bf828fe/runtime/src/model/tokenCounting.ts#L1-L126"
},
{
"contents": "File: `runtime/src/model/client.ts`\n``typescript\n/*...*/\n    errorCodesToRetry?: (number | [number, number | undefined])[];\n\n    /**\n     * How to handle retries for rate limiting (429) errors. If a policy is not provided, a default\n     * policy will be used.\n     */\n    rateLimitRetryPolicy?: {\n        /**\n         * The default wait time in between retries if the server does not\n         * provide a `retry-after` header.\n         */\n        defaultRetryAfterSeconds?: number;\n        /**\n         * The initial extra wait time in between retries. The extra wait time will\n         * be added to the `retry-after` header value (or {@link defaultRetryAfterSeconds} if\n         * the header is not present). After each retry, the extra wait time will grow beyond\n         * this value according to the {@link retryBackoffExtraGrowth} factor.\n         */\n        initialRetryBackoffExtraSeconds?: number;\n        /**\n         * The growth factor for the retry backoff extra time. E.g. 2x, 3x, etc.\n         */\n        retryBackoffExtraGrowth?: number;\n        /**\n         /*...*/\n``File: `runtime/src/model/client.ts`\n``typescript\nimport type { ChatCompletionMessageParam, ChatCompletionToolChoiceOption } from \"openai/resources/chat/completions\";\nimport { Tool } from \"../tools\";\nimport { Event } from \"./event\";\n\n/**\n * Retry policies for the AI client.\n */\nexport type ClientRetryPolicy = {\n    /**\n     * The maximum number of retries for **any** type of retryable failure or error.\n     */\n    maxRetries?: number;\n\n    /**\n     * Specific error codes that should always be retried.\n     * - If a `number`, that specific error code will be retried.\n     * - If a `[number, number]`, all error codes in the range will be retried (inclusive).\n     * - If a `[number, undefined]`, all error codes greater than or equal to the first number will be retried.\n     * - To retry all error codes based on an upper bound, simply use `[0, number]`.\n     *\n     * Some error codes are retried by default even if not specified here, for example 429 (rate limit exceeded).\n     */\n    /*...*/\n``File: `runtime/src/model/client.ts`\n``typescript\n/*...*/\n         * The maximum wait time in between retries.\n         */\n        maxRetryAfterSeconds?: number;\n    };\n};\n\n/**\n * The ideal set of options that a `{@link Client}` expose.\n */\nexport type ClientOptions = {\n    /**\n     * The model to use for LLM completions.\n     */\n    model?: string;\n\n    /**\n     * The proportion of the model's input/prompt token limit\n     * that should be given to tools as their token budget.\n     */\n    toolTokenBudgetProportion?: number;\n\n    retryPolicy?: ClientRetryPolicy;\n\n    /**\n     * Enable thinking mode for the model if available.\n     */\n    thinkingMode?: boolean;\n\n    requestHeaders?: Record<string, string>;\n};\n\nexport type DeepRequire<T> = {\n    [K in keyof T]-?: Exclude<T[K], undefined> extends object\n    ? DeepRequire<T[K]>\n    : T[K];\n};\nexport type ClientOptionsRequired = DeepRequire<ClientOptions>;\n\nexport type GetCompletionWithToolsOptions = {\n    /**\n     * If `true`, then calls do `getCompletionWithTools` will check if the token counts of\n     /*...*/\n``",
"languageName": "TypeScript",
"path": "runtime/src/model/client.ts",
"range": {
"start": 1,
"end": 87
},
"ref": "refs/heads/main",
"type": "snippet",
"url": "https://github.com/github/sweagentd/blob/ab92b4868c47c03b7510cd1b36a4f7e81bf828fe/runtime/src/model/client.ts#L1-L87"
},
{
"contents": "File: `runtime/src/model/capi/image-utils.ts`\n`typescript\n/*...*/\nexport function resolveRepositoryImages(\n    messages: any[],\n    maxImagesToResolve: number,\n    githubServerUrl: string\n): string[] {\n    /*...*/\n            const matches = contents.matchAll(imgMarkdownRegex);\n\n            for (const match of matches) {\n                if (imgUrls.length >= maxImagesToResolve) {\n                    break;\n                }\n\n                const imagePath: string = match[1];\n\n                if (isGitHubUserAttachment(imagePath, githubServerUrl)) {\n                    imgUrls.push(match[1]);\n                    continue;\n                }\n\n                if (isImageInRepoPath(imagePath)) {\n                    const url = convertPathToAbsolutePath(repoInsights[insight], imagePath, githubServerUrl);\n                    imgUrls.push(url);\n                } else if (isImageFilename(imagePath)) {\n                    imgUrls.push(imagePath);\n                }\n            }\n        }\n    }\n\n    return imgUrls;\n}\n/*...*/\n`File: `runtime/src/model/capi/image-utils.ts`\n`typescript\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *--------------------------------------------------------------------------------------------*/\n\nimport path from \"path\";\n\nexport function isImageInRepoPath(path: string): boolean {\n    /*...*/\n}\n\nexport function isImageFilename(path: string): boolean {\n    /*...*/\n}\n\nexport function extractRepositoryInsights(message: string): Record<string, any> | null {\n    /*...*/\n}\n\nexport function convertPathToAbsolutePath(\n    repoInsight: any,\n    imagePath: string,\n    githubServerUrl: string\n): string {\n    /*...*/\n}\n\nexport function resolveRepositoryImages(\n    messages: any[],\n    maxImagesToResolve: number,\n    githubServerUrl: string\n): string[] {\n    /*...*/\n}\n\nfunction isGitHubUserAttachment(url: string, githubServerUrl: string): boolean {\n    /*...*/}\n`File: `runtime/src/model/capi/image-utils.ts`\n`typescript\n/*...*/\nexport function resolveRepositoryImages(\n    messages: any[],\n    maxImagesToResolve: number,\n    githubServerUrl: string\n): string[] {\n    if (maxImagesToResolve <= 0) {\n        return [];\n    }\n\n    const imgUrls: string[] = [];\n    const imgMarkdownRegex = /!\\[.*?\\]\\((.*?)\\)/g;\n\n    // Look through messages for repository insights\n    for (const message of messages) {\n        if (message.role !== 'user' || imgUrls.length >= maxImagesToResolve) {\n            continue;\n        }\n\n        const content = typeof message.content === 'string' ?\n            message.content :\n            JSON.stringify(message.content);\n\n        const repoInsights = extractRepositoryInsights(content);\n        if (!repoInsights) {\n            continue;\n        }\n\n        // Check each repository insight for image references\n        for (const insight in repoInsights) {\n            if (imgUrls.length >= maxImagesToResolve) {\n                break;\n            }\n\n            const contents: string = repoInsights[insight]['contents'];\n            /*...*/\n}\n/*...*/\n`File: `runtime/src/model/capi/image-utils.ts`\n`typescript\n/*...*/\nexport function isImageInRepoPath(path: string): boolean {\n    return !path.startsWith('http://') &&\n        !path.startsWith('https://') &&\n        isImageFilename(path);\n}\n\nexport function isImageFilename(path: string): boolean {\n    const imageExtRegex = /\\.(png|jpe?g|gif|svg|webp|bmp|tiff?|ico|heic|avif)$/i;\n    return imageExtRegex.test(path);\n}\n/*...*/\n`",
"languageName": "TypeScript",
"path": "runtime/src/model/capi/image-utils.ts",
"range": {
"start": 1,
"end": 136
},
"ref": "refs/heads/main",
"type": "snippet",
"url": "https://github.com/github/sweagentd/blob/ab92b4868c47c03b7510cd1b36a4f7e81bf828fe/runtime/src/model/capi/image-utils.ts#L1-L136"
},
{
"contents": "File: `runtime/src/model/capi/chat-completion-client.ts`\n``typescript\n/*...*/\nexport class ChatCompletionClient implements Client {\n    /*...*/\n    private async imageMessagesIfAllowed(messages: ChatCompletionMessageParam[], processedImageUrls: Set<string>): Promise<[ChatCompletionMessageParam[], TelemetryMetrics]> {\n        /*...*/\n                        break;\n                    }\n\n                    if (processedImageUrls.has(imgUrl)) {\n                        this.logger.debug(`Skipping already processed repository image url: ${imgUrl}`);\n                        continue;\n                    }\n\n                    processedImageUrls.add(imgUrl);\n\n                    newImagesMessages.push({\n                        role: 'user',\n                        content: [\n                            { type: 'text', text: `Here is the image url:` },\n                            { type: 'image_url', image_url: { url: imgUrl } },\n                        ],\n                    });\n                    numImages++;\n                }\n            }\n        }\n\n        const imageMetrics: TelemetryMetrics = {\n            imagesExtractedCount: imagesExtractedCount,\n            base64ImagesCount: base64ImagesSize.length,\n            imagesRemovedDueToSize: imagesRemovedDueToSize\n        };\n        /*...*/\n    }\n    /*...*/\n}\n/*...*/\n``File: `runtime/src/model/capi/chat-completion-client.ts`\n`typescript\n/*...*/\nexport class ChatCompletionClient implements Client {\n    /*...*/\n    private async imageMessagesIfAllowed(messages: ChatCompletionMessageParam[], processedImageUrls: Set<string>): Promise<[ChatCompletionMessageParam[], TelemetryMetrics]> {\n        /*...*/\n        // Count how many images were extracted from the issue body or repo insights\n        let imagesExtractedCount = 0;\n        const imgUrls: string[] = [];\n        const base64ImagesSize: number[] = [];\n        let imageTextMap: Map<string, string> = new Map();\n\n        // Find all available img tags\n        for (const message of messages) {\n            if (message.role == 'user') {\n                if (typeof message.content === 'string') {\n                    const imgRefs = this.matchImageReferences(message.content);\n                    const imgRefsSmallerThanMaxSize: string[] = [];\n                    const attachmentSizePromises = imgRefs.map(imgRef =>\n                        this.getAttachmentSize(imgRef, demandCopilotToken(this.settings)).then(size => ({ imgRef, size }))\n                    );\n                    /*...*/\n    }\n    /*...*/\n}\n/*...*/\n`File: `runtime/src/model/capi/chat-completion-client.ts`\n``typescript\n/*...*/\nexport class ChatCompletionClient implements Client {\n    /*...*/\n    public async* getCompletionWithTools(systemMessage: string, initialMessages: ChatCompletionMessageParam[], tools: Tool[], options?: GetCompletionWithToolsOptions): AsyncGenerator<Event> {\n        /*...*/\n                        this.logger.debug(`Truncate context window result: ${JSON.stringify(truncateContextWindowResult, null, 2)}`);\n\n                        this.logger.startGroup('Sending request to the AI model', LogLevel.Debug);\n                        const messageIsImageMessage = (m: ChatCompletionMessageParam): boolean => m.role === \"user\" && typeof m.content !== \"string\" && m.content.some((c) => c.type === \"image_url\");\n\n                        visionTelemetryMetrics[`allImagesSendToLlm`] = messages.filter(messageIsImageMessage).length;\n                        this.logger.debug(`Vision telemetry metrics: ${JSON.stringify(visionTelemetryMetrics, null, 2)}`);\n\n                        requestMessages = messages.map((message, index) => {\n                            return index === messages.length - 1\n                        /*...*/\n    }\n    /*...*/\n}\n/*...*/\n``",
"languageName": "TypeScript",
"path": "runtime/src/model/capi/chat-completion-client.ts",
"range": {
"start": 201,
"end": 786
},
"ref": "refs/heads/main",
"type": "snippet",
"url": "https://github.com/github/sweagentd/blob/ab92b4868c47c03b7510cd1b36a4f7e81bf828fe/runtime/src/model/capi/chat-completion-client.ts#L201-L786"
}
]
</repository_context>
