----
*This section includes details on the pull request with code changes you have already submitted to fix the problem above.*

<pr_title>
Implement Service Catalog GraphQL client for repository determination
</pr_title>

<pr_description>

This PR implements a new Service Catalog client that integrates with the Service Catalog GraphQL API. The client allows fetching service data, including repository URLs, which will be used to determine where to send issues when running the `process-approved-issue` command.

The implementation includes:

1. A `ServiceCatalogClient` with a GraphQL implementation that:
   - <PERSON>les pagination for retrieving services
   - Extracts service names and repository URLs
   - Uses environment variables for configuration:
     - `SERVICE_CATALOG_API_TOKEN` - for authentication
     - `SERVICE_CATALOG_API_URL` - for the API endpoint

2. A mock client for testing in `mock_service_catalog.go`

3. Comprehensive tests for all functionality in `service_catalog_client_test.go`

This implementation addresses the first part of the issue - getting service information from the catalog. The subsequent integration with `process-approved-issue.go` to determine repository destinations will be handled in a future PR.

Fixes #30.
</pr_description>

## Comments on the PR (you are @copilot in this section)

<comments>

<pr_reviews>

<comment_thread>
<file>service_catalog_client.go:30-33</file>
<original_commit>c29ef14</original_commit>

<comment_new>
<comment_id>2042873812</comment_id>
<author>@srt32</author>
@copilot why is there a client and an httpClient?
</comment_new>

</comment_thread>

</pr_reviews>

</comments>

---

- The last **2** git commits in this branch are the changes you have made so far. Use those as your change commit history.
