----
*This section includes details on the pull request with code changes you have already submitted to fix the problem above.*

<pr_title>
Add Microsoft copyright notice to all TypeScript files
</pr_title>

<pr_description>

Added the Microsoft copyright header to 21 TypeScript files that were previously missing it. The standard copyright format is:

```typescript
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
```

The files modified include:

- TypeScript files in shared/ directory
- TypeScript files in func/src/ directory
- TypeScript files in website/src/ directory
- TypeScript files in runtime/ directory

All TypeScript files in the repository now include the Microsoft copyright notice. The code continues to build successfully after these changes.

Fixes #233.
</pr_description>

## Comments on the PR (you are @copilot in this section)

<comments>

<pr_comments>

<comment_old>
<author>@colbylwilliams</author>
Please use the more modern syntax for the header:

```ts
// Copyright (c) Microsoft Corporation.
```

</comment_old>

<comment_old>
<author>@copilot</author>
I've updated all TypeScript files to use the modern copyright format as requested. Changed from the multi-line format to the simpler `// Copyright (c) Microsoft Corporation.` format. All components still build successfully after the change. See commit 20007a4.
</comment_old>

</pr_comments>

<pr_reviews>

<comment_thread>
<file>website/src/hooks/index.ts</file>
<original_commit>20007a4</original_commit>

<comment_old>
<author>@colbylwilliams</author>
no need to add the header to index files that just export other code
</comment_old>

<comment_old>
<author>@copilot</author>
I've removed the copyright header from website/src/hooks/index.ts as requested. I also removed the header from website/src/model/index.ts since it's also just an export file. All components build successfully after these changes. See commit 2e81736.
</comment_old>

</comment_thread>

</pr_reviews>

<pr_comments>

<comment_new>
<comment_id>2050057743</comment_id>
<author>@colbylwilliams</author>
Looks great.  Go ahead and update the rest of the typescript files to use the new header.
</comment_new>

</pr_comments>

</comments>

---

- The last **5** git commits in this branch are the changes you have made so far. Use those as your change commit history.
