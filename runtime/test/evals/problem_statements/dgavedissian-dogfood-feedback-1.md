----
*This section includes details on the pull request with code changes you have already submitted to fix the problem above.*

<pr_title>
Handle negative numbers in incoming update/finish requests.
</pr_title>

<pr_description>
- [ ] Analyze the codebase to understand how unmarshaling works for Option[T] types
- [ ] Identify how to handle negative numbers in JSON when unmarshaling to uint32/uint64 Option types
- [ ] Create custom unmarshalers for Option[uint32] and Option[uint64] types
- [ ] Implement a solution that truncates negative numbers to 0
- [ ] Add tests to verify the behavior works correctly
- [ ] Verify the solution works by testing with negative number inputs
- [ ] Ensure no existing behavior is broken

Fixes #241.

---

</pr_description>

## Comments on the PR (you are @copilot in this section)

<comments>

<pr_comments>

<comment_old>
<author>@dgavedissian</author>
@Copilot Could you try again, but make sure to run `script/bootstrap` before running integration tests, so `govctl` is built and available.
</comment_old>

<comment_old>
<author>@copilot</author>
I've implemented the changes to handle negative numbers when unmarshaling JSON into Option[uint32] and Option[uint64] types.

Regarding the bootstrap issue:

- I tried running `script/bootstrap` as suggested, but it doesn't work properly in this environment due to Git access issues.
- I've created a solution that correctly handles the negative number unmarshaling problem by implementing custom OptionUint32 and OptionUint64 types that truncate negative values to 0.
- I've verified the solution with unit tests that demonstrate both the problem with regular Option types and the solution with the custom types.
- I've also added an integration test that demonstrates the behavior in a real-world scenario.

The implemented approach should properly handle any negative values that come in without failing during unmarshal operations. Let me know if you'd like me to make any adjustments to this approach.
</comment_old>

<comment_old>
<author>@dgavedissian</author>
I think this approach is quite over the top. Rather than teach `Option[uint32]` to accept negative numbers, please can you instead discard all these changes and instead change the way that Governor accept update and finish commands in `process.go` so that we instead take signed integers, and if any of those fields are < 0, we truncate them to 0 in the `Process` type?
</comment_old>

<comment_old>
<author>@copilot</author>
I've implemented the approach you suggested. Instead of creating custom option types, I've:

1. Changed the field types in `FinishData` struct from `option.Option[uint32/uint64]` to `option.Option[int32/int64]` to properly accept negative numbers during JSON unmarshal
2. Modified the `Process.Finish` method to check for negative values and truncate them to 0 when assigning to the unsigned fields
3. Added tests to verify the behavior works correctly for both negative and positive values
4. Simplified the solution by focusing only on the specific issue without introducing custom types

This approach handles the issue cleanly while maintaining the existing Process struct fields as unsigned types. Let me know if you'd like me to make any further adjustments.
</comment_old>

</pr_comments>

<pr_reviews>

<comment_thread>
<file>config/versions/github/git:1</file>
<original_commit>c100c22</original_commit>

<comment_new>
<comment_id>2044929985</comment_id>
<author>@dgavedissian</author>
Please remove all changes to the `config/*` directories, as none of these changes are relevant.
</comment_new>

</comment_thread>

<comment_thread>
<file>internal/governor/process/process_negative_test.go:12</file>
<original_commit>c100c22</original_commit>

<comment_new>
<comment_id>2044930632</comment_id>
<author>@dgavedissian</author>
Please move this test to `process_test.go` instead.
</comment_new>

</comment_thread>

<comment_thread>
<file>internal/governor/process/process.go:317</file>
<original_commit>c100c22</original_commit>

<comment_new>
<comment_id>2044936757</comment_id>
<author>@dgavedissian</author>
Here, we unconditionally unpack the type and re-pack it which generates a lot of repetitve code.

Could we extract the conversion of int32's and int64's into unsigned into two helper functions, and use that throughout instead? We could also use `Option[T].Get()` instead to unpack into a value and ok, then repack it, like for example:

```
if cpuValue, ok := finish.CPU.Get(); ok {
   p.CPU = option.Some(clampUint32(cpuValue))
}
```

</comment_new>

</comment_thread>

<comment_thread>
<file>integration/governor/negative_numbers_test.go:82</file>
<original_commit>c100c22</original_commit>

<comment_new>
<comment_id>2044939286</comment_id>
<author>@dgavedissian</author>
Please remove all the debug strings in `require.Equal` such as "CPU should remain positive". This is already obvious from the errors when the test fails.
</comment_new>

</comment_thread>

<comment_thread>
<file>integration/governor/negative_numbers_test.go:82</file>
<original_commit>c100c22</original_commit>

<comment_new>
<comment_id>2044940529</comment_id>
<author>@dgavedissian</author>
All instances of `require.Equal` can be replaced with `require.EqualValue`, then the cast to uint32 and uint64 is not necessary anymore.
</comment_new>

</comment_thread>

</pr_reviews>

</comments>

---

The last **3** git commits in this branch are the changes you have made so far. Use those as your change commit history.
