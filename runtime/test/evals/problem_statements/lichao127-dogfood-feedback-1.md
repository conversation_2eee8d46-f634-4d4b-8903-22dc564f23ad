----
*This section includes details on the pull request with code changes you have already submitted to fix the problem above.*

<pr_title>
Split compliance findings into HCR and non-HCR buckets
</pr_title>

<pr_description>

Updated the `run_compliance_export` method in `app/storage_to_vuln_agg.py` to separate findings into two buckets based on their type:
- Host Configuration Rule Assessments (HCR) in one bucket
- All other types (Issue, ConfigurationFinding) in another bucket

Each bucket now gets its own export ID and namespace, allowing the findings to be processed separately by the vulnerability aggregator. This ensures different types of findings can be handled independently and more efficiently.

Key changes:
- Added new constants for export namespaces
- Added a helper function `_categorize_finding` to determine finding type
- Modified the compliance export flow to create and complete two separate exports
- Added tracking stats to monitor the distribution of findings between HCR and non-HCR types
- Updated tests to verify the new functionality

Fixes #510.
</pr_description>

## Comments on the PR (you are @copilot in this section)

<comments>

<pr_reviews>

<comment_thread>
<file>app/storage_to_vuln_agg.py:50</file>
<original_commit>b4dc800</original_commit>

<comment_old>
<author>@lichao127</author>
remove this (`_compliance_export_non_hcr_namespace`). `_compliance_export_namespace` is the namespace for non-HCR findings
</comment_old>

<comment_old>
<author>@copilot</author>
Removed `_compliance_export_non_hcr_namespace` as requested and now using `_compliance_export_namespace` for non-HCR findings. Changes in b4dc800.
</comment_old>

</comment_thread>

<comment_thread>
<file>app/storage_to_vuln_agg.py:48</file>
<original_commit>b4dc800</original_commit>

<comment_old>
<author>@lichao127</author>
add `_source_wiz_hcr = "WIZ_HCR"`. refer to https://github.com/github/wiz-therapy/pull/473
</comment_old>

<comment_old>
<author>@copilot</author>
Added `_source_wiz_hcr = "WIZ_HCR"` constant as requested. Changes in b4dc800.
</comment_old>

</comment_thread>

<comment_thread>
<file>app/storage_to_vuln_agg.py:1182</file>
<original_commit>b4dc800</original_commit>

<comment_old>
<author>@lichao127</author>
use export_id without any suffix in this case
</comment_old>

<comment_old>
<author>@copilot</author>
Modified the code to use the export_id without suffix. Changes in b4dc800.
</comment_old>

</comment_thread>

<comment_thread>
<file>app/storage_to_vuln_agg.py:1151</file>
<original_commit>b4dc800</original_commit>

<comment_old>
<author>@lichao127</author>
wrong. remove this chunk.
</comment_old>

<comment_old>
<author>@copilot</author>
Removed the incorrect chunk creating a separate export for non-HCR findings. Changes in b4dc800.
</comment_old>

<comment_old>
<author>@lichao127</author>
remove this chunk of code!
</comment_old>

<comment_old>
<author>@copilot</author>
I've removed this chunk of code in commit 47c1d45. This code is no longer necessary since we're determining the source in the _map_wiz_compliance method.
</comment_old>

</comment_thread>

<comment_thread>
<file>app/storage_to_vuln_agg.py:1119</file>
<original_commit>b4dc800</original_commit>

<comment_old>
<author>@lichao127</author>
we don't need `wiz_non_hcr_count` metric, `compliance_type_configuration_finding` is already counting this.
</comment_old>

<comment_old>
<author>@copilot</author>
Removed the `wiz_non_hcr_count` metric as requested. Changes in b4dc800.
</comment_old>

</comment_thread>

<comment_thread>
<file>app/storage_to_vuln_agg.py:1118</file>
<original_commit>b4dc800</original_commit>

<comment_old>
<author>@lichao127</author>
we don't need `wiz_hcr_count`, `compliance_type_host_configuration_finding` is already counting this
</comment_old>

<comment_old>
<author>@copilot</author>
Removed the `wiz_hcr_count` metric as requested. Changes in b4dc800.
</comment_old>

</comment_thread>

</pr_reviews>

<pr_comments>

<comment_old>
<author>@MattUebel</author>
woah
</comment_old>

</pr_comments>

<pr_reviews>

<comment_thread>
<file>Pipfile:23-26</file>
<original_commit>071f28d</original_commit>

<comment_new>
<comment_id>2045757368</comment_id>
<author>@lichao127</author>
do not remove these comments
</comment_new>

</comment_thread>

</comments>

---

- The last **3** git commits in this branch are the changes you have made so far. Use those as your change commit history.
