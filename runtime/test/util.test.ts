/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import { mkdtemp, realpath } from "fs/promises";
import { tmpdir } from "os";
import { basename, join } from "path";
import { beforeEach, describe, expect, test } from "vitest";
import { InteractiveBashSession } from "../src/tools/interactiveBash";
import {
    defaultStrLenTruncateLimit,
    maybeResolvePathAgainstDir,
    strLenTruncate,
    tokenCountTruncate,
} from "../src/tools/util";

describe("tools/utils", () => {
    describe("truncate", () => {
        const truncateFunctions: [
            string,
            (
                str: string,
                kind: "file" | "directory" | "output",
                contentLen?: number,
                truncateStyle?: "middle" | "end",
            ) => string,
        ][] = [
            [
                "strLenTruncate",
                (
                    str: string,
                    kind: "file" | "directory" | "output",
                    contentLen?: number,
                    truncateStyle?: "middle" | "end",
                ) => strLenTruncate(str, kind, contentLen, truncateStyle),
            ],
            // test tokenCountTruncate with a mock countTokens function which counts characters, this makes its behavior effectively identical to strLenTruncate
            [
                "tokenCountTruncate",
                (
                    str: string,
                    kind: "file" | "directory" | "output",
                    contentLen?: number,
                    truncateStyle?: "middle" | "end",
                ) =>
                    tokenCountTruncate(
                        str,
                        kind,
                        {
                            tokenLimit: contentLen || defaultStrLenTruncateLimit,
                            countTokens: (str: string) => str.length,
                        },
                        truncateStyle,
                    ),
            ],
        ];
        for (let i = 0; i < truncateFunctions.length; i++) {
            const truncateFunctionName = truncateFunctions[i][0];
            const truncateFunction = truncateFunctions[i][1];
            describe(truncateFunctionName, () => {
                describe("from end", () => {
                    test("should truncate single-line string by removing end characters", () => {
                        const testString = "This is a long string and needs to be truncated";
                        const truncationLength = 10;
                        const expectedStart = testString.slice(0, -1 * (testString.length - truncationLength));
                        const droppedCount = testString.length - truncationLength;

                        const truncateResult = truncateFunction(testString, "output", truncationLength, "end");
                        const expectedResult = `${expectedStart}<output too long - dropped ${droppedCount} characters from the end>`;

                        expect(truncateResult).toBe(expectedResult);
                    });
                    test("should truncate multi-line string by removing ending lines", () => {
                        const testStringLines = [
                            "Line 00 - 123456789\n",
                            "Line 01 - 123456789\r\n",
                            "Line 02 - 123456789\r\n",
                            "Line 03 - 123456789\n",
                            "Line 04 - 123456789\r\n",
                            "Line 05 - 123456789",
                        ];
                        const testString = testStringLines.join("");

                        const truncateResult = truncateFunction(
                            testString,
                            "output",
                            testStringLines[0].length * 5,
                            "end",
                        );
                        const expectedResult = [
                            testStringLines[0],
                            testStringLines[1],
                            testStringLines[2],
                            testStringLines[3],
                            "<output too long - dropped 2 lines from the end>\n",
                        ].join("");

                        expect(truncateResult).toBe(expectedResult);
                    });

                    test("should not truncate single-line string is shorter than specified length", () => {
                        expect(truncateFunction("This is a short string", "output", 100, "end")).toBe(
                            "This is a short string",
                        );
                    });

                    test("should handle empty string", () => {
                        expect(truncateFunction("", "output", 100, "end")).toBe("");
                    });

                    test("should handle string with only newlines", () => {
                        expect(truncateFunction("\n\n\n", "output", 100, "end")).toBe("\n\n\n");
                    });

                    test("should handle string that barely needs truncation", () => {
                        const testStringLines = [
                            "Line 00 - 123456789\r\n",
                            "Line 01 - 123456789\n",
                            "Line 02 - 123456789\r\n",
                            "Line 03 - 123456789\n",
                            "Line 04 - 123456789\n",
                            "Line 05 - 123456789",
                        ];
                        const testString = testStringLines.join("");

                        const result = truncateFunction(testString, "output", testString.length - 1, "end");

                        const expectedResult = [
                            testStringLines[0],
                            testStringLines[1],
                            testStringLines[2],
                            testStringLines[3],
                            testStringLines[4],
                            "<output too long - dropped 1 lines from the end>\n",
                        ].join("");

                        expect(result).toBe(expectedResult);
                    });

                    test("should handle very long strings efficiently", () => {
                        const longLine = "x".repeat(1000);
                        const testString = `${longLine}\n`.repeat(100).trimEnd();
                        const result = truncateFunction(testString, "output", 200, "end");

                        const resultLines = result.split("\n");
                        expect(resultLines.length).toBe(3);

                        expect(resultLines[0]).toBe(longLine);
                        expect(resultLines[1]).toContain("<output too long - dropped");
                    });

                    test("should handle mixed line lengths", () => {
                        const testStringLines = [
                            "Short\r\n",
                            "A much longer line here\r\n",
                            "Medium line\n",
                            "Another longer line here\n",
                            "Tiny\n",
                            "The final line\r\n",
                        ];
                        const testString = testStringLines.join("");

                        const result = truncateFunction(
                            testString,
                            "output",
                            testString.length -
                                (testStringLines[3].length + testStringLines[4].length + testStringLines[5].length),
                            "end",
                        );

                        const expectedResult = [
                            testStringLines[0],
                            testStringLines[1],
                            testStringLines[2],
                            "<output too long - dropped 4 lines from the end>\n",
                        ].join("");

                        expect(result).toBe(expectedResult);
                    });

                    test("should handle string exactly at contentLen", () => {
                        const testString = "Exactly 20 chars long";
                        expect(truncateFunction(testString, "output", testString.length, "end")).toBe(testString);
                    });
                });
                describe("from middle", () => {
                    test("should truncate single-line string by removing middle characters", () => {
                        const testString = "This is a long string and needs to be truncated";
                        const truncationLength = 10;
                        const expectedStart = testString.slice(0, truncationLength / 2);
                        const expectedEnd = testString.slice(-truncationLength / 2);
                        const droppedCount = testString.length - truncationLength;

                        expect(truncateFunction(testString, "output", truncationLength, "middle")).toBe(
                            `${expectedStart}<output too long - dropped ${droppedCount} characters from the middle>${expectedEnd}`,
                        );
                    });

                    test("should truncate multi-line string by removing middle lines", () => {
                        const testStringLines = [
                            "Line 00 - 123456789\n",
                            "Line 01 - 123456789\r\n",
                            "Line 02 - 123456789\r\n",
                            "Line 03 - 123456789\n",
                            "Line 04 - 123456789\r\n",
                            "Line 05 - 123456789",
                        ];
                        const testString = testStringLines.join("");

                        const result = truncateFunction(testString, "output", testStringLines[0].length * 5, "middle");

                        const expectedResult = [
                            testStringLines[0],
                            testStringLines[1],
                            "<output too long - dropped 2 lines from the middle>\n",
                            testStringLines[4],
                            testStringLines[5],
                        ].join("");

                        expect(result).toBe(expectedResult);
                    });

                    test("should not truncate single-line string is shorter than specified length", () => {
                        expect(truncateFunction("This is a short string", "output", 100, "middle")).toBe(
                            "This is a short string",
                        );
                    });

                    test("should handle empty string", () => {
                        expect(truncateFunction("", "output", 100, "middle")).toBe("");
                    });

                    test("should handle string with only newlines", () => {
                        expect(truncateFunction("\n\n\n", "output", 100, "middle")).toBe("\n\n\n");
                    });

                    test("should handle string that barely needs truncation", () => {
                        const testStringLines = [
                            "Line 00 - 123456789\r\n",
                            "Line 01 - 123456789\n",
                            "Line 02 - 123456789\r\n",
                            "Line 03 - 123456789\n",
                            "Line 04 - 123456789\n",
                            "Line 05 - 123456789",
                        ];
                        const testString = testStringLines.join("");

                        const result = truncateFunction(testString, "output", testString.length - 1, "middle");

                        const expectedResult = [
                            testStringLines[0],
                            testStringLines[1],
                            testStringLines[2],
                            "<output too long - dropped 1 lines from the middle>\n",
                            testStringLines[4],
                            testStringLines[5],
                        ].join("");

                        expect(result).toBe(expectedResult);
                    });

                    test("should handle very long strings efficiently", () => {
                        const longLine = "x".repeat(1000);
                        const testString = `${longLine}\n`.repeat(100).trimEnd();
                        const result = truncateFunction(testString, "output", 200, "middle");

                        const resultLines = result.split("\n");
                        expect(resultLines.length).toBe(3);

                        expect(resultLines[0]).toBe(longLine);
                        expect(resultLines[1]).toContain("<output too long - dropped");
                        expect(resultLines[2]).toBe(longLine);
                    });

                    test("should handle mixed line lengths", () => {
                        const testStringLines = [
                            "Short\r\n",
                            "A much longer line here\r\n",
                            "Medium line\n",
                            "Another longer line here\n",
                            "Tiny\n",
                            "The final line\r\n",
                        ];
                        const testString = testStringLines.join("");

                        const result = truncateFunction(
                            testString,
                            "output",
                            testString.length -
                                (testStringLines[2].length + testStringLines[3].length + testStringLines[4].length),
                            "middle",
                        );

                        const expectedResult = [
                            testStringLines[0],
                            testStringLines[1],
                            "<output too long - dropped 3 lines from the middle>\n",
                            testStringLines[5],
                        ].join("");

                        expect(result).toBe(expectedResult);
                    });

                    test("should handle string exactly at contentLen", () => {
                        const testString = "Exactly 20 chars long";
                        expect(truncateFunction(testString, "output", testString.length, "middle")).toBe(testString);
                    });
                });
            });
        }
    });

    describe("maybeResolvePathAgainstDir-interactivebash", () => {
        let session: InteractiveBashSession;
        let tempDir: string;

        beforeEach(async () => {
            // Create a temporary directory for testing
            tempDir = await mkdtemp(join(tmpdir(), "bash-session-test-"));
            // On some OS's, the tempDir may be a symlink, resolve it for testing cwd behavior
            tempDir = await realpath(tempDir);
            session = await InteractiveBashSession.create(tempDir);
        }, 10000); // 10s for test setup

        test("should return the path if it is valid", async () => {
            const fileParentDir = join(tempDir, "test");
            await session.executeCommand(`mkdir -p ${fileParentDir}`, 5000);
            const filePath = join(fileParentDir, "test.txt");
            await session.executeCommand(`echo "Hello World" > ${filePath}`, 5000);

            const result = await maybeResolvePathAgainstDir(filePath, (await session.cwd())!);
            expect(result).toBe(filePath);
        });

        test("should return the path joined to bash session cwd when it is valid - simple case", async () => {
            const fileParentDir = join(tempDir, "test");
            await session.executeCommand(`mkdir -p ${fileParentDir}`, 5000);
            const filePath = join(fileParentDir, "test.txt");
            await session.executeCommand(`echo "Hello World" > ${filePath}`, 5000);
            const fileName = basename(filePath);
            await session.executeCommand(`cd ${fileParentDir}`, 5000);
            const result = await maybeResolvePathAgainstDir(fileName, (await session.cwd())!);
            expect(result).toBe(filePath);
        });

        test("should return the path resolved to bash session cwd when it is valid - complex case", async () => {
            const fileParentDir = join(tempDir, "test");
            await session.executeCommand(`mkdir -p ${fileParentDir}`, 5000);
            const filePath = join(fileParentDir, "test.txt");
            await session.executeCommand(`echo "Hello World" > ${filePath}`, 5000);
            const dirName = basename(fileParentDir);
            const fileName = basename(filePath);
            await session.executeCommand(`cd ${fileParentDir}`, 5000);
            const result = await maybeResolvePathAgainstDir(
                `../${dirName}/../${dirName}/${fileName}`,
                (await session.cwd())!,
            );
            expect(result).toBe(filePath);
        });

        test("should return the original path if both it and it resolved to the bash session cwd are invalid", async () => {
            const invalidPath = join(tempDir, "invalid.txt");
            const result = await maybeResolvePathAgainstDir(invalidPath, (await session.cwd())!);
            expect(result).toBe(invalidPath);
        });
    });
});
