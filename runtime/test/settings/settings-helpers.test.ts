/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { beforeEach, describe, expect, it, vi } from "vitest";
import { isWithinPercentOfTimeout, RuntimeSettings } from "../../src/settings/types";

describe("settings-helpers", () => {
    describe("isWithinPercentOfTimeout", () => {
        beforeEach(() => {
            vi.useFakeTimers();
        });

        it("should return false when timeoutMs is not set", () => {
            const settings: RuntimeSettings = {
                startTimeMs: Date.now(),
                timeoutMs: undefined,
            };

            const result = isWithinPercentOfTimeout(settings, 0.1);
            expect(result.isWithin).toBe(false);
            expect(result.withinMs).toBe(0.1);
        });

        it("should return false when non-percentage value is used", () => {
            const baseTime = 1000000000;
            const timeoutMs = 5 * 60 * 1000; // 5 minutes
            vi.setSystemTime(baseTime);

            const settings: RuntimeSettings = {
                startTimeMs: baseTime,
                timeoutMs: timeoutMs,
            };

            const result = isWithinPercentOfTimeout(settings, 1.5); // > 1 is invalid
            expect(result.isWithin).toBe(false);
            expect(result.withinMs).toBe(1.5);
        });

        it("should return false for invalid number percentRemaining parameter", () => {
            const settings: RuntimeSettings = {
                startTimeMs: Date.now(),
                timeoutMs: 5 * 60 * 1000,
            };

            // Test with invalid numbers
            const nanResult = isWithinPercentOfTimeout(settings, NaN);
            expect(nanResult.isWithin).toBe(false);
            expect(isNaN(nanResult.withinMs)).toBe(true);

            const infinityResult = isWithinPercentOfTimeout(settings, Infinity);
            expect(infinityResult.isWithin).toBe(false);
            expect(infinityResult.withinMs).toBe(Infinity);

            const negInfinityResult = isWithinPercentOfTimeout(settings, -Infinity);
            expect(negInfinityResult.isWithin).toBe(false);
            expect(negInfinityResult.withinMs).toBe(-Infinity);

            const negResult = isWithinPercentOfTimeout(settings, -0.1);
            expect(negResult.isWithin).toBe(false);
            expect(negResult.withinMs).toBe(-0.1);

            const zeroResult = isWithinPercentOfTimeout(settings, 0);
            expect(zeroResult.isWithin).toBe(false);
            expect(zeroResult.withinMs).toBe(0);
        });

        it("should handle percentage values correctly", () => {
            const baseTime = 1000000000;
            const timeoutMs = 5 * 60 * 1000; // 5 minutes
            const percentage = 0.1; // 10%
            const expectedThresholdMs = timeoutMs * percentage;
            vi.setSystemTime(baseTime);

            const settings: RuntimeSettings = {
                startTimeMs: baseTime,
                timeoutMs: timeoutMs,
            };

            // Set time to be just before the 10% threshold (should be false)
            vi.setSystemTime(baseTime + timeoutMs - expectedThresholdMs - 1000);
            const result1 = isWithinPercentOfTimeout(settings, percentage);
            expect(result1.isWithin).toBe(false);
            expect(result1.withinMs).toBe(expectedThresholdMs);

            // Set time to be exactly at the 10% threshold (should be true)
            vi.setSystemTime(baseTime + timeoutMs - expectedThresholdMs);
            const result2 = isWithinPercentOfTimeout(settings, percentage);
            expect(result2.isWithin).toBe(true);
            expect(result2.withinMs).toBe(expectedThresholdMs);

            // Set time to be past the 10% threshold (should be true)
            vi.setSystemTime(baseTime + timeoutMs - expectedThresholdMs + 1000);
            const result3 = isWithinPercentOfTimeout(settings, percentage);
            expect(result3.isWithin).toBe(true);
            expect(result3.withinMs).toBe(expectedThresholdMs);
        });

        it("should handle different percentage values correctly", () => {
            const baseTime = 1000000000;
            const timeoutMs = 2 * 60 * 1000; // 2 minutes
            vi.setSystemTime(baseTime);

            const settings: RuntimeSettings = {
                startTimeMs: baseTime,
                timeoutMs: timeoutMs,
            };

            // Test 25% threshold
            const percentage25 = 0.25;
            const expectedThresholdMs25 = timeoutMs * percentage25;
            vi.setSystemTime(baseTime + timeoutMs - expectedThresholdMs25);
            const result25 = isWithinPercentOfTimeout(settings, percentage25);
            expect(result25.isWithin).toBe(true);
            expect(result25.withinMs).toBe(expectedThresholdMs25);

            // Test 5% threshold
            const percentage5 = 0.05;
            const expectedThresholdMs5 = timeoutMs * percentage5;
            vi.setSystemTime(baseTime + timeoutMs - expectedThresholdMs5);
            const result5 = isWithinPercentOfTimeout(settings, percentage5);
            expect(result5.isWithin).toBe(true);
            expect(result5.withinMs).toBe(expectedThresholdMs5);

            // Test 90% threshold (large percentage)
            const percentage90 = 0.9;
            const expectedThresholdMs90 = timeoutMs * percentage90;
            vi.setSystemTime(baseTime + timeoutMs - expectedThresholdMs90);
            const result90 = isWithinPercentOfTimeout(settings, percentage90);
            expect(result90.isWithin).toBe(true);
            expect(result90.withinMs).toBe(expectedThresholdMs90);
        });
    });
});
