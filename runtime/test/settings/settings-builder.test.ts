/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import { describe, expect, it } from "vitest";
import { SettingsBuilder } from "../../src/settings/settings-builder";

describe("SettingsBuilder", () => {
    it("should build empty settings when no values are set", () => {
        const builder = new SettingsBuilder();
        const settings = builder.build();
        expect(settings).toEqual({});
    });

    it("should set GitHub user settings correctly", () => {
        const builder = new SettingsBuilder();
        const settings = builder.setGithubUserName("testuser").setGithubUserEmail("<EMAIL>").build();

        expect(settings.github?.user).toEqual({
            name: "testuser",
            email: "<EMAIL>",
        });
    });

    it("should set GitHub settings correctly", () => {
        const builder = new SettingsBuilder();
        const settings = builder
            .setGithubToken("token123")
            .setGithubHost("github.com")
            .setGithubRepoName("test/repo")
            .setGithubRepoCommit("abc123")
            .build();

        expect(settings.github).toMatchObject({
            token: "token123",
            host: "github.com",
            repo: {
                name: "test/repo",
                commit: "abc123",
            },
        });
    });

    it("should set problem settings correctly", () => {
        const builder = new SettingsBuilder();
        const settings = builder
            .setProblemStatement("test problem")
            .setProblemAction("fix")
            .setGithubIssueNumber(123)
            .setGithubPRNumber(456)
            .build();

        expect(settings.problem).toMatchObject({
            statement: "test problem",
            action: "fix",
        });
        expect(settings.github).toMatchObject({
            issue: { number: 123 },
            pr: {
                number: 456,
            },
        });
    });

    it("should set config settings correctly", () => {
        const builder = new SettingsBuilder();
        const settings = builder
            .setGithubRepoReadWrite(true)
            .setGithubRepoBranch("feature/test")
            .setInstanceId("inst123")
            .setAgentModel("testagent:gpt4")
            .setRetryPolicy({ maxRetries: 3, errorCodesToRetry: [400, 429] })
            .setRequestHeaders({
                "X-Custom-Header": "value",
                "X-Another-Header": "another-value",
            })
            .setCallbackUrl("http://example.com/callback")
            .build();

        expect(settings.github).toMatchObject({
            repo: { readWrite: true, branch: "feature/test" },
        });
        expect(settings.service).toMatchObject({
            instance: { id: "inst123" },
            agent: {
                model: "testagent:gpt4",
                retryPolicy: { maxRetries: 3, errorCodesToRetry: [400, 429] },
                requestHeaders: {
                    "X-Custom-Header": "value",
                    "X-Another-Header": "another-value",
                },
            },
            callback: { url: "http://example.com/callback" },
        });
    });

    it("should set API settings correctly", () => {
        const builder = new SettingsBuilder();
        const settings = builder
            .setAipSweAgentToken("aiptoken")
            .setAnthropicApiKey("antkey")
            .setCopilotIntegrationId("copid")
            .setCopilotHmacKey("copkey")
            .setCopilotSessionId("session123")
            .setOpenAiApiKey("openai-key")
            .setOpenAiBaseUrl("https://test.api.openai.com/")
            .setAzureOpenAiApiVersion("2023-05-15")
            .setAzureOpenAiUrl("https://myendpoint.openai.azure.com/")
            .setAzureOpenAiKeyVaultUri("https://vault.azure.net")
            .setAzureOpenAiSecretName("openai-secret")
            .build();

        expect(settings.api).toMatchObject({
            aipSweAgent: { token: "aiptoken" },
            anthropic: { key: "antkey" },
            openai: {
                baseUrl: "https://test.api.openai.com/",
                apiKey: "openai-key",
                azureKeyVaultUri: "https://vault.azure.net",
                azureSecretName: "openai-secret",
                azure: {
                    url: "https://myendpoint.openai.azure.com/",
                    apiVersion: "2023-05-15",
                },
            },
            copilot: {
                integrationId: "copid",
                hmacKey: "copkey",
                sessionId: "session123",
            },
        });
    });

    it("should set Copilot Azure Key Vault URI correctly", () => {
        const builder = new SettingsBuilder();
        const settings = builder.setCopilotAzureKeyVaultUri("https://myvault.vault.azure.net").build();

        expect(settings.api?.copilot).toMatchObject({
            azureKeyVaultUri: "https://myvault.vault.azure.net",
        });
    });

    it("should handle undefined Copilot Azure Key Vault URI", () => {
        const builder = new SettingsBuilder();
        const settings = builder.setCopilotAzureKeyVaultUri(undefined).build();

        expect(settings.api?.copilot?.azureKeyVaultUri).toBeUndefined();
    });

    it("should ignore undefined values", () => {
        const builder = new SettingsBuilder();
        const settings = builder
            .setGithubUserName(undefined)
            .setGithubToken("token123")
            .setProblemStatement(undefined)
            .setGithubIssueNumber(123)
            .build();

        expect(settings).toEqual({
            github: {
                token: "token123",
                issue: { number: 123 },
            },
        });
    });

    it("should allow method chaining", () => {
        const builder = new SettingsBuilder();
        expect(() => {
            builder
                .setGithubUserName("user")
                .setGithubToken("token")
                .setProblemStatement("problem")
                .setInstanceId("instance")
                .build();
        }).not.toThrow();
    });
});
