/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import { afterEach, beforeEach, describe, expect, it } from "vitest";
import { loadEnvironmentSettings } from "../../src/settings/environment-settings";

describe("loadEnvironmentSettings", () => {
    // Store original env vars
    const originalEnv = { ...process.env };

    beforeEach(() => {
        // Clear env vars before each test
        process.env = {};
    });

    afterEach(() => {
        // Restore original env vars after each test
        process.env = { ...originalEnv };
    });

    it("should return empty settings when no env vars are set", async () => {
        const settings = await loadEnvironmentSettings();
        expect(settings).toEqual({
            service: {
                instance: {
                    id: expect.any(String), // we always have a fallback UUID
                },
            },
            blackbird: {
                mode: expect.any(String), // Blackbird mode should always have a default value
            },
        });
    });

    it("should load GitHub settings from env vars", async () => {
        process.env.GITHUB_HOST = "github.com";
        process.env.GITHUB_TOKEN = "token123";
        process.env.COPILOT_AGENT_COMMIT_LOGIN = "testuser";
        process.env.COPILOT_AGENT_COMMIT_EMAIL = "<EMAIL>";
        process.env.GITHUB_REPOSITORY = "test/repo";
        process.env.COPILOT_AGENT_BRANCH_NAME = "main";
        process.env.COPILOT_AGENT_BASE_COMMIT = "abc123";
        process.env.COPILOT_AGENT_PUSH = "true";

        const settings = await loadEnvironmentSettings();
        expect(settings.github).toMatchObject({
            host: "github.com",
            token: "token123",
            user: {
                name: "testuser",
                email: "<EMAIL>",
            },
            repo: {
                name: "test/repo",
                branch: "main",
                commit: "abc123",
                readWrite: true,
            },
        });
    });

    it("should handle COPILOT_AGENT_PUSH env var correctly", async () => {
        process.env.COPILOT_AGENT_PUSH = "false";
        let settings = await loadEnvironmentSettings();
        expect(settings.github?.repo?.readWrite).toBe(false);

        process.env.COPILOT_AGENT_PUSH = "true";
        settings = await loadEnvironmentSettings();
        expect(settings.github?.repo?.readWrite).toBe(true);

        delete process.env.COPILOT_AGENT_PUSH;
        settings = await loadEnvironmentSettings();
        expect(settings.github?.repo?.readWrite).toBeUndefined();
    });

    it("should handle issue and PR numbers correctly", async () => {
        process.env.COPILOT_AGENT_ISSUE_NUMBER = "123";
        process.env.COPILOT_AGENT_PR_NUMBER = "456";

        const settings = await loadEnvironmentSettings();
        expect(settings.github).toMatchObject({
            issue: { number: 123 },
            pr: {
                number: 456,
            },
        });
    });

    it("should ignore invalid issue and PR numbers", async () => {
        process.env.COPILOT_AGENT_ISSUE_NUMBER = "invalid";
        process.env.COPILOT_AGENT_PR_NUMBER = "invalid";

        const settings = await loadEnvironmentSettings();
        expect(settings.github?.issue).toBeUndefined();
        expect(settings.github?.pr).toBeUndefined();
    });

    it("should load problem settings correctly", async () => {
        process.env.COPILOT_AGENT_PROMPT = "test problem";
        process.env.COPILOT_AGENT_ACTION = "fix";

        const settings = await loadEnvironmentSettings();
        expect(settings.problem).toMatchObject({
            statement: "test problem",
            action: "fix",
        });
    });

    it("should ignore invalid agent action", async () => {
        process.env.COPILOT_AGENT_ACTION = "invalid";

        const settings = await loadEnvironmentSettings();
        expect(settings.problem?.action).toBeUndefined();
    });

    it("should load service settings correctly", async () => {
        process.env.COPILOT_AGENT_JOB_ID = "inst123";
        process.env.COPILOT_AGENT_MODEL = "testagent:gpt4";
        process.env.COPILOT_AGENT_CALLBACK_URL = "http://example.com/callback";

        const settings = await loadEnvironmentSettings();
        expect(settings.service).toMatchObject({
            instance: { id: "inst123" },
            agent: {
                model: "testagent:gpt4",
            },
            callback: { url: "http://example.com/callback" },
        });
    });

    it("should load API settings correctly", async () => {
        process.env.AIP_SWE_AGENT_TOKEN = "aiptoken";
        process.env.ANTHROPIC_API_KEY = "antkey";
        process.env.GITHUB_COPILOT_INTEGRATION_ID = "copid";
        process.env.CAPI_HMAC_KEY = "copkey";
        process.env.CAPI_AZURE_KEY_VAULT_URI = "https://vault.azure.net";
        process.env.AZURE_OPENAI_API_KEY = "azure-openai-key";
        process.env.AZURE_OPENAI_API_ENDPOINT = "https://api.openai.com";
        process.env.AZURE_OPENAI_KEY_VAULT_URI = "https://vault.azure.net";
        process.env.AZURE_OPENAI_KEY_VAULT_SECRET_NAME = "openai-secret";
        process.env.AZURE_OPENAI_API_VERSION = "2023-05-15";
        process.env.OPENAI_BASE_URL = "https://test.api.openai.com/";
        process.env.OPENAI_API_KEY = "openai-key";

        const settings = await loadEnvironmentSettings();
        expect(settings.api).toMatchObject({
            aipSweAgent: { token: "aiptoken" },
            anthropic: { key: "antkey" },
            openai: {
                baseUrl: "https://test.api.openai.com/",
                apiKey: "azure-openai-key",
                azureKeyVaultUri: "https://vault.azure.net",
                azureSecretName: "openai-secret",
                azure: {
                    apiVersion: "2023-05-15",
                    url: "https://api.openai.com",
                },
            },
            copilot: {
                integrationId: "copid",
                hmacKey: "copkey",
                azureKeyVaultUri: "https://vault.azure.net",
            },
        });
    });

    it("should load open API settings correctly", async () => {
        process.env.OPENAI_BASE_URL = "https://test.api.openai.com/";
        process.env.OPENAI_API_KEY = "openai-key";

        const settings = await loadEnvironmentSettings();
        expect(settings.api).toMatchObject({
            openai: {
                baseUrl: "https://test.api.openai.com/",
                apiKey: "openai-key",
            },
        });
    });

    it("should setup client options correctly", async () => {
        process.env.COPILOT_AGENT_ERROR_CODES_TO_RETRY = "400,429";
        process.env.COPILOT_AGENT_REQUEST_HEADERS = JSON.stringify({
            "X-Custom-Header": "value",
            "X-Another-Header": "another-value",
        });

        const settings = await loadEnvironmentSettings();
        expect(settings.service?.agent?.retryPolicy).toEqual({
            errorCodesToRetry: [400, 429],
        });
        expect(settings.service?.agent?.requestHeaders).toEqual({
            "X-Custom-Header": "value",
            "X-Another-Header": "another-value",
        });
    });
});
