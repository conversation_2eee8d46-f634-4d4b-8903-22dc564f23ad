/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import { ChatCompletionMessageParam } from "openai/resources";
import { describe, expect, it } from "vitest";
import {
    createTagSchema,
    extractTopLevelXmlTags,
    messageContainsScreenshot,
    NO_CONTENT_TOKEN,
    preprocessAgentHistory,
} from "../../src/agents/util";

describe("createTagSchema", () => {
    it("should create schema from array of property names", () => {
        const schema = createTagSchema(["title", "body", "author"]);

        expect(schema).toContain(`<title>
</title>
<body>
</body>
<author>
</author>`);
    });

    it("should create schema from object keys", () => {
        const sampleObj = {
            name: "Test",
            email: "<EMAIL>",
            age: 30,
        };

        const schema = createTagSchema(sampleObj);

        expect(schema).toContain("<name>\n</name>");
        expect(schema).toContain("<email>\n</email>");
        expect(schema).toContain("<age>\n</age>");
    });

    it("should apply custom indentation between tags", () => {
        const props = ["first", "second"];

        // Test with custom indentation
        const customIndent = createTagSchema(props, "\n---\n");
        expect(customIndent).toBe("<first>\n</first>\n---\n<second>\n</second>");

        // Test with no indentation
        const noIndent = createTagSchema(props, "");
        expect(noIndent).toBe("<first>\n</first><second>\n</second>");
    });

    it("should return empty string for empty array input", () => {
        const schema = createTagSchema([]);
        expect(schema).toBe("");
    });

    it("should return empty string for invalid input", () => {
        // @ts-expect-error - Testing with invalid input
        const schema = createTagSchema(null);
        expect(schema).toBe("");

        // @ts-expect-error - Testing with invalid input
        const schema2 = createTagSchema(undefined);
        expect(schema2).toBe("");

        // @ts-expect-error - Testing with invalid input
        const schema3 = createTagSchema(42);
        expect(schema3).toBe("");
    });

    it("should work with complex object structures", () => {
        const complexObj = {
            user: { name: "John", email: "<EMAIL>" },
            posts: [{ title: "Hello", content: "World" }],
            settings: { theme: "dark" },
        };

        const schema = createTagSchema(complexObj);

        // Should only extract top-level keys
        expect(schema).toContain("<user>\n</user>");
        expect(schema).toContain("<posts>\n</posts>");
        expect(schema).toContain("<settings>\n</settings>");
        expect(schema).not.toContain("<name>");
        expect(schema).not.toContain("<title>");
    });

    it("should work with typed input using keyof", () => {
        type User = {
            id: number;
            username: string;
            email: string;
        };

        // Create array of keys with type safety
        const keys: Array<keyof User> = ["id", "username", "email"];
        const schema = createTagSchema(keys);

        expect(schema).toContain("<id>\n</id>");
        expect(schema).toContain("<username>\n</username>");
        expect(schema).toContain("<email>\n</email>");
    });

    it("should preserve type information with sample objects", () => {
        type Article = {
            title: string;
            content: string;
            author: string;
        };

        // Use a sample object that matches the type
        const sampleArticle: Article = { title: "", content: "", author: "" };
        const schema = createTagSchema<Article>(sampleArticle);

        expect(schema).toContain("<title>\n</title>");
        expect(schema).toContain("<content>\n</content>");
        expect(schema).toContain("<author>\n</author>");
    });
});

describe("extractTopLevelXmlTags", () => {
    it("should extract basic top-level tags", () => {
        const content = `
      <title>My Title</title>
      <description>This is a description</description>
    `;

        type TestData = {
            title?: string;
            description?: string;
        };

        const result = extractTopLevelXmlTags<TestData>(content);

        expect(result.title).toBe("My Title");
        expect(result.description).toBe("This is a description");
    });

    it("should handle tags with multiline content", () => {
        const content = `
      <body>
        This is a multiline
        text block with
        several lines.
      </body>
    `;

        const result = extractTopLevelXmlTags<{ body?: string }>(content);

        expect(result.body).toBe("This is a multiline\n        text block with\n        several lines.");
    });

    it("should extract only top-level tags and ignore non-XML content", () => {
        const content = `
      Some random text here
      <parent>Parent content <child>Child content</child> more parent</parent>
      More random text
      <sibling>Sibling content</sibling>
    `;

        type TestData = {
            parent?: string;
            child?: string;
            sibling?: string;
        };

        const result = extractTopLevelXmlTags<TestData>(content);

        expect(result.parent).toBe("Parent content <child>Child content</child> more parent");
        expect(result.sibling).toBe("Sibling content");
        expect(result.child).toBeUndefined(); // should not capture nested tags as separate entries
    });

    it("should handle nested tags with nestedTagsAsString=true (default)", () => {
        const content = `
      <outer>This has <inner>nested</inner> tags</outer>
    `;

        const result = extractTopLevelXmlTags<{ outer?: string }>(content);

        expect(result.outer).toBe("This has <inner>nested</inner> tags");
    });

    it("should handle empty tags", () => {
        const content = `
      <empty></empty>
      <also_empty>   </also_empty>
    `;

        const result = extractTopLevelXmlTags<{
            empty?: string;
            also_empty?: string;
        }>(content);

        expect(result.empty).toBe("");
        expect(result.also_empty).toBe("");
    });

    it("should handle tags with special characters", () => {
        const content = `
      <special_tag>Text with special chars: &lt;&gt;&amp;"'</special_tag>
    `;

        const result = extractTopLevelXmlTags<{ special_tag?: string }>(content);

        expect(result.special_tag).toBe("Text with special chars: &lt;&gt;&amp;\"'");
    });

    it("should handle multiple occurrences of the same tag", () => {
        const content = `
      <item>First item</item>
      <item>Second item</item>
    `;

        const result = extractTopLevelXmlTags<{ item?: string }>(content);

        // The implementation will use the last occurrence
        expect(result.item).toBe("Second item");
    });

    it("should handle complex nested structures", () => {
        const content = `
      <root>
        <level1>
          <level2>
            <level3>Deep nesting</level3>
          </level2>
        </level1>
      </root>
    `;

        const result = extractTopLevelXmlTags<{ root?: string }>(content);

        expect(result.root).toContain("<level1>");
        expect(result.root).toContain("<level2>");
        expect(result.root).toContain("<level3>Deep nesting</level3>");
    });

    it("should handle invalid XML gracefully, consider it part of the tag", () => {
        const content = `
        <another_tag>Valid content</another_tag>
      <unclosed_tag>
      hello world
    `;

        const result = extractTopLevelXmlTags<{
            another_tag?: string;
            unclosed_tag?: string;
        }>(content);

        expect(result.another_tag).toBe("Valid content");
        expect(result.unclosed_tag).toBe("hello world");
    });

    it("should handle invalid nested XML gracefully, consider it part of the tag", () => {
        const content = `
        <unclosed_tag>
        <another_tag>Valid content</another_tag>
        hello world
    `;

        const result = extractTopLevelXmlTags<{
            unclosed_tag?: string;
            another_tag?: string;
        }>(content);

        expect(result.unclosed_tag).toBe("<another_tag>Valid content</another_tag>\n        hello world");
        expect(result.another_tag).toBeUndefined();
    });

    it("should handle same tag nested as content", () => {
        const content = `
      <tag>
        <tag>Nested tag</tag>
      </tag>
    `;

        const result = extractTopLevelXmlTags<{ tag?: string }>(content);

        expect(result.tag).toBe("<tag>Nested tag");
    });

    it("it should handle broken nested tags gracefully", () => {
        const content = `
      <outer>
        <inner>Text
    </outer>
    `;

        const result = extractTopLevelXmlTags<{ outer?: string }>(content);

        expect(result.outer).toBe("<inner>Text");
    });

    it("should ignore invalid tags of the same name", () => {
        const content = `
      <tag>Valid content</tag>
      Invalid content</tag>
    `;
        const result = extractTopLevelXmlTags<{ tag?: string }>(content);
        expect(result.tag).toBe("Valid content");
    });

    it("should handle NO_CONTENT_TOKEN by returning empty string", () => {
        const content = `
      <title>My Title</title>
      <empty_field>${NO_CONTENT_TOKEN}</empty_field>
      <description>Some description</description>
    `;

        type TestData = {
            title?: string;
            empty_field?: string;
            description?: string;
        };

        const result = extractTopLevelXmlTags<TestData>(content);

        expect(result.title).toBe("My Title");
        expect(result.empty_field).toBe("");
        expect(result.description).toBe("Some description");
    });

    it("should handle entire tag body as NO_CONTENT_TOKEN", () => {
        const content = NO_CONTENT_TOKEN;

        type TestData = {
            title?: string;
            empty_tag?: string;
            another_empty?: string;
            description?: string;
        };

        const result = extractTopLevelXmlTags<TestData>(content);

        expect(result.title).toBeUndefined();
        expect(result.empty_tag).toBeUndefined();
        expect(result.another_empty).toBeUndefined();
        expect(result.description).toBeUndefined();
    });
});

describe("preprocessAgentHistory", () => {
    test("should remove content items with type image_url from message history", () => {
        const history: ChatCompletionMessageParam[] = [
            {
                role: "user",
                content: [
                    {
                        type: "text",
                        text: "Here is the url of a screenshot: https://github.com/copilot/chat/attachments/<id>",
                    },
                    {
                        type: "image_url",
                        image_url: { url: "http://example.com/image.png" },
                    },
                ],
            },
            {
                role: "assistant",
                content: [{ type: "text", text: "Hi there!" }],
            },
            { role: "user", content: "Another message" },
            {
                role: "user",
                content: [{ type: "text", text: "Check this out" }],
            },
        ];

        const [processedHistory, containsScreenshots] = preprocessAgentHistory(history);
        expect(processedHistory).toEqual([
            {
                role: "user",
                content: "Here is the url of a screenshot: https://github.com/copilot/chat/attachments/<id>",
            },
            {
                role: "assistant",
                content: [{ type: "text", text: "Hi there!" }],
            },
            { role: "user", content: "Another message" },
            {
                role: "user",
                content: [{ type: "text", text: "Check this out" }],
            },
        ]);
        expect(containsScreenshots).toBe(true);
    });
});

describe("messageContainsScreenshot", () => {
    test("should correctly identify messages with screenshots", () => {
        const messages: ChatCompletionMessageParam[] = [
            {
                role: "user",
                content: [
                    {
                        type: "text",
                        text: "Here is the url of a screenshot: https://github.com/copilot/chat/attachments/<id>",
                    },
                    {
                        type: "image_url",
                        image_url: { url: "http://example.com/image.png" },
                    },
                ],
            },
            {
                role: "user",
                content: [
                    {
                        type: "text",
                        text: "Here is the image: https://github.com/copilot/chat/attachments/<id>",
                    },
                    {
                        type: "image_url",
                        image_url: { url: "http://example.com/image.png" },
                    },
                ],
            },
            {
                role: "assistant",
                content: [{ type: "text", text: "Hi there!" }],
            },
            { role: "user", content: "Another message" },
            {
                role: "user",
                content: [{ type: "text", text: "Check this out" }],
            },
        ];

        const expectations = [
            { message: messages[0], expected: true },
            { message: messages[1], expected: false },
            { message: messages[2], expected: false },
            { message: messages[3], expected: false },
            { message: messages[4], expected: false },
        ];

        expectations.forEach(({ message, expected }) => {
            const result = messageContainsScreenshot(message);
            expect(result).toBe(expected);
        });
    });
});
