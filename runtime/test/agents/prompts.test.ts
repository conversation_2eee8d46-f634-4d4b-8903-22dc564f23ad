/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import { describe, it } from "vitest";
import { prDescriptionUserMessage } from "../../src/agents/prompts";
import { reportProgressToolDescription } from "../../src/tools";

describe("prompts that vary based on issue number", () => {
    it("pr description user message should support issue number being zero", () => {
        const prDesc = prDescriptionUserMessage(0, "~~THIS IS A TEST~~", false);
        assert.include(prDesc, "~~THIS IS A TEST~~", "Response structure was not included");
        assert.notInclude(prDesc, "Fixes #0", "Issue number should not be in the prompt.");
        assert.notInclude(prDesc, "Fixes #123", "Issue number example not be in the prompt.");
    });
    it("pr description user message should support issue number being non-zero", () => {
        const prDesc = prDescriptionUserMessage(90210, "~~THIS IS A TEST~~", false);
        assert.include(prDesc, "~~THIS IS A TEST~~", "Response structure was not included");
        assert.include(prDesc, "Fixes #90210", "Issue number should not be in the prompt.");
        assert.include(prDesc, "Fixes #123", "Issue number example not be in the prompt.");
    });

    it("pr description should include screenshot", () => {
        const prDesc = prDescriptionUserMessage(90210, "~~THIS IS A TEST~~", true);
        assert.include(prDesc, "include screenshot", "Screenshot prompt was included");
    });

    it("report_progress should support issue number being zero", () => {
        const toolDesc = reportProgressToolDescription(0);
        assert.notInclude(toolDesc, "Fixes #0", "Issue number should not be in the prompt.");
    });
    it("report_progress should support issue number being non-zero", () => {
        const toolDesc = reportProgressToolDescription(90210);
        assert.include(toolDesc, "Fixes #90210", "Issue number should be in the prompt.");
    });
});
