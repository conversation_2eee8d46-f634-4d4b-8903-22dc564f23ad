/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { describe, expect, test } from "vitest";
import { getAgent, getClient } from "../../src/agents/sweagent";
import { SweBenchAgent } from "../../src/agents/swebench";
import { AIClient as AIPAIClient } from "../../src/model/aip-swe-agent/ai";
import { DEFAULT_MODEL as aipDefaultModel } from "../../src/model/aip-swe-agent/reverseproxy";
import { AIClient as AnthropicAIClient } from "../../src/model/anthropic/ai";
import { AIClient as CAPIAIClient } from "../../src/model/capi/ai";
import { DefaultAgentModel, SweAgentCapiDefault } from "../../src/model/defaults";
import { AIClient as OAIClient } from "../../src/model/openai/oai-chat-completions-ai";
import { AIClient as OAIResponseAIClient } from "../../src/model/openai/oai-responses-ai";
import { splitAgentModelSetting } from "../../src/model/util";
import { DefaultExec } from "../../src/runner/exec/default";
import { ConsoleLogger } from "../../src/runner/logger/console";
import { RuntimeSettings } from "../../src/settings";

describe("splitAgentModel", () => {
    test("undefined agentModel", () => {
        const agentModelSetting = undefined;
        const { agent, model } = splitAgentModelSetting(agentModelSetting);
        expect(agent).toBe(DefaultAgentModel.agent);
        expect(model).toBe(DefaultAgentModel.model);
    });

    test("empty agentModel", () => {
        const agentModelSetting = "";
        const { agent, model } = splitAgentModelSetting(agentModelSetting);
        expect(agent).toBe(DefaultAgentModel.agent);
        expect(model).toBe(DefaultAgentModel.model);
    });

    test("agent", () => {
        const agentModelSetting = "agent";
        const { agent, model } = splitAgentModelSetting(agentModelSetting);
        expect(agent).toBe("agent");
        expect(model).toBeUndefined();
    });

    test("agent:", () => {
        const agentModelSetting = "agent:";
        const { agent, model } = splitAgentModelSetting(agentModelSetting);
        expect(agent).toBe("agent");
        expect(model).toBeUndefined();
    });

    test("agent:model", () => {
        const agentModelSetting = "agent:model";
        const { agent, model } = splitAgentModelSetting(agentModelSetting);
        expect(agent).toBe("agent");
        expect(model).toBe("model");
    });

    test("agent:model:", () => {
        const agentModelSetting = "agent:model:";
        const { agent, model } = splitAgentModelSetting(agentModelSetting);
        expect(agent).toBe("agent");
        expect(model).toBe("model:");
    });

    test("agent:model:extra", () => {
        const agentModelSetting = "agent:model:extra";
        const { agent, model } = splitAgentModelSetting(agentModelSetting);
        expect(agent).toBe("agent");
        expect(model).toBe("model:extra");
    });

    test("agent:model:extra:more", () => {
        const agentModelSetting = "agent:model:extra:more";
        const { agent, model } = splitAgentModelSetting(agentModelSetting);
        expect(agent).toBe("agent");
        expect(model).toBe("model:extra:more");
    });

    test(":model:extra:more", () => {
        const agentModelSetting = ":model:extra:more";
        const { agent, model } = splitAgentModelSetting(agentModelSetting);
        expect(agent).toBeUndefined();
        expect(model).toBe("model:extra:more");
    });

    test("::extra:more", () => {
        const agentModelSetting = "::extra:more";
        const { agent, model } = splitAgentModelSetting(agentModelSetting);
        expect(agent).toBeUndefined();
        expect(model).toBe(":extra:more");
    });

    test("::", () => {
        const agentModelSetting = "::";
        const { agent, model } = splitAgentModelSetting(agentModelSetting);
        expect(agent).toBeUndefined();
        expect(model).toBe(":");
    });
});

describe("Agent and Model Creation", () => {
    const mockSettings: RuntimeSettings = {
        api: {
            copilot: {
                integrationId: "mock-integration-id",
                token: "mock-token",
            },
            openai: {
                azure: {
                    url: "https://mock-openai-url.com",
                },
            },
        },
        service: {
            instance: {
                id: "mock-instance-id",
            },
        },
    } as RuntimeSettings;

    const mockLogger = new ConsoleLogger();
    const mockExec = new DefaultExec(mockLogger);

    describe("getClient", () => {
        test("default - returns CAPIAIClient with default model", () => {
            const client = getClient(mockSettings, mockLogger);
            expect(client).toBeInstanceOf(CAPIAIClient);
            expect(client.model).toBe(SweAgentCapiDefault.model);
        });

        test("default - returns CAPIAIClient with specified model", () => {
            const testModel = "gpt-4";
            const client = getClient(mockSettings, mockLogger, undefined, {
                model: testModel,
            });
            expect(client).toBeInstanceOf(CAPIAIClient);
            expect(client.model).toBe(testModel);
        });

        test("sweagent creates CAPIAIClient with default model", () => {
            const client = getClient(mockSettings, mockLogger, "sweagent-capi");
            expect(client).toBeInstanceOf(CAPIAIClient);
            expect(client.model).toBe(SweAgentCapiDefault.model);
        });

        test("sweagent creates CAPIAIClient with specified model", () => {
            const testModel = "gpt-4";
            const client = getClient(mockSettings, mockLogger, "sweagent-capi", { model: testModel });
            expect(client).toBeInstanceOf(CAPIAIClient);
            expect(client.model).toBe(testModel);
        });

        test("sweagent-aip creates AIPAIClient with default model", () => {
            const client = getClient(mockSettings, mockLogger, "sweagent-aip");
            expect(client).toBeInstanceOf(AIPAIClient);
            expect(client.model).toBe(aipDefaultModel);
            expect((client as AIPAIClient).runLocal).toBe(true);
        });

        test("sweagent-aip creates AIPAIClient with specified model", async () => {
            const testModel = aipDefaultModel;
            const client = getClient(mockSettings, mockLogger, "sweagent-aip", {
                model: testModel,
            });
            expect(client).toBeInstanceOf(AIPAIClient);
            expect(client.model).toBe(testModel);
            expect((client as AIPAIClient).runLocal).toBe(true);
        });

        test("sweagent-aip with invalid model throws error", () => {
            const testModel = "invalid-model";
            expect(() =>
                getClient(mockSettings, mockLogger, "sweagent-aip", {
                    model: testModel,
                }),
            ).toThrowError("Model 'invalid-model' is not supported");
        });

        test("sweagent-anthropic returns AnthropicAIClient", () => {
            const client = getClient(mockSettings, mockLogger, "sweagent-anthropic");
            expect(client).toBeInstanceOf(AnthropicAIClient);
            expect(client.model).toBe("claude-3-5-sonnet-20241022");
        });

        test("sweagent-capi returns CAPIAIClient", () => {
            const testModel = "mock-capi-model";
            const client = getClient(mockSettings, mockLogger, "sweagent-capi", { model: testModel });
            expect(client).toBeInstanceOf(CAPIAIClient);
            expect(client.model).toBe(testModel);
        });

        test("sweagent-openai returns OAIClient", () => {
            const testModel = "gpt-4";
            const client = getClient(mockSettings, mockLogger, "sweagent-openai", { model: testModel });
            expect(client).toBeInstanceOf(OAIClient);
            expect(client.model).toBe(testModel);
        });

        test("sweagent-openai with thinkingMode returns OAIResponseAIClient", () => {
            const testModel = "o3";
            const client = getClient(mockSettings, mockLogger, "sweagent-openai", {
                model: testModel,
                thinkingMode: true,
            });
            expect(client).toBeInstanceOf(OAIResponseAIClient);
            expect(client.model).toBe(testModel);
        });

        test("sweagent-openai with thinking feature flag returns thinking OAIResponsesClient", () => {
            const testModel = "o3";
            const client = getClient(
                {
                    ...mockSettings,
                    featureFlags: { copilot_swe_agent_thinking_mode: true },
                },
                mockLogger,
                "sweagent-openai",
                { model: testModel },
            );
            expect(client).toBeInstanceOf(OAIResponseAIClient);
            expect(client.model).toBe(testModel);
        });

        test("sweagent-openai with thinking feature flag does not override model specific configuration", () => {
            const testModel = "o3";
            const client = getClient(
                {
                    ...mockSettings,
                    featureFlags: { copilot_swe_agent_thinking_mode: true },
                },
                mockLogger,
                "sweagent-openai",
                { model: testModel, thinkingMode: false },
            );
            expect(client).toBeInstanceOf(OAIClient);
            expect(client.model).toBe(testModel);
        });
    });

    describe("getAgent", () => {
        test("default - returns SweBenchAgent with CAPIAIClient", () => {
            const agent = getAgent(mockSettings, mockLogger, mockExec);
            expect(agent).toBeInstanceOf(SweBenchAgent);
            expect((agent as SweBenchAgent)["client"]).toBeInstanceOf(CAPIAIClient);
            expect((agent as SweBenchAgent)["client"].model).toBe(SweAgentCapiDefault.model);
        });

        test("default with custom model - returns SweBenchAgent with CAPIAIClient using specified model", () => {
            const testModel = "gpt-4";
            const agent = getAgent(mockSettings, mockLogger, mockExec, undefined, { model: testModel });
            expect(agent).toBeInstanceOf(SweBenchAgent);
            expect((agent as SweBenchAgent)["client"]).toBeInstanceOf(CAPIAIClient);
            expect((agent as SweBenchAgent)["client"].model).toBe(testModel);
        });

        test("sweagent with custom model - returns SweBenchAgent with CAPIAIClient using specified model", () => {
            const testModel = "gpt-4";
            const agent = getAgent(mockSettings, mockLogger, mockExec, "sweagent-capi", { model: testModel });
            expect(agent).toBeInstanceOf(SweBenchAgent);
            expect((agent as SweBenchAgent)["client"]).toBeInstanceOf(CAPIAIClient);
            expect((agent as SweBenchAgent)["client"].model).toBe(testModel);
        });

        test("sweagent-aip returns SweBenchAgent with AIPAIClient", () => {
            const testModel = aipDefaultModel;
            const agent = getAgent(mockSettings, mockLogger, mockExec, "sweagent-aip", { model: testModel });
            expect(agent).toBeInstanceOf(SweBenchAgent);
            expect((agent as SweBenchAgent)["client"]).toBeInstanceOf(AIPAIClient);
            expect((agent as SweBenchAgent)["client"].model).toBe(testModel);
        });

        test("sweagent-anthropic returns SweBenchAgent with AnthropicAIClient", () => {
            const agent = getAgent(mockSettings, mockLogger, mockExec, "sweagent-anthropic");
            expect(agent).toBeInstanceOf(SweBenchAgent);
            expect((agent as SweBenchAgent)["client"]).toBeInstanceOf(AnthropicAIClient);
            expect((agent as SweBenchAgent)["client"].model).toBe("claude-3-5-sonnet-20241022");
        });

        test("sweagent-capi returns SweBenchAgent with CAPIAIClient", () => {
            const testModel = "mock-capi-model";
            const agent = getAgent(mockSettings, mockLogger, mockExec, "sweagent-capi", { model: testModel });
            expect(agent).toBeInstanceOf(SweBenchAgent);
            expect((agent as SweBenchAgent)["client"]).toBeInstanceOf(CAPIAIClient);
            expect((agent as SweBenchAgent)["client"].model).toBe(testModel);
        });

        test("sweagent-openai returns SweBenchAgent with OAIClient", () => {
            const testModel = "gpt-4";
            const agent = getAgent(mockSettings, mockLogger, mockExec, "sweagent-openai", { model: testModel });
            expect(agent).toBeInstanceOf(SweBenchAgent);
            expect((agent as SweBenchAgent)["client"]).toBeInstanceOf(OAIClient);
            expect((agent as SweBenchAgent)["client"].model).toBe(testModel);
        });

        test("sweagent-openai with thinkingMode returns SweBenchAgent with OAIResponseAIClient", () => {
            const testModel = "o3";
            const agent = getAgent(mockSettings, mockLogger, mockExec, "sweagent-openai", {
                model: testModel,
                thinkingMode: true,
            });
            expect(agent).toBeInstanceOf(SweBenchAgent);
            expect((agent as SweBenchAgent)["client"]).toBeInstanceOf(OAIResponseAIClient);
            expect((agent as SweBenchAgent)["client"].model).toBe(testModel);
        });

        test("sweagent-aip with invalid model throws error", () => {
            const testModel = "invalid-model";
            expect(() =>
                getAgent(mockSettings, mockLogger, mockExec, "sweagent-aip", {
                    model: testModel,
                }),
            ).toThrowError("Model 'invalid-model' is not supported");
        });
    });
});
