/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { ChatCompletionChunk } from "openai/resources.mjs";
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import {
    AgentTelemetryEmitter,
    GetCompletionWithToolsTurnTelemetryEvent,
    getToolCallFromTurnData,
    redactToolCallBasedOnToolTelemetryRules,
    ToolCallExecutedTelemetry,
} from "../../src/agents/telemetry";
import { IA<PERSON><PERSON><PERSON>back } from "../../src/callbacks/callback";
import { DeepPartial, RuntimeSettings } from "../../src/settings/types";
import { Tool } from "../../src/tools";
import { AgentCallbackProgressEvent } from "../../src/types";

describe("AgentTelemetryEmitter", () => {
    let telemetryEmitter: AgentTelemetryEmitter;
    let mockCallback: IAgentCallback;
    let eventsSentToMockCallback: AgentCallbackProgressEvent[];

    beforeEach(async () => {
        // Set up mocks
        eventsSentToMockCallback = [];
        mockCallback = {
            progress: (event: AgentCallbackProgressEvent) => {
                eventsSentToMockCallback.push(event);
            },
        } as unknown as IAgentCallback;

        // Create standard telemetry emitter instance, tests can create their own if they wish
        telemetryEmitter = new AgentTelemetryEmitter(
            "test-agent",
            mockCallback,
            {} as DeepPartial<RuntimeSettings>,
            [],
        );
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    it("should initialize with empty turn data cache", () => {
        const turnData = telemetryEmitter.getTurnData();
        expect(turnData.size).toBe(0);
    });

    describe("turn data utils", () => {
        describe("getToolCallFromTurnData", () => {
            it("should return tool call data when toolCallId exists", () => {
                const turnData = {
                    featureFlagsAsString: "",
                    toolCalls: [
                        {
                            toolCallId: "call_1",
                            toolName: "test_tool",
                            arguments: '{"param": "value"}',
                            safeForTelemetryToolName: "test_tool",
                            safeForTelemetryArguments: '{"param": "hashed_value"}',
                        },
                        {
                            toolCallId: "call_2",
                            toolName: "another_tool",
                            arguments: '{"param2": "value2"}',
                            safeForTelemetryToolName: "another_tool",
                            safeForTelemetryArguments: '{"param2": "hashed_value2"}',
                        },
                    ],
                    toolCallExecutions: [],
                    turn: 1,
                };

                const result = getToolCallFromTurnData(turnData, "call_2");

                expect(result).toBeDefined();
                expect(result?.toolCallId).toBe("call_2");
                expect(result?.toolName).toBe("another_tool");
                expect(result?.arguments).toBe('{"param2": "value2"}');
                expect(result?.safeForTelemetryToolName).toBe("another_tool");
                expect(result?.safeForTelemetryArguments).toBe('{"param2": "hashed_value2"}');
            });

            it("should return undefined when toolCallId does not exist", () => {
                const turnData = {
                    featureFlagsAsString: "",
                    toolCalls: [
                        {
                            toolCallId: "call_1",
                            toolName: "test_tool",
                            arguments: '{"param": "value"}',
                            safeForTelemetryToolName: "test_tool",
                            safeForTelemetryArguments: '{"param": "hashed_value"}',
                        },
                    ],
                    toolCallExecutions: [],
                    turn: 1,
                };

                const result = getToolCallFromTurnData(turnData, "nonexistent_call");

                expect(result).toBeUndefined();
            });

            it("should return undefined when toolCalls array is empty", () => {
                const turnData = {
                    featureFlagsAsString: "",
                    toolCalls: [],
                    toolCallExecutions: [],
                    turn: 1,
                };

                const result = getToolCallFromTurnData(turnData, "call_1");

                expect(result).toBeUndefined();
            });
        });

        describe("redactToolCallBasedOnToolTelemetryRules", () => {
            const mockToolCall = {
                id: "call_123",
                type: "function" as const,
                function: {
                    name: "test_tool",
                    arguments: '{"param1": "value1", "param2": "value2"}',
                },
            };

            it("should hash everything when tool is not found in toolsInUse", () => {
                const toolsInUse: Tool[] = [];

                const result = redactToolCallBasedOnToolTelemetryRules(toolsInUse, mockToolCall);

                expect(result.toolCallId).toBe("call_123");
                expect(result.toolName).toBe("test_tool");
                expect(result.arguments).toBe('{"param1": "value1", "param2": "value2"}');
                expect(result.safeForTelemetryToolName).toBe(
                    "c877fbde6877ae812fa11d00e82fc0628258f70e816180f06b725753e95d8c5a",
                );
                expect(result.safeForTelemetryArguments).toBe("");
            });

            it("should use original values when safeForTelemetry is true", () => {
                const toolsInUse: Tool[] = [
                    {
                        name: "test_tool",
                        description: "Test tool",
                        input_schema: { type: "object", properties: {} },
                        callback: async () => ({
                            textResultForLlm: "result",
                            resultType: "success" as const,
                            toolTelemetry: {
                                properties: {},
                                restrictedProperties: {},
                                metrics: {},
                            },
                        }),
                        safeForTelemetry: true,
                    },
                ];

                const result = redactToolCallBasedOnToolTelemetryRules(toolsInUse, mockToolCall);

                expect(result.toolCallId).toBe("call_123");
                expect(result.toolName).toBe("test_tool");
                expect(result.arguments).toBe('{"param1": "value1", "param2": "value2"}');
                expect(result.safeForTelemetryToolName).toBe("test_tool");
                // Even when safe, arguments are hashed with parameter names visible
                const parsedArgs = JSON.parse(result.safeForTelemetryArguments);
                expect(parsedArgs).toHaveProperty("param1");
                expect(parsedArgs).toHaveProperty("param2");
                expect(typeof parsedArgs.param1).toBe("string");
                expect(parsedArgs.param1).not.toBe("value1");
                expect(typeof parsedArgs.param2).toBe("string");
                expect(parsedArgs.param2).not.toBe("value2");
            });

            it("should handle partial safety settings - name safe, inputs not safe", () => {
                const toolsInUse: Tool[] = [
                    {
                        name: "test_tool",
                        description: "Test tool",
                        input_schema: { type: "object", properties: {} },
                        callback: async () => ({
                            textResultForLlm: "result",
                            resultType: "success" as const,
                            toolTelemetry: {
                                properties: {},
                                restrictedProperties: {},
                                metrics: {},
                            },
                        }),
                        safeForTelemetry: {
                            name: true,
                            inputsNames: false,
                        },
                    },
                ];

                const result = redactToolCallBasedOnToolTelemetryRules(toolsInUse, mockToolCall);

                expect(result.toolCallId).toBe("call_123");
                expect(result.toolName).toBe("test_tool");
                expect(result.arguments).toBe('{"param1": "value1", "param2": "value2"}');
                expect(result.safeForTelemetryToolName).toBe("test_tool");
                expect(result.safeForTelemetryArguments).toBe("");
            });

            it("should handle partial safety settings - name not safe, inputs safe", () => {
                const toolsInUse: Tool[] = [
                    {
                        name: "test_tool",
                        description: "Test tool",
                        input_schema: { type: "object", properties: {} },
                        callback: async () => ({
                            textResultForLlm: "result",
                            resultType: "success" as const,
                            toolTelemetry: {
                                properties: {},
                                restrictedProperties: {},
                                metrics: {},
                            },
                        }),
                        safeForTelemetry: {
                            name: false,
                            inputsNames: true,
                        },
                    },
                ];

                const result = redactToolCallBasedOnToolTelemetryRules(toolsInUse, mockToolCall);

                expect(result.toolCallId).toBe("call_123");
                expect(result.toolName).toBe("test_tool");
                expect(result.arguments).toBe('{"param1": "value1", "param2": "value2"}');
                expect(result.safeForTelemetryToolName).toBe(
                    "c877fbde6877ae812fa11d00e82fc0628258f70e816180f06b725753e95d8c5a",
                );
                // Arguments should show parameter names but hash values
                const parsedArgs = JSON.parse(result.safeForTelemetryArguments);
                expect(parsedArgs).toHaveProperty("param1");
                expect(parsedArgs).toHaveProperty("param2");
                expect(typeof parsedArgs.param1).toBe("string");
                expect(parsedArgs.param1).not.toBe("value1");
                expect(typeof parsedArgs.param2).toBe("string");
                expect(parsedArgs.param2).not.toBe("value2");
            });

            it("should handle invalid JSON in tool call arguments", () => {
                const toolsInUse: Tool[] = [
                    {
                        name: "test_tool",
                        description: "Test tool",
                        input_schema: { type: "object", properties: {} },
                        callback: async () => ({
                            textResultForLlm: "result",
                            resultType: "success" as const,
                            toolTelemetry: {
                                properties: {},
                                restrictedProperties: {},
                                metrics: {},
                            },
                        }),
                        safeForTelemetry: true,
                    },
                ];

                const invalidJsonToolCall = {
                    id: "call_123",
                    type: "function" as const,
                    function: {
                        name: "test_tool",
                        arguments: '{"param1": "value1", invalid json',
                    },
                };

                const result = redactToolCallBasedOnToolTelemetryRules(toolsInUse, invalidJsonToolCall);

                expect(result.toolCallId).toBe("call_123");
                expect(result.toolName).toBe("test_tool");
                expect(result.arguments).toBe('{"param1": "value1", invalid json');
                expect(result.safeForTelemetryToolName).toBe(
                    "c877fbde6877ae812fa11d00e82fc0628258f70e816180f06b725753e95d8c5a",
                );
                expect(result.safeForTelemetryArguments).toBe("");
            });

            it("should handle empty arguments", () => {
                const toolsInUse: Tool[] = [
                    {
                        name: "test_tool",
                        description: "Test tool",
                        input_schema: { type: "object", properties: {} },
                        callback: async () => ({
                            textResultForLlm: "result",
                            resultType: "success" as const,
                            toolTelemetry: {
                                properties: {},
                                restrictedProperties: {},
                                metrics: {},
                            },
                        }),
                        safeForTelemetry: true,
                    },
                ];

                const emptyArgsToolCall = {
                    id: "call_123",
                    type: "function" as const,
                    function: {
                        name: "test_tool",
                        arguments: "",
                    },
                };

                const result = redactToolCallBasedOnToolTelemetryRules(toolsInUse, emptyArgsToolCall);

                expect(result.toolCallId).toBe("call_123");
                expect(result.toolName).toBe("test_tool");
                expect(result.arguments).toBe("");
                expect(result.safeForTelemetryToolName).toBe("test_tool");
                expect(result.safeForTelemetryArguments).toBe("{}");
            });
        });
    });

    describe("turn telemetry", () => {
        it("should handle turn_started event", async () => {
            const event = {
                kind: "turn_started" as const,
                model: "gpt-4",
                modelInfo: { version: "2024-01-01" },
                turn: 0,
                timestampMs: 1234567890,
            };

            await telemetryEmitter.ingestEvent(event);

            const turnData = telemetryEmitter.getTurnData();
            expect(turnData.size).toBe(1);
            expect(turnData.get(1)).toEqual({
                featureFlagsAsString: "",
                toolCalls: [],
                toolCallExecutions: [],
                turn: 1,
                model: "gpt-4",
                modelInfo: '{"version":"2024-01-01"}',
                startTimeMs: 1234567890,
            });
        });

        it("should handle turn_ended event and emit telemetry", async () => {
            // First start a turn
            const startEvent = {
                kind: "turn_started" as const,
                model: "gpt-4",
                modelInfo: { version: "2024-01-01" },
                turn: 0,
                timestampMs: 1234567890,
            };

            await telemetryEmitter.ingestEvent(startEvent);

            // Then end the turn
            const endEvent = {
                kind: "turn_ended" as const,
                model: "gpt-4",
                modelInfo: { version: "2024-01-01" },
                turn: 0,
                timestampMs: 1234567900,
            };

            await telemetryEmitter.ingestEvent(endEvent);

            expect(eventsSentToMockCallback).toHaveLength(1);
            const telemetryEvent = eventsSentToMockCallback[0] as GetCompletionWithToolsTurnTelemetryEvent;
            expect(telemetryEvent.kind).toBe("telemetry");
            expect(telemetryEvent.telemetry.event).toBe("get_completion_with_tools_turn");
            expect(telemetryEvent.telemetry.properties.model).toBe("gpt-4");
            expect(telemetryEvent.telemetry.properties.agent).toBe("test-agent");
            expect(telemetryEvent.telemetry.metrics.turn).toBe(1);
            expect(telemetryEvent.telemetry.metrics.turnDurationMs).toBe(10);
        });

        it("should handle turn_failed event", async () => {
            const event = {
                kind: "turn_failed" as const,
                model: "gpt-4",
                modelInfo: { version: "2024-01-01" },
                turn: 0,
                timestampMs: 1234567890,
                error: "API timeout",
            };

            await telemetryEmitter.ingestEvent(event);

            const turnData = telemetryEmitter.getTurnData();
            expect(turnData.get(1)?.error).toBe("API timeout");
            expect(turnData.get(1)?.endTimeMs).toBe(1234567890);
        });

        it("should handle turn_retry event", async () => {
            const event = {
                kind: "turn_retry" as const,
                model: "gpt-4",
                modelInfo: { version: "2024-01-01" },
                turn: 0,
                timestampMs: 1234567890,
            };

            await telemetryEmitter.ingestEvent(event);

            const turnData = telemetryEmitter.getTurnData();
            expect(turnData.get(1)?.retriesUsed).toBe(1);
            expect(turnData.get(1)?.endTimeMs).toBe(1234567890);
        });

        it("should handle multiple retries", async () => {
            const event1 = {
                kind: "turn_retry" as const,
                model: "gpt-4",
                modelInfo: { version: "2024-01-01" },
                turn: 0,
                timestampMs: 1234567890,
            };

            const event2 = {
                kind: "turn_retry" as const,
                model: "gpt-4",
                modelInfo: { version: "2024-01-01" },
                turn: 0,
                timestampMs: 1234567891,
            };

            await telemetryEmitter.ingestEvent(event1);
            await telemetryEmitter.ingestEvent(event2);

            const turnData = telemetryEmitter.getTurnData();
            expect(turnData.get(1)?.retriesUsed).toBe(2);
        });

        it("should handle model_call_success event", async () => {
            const event = {
                kind: "model_call_success" as const,
                turn: 0,
                modelCallDurationMs: 1500,
                modelCall: {
                    api_id: "api-123",
                    request_id: "req-456",
                },
                responseChunk: {} as ChatCompletionChunk,
                responseUsage: {
                    prompt_tokens: 100,
                    completion_tokens: 50,
                    total_tokens: 150,
                },
            };

            await telemetryEmitter.ingestEvent(event);

            const turnData = telemetryEmitter.getTurnData();
            expect(turnData.get(1)?.modelCallDurationMs).toBe(1500);
            expect(turnData.get(1)?.api_call_id).toBe("api-123");
            expect(turnData.get(1)?.provider_call_id).toBe("req-456");
            expect(turnData.get(1)?.responsePromptTokens).toBe(100);
            expect(turnData.get(1)?.responseCompletionTokens).toBe(50);
            expect(turnData.get(1)?.responseTotalTokens).toBe(150);
        });

        it("should handle model_call_failure event", async () => {
            const event = {
                kind: "model_call_failure" as const,
                turn: 0,
                modelCallDurationMs: 2000,
                modelCall: {
                    api_id: "api-456",
                    request_id: "req-789",
                    error: "Request timeout",
                    status: 500,
                },
            };

            await telemetryEmitter.ingestEvent(event);

            const turnData = telemetryEmitter.getTurnData();
            expect(turnData.get(1)?.modelCallDurationMs).toBe(2000);
            expect(turnData.get(1)?.api_call_id).toBe("api-456");
            expect(turnData.get(1)?.provider_call_id).toBe("req-789");
            expect(turnData.get(1)?.responsePromptTokens).toBeUndefined();
            expect(turnData.get(1)?.responseCompletionTokens).toBeUndefined();
            expect(turnData.get(1)?.responseTotalTokens).toBeUndefined();
        });

        it("should handle history_truncated event", async () => {
            const truncateResult = {
                preTruncationTokensInMessages: 1000,
                preTruncationMessagesLength: 10,
                postTruncationTokensInMessages: 800,
                postTruncationMessagesLength: 8,
                tokensRemovedDuringTruncation: 200,
                messagesRemovedDuringTruncation: 2,
            };

            const event = {
                kind: "history_truncated" as const,
                turn: 0,
                truncateResult,
            };

            await telemetryEmitter.ingestEvent(event);

            const turnData = telemetryEmitter.getTurnData();
            expect(turnData.get(1)?.truncateResult).toEqual(truncateResult);
        });

        it("should handle image_processing event", async () => {
            const imageProcessingMetrics = {
                imagesExtractedCount: 3,
                base64ImagesCount: 2,
                imagesRemovedDueToSize: 1,
                imagesRemovedDueToDimensions: 0,
                imagesResolvedFromGitHubMCPCount: 1,
                allImagesSendToLlm: 2,
            };

            const event = {
                kind: "image_processing" as const,
                turn: 0,
                imageProcessingMetrics,
            };

            await telemetryEmitter.ingestEvent(event);

            const turnData = telemetryEmitter.getTurnData();
            expect(turnData.get(1)?.imageProcessingMetrics).toEqual(imageProcessingMetrics);
        });

        it("should handle images_removed event", async () => {
            const event = {
                kind: "images_removed" as const,
                turn: 0,
                largeImagesRemoved: 2,
                imagesRemoved: 1,
            };

            await telemetryEmitter.ingestEvent(event);

            const turnData = telemetryEmitter.getTurnData();
            // The implementation treats largeImagesRemoved as a boolean (1 if truthy, 0 if falsy)
            expect(turnData.get(1)?.largeImagesRemoved).toBe(1);
            expect(turnData.get(1)?.imagesRemoved).toBe(1);
        });

        it("should accumulate multiple images_removed events", async () => {
            const event1 = {
                kind: "images_removed" as const,
                turn: 0,
                largeImagesRemoved: 1,
                imagesRemoved: 2,
            };

            const event2 = {
                kind: "images_removed" as const,
                turn: 0,
                largeImagesRemoved: 2,
                imagesRemoved: 1,
            };

            await telemetryEmitter.ingestEvent(event1);
            await telemetryEmitter.ingestEvent(event2);

            const turnData = telemetryEmitter.getTurnData();
            // Each event contributes 1 to the count regardless of the actual value
            expect(turnData.get(1)?.largeImagesRemoved).toBe(2);
            expect(turnData.get(1)?.imagesRemoved).toBe(2);
        });

        it("should handle user message with jit-instruction source", async () => {
            const event = {
                kind: "message" as const,
                message: {
                    role: "user" as const,
                    content: "Test instruction",
                },
                turn: 0,
                source: "jit-instruction" as const,
            };

            await telemetryEmitter.ingestEvent(event);

            const turnData = telemetryEmitter.getTurnData();
            expect(turnData.get(1)?.jitInstructionsAdded).toBe(1);
        });

        it("should accumulate multiple jit instructions", async () => {
            const event1 = {
                kind: "message" as const,
                message: {
                    role: "user" as const,
                    content: "Test instruction 1",
                },
                turn: 0,
                source: "jit-instruction" as const,
            };

            const event2 = {
                kind: "message" as const,
                message: {
                    role: "user" as const,
                    content: "Test instruction 2",
                },
                turn: 0,
                source: "jit-instruction" as const,
            };

            await telemetryEmitter.ingestEvent(event1);
            await telemetryEmitter.ingestEvent(event2);

            const turnData = telemetryEmitter.getTurnData();
            expect(turnData.get(1)?.jitInstructionsAdded).toBe(2);
        });

        it("should handle assistant message with tool calls", async () => {
            const event = {
                kind: "message" as const,
                message: {
                    role: "assistant" as const,
                    content: "I'll help you with that.",
                    tool_calls: [
                        {
                            id: "call_1",
                            type: "function" as const,
                            function: {
                                name: "test_tool",
                                arguments: '{"param1": "value1"}',
                            },
                        },
                        {
                            id: "call_2",
                            type: "function" as const,
                            function: {
                                name: "another_tool",
                                arguments: '{"param2": "value2"}',
                            },
                        },
                    ],
                },
                turn: 0,
            };

            await telemetryEmitter.ingestEvent(event);

            const turnData = telemetryEmitter.getTurnData();
            expect(turnData.get(1)?.toolCalls).toHaveLength(2);
            expect(turnData.get(1)?.toolCalls[0].toolCallId).toBe("call_1");
            expect(turnData.get(1)?.toolCalls[0].safeForTelemetryArguments).toEqual("");
            expect(turnData.get(1)?.toolCalls[1].toolCallId).toBe("call_2");
        });

        it("should handle tool calls with safe telemetry settings", async () => {
            // Create emitter with tools that have safe telemetry settings
            const toolsWithSafeTelemetry: Tool[] = [
                {
                    name: "safe_tool",
                    description: "A safe tool",
                    input_schema: { type: "object", properties: {} },
                    callback: async () => ({
                        textResultForLlm: "result",
                        resultType: "success" as const,
                        toolTelemetry: {
                            properties: {},
                            restrictedProperties: {},
                            metrics: {},
                        },
                    }),
                    safeForTelemetry: true,
                },
                {
                    name: "partially_safe_tool",
                    description: "A partially safe tool",
                    input_schema: { type: "object", properties: {} },
                    callback: async () => ({
                        textResultForLlm: "result",
                        resultType: "success" as const,
                        toolTelemetry: {
                            properties: {},
                            restrictedProperties: {},
                            metrics: {},
                        },
                    }),
                    safeForTelemetry: {
                        name: true,
                        inputsNames: false,
                    },
                },
            ];

            const safeEmitter = new AgentTelemetryEmitter(
                "test-agent",
                mockCallback,
                {} as DeepPartial<RuntimeSettings>,
                toolsWithSafeTelemetry,
            );

            const event = {
                kind: "message" as const,
                message: {
                    role: "assistant" as const,
                    content: "I'll help you with that.",
                    tool_calls: [
                        {
                            id: "call_1",
                            type: "function" as const,
                            function: {
                                name: "safe_tool",
                                arguments: '{"param1": "value1"}',
                            },
                        },
                        {
                            id: "call_2",
                            type: "function" as const,
                            function: {
                                name: "partially_safe_tool",
                                arguments: '{"param2": "value2"}',
                            },
                        },
                        {
                            id: "call_3",
                            type: "function" as const,
                            function: {
                                name: "unsafe_tool",
                                arguments: '{"param3": "value3"}',
                            },
                        },
                    ],
                },
                turn: 0,
            };

            await safeEmitter.ingestEvent(event);

            const turnData = safeEmitter.getTurnData();
            expect(turnData.get(1)?.toolCalls).toHaveLength(3);

            // Safe tool should have original name and arguments (but arguments are still hashed)
            expect(turnData.get(1)?.toolCalls[0].safeForTelemetryToolName).toBe("safe_tool");
            const safeToolArgs = turnData.get(1)?.toolCalls[0].safeForTelemetryArguments;
            expect(typeof safeToolArgs).toBe("string");
            if (typeof safeToolArgs === "string") {
                expect(JSON.parse(safeToolArgs)).toEqual({
                    param1: expect.any(String),
                });
            }

            // Partially safe tool should have original name but no arguments
            expect(turnData.get(1)?.toolCalls[1].safeForTelemetryToolName).toBe("partially_safe_tool");
            expect(turnData.get(1)?.toolCalls[1].safeForTelemetryArguments).toEqual("");

            // Unsafe tool should have hashed name and no arguments
            expect(turnData.get(1)?.toolCalls[2].safeForTelemetryToolName).not.toBe("unsafe_tool");
            expect(turnData.get(1)?.toolCalls[2].safeForTelemetryArguments).toEqual("");
        });

        it("should handle tool message events", async () => {
            const event = {
                kind: "message" as const,
                message: {
                    role: "tool" as const,
                    content: "Tool execution result",
                    tool_call_id: "call_123",
                },
                turn: 0,
            };

            await telemetryEmitter.ingestEvent(event);

            const turnData = telemetryEmitter.getTurnData();
            expect(turnData.get(1)?.toolCallExecutions).toContain("call_123");
        });

        it("should handle multiple tool executions", async () => {
            const event1 = {
                kind: "message" as const,
                message: {
                    role: "tool" as const,
                    content: "Tool execution result 1",
                    tool_call_id: "call_123",
                },
                turn: 0,
            };

            const event2 = {
                kind: "message" as const,
                message: {
                    role: "tool" as const,
                    content: "Tool execution result 2",
                    tool_call_id: "call_456",
                },
                turn: 0,
            };

            await telemetryEmitter.ingestEvent(event1);
            await telemetryEmitter.ingestEvent(event2);

            const turnData = telemetryEmitter.getTurnData();
            expect(turnData.get(1)?.toolCallExecutions).toEqual(["call_123", "call_456"]);
        });

        it("should ignore events without turn number", async () => {
            const event = {
                kind: "message" as const,
                message: {
                    role: "user" as const,
                    content: "Test message",
                },
                // No turn property
            };

            await telemetryEmitter.ingestEvent(event);

            const turnData = telemetryEmitter.getTurnData();
            expect(turnData.size).toBe(0);
        });

        it("should not emit telemetry for already emitted turns", async () => {
            // Start and end a turn
            const startEvent = {
                kind: "turn_started" as const,
                model: "gpt-4",
                modelInfo: { version: "2024-01-01" },
                turn: 0,
                timestampMs: 1234567890,
            };

            const endEvent = {
                kind: "turn_ended" as const,
                model: "gpt-4",
                modelInfo: { version: "2024-01-01" },
                turn: 0,
                timestampMs: 1234567900,
            };

            await telemetryEmitter.ingestEvent(startEvent);
            await telemetryEmitter.ingestEvent(endEvent);

            expect(eventsSentToMockCallback).toHaveLength(1);

            // Try to add another event for the same turn
            const anotherEvent = {
                kind: "turn_retry" as const,
                model: "gpt-4",
                modelInfo: { version: "2024-01-01" },
                turn: 0,
                timestampMs: 1234567910,
            };

            await telemetryEmitter.ingestEvent(anotherEvent);

            // Should still only have one telemetry event
            expect(eventsSentToMockCallback).toHaveLength(1);
        });

        it("should handle complete turn lifecycle with telemetry", async () => {
            const events = [
                {
                    kind: "turn_started" as const,
                    model: "gpt-4",
                    modelInfo: { version: "2024-01-01" },
                    turn: 0,
                    timestampMs: 1234567890,
                },
                {
                    kind: "model_call_success" as const,
                    turn: 0,
                    modelCallDurationMs: 1500,
                    modelCall: {
                        api_id: "api-123",
                        request_id: "req-456",
                    },
                    responseChunk: {} as ChatCompletionChunk,
                    responseUsage: {
                        prompt_tokens: 100,
                        completion_tokens: 50,
                        total_tokens: 150,
                    },
                },
                {
                    kind: "message" as const,
                    message: {
                        role: "assistant" as const,
                        content: "I'll help you with that.",
                        tool_calls: [
                            {
                                id: "call_1",
                                type: "function" as const,
                                function: {
                                    name: "test_tool",
                                    arguments: '{"param1": "value1"}',
                                },
                            },
                        ],
                    },
                    turn: 0,
                },
                {
                    kind: "message" as const,
                    message: {
                        role: "tool" as const,
                        content: "Tool execution result",
                        tool_call_id: "call_1",
                    },
                    turn: 0,
                },
                {
                    kind: "turn_ended" as const,
                    model: "gpt-4",
                    modelInfo: { version: "2024-01-01" },
                    turn: 0,
                    timestampMs: 1234567900,
                },
            ];

            for (const event of events) {
                await telemetryEmitter.ingestEvent(event);
            }

            expect(eventsSentToMockCallback).toHaveLength(1);
            const telemetryEvent = eventsSentToMockCallback[0] as GetCompletionWithToolsTurnTelemetryEvent;

            expect(telemetryEvent.telemetry.properties.model).toBe("gpt-4");
            expect(telemetryEvent.telemetry.properties.agent).toBe("test-agent");
            expect(telemetryEvent.telemetry.properties.api_call_id).toBe("api-123");
            expect(telemetryEvent.telemetry.properties.provider_call_id).toBe("req-456");
            expect(telemetryEvent.telemetry.properties.toolCalls).toBeDefined();
            expect(telemetryEvent.telemetry.properties.toolCallExecutions).toBeDefined();

            expect(telemetryEvent.telemetry.metrics.turn).toBe(1);
            expect(telemetryEvent.telemetry.metrics.turnDurationMs).toBe(10);
            expect(telemetryEvent.telemetry.metrics.modelCallDurationMs).toBe(1500);
            expect(telemetryEvent.telemetry.metrics.numToolCalls).toBe(1);
            expect(telemetryEvent.telemetry.metrics.numToolExecutions).toBe(1);
            expect(telemetryEvent.telemetry.metrics.responsePromptTokens).toBe(100);
            expect(telemetryEvent.telemetry.metrics.responseCompletionTokens).toBe(50);
            expect(telemetryEvent.telemetry.metrics.responseTotalTokens).toBe(150);
        });

        it("should handle feature flags in settings", async () => {
            const settingsWithFeatureFlags: DeepPartial<RuntimeSettings> = {
                featureFlags: {
                    someFlag: true,
                    anotherFlag: false,
                },
            };

            const emitterWithFlags = new AgentTelemetryEmitter(
                "test-agent",
                mockCallback,
                settingsWithFeatureFlags,
                [],
            );

            const startEvent = {
                kind: "turn_started" as const,
                model: "gpt-4",
                modelInfo: { version: "2024-01-01" },
                turn: 0,
                timestampMs: 1234567890,
            };

            const endEvent = {
                kind: "turn_ended" as const,
                model: "gpt-4",
                modelInfo: { version: "2024-01-01" },
                turn: 0,
                timestampMs: 1234567900,
            };

            await emitterWithFlags.ingestEvent(startEvent);
            await emitterWithFlags.ingestEvent(endEvent);

            expect(eventsSentToMockCallback).toHaveLength(1);
            const telemetryEvent = eventsSentToMockCallback[0] as GetCompletionWithToolsTurnTelemetryEvent;
            expect(telemetryEvent.telemetry.properties.featureFlags).toBeDefined();
            expect(telemetryEvent.telemetry.properties.featureFlags).toContain("someFlag");
        });
    });

    describe("tool execution telemetry", () => {
        it("should handle tool_execution event and emit tool call executed telemetry", async () => {
            // First add a tool call to turn data
            const assistantEvent = {
                kind: "message" as const,
                message: {
                    role: "assistant" as const,
                    content: "I'll help you with that.",
                    tool_calls: [
                        {
                            id: "call_123",
                            type: "function" as const,
                            function: {
                                name: "test_tool",
                                arguments: '{"param1": "value1"}',
                            },
                        },
                    ],
                },
                turn: 0,
            };

            await telemetryEmitter.ingestEvent(assistantEvent);

            // Now add the tool execution event
            const toolExecutionEvent = {
                kind: "tool_execution" as const,
                turn: 0,
                toolCallId: "call_123",
                durationMs: 1500,
                toolResult: {
                    textResultForLlm: "Tool execution result",
                    resultType: "success" as const,
                    toolTelemetry: {
                        properties: {
                            customProp: "custom_value",
                        },
                        restrictedProperties: {
                            sensitiveData: "sensitive_value",
                        },
                        metrics: {
                            customMetric: 42,
                        },
                    },
                },
            };

            await telemetryEmitter.ingestEvent(toolExecutionEvent);

            expect(eventsSentToMockCallback).toHaveLength(1);
            const telemetryEvent = eventsSentToMockCallback[0] as ToolCallExecutedTelemetry;

            expect(telemetryEvent.kind).toBe("telemetry");
            expect(telemetryEvent.telemetry.event).toBe("tool_call_executed");
            expect(telemetryEvent.telemetry.properties.resultType).toBe("success");
            expect(telemetryEvent.telemetry.properties.toolCallId).toBe("call_123");
            expect(telemetryEvent.telemetry.properties.customProp).toBe("custom_value");
            expect(telemetryEvent.telemetry.properties.toolName).toEqual(
                "c877fbde6877ae812fa11d00e82fc0628258f70e816180f06b725753e95d8c5a",
            );
            expect(telemetryEvent.telemetry.properties.arguments).toEqual("");

            expect(telemetryEvent.telemetry.restrictedProperties.arguments).toBe('{"param1": "value1"}');
            expect(telemetryEvent.telemetry.restrictedProperties.toolName).toBe("test_tool");
            expect(telemetryEvent.telemetry.restrictedProperties.sensitiveData).toBe("sensitive_value");

            expect(telemetryEvent.telemetry.metrics.durationMs).toBe(1500);
            expect(telemetryEvent.telemetry.metrics.customMetric).toBe(42);
        });

        it("should handle tool_execution event with error", async () => {
            // First add a tool call to turn data
            const assistantEvent = {
                kind: "message" as const,
                message: {
                    role: "assistant" as const,
                    content: "I'll help you with that.",
                    tool_calls: [
                        {
                            id: "call_456",
                            type: "function" as const,
                            function: {
                                name: "failing_tool",
                                arguments: '{"param1": "value1"}',
                            },
                        },
                    ],
                },
                turn: 0,
            };

            await telemetryEmitter.ingestEvent(assistantEvent);

            // Now add the tool execution event with failure
            const toolExecutionEvent = {
                kind: "tool_execution" as const,
                turn: 0,
                toolCallId: "call_456",
                durationMs: 500,
                toolResult: {
                    textResultForLlm: "Tool execution failed",
                    resultType: "failure" as const,
                    error: "Tool execution error message",
                    toolTelemetry: {
                        properties: {},
                        restrictedProperties: {},
                        metrics: {},
                    },
                },
            };

            await telemetryEmitter.ingestEvent(toolExecutionEvent);

            expect(eventsSentToMockCallback).toHaveLength(1);
            const telemetryEvent = eventsSentToMockCallback[0] as ToolCallExecutedTelemetry;

            expect(telemetryEvent.kind).toBe("telemetry");
            expect(telemetryEvent.telemetry.event).toBe("tool_call_executed");
            expect(telemetryEvent.telemetry.properties.resultType).toBe("failure");
            expect(telemetryEvent.telemetry.properties.toolCallId).toBe("call_456");

            expect(telemetryEvent.telemetry.restrictedProperties.error).toBe("Tool execution error message");
            expect(telemetryEvent.telemetry.restrictedProperties.toolName).toBe("failing_tool");

            expect(telemetryEvent.telemetry.metrics.durationMs).toBe(500);
        });

        it("should handle tool_execution event with safe telemetry settings", async () => {
            // Create emitter with tools that have safe telemetry settings
            const toolsWithSafeTelemetry: Tool[] = [
                {
                    name: "safe_tool",
                    description: "A safe tool",
                    input_schema: { type: "object", properties: {} },
                    callback: async () => ({
                        textResultForLlm: "result",
                        resultType: "success" as const,
                        toolTelemetry: {
                            properties: {},
                            restrictedProperties: {},
                            metrics: {},
                        },
                    }),
                    safeForTelemetry: true,
                },
            ];

            const safeEmitter = new AgentTelemetryEmitter(
                "test-agent",
                mockCallback,
                {} as DeepPartial<RuntimeSettings>,
                toolsWithSafeTelemetry,
            );

            // First add a tool call to turn data
            const assistantEvent = {
                kind: "message" as const,
                message: {
                    role: "assistant" as const,
                    content: "I'll help you with that.",
                    tool_calls: [
                        {
                            id: "call_789",
                            type: "function" as const,
                            function: {
                                name: "safe_tool",
                                arguments: '{"param1": "value1"}',
                            },
                        },
                    ],
                },
                turn: 0,
            };

            await safeEmitter.ingestEvent(assistantEvent);

            // Now add the tool execution event
            const toolExecutionEvent = {
                kind: "tool_execution" as const,
                turn: 0,
                toolCallId: "call_789",
                durationMs: 800,
                toolResult: {
                    textResultForLlm: "Tool execution result",
                    resultType: "success" as const,
                    toolTelemetry: {
                        properties: {
                            customProp: "custom_value",
                        },
                        restrictedProperties: {
                            sensitiveData: "sensitive_value",
                        },
                        metrics: {
                            customMetric: 100,
                        },
                    },
                },
            };

            await safeEmitter.ingestEvent(toolExecutionEvent);

            expect(eventsSentToMockCallback).toHaveLength(1);
            const telemetryEvent = eventsSentToMockCallback[0] as ToolCallExecutedTelemetry;

            expect(telemetryEvent.kind).toBe("telemetry");
            expect(telemetryEvent.telemetry.event).toBe("tool_call_executed");
            expect(telemetryEvent.telemetry.properties.resultType).toBe("success");
            expect(telemetryEvent.telemetry.properties.toolCallId).toBe("call_789");
            expect(telemetryEvent.telemetry.properties.customProp).toBe("custom_value");

            // For safe tools, the tool name should not be hashed
            expect(telemetryEvent.telemetry.properties.toolName).toBe("safe_tool");

            // Arguments should still be hashed even for safe tools (because safeForTelemetry: true doesn't include raw arguments)
            const safeToolArgs = telemetryEvent.telemetry.properties.arguments;
            expect(typeof safeToolArgs).toBe("string");
            if (typeof safeToolArgs === "string") {
                expect(JSON.parse(safeToolArgs)).toEqual({
                    param1: expect.any(String),
                });
            }

            expect(telemetryEvent.telemetry.restrictedProperties.arguments).toBe('{"param1": "value1"}');
            expect(telemetryEvent.telemetry.restrictedProperties.toolName).toBe("safe_tool");
            expect(telemetryEvent.telemetry.restrictedProperties.sensitiveData).toBe("sensitive_value");

            expect(telemetryEvent.telemetry.metrics.durationMs).toBe(800);
            expect(telemetryEvent.telemetry.metrics.customMetric).toBe(100);
        });

        it("should not emit tool execution telemetry if tool call data not found", async () => {
            // Add a tool execution event without first adding the corresponding assistant message
            const toolExecutionEvent = {
                kind: "tool_execution" as const,
                turn: 0,
                toolCallId: "nonexistent_call",
                durationMs: 1000,
                toolResult: {
                    textResultForLlm: "Tool execution result",
                    resultType: "success" as const,
                    toolTelemetry: {
                        properties: {},
                        restrictedProperties: {},
                        metrics: {},
                    },
                },
            };

            await telemetryEmitter.ingestEvent(toolExecutionEvent);

            // No telemetry should be emitted because the tool call data wasn't found
            expect(eventsSentToMockCallback).toHaveLength(0);
        });

        it("should handle tool_execution event with missing error field", async () => {
            // First add a tool call to turn data
            const assistantEvent = {
                kind: "message" as const,
                message: {
                    role: "assistant" as const,
                    content: "I'll help you with that.",
                    tool_calls: [
                        {
                            id: "call_no_error",
                            type: "function" as const,
                            function: {
                                name: "test_tool",
                                arguments: '{"param1": "value1"}',
                            },
                        },
                    ],
                },
                turn: 0,
            };

            await telemetryEmitter.ingestEvent(assistantEvent);

            // Tool execution event without error field
            const toolExecutionEvent = {
                kind: "tool_execution" as const,
                turn: 0,
                toolCallId: "call_no_error",
                durationMs: 750,
                toolResult: {
                    textResultForLlm: "Tool execution result",
                    resultType: "success" as const,
                    // No error field
                    toolTelemetry: {
                        properties: {},
                        restrictedProperties: {},
                        metrics: {},
                    },
                },
            };

            await telemetryEmitter.ingestEvent(toolExecutionEvent);

            expect(eventsSentToMockCallback).toHaveLength(1);
            const telemetryEvent = eventsSentToMockCallback[0] as ToolCallExecutedTelemetry;

            expect(telemetryEvent.telemetry.restrictedProperties.error).toBeUndefined();
            expect(telemetryEvent.telemetry.metrics.durationMs).toBe(750);
        });
    });
});
