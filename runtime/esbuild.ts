/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import * as watcher from "@parcel/watcher";
import dedent from "dedent";
import * as esbuild from "esbuild";
import { copy } from "esbuild-plugin-copy";
import path from "node:path";

const watch = process.argv.includes("--watch") || process.argv.includes("-w");

const nativeNodeModulesPlugin: esbuild.Plugin = {
    name: "native-node-modules",
    setup(build) {
        // If a ".node" file is imported within a module in the "file" namespace, resolve
        // it to an external dependency that esbuild will not bundle.
        build.onResolve({ filter: /\.node$/, namespace: "file" }, (args) => ({
            path: args.path,
            external: true,
        }));
    },
};

const build: esbuild.BuildOptions = {
    banner: {
        // This makes a dynamic `require` available at runtime for CJS
        // dependencies bundled into the output index.js file which is
        // transpiled and run as ESM. Note, it is added as a banner because
        // esbuild does not support injecting `require`.
        js: dedent`import { createRequire } from "module";
            const require = createRequire(import.meta.url);`,
    },
    bundle: true,
    entryPoints: [{ in: "src/cli/index.ts", out: "index" }],
    external: ["node-pty"],
    format: "esm",
    inject: ["./script/cjsShims.js"],
    logLevel: "info", // Required for the vscode problem matcher
    outdir: "dist-cli",
    platform: "node",
    plugins: [
        nativeNodeModulesPlugin,
        copy({
            assets: [{ from: "src/cli/README.md", to: "." }],
        }),
    ],
    sourcemap: "linked",
    sourcesContent: false,
    target: "node22",
    treeShaking: true,
};

async function watchRebuild(contexts: esbuild.BuildContext[]) {
    console.log("[watch] build started");
    for (const ctx of contexts) {
        try {
            await ctx.cancel();
            await ctx.rebuild();
        } catch (error) {
            console.error("[watch]", error);
        }
    }
    console.log("[watch] build finished");
}

async function main() {
    // Build mode
    if (!watch) {
        return await esbuild.build(build);
    }

    // Watch mode
    const context = await esbuild.context(build);
    let debounce: NodeJS.Timeout | undefined;

    // Function to rebuild when files change with a debounce
    function rebuildDebounced() {
        if (debounce) clearTimeout(debounce);
        debounce = setTimeout(() => void watchRebuild([context]), 100);
    }

    // Initial build
    rebuildDebounced();

    // Watch for file changes
    await watcher.subscribe(path.join(import.meta.dirname, "src"), (err, events) => {
        if (err) {
            console.error(`File watcher error: ${err.message}`);
            return;
        }
        for (const event of events) {
            console.log(`File change detected: ${event.path}`);
        }
        rebuildDebounced();
    });
}
void main();
