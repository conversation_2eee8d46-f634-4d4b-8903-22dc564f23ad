/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

// @ts-check

import eslint from "@eslint/js";
import tseslint from "typescript-eslint";
import headers from "eslint-plugin-headers";
import eslintConfigPrettier from "eslint-config-prettier/flat";
import globals from "globals";

export default tseslint.config(
    eslint.configs.recommended,
    tseslint.configs.recommended,
    {
        plugins: {
            headers,
        },
        rules: {
            "@typescript-eslint/no-unused-vars": [
                "error",
                {
                    args: "all",
                    argsIgnorePattern: "^_",
                    caughtErrors: "all",
                    caughtErrorsIgnorePattern: "^_",
                    destructuredArrayIgnorePattern: "^_",
                    varsIgnorePattern: "^_",
                    ignoreRestSiblings: true,
                },
            ],
            "headers/header-format": [
                // warn for now - remove this after big CLI PR
                "error",
                {
                    source: "string",
                    content: `Copyright (c) Microsoft Corporation. All rights reserved.`,
                    linePrefix: " *  ",
                    blockPrefix:
                        "---------------------------------------------------------------------------------------------\n",
                    blockSuffix:
                        "\n *--------------------------------------------------------------------------------------------",
                },
            ],
            "@typescript-eslint/no-floating-promises": "error",
            "@typescript-eslint/no-misused-promises": [
                "error",
                {
                    checksConditionals: true,
                    checksSpreads: true,
                    checksVoidReturn: true,
                },
            ],
            "comma-dangle": ["error", "always-multiline"],
            "no-trailing-spaces": "error",
        },
        languageOptions: {
            parserOptions: {
                projectService: true,
                tsconfigRootDir: import.meta.dirname,
            },
            globals: globals.node,
        },
    },
    {
        ignores: [
            "dist/**",
            "dist-mcp/**",
            "dist-mcp-client/**",
            "dist-cli/**",
            "action/script/log-visualizer/dist/**",

            // TODO: This file has a number of lint issues before it can be
            // included in linting (but without type checking).
            "firewall/test/smoke-test.js",
        ],
    },
    {
        // Disable type-checked linting for files not included in the tsconfig.
        files: ["script/**/*.js", "eslint.*.mjs", "rollup.*.ts", "vitest.config.ts", "vitest.workspace.ts"],
        extends: [tseslint.configs.disableTypeChecked],
    },
    eslintConfigPrettier,
);
