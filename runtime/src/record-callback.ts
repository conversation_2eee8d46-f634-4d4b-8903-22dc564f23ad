/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { AgentAction, AgentCallbackPayload, AgentUpdateKind } from "./agent";
import { AgentUpdateContentMap } from "./callbacks/callback";
import { ghFetch } from "./helpers/gh-fetch-helpers";
import { Runner } from "./runner";
import { RuntimeSettings } from "./settings";
import { demandCopilotIntegrationId, demandCopilotToken } from "./settings/demand";
import { addFeatureFlags, isTelemetryEvent, redactTelemetry } from "./telemetry";

export function newRecordCallback<K extends AgentUpdateKind>(
    callbackUrl: string,
    runner: Runner,
    repo: string,
    repo_id: number,
    repo_name: string,
    repo_owner: string,
    repo_owner_id: number,
    job_id: string,
    action: AgentAction,
    settings: RuntimeSettings,
): (kind: K, content: AgentUpdateContentMap[K]) => Promise<void> {
    return async (kind: K, content: AgentUpdateContentMap[K]) => {
        if (isTelemetryEvent(content)) {
            addFeatureFlags(content, settings);
            redactTelemetry(content);
        }

        const payload: AgentCallbackPayload = {
            job_id: job_id,
            kind: kind,
            action: action,
            created_at: new Date().toISOString(), // ISO 8601 format: YYYY-MM-DDTHH:mm:ss.sssZ
            repo_id: repo_id,
            repo_name: repo_name,
            repo: repo,
            repo_owner_id: repo_owner_id,
            repo_owner: repo_owner,
            content: JSON.stringify(content),
        };

        const headers: Record<string, string> = {
            "Content-Type": "application/json",
        };

        // Make sure the url ends in a trailing slash to ensure appending
        // jobs to the URL works as expected.
        let url = new URL(callbackUrl);
        if (!url.pathname.endsWith("/")) {
            url.pathname += "/";
        }
        url = new URL("./jobs", url);

        // If we are running in a GitHub Actions workflow, so we need to use the
        // CAPI to send the callback. Ensure this and add the necessary headers.
        if (runner.type === "GITHUB_ACTIONS") {
            const integrationId = demandCopilotIntegrationId(settings);
            headers["Copilot-Integration-Id"] = integrationId;

            const token = demandCopilotToken(settings);
            headers["Authorization"] = `Bearer ${token}`;

            // Add the nonce header for job validation
            if (settings.job?.nonce) {
                headers["X-GitHub-Job-Nonce"] = settings.job?.nonce;
            }
        }

        try {
            await ghFetch(
                url,
                {
                    method: "POST",
                    body: JSON.stringify(payload),
                    headers: headers,
                },
                runner.logger,
                "agent callback",
            );
        } catch (err) {
            const error = err as Error;
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            const requestId = (error as any)?.requestId || "unknown";
            runner.logger.error(`Failed to send to agent callback endpoint (request ID: ${requestId}): ${error}`);
        }
    };
}
