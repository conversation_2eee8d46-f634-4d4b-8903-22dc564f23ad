/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

export declare type AgentAction = "fix" | "fix-pr-comment";

export declare interface AgentArgs {
    repo: string;
    repoId: number;
    repoName: string;
    repoOwner: string;
    repoOwnerId: number;
    baseCommit: string;
    problemStatement: string;
    jobId: string;
    callbackUrl?: string;
    issueNumber?: number;
    push: boolean;
    agent?: string;
    action: AgentAction;
    encryptedToken: string;
    actorLogin: string;
    prNumber?: number; // only for fix after PR is first created
    branchName?: string; // only for fix after PR is first created
    commitCount?: number; // only for fix after PR is first created
}

export declare type AgentUpdateKind = "progress" | "partialResult" | "commentReply" | "result" | "error";

export declare interface AgentCallbackPayload {
    job_id: string;
    kind: AgentUpdateKind;
    action: AgentAction;
    created_at: string; // ISO 8601 timestamp
    repo: string; // owner/repo
    repo_id: number;
    repo_name: string;
    repo_owner: string;
    repo_owner_id: number;
    // For a result, this is a JSON payload.  For each action the payload is different:
    // - For "fix", the payload is a JSON object with the following keys: diff, branchName, prDescription
    // content: any;
    content: string;
}
