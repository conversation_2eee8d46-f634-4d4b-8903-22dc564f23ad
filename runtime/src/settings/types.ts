/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { AgentAction } from "../agent";
import { ContentFilterMode } from "../helpers/content-filter";
import { ClientOptions } from "../model/client";

// Helper type to make all properties and nested properties optional
export type DeepPartial<T> = {
    [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RuntimeSettings = DeepPartial<IRuntimeSettings>;
/**
 * The complete settings type that represents all possible configuration options.
 */
interface IRuntimeSettings {
    github: {
        serverUrl: string;
        host: string; // Defaults to host from the serverUrl
        hostProtocol: string; // Defaults to protocol from the serverUrl
        token: string;
        user: {
            name: string;
            email: string;
            // ID of the user that triggered the agent
            actorId?: number;
            // Login (username) of the user that triggered the agent
            actorLogin?: string;
        };
        owner: {
            id: number;
            name: string;
        };
        repo: {
            id: number;
            name: string;
            branch: string;
            commit: string;
            readWrite: boolean;
        };
        issue: {
            number?: number;
        };
        pr: {
            number: number;
            commitCount?: number;
        };
    };
    problem: {
        statement: string;
        contentFilterMode?: ContentFilterMode;
        action: AgentAction;
    };
    service: {
        instance: {
            id: string;
        };
        /**
         * - Options beyond `model` are currently only respected
         * when going through `RuntimeHarness` methods.
         * - When going through `RuntimeHarness` methods, the value
         * of the `model` option should be: `AgentName:ModelName`.
         */
        agent: ClientOptions;
        /**
         * Settings for tools that are used by the agent. Refer to the
         * source/documentation for each tool for their specific settings.
         */
        tools: { [toolName: string]: { [key: string]: unknown } };
        callback: {
            url: string;
        };
    };
    api: {
        aipSweAgent: {
            token: string;
        };
        anthropic: {
            key: string;
        };
        openai: {
            baseUrl: string;
            apiKey: string;
            azureKeyVaultUri: string;
            azureSecretName: string;
            azure: {
                url: string;
                apiVersion: string;
            };
        };
        copilot: {
            url: string;
            integrationId: string;
            hmacKey: string;
            azureKeyVaultUri: string;
            token: string;
            useSessions: boolean;
            sessionId: string;
        };
        github: {
            /**
             * The GITHUB_PERSONAL_ACCESS_TOKEN that is passed to `github-mcp-server` when it is
             * started. (Currently only supported in the `cpd` entry point.)
             */
            mcpServerToken: string;
        };
    };
    blackbird: {
        mode: "initial-search" | "tool";
        backfillScoreThreshold?: number;
        repoNwo?: string;
        /**
         * The auth object contains the credentials for Blackbird's Metis API.
         * - modelBasedRetrievalToken: Token for model-based retrieval.
         * - metisApiKey: API key for Metis.
         */
        auth: {
            modelBasedRetrievalToken: string;
            metisApiKey: string;
        };
    };
    swebench_base_commit?: string;
    trajectory: {
        outputFile: string;
    };
    job: {
        nonce?: string;
    };
    onlineEvaluation: {
        disableOnlineEvaluation?: boolean;
        enableOnlineEvaluationOutputFile?: boolean;
    };
    tools: {
        bash: {
            /**
             * The default timeout for bash commands in seconds. If undefined, a default of 120 seconds (2 minutes) is used.
             */
            defaultTimeout?: number;
        };
    };
    /**
     * The set of feature flags passed to the agent runtime process by sweagentd.
     *
     * Only flags listed in internal/launcher/runtime_feature_flags.go are passed
     * to the runtime.
     *
     * To add a new flag:
     * - Define it in accordance with the feature flag docs: https://thehub.github.com/epd/engineering/products-and-services/dotcom/features/feature-flags/
     * - Add it to runtime_feature_flags.txt
     * - Check whether it exists in the following object.
     *
     * Read a feature flag value with: @see isFeatureFlagEnabled
     *
     *    if (isFeatureFlagEnabled(settings, 'copilot_swe_agent_flag_name')) {
     *    }
     *
     * Report feature flag values in telemetry with: @see featureFlagsAsString in
     * a property named @see FEATURE_FLAGS_TELEMETRY_PROPERTY_NAME.
     *
     * NOTE: feature flag names may be visible to the user in logs or other output.
     */
    featureFlags: { [key: string]: boolean };

    /**
     * How many ms the runtime/the thing hosting the runtime has available to run
     * before it is considered to have timed out.
     */
    timeoutMs: number;

    /**
     * The time when the runtime/the thing hosting the runtime started, in ms since epoch.
     * May not be 100% accurate. Not typically set by hand.
     */
    startTimeMs: number;
}

/**
 * Checks if the specified feature flag is enabled in the runtime settings.
 */
export function isFeatureFlagEnabled(settings: RuntimeSettings | undefined, flagName: string): boolean {
    return settings?.featureFlags?.[flagName] ?? false;
}

/**
 * Gets a comma-separated string of enabled feature flags from the runtime settings.
 *
 * This is particularly useful for telemetry.
 */
export function featureFlagsAsString(settings: RuntimeSettings): string {
    return Object.keys(settings.featureFlags ?? {})
        .filter((key) => settings.featureFlags?.[key])
        .join(",");
}

/**
 * If any {@link RuntimeSettings} have this name, their values should be considered secrets.
 */
const secretSettingNames = ["token", "secret", "key", "hmacKey", "modelBasedRetrievalToken", "metisApiKey"];

/**
 * Recurses through the {@param settingsObj}, finding any settings with a key that is in {@link secretSettingNames}.
 * - Use signature without {@param accumulator} as the first call.
 * - Use signature with {@param accumulator} for recursive calls.
 */
export function getSettingsSecretVals(settings: RuntimeSettings): string[];
export function getSettingsSecretVals(subSettings: object, accumulator: string[]): string[];
export function getSettingsSecretVals(
    settingsOrSubSettings: RuntimeSettings | object,
    accumulator: string[] = [],
): string[] {
    for (const [key, value] of Object.entries(settingsOrSubSettings)) {
        if (value === null) {
            continue;
        }
        switch (typeof value) {
            case "object":
                getSettingsSecretVals(value, accumulator);
                break;
            case "undefined":
                break;
            default:
                if (secretSettingNames.includes(key)) {
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    const stringVal = (value as any).toString().trim();
                    if (stringVal != "") {
                        accumulator.push(stringVal);
                    }
                }
                break;
        }
    }
    return accumulator;
}

/**
 * Determines if the current time is within a specified percentage threshold of the timeout.
 */
export function isWithinPercentOfTimeout(
    settings: RuntimeSettings,
    percentRemaining: number,
): { isWithin: boolean; withinMs: number } {
    // Handle edge cases for number input
    if (isNaN(percentRemaining) || !isFinite(percentRemaining) || percentRemaining <= 0) {
        // If input is not a valid number, return false
        return { isWithin: false, withinMs: percentRemaining };
    }

    if (percentRemaining > 1) {
        // If given a non-percentage value, return false
        return { isWithin: false, withinMs: percentRemaining };
    }

    if (!settings.timeoutMs) {
        // No timeout set, so not within the threshold
        return { isWithin: false, withinMs: percentRemaining };
    }

    // Calculate what percentage of time remaining would equal this threshold
    const msRemaining = settings.timeoutMs * percentRemaining;

    if (!settings.startTimeMs) {
        // No start time set, so we can't determine if we're close to timeout
        return { isWithin: false, withinMs: msRemaining };
    }

    const currDuration = Date.now() - settings.startTimeMs;
    const close = currDuration >= settings.timeoutMs - msRemaining;
    return { isWithin: close, withinMs: msRemaining };
}
