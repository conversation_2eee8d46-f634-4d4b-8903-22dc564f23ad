/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { v4 as uuidv4 } from "uuid";
import type { AgentAction } from "../agent";
import { ContentFilterMode } from "../helpers/content-filter";
import { ClientRetryPolicy } from "../model/client";
import { DeepPartial, RuntimeSettings } from "./types";

/**
 * A builder class for constructing runtime settings in a fluent, type-safe way.
 * Each method sets a single property and returns the builder for chaining.
 */
export class SettingsBuilder {
    private settings: RuntimeSettings = {};

    // Helper method to safely merge nested partial objects (shallow merge)
    private mergePartial<T>(current: DeepPartial<T> | undefined, update: DeepPartial<T>): DeepPartial<T> {
        return { ...current, ...update };
    }

    setBlackbirdMode(mode: string | undefined): this {
        if (mode !== undefined && (mode == "tool" || mode == "initial-search")) {
            this.settings.blackbird = this.mergePartial(this.settings.blackbird, { mode });
        }
        return this;
    }

    setSwebenchBaseCommit(commit: string | undefined): this {
        if (commit !== undefined) {
            this.settings.swebench_base_commit = commit;
        }
        return this;
    }

    // GitHub User Settings
    setGithubUserName(name: string | undefined): this {
        if (name !== undefined) {
            const currentUser = this.settings.github?.user || {};
            this.settings.github = this.mergePartial(this.settings.github, {
                user: { ...currentUser, name },
            });
        }
        return this;
    }

    setGithubUserEmail(email: string | undefined): this {
        if (email !== undefined) {
            const currentUser = this.settings.github?.user || {};
            this.settings.github = this.mergePartial(this.settings.github, {
                user: { ...currentUser, email },
            });
        }
        return this;
    }

    // GitHub Settings
    setGithubToken(token: string | undefined): this {
        if (token !== undefined) {
            this.settings.github = this.mergePartial(this.settings.github, {
                token,
            });
        }
        return this;
    }

    setGithubServerUrl(serverUrl: string | undefined): this {
        if (serverUrl !== undefined) {
            this.settings.github = this.mergePartial(this.settings.github, {
                serverUrl: serverUrl ?? "https://github.com",
            });
            if (!this.settings.github.host || !this.settings.github.hostProtocol) {
                this.settings.github = this.mergePartial(this.settings.github, new URL(serverUrl));
            }
        }
        return this;
    }

    setGithubHost(host: string | undefined): this {
        if (host !== undefined) {
            this.settings.github = this.mergePartial(this.settings.github, {
                host,
            });
        }
        return this;
    }

    setGithubHostProtocol(hostProtocol: string | undefined): this {
        if (hostProtocol !== undefined) {
            this.settings.github = this.mergePartial(this.settings.github, {
                hostProtocol,
            });
        }
        return this;
    }
    setGithubActorId(actorId: number | undefined): this {
        if (actorId !== undefined) {
            const currentUser = this.settings.github?.user || {};
            this.settings.github = this.mergePartial(this.settings.github, {
                user: { ...currentUser, actorId },
            });
        }
        return this;
    }

    setGithubActorLogin(actorLogin: string | undefined): this {
        if (actorLogin !== undefined) {
            const currentUser = this.settings.github?.user || {};
            this.settings.github = this.mergePartial(this.settings.github, {
                user: { ...currentUser, actorLogin },
            });
        }
        return this;
    }

    setGithubRepoName(name: string | undefined): this {
        if (name !== undefined) {
            const currentRepo = this.settings.github?.repo || {};
            this.settings.github = this.mergePartial(this.settings.github, {
                repo: { ...currentRepo, name },
            });
        }
        return this;
    }

    setGithubRepoId(idStr: string | undefined): this {
        if (idStr !== undefined) {
            const currentRepo = this.settings.github?.repo || {};
            const id = Number(idStr);
            if (!isNaN(id)) {
                this.settings.github = this.mergePartial(this.settings.github, {
                    repo: { ...currentRepo, id },
                });
            }
        }
        return this;
    }

    setGithubRepoOwnerName(name: string | undefined): this {
        if (name !== undefined) {
            const currentOwner = this.settings.github?.owner || {};
            this.settings.github = this.mergePartial(this.settings.github, {
                owner: { ...currentOwner, name },
            });
        }
        return this;
    }

    setGithubRepoOwnerId(idStr: string | undefined): this {
        if (idStr !== undefined) {
            const currentOwner = this.settings.github?.owner || {};
            const id = Number(idStr);
            if (!isNaN(id)) {
                this.settings.github = this.mergePartial(this.settings.github, {
                    owner: { ...currentOwner, id },
                });
            }
        }
        return this;
    }

    setGithubRepoBranch(branch: string | undefined | null): this {
        if (branch) {
            // Ensure branch name is valid and remove any ref prefix
            const branchName = branch.replace(/^refs\/heads\//, "");
            if (branchName.length === 0) {
                return this;
            }
            const currentRepo = this.settings.github?.repo || {};
            this.settings.github = this.mergePartial(this.settings.github, {
                repo: { ...currentRepo, branch: branchName },
            });
        }
        return this;
    }

    setGithubRepoCommit(commit: string | undefined): this {
        if (commit !== undefined) {
            const currentRepo = this.settings.github?.repo || {};
            this.settings.github = this.mergePartial(this.settings.github, {
                repo: { ...currentRepo, commit },
            });
        }
        return this;
    }

    setGithubRepoReadWrite(readWrite: boolean | undefined): this {
        if (readWrite !== undefined) {
            const currentRepo = this.settings.github?.repo || {};
            this.settings.github = this.mergePartial(this.settings.github, {
                repo: { ...currentRepo, readWrite },
            });
        }
        return this;
    }

    // Problem Settings
    setProblemStatement(statement: string | undefined): this {
        // Filter the problem statement
        if (statement !== undefined) {
            this.settings.problem = this.mergePartial(this.settings.problem, {
                statement,
            });
        }
        return this;
    }

    setProblemContentFilterMode(contentFilterMode: ContentFilterMode | string | undefined): this {
        if (contentFilterMode !== undefined) {
            this.settings.problem = this.mergePartial(this.settings.problem, {
                contentFilterMode: contentFilterMode as ContentFilterMode,
            });
        }
        return this;
    }

    setProblemAction(action: AgentAction | undefined): this {
        if (action !== undefined) {
            this.settings.problem = this.mergePartial(this.settings.problem, {
                action,
            });
        }
        return this;
    }

    // GitHub Issue and PR Settings
    setGithubIssueNumber(number: number | undefined): this {
        if (number !== undefined) {
            this.settings.github = this.mergePartial(this.settings.github, {
                issue: { number },
            });
        }
        return this;
    }

    setGithubPRNumber(number: number | undefined): this {
        if (number !== undefined) {
            this.settings.github = this.mergePartial(this.settings.github, {
                pr: { number },
            });
        }
        return this;
    }

    setGithubPRCommitCount(count: number | undefined): this {
        if (count !== undefined) {
            const currentPR = this.settings.github?.pr || {};
            this.settings.github = this.mergePartial(this.settings.github, {
                pr: { ...currentPR, commitCount: count },
            });
        }
        return this;
    }

    // Service Settings
    setInstanceId(id: string | undefined): this {
        this.settings.service = this.mergePartial(this.settings.service, {
            instance: { id: id ?? uuidv4() },
        });
        return this;
    }

    setAgentModel(model: string | undefined): this {
        if (model !== undefined) {
            const currentAgent = this.settings.service?.agent || {};
            this.settings.service = this.mergePartial(this.settings.service, {
                agent: { ...currentAgent, model },
            });
        }
        return this;
    }

    setRequestHeaders(headers: Record<string, string> | undefined): this {
        if (headers !== undefined) {
            const currentAgent = this.settings.service?.agent || {};
            this.settings.service = this.mergePartial(this.settings.service, {
                agent: { ...currentAgent, requestHeaders: headers },
            });
        }
        return this;
    }

    setRetryPolicy(retryPolicy: ClientRetryPolicy | undefined): this {
        if (retryPolicy !== undefined) {
            const currentAgent = this.settings.service?.agent || {};
            this.settings.service = this.mergePartial(this.settings.service, {
                agent: { ...currentAgent, retryPolicy },
            });
        }
        return this;
    }

    setCallbackUrl(url: string | undefined): this {
        if (url !== undefined) {
            this.settings.service = this.mergePartial(this.settings.service, {
                callback: { url },
            });
        }
        return this;
    }

    // API Settings
    setAipSweAgentToken(token: string | undefined): this {
        if (token !== undefined) {
            this.settings.api = this.mergePartial(this.settings.api, {
                aipSweAgent: { token },
            });
        }
        return this;
    }

    setAnthropicApiKey(key: string | undefined): this {
        if (key !== undefined) {
            this.settings.api = this.mergePartial(this.settings.api, {
                anthropic: { key },
            });
        }
        return this;
    }

    setOpenAiApiKey(key: string | undefined): this {
        if (key !== undefined) {
            const currentOpenAI = this.settings.api?.openai || {};
            this.settings.api = this.mergePartial(this.settings.api, {
                openai: { ...currentOpenAI, apiKey: key },
            });
        }
        return this;
    }

    setOpenAiBaseUrl(baseUrl: string | undefined): this {
        if (baseUrl !== undefined) {
            const currentOpenAI = this.settings.api?.openai || {};
            this.settings.api = this.mergePartial(this.settings.api, {
                openai: { ...currentOpenAI, baseUrl },
            });
        }
        return this;
    }

    setAzureOpenAiUrl(url: string | undefined): this {
        if (url !== undefined) {
            const currentOpenAI = this.settings.api?.openai || {};
            this.settings.api = this.mergePartial(this.settings.api, {
                openai: {
                    ...currentOpenAI,
                    azure: { ...currentOpenAI.azure, url },
                },
            });
        }
        return this;
    }

    setAzureOpenAiApiVersion(apiVersion: string | undefined): this {
        if (apiVersion !== undefined) {
            const currentOpenAI = this.settings.api?.openai || {};
            this.settings.api = this.mergePartial(this.settings.api, {
                openai: {
                    ...currentOpenAI,
                    azure: { ...currentOpenAI.azure, apiVersion },
                },
            });
        }
        return this;
    }

    setAzureOpenAiKeyVaultUri(uri: string | undefined): this {
        if (uri !== undefined) {
            const currentOpenAI = this.settings.api?.openai || {};
            this.settings.api = this.mergePartial(this.settings.api, {
                openai: { ...currentOpenAI, azureKeyVaultUri: uri },
            });
        }
        return this;
    }

    setAzureOpenAiSecretName(secretName: string | undefined): this {
        if (secretName !== undefined) {
            const currentOpenAI = this.settings.api?.openai || {};
            this.settings.api = this.mergePartial(this.settings.api, {
                openai: { ...currentOpenAI, azureSecretName: secretName },
            });
        }
        return this;
    }

    setCopilotUrl(url: string | undefined): this {
        if (url !== undefined) {
            const currentCopilot = this.settings.api?.copilot || {};
            this.settings.api = this.mergePartial(this.settings.api, {
                copilot: { ...currentCopilot, url },
            });
        }
        return this;
    }

    setCopilotIntegrationId(id: string | undefined): this {
        if (id !== undefined) {
            const currentCopilot = this.settings.api?.copilot || {};
            this.settings.api = this.mergePartial(this.settings.api, {
                copilot: { ...currentCopilot, integrationId: id },
            });
        }
        return this;
    }

    setCopilotHmacKey(key: string | undefined): this {
        if (key !== undefined) {
            const currentCopilot = this.settings.api?.copilot || {};
            this.settings.api = this.mergePartial(this.settings.api, {
                copilot: { ...currentCopilot, hmacKey: key },
            });
        }
        return this;
    }

    setCopilotToken(token: string | undefined): this {
        if (token !== undefined) {
            const currentCopilot = this.settings.api?.copilot || {};
            this.settings.api = this.mergePartial(this.settings.api, {
                copilot: { ...currentCopilot, token },
            });
        }
        return this;
    }

    setCopilotAzureKeyVaultUri(uri: string | undefined): this {
        if (uri !== undefined) {
            const currentCopilot = this.settings.api?.copilot || {};
            this.settings.api = this.mergePartial(this.settings.api, {
                copilot: { ...currentCopilot, azureKeyVaultUri: uri },
            });
        }
        return this;
    }

    setCopilotSessionId(sessionId: string | undefined): this {
        if (sessionId !== undefined) {
            const currentCopilot = this.settings.api?.copilot || {};
            this.settings.api = this.mergePartial(this.settings.api, {
                copilot: { ...currentCopilot, sessionId },
            });
        }
        return this;
    }

    setCopilotUseSessions(useSessions: boolean | undefined): this {
        if (useSessions !== undefined) {
            const currentCopilot = this.settings.api?.copilot || {};
            this.settings.api = this.mergePartial(this.settings.api, {
                copilot: { ...currentCopilot, useSessions },
            });
        }
        return this;
    }

    setCopilotJobNonce(nonce: string | undefined): this {
        if (nonce !== undefined) {
            this.settings.job = this.mergePartial(this.settings.job, { nonce });
        }
        return this;
    }

    // Trajectory Output Settings
    setTrajectoryOutputFile(outputFile: string | undefined): this {
        if (outputFile !== undefined) {
            this.settings.trajectory = this.mergePartial(this.settings.trajectory, {
                outputFile,
            });
        }
        return this;
    }

    setDisableOnlineEvaluation(disableOnlineEvaluation?: boolean): this {
        if (disableOnlineEvaluation == true && this.settings.onlineEvaluation?.disableOnlineEvaluation !== true) {
            this.settings.onlineEvaluation = this.mergePartial(this.settings.onlineEvaluation, {
                disableOnlineEvaluation,
            });
        }
        return this;
    }

    setEnableOnlineEvaluationOutputFile(enableOnlineEvaluationOutputFile?: boolean): this {
        if (
            enableOnlineEvaluationOutputFile == true &&
            this.settings.onlineEvaluation?.enableOnlineEvaluationOutputFile !== true
        ) {
            this.settings.onlineEvaluation = this.mergePartial(this.settings.onlineEvaluation, {
                enableOnlineEvaluationOutputFile,
            });
        }
        return this;
    }

    setGitHubMCPServerToken(token: string | undefined): this {
        if (token !== undefined) {
            this.settings.api = this.mergePartial(this.settings.api, {
                github: {
                    mcpServerToken: token,
                },
            });
        }

        return this;
    }

    setFeatureFlagEnabled(featureFlag: string): this {
        this.settings.featureFlags = this.mergePartial(this.settings.featureFlags, {
            [featureFlag]: true,
        });

        return this;
    }

    setTimeoutMs(timeoutMs: number | undefined): this {
        if (timeoutMs !== undefined) {
            this.settings.timeoutMs = timeoutMs;
        }
        return this;
    }

    setStartTimeMs(startTimeMs: number | undefined): this {
        if (startTimeMs !== undefined) {
            this.settings.startTimeMs = startTimeMs;
        }
        return this;
    }

    build(): RuntimeSettings {
        return this.settings;
    }
}
