/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import merge from "lodash.merge";
import { getCurrentSettings, hasCurrentSettings, setCurrentSettings } from "./current";
import { loadEnvironmentSettings } from "./environment-settings";
import { RuntimeSettings } from "./types";

async function initEnvSettings(overrides: RuntimeSettings = {}): Promise<RuntimeSettings> {
    const envSettings = await loadEnvironmentSettings();
    return setCurrentSettings(merge({}, envSettings, overrides));
}

export async function getOrInitSettings(): Promise<RuntimeSettings> {
    if (!hasCurrentSettings()) {
        return setCurrentSettings(await initSettings());
    }
    return getCurrentSettings()!;
}

export async function initSettings(overrides: RuntimeSettings = {}): Promise<RuntimeSettings> {
    if (hasCurrentSettings()) {
        throw new Error("Settings have already been initialized");
    }

    return initEnvSettings(overrides);
}
