/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

// This file includes methods that can be used to simply get or set current settings.
// Keeping all of this separate from the factory allows it to be imported when needed
// w/o needing to import the entire factory module - which can cause circular dependencies.

import { RuntimeSettings } from "./types";

let settings: RuntimeSettings | null = null;

export function hasCurrentSettings(): boolean {
    return !!settings;
}

export function getCurrentSettings(): RuntimeSettings {
    if (!settings) {
        throw new Error("Settings have not been initialized");
    }
    return settings;
}

export function setCurrentSettings(newSettings: RuntimeSettings): RuntimeSettings {
    settings = newSettings;
    return newSettings;
}

export function clearCurrentSettings(): void {
    settings = null;
}
