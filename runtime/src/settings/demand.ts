/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { AgentAction } from "../agent";
import { ContentFilterMode } from "../helpers/content-filter";
import { RuntimeSettings } from "./types";

const DEMAND_COPILOT_API_URL_DEFAULT = "https://api.githubcopilot.com";

export function demandGitHubUserName(settings: RuntimeSettings): string {
    if (!settings.github?.user?.name) {
        throw new Error("GitHub user name is required");
    }
    return settings.github.user.name;
}

export function demandGitHubUserEmail(settings: RuntimeSettings): string {
    if (!settings.github?.user?.email) {
        throw new Error("GitHub user email is required");
    }
    return settings.github.user.email;
}

export function getGithubActorInfo(settings: RuntimeSettings): { login: string; id: number } | undefined {
    const login = settings.github?.user?.actorLogin;
    const id = settings.github?.user?.actorId;

    if (!login || !id) {
        return undefined;
    }

    return { login, id };
}

export function demandGitHubServerUrl(settings: RuntimeSettings): string {
    if (!settings.github?.serverUrl) {
        throw new Error("GitHub server URL is required");
    }
    return settings.github.serverUrl;
}

export function demandGitHubHost(settings: RuntimeSettings): string {
    if (!settings.github?.host) {
        throw new Error("GitHub host is required");
    }
    return settings.github.host;
}

export function demandGitHubHostProtocol(settings: RuntimeSettings): string {
    if (!settings.github?.hostProtocol) {
        throw new Error("GitHub host protocol is required");
    }
    return settings.github.hostProtocol;
}

export function demandGitHubToken(settings: RuntimeSettings): string {
    if (!settings.github?.token) {
        throw new Error("GitHub token is required");
    }
    return settings.github.token;
}

export function demandGitHubRepoName(settings: RuntimeSettings): string {
    if (!settings.github?.repo?.name) {
        throw new Error("GitHub repository name is required");
    }
    return settings.github.repo.name;
}

export function demandGitHubRepoId(settings: RuntimeSettings): number {
    if (!settings.github?.repo?.id) {
        throw new Error("GitHub repository id is required");
    }
    return settings.github.repo.id;
}

export function demandGitHubRepoOwner(settings: RuntimeSettings): string {
    if (!settings.github?.owner?.name) {
        throw new Error("GitHub repository owner name is required");
    }
    return settings.github.owner.name;
}

export function demandGitHubRepoOwnerId(settings: RuntimeSettings): number {
    if (!settings.github?.owner?.id) {
        throw new Error("GitHub repository owner id is required");
    }
    return settings.github.owner.id;
}

export function demandGitHubRepoBranch(settings: RuntimeSettings): string {
    if (!settings.github?.repo?.branch) {
        throw new Error("GitHub repository branch is required");
    }
    return settings.github.repo.branch;
}

export function demandGitHubRepoCommit(settings: RuntimeSettings): string {
    if (!settings.github?.repo?.commit) {
        throw new Error("GitHub repository commit is required");
    }
    return settings.github.repo.commit;
}

export function demandGitHubIssueNumber(settings: RuntimeSettings): number {
    if (!settings.github?.issue?.number) {
        throw new Error("GitHub issue number is required");
    }
    return settings.github.issue.number;
}

export function demandProblemStatement(settings: RuntimeSettings): string {
    if (!settings.problem?.statement) {
        throw new Error("Problem statement is required");
    }
    return settings.problem.statement;
}

export function demandProblemContentFilterMode(settings: RuntimeSettings): ContentFilterMode {
    if (!settings.problem?.contentFilterMode) {
        throw new Error("Content filter mode is required");
    }
    return settings.problem.contentFilterMode;
}

export function demandInstanceId(settings: RuntimeSettings): string {
    if (!settings.service?.instance?.id) {
        throw new Error("Instance ID is required");
    }
    return settings.service.instance.id;
}

export function demandAction(settings: RuntimeSettings): AgentAction {
    if (!settings.problem?.action) {
        throw new Error("Action is required");
    }
    return settings.problem.action as AgentAction;
}

export function demandCallbackUrl(settings: RuntimeSettings): string {
    if (!settings.service?.callback?.url) {
        throw new Error("Callback URL is required");
    }
    return settings.service.callback.url;
}

export function demandGitHubPRNumber(settings: RuntimeSettings): number {
    if (!settings.github?.pr?.number) {
        throw new Error("GitHub PR number is required");
    }
    return settings.github.pr.number;
}

export function demandAnthropicApiKey(settings: RuntimeSettings): string {
    if (!settings.api?.anthropic?.key) {
        throw new Error("Anthropic API key is required");
    }
    return settings.api.anthropic.key;
}

export function demandOpenAIApiKey(settings: RuntimeSettings): string {
    if (!settings.api?.openai?.apiKey) {
        throw new Error("OpenAI API key is required");
    }
    return settings.api.openai.apiKey;
}

export function demandAzureOpenAiUrl(settings: RuntimeSettings): string {
    if (!settings.api?.openai?.azure?.url) {
        throw new Error("Azure OpenAI deployment URL is required");
    }
    return settings.api.openai.azure.url;
}

export function demandCopilotUrl(
    settings: RuntimeSettings,
    defaultValue: string | undefined = DEMAND_COPILOT_API_URL_DEFAULT,
): string {
    const value = settings.api?.copilot?.url || defaultValue;
    if (!value) {
        throw new Error("Copilot URL is required");
    }
    return value;
}

export function demandCopilotIntegrationId(settings: RuntimeSettings): string {
    if (!settings.api?.copilot?.integrationId) {
        throw new Error("Copilot integration ID is required");
    }
    return settings.api.copilot.integrationId;
}

export function demandCopilotHmacKey(settings: RuntimeSettings): string {
    if (!settings.api?.copilot?.hmacKey) {
        throw new Error("Copilot HMAC key is required");
    }
    return settings.api.copilot.hmacKey;
}

export function demandCopilotToken(settings: RuntimeSettings): string {
    if (!settings.api?.copilot?.token) {
        throw new Error("Copilot token is required");
    }
    return settings.api.copilot.token;
}

export function demandCopilotSessionId(settings: RuntimeSettings): string {
    if (!settings.api?.copilot?.sessionId) {
        throw new Error("Copilot session ID is required");
    }
    return settings.api.copilot.sessionId;
}

export function demandGitHubMcpServerToken(settings: RuntimeSettings): string {
    if (!settings.api?.github?.mcpServerToken) {
        throw new Error("GitHub MCP Personal Access Token is required");
    }
    return settings.api?.github?.mcpServerToken;
}
