/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

// don't import process here, it breaks tests 🙃
import type { AgentAction } from "../agent";
import { SettingsBuilder } from "./settings-builder";
import type { RuntimeSettings } from "./types";

// Subset of environment variables from /internal/launcher/actions.go and actions_workflow.yaml and /internal/launcher/docker.go
// that should be considered "secrets" and therefore not displayed in logs or passed into the agent's bash tool.
export const secretEnvVarNames = [
    "GITHUB_TOKEN",
    "GITHUB_COPILOT_API_TOKEN",
    "CAPI_HMAC_KEY",
    "CAPI_HMAC_KEY_OVERRIDE",
    "ANTHROPIC_API_KEY",
    "AIP_SWE_AGENT_TOKEN",
    "CAPI_AZURE_KEY_VAULT_URI",
    "COPILOT_JOB_NONCE",
    "GITHUB_MCP_SERVER_TOKEN",
    "OPENAI_BASE_URL",
    "OPENAI_API_KEY",
    "COPILOT_AGENT_REQUEST_HEADERS", // could contain sensitive information
    "AZURE_OPENAI_API_KEY",
    "AZURE_OPENAI_API_ENDPOINT",
    "AZURE_OPENAI_KEY_VAULT_URI",
    "AZURE_OPENAI_KEY_VAULT_SECRET_NAME",
    "BLACKBIRD_AUTH_METIS_API_KEY",
    "BLACKBIRD_AUTH_MODEL_BASED_RETRIEVAL_TOKEN",
];

// This is a more complete list that also filters out additional content from the bash tool in addition to secrets.
export const envVarNamesToFilterFromBash = [
    // Internal env vars
    "COPILOT_CALLBACK_URL",
    "COPILOT_AGENT_MODEL",
    "COPILOT_AGENT_JOB_ID",
    "COPILOT_AGENT_PROMPT",
    "COPILOT_AGENT_PUSH",
    "COPILOT_FIREWALL_ENABLED",
    "COPILOT_FIREWALL_ALLOW_LIST",
    "GITHUB_COPILOT_INTEGRATION_ID",
    "COPILOT_INTEGRATION_ID_OVERRIDE",
    "COPILOT_AGENT_SESSION_ID",
    "COPILOT_AGENT_EVENT_URL",
    "COPILOT_AGENT_EVENT_TYPE",
    // User identifying
    "COPILOT_AGENT_ACTOR",
    "COPILOT_AGENT_ACTOR_ID",
    // Critical: Secrets
    ...secretEnvVarNames,
];

export async function loadEnvironmentSettings(): Promise<RuntimeSettings> {
    const builder = new SettingsBuilder();

    // GitHub settings
    builder
        .setGithubServerUrl(process.env.GITHUB_SERVER_URL)
        .setGithubHost(process.env.GITHUB_HOST)
        .setGithubHostProtocol(process.env.GITHUB_HOST_PROTOCOL)
        .setGithubToken(process.env.GITHUB_TOKEN)
        .setGithubUserName(process.env.COPILOT_AGENT_COMMIT_LOGIN)
        .setGithubUserEmail(process.env.COPILOT_AGENT_COMMIT_EMAIL)
        .setGithubRepoOwnerId(process.env.GITHUB_REPOSITORY_OWNER_ID)
        .setGithubRepoOwnerName(process.env.GITHUB_REPOSITORY_OWNER)
        .setGithubRepoId(process.env.GITHUB_REPOSITORY_ID)
        .setGithubRepoName(process.env.GITHUB_REPOSITORY)
        .setGithubRepoBranch(process.env.COPILOT_AGENT_BRANCH_NAME)
        .setGithubRepoCommit(process.env.COPILOT_AGENT_BASE_COMMIT);

    // Only set readWrite if COPILOT_AGENT_PUSH is defined
    if (process.env.COPILOT_AGENT_PUSH !== undefined) {
        builder.setGithubRepoReadWrite(process.env.COPILOT_AGENT_PUSH === "true");
    }

    // Issue and PR settings - only set if valid numbers
    const issueNumber = process.env.COPILOT_AGENT_ISSUE_NUMBER
        ? parseInt(process.env.COPILOT_AGENT_ISSUE_NUMBER, 10)
        : undefined;
    if (!isNaN(issueNumber!)) {
        builder.setGithubIssueNumber(issueNumber);
    }

    const prNumber = process.env.COPILOT_AGENT_PR_NUMBER
        ? parseInt(process.env.COPILOT_AGENT_PR_NUMBER, 10)
        : undefined;
    if (!isNaN(prNumber!)) {
        builder.setGithubPRNumber(prNumber);
    }

    builder.setGithubPRCommitCount(
        process.env.COPILOT_AGENT_PR_COMMIT_COUNT ? parseInt(process.env.COPILOT_AGENT_PR_COMMIT_COUNT, 10) : undefined,
    );

    // Problem settings
    builder
        .setProblemStatement(process.env.COPILOT_AGENT_PROMPT)
        .setProblemContentFilterMode(process.env.COPILOT_AGENT_CONTENT_FILTER_MODE);

    // Only set action if it's a valid AgentAction
    const action = process.env.COPILOT_AGENT_ACTION as AgentAction | undefined;
    if (action && ["fix", "fix-pr-comment"].includes(action)) {
        builder.setProblemAction(action);
    }

    // Actor settings
    if (process.env.COPILOT_AGENT_ACTOR_ID) {
        builder.setGithubActorId(parseInt(process.env.COPILOT_AGENT_ACTOR_ID, 10));
    }
    if (process.env.COPILOT_AGENT_ACTOR) {
        builder.setGithubActorLogin(process.env.COPILOT_AGENT_ACTOR);
    }

    // Service settings
    builder
        .setInstanceId(process.env.COPILOT_AGENT_JOB_ID)
        .setCopilotJobNonce(process.env.COPILOT_JOB_NONCE)
        .setAgentModel(process.env.COPILOT_AGENT_MODEL)
        .setCallbackUrl(process.env.COPILOT_AGENT_CALLBACK_URL);

    // API settings
    builder
        .setAipSweAgentToken(process.env.AIP_SWE_AGENT_TOKEN)
        .setAnthropicApiKey(process.env.ANTHROPIC_API_KEY)
        .setCopilotUrl(process.env.COPILOT_API_URL)
        .setCopilotIntegrationId(process.env.GITHUB_COPILOT_INTEGRATION_ID)
        .setCopilotHmacKey(process.env.CAPI_HMAC_KEY)
        .setCopilotToken(process.env.GITHUB_COPILOT_API_TOKEN)
        .setCopilotAzureKeyVaultUri(process.env.CAPI_AZURE_KEY_VAULT_URI)
        .setBlackbirdMode(process.env.BLACKBIRD_MODE || "initial-search")
        .setCopilotSessionId(process.env.COPILOT_AGENT_SESSION_ID)
        .setSwebenchBaseCommit(process.env.SWEBENCH_BASE_COMMIT)
        .setGitHubMCPServerToken(process.env.GITHUB_MCP_SERVER_TOKEN)
        .setAzureOpenAiKeyVaultUri(process.env.AZURE_OPENAI_KEY_VAULT_URI)
        .setAzureOpenAiSecretName(process.env.AZURE_OPENAI_KEY_VAULT_SECRET_NAME)
        .setOpenAiBaseUrl(process.env.OPENAI_BASE_URL)
        .setOpenAiApiKey(process.env.AZURE_OPENAI_API_KEY || process.env.OPENAI_API_KEY)
        .setAzureOpenAiUrl(process.env.AZURE_OPENAI_API_ENDPOINT)
        .setAzureOpenAiApiVersion(process.env.AZURE_OPENAI_API_VERSION);
    if (process.env.COPILOT_USE_SESSIONS !== undefined) {
        builder.setCopilotUseSessions(process.env.COPILOT_USE_SESSIONS === "true");
    }

    // Trajectory output settings
    builder.setTrajectoryOutputFile(process.env.CPD_SAVE_TRAJECTORY_OUTPUT);

    if (process.env.COPILOT_AGENT_ONLINE_EVALUATION_DISABLED !== undefined) {
        builder.setDisableOnlineEvaluation(process.env.COPILOT_AGENT_ONLINE_EVALUATION_DISABLED === "true");
    }

    if (process.env.COPILOT_AGENT_ONLINE_EVALUATION_OUTPUT_FILE !== undefined) {
        builder.setEnableOnlineEvaluationOutputFile(process.env.COPILOT_AGENT_ONLINE_EVALUATION_OUTPUT_FILE === "true");
    }

    if (process.env.COPILOT_FEATURE_FLAGS) {
        process.env.COPILOT_FEATURE_FLAGS.split(",")
            .map((featureFlag) => featureFlag.trim())
            .filter((featureFlag) => featureFlag.length > 0)
            .forEach((featureFlag) => builder.setFeatureFlagEnabled(featureFlag));
    }

    if (process.env.COPILOT_AGENT_ERROR_CODES_TO_RETRY) {
        const errorCodesToRetry = process.env.COPILOT_AGENT_ERROR_CODES_TO_RETRY.split(",").map((code) =>
            parseInt(code.trim(), 10),
        );

        builder.setRetryPolicy({
            errorCodesToRetry: errorCodesToRetry,
        });
    }

    if (process.env.COPILOT_AGENT_REQUEST_HEADERS) {
        try {
            const requestHeaders = JSON.parse(process.env.COPILOT_AGENT_REQUEST_HEADERS) as Record<string, string>;
            builder.setRequestHeaders(requestHeaders);
        } catch (error) {
            console.error("Failed to parse COPILOT_AGENT_REQUEST_HEADERS as JSON", error);
        }
    }

    const parsedTimeoutMin = process.env.COPILOT_AGENT_TIMEOUT_MIN
        ? parseInt(process.env.COPILOT_AGENT_TIMEOUT_MIN, 10)
        : undefined;
    if (parsedTimeoutMin !== undefined && !Number.isNaN(parsedTimeoutMin)) {
        // COPILOT_AGENT_TIMEOUT_MIN is in minutes, convert to milliseconds
        builder.setTimeoutMs(parsedTimeoutMin * 60 * 1000);
    }

    const parsedStartTime = process.env.COPILOT_AGENT_START_TIME_SEC
        ? parseInt(process.env.COPILOT_AGENT_START_TIME_SEC, 10)
        : undefined;
    if (parsedStartTime !== undefined && !Number.isNaN(parsedStartTime)) {
        // COPILOT_AGENT_START_TIME_SEC is in seconds, convert to milliseconds
        builder.setStartTimeMs(parsedStartTime * 1000);
    }

    return builder.build();
}
