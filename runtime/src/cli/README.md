# GitHub Copilot CLI

CLI for the Copilot Coding Agent

## Installation

Authentication with [the GitHub Packages npm registry](https://docs.github.com/en/packages/working-with-a-github-packages-registry/working-with-the-npm-registry#authenticating-to-github-packages) is required currently.

First, create [a classic PAT](https://github.com/settings/tokens) with
`read:packages` scope and SSO enabled for the `github` organization.

Then run:

```sh
npm login --scope=@github --auth-type=legacy --registry=https://npm.pkg.github.com
```

When prompted enter your GitHub username as the username and the PAT as the
password.

```sh
npm install -g @github/copilot
```

## Usage

```sh
copilot
```
