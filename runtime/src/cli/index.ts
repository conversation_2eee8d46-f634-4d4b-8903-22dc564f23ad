#!/usr/bin/env node

/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

// Comprehensive Node.js environment setup for React/Ink
process.env.NODE_ENV = "production";
process.env.DEV = "false";

// Prevent React DevTools from loading
(global as Record<string, unknown>).__REACT_DEVTOOLS_GLOBAL_HOOK__ = {
    isDisabled: true,
    supportsFiber: false,
    inject: () => {},
    onCommitFiberRoot: () => {},
    onCommitFiberUnmount: () => {},
};

// Import WebSocket for Node.js
import WebSocket from "ws";

// Set up WebSocket
if (typeof (global as Record<string, unknown>).WebSocket === "undefined") {
    (global as Record<string, unknown>).WebSocket = WebSocket;
}

import { render } from "ink";
import React from "react";
import App from "./app.js";

// Parse command line arguments
const args = process.argv.slice(2);
let promptArg: string | null = null;

// Look for -p flag
const pIndex = args.indexOf("-p");
if (pIndex !== -1 && pIndex + 1 < args.length) {
    promptArg = args[pIndex + 1];
}

// If prompt mode, execute directly without Ink UI
if (promptArg) {
    // Import and execute the prompt directly
    void (async () => {
        try {
            const { executePromptDirectly } = await import("./app.js");
            await executePromptDirectly(promptArg);
        } catch (error: unknown) {
            console.error("Error executing prompt:", error);
            process.exit(1);
        }
    })();
} else {
    // Normal interactive mode - render the Ink UI
    render(React.createElement(App));
}
