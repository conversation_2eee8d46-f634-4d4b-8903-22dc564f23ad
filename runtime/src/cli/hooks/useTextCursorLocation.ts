/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { useReducer } from "react";
import { nonAlphanumericKeys } from "../../helpers/parseKeypress";
import { Key } from "./useInput";

type TextCursorLocationAction =
    | { type: "set_text"; payload: string }
    | { type: "set_cursor_position"; payload: number }
    | { type: "insert_input"; payload: string }
    | { type: "backspace" }
    | { type: "forward_delete" }
    | { type: "backspace_word" }
    | { type: "forward_delete_word" }
    | { type: "move_word_left" }
    | { type: "move_word_right" }
    | { type: "clear" }
    | { type: "move_right" }
    | { type: "move_left" };

/**
 * Helper function to find the end of the current word from a given position
 */
function findWordEnd(text: string, position: number): number {
    if (position >= text.length) return text.length;

    let pos = position;

    // Skip whitespace to the right
    while (pos < text.length && text[pos] === " ") {
        pos++;
    }

    // Find the end of the word
    while (pos < text.length && text[pos] !== " ") {
        pos++;
    }

    return pos;
}

/**
 * Helper function to find the start of the previous word
 */
function findPreviousWordStart(text: string, position: number): number {
    if (position === 0) return 0;

    let pos = position - 1;

    // Skip whitespace to the left
    while (pos >= 0 && text[pos] === " ") {
        pos--;
    }

    if (pos === 0) return 0;

    // Find the start of the previous word
    while (pos >= 0 && text[pos] !== " ") {
        pos--;
    }

    return pos + 1;
}

/**
 * Helper function to find the start of the next word
 */
function findNextWordStart(text: string, position: number): number {
    if (position >= text.length) return text.length;

    let pos = position;

    // Skip current word
    while (pos < text.length && text[pos] !== " ") {
        pos++;
    }

    // Skip whitespace
    while (pos < text.length && text[pos] === " ") {
        pos++;
    }

    return pos;
}

function TextCursorLocationReducer(
    state: { text: string; cursorPosition: number },
    action: TextCursorLocationAction,
): { text: string; cursorPosition: number } {
    switch (action.type) {
        case "set_text":
            return {
                text: action.payload,
                cursorPosition: action.payload.length,
            };
        case "set_cursor_position":
            return {
                ...state,
                cursorPosition: action.payload,
            };
        case "insert_input": {
            const { text, cursorPosition } = state;
            const beforeCursor = text.slice(0, cursorPosition);
            const afterCursor = text.slice(cursorPosition);
            const newText = beforeCursor + action.payload + afterCursor;
            const newCursorPosition = cursorPosition + action.payload.length;

            return {
                text: newText,
                cursorPosition: newCursorPosition,
            };
        }
        case "backspace": {
            if (state.cursorPosition === 0) {
                return state; // No change if at the start
            }
            const newText = state.text.slice(0, state.cursorPosition - 1) + state.text.slice(state.cursorPosition);

            return {
                text: newText,
                cursorPosition: state.cursorPosition - 1,
            };
        }
        case "forward_delete": {
            if (state.cursorPosition >= state.text.length) {
                return state; // No change if at the end
            }
            const newText = state.text.slice(0, state.cursorPosition) + state.text.slice(state.cursorPosition + 1);
            return {
                ...state,
                text: newText,
            };
        }
        case "backspace_word": {
            const { text, cursorPosition } = state;
            if (cursorPosition === 0) {
                return state; // No change if at the start
            }

            const wordStart = findPreviousWordStart(text, cursorPosition);
            const newText = text.slice(0, wordStart) + text.slice(cursorPosition);

            return {
                text: newText,
                cursorPosition: wordStart,
            };
        }
        case "forward_delete_word": {
            const { text, cursorPosition } = state;
            if (cursorPosition >= text.length) {
                return state; // No change if at the end
            }

            const wordEnd = findWordEnd(text, cursorPosition);
            const newText = text.slice(0, cursorPosition) + text.slice(wordEnd);

            return {
                ...state,
                text: newText,
            };
        }
        case "move_word_left": {
            const { text, cursorPosition } = state;
            if (cursorPosition === 0) {
                return state; // No change if at the start
            }

            const newPosition = findPreviousWordStart(text, cursorPosition);

            return {
                ...state,
                cursorPosition: newPosition,
            };
        }
        case "move_word_right": {
            const { text, cursorPosition } = state;
            if (cursorPosition >= text.length) {
                return state; // No change if at the end
            }

            const newPosition = findNextWordStart(text, cursorPosition);

            return {
                ...state,
                cursorPosition: newPosition,
            };
        }
        case "move_right": {
            if (state.cursorPosition === state.text.length) {
                return state; // No change if at the end
            }

            return {
                ...state,
                cursorPosition: state.cursorPosition + 1,
            };
        }
        case "move_left": {
            if (state.cursorPosition === 0) {
                return state;
            }

            return {
                ...state,
                cursorPosition: state.cursorPosition - 1,
            };
        }
        case "clear":
            return { text: "", cursorPosition: 0 };
        default:
            return state;
    }
}

/**
 * Represents the location of the text cursor in a text input.
 * Provides methods to manipulate the text and cursor position.
 */
export interface TextCursorLocation {
    /** The text content the user has entered */
    text: string;

    /* The index of the cursor in the text */
    cursorPosition: number;

    /* Set the cursor position */
    setCursorPosition: (newPosition: number) => void;

    /* Set the text content and cursor position to end of string*/
    setText: (newText: string) => void;

    /* Add input at the current cursor position */
    insertInput: (input: string) => void;

    /* Remove the character before the cursor */
    backspace: () => void;

    /* Remove the character after the cursor */
    forwardDelete: () => void;

    /* Remove the word before the cursor */
    backspaceWord: () => void;

    /* Remove the word after the cursor */
    forwardDeleteWord: () => void;

    /* Move the cursor one character to the left */
    moveLeft: () => void;

    /* Move the cursor one character to the right */
    moveRight: () => void;

    /* Move the cursor to the start of the previous word */
    moveWordLeft: () => void;

    /* Move the cursor to the start of the next word */
    moveWordRight: () => void;

    /* Clear the text and reset cursor position */
    clear: () => void;

    /** Handle common key press events such as input, directional, and deletion keys */
    handleCommonKeyPress: (input: string, key: Key) => void;
}

/**
 * Returns an object representing the location of the text cursor in a text input.
 * Provides methods to manipulate the text and cursor position.
 */
export const useTextCursorLocation = (): TextCursorLocation => {
    const [textCursorLocation, dispatch] = useReducer(TextCursorLocationReducer, { text: "", cursorPosition: 0 });

    const { text, cursorPosition } = textCursorLocation;

    const setCursorPosition = (newPosition: number) => {
        dispatch({ type: "set_cursor_position", payload: newPosition });
    };

    const setText = (newText: string) => {
        dispatch({ type: "set_text", payload: newText });
    };

    const insertInput = (input: string) => {
        dispatch({ type: "insert_input", payload: input });
    };

    const backspace = () => {
        if (cursorPosition === 0) {
            return;
        }
        dispatch({ type: "backspace" });
    };

    const forwardDelete = () => {
        if (cursorPosition >= text.length) {
            return;
        }
        dispatch({ type: "forward_delete" });
    };

    const backspaceWord = () => {
        if (cursorPosition === 0) {
            return;
        }
        dispatch({ type: "backspace_word" });
    };

    const forwardDeleteWord = () => {
        if (cursorPosition >= text.length) {
            return;
        }
        dispatch({ type: "forward_delete_word" });
    };

    const moveLeft = () => {
        if (cursorPosition === 0) {
            return;
        }

        dispatch({ type: "move_left" });
    };

    const moveRight = () => {
        if (cursorPosition >= text.length) {
            return;
        }

        dispatch({ type: "move_right" });
    };

    const moveWordLeft = () => {
        if (cursorPosition === 0) {
            return;
        }

        dispatch({ type: "move_word_left" });
    };

    const moveWordRight = () => {
        if (cursorPosition >= text.length) {
            return;
        }

        dispatch({ type: "move_word_right" });
    };

    const clear = () => {
        dispatch({ type: "clear" });
    };

    const handleCommonKeyPress = (input: string, key: Key) => {
        // Handle shortcuts first
        if (key.ctrl || key.meta) {
            // Word deletion shortcuts
            if (key.backspace || (key.ctrl && input === "w")) {
                backspaceWord();
                return;
            }

            if (key.delete) {
                forwardDeleteWord();
                return;
            }

            // Word navigation shortcuts
            if (key.leftArrow || (key.meta && input === "b")) {
                moveWordLeft();
                return;
            }

            if (key.rightArrow || (key.meta && input === "f")) {
                moveWordRight();
                return;
            }

            // Home/End shortcuts
            if (key.ctrl && input === "a") {
                setCursorPosition(0);
                return;
            }

            if (key.ctrl && input === "e") {
                setCursorPosition(text.length);
                return;
            }

            // Single character deletion shortcuts
            if (key.ctrl && input === "h") {
                backspace();
                return;
            }

            if (key.ctrl && input === "d") {
                forwardDelete();
                return;
            }
        }

        // Handle standard keys
        if (key.home) {
            setCursorPosition(0);
            return;
        }

        if (key.end) {
            setCursorPosition(text.length);
            return;
        }

        if (key.leftArrow) {
            moveLeft();
            return;
        }

        if (key.rightArrow) {
            moveRight();
            return;
        }

        if (key.backspace) {
            backspace();
            return;
        }

        if (key.delete) {
            forwardDelete();
            return;
        }
        // If the input is a non-alphanumeric key or is part of a shortcut (ctrl/meta key pressed), do not insert it
        if (!nonAlphanumericKeys.includes(input) && !key.ctrl && !key.meta) {
            insertInput(input);
            return;
        }
    };

    return {
        text,
        cursorPosition,
        setCursorPosition,
        setText,
        insertInput,
        backspace,
        forwardDelete,
        backspaceWord,
        forwardDeleteWord,
        moveLeft,
        moveRight,
        moveWordLeft,
        moveWordRight,
        clear,
        handleCommonKeyPress,
    };
};
