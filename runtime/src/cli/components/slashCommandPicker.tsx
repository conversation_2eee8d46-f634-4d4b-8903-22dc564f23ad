/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { Box, Text } from "ink";
import React, { useRef } from "react";
import { Colors } from "../themes";
import { SlashCommandDescription } from "../commands/slashCommands";

interface SlashCommandPickerProps {
    options: SlashCommandDescription[];
    selectedIndex: number;
    maxRows: number;
}

const commandColumnWidth = 45;

const SlashCommandPicker: React.FC<SlashCommandPickerProps> = (props) => {
    // Track scroll offset as a ref, rather than with useState+useEffect, so that we can update it
    // synchronously during render. Otherwise scrolling would lag as useEffect runs after rendering.
    const scrollOffsetRef = useRef(0);
    if (props.selectedIndex < scrollOffsetRef.current) {
        scrollOffsetRef.current = props.selectedIndex;
    } else if (props.selectedIndex >= scrollOffsetRef.current + props.maxRows) {
        scrollOffsetRef.current = props.selectedIndex - props.maxRows + 1;
    }

    const optionsInView = props.options.slice(scrollOffsetRef.current, scrollOffsetRef.current + props.maxRows);
    return (
        <Box paddingY={1} flexDirection="column">
            {optionsInView.map((command, index) => {
                const color =
                    optionsInView[index] === props.options[props.selectedIndex] ? Colors.SELECTED : Colors.MUTED;
                const commandText = `${command.name}${command.args ? ` ${command.args}` : ""}`;
                return (
                    <Box flexDirection="row" key={command.name}>
                        <Box width={commandColumnWidth}>
                            <Text color={color} wrap="truncate">
                                {commandText}
                            </Text>
                        </Box>
                        <Text color={color} wrap="truncate">
                            {command.help}
                        </Text>
                    </Box>
                );
            })}
        </Box>
    );
};

export default SlashCommandPicker;
