/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { Box, Text } from "ink";
import React from "react";
import { Colors } from "../themes";

interface AppHeaderProps {
    currentWorkingDirectory: string;
    gitResult: { found: boolean; gitRoot?: string };
}

export const AppHeader: React.FC<AppHeaderProps> = ({ currentWorkingDirectory, gitResult }) => {
    return (
        <Box flexDirection="column" marginBottom={1}>
            <Text color={Colors.COPILOT} bold>
                Welcome to Copilot
            </Text>
            <Text color={Colors.MUTED}>cwd: {currentWorkingDirectory}</Text>
            {gitResult.found && <Text color={Colors.MUTED}>git repo: {gitResult.gitRoot}</Text>}
        </Box>
    );
};

AppHeader.displayName = "AppHeader";
