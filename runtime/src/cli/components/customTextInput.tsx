/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { Text } from "ink";
import React, { useCallback } from "react";
import { TextCursorLocation } from "../hooks/useTextCursorLocation";
import useInput, { Key } from "../hooks/useInput";

interface CustomTextInputProps {
    textCursorLocation: TextCursorLocation;
    onSubmit: (value: string) => void;
    placeholder?: string;
    focus?: boolean;
}

const CustomTextInput: React.FC<CustomTextInputProps> = React.memo(
    ({ textCursorLocation, onSubmit, focus = true, placeholder }) => {
        const inputHandler = useCallback(
            (input: string, key: Key) => {
                if (!focus) return;

                if (key.return) {
                    onSubmit(textCursorLocation.text + (placeholder || ""));
                    return;
                }

                if (key.tab) {
                    if (placeholder && placeholder.length > 0) {
                        const newValue = textCursorLocation.text + placeholder;
                        textCursorLocation.setText(newValue);
                    }
                    return;
                }

                textCursorLocation.handleCommonKeyPress(input, key);
            },
            [focus, textCursorLocation, placeholder, onSubmit],
        );

        useInput(inputHandler, { isActive: focus });

        const { text, cursorPosition } = textCursorLocation;
        // Always render a consistent layout, even when empty
        if (!text) {
            // When empty, always show a space with cursor
            return (
                <Text>
                    <Text inverse={focus}> </Text>
                </Text>
            );
        }

        // For rendering: cursor position represents insertion point (between characters)
        // Show cursor on the character at the cursor position (current character)
        const beforeCursor = text.slice(0, cursorPosition);
        let atCursor = text.slice(cursorPosition, cursorPosition + 1) || " ";
        const afterCursor = text.slice(cursorPosition + 1);

        // If there's a placeholder, the cursor can overlap it (i.e., placeholder's first
        // character is on the cursor, not after it)
        let placeholderAfterCursor = placeholder;
        if (cursorPosition === text.length && placeholder?.length) {
            atCursor = placeholder[0];
            placeholderAfterCursor = placeholder.slice(1);
        }

        return (
            <Text>
                {beforeCursor}
                <Text inverse={focus}>{atCursor}</Text>
                {afterCursor}
                <Text dimColor>{placeholderAfterCursor}</Text>
            </Text>
        );
    },
);

export default CustomTextInput;
