/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import React, { useEffect, useState } from "react";
import { Box, Text, useInput } from "ink";
import { Spinner } from "@inkjs/ui";
import open from "open";
import clipboard from "copy-paste/promises";
import { join } from "path";
import { homedir } from "os";
import { mkdir, writeFile } from "fs/promises";
import { Colors } from "../themes";
interface LoginUIProps {
    onLoginSuccess: () => void;
    onLoginClose: () => void;
}

const githubUrl = "https://github.com"; // Note, will need to be configurable to support Proxima
const deviceCodePath = "/login/device/code";
const accessTokenPath = "/login/oauth/access_token";
const clientId = "Iv23ctiIS8xpNhgo6m86"; // https://github.com/organizations/github/settings/apps/github-copilot-cli

interface DeviceCodeResponse {
    device_code: string;
    user_code: string;
    verification_uri: string;
    expires_in: number;
    interval: number;
}

async function requestDeviceCode(): Promise<DeviceCodeResponse> {
    const url = new URL(deviceCodePath, githubUrl);
    const response = await fetch(url.href, {
        method: "POST",
        headers: {
            "Content-Type": "application/x-www-form-urlencoded",
            Accept: "application/json",
        },
        body: new URLSearchParams({
            client_id: clientId,
            scope: "user:email",
        }),
    });
    if (!response.ok) {
        throw new Error(`Failed to request device code: ${response.statusText}`);
    }
    return (await response.json()) as DeviceCodeResponse;
}

/** Repeatedly request access token until successful or expired */
async function requestAccessToken(
    deviceCodeResponse: DeviceCodeResponse,
    setErrorMessage: (message: string) => void,
): Promise<string> {
    const start = performance.now();
    const { device_code, expires_in, interval, verification_uri, user_code } = deviceCodeResponse;
    // Attempt to copy the user code to the clipboard
    try {
        await clipboard.copy(user_code);
    } catch {
        setErrorMessage(`Failed to copy to clipboard. Please copy ${user_code} manually.`);
    }
    // Attempt to open the verification URI in the default browser
    try {
        await open(verification_uri);
    } catch {
        setErrorMessage(
            `Failed to open browser. Please visit ${verification_uri} and enter the code ${user_code} manually.`,
        );
    }
    while (performance.now() - start < expires_in * 1000) {
        await new Promise((r) => setTimeout(r, interval * 1000));
        const url = new URL(accessTokenPath, githubUrl);
        const response = await fetch(url.href, {
            method: "POST",
            headers: {
                "Content-Type": "application/x-www-form-urlencoded",
                Accept: "application/json",
            },
            body: new URLSearchParams({
                client_id: clientId,
                device_code,
                grant_type: "urn:ietf:params:oauth:grant-type:device_code",
            }),
        });

        if (!response.ok) {
            throw new Error(`Failed to authorize: ${response.statusText}`);
        }
        const data = (await response.json()) as Record<string, unknown>;
        if (typeof data.access_token === "string") {
            return data.access_token;
        }
        if (data.error !== "authorization_pending" && data.error !== "slow_down") {
            throw new Error((data.error_description as string) || `Unexpected error: ${data.error}`);
        }
    }
    throw new Error("Timed out waiting for user authorization");
}

export async function writeTokenToConfig(token?: string) {
    const copilotDir = join(homedir(), ".copilot");
    const configPath = join(copilotDir, "config");

    // Ensure ~/.copilot directory exists
    try {
        await mkdir(copilotDir, { recursive: true });
    } catch {
        // Ignore error if directory already exists
    }

    try {
        if (token) {
            await writeFile(configPath, `COPILOT_TOKEN=${token}\n`, "utf8");
        } else {
            // If no token is provided, clear the config file
            await writeFile(configPath, "", "utf8");
        }
    } catch (error) {
        throw new Error(
            `Error saving config to ${configPath}. Please check file permissions or ensure the directory exists: ${error}`,
        );
    }
}

enum LoginStatus {
    Idle,
    Authorizing,
    Error,
}

export function LoginUI({ onLoginSuccess, onLoginClose }: LoginUIProps) {
    const [deviceCodeResponse, setDeviceCodeResponse] = useState<DeviceCodeResponse | null>(null);
    const [status, setStatus] = useState<LoginStatus>(LoginStatus.Idle);
    const [errorMessage, setErrorMessage] = useState<string | null>(null);

    async function initiateLogin() {
        const response = await requestDeviceCode();
        setDeviceCodeResponse(response);
    }

    useEffect(() => {
        initiateLogin().catch((error) => {
            setErrorMessage(error.message);
            setStatus(LoginStatus.Error);
        });
    }, []);

    useInput((_, key) => {
        if (key.escape) {
            onLoginClose();
            return;
        }
        async function requestToken() {
            if (!deviceCodeResponse) return;
            const token = await requestAccessToken(deviceCodeResponse, setErrorMessage);
            await writeTokenToConfig(token);
            onLoginSuccess();
        }
        if (status === LoginStatus.Idle) {
            setStatus(LoginStatus.Authorizing);
            requestToken().catch((error) => {
                setErrorMessage(error.message);
                setStatus(LoginStatus.Error);
            });
        }
        if (status === LoginStatus.Error) {
            setErrorMessage(null);
            setDeviceCodeResponse(null);
            setStatus(LoginStatus.Idle);
            initiateLogin().catch((error) => {
                setErrorMessage(error.message);
                setStatus(LoginStatus.Error);
            });
        }
    });

    return (
        <Box flexDirection="column" padding={1}>
            {status === LoginStatus.Idle && deviceCodeResponse && (
                <Box flexDirection="column" rowGap={1}>
                    <Text>
                        Press any key to copy{" "}
                        <Text bold color="blue">
                            {deviceCodeResponse.user_code}
                        </Text>{" "}
                        and open {deviceCodeResponse.verification_uri}.
                    </Text>
                </Box>
            )}
            {status == LoginStatus.Authorizing && (
                <>
                    <Spinner label="Waiting for authorization..." />
                    {errorMessage && (
                        <Box marginTop={1}>
                            <Text color="red">{errorMessage}</Text>
                        </Box>
                    )}
                </>
            )}
            {status == LoginStatus.Error && (
                <Box flexDirection="column">
                    <Text color="red">Error during sign-in: {errorMessage}</Text>
                    <Text>Press any key to retry</Text>
                </Box>
            )}
            <Box marginTop={1}>
                <Text color={Colors.MUTED}>Press ESC to cancel</Text>
            </Box>
        </Box>
    );
}
