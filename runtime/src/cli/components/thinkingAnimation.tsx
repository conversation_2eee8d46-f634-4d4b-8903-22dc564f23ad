/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { Box, Text } from "ink";
import React, { useState, useEffect } from "react";
import { Colors } from "../themes";
interface ThinkingAnimationProps {
    isCancelling: boolean;
}

// Thinking animation component
const ThinkingAnimation: React.FC<ThinkingAnimationProps> = (props) => {
    const [dots, setDots] = useState("");

    useEffect(() => {
        const interval = setInterval(() => {
            setDots((prev) => {
                if (prev === "...") return "";
                return prev + ".";
            });
        }, 500);

        return () => clearInterval(interval);
    }, []);

    const text = props.isCancelling ? "Cancelling" : "Thinking";

    return (
        <Box paddingLeft={1}>
            <Text color={Colors.COPILOT}>
                ◐ {text}
                {dots}
            </Text>
        </Box>
    );
};

export default ThinkingAnimation;
