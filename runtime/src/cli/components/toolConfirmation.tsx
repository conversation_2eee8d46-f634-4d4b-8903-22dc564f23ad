/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import React from "react";
import { Box, Text } from "ink";
import SelectInput from "ink-select-input";
import { matchToolIntention, ToolConfirmationResult, ToolIntention } from "../../../src/tools";
import { Colors } from "../themes";
/**
 * Diff component for displaying git diff output with syntax highlighting.
 *
 * @param diff The git diff string to display
 * @returns A React component that renders the diff with proper colors
 */
const Diff: React.FC<{ diff: string }> = ({ diff }) => {
    const lines = diff.split("\n");
    let currentOldLineNumber = 0;
    let currentNewLineNumber = 0;
    let inHunk = false;

    return (
        <Box flexDirection="column" borderStyle="single" borderColor={Colors.MUTED} paddingX={1} paddingY={0}>
            {lines.map((line, index) => {
                let color = Colors.INFO;
                let lineNumber = "";

                if (line.startsWith("@@")) {
                    // Parse hunk header: @@ -oldStart,oldCount +newStart,newCount @@
                    const hunkMatch = line.match(/@@ -(\d+)(?:,\d+)? \+(\d+)(?:,\d+)? @@/);
                    if (hunkMatch) {
                        currentOldLineNumber = parseInt(hunkMatch[1]);
                        currentNewLineNumber = parseInt(hunkMatch[2]);
                        inHunk = true;
                    }
                    color = Colors.ACCENT;
                    // Don't show line numbers for hunk headers
                } else if (
                    line.startsWith("diff --git") ||
                    line.startsWith("index ") ||
                    line.startsWith("---") ||
                    line.startsWith("+++")
                ) {
                    color = Colors.WARNING;
                    inHunk = false;
                    // Don't show line numbers for file headers
                } else if (inHunk) {
                    if (line.startsWith("+")) {
                        color = Colors.SUCCESS;
                        lineNumber = currentNewLineNumber.toString().padStart(3, " ");
                        currentNewLineNumber++;
                    } else if (line.startsWith("-")) {
                        color = Colors.ERROR;
                        lineNumber = currentOldLineNumber.toString().padStart(3, " ");
                        currentOldLineNumber++;
                    } else if (line.startsWith(" ")) {
                        color = Colors.MUTED;
                        lineNumber = currentNewLineNumber.toString().padStart(3, " ");
                        currentOldLineNumber++;
                        currentNewLineNumber++;
                    }
                } else {
                    // Lines outside hunks (like empty lines)
                    if (line.startsWith(" ")) {
                        color = Colors.MUTED;
                    }
                }

                return (
                    <Box key={index} flexDirection="row">
                        <Text color={Colors.MUTED}>{lineNumber || "   "}</Text>
                        <Text color={Colors.MUTED}>{" | "}</Text>
                        <Text color={color}>{line}</Text>
                    </Box>
                );
            })}
        </Box>
    );
};

/**
 *  ToolConfirmation component for CLI tool calls.
 *
 * @param intention The intention of the tool call, which can be a string or an object containing tool name and arguments.
 * @param onConfirm A callback function that will be called with the user's confirmation choice.
 * @returns A React component that renders the tool confirmation UI.
 */
export const ToolConfirmation: React.FC<{
    intention: ToolIntention;
    onConfirm: (result: ToolConfirmationResult) => void;
}> = ({ intention, onConfirm }) => {
    return (
        <Box flexDirection="column" borderStyle="single" borderColor={Colors.WARNING} paddingX={1} paddingY={0} gap={1}>
            {matchToolIntention(intention, {
                onExec: (title, command) => {
                    const items: Array<{ label: string; value: ToolConfirmationResult }> = [
                        { label: "Yes", value: "proceed-once" },
                        { label: "No, and tell Copilot what to do differently", value: "rejected" },
                    ];

                    return (
                        <Box flexDirection="column" gap={1}>
                            <Box>
                                <Text color={Colors.ACCENT}>{title}: </Text>
                                <Text>{command}</Text>
                            </Box>
                            <Text>Do you want to run this command?</Text>
                            <SelectInput
                                items={items}
                                onSelect={(item) => {
                                    onConfirm(item.value);
                                }}
                            />
                            <Text color={Colors.MUTED} dimColor>
                                Press Enter to confirm
                            </Text>
                        </Box>
                    );
                },
                onEdit: (title, fileName, diff) => {
                    const items: Array<{ label: string; value: ToolConfirmationResult }> = [
                        { label: "Yes", value: "proceed-once" },
                        { label: "No, and tell Copilot what to do differently", value: "rejected" },
                    ];

                    return (
                        <Box flexDirection="column" gap={1}>
                            <Box>
                                <Text color={Colors.ACCENT}>{title}: </Text>
                                <Text>{fileName}</Text>
                            </Box>
                            <Diff diff={diff} />
                            <Text>Do you want to edit this file?</Text>
                            <SelectInput
                                items={items}
                                onSelect={(item) => {
                                    onConfirm(item.value);
                                }}
                            />
                            <Text color={Colors.MUTED}>Press Enter to confirm</Text>
                        </Box>
                    );
                },
                onMcp: (toolName, args) => {
                    const items: Array<{ label: string; value: ToolConfirmationResult }> = [
                        { label: "Yes", value: "proceed-once" },
                        { label: "No, and tell Copilot what to do differently", value: "rejected" },
                    ];

                    return (
                        <Box flexDirection="column" gap={1}>
                            <Box>
                                <Text color={Colors.ACCENT}>{toolName}: </Text>
                            </Box>
                            <Box borderStyle="single" borderColor={Colors.MUTED} paddingX={1}>
                                <Text color={Colors.INFO}>{JSON.stringify(args, null, 2)}</Text>
                            </Box>
                            <Text>Do you want to use this tool?</Text>
                            <SelectInput
                                items={items}
                                onSelect={(item) => {
                                    onConfirm(item.value);
                                }}
                            />
                            <Text color={Colors.MUTED}>Press Enter to confirm</Text>
                        </Box>
                    );
                },
            })}
        </Box>
    );
};
