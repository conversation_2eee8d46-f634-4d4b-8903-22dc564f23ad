/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import React, { useEffect, useState } from "react";
import { Box, Text, useInput } from "ink";
import TextInput from "ink-text-input";
import { targetFeedbackRepo } from "../commands/submitFeedback";
import { Colors } from "../themes";
interface FeedbackFormProps {
    isSubmitting: boolean;
    onSubmit: (title: string, description: string) => void;
    onCancel: () => void; // Optional cancel handler
}

// First ask for a title, then ask for a description
// Finally, submit the feedback
export function FeedbackForm({ onSubmit, isSubmitting, onCancel }: FeedbackFormProps) {
    const [title, setTitle] = useState("");
    const [errorMessage, setErrorMessage] = useState("");
    const [description, setDescription] = useState("");
    const [state, setState] = useState<"privacy_disclaimer" | "title" | "description">("privacy_disclaimer");

    const [dots, setDots] = useState("");

    useEffect(() => {
        const interval = setInterval(() => {
            setDots((prev) => {
                if (prev === "...") return "";
                return prev + ".";
            });
        }, 500);

        return () => clearInterval(interval);
    }, []);

    // Handle form submission logic here
    const handleEnterKey = () => {
        // Clear any previous error messages
        setErrorMessage("");

        if (state === "privacy_disclaimer") {
            // If we are in the privacy disclaimer state, switch to title input
            setState("title");
            return;
        }

        if (state === "title") {
            if (!title) {
                setErrorMessage("Title is required.");
                return;
            }

            // If we are entering the title, switch to description input
            setState("description");
            return;
        }

        // Submit the feedback to the server or process it as needed
        if (!description) {
            setErrorMessage("Description is required.");
            return;
        }

        onSubmit(title, description);
    };

    // Handle Enter key to submit the form.
    // Handle Escape key to go a step back (from description to title,
    // from title to cancel form)
    useInput((_, key) => {
        if (isSubmitting) {
            return;
        }
        if (key.return) {
            handleEnterKey();
        }
        if (key.escape) {
            if (state === "title" || state === "privacy_disclaimer") {
                // If we are entering the title, cancel the form
                onCancel();
            } else {
                // If we are entering the description, go back to title input
                setState("title");
            }
        }
    });

    const privacyDisclaimer = (
        <>
            <Text color={Colors.ERROR} bold>
                By submitting feedback, you agree that your conversation history will be included in the issue. This can
                include conversation history from previous sessions, so BE WARNED. Learn more:
                https://github.com/github/sweagentd/issues/4157
            </Text>
            <Text color={Colors.ERROR} bold>
                Please do not include any sensitive or personal information.
            </Text>
            <Text color={Colors.ERROR} bold>
                Do you want to continue?
            </Text>
        </>
    );

    const feedbackPrompt = (
        <Text>
            We value your feedback! Please share your thoughts. This will create an issue in the{" "}
            <Text color={Colors.SUCCESS}>{targetFeedbackRepo}</Text> repository.{" "}
        </Text>
    );

    const titleInput = (
        <Box>
            <Text bold>Title: </Text>
            {state === "title" ? (
                <TextInput value={title} onChange={setTitle} placeholder="Enter feedback title" />
            ) : (
                <Text color={Colors.MUTED}>{title}</Text>
            )}
        </Box>
    );

    const descriptionInput = (
        <Box>
            <Text bold>Description: </Text>
            {!isSubmitting ? (
                <TextInput value={description} onChange={setDescription} placeholder="Enter feedback description" />
            ) : (
                <Text color={Colors.MUTED}>{description}</Text>
            )}
        </Box>
    );

    // Render the feedback form
    return (
        <Box flexDirection="column" padding={1} gap={1}>
            <Text color={Colors.WARNING} bold>
                Submit Feedback
            </Text>

            {state === "privacy_disclaimer" ? (
                privacyDisclaimer
            ) : (
                <>
                    {feedbackPrompt}
                    {titleInput}
                    {state === "description" && descriptionInput}
                    {errorMessage && <Text color={Colors.ERROR}>{errorMessage}</Text>}
                </>
            )}
            <Box>
                {isSubmitting ? (
                    <Text color={Colors.WARNING}>◐ Submitting feedback{dots}</Text>
                ) : (
                    <Text color={Colors.SUCCESS}>
                        Press Enter to {state === "description" ? "submit" : "continue"} or Esc to go back
                    </Text>
                )}
            </Box>
        </Box>
    );
}
