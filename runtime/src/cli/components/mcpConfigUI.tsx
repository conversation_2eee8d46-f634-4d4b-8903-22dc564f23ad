/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import React, { useState, useEffect } from "react";
import { Box, Text, useInput } from "ink";
import TextInput from "ink-text-input";
import { readFileSync, existsSync, mkdirSync, writeFileSync } from "fs";
import { join } from "path";
import { homedir } from "os";
import { Colors } from "../themes";
interface MCPServerConfig {
    command?: string;
    args?: string[];
    env?: { [key: string]: string };
    tools?: string[];
    type?: string;
    url?: string;
    headers?: { [key: string]: string };
    isDefaultServer?: boolean;
}

interface MCPConfig {
    mcpServers: { [serverName: string]: MCPServerConfig };
}

interface MCPConfigUIProps {
    onClose: () => void;
    onConfigSaved: () => void;
}

interface ServerFormData {
    name: string;
    type: "Local" | "Remote";
    command: string;
    args: string;
    env: string;
    url: string;
    headers: string;
    tools: string;
}

const defaultFormData: ServerFormData = {
    name: "",
    type: "Local",
    command: "",
    args: "",
    env: "",
    url: "",
    headers: "",
    tools: "*",
};

export const MCPConfigUI: React.FC<MCPConfigUIProps> = ({ onClose, onConfigSaved }) => {
    const [config, setConfig] = useState<MCPConfig>({ mcpServers: {} });
    const [selectedServer, setSelectedServer] = useState<string | null>(null);
    const [formData, setFormData] = useState<ServerFormData>(defaultFormData);
    const [currentField, setCurrentField] = useState<keyof ServerFormData>("name");
    const [mode, setMode] = useState<"list" | "edit" | "add">("list");
    const [errors, setErrors] = useState<string[]>([]);

    // Load MCP config on mount
    useEffect(() => {
        loadConfig();
    }, []);

    const getMcpConfigPath = (): string => {
        const copilotDir = join(homedir(), ".copilot");
        return join(copilotDir, "mcpconfig");
    };

    const loadConfig = () => {
        const mcpConfigPath = getMcpConfigPath();
        try {
            if (existsSync(mcpConfigPath)) {
                const configContent = readFileSync(mcpConfigPath, "utf8");
                setConfig(JSON.parse(configContent));
            }
        } catch (error) {
            console.error("Error reading MCP config:", error);
        }
    };

    const saveConfig = (newConfig: MCPConfig) => {
        const mcpConfigPath = getMcpConfigPath();
        const copilotDir = join(homedir(), ".copilot");

        try {
            mkdirSync(copilotDir, { recursive: true });
            writeFileSync(mcpConfigPath, JSON.stringify(newConfig, null, 2), "utf8");
            setConfig(newConfig);
            onConfigSaved();
        } catch (error) {
            setErrors([`Failed to save config: ${error}`]);
        }
    };

    const validateFormData = (data: ServerFormData): string[] => {
        const errors: string[] = [];

        if (!data.name.trim()) {
            errors.push("Server name is required");
        }

        if (!data.tools.trim()) {
            errors.push('Tools field is required (use "*" for all tools)');
        }

        if (data.type === "Remote") {
            if (!data.url.trim()) {
                errors.push("URL is required for remote servers");
            }
        } else {
            if (!data.command.trim()) {
                errors.push("Command is required for local servers");
            }
        }

        return errors;
    };

    const parseArrayField = (value: string): string[] => {
        if (!value.trim()) return [];
        if (value.trim() === "*") return ["*"];
        return value
            .split(",")
            .map((item) => {
                const trimmed = item.trim();
                // Remove surrounding quotes if present
                if (
                    (trimmed.startsWith('"') && trimmed.endsWith('"')) ||
                    (trimmed.startsWith("'") && trimmed.endsWith("'"))
                ) {
                    return trimmed.slice(1, -1);
                }
                return trimmed;
            })
            .filter((item) => item);
    };

    const parseObjectField = (value: string): { [key: string]: string } => {
        if (!value.trim()) return {};
        try {
            return JSON.parse(value);
        } catch {
            // Try to parse as key=value pairs
            const result: { [key: string]: string } = {};
            value.split(",").forEach((pair) => {
                const [key, val] = pair.split("=").map((s) => s.trim());
                if (key && val) {
                    result[key] = val;
                }
            });
            return result;
        }
    };

    const formDataToServerConfig = (data: ServerFormData): MCPServerConfig => {
        const config: MCPServerConfig = {
            type: data.type,
            tools: parseArrayField(data.tools),
        };

        if (data.type === "Remote") {
            config.url = data.url;
            if (data.headers.trim()) {
                config.headers = parseObjectField(data.headers);
            }
        } else {
            config.command = data.command;
            if (data.args.trim()) {
                config.args = parseArrayField(data.args);
            }
            if (data.env.trim()) {
                config.env = parseObjectField(data.env);
            }
        }

        return config;
    };

    const serverConfigToFormData = (name: string, config: MCPServerConfig): ServerFormData => {
        return {
            name,
            type: config.type === "Remote" ? "Remote" : "Local",
            command: config.command || "",
            args: config.args ? config.args.join(", ") : "",
            env: config.env ? JSON.stringify(config.env) : "",
            url: config.url || "",
            headers: config.headers ? JSON.stringify(config.headers) : "",
            tools: config.tools ? (config.tools.includes("*") ? "*" : config.tools.join(", ")) : "*",
        };
    };

    const handleSave = () => {
        const validationErrors = validateFormData(formData);
        if (validationErrors.length > 0) {
            setErrors(validationErrors);
            return;
        }

        const serverConfig = formDataToServerConfig(formData);
        const newConfig = { ...config };

        // If editing and name changed, remove old entry
        if (mode === "edit" && selectedServer && selectedServer !== formData.name) {
            delete newConfig.mcpServers[selectedServer];
        }

        newConfig.mcpServers[formData.name] = serverConfig;
        saveConfig(newConfig);
        setMode("list");
        setSelectedServer(null);
        setFormData(defaultFormData);
        setCurrentField("name");
        setErrors([]);
    };

    const handleDelete = (serverName: string) => {
        const newConfig = { ...config };
        delete newConfig.mcpServers[serverName];
        saveConfig(newConfig);
        if (selectedServer === serverName) {
            setSelectedServer(null);
            setMode("list");
        }
    };

    const handleEdit = (serverName: string) => {
        const serverConfig = config.mcpServers[serverName];
        if (serverConfig) {
            setFormData(serverConfigToFormData(serverName, serverConfig));
            setSelectedServer(serverName);
            setMode("edit");
            setCurrentField("name");
            setErrors([]);
        }
    };

    const handleAdd = () => {
        setFormData(defaultFormData);
        setSelectedServer(null);
        setMode("add");
        setCurrentField("name");
        setErrors([]);
    };

    const handleCancel = () => {
        setMode("list");
        setSelectedServer(null);
        setFormData(defaultFormData);
        setCurrentField("name");
        setErrors([]);
    };

    const fieldOrder: (keyof ServerFormData)[] = ["name", "type", "command", "args", "env", "url", "headers", "tools"];
    const visibleFields = fieldOrder.filter((field) => {
        if (formData.type === "Remote") {
            return !["command", "args", "env"].includes(field);
        } else {
            return !["url", "headers"].includes(field);
        }
    });

    const nextField = () => {
        const currentIndex = visibleFields.indexOf(currentField);
        if (currentIndex < visibleFields.length - 1) {
            setCurrentField(visibleFields[currentIndex + 1]);
        }
    };

    const prevField = () => {
        const currentIndex = visibleFields.indexOf(currentField);
        if (currentIndex > 0) {
            setCurrentField(visibleFields[currentIndex - 1]);
        }
    };

    useInput((input, key) => {
        if (key.escape) {
            if (mode === "list") {
                onClose();
            } else {
                handleCancel();
            }
            return;
        }

        if (mode === "list") {
            if (input === "a") {
                handleAdd();
            } else if (input === "q") {
                onClose();
            }
            return;
        }

        if (mode === "edit" || mode === "add") {
            if (key.tab) {
                nextField();
                return;
            }

            if (key.shift && key.tab) {
                prevField();
                return;
            }

            if (key.ctrl && input === "s") {
                handleSave();
                return;
            }

            if (currentField === "type" && (input === "1" || input === "2")) {
                const newType = input === "1" ? "Local" : "Remote";
                setFormData((prev) => ({ ...prev, type: newType }));
                return;
            }
        }
    });

    const getFieldLabel = (field: keyof ServerFormData): string => {
        const labels = {
            name: "Server Name",
            type: "Server Type",
            command: "Command",
            args: "Arguments",
            env: "Environment Variables",
            url: "URL",
            headers: "HTTP Headers",
            tools: "Tools",
        };
        return labels[field];
    };

    const getFieldHelp = (field: keyof ServerFormData): string => {
        const help = {
            name: "Unique name for this MCP server",
            type: "Press 1 for Local, 2 for Remote",
            command: 'Command to start the server (e.g., "python")',
            args: "Command arguments, comma-separated (e.g., -m, my_server)",
            env: "Environment variables as JSON or key=value pairs",
            url: 'Remote server URL (e.g., "https://api.example.com/mcp")',
            headers: 'HTTP headers as JSON (e.g., {"Authorization": "Bearer token"})',
            tools: 'Tools to include: "*" for all, or comma-separated list (no quotes needed)',
        };
        return help[field];
    };

    if (mode === "list") {
        const serverNames = Object.keys(config.mcpServers);

        return (
            <Box flexDirection="column" paddingX={1}>
                <Box marginBottom={1}>
                    <Text color={Colors.COPILOT} bold>
                        MCP Server Configuration
                    </Text>
                </Box>

                {serverNames.length === 0 ? (
                    <Box marginBottom={1}>
                        <Text color={Colors.MUTED}>No MCP servers configured.</Text>
                    </Box>
                ) : (
                    <Box flexDirection="column" marginBottom={1}>
                        {serverNames.map((name, index) => {
                            const server = config.mcpServers[name];
                            const type = server.type || "local";
                            const details = type === "remote" ? server.url : server.command;

                            return (
                                <Box key={name} flexDirection="row" marginBottom={1}>
                                    <Text color={Colors.WARNING}>{index + 1}. </Text>
                                    <Text color={Colors.INFO} bold>
                                        {name}
                                    </Text>
                                    <Text color={Colors.MUTED}>
                                        {" "}
                                        ({type}): {details}
                                    </Text>
                                </Box>
                            );
                        })}
                    </Box>
                )}

                <Box flexDirection="column" marginBottom={1}>
                    <Text color={Colors.SUCCESS}>Commands:</Text>
                    <Text>• Press 'a' to add a new server</Text>
                    {serverNames.length > 0 && (
                        <>
                            <Text>• Press number (1-{serverNames.length}) to edit server</Text>
                            <Text>• Press Ctrl+D after number to delete server</Text>
                        </>
                    )}
                    <Text>• Press 'q' or Escape to exit</Text>
                </Box>

                {/* Handle number input for editing/deleting */}
                <ServerListHandler serverNames={serverNames} onEdit={handleEdit} onDelete={handleDelete} />
            </Box>
        );
    }

    return (
        <Box flexDirection="column" paddingX={1}>
            <Box marginBottom={1}>
                <Text color={Colors.COPILOT} bold>
                    {mode === "add" ? "Add New MCP Server" : `Edit Server: ${selectedServer}`}
                </Text>
            </Box>

            {errors.length > 0 && (
                <Box flexDirection="column" marginBottom={1}>
                    <Text color={Colors.ERROR} bold>
                        Errors:
                    </Text>
                    {errors.map((error, index) => (
                        <Text key={index} color={Colors.ERROR}>
                            • {error}
                        </Text>
                    ))}
                </Box>
            )}

            <Box flexDirection="column" marginBottom={1}>
                {visibleFields.map((field) => (
                    <Box key={field} flexDirection="column" marginBottom={1}>
                        <Text color={currentField === field ? "cyan" : "gray"}>{getFieldLabel(field)}:</Text>
                        <Text color={Colors.MUTED}>{getFieldHelp(field)}</Text>

                        {field === "type" ? (
                            <Box flexDirection="row">
                                <Text color={formData.type === "Local" ? "green" : "gray"}>[1] Local</Text>
                                <Text> </Text>
                                <Text color={formData.type === "Remote" ? "green" : "gray"}>[2] Remote</Text>
                            </Box>
                        ) : (
                            <Box borderStyle="single" borderColor={currentField === field ? "cyan" : "gray"}>
                                <TextInput
                                    value={formData[field] as string}
                                    onChange={(value) =>
                                        setFormData((prev) => ({
                                            ...prev,
                                            [field]: value,
                                        }))
                                    }
                                    focus={currentField === field}
                                />
                            </Box>
                        )}
                    </Box>
                ))}
            </Box>

            <Box flexDirection="column">
                <Text color={Colors.SUCCESS}>Navigation:</Text>
                <Text>• Tab/Shift+Tab: Navigate fields</Text>
                <Text>• Ctrl+S: Save server</Text>
                <Text>• Escape: Cancel</Text>
            </Box>
        </Box>
    );
};

// Helper component to handle number input for server selection
interface ServerListHandlerProps {
    serverNames: string[];
    onEdit: (serverName: string) => void;
    onDelete: (serverName: string) => void;
}

const ServerListHandler: React.FC<ServerListHandlerProps> = ({ serverNames, onEdit, onDelete }) => {
    const [pendingNumber, setPendingNumber] = useState<number | null>(null);

    useInput((input, key) => {
        const num = parseInt(input);
        if (!isNaN(num) && num >= 1 && num <= serverNames.length) {
            if (key.ctrl && input === "d" && pendingNumber !== null) {
                // Delete with Ctrl+D
                onDelete(serverNames[pendingNumber - 1]);
                setPendingNumber(null);
            } else {
                setPendingNumber(num);
                // Auto-edit after short delay
                setTimeout(() => {
                    if (pendingNumber === num) {
                        onEdit(serverNames[num - 1]);
                        setPendingNumber(null);
                    }
                }, 1000);
            }
        }
    });

    return null;
};
