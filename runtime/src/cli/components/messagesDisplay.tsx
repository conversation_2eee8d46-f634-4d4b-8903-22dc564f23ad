/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { Box, Text } from "ink";
import React from "react";
import { matchMessage, Message } from "../types";
import { Colors } from "../themes";

type MessagesDisplayProps = {
    messages: Message[];
    expand: boolean;
};

const MessagesDisplay: React.FC<MessagesDisplayProps> = ({ messages, expand }) => {
    if (messages.length === 0) return null;

    return (
        <Box flexDirection="column">
            {messages.map((message) => {
                return (
                    <Box key={message.id} flexDirection="column" marginBottom={1}>
                        {matchMessage(message, {
                            onCopilot: (m) => <CopilotMessage {...m} />,
                            onError: (m) => <ErrorMessage {...m} />,
                            onInfo: (m) => <InfoMessage {...m} />,
                            onToolCallRequested: (m) => <ToolCallRequestedMessage {...m} />,
                            onToolCallCompleted: (m) => <ToolCallResultMessage expand={expand} {...m} />,
                            onUser: (m) => <UserMessage {...m} />,
                        })}
                    </Box>
                );
            })}
        </Box>
    );
};

MessagesDisplay.displayName = "MessagesDisplay";

type CopilotMessageProps = {
    text: string;
};

const CopilotMessage: React.FC<CopilotMessageProps> = ({ text }) => {
    return (
        <Box flexDirection="row">
            <Text color={Colors.COPILOT}>● </Text>
            <Text color={Colors.INFO}>{`${text}`}</Text>
        </Box>
    );
};

type ErrorMessageProps = {
    text: string;
};

const ErrorMessage: React.FC<ErrorMessageProps> = ({ text }) => {
    return (
        <Box flexDirection="row">
            <Text color={Colors.ERROR}>● </Text>
            <Text color={Colors.INFO}>{`${text}`}</Text>
        </Box>
    );
};

type InfoMessageProps = {
    text: string;
};

const InfoMessage: React.FC<InfoMessageProps> = ({ text }) => {
    return (
        <Box flexDirection="row">
            <Text color={Colors.ACCENT}>● </Text>
            <Text color={Colors.INFO}>{`${text}`}</Text>
        </Box>
    );
};

type UserMessageProps = {
    text: string;
};

const UserMessage: React.FC<UserMessageProps> = ({ text }) => {
    return (
        <Box flexDirection="row">
            <Text color={Colors.MUTED}>{`> ${text}`}</Text>
        </Box>
    );
};

type ToolCallRequestedMessageProps = {
    callId: string;
    name: string;
    intentionSummary: string | null;
};

export const ToolCallRequestedMessage: React.FC<ToolCallRequestedMessageProps> = ({ name, intentionSummary }) => {
    return (
        <Box flexDirection="row">
            <Text color={Colors.MUTED}>● </Text>
            {intentionSummary ? (
                <>
                    <Text color={Colors.INFO}>{`${name}: `}</Text>
                    <Text color={Colors.MUTED}>{`${intentionSummary}`}</Text>
                </>
            ) : (
                <Text color={Colors.INFO}>{`${name}`}</Text>
            )}
        </Box>
    );
};

type ToolCallResultMessageProps = {
    callId: string;
    name: string;
    intentionSummary: string | null;
    result:
        | {
              type: "success";
              log: string;
          }
        | {
              type: "failure";
              log: string;
          }
        | {
              type: "rejected";
          };
    expand: boolean;
};

export const ToolCallResultMessage: React.FC<ToolCallResultMessageProps> = ({
    name,
    intentionSummary,
    result,
    expand,
}) => {
    const iconColor = result.type === "success" ? Colors.SUCCESS : Colors.ERROR;

    type Content = {
        showToggleHelp: boolean;
        text: string | null;
        textColor?: string;
    };

    const content = React.useMemo((): Content => {
        if (result.type === "rejected") {
            return {
                showToggleHelp: false,
                text: "Cancelled by you.",
                textColor: Colors.ERROR,
            };
        }

        if (result.type === "failure") {
            return {
                showToggleHelp: false,
                text: result.log,
                textColor: Colors.ERROR,
            };
        }

        if (result.log.trim() === "") {
            return {
                showToggleHelp: false,
                text: "No Content",
                textColor: Colors.MUTED,
            };
        }

        if (expand) {
            return {
                showToggleHelp: true,
                text: result.log.trim(),
                textColor: Colors.INFO,
            };
        }

        return {
            showToggleHelp: true,
            text: null,
        };
    }, [expand, result]);

    return (
        <Box flexDirection="column">
            <Box flexDirection="row">
                <Text color={iconColor}>● </Text>
                {intentionSummary ? (
                    <>
                        <Text color={Colors.INFO}>{`${name}: `}</Text>
                        <Text color={Colors.MUTED}>{`${intentionSummary}`}</Text>
                    </>
                ) : (
                    <Text color={Colors.INFO}>{`${name}`}</Text>
                )}
            </Box>
            <Box flexDirection="column" paddingLeft={2}>
                {content.text && <Text color={content.textColor}>{content.text}</Text>}
                {content.showToggleHelp && (
                    <Text color={Colors.MUTED}>Press ctrl+r to {expand ? "collapse" : "expand"}</Text>
                )}
            </Box>
        </Box>
    );
};

export default React.memo(MessagesDisplay);
