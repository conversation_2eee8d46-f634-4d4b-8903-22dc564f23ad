/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { Box, Text } from "ink";
import React, { useState, useCallback } from "react";
import CustomTextInput from "./customTextInput";
import ThinkingAnimation from "./thinkingAnimation";
import { Colors } from "../themes";
import { TextCursorLocation } from "../hooks/useTextCursorLocation";

interface InputAreaProps {
    onSubmit: (value: string) => void;
    isDisabled: boolean;
    isCancelling: boolean;
    commandHistory: string[];
    textCursorLocation: TextCursorLocation;
    userInputPlaceholder?: string;
    historyIndex: number;
    setHistoryIndex: (index: number) => void;
    isThinking: boolean;
}

const InputArea: React.FC<InputAreaProps> = React.memo(
    ({ onSubmit, isDisabled, isCancelling, textCursorLocation, userInputPlaceholder, setHistoryIndex, isThinking }) => {
        const [isInputFocused] = useState(true);
        const prompt = "> ";

        const handleSubmit = useCallback(
            (value: string) => {
                if (value.trim() && !isDisabled) {
                    onSubmit(value.trim());
                    textCursorLocation.clear();
                    setHistoryIndex(-1); // Reset history index
                }
            },
            [onSubmit, isDisabled, textCursorLocation, setHistoryIndex],
        );

        return (
            <Box flexShrink={0} flexDirection="column" width="100%">
                {isThinking && <ThinkingAnimation isCancelling={isCancelling} />}
                <Box
                    borderStyle="single"
                    borderColor={isThinking ? Colors.COPILOT : Colors.MUTED}
                    paddingLeft={1}
                    paddingRight={1}
                    flexGrow={1}
                >
                    <Text color={isThinking ? Colors.COPILOT : Colors.MUTED}>{prompt}</Text>
                    <CustomTextInput
                        textCursorLocation={textCursorLocation}
                        onSubmit={handleSubmit}
                        placeholder={userInputPlaceholder}
                        focus={isInputFocused && !isDisabled}
                    />
                </Box>
                <Box paddingLeft={1}>
                    <Text color={Colors.MUTED}>Type ? for help</Text>
                </Box>
            </Box>
        );
    },
);

InputArea.displayName = "InputArea";

export default InputArea;
