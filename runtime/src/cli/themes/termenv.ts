/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

interface RGB {
    r: number;
    g: number;
    b: number;
}

interface HSL {
    h: number;
    s: number;
    l: number;
}

/**
 * Convert RGB values to HSL
 */
function rgbToHsl(rgb: RGB): HSL {
    const r = rgb.r / 255;
    const g = rgb.g / 255;
    const b = rgb.b / 255;

    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    const diff = max - min;

    let h = 0;
    let s = 0;
    const l = (max + min) / 2;

    if (diff !== 0) {
        s = l > 0.5 ? diff / (2 - max - min) : diff / (max + min);

        switch (max) {
            case r:
                h = (g - b) / diff + (g < b ? 6 : 0);
                break;
            case g:
                h = (b - r) / diff + 2;
                break;
            case b:
                h = (r - g) / diff + 4;
                break;
        }
        h /= 6;
    }

    return { h: h * 360, s, l };
}

/**
 * Parse xterm color response (e.g., "rgb:1234/5678/9abc")
 */
export function parseXTermColor(response: string): RGB | null {
    // Validate response format and length more strictly
    if (response.length < 12 || response.length > 18) {
        return null;
    }

    const match = response.match(/rgb:([0-9a-f]+)\/([0-9a-f]+)\/([0-9a-f]+)/i);
    if (!match) {
        return null;
    }

    const [, rHex, gHex, bHex] = match;

    // XTerm can return 2, 4, or more hex digits per component
    // We need to normalize to 8-bit values (0-255)
    const normalizeHex = (hex: string): number => {
        if (hex.length >= 2) {
            // Take the first 2 characters for 8-bit precision
            return parseInt(hex.substring(0, 2), 16);
        } else if (hex.length === 1) {
            // If only 1 character, duplicate it (e.g., "f" -> "ff")
            return parseInt(hex + hex, 16);
        }
        return 0;
    };

    const r = normalizeHex(rHex);
    const g = normalizeHex(gHex);
    const b = normalizeHex(bHex);

    return { r, g, b };
}

/**
 * Complete ANSI color palette (0-255) with hex values
 * This matches the standard terminal color palette used by most terminals
 */
export const ANSI_COLOR_PALETTE: string[] = [
    // Standard colors (0-7)
    "#000000",
    "#800000",
    "#008000",
    "#808000",
    "#000080",
    "#800080",
    "#008080",
    "#c0c0c0",
    // Bright colors (8-15)
    "#808080",
    "#ff0000",
    "#00ff00",
    "#ffff00",
    "#0000ff",
    "#ff00ff",
    "#00ffff",
    "#ffffff",
    // 216 colors (16-231): 6x6x6 color cube
    "#000000",
    "#00005f",
    "#000087",
    "#0000af",
    "#0000d7",
    "#0000ff",
    "#005f00",
    "#005f5f",
    "#005f87",
    "#005faf",
    "#005fd7",
    "#005fff",
    "#008700",
    "#00875f",
    "#008787",
    "#0087af",
    "#0087d7",
    "#0087ff",
    "#00af00",
    "#00af5f",
    "#00af87",
    "#00afaf",
    "#00afd7",
    "#00afff",
    "#00d700",
    "#00d75f",
    "#00d787",
    "#00d7af",
    "#00d7d7",
    "#00d7ff",
    "#00ff00",
    "#00ff5f",
    "#00ff87",
    "#00ffaf",
    "#00ffd7",
    "#00ffff",
    "#5f0000",
    "#5f005f",
    "#5f0087",
    "#5f00af",
    "#5f00d7",
    "#5f00ff",
    "#5f5f00",
    "#5f5f5f",
    "#5f5f87",
    "#5f5faf",
    "#5f5fd7",
    "#5f5fff",
    "#5f8700",
    "#5f875f",
    "#5f8787",
    "#5f87af",
    "#5f87d7",
    "#5f87ff",
    "#5faf00",
    "#5faf5f",
    "#5faf87",
    "#5fafaf",
    "#5fafd7",
    "#5fafff",
    "#5fd700",
    "#5fd75f",
    "#5fd787",
    "#5fd7af",
    "#5fd7d7",
    "#5fd7ff",
    "#5fff00",
    "#5fff5f",
    "#5fff87",
    "#5fffaf",
    "#5fffd7",
    "#5fffff",
    "#870000",
    "#87005f",
    "#870087",
    "#8700af",
    "#8700d7",
    "#8700ff",
    "#875f00",
    "#875f5f",
    "#875f87",
    "#875faf",
    "#875fd7",
    "#875fff",
    "#878700",
    "#87875f",
    "#878787",
    "#8787af",
    "#8787d7",
    "#8787ff",
    "#87af00",
    "#87af5f",
    "#87af87",
    "#87afaf",
    "#87afd7",
    "#87afff",
    "#87d700",
    "#87d75f",
    "#87d787",
    "#87d7af",
    "#87d7d7",
    "#87d7ff",
    "#87ff00",
    "#87ff5f",
    "#87ff87",
    "#87ffaf",
    "#87ffd7",
    "#87ffff",
    "#af0000",
    "#af005f",
    "#af0087",
    "#af00af",
    "#af00d7",
    "#af00ff",
    "#af5f00",
    "#af5f5f",
    "#af5f87",
    "#af5faf",
    "#af5fd7",
    "#af5fff",
    "#af8700",
    "#af875f",
    "#af8787",
    "#af87af",
    "#af87d7",
    "#af87ff",
    "#afaf00",
    "#afaf5f",
    "#afaf87",
    "#afafaf",
    "#afafd7",
    "#afafff",
    "#afd700",
    "#afd75f",
    "#afd787",
    "#afd7af",
    "#afd7d7",
    "#afd7ff",
    "#afff00",
    "#afff5f",
    "#afff87",
    "#afffaf",
    "#afffd7",
    "#afffff",
    "#d70000",
    "#d7005f",
    "#d70087",
    "#d700af",
    "#d700d7",
    "#d700ff",
    "#d75f00",
    "#d75f5f",
    "#d75f87",
    "#d75faf",
    "#d75fd7",
    "#d75fff",
    "#d78700",
    "#d7875f",
    "#d78787",
    "#d787af",
    "#d787d7",
    "#d787ff",
    "#d7af00",
    "#d7af5f",
    "#d7af87",
    "#d7afaf",
    "#d7afd7",
    "#d7afff",
    "#d7d700",
    "#d7d75f",
    "#d7d787",
    "#d7d7af",
    "#d7d7d7",
    "#d7d7ff",
    "#d7ff00",
    "#d7ff5f",
    "#d7ff87",
    "#d7ffaf",
    "#d7ffd7",
    "#d7ffff",
    "#ff0000",
    "#ff005f",
    "#ff0087",
    "#ff00af",
    "#ff00d7",
    "#ff00ff",
    "#ff5f00",
    "#ff5f5f",
    "#ff5f87",
    "#ff5faf",
    "#ff5fd7",
    "#ff5fff",
    "#ff8700",
    "#ff875f",
    "#ff8787",
    "#ff87af",
    "#ff87d7",
    "#ff87ff",
    "#ffaf00",
    "#ffaf5f",
    "#ffaf87",
    "#ffafaf",
    "#ffafd7",
    "#ffafff",
    "#ffd700",
    "#ffd75f",
    "#ffd787",
    "#ffd7af",
    "#ffd7d7",
    "#ffd7ff",
    "#ffff00",
    "#ffff5f",
    "#ffff87",
    "#ffffaf",
    "#ffffd7",
    "#ffffff",
    // Grayscale colors (232-255)
    "#080808",
    "#121212",
    "#1c1c1c",
    "#262626",
    "#303030",
    "#3a3a3a",
    "#444444",
    "#4e4e4e",
    "#585858",
    "#626262",
    "#6c6c6c",
    "#767676",
    "#808080",
    "#8a8a8a",
    "#949494",
    "#9e9e9e",
    "#a8a8a8",
    "#b2b2b2",
    "#bcbcbc",
    "#c6c6c6",
    "#d0d0d0",
    "#dadada",
    "#e4e4e4",
    "#eeeeee",
];

/**
 * Convert hex color to RGB
 */
export function hexToRgb(hex: string): RGB {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    if (!result) {
        return { r: 0, g: 0, b: 0 };
    }
    return {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16),
    };
}

/**
 * Convert ANSI color index to RGB
 */
export function ansiColorToRgb(colorIndex: number): RGB {
    // Validate color index range
    if (colorIndex < 0 || colorIndex >= ANSI_COLOR_PALETTE.length) {
        return { r: 0, g: 0, b: 0 }; // Default to black for invalid indices
    }

    return hexToRgb(ANSI_COLOR_PALETTE[colorIndex]);
}

/**
 * Parse and validate xterm OSC 11 response
 * Handles different response formats and terminators like the Go termenv library
 */
function parseOSC11Response(response: string): RGB | null {
    // Validate response length (should be between 15-25 characters typically)
    if (response.length < 15 || response.length > 30) {
        return null;
    }

    // Remove various possible terminators
    let cleanResponse = response;
    if (cleanResponse.endsWith("\x07")) {
        // BEL
        cleanResponse = cleanResponse.slice(0, -1);
    } else if (cleanResponse.endsWith("\x1b\\")) {
        // ESC \
        cleanResponse = cleanResponse.slice(0, -2);
    } else if (cleanResponse.endsWith("\x9c")) {
        // ST (String Terminator)
        cleanResponse = cleanResponse.slice(0, -1);
    }

    // The response should start with something like "11;rgb:" or just "rgb:"
    let colorPart = cleanResponse;
    if (colorPart.startsWith("11;")) {
        colorPart = colorPart.substring(3);
    }

    if (!colorPart.startsWith("rgb:")) {
        return null;
    }

    return parseXTermColor(colorPart);
}

/**
 * Query terminal background color using OSC 11
 */
async function queryTerminalBackgroundColor(timeoutMs: number = 100): Promise<RGB | null> {
    return new Promise((resolve) => {
        if (!process.stdout.isTTY) {
            resolve(null);
            return;
        }

        let responseBuffer = "";
        let timeoutId: NodeJS.Timeout | null = null;

        const cleanup = () => {
            process.stdin.removeListener("data", onData);
            process.stdin.setRawMode(false);
            if (timeoutId) {
                clearTimeout(timeoutId);
            }
        };

        const onData = (chunk: Buffer) => {
            responseBuffer += chunk.toString();

            // Look for OSC 11 response pattern with various terminators
            // Pattern: ESC ] 11 ; rgb:xxxx/yyyy/zzzz (BEL|ESC \|ST)
            // eslint-disable-next-line no-control-regex
            const oscMatch = responseBuffer.match(/\x1b\]11;([^\x07\x1b\x9c]*?)(?:\x07|\x1b\\|\x9c)/);
            if (oscMatch) {
                cleanup();
                const colorResponse = `11;${oscMatch[1]}`;
                resolve(parseOSC11Response(colorResponse));
                return;
            }

            // Also handle responses that might not have the ESC] prefix in our buffer
            // eslint-disable-next-line no-control-regex
            const directMatch = responseBuffer.match(/11;rgb:([0-9a-f/]+)(?:\x07|\x1b\\|\x9c)/i);
            if (directMatch) {
                cleanup();
                resolve(parseOSC11Response(`11;rgb:${directMatch[1]}`));
                return;
            }
        };

        timeoutId = setTimeout(() => {
            cleanup();
            resolve(null);
        }, timeoutMs);

        try {
            process.stdin.setRawMode(true);
            process.stdin.on("data", onData);

            // Send OSC 11 query (request background color)
            process.stdout.write("\x1b]11;?\x1b\\");
        } catch (_error) {
            cleanup();
            resolve(null);
        }
    });
}

/**
 * Get background color from COLORFGBG environment variable
 */
function getColorFromEnvironment(): RGB | null {
    const colorFgBg = process.env.COLORFGBG;
    if (!colorFgBg || !colorFgBg.includes(";")) {
        return null;
    }

    const parts = colorFgBg.split(";");
    const bgColorStr = parts[parts.length - 1];
    const bgColorIndex = parseInt(bgColorStr, 10);

    if (isNaN(bgColorIndex)) {
        return null;
    }

    return ansiColorToRgb(bgColorIndex);
}

/**
 * Determine if the terminal has a dark background.
 *
 * This function attempts to detect the terminal background color using:
 * 1. OSC 11 terminal query (works on most modern terminals)
 * 2. COLORFGBG environment variable (fallback)
 * 3. Default assumption (dark background)
 *
 * Note: On Windows, this works best with Windows Terminal, PowerShell, or other
 * modern terminals that support OSC sequences. Legacy Command Prompt may not work.
 *
 * @returns Promise<boolean> - true if the background is dark, false if light
 */
export async function hasDarkBackground(): Promise<boolean> {
    try {
        // First try OSC 11 query
        const oscColor = await queryTerminalBackgroundColor();
        if (oscColor) {
            const hsl = rgbToHsl(oscColor);
            return hsl.l < 0.5;
        }

        // Fallback to COLORFGBG environment variable
        const envColor = getColorFromEnvironment();
        if (envColor) {
            const hsl = rgbToHsl(envColor);
            return hsl.l < 0.5;
        }

        // Default to dark background if we can't determine
        return true;
    } catch (_error) {
        // If anything fails, assume dark background
        return true;
    }
}
