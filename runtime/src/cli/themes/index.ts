/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { hasDarkBackground } from "./termenv";

// TODO(wm): we should put theming in a hook so that it can be changed live, but for the moment we're
// just choosing one on startup.

type SemanticColors = "INFO" | "ACCENT" | "SUCCESS" | "ERROR" | "WARNING" | "COPILOT" | "MUTED" | "SELECTED";

export type Theme = Record<SemanticColors, ANSIColors>;

export type ANSIColors =
    | "black"
    | "red"
    | "green"
    | "yellow"
    | "blue"
    | "magenta"
    | "cyan"
    | "white"
    | "blackBright"
    | "gray"
    | "grey"
    | "redBright"
    | "greenBright"
    | "yellowBright"
    | "blueBright"
    | "magentaBright"
    | "cyanBright"
    | "whiteBright";

export const ANSI_DARK: Theme = {
    INFO: "whiteBright",
    ACCENT: "blueBright",
    SUCCESS: "greenBright",
    ERROR: "redBright",
    WARNING: "yellowBright",
    COPILOT: "magentaBright",
    MUTED: "gray",
    SELECTED: "cyan",
};

export const ANSI_LIGHT: Theme = {
    INFO: "black",
    ACCENT: "blue",
    SUCCESS: "green",
    ERROR: "redBright",
    WARNING: "yellow",
    COPILOT: "magenta",
    MUTED: "gray",
    SELECTED: "cyan",
};

/**
 * Get the appropriate theme based on the terminal background
 */
async function getThemeForTerminal(): Promise<Theme> {
    const isDark = await hasDarkBackground();
    return isDark ? ANSI_DARK : ANSI_LIGHT;
}

export const Colors = await getThemeForTerminal();
