/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

export type CopilotMessage = {
    type: "copilot";
    text: string;
};

export type ErrorMessage = {
    type: "error";
    text: string;
};

export type InfoMessage = {
    type: "info";
    text: string;
};

export type UserMessage = {
    type: "user";
    text: string;
};

export type ToolCallRequestedMessage = {
    type: "tool_call_requested";
    callId: string;
    name: string;
    // A human readable description of what this tool will be doing when it is running
    intentionSummary: string | null;
};

export type ToolCallCompletedMessage = {
    type: "tool_call_completed";
    callId: string;
    name: string;
    // A human readable description of what this tool will be doing when it is running
    intentionSummary: string | null;
    result:
        | {
              type: "success";
              log: string;
          }
        | {
              type: "failure";
              log: string;
          }
        | {
              type: "rejected";
          };
};

export type MessageWithoutID =
    | CopilotMessage
    | ErrorMessage
    | InfoMessage
    | ToolCallRequestedMessage
    | ToolCallCompletedMessage
    | UserMessage;

export type Message = MessageWithoutID & { id: number };

export function matchMessage<R>(
    message: MessageWithoutID,
    fns: {
        onCopilot: (m: CopilotMessage) => R;
        onError: (m: ErrorMessage) => R;
        onInfo: (m: InfoMessage) => R;
        onToolCallRequested: (m: ToolCallRequestedMessage) => R;
        onToolCallCompleted: (m: ToolCallCompletedMessage) => R;
        onUser: (m: UserMessage) => R;
    },
): R {
    switch (message.type) {
        case "copilot":
            return fns.onCopilot(message);
        case "error":
            return fns.onError(message);
        case "info":
            return fns.onInfo(message);
        case "tool_call_requested":
            return fns.onToolCallRequested(message);
        case "tool_call_completed":
            return fns.onToolCallCompleted(message);
        case "user":
            return fns.onUser(message);
        default: {
            const _exhaustive: never = message;
            throw new Error(`Unexpected message type: ${JSON.stringify(message)}`);
        }
    }
}
