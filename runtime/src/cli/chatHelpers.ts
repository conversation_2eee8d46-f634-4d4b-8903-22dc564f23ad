/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import {
    Event,
    ImageProcessingEvent,
    ImageRemovalEvent,
    MessageEvent,
    ModelCallFailureEvent,
    ModelCallSuccessEvent,
    ResponseEvent,
    SessionLogEvent,
    ToolExecutionEvent,
    TruncationEvent,
    TurnEvent,
} from "../model/event";

export function isNonEmptyString(value: string | null): value is string {
    return typeof value === "string" && value.trim().length > 0;
}

export function matchEvent<R>(
    event: Event,
    fns: {
        onImageProcessingEvent: (e: ImageProcessingEvent) => R;
        onImageRemovalEvent: (e: ImageRemovalEvent) => R;
        onMessage: (e: MessageEvent) => R;
        onResponse: (e: ResponseEvent) => R;
        onSessionLog: (e: SessionLogEvent) => R;
        onModelCallFailure: (e: ModelCallFailureEvent) => R;
        onModelCallSuccess: (e: ModelCallSuccessEvent) => R;
        onToolExecution: (e: ToolExecutionEvent) => R;
        onTruncationEvent: (e: TruncationEvent) => R;
        onTurnEvent: (e: TurnEvent) => R;
    },
): R {
    switch (event.kind) {
        case "image_processing":
            return fns.onImageProcessingEvent(event);
        case "images_removed":
            return fns.onImageRemovalEvent(event);
        case "message":
            return fns.onMessage(event);
        case "model_call_failure":
            return fns.onModelCallFailure(event);
        case "model_call_success":
            return fns.onModelCallSuccess(event);
        case "response":
            return fns.onResponse(event);
        case "tool_execution":
            return fns.onToolExecution(event);
        case "history_truncated":
            return fns.onTruncationEvent(event);
        case "turn_started":
        case "turn_ended":
        case "turn_failed":
        case "turn_retry":
            return fns.onTurnEvent(event);
        case "session_log":
            return fns.onSessionLog(event);
        default: {
            // satisfies the exhaustiveness check at compile‑time
            const _exhaustive: never = event;
            throw new Error(`unexpected event kind: ${JSON.stringify(event)}`);
        }
    }
}
