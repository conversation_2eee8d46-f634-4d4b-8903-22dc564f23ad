/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { conversationHistoryManager } from "../conversationHistory";
import { spawnAsync, SpawnError } from "../spawn-async";
import { Message } from "../types";

export const targetFeedbackRepo = "github/sweagentd";

/**
 * Submits feedback to the target GitHub repository.
 * @param title Title of the issue
 * @param description Description of the feedback
 * @param conversationSessionId Conversation session ID
 * @param agentMessages Messages from the agent
 * @returns The URL of the created issue
 */
export async function submitFeedback(
    title: string,
    description: string,
    conversationSessionId: string | null,
    messages: ReadonlyArray<Message>,
): Promise<string> {
    // Check if `gh` CLI is installed
    try {
        await spawnAsync("gh", ["--version"]);
    } catch (_error) {
        throw new Error(
            "GitHub CLI (`gh`) is not installed or not available on the PATH. Please install it to submit feedback. You can find installation instructions at https://cli.github.com/",
        );
    }

    // Check if `gh` CLI is logged in with `gh auth status --active --hostname github.com`
    try {
        await spawnAsync("gh", ["auth", "status", "--active", "--hostname", "github.com"]);
    } catch (_error) {
        throw new Error(
            "GitHub CLI (`gh`) is not logged into GitHub. Run 'gh auth login' to log in, and ensure to give access to the 'GitHub' organization.",
        );
    }

    // Check if the current `gh` login token has access to the target repository
    try {
        await spawnAsync("gh", ["api", `repos/${targetFeedbackRepo}`]);
    } catch (_error) {
        if (
            _error instanceof SpawnError &&
            _error.stdout.includes("Resource protected by organization SAML enforcement")
        ) {
            throw new Error(
                `GitHub CLI (\`gh\`) does not have access to the target repository: ${targetFeedbackRepo}. Run \`gh auth refresh\` and give access to the 'GitHub' org, or update your PAT with the necessary permissions.`,
            );
        } else {
            throw new Error(
                `GitHub CLI (\`gh\`) is not logged in or does not have access to the target repository: ${targetFeedbackRepo}. Please ensure you have the necessary permissions.`,
            );
        }
    }

    // Wrap conversation in a `details` block
    // Prepare the body for the issue
    const formattedConversation =
        conversationSessionId === null
            ? "Not in a conversation session (session ID is null)"
            : JSON.stringify(conversationHistoryManager.getSessionHistory(conversationSessionId), null, 2);
    const formattedMessages = messages
        .map((msg, index) => {
            return `- ${index + 1} ${JSON.stringify(msg)}`;
        })
        .join("\n");
    const body = [
        `**Description:**\n${description}`,
        `**Messages:**\n<details>\n<summary>Click to expand</summary>\n\n\`\`\`\n${formattedMessages}\n\`\`\`\n\n</details>`,
        `**Conversation History:**\n<details>\n<summary>Click to expand</summary>\n\n\`\`\`\n${formattedConversation}\n\`\`\`\n\n</details>`,
    ].join("\n\n");

    // Execute the command
    try {
        const result = await spawnAsync("gh", [
            "issue",
            "create",
            "--repo",
            targetFeedbackRepo,
            "--title",
            title,
            "--body",
            body,
            "--label",
            "cli",
            "--label",
            "feedback",
        ]);

        return result.stdout.trim();
    } catch (error) {
        if (error instanceof SpawnError) {
            throw new Error(
                `Failed to create issue in ${targetFeedbackRepo}: ${error.message}\n\nstdout: ${error.stdout}\nstderr: ${error.stderr}`,
            );
        } else {
            throw error;
        }
    }
}
