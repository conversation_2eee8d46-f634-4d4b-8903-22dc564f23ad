/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { existsSync, mkdirSync, readFileSync, rmSync, unlinkSync, writeFileSync } from "fs";
import { homedir } from "os";
import { join } from "path";
import { conversationHistoryManager } from "../conversationHistory";
import { MessageWithoutID } from "../types";
import { targetFeedbackRepo } from "./submitFeedback";

// Types
interface MCPServerConfig {
    command?: string;
    args?: string[];
    env?: { [key: string]: string };
    tools?: string[];
    type?: string;
    url?: string;
    headers?: { [key: string]: string };
    isDefaultServer?: boolean;
}

interface MCPConfig {
    mcpServers: { [serverName: string]: MCPServerConfig };
}

export interface SlashCommandDescription {
    name: string;
    args?: string;
    help: string;
}

interface SlashCommandResult {
    handled: boolean;
    showFeedbackForm?: boolean;
    showLoginUI?: boolean;
    logout?: boolean;
    showMCPConfigUI?: boolean;
    addMessage?: MessageWithoutID;
    clearMessages?: boolean;
    clearHistory?: boolean;
    clearSession?: boolean;
}

const knownSlashCommands: SlashCommandDescription[] = [
    {
        name: "/clear",
        help: "Clear chat history and start a fresh session",
    },
    {
        name: "/cwd",
        args: "[directory]",
        help: "Change working directory or show current directory",
    },
    {
        name: "/feedback",
        help: `Provide feedback about the CLI and submit it to ${targetFeedbackRepo}`,
    },
    {
        name: "/history",
        args: "[show|clear]",
        help: "View or clear conversation history",
    },
    {
        name: "/login",
        help: "Log in to Copilot",
    },
    {
        name: "/logout",
        help: "Log out of Copilot",
    },
    {
        name: "/mcp",
        args: "[show|add|edit|delete] [server-name]",
        help: "Manage MCP server configuration",
    },
];

// Global references (these will be passed in from App.tsx)
let globalAddMessage: (message: MessageWithoutID) => void;

// Initialize global references
export function initializeSlashCommands(addMessage: (message: MessageWithoutID) => void) {
    globalAddMessage = addMessage;
}

// MCP Configuration utilities
function getMcpConfigPath(): string {
    const copilotDir = join(homedir(), ".copilot");
    return join(copilotDir, "mcpconfig");
}

function readMcpConfig(): MCPConfig {
    const mcpConfigPath = getMcpConfigPath();
    try {
        if (existsSync(mcpConfigPath)) {
            const configContent = readFileSync(mcpConfigPath, "utf8");
            return JSON.parse(configContent);
        }
    } catch (error) {
        console.error("Error reading MCP config:", error);
    }
    return { mcpServers: {} };
}

function writeMcpConfig(config: MCPConfig): void {
    const mcpConfigPath = getMcpConfigPath();
    const copilotDir = join(homedir(), ".copilot");

    try {
        mkdirSync(copilotDir, { recursive: true });
        writeFileSync(mcpConfigPath, JSON.stringify(config, null, 2), "utf8");
    } catch (error) {
        throw new Error(`Failed to write MCP config: ${error}`);
    }
}

function loadMcpConfigIntoMemory(): MCPConfig {
    return readMcpConfig();
}

export function getMatchingSlashCommands(input: string): SlashCommandDescription[] {
    if (!input.startsWith("/")) {
        return [];
    }

    const inputFirstWordEnd = input.indexOf(" ");
    const inputFirstWord = inputFirstWordEnd < 0 ? input : input.substring(0, inputFirstWordEnd);
    return knownSlashCommands.filter((c) => c.name.startsWith(inputFirstWord));
}

// Command history utilities
// Slash command handlers
export function handleHelpCommand(): SlashCommandResult {
    const helpText = [
        "Available commands:",
        ...knownSlashCommands.map((cmd) => `${cmd.name}${cmd.args ? ` ${cmd.args}` : ""} - ${cmd.help}`),
        "",
        "Use ↑/↓ arrow keys to navigate command history",
    ].join("\n");

    return {
        handled: true,
        addMessage: { type: "info", text: helpText },
    };
}

export function handleClearCommand(
    setSessionCleared: (cleared: boolean) => void,
    setCachedExecuteCommand: (command: unknown) => void,
): SlashCommandResult {
    try {
        const copilotDir = join(homedir(), ".copilot");

        // Clear conversation history using the manager
        conversationHistoryManager.clearHistory();
        console.log("Cleared conversation history");

        // Delete conversation history file (legacy cleanup)
        const historyFile = join(copilotDir, "conversation-history.json");
        if (existsSync(historyFile)) {
            unlinkSync(historyFile);
            console.log("Deleted conversation history file");
        }

        // Also clean up old conversation history files from previous locations
        const oldHistoryFile = join(homedir(), ".conversation-history.json");
        const localHistoryFile = join(process.cwd(), ".conversation-history.json");
        if (existsSync(oldHistoryFile)) {
            unlinkSync(oldHistoryFile);
            console.log("Deleted old conversation history file from home directory");
        }
        if (existsSync(localHistoryFile)) {
            unlinkSync(localHistoryFile);
            console.log("Deleted old conversation history file from working directory");
        }

        // Delete log file
        const logFile = join(copilotDir, "cli.log");
        if (existsSync(logFile)) {
            unlinkSync(logFile);
            console.log("Deleted log file");
        }

        // Delete local config file that might contain session data
        const configFile = join(process.cwd(), "cpd.local.settings.yaml");
        if (existsSync(configFile)) {
            unlinkSync(configFile);
            console.log("Deleted local config file");
        }

        // Delete AI console session files
        const aiConsoleDir = join(homedir(), ".ai-console");
        if (existsSync(aiConsoleDir)) {
            rmSync(aiConsoleDir, { recursive: true, force: true });
            console.log("Deleted AI console session directory");
        }

        // Delete any other potential session/conversation files
        const potentialFiles = [
            join(homedir(), ".claude"),
            join(homedir(), ".claude-session"),
            join(homedir(), ".anthropic"),
            join(homedir(), ".copilot-session"),
            join(homedir(), ".copilot-conversation"),
            join(process.cwd(), ".claude.md"),
            join(process.cwd(), "CLAUDE.md"),
        ];

        for (const file of potentialFiles) {
            try {
                if (existsSync(file)) {
                    rmSync(file, { recursive: true, force: true });
                    console.log(`Deleted ${file}`);
                }
            } catch (err) {
                console.log(`Could not delete ${file}:`, err);
            }
        }

        // Clear cached execute command
        setCachedExecuteCommand(null);

        // Clear session-related environment variables
        delete process.env.COPILOT_AGENT_JOB_ID;
        delete process.env.COPILOT_JOB_NONCE;
        delete process.env.GITHUB_RUN_ID;
        delete process.env.GITHUB_JOB;
        delete process.env.GITHUB_WORKFLOW;

        // Set flag to force fresh session
        setSessionCleared(true);

        return {
            handled: true,
            clearMessages: true,
            clearHistory: true,
            clearSession: true,
            addMessage: { type: "info", text: "Chat cleared." },
        };
    } catch (error) {
        console.error("Failed to delete history files:", error);
        return {
            handled: true,
            clearMessages: true,
            clearHistory: true,
            clearSession: true,
            addMessage: {
                type: "info",
                text: "Chat cleared (some files could not be deleted)",
            },
        };
    }
}

export function handleCwdCommand(args: string[], onDirectoryChange?: () => void): SlashCommandResult {
    if (args.length === 1) {
        // Show current working directory
        const currentDir = process.cwd();
        return {
            handled: true,
            addMessage: {
                type: "info",
                text: `Current working directory: ${currentDir}`,
            },
        };
    } else {
        // Change working directory
        const newDir = args.slice(1).join(" ");
        try {
            process.chdir(newDir);
            const currentDir = process.cwd();

            // Notify about directory change
            if (onDirectoryChange) {
                onDirectoryChange();
            }

            return {
                handled: true,
                addMessage: {
                    type: "info",
                    text: `Changed working directory to: ${currentDir}`,
                },
            };
        } catch (error: unknown) {
            return {
                handled: true,
                addMessage: {
                    type: "error",
                    text: `Failed to change directory: ${error instanceof Error ? error.message : String(error)}`,
                },
            };
        }
    }
}

export function handleLoginCommand(): SlashCommandResult {
    return {
        handled: true,
        showLoginUI: true,
    };
}

export function handleLogoutCommand(): SlashCommandResult {
    return {
        handled: true,
        logout: true,
    };
}

export function handleMcpCommand(args: string[]): SlashCommandResult {
    const subcommand = args[1] || "config";

    try {
        if (subcommand === "config") {
            return {
                handled: true,
                showMCPConfigUI: true,
            };
        } else if (subcommand === "show") {
            const config = loadMcpConfigIntoMemory();
            const serverNames = Object.keys(config.mcpServers);

            if (serverNames.length === 0) {
                return {
                    handled: true,
                    addMessage: {
                        type: "info",
                        text: "No MCP servers configured.",
                    },
                };
            } else {
                const output = [
                    "MCP Server Configuration:",
                    "",
                    ...serverNames.map((name) => {
                        const server = config.mcpServers[name];
                        const type = server.type || "local";
                        const details = type === "remote" ? `URL: ${server.url}` : `Command: ${server.command}`;
                        return `• ${name} (${type}): ${details}`;
                    }),
                    "",
                    `Total servers: ${serverNames.length}`,
                    `Config file: ${getMcpConfigPath()}`,
                ].join("\n");

                return {
                    handled: true,
                    addMessage: { type: "info", text: output },
                };
            }
        } else if (subcommand === "add") {
            if (args.length < 3) {
                return {
                    handled: true,
                    addMessage: {
                        type: "error",
                        text: "Usage: /mcp add <server-name>\nThis will start an interactive configuration wizard.",
                    },
                };
            }

            const serverName = args[2];
            globalAddMessage({
                type: "info",
                text: `Setting up MCP server "${serverName}"...`,
            });
            globalAddMessage({
                type: "info",
                text: "Opening interactive configuration wizard...",
            });

            return {
                handled: true,
                showMCPConfigUI: true,
            };
        } else if (subcommand === "edit") {
            if (args.length < 3) {
                return {
                    handled: true,
                    addMessage: {
                        text: "Usage: /mcp edit <server-name>\nThis will start an interactive configuration wizard.",
                        type: "error",
                    },
                };
            }

            const serverName = args[2];
            const config = loadMcpConfigIntoMemory();

            if (!config.mcpServers[serverName]) {
                return {
                    handled: true,
                    addMessage: {
                        type: "error",
                        text: `Server "${serverName}" not found. Use /mcp add to create it.`,
                    },
                };
            }

            globalAddMessage({
                type: "info",
                text: `Editing MCP server "${serverName}"...`,
            });
            globalAddMessage({
                type: "info",
                text: "Opening interactive configuration wizard...",
            });

            return {
                handled: true,
                showMCPConfigUI: true,
            };
        } else if (subcommand === "delete") {
            if (args.length < 3) {
                return {
                    handled: true,
                    addMessage: {
                        type: "error",
                        text: "Usage: /mcp delete <server-name>",
                    },
                };
            }

            const serverName = args[2];
            const config = loadMcpConfigIntoMemory();

            if (!config.mcpServers[serverName]) {
                return {
                    handled: true,
                    addMessage: {
                        type: "error",
                        text: `Server "${serverName}" not found.`,
                    },
                };
            }

            delete config.mcpServers[serverName];
            writeMcpConfig(config);

            return {
                handled: true,
                addMessage: {
                    type: "info",
                    text: `Successfully deleted MCP server "${serverName}" and updated in memory. Changes effective immediately.`,
                },
            };
        } else {
            const helpText = [
                "MCP Command Usage:",
                "/mcp (or /mcp config) - Open visual MCP server configuration interface",
                "/mcp show - Display all configured MCP servers",
                "/mcp add <server-name> - Add a new MCP server (interactive wizard)",
                "/mcp edit <server-name> - Edit an existing MCP server (interactive wizard)",
                "/mcp delete <server-name> - Delete an MCP server",
                "",
                "The add and edit commands will open an interactive wizard that guides you",
                "through configuring your MCP server with individual input fields.",
            ].join("\n");

            return {
                handled: true,
                addMessage: { type: "info", text: helpText },
            };
        }
    } catch (error: unknown) {
        return {
            handled: true,
            addMessage: {
                type: "error",
                text: `MCP command error: ${error instanceof Error ? error.message : String(error)}`,
            },
        };
    }
}

export function handleHistoryCommand(args: string[]): SlashCommandResult {
    try {
        const action = args[1]?.toLowerCase() || "show";

        if (action === "show") {
            const history = conversationHistoryManager.getDisplayHistory(10);

            if (history.length === 0) {
                return {
                    handled: true,
                    addMessage: {
                        type: "info",
                        text: "No conversation history found.",
                    },
                };
            }

            const historyText = history
                .map((entry, index) => {
                    const date = new Date(entry.timestamp).toLocaleString();
                    const userMsg =
                        entry.userMessage.length > 100
                            ? entry.userMessage.substring(0, 100) + "..."
                            : entry.userMessage;
                    const assistantMsg =
                        entry.assistantResponse.length > 100
                            ? entry.assistantResponse.substring(0, 100) + "..."
                            : entry.assistantResponse;

                    return [
                        `${index + 1}. ${date} (Session: ${entry.sessionId})`,
                        `   User: ${userMsg}`,
                        `   Assistant: ${assistantMsg}`,
                        "",
                    ].join("\n");
                })
                .join("\n");

            return {
                handled: true,
                addMessage: {
                    type: "info",
                    text: `Recent conversation history:\n\n${historyText}`,
                },
            };
        } else if (action === "clear") {
            conversationHistoryManager.clearHistory();
            return {
                handled: true,
                addMessage: {
                    type: "info",
                    text: "Conversation history cleared.",
                },
            };
        } else {
            return {
                handled: true,
                addMessage: {
                    type: "info",
                    text: "Usage: /history [show|clear]\n  show - Display recent conversation history\n  clear - Clear all conversation history",
                },
            };
        }
    } catch (error: unknown) {
        return {
            handled: true,
            addMessage: {
                type: "error",
                text: `History command error: ${error instanceof Error ? error.message : String(error)}`,
            },
        };
    }
}

function handleFeedbackCommand(): SlashCommandResult {
    return {
        handled: true,
        showFeedbackForm: true,
    };
}

// Main slash command handler
export function handleSlashCommand(
    value: string,
    addMessage: (message: MessageWithoutID) => void,
    setSessionCleared: (cleared: boolean) => void,
    setCachedExecuteCommand: (command: unknown) => void,
    onDirectoryChange?: () => void,
): SlashCommandResult {
    // Initialize global references
    initializeSlashCommands(addMessage);

    const trimmedValue = value.trim();

    // Handle help command
    if (trimmedValue === "?") {
        return handleHelpCommand();
    }

    // Handle /clear command
    if (trimmedValue === "/clear" || trimmedValue === "clear") {
        return handleClearCommand(setSessionCleared, setCachedExecuteCommand);
    }

    // Handle /cwd command
    if (trimmedValue.startsWith("/cwd")) {
        const args = trimmedValue.split(/\s+/);
        return handleCwdCommand(args, onDirectoryChange);
    }

    // Handle /login command
    if (trimmedValue === "/login") {
        return handleLoginCommand();
    }

    // Handle /logout command
    if (trimmedValue === "/logout") {
        return handleLogoutCommand();
    }

    // Handle /mcp command
    if (trimmedValue.startsWith("/mcp")) {
        const args = trimmedValue.split(/\s+/);
        return handleMcpCommand(args);
    }

    // Handle /history command
    if (trimmedValue.startsWith("/history")) {
        const args = trimmedValue.split(/\s+/);
        return handleHistoryCommand(args);
    }

    // Handle /feedback command
    if (trimmedValue.startsWith("/feedback")) {
        return handleFeedbackCommand();
    }

    // Not a slash command
    return { handled: false };
}
