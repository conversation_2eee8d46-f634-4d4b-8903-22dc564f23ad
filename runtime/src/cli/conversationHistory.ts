/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import { writeFileSync, readFileSync, existsSync, mkdirSync, statSync, unlinkSync } from "fs";
import { join } from "path";
import { homedir } from "os";
import type { ChatCompletionMessageParam } from "openai/resources/chat/completions";

export interface ConversationEntry {
    timestamp: string;
    sessionId: string;
    userMessage: string;
    assistantResponse: string;
    messages: ChatCompletionMessageParam[];
}

export interface ConversationHistory {
    entries: ConversationEntry[];
}

// Constants for conversation history management
const DEFAULT_MAX_CONVERSATIONS = 50; // Total conversations to store and use for context
const CONVERSATION_HISTORY_FILENAME = "conversation-history.json";
const HISTORY_FILE_PATH = join(homedir(), ".copilot", CONVERSATION_HISTORY_FILENAME);

export class ConversationHistoryManager {
    private cachedHistory: ConversationHistory | null = null;
    private lastFileModTime: number = 0;

    constructor() {
        const copilotDir = join(homedir(), ".copilot");

        // Ensure directory exists
        try {
            mkdirSync(copilotDir, { recursive: true });
        } catch (error: unknown) {
            if (error && typeof error === "object" && "code" in error && error.code !== "EEXIST") {
                console.error("Error creating directory:", error);
                throw error; // Rethrow unexpected errors
            }
        }
    }

    /**
     * Load conversation history from disk
     */
    private loadHistory(): ConversationHistory {
        try {
            if (existsSync(HISTORY_FILE_PATH)) {
                const stats = statSync(HISTORY_FILE_PATH);
                const currentModTime = stats.mtime.getTime();

                // Use cached history if file hasn't changed
                if (this.cachedHistory && this.lastFileModTime === currentModTime) {
                    return this.cachedHistory;
                }

                const content = readFileSync(HISTORY_FILE_PATH, "utf8");
                const data = JSON.parse(content);
                const history = {
                    entries: Array.isArray(data.entries) ? data.entries : [],
                };

                // Update cache
                this.cachedHistory = history;
                this.lastFileModTime = currentModTime;

                return history;
            }
        } catch (error) {
            console.error("Error loading conversation history:", error);
            // Clear cache on error
            this.cachedHistory = null;
            this.lastFileModTime = 0;
        }
        const emptyHistory = { entries: [] };
        this.cachedHistory = emptyHistory;
        this.lastFileModTime = 0;
        return emptyHistory;
    }

    /**
     * Save conversation history to disk
     */
    private saveHistory(history: ConversationHistory): void {
        try {
            writeFileSync(HISTORY_FILE_PATH, JSON.stringify(history, null, 2), "utf8");

            // Update cache
            this.cachedHistory = { entries: [...history.entries] };
            const stats = statSync(HISTORY_FILE_PATH);
            this.lastFileModTime = stats.mtime.getTime();
        } catch (error) {
            console.error("Error saving conversation history:", error);
            // Clear cache on error
            this.cachedHistory = null;
            this.lastFileModTime = 0;
        }
    }

    /**
     * Record a new conversation entry
     */
    recordConversation(
        sessionId: string,
        userMessage: string,
        assistantResponse: string,
        allMessages: ChatCompletionMessageParam[],
    ): void {
        const history = this.loadHistory();

        const entry: ConversationEntry = {
            timestamp: new Date().toISOString(),
            sessionId,
            userMessage,
            assistantResponse,
            messages: allMessages,
        };

        history.entries.push(entry);

        // Keep only the most recent entries
        if (history.entries.length > DEFAULT_MAX_CONVERSATIONS) {
            history.entries = history.entries.slice(-DEFAULT_MAX_CONVERSATIONS);
        }

        this.saveHistory(history);
    }

    /**
     * Get conversation history for LLM context
     * Returns recent conversations formatted for use as context
     */
    getHistoryForContext(maxEntries: number = DEFAULT_MAX_CONVERSATIONS): ChatCompletionMessageParam[] {
        const history = this.loadHistory();
        const recentEntries = history.entries.slice(-maxEntries);

        const contextMessages: ChatCompletionMessageParam[] = [];

        for (const entry of recentEntries) {
            // Add each conversation as user/assistant message pairs
            contextMessages.push({
                role: "user",
                content: entry.userMessage,
            });

            contextMessages.push({
                role: "assistant",
                content: entry.assistantResponse,
            });
        }

        return contextMessages;
    }

    /**
     * Get conversation history for display/debugging
     */
    getDisplayHistory(maxEntries: number = DEFAULT_MAX_CONVERSATIONS): ConversationEntry[] {
        const history = this.loadHistory();
        return history.entries.slice(-maxEntries);
    }

    /**
     * Clear all conversation history
     */
    clearHistory(): void {
        const emptyHistory: ConversationHistory = { entries: [] };

        // Clear file first
        try {
            if (existsSync(HISTORY_FILE_PATH)) {
                unlinkSync(HISTORY_FILE_PATH);
            }
        } catch (error) {
            // If we can't delete, try to overwrite
            console.warn("Could not delete history file, overwriting instead:", error);
        }

        // Write empty history
        this.saveHistory(emptyHistory);

        // Force clear cache
        this.cachedHistory = emptyHistory;
        this.lastFileModTime = 0;
    }

    /**
     * Get conversation history for a specific session
     */
    getSessionHistory(sessionId: string): ConversationEntry[] {
        const history = this.loadHistory();
        return history.entries.filter((entry) => entry.sessionId === sessionId);
    }

    /**
     * Backup current conversation history
     * Returns the backup data as a string, or null if no history exists
     */
    backupHistory(): string | null {
        try {
            if (existsSync(HISTORY_FILE_PATH)) {
                return readFileSync(HISTORY_FILE_PATH, "utf8");
            }
        } catch (error) {
            console.error("Error backing up conversation history:", error);
        }
        return null;
    }

    /**
     * Restore conversation history from backup data
     * If backupData is null, clears the history file
     */
    restoreHistory(backupData: string | null): void {
        try {
            if (backupData === null) {
                // Clear history if no backup data
                this.clearHistory();
            } else {
                // Validate the backup data is valid JSON
                const parsed = JSON.parse(backupData);
                writeFileSync(HISTORY_FILE_PATH, backupData, "utf8");

                // Update cache
                this.cachedHistory = {
                    entries: Array.isArray(parsed.entries) ? parsed.entries : [],
                };
                const stats = statSync(HISTORY_FILE_PATH);
                this.lastFileModTime = stats.mtime.getTime();
            }
        } catch (error) {
            console.error("Error restoring conversation history:", error);
            // Clear cache on error
            this.cachedHistory = null;
            this.lastFileModTime = 0;
            throw error;
        }
    }
}

// Global instance for use throughout the application
export const conversationHistoryManager = new ConversationHistoryManager();
