{"name": "@github/copilot", "version": "0.0.1", "type": "module", "repository": {"type": "git", "url": "git+https://github.com/github/sweagentd.git"}, "bugs": {"url": "https://github.com/github/sweagentd/issues"}, "homepage": "https://github.com/github/sweagentd/#readme", "author": "GitHub", "bin": {"copilot": "index.js"}, "publishConfig": {"registry": "https://npm.pkg.github.com"}, "engines": {"node": ">=22"}, "files": ["index.js"]}