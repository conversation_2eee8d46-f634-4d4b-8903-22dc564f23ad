/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { spawn } from "node:child_process";

type SpawnResult = {
    code: number;
    signal: NodeJS.Signals | null;
    stdout: string;
    stderr: string;
};

export class SpawnError extends Error {
    code: number | null;
    signal: NodeJS.Signals | null;
    stdout: string;
    stderr: string;

    constructor(message: string, code: number | null, signal: NodeJS.Signals | null, stdout: string, stderr: string) {
        super(message);
        this.code = code;
        this.signal = signal;
        this.stdout = stdout;
        this.stderr = stderr;
        Object.setPrototypeOf(this, SpawnError.prototype);
    }
}

export function spawnAsync(command: string, args: ReadonlyArray<string>, options = {}) {
    return new Promise<SpawnResult>((resolve, reject) => {
        const child = spawn(command, args, options);
        let stdout = "";
        let stderr = "";

        child.stdout.on("data", (data) => {
            stdout += data.toString();
        });

        child.stderr.on("data", (data) => {
            stderr += data.toString();
        });

        child.on("error", reject);

        child.on("exit", (code, signal) => {
            if (code === 0) {
                resolve({ code, signal, stdout, stderr });
            } else {
                const err = new SpawnError(
                    `Process exited with code ${code}${signal ? ` (signal: ${signal})` : ""}`,
                    code,
                    signal,
                    stdout,
                    stderr,
                );
                reject(err);
            }
        });
    });
}
