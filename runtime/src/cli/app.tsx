/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import type { ChildProcess } from "child_process";
import { spawn } from "child_process";
import { existsSync, mkdirSync, readFileSync, writeFileSync } from "fs";
import { Box, Text, useInput } from "ink";
import { homedir } from "os";
import { dirname, join } from "path";
import * as process from "process";
import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useTextCursorLocation } from "./hooks/useTextCursorLocation";
import { cliSystemMessage } from "../agents/prompts";
import { deepJsonParse } from "../helpers/json-helpers";
import { findGitRoot } from "../helpers/path-helpers";
import { isAssistantMessageEvent, isAssistantMessageWithToolCalls } from "../model/event";
import { ToolConfirmationResult, ToolIntention } from "../tools";
import { isNonEmptyString, matchEvent } from "./chatHelpers";
import { getMatchingSlashCommands, handleSlashCommand } from "./commands/slashCommands";
import { submitFeedback } from "./commands/submitFeedback";
import { FeedbackForm } from "./components/feedbackForm";
import InputArea from "./components/inputArea";
import { MCPConfigUI } from "./components/mcpConfigUI";
import MessagesDisplay from "./components/messagesDisplay";
import { ToolConfirmation } from "./components/toolConfirmation";
import { conversationHistoryManager } from "./conversationHistory";
import { LoginUI, writeTokenToConfig } from "./components/loginUI";
import { AppHeader } from "./components/appHeader";
import { Message, MessageWithoutID, ToolCallRequestedMessage } from "./types";
import SlashCommandPicker from "./components/slashCommandPicker";
import { BasicTruncator } from "../model/capi/truncateContextWindow";
import { Colors } from "./themes";

// Global message queue for agent messages
let globalMessageQueue: Message[] = [];
const messageListeners: ((messages: Message[]) => void)[] = [];
let currentSessionId: string | null = null;

// Global flag to track if we're in prompt mode
let globalPromptMode = false;

// Global MCP configuration cache
let globalMcpConfig: { mcpServers: { [serverName: string]: unknown } } | null = null;

// Global MCP registry server process
let mcpRegistryProcess: ChildProcess | null = null;

function setGlobalPromptMode(mode: boolean) {
    globalPromptMode = mode;
}

function addMessage(message: MessageWithoutID) {
    try {
        // In prompt mode, output directly to stdout instead of UI
        if (globalPromptMode) {
            if (message.type === "tool_call_requested") {
                // In prompt mode, we don't handle tool calls interactively
                return;
            }

            if (message.type === "tool_call_completed") {
                // This shouldn't really be a possible state right now. It could happen in the future
                // with --disallow-tools or something.
                if (message.result.type === "rejected") {
                    return;
                }

                process.stdout.write(message.result.log + "\n");
                return;
            }

            process.stdout.write(message.text + "\n");
            return;
        }

        globalMessageQueue.push({
            id: Date.now() + Math.random(),
            ...message,
        });
        // Keep only last 50 messages to prevent memory issues
        if (globalMessageQueue.length > 50) {
            globalMessageQueue = globalMessageQueue.slice(-50);
        }
        // Safely notify listeners
        messageListeners.forEach((listener, index) => {
            try {
                listener([...globalMessageQueue]);
            } catch (listenerError) {
                console.error("Error in message listener", index, ":", listenerError);
            }
        });
    } catch (error) {
        console.error("Error adding agent message:", error);
    }
}

function clearMessages() {
    try {
        globalMessageQueue = [];
        messageListeners.forEach((listener) => {
            try {
                listener([]);
            } catch (listenerError) {
                console.error("Error in message listener during clear:", listenerError);
            }
        });
    } catch (error) {
        console.error("Error clearing agent messages:", error);
    }
}

function startNewSession() {
    currentSessionId = Date.now().toString();
    // Don't clear agent messages - let them persist across queries
    return currentSessionId;
}

// Cache for the CLI class to avoid reimporting
let cachedCLI: typeof import("../../src/cli").CLI | null = null;

type SetToolConfirmationRequest = (request: {
    intention: ToolIntention;
    resolve: (value: ToolConfirmationResult) => void;
}) => void;

type CompletePendingToolCall = (result: {
    callId: string;
    resultType: "success" | "failure" | "rejected";
    log: string;
}) => void;

// Global execution lock to prevent concurrent runs
let isExecuting = false;
const executionQueue: Array<{
    messageId: number;
    problemStatement: string;
    resolve: (result: string) => void;
    reject: (error: unknown) => void;
    setToolConfirmationRequest: SetToolConfirmationRequest;
    completePendingToolCall: CompletePendingToolCall;
}> = [];

// Global abort controller for cancelling in-flight requests
let globalAbortController: AbortController | null = null;
let isCancelling = false;

// Flag to track if we need to force a fresh session
let sessionCleared = false;

async function processQueue() {
    if (isExecuting || executionQueue.length === 0) {
        return;
    }

    isExecuting = true;

    const { messageId, problemStatement, resolve, reject, setToolConfirmationRequest, completePendingToolCall } =
        executionQueue.shift()!;

    try {
        const result = await executeActualCommand(
            messageId,
            problemStatement,
            setToolConfirmationRequest,
            completePendingToolCall,
        );
        resolve(result);
    } catch (error: unknown) {
        // Handle cancellation errors gracefully in the queue
        if (error && typeof error === "object" && "name" in error && error.name === "AbortError") {
            resolve("Operation cancelled");
        } else if (
            error &&
            typeof error === "object" &&
            "message" in error &&
            typeof error.message === "string" &&
            error.message.includes("Operation was cancelled")
        ) {
            resolve("Operation cancelled");
        } else {
            reject(error);
        }
    } finally {
        isExecuting = false;
        // Process next item in queue if any
        if (executionQueue.length > 0) {
            setTimeout(() => void processQueue(), 100);
        }
    }
}

async function executeActualCommand(
    _messageId: number,
    problemStatement: string,
    setToolConfirmationRequest: SetToolConfirmationRequest,
    completePendingToolCall: CompletePendingToolCall,
): Promise<string> {
    // Create a new abort controller for this execution
    globalAbortController = new AbortController();

    try {
        // Read configuration from ~/.copilot/config
        const copilotDir = join(homedir(), ".copilot");
        const configPath = join(copilotDir, "config");

        // Ensure ~/.copilot directory exists
        try {
            mkdirSync(copilotDir, { recursive: true });
        } catch {
            // Ignore error if directory already exists
        }
        let config: Record<string, string> = {};

        try {
            if (existsSync(configPath)) {
                const configContent = readFileSync(configPath, "utf8");
                // Parse as JSON or key=value format
                if (configContent.trim().startsWith("{")) {
                    config = JSON.parse(configContent);
                } else {
                    // Parse key=value format
                    configContent.split("\n").forEach((line) => {
                        const [key, value] = line.split("=").map((s) => s.trim());
                        if (key && value) {
                            config[key] = value;
                        }
                    });
                }
            }
        } catch (error) {
            console.error("Config error:", error);
        }

        // Set CLI mode to skip default MCP servers
        process.env.GITHUB_COPILOT_CLI_MODE = "true";

        // Read MCP configuration (use cached version if available)
        let mcpConfig: {
            mcpServers: { [serverName: string]: unknown };
        } | null = null;
        try {
            const config = loadMcpConfigIntoMemory();
            if (Object.keys(config.mcpServers).length > 0) {
                mcpConfig = config;
                // Enable MCP transport when servers are configured
                process.env.COPILOT_MCP_ENABLED = "true";
                // Set MCP configuration environment variables for the registry
                process.env.GITHUB_COPILOT_MCP_JSON = JSON.stringify(config);
                process.env.GITHUB_COPILOT_3P_MCP_ENABLED = "true";

                // Start MCP registry server if not already running
                await startMcpRegistryServer();
            }
        } catch (error) {
            console.error("MCP config error:", error);
        }

        // Build options for the CLI
        const options = {
            config: "cpd.local.settings.yaml",
            problemStatement: problemStatement,
            log: join(copilotDir, "cli.log"),
            saveTrajectoryOutput: undefined, // Explicitly disable trajectory creation for CLI
            apiCopilotIntegrationId:
                config.COPILOT_INTEGRATION_ID ||
                config.copilot_integration_id ||
                config.GITHUB_COPILOT_INTEGRATION_ID ||
                process.env.GITHUB_COPILOT_INTEGRATION_ID ||
                "copilot-developer-cli",
            apiCopilotHmacKey:
                config.COPILOT_HMAC_KEY || config.copilot_hmac_key || config.CAPI_HMAC_KEY || process.env.CAPI_HMAC_KEY,
            apiCopilotUrl: config.COPILOT_URL || config.copilot_url,
            apiCopilotToken: config.COPILOT_TOKEN || config.copilot_token,
            // Force a new session ID for each execution (especially after clear)
            serviceInstanceId: currentSessionId || `cli-${Date.now()}-${Math.random().toString(36).substring(7)}`,
            // Pass MCP config directly if loaded
            mcpConfig: mcpConfig ? JSON.stringify(mcpConfig) : undefined,
        };

        // Remove undefined values
        const optionsRecord = options as Record<string, unknown>;
        Object.keys(options).forEach((key) => {
            if (optionsRecord[key] === undefined) {
                delete optionsRecord[key];
            }
        });

        // Use cached import or import once (force reimport if session was cleared)
        if (!cachedCLI || sessionCleared) {
            try {
                const cliModule = await import("../../src/cli");
                cachedCLI = cliModule.CLI;
                sessionCleared = false; // Reset the flag
            } catch (importError) {
                console.error("Failed to import CLI module:", importError);
                throw importError;
            }
        }

        let userInterruptedPreviousToolCall = false;

        // Pass the message handler to the CLI and disable session logging
        const optionsWithMessageHandler = {
            ...options,
            onToolCallRequest: (intention: ToolIntention): Promise<ToolConfirmationResult> => {
                // If the user interrupted a previous tool call, we should not ask again
                if (userInterruptedPreviousToolCall) {
                    return Promise.resolve("previously-interrupted");
                }

                return new Promise((resolve) => {
                    // Call the provided setToolConfirmationRequest to handle the confirmation
                    setToolConfirmationRequest({
                        intention,
                        resolve: (value: ToolConfirmationResult) => {
                            // Set the userInterruptedPreviousToolCall flag based on the result
                            userInterruptedPreviousToolCall = value === "rejected";
                            resolve(value);
                        },
                    });
                });
            },
            showEvents: true, // Enable console event logging for CLI
            showSessionLog: false, // Disable session log display to avoid duplication
            useSessionLogOnly: false, // Use onMessage handler instead
            abortSignal: globalAbortController.signal, // Pass abort signal for cancellation
            suppressTrajectoryEvents: true, // Suppress XML function calls in CLI
        };

        try {
            // Create CLI instance
            const cli = await cachedCLI.createFromOptions(optionsWithMessageHandler);

            // Use the completion method instead of execute
            const gitResult = findGitRoot(process.cwd());
            const agentSystemMessage = await cliSystemMessage(gitResult.found ? gitResult.gitRoot : "");

            // Get conversation history for context (uses default max)
            const historyMessages = conversationHistoryManager.getHistoryForContext();

            // Create initial messages including history
            const initialUserMessage = {
                role: "user" as const,
                content: problemStatement,
            };

            const allInitialMessages = [...historyMessages, initialUserMessage];

            const stream = cli.getCompletionWithTools(agentSystemMessage, allInitialMessages, {
                failIfInitialInputsTooLong: false,
                processors: { preRequest: [new BasicTruncator()] },
            });

            // Process the stream and handle events
            let assistantResponse = "";
            const messages = [...allInitialMessages];

            for await (const event of stream) {
                // Check for cancellation during stream processing
                if (globalAbortController?.signal.aborted) {
                    const error = new Error("Operation was cancelled");
                    error.name = "AbortError";
                    throw error;
                }

                matchEvent(event, {
                    onImageProcessingEvent: (_e) => {
                        // No design thought has been put into image processing events yet.
                    },
                    onImageRemovalEvent: (_e) => {
                        // No design thought has been put into image removal events yet.
                    },
                    onMessage: (e) => {
                        messages.push(e.message);

                        if (isAssistantMessageEvent(e) && e.message.content) {
                            if (typeof e.message.content === "string") {
                                assistantResponse += e.message.content;

                                addMessage({
                                    type: "copilot",
                                    text: e.message.content,
                                });
                                return;
                            }

                            // Just not sure under what circumstances this would occur.
                            console.warn("Unexpected message content type:", JSON.stringify(e.message.content));
                        }

                        if (isAssistantMessageWithToolCalls(e)) {
                            for (const toolCall of e.message.tool_calls) {
                                const intentionSummary = cli.getToolIntentionSummary(
                                    toolCall.function.name,
                                    deepJsonParse(toolCall.function.arguments),
                                );
                                addMessage({
                                    type: "tool_call_requested",
                                    callId: toolCall.id,
                                    name: toolCall.function.name,
                                    intentionSummary: intentionSummary,
                                });
                            }
                        }
                    },
                    onResponse: (e) => {
                        if (isNonEmptyString(e.response.content)) {
                            assistantResponse += e.response.content;
                            addMessage({
                                type: "copilot",
                                text: e.response.content,
                            });
                        }
                    },
                    onSessionLog: (_e) => {
                        // Ignore session logs in CLI mode
                    },
                    onModelCallFailure: (_e) => {
                        // No design thought has been put into model call failures yet.
                    },
                    onModelCallSuccess: (_e) => {
                        // No design thought has been put into model call successes yet.
                    },
                    onToolExecution: (e) => {
                        completePendingToolCall({
                            callId: e.toolCallId,
                            resultType: e.toolResult.resultType,
                            log: e.toolResult.sessionLog || e.toolResult.textResultForLlm,
                        });
                    },
                    onTruncationEvent: (_e) => {
                        // No design thought has been put into truncation events yet.
                    },
                    onTurnEvent: (_e) => {
                        // No design thought has been put into turn events yet.
                    },
                });
            }

            // Shutdown the CLI instance
            await cli.shutdown();

            // Record conversation history
            try {
                conversationHistoryManager.recordConversation(
                    currentSessionId || "unknown",
                    problemStatement,
                    assistantResponse,
                    messages,
                );
            } catch (historyError) {
                console.error("Error recording conversation history:", historyError);
            }

            return "Command completed successfully";
        } catch (executeError: unknown) {
            // Don't log stack traces for normal cancellation
            const isAbortError =
                executeError &&
                typeof executeError === "object" &&
                "name" in executeError &&
                executeError.name === "AbortError";
            const isCancellationError =
                executeError &&
                typeof executeError === "object" &&
                "message" in executeError &&
                typeof executeError.message === "string" &&
                executeError.message.includes("Operation was cancelled");

            if (!isAbortError && !isCancellationError) {
                console.error("Command threw error:", executeError);
            }
            throw executeError;
        }
    } catch (error: unknown) {
        // Handle cancellation differently from other errors
        const isAbortError = error && typeof error === "object" && "name" in error && error.name === "AbortError";
        const isCancellationError =
            error &&
            typeof error === "object" &&
            "message" in error &&
            typeof error.message === "string" &&
            error.message.includes("Operation was cancelled");
        const isAborted = globalAbortController?.signal.aborted;

        if (isAbortError || isCancellationError || isAborted) {
            addMessage({
                type: "info",
                text: "Operation cancelled by user",
            });
            return "Operation cancelled";
        }

        const errorMessage =
            error instanceof Error
                ? error.message
                : error && typeof error === "object" && "toString" in error
                  ? String(error)
                  : "Unknown error occurred";
        addMessage({
            type: "error",
            text: `Execution failed: ${errorMessage}`,
        });
        throw error;
    } finally {
        // Clean up the abort controller
        globalAbortController = null;
        isCancelling = false;
    }
}

function executeInQueue(
    messageId: number,
    problemStatement: string,
    setToolConfirmationRequest: SetToolConfirmationRequest,
    completePendingToolCall: CompletePendingToolCall,
): Promise<string> {
    return new Promise((resolve, reject) => {
        executionQueue.push({
            messageId,
            problemStatement,
            resolve,
            reject,
            setToolConfirmationRequest,
            completePendingToolCall,
        });
        void processQueue();
    });
}

// Direct prompt execution function for CLI mode
export async function executePromptDirectly(prompt: string): Promise<void> {
    setGlobalPromptMode(true);

    try {
        startNewSession();
        const messageId = Date.now();
        await executeInQueue(
            messageId,
            prompt,
            // When setToolConfirmationRequest is called, we should immediately resolve with "proceed-once"
            (request) => request.resolve("proceed-once"),
            // This is really starting to stretch at the seams on this execution queue approach, and having callbacks that
            // reach out to react state wired through it. We need to log here directly because updating the pending tool
            // call to completed edits the globlalMessageQueue without going through addMessage (which is where the logging happens).
            // Think we should revisit the boundaries between interactive and non-interactive modes.
            (result) => {
                process.stdout.write(result.log + "\n");
            },
        );
        process.exit(0);
    } catch (error: unknown) {
        console.error("Error executing prompt:", error);
        process.exit(1);
    }
}

// MCP Configuration utilities - moved to slashCommands.ts
function readMcpConfig(): { mcpServers: { [serverName: string]: unknown } } {
    const copilotDir = join(homedir(), ".copilot");
    const mcpConfigPath = join(copilotDir, "mcpconfig");
    try {
        if (existsSync(mcpConfigPath)) {
            const configContent = readFileSync(mcpConfigPath, "utf8");
            return JSON.parse(configContent);
        }
    } catch (error) {
        console.error("Error reading MCP config:", error);
    }
    return { mcpServers: {} };
}

// Command history utilities - moved to slashCommands.ts
function getHistoryFilePath(): string {
    const copilotDir = join(homedir(), ".copilot");
    return join(copilotDir, "command-history.json");
}

function loadCommandHistory(): string[] {
    const historyPath = getHistoryFilePath();
    try {
        if (existsSync(historyPath)) {
            const historyContent = readFileSync(historyPath, "utf8");
            const history = JSON.parse(historyContent);
            return Array.isArray(history) ? history : [];
        }
    } catch (error) {
        console.error("Error loading command history:", error);
    }
    return [];
}

function saveCommandHistory(history: string[]): void {
    const historyPath = getHistoryFilePath();
    const copilotDir = join(homedir(), ".copilot");

    try {
        mkdirSync(copilotDir, { recursive: true });
        writeFileSync(historyPath, JSON.stringify(history, null, 2), "utf8");
    } catch (error) {
        console.error("Error saving command history:", error);
    }
}

function loadMcpConfigIntoMemory(): {
    mcpServers: { [serverName: string]: unknown };
} {
    if (globalMcpConfig === null) {
        globalMcpConfig = readMcpConfig();
    }
    return globalMcpConfig;
}

async function startMcpRegistryServer(): Promise<void> {
    // Don't start if already running
    if (mcpRegistryProcess) {
        return;
    }

    try {
        // Start the MCP registry server as a background process
        // Try to resolve the mcp-client package location dynamically
        let builtMcpPath: string;
        let sourceMcpPath: string;

        try {
            // Find the package directory by walking up from the current file location
            const currentFileUrl = import.meta.url;
            const currentFilePath = new URL(currentFileUrl).pathname;

            // Look for package.json by walking up the directory tree
            let searchDir = dirname(currentFilePath);
            let packageDir: string | null = null;

            for (let i = 0; i < 10; i++) {
                // Limit search depth to prevent infinite loops
                const packageJsonPath = join(searchDir, "package.json");
                if (existsSync(packageJsonPath)) {
                    // Verify this is the right package by checking for our specific files
                    const mcpClientBuilt = join(searchDir, "dist-mcp-client/index.js");
                    const mcpClientSource = join(searchDir, "src/mcp-client/index.ts");

                    if (existsSync(mcpClientBuilt) || existsSync(mcpClientSource)) {
                        packageDir = searchDir;
                        break;
                    }
                }
                const parentDir = dirname(searchDir);
                if (parentDir === searchDir) break; // Reached filesystem root
                searchDir = parentDir;
            }

            if (packageDir) {
                builtMcpPath = join(packageDir, "dist-mcp-client/index.js");
                sourceMcpPath = join(packageDir, "src/mcp-client/index.ts");
            } else {
                throw new Error("Could not find package directory with MCP client");
            }
        } catch (_error) {
            // Fallback to relative paths for development
            const currentDir = new URL(".", import.meta.url).pathname;
            // currentDir is like: /path/to/runtime/dist-cli/
            // We need to go up one level to get to runtime directory
            const runtimeDir = join(currentDir, "..");
            builtMcpPath = join(runtimeDir, "dist-mcp-client/index.js");
            sourceMcpPath = join(runtimeDir, "src/mcp-client/index.ts");
        }

        let mcpRegistryPath: string;
        let spawnCommand: string;
        let spawnArgs: string[];

        if (existsSync(builtMcpPath)) {
            mcpRegistryPath = builtMcpPath;
            spawnCommand = "node";
            spawnArgs = [mcpRegistryPath];
        } else {
            mcpRegistryPath = sourceMcpPath;
            spawnCommand = "npx";
            spawnArgs = ["tsx", mcpRegistryPath];
        }

        mcpRegistryProcess = spawn(spawnCommand, spawnArgs, {
            stdio: ["pipe", "pipe", "pipe"],
            env: {
                ...process.env,
                // Ensure the environment variables are set
                GITHUB_COPILOT_MCP_JSON: process.env.GITHUB_COPILOT_MCP_JSON,
                GITHUB_COPILOT_3P_MCP_ENABLED: process.env.GITHUB_COPILOT_3P_MCP_ENABLED,
            },
        });

        // Handle process events
        mcpRegistryProcess.on("error", (error: Error) => {
            console.error("MCP registry server error:", error);
            mcpRegistryProcess = null;
        });

        mcpRegistryProcess.on("exit", () => {
            mcpRegistryProcess = null;
        });

        mcpRegistryProcess.stderr?.on("data", (data: Buffer) => {
            console.error("MCP registry server stderr:", data.toString().trim());
        });

        // Give the server a moment to start up
        await new Promise((resolve) => setTimeout(resolve, 1000));
    } catch (error) {
        console.error("Failed to start MCP registry server:", error);
        mcpRegistryProcess = null;
    }
}

function stopMcpRegistryServer(): void {
    if (mcpRegistryProcess) {
        mcpRegistryProcess.kill("SIGTERM");
        mcpRegistryProcess = null;
    }
}

// Export the message handler for use by the callback
export { addMessage, clearMessages, setGlobalPromptMode, startNewSession };

enum LoginStatus {
    Unknown,
    LoggedIn,
    NotLoggedIn,
}

const App: React.FC = () => {
    const [messages, setMessages] = useState<Message[]>([]);
    const [expandMessages, setExpandMessages] = useState(false);
    const [isRuntimeRunning, setIsRuntimeRunning] = useState(false);
    const [commandHistory, setCommandHistory] = useState<string[]>([]);
    const [historyIndex, setHistoryIndex] = useState(-1);
    const [showLoginUI, setShowLoginUI] = useState(false);
    const [loginStatus, setLoginStatus] = useState(LoginStatus.Unknown);
    const [showMCPConfigUI, setShowMCPConfigUI] = useState(false);
    const [isSubmittingFeedback, setIsSubmittingFeedback] = useState(false);
    const [showFeedbackForm, setShowFeedbackForm] = useState(false);
    const [slashCommandPickerIndex, setSlashCommandPickerIndex] = useState(0);
    const [configChecked, setConfigChecked] = useState(false);
    const [currentWorkingDirectory, setCurrentWorkingDirectory] = useState(process.cwd());
    const [toolConfirmationRequest, setToolConfirmationRequest] = useState<null | {
        intention: ToolIntention;
        resolve: (result: ToolConfirmationResult) => void;
    }>(null);
    const isMountedRef = useRef(true);

    // Calculate git root based on current working directory
    const gitResult = useMemo(() => findGitRoot(currentWorkingDirectory), [currentWorkingDirectory]);

    const textCursorLocation = useTextCursorLocation();

    const completePendingToolCall = useCallback(
        (result: { callId: string; resultType: "success" | "failure" | "rejected"; log: string }) => {
            // Update the global message queue instead of React state directly
            // This ensures consistency between the two message systems
            const messageIndex = globalMessageQueue.findLastIndex(
                (msg) => msg.type === "tool_call_requested" && msg.callId === result.callId,
            );

            if (messageIndex === -1) {
                console.error(`Pending tool call with ID ${result.callId} not found`);
                // Maybe we should be setting some error state here, but we haven't decided on an error
                // design yet.
                return;
            }

            // Type cast the message. Since we checked this in the findLastIndex,
            // it should be safe to assume it's a tool call requested message, even if the compiler
            // doesn't know it.
            const msg = globalMessageQueue[messageIndex] as Message & ToolCallRequestedMessage;

            // Update the global message queue with the completed tool call
            // Ideally we would be setting the messages state directly here but if we don't set the global
            // message queue and notify the listener (this component), then on the next messages, the
            // changes will be wiped out by the contents of the global message queue. Sad! Probably worth
            // revisiting the global message queue.
            globalMessageQueue[messageIndex] = {
                type: "tool_call_completed",
                id: msg.id,
                callId: msg.callId,
                name: msg.name,
                intentionSummary: msg.intentionSummary,
                result: {
                    type: result.resultType,
                    log: result.log,
                },
            };

            // Notify all listeners (including the React state updater)
            messageListeners.forEach((listener, index) => {
                try {
                    listener([...globalMessageQueue]);
                } catch (listenerError) {
                    console.error("Error in message listener", index, ":", listenerError);
                }
            });
        },
        [], // No dependencies needed since we're using global variables
    );

    // Start a new conversation session on startup
    useEffect(() => {
        startNewSession();
    }, []);

    // Load command history on startup
    useEffect(() => {
        const loadedHistory = loadCommandHistory();
        setCommandHistory(loadedHistory);
    }, []);

    // Check config on startup
    useEffect(() => {
        const checkConfig = () => {
            try {
                const copilotDir = join(homedir(), ".copilot");
                const configPath = join(copilotDir, "config");
                let config: Record<string, string> = {};

                if (existsSync(configPath)) {
                    const configContent = readFileSync(configPath, "utf8");
                    if (configContent.trim().startsWith("{")) {
                        config = JSON.parse(configContent);
                    } else {
                        configContent.split("\n").forEach((line) => {
                            const [key, value] = line.split("=").map((s) => s.trim());
                            if (key && value) {
                                config[key] = value;
                            }
                        });
                    }
                }

                // Check if Copilot Token is missing
                const hasCopilotToken =
                    config.COPILOT_TOKEN || config.copilot_token || config.COPILOT_HMAC_KEY || config.copilot_hmac_key;

                if (!hasCopilotToken) {
                    setLoginStatus(LoginStatus.NotLoggedIn);
                } else {
                    setLoginStatus(LoginStatus.LoggedIn);
                }
            } catch (error) {
                console.error("Error checking config:", error);
            } finally {
                setConfigChecked(true);
            }
        };

        checkConfig();
    }, []);

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            isMountedRef.current = false;
            // Stop MCP registry server when component unmounts
            stopMcpRegistryServer();
        };
    }, []);

    // Subscribe to agent messages with debouncing
    useEffect(() => {
        let timeoutId: NodeJS.Timeout;

        const listener = (messages: Message[]) => {
            if (isMountedRef.current) {
                // Clear existing timeout
                clearTimeout(timeoutId);

                // Debounce updates to prevent rapid re-renders
                timeoutId = setTimeout(() => {
                    try {
                        setMessages(messages);
                    } catch (setError) {
                        console.error("Error in setMessages:", setError);
                    }
                }, 50); // 50ms debounce
            }
        };
        messageListeners.push(listener);

        return () => {
            clearTimeout(timeoutId);
            const index = messageListeners.indexOf(listener);
            if (index > -1) {
                messageListeners.splice(index, 1);
            }
        };
    }, []);

    // Check MCP servers after component is mounted and message listeners are set up
    useEffect(() => {
        if (configChecked) {
            try {
                const mcpConfig = loadMcpConfigIntoMemory();
                const serverNames = Object.keys(mcpConfig.mcpServers);
                if (serverNames.length > 0) {
                    const mcpConfigurationMessage = `Configured MCP servers: ${serverNames.join(", ")}`;
                    addMessage({
                        type: "info",
                        text: mcpConfigurationMessage,
                    });
                }
            } catch {
                // Ignore MCP config errors during startup
            }
        }
    }, [configChecked]);

    const slashCommandPickerOptions = useMemo(() => {
        setSlashCommandPickerIndex(0);
        return getMatchingSlashCommands(textCursorLocation.text);
    }, [textCursorLocation.text]);

    const userInputPlaceholder = useMemo(() => {
        // If there's a current slash command, use it as placeholder for autocompletion
        const currentSlashCommand = slashCommandPickerOptions[slashCommandPickerIndex];
        if (currentSlashCommand) {
            const completableText = currentSlashCommand.name + (currentSlashCommand.args ? " " : "");
            return completableText.substring(textCursorLocation.text.length);
        }

        return undefined;
    }, [textCursorLocation.text, slashCommandPickerIndex, slashCommandPickerOptions]);

    const handleSubmit = useCallback(
        async (value: string) => {
            if (!isRuntimeRunning && !isExecuting) {
                // Add command to history first (avoid duplicates and keep max 50 commands)
                // This ensures all commands including slash commands are added to history
                setCommandHistory((prev) => {
                    const newHistory = prev.filter((cmd) => cmd !== value);
                    newHistory.push(value);
                    const trimmedHistory = newHistory.slice(-50); // Keep only last 50 commands

                    // Save history to disk
                    saveCommandHistory(trimmedHistory);

                    return trimmedHistory;
                });

                // Handle slash commands using the new helper
                // TODO: turn this into a hook
                const slashResult = handleSlashCommand(
                    value,
                    addMessage,
                    (cleared: boolean) => {
                        sessionCleared = cleared;
                    },
                    (cli: unknown) => {
                        cachedCLI = cli as typeof import("../../src/cli").CLI;
                    },
                    () => {
                        // Update current working directory when /cwd command changes it
                        setCurrentWorkingDirectory(process.cwd());
                    },
                );

                if (slashResult.handled) {
                    // Handle side effects from slash commands
                    if (slashResult.addMessage) {
                        addMessage(slashResult.addMessage);
                    }

                    if (slashResult.clearMessages) {
                        clearMessages();
                        setMessages([]);
                    }

                    if (slashResult.clearHistory) {
                        setCommandHistory([]);
                        saveCommandHistory([]);
                    }

                    if (slashResult.showLoginUI) {
                        setShowLoginUI(true);
                    }

                    if (slashResult.logout) {
                        await writeTokenToConfig(); // Clear token
                        addMessage({
                            type: "info",
                            text: "You have been logged out successfully.",
                        });
                        setLoginStatus(LoginStatus.NotLoggedIn);
                    }

                    if (slashResult.showMCPConfigUI) {
                        setShowMCPConfigUI(true);
                    }

                    if (slashResult.showFeedbackForm) {
                        setShowFeedbackForm(true);
                    }

                    return;
                }

                addMessage({
                    type: "user",
                    text: value,
                });

                const messageId = Date.now();

                setIsRuntimeRunning(true);

                // If this is the first query after a clear, add a prefix to ensure fresh context
                let problemStatement = value;
                if (sessionCleared) {
                    problemStatement = `[NEW CONVERSATION - IGNORE ANY PREVIOUS CONTEXT] ${value}`;
                    sessionCleared = false; // Reset the flag
                }

                try {
                    await executeInQueue(
                        messageId,
                        problemStatement,
                        setToolConfirmationRequest,
                        completePendingToolCall,
                    );
                } catch (error: unknown) {
                    const errorMessage = error instanceof Error ? error.message : String(error);
                    addMessage({
                        type: "error",
                        text: `Execution failed: ${errorMessage}`,
                    });
                } finally {
                    if (isMountedRef.current) {
                        setIsRuntimeRunning(false);
                    }
                }
            }
        },
        [isRuntimeRunning, completePendingToolCall],
    );

    // Handle global keyboard input
    useInput((input, key) => {
        if (key.ctrl && input === "c") {
            // Clean up MCP registry server before exiting
            stopMcpRegistryServer();
            process.exit(0);
        }

        // Handle escape key to cancel in-flight operations or clear input
        if (key.escape) {
            if (isRuntimeRunning || isExecuting) {
                if (globalAbortController && !globalAbortController.signal.aborted) {
                    isCancelling = true;
                    globalAbortController.abort();
                    addMessage({
                        type: "info",
                        text: "Cancelling operation...",
                    });
                }
            } else {
                // Clear input box when not processing
                textCursorLocation.clear();
                setHistoryIndex(-1);
            }
            return;
        }

        if (key.ctrl && input === "r") {
            setExpandMessages((prev) => !prev);
        }

        // Only handle navigation keys when not running
        if (isRuntimeRunning || isExecuting) return;

        if (key.upArrow) {
            if (slashCommandPickerOptions.length) {
                setSlashCommandPickerIndex(Math.max(0, slashCommandPickerIndex - 1));
            } else if (commandHistory.length > 0) {
                const newIndex = historyIndex + 1;
                if (newIndex < commandHistory.length) {
                    setHistoryIndex(newIndex);
                    const historyCommand = commandHistory[commandHistory.length - 1 - newIndex];
                    textCursorLocation.setText(historyCommand);
                }
            }
        } else if (key.downArrow) {
            if (slashCommandPickerOptions.length) {
                setSlashCommandPickerIndex(Math.min(slashCommandPickerOptions.length - 1, slashCommandPickerIndex + 1));
            } else if (historyIndex > 0) {
                const newIndex = historyIndex - 1;
                setHistoryIndex(newIndex);
                const historyCommand = commandHistory[commandHistory.length - 1 - newIndex];
                textCursorLocation.setText(historyCommand);
            } else if (historyIndex === 0) {
                setHistoryIndex(-1);
                textCursorLocation.clear();
            }
        }
    });

    // Render MCP config UI if showing MCP config
    if (showMCPConfigUI) {
        return (
            <Box flexDirection="column" paddingX={1} height="100%">
                {/* Show existing messages above MCP config UI if any */}
                {messages.length > 0 && (
                    <Box flexDirection="column" marginBottom={1}>
                        <MessagesDisplay messages={messages} expand={expandMessages} />
                        <Text> </Text>
                    </Box>
                )}

                {/* MCP Config UI */}
                <MCPConfigUI
                    onClose={() => {
                        setShowMCPConfigUI(false);
                    }}
                    onConfigSaved={() => {
                        // Configuration is automatically reloaded
                        addMessage({
                            type: "info",
                            text: "MCP configuration saved successfully! Changes will take effect immediately.",
                        });
                    }}
                />
            </Box>
        );
    }

    // Render sign-in UI if showing sign-in
    if (showLoginUI) {
        return (
            <Box flexDirection="column" paddingX={1} height="100%">
                {/* Show existing messages above sign-in UI if any */}
                {messages.length > 0 && (
                    <Box flexDirection="column" marginBottom={1}>
                        <MessagesDisplay messages={messages} expand={expandMessages} />
                        <Text> </Text>
                    </Box>
                )}

                {/* Sign-in UI */}
                <LoginUI
                    onLoginSuccess={() => {
                        setShowLoginUI(false);
                        // Config is automatically reloaded on next command execution
                        addMessage({
                            type: "info",
                            text: "Signed in successfully! You can now use Copilot.",
                        });
                        setLoginStatus(LoginStatus.LoggedIn);
                        setConfigChecked(true); // Mark config as checked after signing in
                    }}
                    onLoginClose={() => {
                        setShowLoginUI(false);
                    }}
                />
            </Box>
        );
    }

    if (showFeedbackForm) {
        const onSubmitFeedback = async (title: string, description: string) => {
            setIsSubmittingFeedback(true);
            try {
                const feedbackURL = await submitFeedback(title, description, currentSessionId, messages);
                addMessage({
                    type: "info",
                    text: `Feedback submitted successfully! You can view it here: ${feedbackURL}`,
                });
            } catch (error) {
                console.error("Failed to submit feedback:", error);
                addMessage({
                    type: "error",
                    text: `Failed to submit feedback. Please try again later. Error: ${
                        error instanceof Error ? error.message : String(error)
                    }`,
                });
            } finally {
                setIsSubmittingFeedback(false);
                setShowFeedbackForm(false);
            }
        };

        return (
            <FeedbackForm
                // eslint-disable-next-line @typescript-eslint/no-misused-promises
                onSubmit={onSubmitFeedback}
                onCancel={() => setShowFeedbackForm(false)}
                isSubmitting={isSubmittingFeedback}
            />
        );
    }

    return (
        <Box flexDirection="column" paddingX={1} height="100%">
            {/* Header - always visible */}
            <AppHeader currentWorkingDirectory={currentWorkingDirectory} gitResult={gitResult} />

            {/* Messages area - scrollable content */}
            <Box flexDirection="column" flexGrow={1}>
                <MessagesDisplay messages={messages} expand={expandMessages} />
            </Box>

            {loginStatus === LoginStatus.NotLoggedIn && (
                <Text bold color={Colors.COPILOT}>
                    Please use /login to sign in to use Copilot
                </Text>
            )}

            {/* Input area - always at bottom */}
            {toolConfirmationRequest ? (
                <ToolConfirmation
                    intention={toolConfirmationRequest.intention}
                    onConfirm={(result) => {
                        toolConfirmationRequest.resolve(result);
                        setToolConfirmationRequest(null); // Reset tool confirmation state
                    }}
                />
            ) : (
                <>
                    <InputArea
                        // eslint-disable-next-line @typescript-eslint/no-misused-promises
                        onSubmit={handleSubmit}
                        isDisabled={isRuntimeRunning || isExecuting}
                        commandHistory={commandHistory}
                        textCursorLocation={textCursorLocation}
                        historyIndex={historyIndex}
                        setHistoryIndex={setHistoryIndex}
                        isThinking={isRuntimeRunning || isExecuting}
                        isCancelling={isCancelling}
                        userInputPlaceholder={userInputPlaceholder}
                    />

                    {slashCommandPickerOptions.length > 0 && (
                        <SlashCommandPicker
                            options={slashCommandPickerOptions}
                            selectedIndex={slashCommandPickerIndex}
                            maxRows={5}
                        />
                    )}
                </>
            )}
        </Box>
    );
};

export default App;
