/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import type { ChatCompletionMessageParam } from "openai/resources/chat/completions";
import { fixToolsForCLI } from "./agents/config";
import { getClient } from "./agents/sweagent";
import { readFromFileIfNeeded } from "./helpers/file-reader";
import { Client, GetCompletionWithToolsOptions } from "./model/client";
import { Event } from "./model/event";
import { splitAgentModelSetting } from "./model/util";
import { Log<PERSON><PERSON>l, RunnerLogger } from "./runner";
import { FileLogger } from "./runner/logger/file";
import { RuntimeSettings } from "./settings";
import { clearCurrentSettings, hasCurrentSettings } from "./settings/current";
import { initSettings } from "./settings/factory";
import { SettingsBuilder } from "./settings/settings-builder";
import { Tool, ToolConfig, ToolConfirmationCallback } from "./tools";

export interface CLIExecuteOptions {
    config?: string;
    // Problem and Service
    problemStatement?: string;
    serviceInstanceId?: string;
    serviceAgentModel?: string;
    serviceCallbackUrl?: string;
    // API Keys
    apiAipSweAgentToken?: string;
    apiAnthropicKey?: string;
    apiCopilotUrl?: string;
    apiCopilotIntegrationId?: string;
    apiCopilotHmacKey?: string;
    apiCopilotToken?: string;
    // Miscellaneous
    verbose?: boolean;
    log?: string;
    saveTrajectoryOutput?: string;
    disableOnlineEvaluation?: boolean;
    showSessionLog?: boolean;
    mcpConfig?: string;
    onToolCallRequest: ToolConfirmationCallback;
    // CLI console output control
    showEvents?: boolean;
    useSessionLogOnly?: boolean;
    logSession?: (msg: string) => Promise<void>;
    // Cancellation support
    abortSignal?: AbortSignal;
}

export class CLI {
    private client?: Client;
    private settings?: RuntimeSettings;
    private logger?: RunnerLogger;
    private toolConfig?: ToolConfig;
    private availableTools: Tool[] = [];

    constructor() {
        // Empty constructor - will be configured via createFromOptions
    }

    static async createFromOptions(options: CLIExecuteOptions): Promise<CLI> {
        const cli = new CLI();
        await cli.initializeFromOptions(options);
        return cli;
    }

    private async initializeFromOptions(options: CLIExecuteOptions): Promise<void> {
        // Set up logging
        const loggingToFile = options.log !== undefined;
        const logToLevel = options.verbose ? LogLevel.All : loggingToFile ? LogLevel.None : LogLevel.Error;
        this.logger = new FileLogger(options.log ?? "", logToLevel);

        // Build settings
        const builder = new SettingsBuilder();
        const overrides = builder
            .setProblemStatement(readFromFileIfNeeded(options.problemStatement))
            .setInstanceId(readFromFileIfNeeded(options.serviceInstanceId))
            .setAgentModel(readFromFileIfNeeded(options.serviceAgentModel))
            .setCallbackUrl(readFromFileIfNeeded(options.serviceCallbackUrl))
            .setAipSweAgentToken(readFromFileIfNeeded(options.apiAipSweAgentToken))
            .setAnthropicApiKey(readFromFileIfNeeded(options.apiAnthropicKey))
            .setCopilotUrl(readFromFileIfNeeded(options.apiCopilotUrl))
            .setCopilotIntegrationId(readFromFileIfNeeded(options.apiCopilotIntegrationId))
            .setCopilotHmacKey(readFromFileIfNeeded(options.apiCopilotHmacKey))
            .setCopilotToken(readFromFileIfNeeded(options.apiCopilotToken))
            .setTrajectoryOutputFile(readFromFileIfNeeded(options.saveTrajectoryOutput))
            .setDisableOnlineEvaluation(options.disableOnlineEvaluation)
            .setGithubRepoName("temp-repo")
            .setGithubRepoCommit("temp-commit")
            .setGithubRepoReadWrite(false)
            .build();

        // Initialize or reuse settings
        if (hasCurrentSettings()) {
            clearCurrentSettings();
            this.settings = await initSettings(overrides);
        } else {
            this.settings = await initSettings(overrides);
        }

        // Handle MCP configuration if provided
        if (options.mcpConfig) {
            try {
                let mcpConfigData: string;
                if (options.mcpConfig.startsWith("{") || options.mcpConfig.startsWith("[")) {
                    mcpConfigData = options.mcpConfig;
                } else {
                    mcpConfigData = readFromFileIfNeeded(options.mcpConfig) || "{}";
                }
                JSON.parse(mcpConfigData);
                process.env.GITHUB_COPILOT_MCP_JSON = mcpConfigData;
                process.env.GITHUB_COPILOT_3P_MCP_ENABLED = "true";
                this.logger.info("MCP configuration loaded from parameter");
            } catch (error) {
                this.logger!.error(`Invalid MCP configuration: ${error}`);
                throw new Error(`Invalid MCP configuration: ${error}`);
            }
        }

        // Parse agent model setting and create client
        const agentModelSetting = this.settings.service?.agent?.model;
        const result = splitAgentModelSetting(agentModelSetting);
        const agentName = result.agent;
        const agentOptions = result.model ? { model: result.model } : undefined;
        this.client = getClient(this.settings, this.logger, agentName, agentOptions);

        // Create tool configuration
        this.toolConfig = {
            location: ".",
            timeout: 30000,
            requireReasoning: true,
            toolConfirmationCallback: options.onToolCallRequest,
        };

        // Initialize tools
        this.availableTools = await fixToolsForCLI(this.toolConfig, this.logger, this.settings);
    }

    async *getCompletionWithTools(
        systemMessage: string,
        initialMessages: ChatCompletionMessageParam[],
        options?: GetCompletionWithToolsOptions,
    ): AsyncGenerator<Event> {
        if (!this.client) {
            throw new Error("CLI not properly initialized");
        }

        yield* this.client.getCompletionWithTools(systemMessage, initialMessages, this.availableTools, options);
    }

    getToolIntentionSummary(toolName: string, input: unknown): string | null {
        const tool = this.availableTools.find((t) => t.name === toolName);
        if (!tool) {
            throw new Error(`Tool not found: ${toolName}`);
        }
        return tool.summariseIntention?.(input) || null;
    }

    getAvailableTools(): Tool[] {
        return this.availableTools;
    }

    getModel(): string {
        return this.client?.model || "unknown";
    }

    async shutdown(): Promise<void> {
        // Shutdown all tools that have a shutdown method
        for (const tool of this.availableTools) {
            if (tool.shutdown) {
                await tool.shutdown();
            }
        }
    }
}
