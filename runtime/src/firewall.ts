/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import * as fs from "fs";
import * as readline from "readline";

type LoggedBlockedRequest = BlockedRequest & {
    blocked: boolean;
};

/**
 * https://github.com/github/ebpf-padawan-egress-firewall/blob/c00dd1d15907585336fd154088a8eb7ee88c9841/pkg/logger/request.go#L71-L83
 */
export interface BlockedRequest {
    because: string;
    blockedAt: string;
    cmd: string;
    domains: string;
    hasBeenRedirected: boolean;
    ip: string;
    originalIp: string;
    port: string;
    ruleSourceComment: string;
    url: string;
}

export async function getBlockedRequests(): Promise<BlockedRequest[]> {
    const uniqueFwLogs = new Map<string, BlockedRequest>();
    const firewallLogFile = process.env["COPILOT_AGENT_FIREWALL_LOG_FILE"];
    if (firewallLogFile) {
        let fileStream: fs.ReadStream | null = null;
        try {
            fileStream = fs.createReadStream(firewallLogFile, "utf8");
            const rl = readline.createInterface({
                input: fileStream,
                crlfDelay: Infinity,
            });
            for await (const line of rl) {
                if (line.trim()) {
                    try {
                        const logObj: LoggedBlockedRequest = JSON.parse(line);
                        // Only include lines where something was blocked - these logs can get very verbose
                        if (logObj.blocked && logObj.because !== "IPv6AlwaysBlocked" && !logObj.hasBeenRedirected) {
                            const {
                                because,
                                domains,
                                ip,
                                originalIp,
                                port,
                                url,
                                ruleSourceComment,
                                hasBeenRedirected,
                                cmd,
                                blockedAt,
                            } = logObj;
                            const blockedObj: BlockedRequest = {
                                because,
                                domains,
                                ip,
                                originalIp,
                                port,
                                url,
                                ruleSourceComment,
                                hasBeenRedirected,
                                cmd,
                                blockedAt,
                            };
                            // TODO: Firewall PID to command can be intermittent, clean up unknown cmds from output. Will fix up on fw side in future release
                            // Issue: https://github.com/github/ebpf-padawan-egress-firewall/issues/30
                            if (
                                blockedObj.cmd === "root process (pid 0)" ||
                                /process with pid \d+ no longer exists/.test(blockedObj.cmd)
                            ) {
                                blockedObj.cmd = "";
                            }
                            uniqueFwLogs.set(JSON.stringify(blockedObj), blockedObj);
                        }
                    } catch {
                        // Ignore JSON parse errors and continue to next line
                        continue;
                    }
                }
            }
        } finally {
            fileStream?.close();
        }
    }
    return Array.from(uniqueFwLogs.values());
}
