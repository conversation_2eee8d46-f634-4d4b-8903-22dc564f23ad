/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

// This file is the entrypoint for the runtime when run during an MSBench run. It is launched by the
// [`cpd-msbench-runner` tool](../benchmarks/msbench/agent_pkg/cpd-msbench-runner/)

import { program } from "commander";
import { forceExit } from "../helpers/force-exit";
import { registerFixCommand } from "./commands/fix";

registerFixCommand(program);

// eslint-disable-next-line @typescript-eslint/no-floating-promises
program
    .parseAsync(process.argv)
    .catch((error) => {
        console.error(`Error: ${error}`);
        console.error(`Stack trace: ${error.stack}`);
        process.exit(1);
    })
    .then(forceExit);
