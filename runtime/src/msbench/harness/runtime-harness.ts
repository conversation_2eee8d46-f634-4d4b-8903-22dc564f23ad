/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { Client as McpClient } from "@modelcontextprotocol/sdk/client/index.js";
import { getDefaultEnvironment, StdioClientTransport } from "@modelcontextprotocol/sdk/client/stdio.js";
import * as fs from "fs";
import * as path from "path";
import { getAgent } from "../../agents/sweagent";
import { CompoundCallback } from "../../callbacks/callback";
import { LoggerCallback } from "../../callbacks/LoggerCallback";
import { TrajectoryFileCallback } from "../../callbacks/TrajectoryFileCallback";
import { ClientOptions } from "../../model/client";
import { splitAgentModelSetting } from "../../model/util";
import { RunnerLogger } from "../../runner";
import { RunnerExec } from "../../runner/exec";
import { DefaultGitHandler } from "../../runner/git/default";
import type { RuntimeSettings } from "../../settings";
import { demandGitHubMcpServerToken, demandProblemStatement } from "../../settings/demand";
import { ClientInfo } from "../../tools/mcp-transport";

export interface HarnessOptions {
    exec: RunnerExec;
    logger: RunnerLogger;
    repoDir: string;
    settings: RuntimeSettings;
}

export class RuntimeHarness {
    private logger: RunnerLogger;
    private repoDir: string;
    private settings: RuntimeSettings;
    private exec: RunnerExec;

    constructor(options: HarnessOptions) {
        this.logger = options.logger;
        this.repoDir = options.repoDir;
        this.settings = options.settings;
        this.exec = options.exec;
    }

    async launchMcpServers(): Promise<[ClientInfo[], StdioClientTransport[]]> {
        const mcpClients: ClientInfo[] = [];
        const transports: StdioClientTransport[] = [];

        const gitHubMcpServerPath = path.join(path.dirname(process.argv[1]), "github-mcp-server");

        if (fs.existsSync(gitHubMcpServerPath)) {
            const githubAccessToken = demandGitHubMcpServerToken(this.settings);

            const client = new McpClient({
                name: "github-copilot-developer",
                version: "1.0.0",
            });

            const transport = new StdioClientTransport({
                command: gitHubMcpServerPath,
                args: ["stdio", "--read-only"],
                env: {
                    ...getDefaultEnvironment(),
                    GITHUB_PERSONAL_ACCESS_TOKEN: githubAccessToken,
                },
            });

            await client.connect(transport);
            mcpClients.push({
                clientName: "github-mcp-server",
                mcpClient: client,
                safeForTelemetry: true,
            });
            transports.push(transport);

            this.logger.info("Connected to GitHub MCP server");
        }

        return [mcpClients, transports];
    }

    async runPRAgent() {
        const problemStatement = demandProblemStatement(this.settings);
        const issueNumber = this.settings.github?.issue?.number ?? 0;
        const repoName = this.settings.github?.repo?.name ?? "./.";
        const branchName = this.settings.github?.repo?.branch ?? "main";
        const readWrite = this.settings.github?.repo?.readWrite ?? false;
        const agentNameAndModel = this.settings.service?.agent?.model;
        const trajectoryOutputFile = this.settings.trajectory?.outputFile;

        const { agent: agentName, model: agentModel } = splitAgentModelSetting(agentNameAndModel);
        const agentOptions: ClientOptions = {
            ...this.settings.service?.agent,
            model: agentModel,
        };

        const callback = new CompoundCallback()
            .addCallback(new LoggerCallback((msg) => console.log(msg)))
            .addCallback(trajectoryOutputFile ? new TrajectoryFileCallback(trajectoryOutputFile) : undefined);

        const [mcpClients, transports] = await this.launchMcpServers();

        try {
            // Get the agent instance and run the PR agent
            const agent = getAgent(
                this.settings,
                this.logger,
                this.exec,
                agentName,
                agentOptions,
                mcpClients,
                callback,
            );
            const result = await agent.createPRAgent(
                this.repoDir,
                problemStatement,
                repoName,
                issueNumber,
                branchName,
                {
                    location: this.repoDir,
                    push: readWrite,
                    branchName: branchName,
                },
            );

            const git = new DefaultGitHandler(this.logger, this.exec);
            const diff = await git.diff(this.repoDir);

            await callback.result({
                diff,
                branchName,
                prTitle: result.title,
                prDescription: result.description,
            });

            return `${result.title}\n\n${result.description}`;
        } finally {
            for (const transport of transports) {
                await transport.close();
            }
        }
    }
}
