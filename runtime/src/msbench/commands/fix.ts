/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { Command } from "commander";
import { readFromFileIfNeeded } from "../../helpers/file-reader";
import { LogLevel } from "../../runner";
import { DefaultExec } from "../../runner/exec/default";
import { CompoundLogger } from "../../runner/logger/compound";
import { ConsoleLogger } from "../../runner/logger/console";
import { FileLogger } from "../../runner/logger/file";
import { initSettings } from "../../settings/factory";
import { SettingsBuilder } from "../../settings/settings-builder";
import { RuntimeHarness } from "../harness/runtime-harness";

export function registerFixCommand(program: Command) {
    const aliases: { [key: string]: string } = {
        // GitHub settings - User
        "--user-name": "--github-user-name",
        "--user-email": "--github-user-email",
        "--token": "--github-token",
        "--host": "--github-host",
        // GitHub settings - Repository
        "--repo-dir": "--github-repo-directory",
        "--directory": "--github-repo-directory",
        "--folder": "--github-repo-directory",
        "--repo-name": "--github-repo-name",
        "--repo-branch": "--github-repo-branch",
        "--branch": "--github-repo-branch",
        "--branch-name": "--github-repo-branch",
        "--repo-commit": "--github-repo-commit",
        "--commit": "--github-repo-commit",
        "--repo-read-write": "--github-repo-read-write",
        "--read-write": "--github-repo-read-write",
        "--push": "--github-repo-read-write",
        // GitHub settings - Issue and PR
        "--issue": "--github-issue-number",
        "--pr": "--github-pr-number",
        // Problem and service settings
        "--problem": "--problem-statement",
        "--instructions": "--problem-statement",
        "--instance-id": "--service-instance-id",
        "--instance": "--service-instance-id",
        "--agent-model": "--service-agent-model",
        "--model": "--service-agent-model",
        "--callback": "--service-callback-url",
        // API keys and authentication
        "--aip-token": "--api-aip-swe-agent-token",
        "--anthropic-key": "--api-anthropic-key",
        "--copilot-url": "--api-copilot-url",
        "--copilot-integration-id": "--api-copilot-integration-id",
        "--integration-id": "--api-copilot-integration-id",
        "--copilot-hmac": "--api-copilot-hmac-key",
        "--hmac-key": "--api-copilot-hmac-key",
        "--hmac": "--api-copilot-hmac-key",
        "--copilot-vault": "--api-copilot-azure-key-vault-uri",
        "--copilot-vault-uri": "--api-copilot-azure-key-vault-uri",
        "--copilot-key-vault": "--api-copilot-azure-key-vault-uri",
        "--copilot-key-vault-uri": "--api-copilot-azure-key-vault-uri",
        "--copilot-azure-key-vault": "--api-copilot-azure-key-vault-uri",
        "--copilot-azure-key-vault-uri": "--api-copilot-azure-key-vault-uri",
        "--copilot-token": "--api-copilot-token",
    };

    process.argv = process.argv.map((arg: string) => aliases[arg] || arg);

    program
        .command("fix")
        .description("Run PR agent to fix an issue")
        // GitHub settings - User
        .option("--github-user-name <name>", "GitHub username")
        .option("--github-user-email <email>", "GitHub user email")
        .option("--github-token <token>", "GitHub authentication token")
        .option("--github-host <host>", "GitHub host name (e.g., github.com)")
        // GitHub settings - Repository
        .option("--github-repo-directory <dir>", "Repository directory")
        .option("--github-repo-name <name>", "GitHub repository name")
        .option("--github-repo-branch <branch>", "Repository branch name")
        .option("--github-repo-commit <commit>", "Base commit SHA")
        .option("--github-repo-read-write", "Enable repository write access", false)
        // GitHub settings - Issue and PR
        .option("--github-issue-number <number>", "Issue number")
        .option("--github-pr-number <number>", "Pull request number")
        // Problem and service settings
        .option("--problem-statement <statement>", "Problem statement")
        .option("--instructions <statement>", "Alias for --problem-statement")
        .option("--service-instance-id <id>", "Service instance ID")
        .option("--service-agent-model <model>", "Agent model")
        .option("--service-callback-url <url>", "Callback URL")
        // API keys and authentication
        .option("--api-aip-swe-agent-token <token>", "AIP SWE agent token")
        .option("--api-anthropic-key <key>", "Anthropic API key")
        .option("--api-copilot-url <url>", "Copilot URL")
        .option("--api-copilot-integration-id <id>", "Copilot integration ID")
        .option("--api-copilot-hmac-key <key>", "Copilot HMAC key")
        .option("--api-copilot-token <token>", "Copilot token")
        .option("--api-copilot-azure-key-vault-uri <uri>", "Copilot Azure Key Vault URI")
        // Miscellaneous
        .option("--verbose", "Enable verbose logging")
        .option("--log <file>", "Log to file")
        .option("--save-trajectory-output <file>", "Save trajectory output to file")
        .option("--disable-online-evaluation", "Disable online evaluation", false)
        .action(runFixCommand);
}

interface FixProblemCommandOptions {
    // GitHub User
    githubUserName?: string;
    githubUserEmail?: string;
    githubToken?: string;
    githubHost?: string;
    // GitHub Repo
    githubRepoDirectory: string;
    githubRepoName?: string;
    githubRepoBranch?: string;
    githubRepoCommit?: string;
    githubRepoReadWrite: boolean;
    // GitHub Issue/PR
    githubIssueNumber?: number;
    githubPrNumber?: number;
    // Problem and Service
    problemStatement?: string;
    serviceInstanceId?: string;
    serviceAgentModel?: string;
    serviceCallbackUrl?: string;
    // API Keys
    apiAipSweAgentToken?: string;
    apiAnthropicKey?: string;
    apiCopilotUrl?: string;
    apiCopilotIntegrationId?: string;
    apiCopilotHmacKey?: string;
    apiCopilotToken?: string;
    apiCopilotAzureKeyVaultUri?: string;
    // Miscellaneous
    verbose?: boolean;
    log?: string;
    saveTrajectoryOutput?: string;
    disableOnlineEvaluation?: boolean | undefined;
}

export async function runFixCommand(options: FixProblemCommandOptions) {
    const loggingToFile = options.log !== undefined;
    const logToConsoleLevel = options.verbose ? LogLevel.All : loggingToFile ? LogLevel.None : LogLevel.Error;
    const logger = new CompoundLogger([
        new ConsoleLogger(logToConsoleLevel),
        ...(options.log ? [new FileLogger(options.log, LogLevel.All)] : []),
    ]);
    const exec = new DefaultExec(logger);

    try {
        const builder = new SettingsBuilder();
        const overrides = builder
            // GitHub User settings
            .setGithubUserName(readFromFileIfNeeded(options.githubUserName))
            .setGithubUserEmail(readFromFileIfNeeded(options.githubUserEmail))
            .setGithubToken(readFromFileIfNeeded(options.githubToken))
            .setGithubHost(readFromFileIfNeeded(options.githubHost))
            // GitHub Repo settings
            .setGithubRepoName(readFromFileIfNeeded(options.githubRepoName))
            .setGithubRepoCommit(readFromFileIfNeeded(options.githubRepoCommit))
            .setGithubRepoBranch(readFromFileIfNeeded(options.githubRepoBranch))
            .setGithubRepoReadWrite(options.githubRepoReadWrite)
            // GitHub Issue/PR settings
            .setGithubIssueNumber(options.githubIssueNumber)
            .setGithubPRNumber(options.githubPrNumber)
            // Problem and Service settings
            .setProblemStatement(readFromFileIfNeeded(options.problemStatement))
            .setProblemAction("fix")
            .setInstanceId(readFromFileIfNeeded(options.serviceInstanceId))
            .setAgentModel(readFromFileIfNeeded(options.serviceAgentModel))
            .setCallbackUrl(readFromFileIfNeeded(options.serviceCallbackUrl))
            // API settings
            .setAipSweAgentToken(readFromFileIfNeeded(options.apiAipSweAgentToken))
            .setAnthropicApiKey(readFromFileIfNeeded(options.apiAnthropicKey))
            .setCopilotUrl(readFromFileIfNeeded(options.apiCopilotUrl))
            .setCopilotIntegrationId(readFromFileIfNeeded(options.apiCopilotIntegrationId))
            .setCopilotHmacKey(readFromFileIfNeeded(options.apiCopilotHmacKey))
            .setCopilotToken(readFromFileIfNeeded(options.apiCopilotToken))
            .setCopilotAzureKeyVaultUri(readFromFileIfNeeded(options.apiCopilotAzureKeyVaultUri))
            // Trajectory settings
            .setTrajectoryOutputFile(readFromFileIfNeeded(options.saveTrajectoryOutput))
            .setDisableOnlineEvaluation(options.disableOnlineEvaluation)
            .build();

        // Initialize settings
        const settings = await initSettings(overrides);

        // Create and run harness
        const harness = new RuntimeHarness({
            exec,
            logger,
            repoDir: options.githubRepoDirectory ?? ".",
            settings,
        });

        await harness.runPRAgent();
    } catch (error) {
        logger.error(`Error: ${error}`);
        process.exit(1);
    }
}
