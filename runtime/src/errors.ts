/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { SWEBenchVerifiedProblem } from ".";
import { CAPIError } from "./model/capi/copilot-client";
import { CopilotSessionsClient } from "./model/capi/sessions-client";
import { GitHandler } from "./runner/git";
import { RunnerLogger } from "./runner/logger";
import { RuntimeSettings } from "./settings";
import { Agent<PERSON>allbackErrorEvent } from "./types";

/*
 This file contains the ErrorHandler class which is responsible for handling exceptions that are thrown
 during the execution of an agent and are otherwise unhandled. Its goal is to send enough information to
 telemetry without overloading or overwhelming alerting or pushing giant messages. In cases where a stdout/stderr
 is present in the error, it will be logged to the session service as non-completion content since this may
 require that the user takes action to resolve it.
*/

const maxFieldSize = 5000;

export class ErrorHandler {
    constructor(
        private settings: RuntimeSettings,
        private logger: RunnerLogger,
        private problem: SWEBenchVerifiedProblem,
        private git: GitHandler,
        private sessionsClient: CopilotSessionsClient,
    ) {}

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    async handleError(err: any) {
        // We are hitting scenarios where we are sending back giant 2MB error messages in certain conditions.
        // This can bomb sweagentd and/or the session service, so  we need to make sure we are passing back
        // no more than 5k characters per field.
        try {
            if (!(err instanceof Error)) {
                let text = String(err);
                if (text.length > maxFieldSize) {
                    text = text.slice(0, maxFieldSize) + "...";
                }
                err = new Error(text);
            } else if (err.message.length > maxFieldSize) {
                err.message = err.message.slice(0, maxFieldSize) + "...";
            }

            // Set the error name to the class name if not already set
            if (!err.name) {
                err.name = err.prototype?.constructor?.name ?? err.constructor.name;
            }

            // Locally we want to log extended details about the error including stderr / stdout / cmd if present on the error object (but truncated)
            this.logger.error(
                `${err.name ?? "Error:"} ${err.message} ${err.cmd ?? ""}${err.stdout ? "\n" + err.stdout : ""}${err.stderr ? "\n" + err.stderr : ""}`,
            );
            // Send the error to the callback and optionally report
            await this.sendErrorToCallback(err);
            // Update the session with command details as appropriate - rather than locking to specific error types, we
            // generally should surface the cmd, stderr, stdout if available on the error so we can sub-type freely.
            if (err.cmd || err.stdout || err.stderr) {
                await this.addErrorToSessionLog(err);
            }

            // Try to push any content that is local using "--no-verify" so that the
            // user can ask the agent to continue on without any data loss even if hooks failed.
            if (this.problem.push) {
                try {
                    this.logger.info("Committing and pushing any changes that occurred before error occurred.");
                    await this.git.commitAndPushChanges(
                        this.settings,
                        this.problem.branchName,
                        this.problem.repoLocation,
                        "Changes before error encountered",
                        false,
                        true,
                    );
                } catch (commitError) {
                    this.logger.error(`Error while committing content to session: ${commitError}`);
                    await this.sendErrorToCallback(commitError);
                    await this.addErrorToSessionLog(commitError);
                }
            }
            // Finally error out the session
            try {
                await this.sessionsClient.error(err);
            } catch (sessionError) {
                this.logger.error(`Error while adding error to session: ${sessionError}`);
            }
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } catch (err: any) {
            // This should never happen, but if it does, we want to log it
            this.logger.error(`Error while handling error: ${err.message || err}`);
        }

        process.exitCode = 1;
    }

    // Generates an AgentCallbackErrorEvent and send it to the callback.
    // Never throw in this method since we want to continue on failure.
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    async sendErrorToCallback(err: any) {
        const event: AgentCallbackErrorEvent = {
            name: err.name,
            text: err.message,
            stack: err.stack,
        };
        // Remove time elements from text since this is used to classify errors
        const timeValRegEx = /(\d+)\s+(second|minute|min|hour|hr|times)/gm;
        let match: RegExpExecArray | null;
        while ((match = timeValRegEx.exec(event.text)) !== null) {
            event.text = event.text.replace(match[0], `n ${match[2]}`);
        }
        event.text = event.text.trim();
        event.message = event.text;

        const truncateAndAddToEventIfExists = (
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            source: any,
            key: keyof AgentCallbackErrorEvent,
        ) => {
            if (source[key]) {
                const value = String(source[key]);
                if (value.length > maxFieldSize) {
                    const truncated = value.slice(0, maxFieldSize) + "...";
                    source[key] = truncated;
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    (event as any)[key] = truncated;
                } else {
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    (event as any)[key] = value;
                }
            }
        };

        for (const key of [
            "cmd",
            "stdout",
            "stderr",
            "code",
            "signal",
            "request_id",
            "ghRequestId",
        ] satisfies (keyof AgentCallbackErrorEvent)[]) {
            truncateAndAddToEventIfExists(err, key);
        }

        if (err.cause && err.cause instanceof CAPIError) {
            for (const key of ["code", "request_id", "ghRequestId"] satisfies (keyof AgentCallbackErrorEvent)[]) {
                truncateAndAddToEventIfExists(err.cause, key);
            }
        }

        // Indicates whether the error should be reported to Sentry or just logged
        event.skipReport = err.skipReport ?? false;
        // Indicates whether the error happened in a vision flow
        event.isVisionFlow = err.isVisionFlow ?? false;

        // Fire the callback to update telemetry and report optionally the error - never throw
        try {
            await this.problem.callback.error(event);
        } catch (callbackError) {
            this.logger.error(`Error while sending error: ${callbackError}`);
        }
    }

    // Adds any stdout/stderr content from the error to the session.
    // Never throw in this method since we want to continue on failure.
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    private async addErrorToSessionLog(err: any) {
        try {
            let content = "";
            if (err.cmd) {
                content += `$ ${err.cmd}\n`;
            }
            if (err.stdout) {
                content += `${err.stdout}\n`;
            }
            if (err.stderr) {
                content += `${err.stderr}\n`;
            }
            if (err.code) {
                content += `<exited with exit code ${err.code}>\n`;
            }
            if (err.signal) {
                content += `<terminated by signal ${err.signal}>\n`;
            }
            content = content.trim();
            // TODO: Currently there's not a great way to specify a content type for the session log,
            // so we're using <error></error>. Revise this and the logNonCompletionContent when we
            // have a better way to specify content types.
            await this.sessionsClient.logNonCompletionContent(`<error>${content}</error>`);
        } catch (sessionError) {
            this.logger.error(`Error while logging non-completion content to session: ${sessionError}`);
        }
    }
}

export class JobError {
    service: string;
    code: string;

    constructor(service: string, code: string) {
        this.service = service;
        this.code = code;
    }

    toString(): string {
        return `${this.service}:${this.code}`;
    }
}
