/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { SWEBenchVerifiedProblem } from ".";
import {
    hasAtLeastOneGitHubInstructionsMd,
    hasClaudeCustomInstructions,
    hasClaudeHooks,
    hasCodexCustomInstructions,
    hasCopilotInstructions,
    hasCopilotSetupSteps,
    hasGeminiCustomInstructions,
} from "./helpers/repo-helpers";
import { RuntimeSettings } from "./settings";
import { TelemetryEvent } from "./telemetry";

type PostCloneTelemetryEvent = TelemetryEvent<
    "post_clone",
    {
        properties: {
            hasCopilotInstructions: string;
            hasCopilotSetupSteps: string;
            hasCodexCustomInstructions: string;
            hasClaudeCustomInstructions: string;
            hasGeminiCustomInstructions: string;
            hasAtLeastOneGitHubInstructionsMd: string;
            hasClaudeHooks: string;
        };
        restrictedProperties: Record<string, never>;
        metrics: {
            /**
             * The time at which this event was created, in ms since epoch.
             * This metric exists to serve as a 100% accurate timestamp for the event.
             */
            nowMs: number;
            /**
             * How many ms the runtime/the thing hosting the runtime has available to run
             * before it is considered to have timed out.
             */
            runtimeTimeoutMs: number | undefined;
            /**
             * The time when the runtime/the thing hosting the runtime started, in ms since epoch.
             * May not be 100% accurate.
             */
            runtimeStartTimeMs: number | undefined;
        };
    }
>;

export async function emitPostCloneTelemetry(
    problem: SWEBenchVerifiedProblem,
    settings: RuntimeSettings,
): Promise<void> {
    const hasCopilotInstructionsStr = hasCopilotInstructions(problem.repoLocation).exists ? "true" : "false";
    const hasCopilotSetupStepsStr = hasCopilotSetupSteps(problem.repoLocation) ? "true" : "false";
    const hasCodexCustomInstructionsStr = hasCodexCustomInstructions(problem.repoLocation).exists ? "true" : "false";
    const hasClaudeCustomInstructionsStr = hasClaudeCustomInstructions(problem.repoLocation).exists ? "true" : "false";
    const hasGeminiCustomInstructionsStr = hasGeminiCustomInstructions(problem.repoLocation).exists ? "true" : "false";
    const hasAtLeastOneGitHubInstructionsMdStr = hasAtLeastOneGitHubInstructionsMd(problem.repoLocation)
        ? "true"
        : "false";
    const hasClaudeHooksStr = hasClaudeHooks(problem.repoLocation) ? "true" : "false";

    const postCloneEvent: PostCloneTelemetryEvent = {
        kind: "telemetry",
        telemetry: {
            event: "post_clone",
            properties: {
                hasCopilotInstructions: hasCopilotInstructionsStr,
                hasCopilotSetupSteps: hasCopilotSetupStepsStr,
                hasCodexCustomInstructions: hasCodexCustomInstructionsStr,
                hasClaudeCustomInstructions: hasClaudeCustomInstructionsStr,
                hasGeminiCustomInstructions: hasGeminiCustomInstructionsStr,
                hasAtLeastOneGitHubInstructionsMd: hasAtLeastOneGitHubInstructionsMdStr,
                hasClaudeHooks: hasClaudeHooksStr,
            },
            restrictedProperties: {},
            metrics: {
                nowMs: Date.now(),
                runtimeTimeoutMs: settings.timeoutMs,
                runtimeStartTimeMs: settings.startTimeMs,
            },
        },
    };
    await problem.callback.progress(postCloneEvent);
}
