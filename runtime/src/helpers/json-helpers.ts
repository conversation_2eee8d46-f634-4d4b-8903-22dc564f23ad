/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { hashString } from "./crypto-helpers";

/**
 * Repeatedly parses a JSON string until an object is returned or an error is thrown. If parsing fails at
 * any point, that error will be re-thrown.
 *
 * This function is useful for handling cases where the input string may be a JSON string that has been
 * stringified multiple times. For example, if the input string is a JSON string that has been
 * stringified twice, this function will parse it twice to obtain the original object.
 *
 *
 * @example
 * // returns `{ x: 1 }`
 * deepJsonParse('{"x":1}')
 *
 * @example
 * // returns `{ x: 1 }`
 * deepJsonParse('"\\"{\\\\\\"x\\\\\\":1}\\""')
 *
 * @example
 * // throws an error as the final parse of '{"x":falsey}' fails
 * deepJsonParse('"\\"{\\\\\\"x\\\\\\":falsey}\\""')
 *
 * @example
 * // throws an error as the original text is not a valid JSON string
 * deepJsonParse('falsey')
 */
export function deepJsonParse(
    text: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    reviver?: (this: any, key: string, value: any) => any,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
): any {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let lastParseResult: any = text;
    while (typeof lastParseResult !== "object") {
        lastParseResult = JSON.parse(lastParseResult, reviver);
    }
    return lastParseResult;
}

/**
 * Uses JSON.stringify to serialize an object to a string, but before doing so, hashses the
 * value of each property in the object. All values will be converted to strings and then hashed.
 */
export function jsonStringifyHashed(obj: { [key: string]: unknown }): string {
    const hashObj: { [key: string]: string } = {};
    for (const [key, value] of Object.entries(obj)) {
        const valueToHash =
            typeof value !== "string" ? JSON.stringify(value) : value === undefined ? "undefined" : value;
        hashObj[key] = hashString(valueToHash);
    }
    return JSON.stringify(hashObj);
}

export function compareWithExceptions(
    obj1: Record<string, string | number | boolean | undefined> | undefined,
    obj2: Record<string, string | number | boolean | undefined> | undefined,
    excludeFields: string[],
): boolean {
    if ((obj1 === undefined && obj2 !== undefined) || (obj1 !== undefined && obj2 === undefined)) {
        return false;
    } else if (obj1 === undefined && obj2 === undefined) {
        return true;
    } else if (obj1 !== undefined && obj2 !== undefined) {
        for (const key of Object.keys(obj1)) {
            if (excludeFields.includes(key)) {
                continue;
            }
            if (obj1[key] !== obj2[key]) {
                return false;
            }
        }
        for (const key of Object.keys(obj2)) {
            if (excludeFields.includes(key)) {
                continue;
            }
            if (obj1[key] !== obj2[key]) {
                return false;
            }
        }
    }
    return true;
}
