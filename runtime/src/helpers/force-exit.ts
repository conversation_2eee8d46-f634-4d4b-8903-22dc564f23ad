/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

/**
 * forceExit forces the process to exit after a short timeout. By default, node will not exit if any
 * async operations are still pending. We have observed cases where the agent completes (i.e. the promise
 * returned by `main()` or `program.parseAsync()` resolves), but the process does not exit. We suspect this
 * is due to an async operation pending (likely due to some leaked promise from monitoring an external job),
 * but haven't yet tracked down the source. This workaround ensures that the process exist even if one of these
 * jobs is still pending on the assumption that the user visible work of the agent is complete, since the promise
 * for it has resolved.
 *
 * The node documentation states that it is not good practice to use process.exit explicitly, but it is better
 * we do this than leave the process hanging indefinitely. One reason the documentation encourages you not to do this
 * is that writes to stdout/stderr may take multiple calls to complete and you could end up with a partial write.
 * To mitigate this, we wait 5 seconds before calling process.exit, to give these writes time to complete. This isn't
 * a perfect solution, but better than the alternatives.
 *
 * FUTURE: We could consider using something like the `why-is-node-running` package to track down what operations would
 * be keeping the process alive and perhaps send a telemetry event so we could try to track down and fix the source of
 * the leaks.
 */
export function forceExit() {
    // NOTE: We call `process.exit` without an argument so that we pick up the value of `process.exitCode` which some
    // of our error paths set. The promise for running the agent can resolve successfully even when `process.exitCode` is
    console.log("forceExit is shutting down the process");
    process.exit();
}
