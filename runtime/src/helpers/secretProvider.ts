/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { DefaultAzureCredential } from "@azure/identity";
import { SecretClient } from "@azure/keyvault-secrets";
import { RunnerLogger } from "../runner";
import { RuntimeSettings } from "../settings";

export interface SecretProvider {
    getSecret(secretName: string): Promise<string | undefined>;
}
class AzureSecretProvider implements SecretProvider {
    private client?: SecretClient;
    private logger: RunnerLogger;
    constructor(vaultUri: string | undefined, logger: RunnerLogger) {
        this.logger = logger;

        if (vaultUri) {
            this.logger.debug(`Using Azure Key Vault at ${vaultUri}`);
            const credential = new DefaultAzureCredential();
            this.client = new SecretClient(vaultUri, credential);
        } else {
            this.logger.debug("No Azure Key Vault URI provided, secret provider disabled");
        }
    }

    async getSecret(secretName: string): Promise<string | undefined> {
        if (!this.client) {
            return undefined;
        }

        try {
            const secret = await this.client.getSecret(secretName);
            return secret.value;
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } catch (error: any) {
            this.logger.warning(`Error fetching secret ${secretName}: ${error}`);
            if (error?.code === "SecretNotFound") {
                return undefined;
            }
            throw error;
        }
    }
}

export function createCapiSecretProvider(settings: RuntimeSettings, logger: RunnerLogger): SecretProvider {
    const azureVaultUri = settings.api?.copilot?.azureKeyVaultUri;
    return createAzureSecretProvider(azureVaultUri, logger);
}

export function createAzureSecretProvider(azureVaultUri: string | undefined, logger: RunnerLogger): SecretProvider {
    return new AzureSecretProvider(azureVaultUri, logger);
}
