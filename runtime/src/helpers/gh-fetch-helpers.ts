/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import { RunnerLogger } from "../runner";

export interface RetryOptions {
    maxRetries?: number;
    defaultRetryDelaySeconds?: number;
    backoffFactor?: number;
}

/**
 * Default options for fetch retries
 */
const DEFAULT_RETRY_OPTIONS: RetryOptions = {
    maxRetries: 5,
    defaultRetryDelaySeconds: 5,
    backoffFactor: 2, // Exponential backoff multiplier
};

/**
 * Default HTTP status codes that are considered retriable
 */
const RETRIABLE_STATUS_CODES = [429, 500, 502];

/**
 * Perform a fetch request with automatic retry on rate limit (HTTP 429) and server error (HTTP 500) responses.
 *
 * @param url URL to fetch
 * @param options Fetch request options
 * @param logger Logger for outputting warning and error messages
 * @param retryOptions Custom retry behavior options
 * @returns A Promise that resolves to the Response object
 */
export async function ghFetch(
    url: URL | string,
    options: RequestInit,
    logger: RunnerLogger,
    apiName: string = "GitHub API",
    retryOptions: RetryOptions = DEFAULT_RETRY_OPTIONS,
): Promise<Response> {
    const maxRetries = retryOptions.maxRetries ?? DEFAULT_RETRY_OPTIONS.maxRetries!;
    const defaultRetryDelay = retryOptions.defaultRetryDelaySeconds ?? DEFAULT_RETRY_OPTIONS.defaultRetryDelaySeconds!;
    const backoffFactor = retryOptions.backoffFactor ?? DEFAULT_RETRY_OPTIONS.backoffFactor!;

    let retries = 0;

    while (retries <= maxRetries) {
        let response: Response | undefined = undefined;
        try {
            response = await fetch(url, options);
        } catch (error) {
            const errorToLog = typeof error === "object" && error !== null && "cause" in error ? error.cause : error;
            logger.error(`Error making ${apiName} request: ${errorToLog}`);
            if (retries >= maxRetries) {
                throw error;
            }
        }

        if (response) {
            // If the response is OK, return it immediately
            if (response.ok) {
                return response;
            }

            const requestId = response.headers.get("x-github-request-id") || "unknown";

            // If it's not a retriable error or we've reached max retries, return the error response
            if (!isRetriableError(response) || retries >= maxRetries) {
                logger.error(`Request to ${apiName} failed with status ${response.status} (request ID: ${requestId}).`);
                return response;
            }
        }

        // Get retry delay from header or use default with exponential backoff
        const retryAfter = response?.headers.get("retry-after");
        // Increment retry counter before calculating delay (so first retry uses backoffFactor^1)
        retries++;

        // Apply exponential backoff: base delay * (backoff factor ^ retry number)
        // If GitHub provides a retry-after header, use that as the base delay
        const baseDelay = retryAfter ? parseInt(retryAfter, 10) : defaultRetryDelay;
        const delaySeconds = Math.floor(baseDelay * Math.pow(backoffFactor, retries - 1));

        logger.warning(
            `Rate limited (429) when making ${apiName} request. Retrying in ${delaySeconds} seconds. Attempt ${retries}/${maxRetries}`,
        );

        // Wait before retrying
        await new Promise((resolve) => setTimeout(resolve, delaySeconds * 1000));
    }

    // This should never happen due to the loop conditions, but TypeScript needs a return
    throw new Error(`Failed to make ${apiName} request.`);
}

function isRetriableError(response: Response): boolean {
    return RETRIABLE_STATUS_CODES.includes(response.status);
}
