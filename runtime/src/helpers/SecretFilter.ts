/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

// Use the "current" variation of these methods to avoid infinite loops due to circular references
import { getCurrentSettings, hasCurrentSettings } from "../settings/current";

import { Runner, RunnerLogger } from "../runner";
import { secretEnvVarNames } from "../settings/environment-settings";
import { getSettingsSecretVals } from "../settings/types";
import { getNotEmptyStringEnvVar } from "./environment-variables";

/**
 * Copied from Actions' patterns
 * https://github.com/github/actions-run-service/blob/c223c6d6c842a21bdf19d960b45faea53e55fdf2/api/rest/acquirejobhandler.go#L1171-L1221
 */
const logRedactionPatterns: RegExp[] = [
    // Matches the prefix of JWT tokens, plus the rest of the token
    /\b(?:eyJ0eXAiOi|eyJhbGciOi|eyJ4NXQiOi|eyJraWQiOi)[^\s'";]+/g,
    // Bearer tokens, e.g. `curl https://bing.com -H "Authorization: Bearer 12345"`
    /\bBearer\s+[^\s'";]+/g,
    // Passwords in database connection strings.
    /\b(?:Password|Pwd)=(?:[^\s'";]+|"[^"]+")/gi,
    // Passwords passed as arguments to commands at the shell with empty space preceding Password|Pwd.
    /\s+-(?:Password|Pwd)\s+(?:[^\s'";]+|"[^"]+")/gi,
    // GitHub installation token.
    /\bv1\.[0-9A-Fa-f]{40}\b/g,
    // GitHub installation token v2.
    /\bgh[pousr]{1}_[A-Za-z0-9]{36}\b/g,
    // GitHub PAT token v2.
    /\bgithub_pat_[0-9][A-Za-z0-9]{21}_[A-Za-z0-9]{59}\b/g,
    // Matches URIs that contain Basic authentication credentials in them,
    // e.g. http://username:<EMAIL>
    /(?:[a-zA-Z][a-zA-Z\d+-.]*):\/\/([a-zA-Z\d\-._~!$&'()*+,;=%]+):([a-zA-Z\d\-._~!$&'()*+,;=:%]*)@/g,
    // The two most recent AAD client app password formats
    /\b[0-9A-Za-z-_~.]{3}7Q~[0-9A-Za-z-_~.]{31}\b|\b[0-9A-Za-z-_~.]{3}8Q~[0-9A-Za-z-_~.]{34}\b/g,
    // 64-byte Azure keys (Azure Storage, CosmosDB, Batch, APIM, and Azure ML Classic)
    /\b[0-9A-Za-z+/]{76}(APIM|ACDb|\+(ABa|AMC|ASt))[0-9A-Za-z+/]{5}[AQgw]==/g,
    // 32-byte Azure keys (Azure Service Bus, EventHub and Relay Management, Azure IOT Devices, Hub and Provisioning)
    /\b[0-9A-Za-z+/]{33}(AIoT|\+(ASb|AEh|ARm))[A-P][0-9A-Za-z+/]{5}=/g,
    // Azure Function Host key
    /\b[0-9A-Za-z_-]{44}AzFu[0-9A-Za-z\-_]{5}[AQgw]==/g,
    // Azure Search key
    /\b[0-9A-Za-z]{42}AzSe[A-D][0-9A-Za-z]{5}\b/g,
    // Azure Container Registry key
    /\b[0-9A-Za-z+/]{42}\+ACR[A-D][0-9A-Za-z+/]{5}\b/g,
    // Azure Cache for Redis key
    /\b[0-9A-Za-z]{33}AzCa[A-P][0-9A-Za-z]{5}=/g,
    // NuGet API key
    /\boy2[a-p][0-9a-z]{15}[aq][0-9a-z]{11}[eu][bdfhjlnprtvxz357][a-p][0-9a-z]{11}[aeimquy4]\b/g,
    // npm author token
    /\bnpm_[0-9A-Za-z]{36}\b/g,
    // GHCR presigned URL x-ghcr-signature query string parameter
    /\bx-ghcr-signature=[^&]+/g,
];

export const redactedValue = "******";

/**
 * Filters out secret values from a variety of things.
 *
 * Secret values are grabbed from:
 * - Values of standard environment variables that are considered secret, see {@link secretEnvVarNames}.
 * - Values of environment variables whose names are included in the keys from the given {@link Runner.sensitiveKeys}.
 * - Settings that have keys in {@link secretSettingNames}.
 * - Any value that matches a pattern in {@link logRedactionPatterns}.
 *
 * Looks for known secret values both in plain text and base64 encoded.
 */
export class SecretFilter {
    private static singleton: SecretFilter | undefined;
    public static getInstance(): SecretFilter {
        if (!SecretFilter.singleton) {
            SecretFilter.singleton = new SecretFilter();
        }
        return SecretFilter.singleton;
    }

    private secretValuesToFilter: Set<string> | undefined;
    private runner: Runner | undefined;
    private logger: RunnerLogger | undefined;

    public constructor() {}

    public setRunner(runner: Runner): void {
        this.runner = runner;
        this.logger = runner.logger;
        // Reset cached secret values when runner is set
        this.secretValuesToFilter = undefined;
    }

    public filterSecrets(str: string): string;
    public filterSecrets(error: Error): Error;
    public filterSecrets(strOrError: string | Error): string | Error {
        const isErrorLike =
            typeof strOrError === "object" && Object.prototype.hasOwnProperty.call(strOrError, "message");
        let filteredMessage: string = isErrorLike
            ? // eslint-disable-next-line @typescript-eslint/no-explicit-any
              ((strOrError as any).message ?? strOrError.toString())
            : strOrError;
        const secretVals = this.getSecretValues();
        for (const secret of secretVals) {
            filteredMessage = filteredMessage.replaceAll(secret, redactedValue);
        }
        for (const pattern of logRedactionPatterns) {
            filteredMessage = filteredMessage.replaceAll(pattern, redactedValue);
        }
        if (isErrorLike) {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            (strOrError as any).message = filteredMessage;
            return strOrError;
        }
        return filteredMessage;
    }

    public filterSecretsFromJsonString(jsonString: string): string {
        try {
            // Attempt to parse the arguments as JSON
            const args = JSON.parse(jsonString);

            // Recursively redact string and number values in the parsed object
            const redactedArgs = this.filterSecretsFromObj(args);

            // Re-stringify the redacted object
            return JSON.stringify(redactedArgs);
        } catch {
            // If JSON parsing fails, fall back to redacting the entire string
            return this.filterSecrets(jsonString);
        }
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    public filterSecretsFromObj(obj: unknown): any {
        if (obj === null || obj === undefined) {
            return obj;
        }

        if (typeof obj === "string") {
            return this.filterSecrets(obj);
        }

        if (typeof obj === "number") {
            // Redact numbers by converting to string, redacting, then back to number if possible
            const redactedString = this.filterSecrets(obj.toString());
            const parsedNumber = Number(redactedString);
            // If the redacted string is still a valid number, return it as number
            // Otherwise return the redacted string to preserve the redaction
            return isNaN(parsedNumber) ? redactedString : parsedNumber;
        }

        if (Array.isArray(obj)) {
            return obj.map((item) => this.filterSecretsFromObj(item));
        }

        if (typeof obj === "object") {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            const redactedObj: any = {};
            for (const [key, value] of Object.entries(obj)) {
                redactedObj[key] = this.filterSecretsFromObj(value);
            }
            return redactedObj;
        }

        // For other types (boolean, function, etc.), return as-is
        return obj;
    }

    private getSecretValues(): string[] | Set<string> {
        if (this.secretValuesToFilter) {
            return this.secretValuesToFilter;
        }

        const loadedSecretsFrom: { [source: string]: boolean } = {
            secretEnvVars: false,
            runner: false,
            settings: false,
        };

        const secretValues: Set<string> = new Set<string>();

        // Standard secret environment variables we own that we want to filter out
        for (const varName of secretEnvVarNames) {
            const maybeSecretValue = getNotEmptyStringEnvVar(varName);
            if (maybeSecretValue) {
                addStringAndBase64StringToSet(secretValues, maybeSecretValue);
            }
        }
        loadedSecretsFrom.secretEnvVars = true;

        if (this.runner) {
            // Once the runner is available, add to the set of secret values
            for (const varName of this.runner.sensitiveKeys) {
                const maybeSecretValue = getNotEmptyStringEnvVar(varName);
                if (maybeSecretValue) {
                    addStringAndBase64StringToSet(secretValues, maybeSecretValue);
                }
            }
            loadedSecretsFrom.runner = true;
        }

        if (hasCurrentSettings()) {
            // Once settings are available, add to the set of secret values
            const settingsSecretVals = getSettingsSecretVals(getCurrentSettings());
            for (const secret of settingsSecretVals) {
                addStringAndBase64StringToSet(secretValues, secret);
            }
            loadedSecretsFrom.settings = true;
        }

        if (Object.values(loadedSecretsFrom).every((v) => v)) {
            // Only cache once we've loaded all sources of secrets
            this.secretValuesToFilter = secretValues;
            this.logger?.debug(`Caching secret values to filter from all sources.`);
        }

        return secretValues;
    }
}

function addStringAndBase64StringToSet(set: Set<string>, value: string): void {
    set.add(value);
    set.add(Buffer.from(value, "utf8").toString("base64"));
}
