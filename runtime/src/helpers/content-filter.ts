/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

/**
 * Content filtering module for problem statements
 *
 * Implements filtering of:
 * - Unicode characters that can hide directives or affect AI processing
 * - HTML comments outside of code blocks
 * - HTML tags and attributes based on allowlist
 *
 * Uses DOMPurify for HTML sanitization along with custom handling for invisible chars
 *
 * See docs/input-content-filtering.md for specific rules applied here and reasoning.
 *
 */
import DOMPurify, { Config, UponSanitizeAttributeHookEvent } from "dompurify";
import { Window } from "happy-dom";

// Allowed HTML tags with their allowed attributes
const ALLOWED_TAGS: string[] = [
    "b",
    "blockquote",
    "br",
    "code",
    "em",
    "h1",
    "h2",
    "h3",
    "h4",
    "h5",
    "h6",
    "hr",
    "i",
    "li",
    "ol",
    "p",
    "pre",
    "strong",
    "sub",
    "sup",
    "table",
    "tbody",
    "td",
    "th",
    "thead",
    "tr",
    "ul",
    "img",
    "a",
];

const GLOBAL_ALLOWED_ATTRIBUTES = ["href", "src", "alt", "title"];
const ALLOWED_ATTRIBUTES: Record<string, string[]> = {
    img: ["src", "alt", "title"],
    a: ["href", "title"],
};

export const problemStatementXMLTags = [
    "comments",
    "issue_title",
    "issue_description",
    "pr_title",
    "pr_description",
    "pr_reviews",
    "pr_comments",
    "comment_thread",
    "comment_id",
    "comment_old",
    "comment_new",
    "thread_id",
    "commit",
    "original_commit",
    "file",
    "author",
];

// Initialize DOMPurify with a window object from happy-dom
const window = new Window();
const basePurifyConfig: Config = {
    ALLOWED_TAGS,
    ALLOW_DATA_ATTR: false,
    ALLOWED_ATTR: GLOBAL_ALLOWED_ATTRIBUTES,
    KEEP_CONTENT: true,
    RETURN_DOM: false,
    RETURN_DOM_FRAGMENT: false,
    WHOLE_DOCUMENT: false, // Don't require a full HTML document
    SANITIZE_DOM: true,
};

function uponSanitizeAttribute(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    currentNode: any,
    hookEvent: UponSanitizeAttributeHookEvent,
    _config: Config,
) {
    // Remove disallowed attributes from allowed tags
    const tag = currentNode.tagName.toLowerCase();
    if (!(tag in ALLOWED_ATTRIBUTES)) {
        hookEvent.forceKeepAttr = false;
        hookEvent.keepAttr = false; // Remove attributes from disallowed tags
        return;
    }
    const allowedAttrs = ALLOWED_ATTRIBUTES[tag];
    const attr = hookEvent.attrName.toLowerCase();
    if (!allowedAttrs.includes(attr)) {
        hookEvent.forceKeepAttr = false;
        hookEvent.keepAttr = false; // Remove attributes from disallowed tags
        return;
    }
    if ((tag === "img" && attr === "src") || (tag === "a" && attr === "href")) {
        try {
            // Only allow http/https URLs
            const url = URL.parse(hookEvent.attrValue);
            if (url && (url.protocol === "http:" || url.protocol === "https:")) {
                return;
            }
        } catch {
            // Fall through and omit the attribute if it can't be parsed
        }
        hookEvent.forceKeepAttr = false;
        hookEvent.keepAttr = false;
    }
    return;
}

export enum ContentFilterMode {
    None = "none",
    Markdown = "markdown",
    HiddenCharacters = "hidden_characters",
}

/**
 * Applies content filtering based on the specified filter mode.
 *
 * @param text The text to filter
 * @param filterMode The filter mode to apply (none, markdown, hidden_characters)
 * @returns Filtered text
 */
export function applyContentFilter(text: string, filterMode: ContentFilterMode): string {
    if (!text) {
        return text;
    }

    try {
        return filterContent(text, filterMode);
    } catch (error) {
        throw new Error(`Failed to apply content filter: ${error}`);
    }
}

/**
 * Takes in a string representing the filter mode and returns the corresponding ContentFilterMode enum value.
 *
 * @param filterVal The string value representing the filter mode (none, markdown, hidden_characters)
 * @returns ContentFilterMode enum value
 */
export function getContentFilterMode(filterVal: string): ContentFilterMode {
    switch (filterVal.toLowerCase()) {
        case "none":
            return ContentFilterMode.None;
        case "markdown":
            return ContentFilterMode.Markdown;
        case "hidden_characters":
            return ContentFilterMode.HiddenCharacters;
        default:
            return ContentFilterMode.HiddenCharacters;
    }
}

/**
 * Iterate through all JSON fields (including nested) and apply content filtering to string values.
 * This method mutates the input object in place.
 *
 * @param parsedJSON The parsed JSON object to filter
 * @param filterMode The filter mode to apply (none, markdown, hidden_characters)
 * @returns Filtered parsedJSON object
 */
export function filterAllJsonFields(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    parsedJson: any,
    filterMode: ContentFilterMode,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
): any {
    if (parsedJson === null || typeof parsedJson !== "object") {
        return parsedJson;
    }
    for (const [key, value] of Object.entries(parsedJson)) {
        if (typeof value === "string") {
            const filteredValue = applyContentFilter(value, filterMode);
            parsedJson[key] = filteredValue;
        } else if (typeof value === "object" && value !== null) {
            parsedJson[key] = filterAllJsonFields(value, filterMode);
        }
    }
    return parsedJson;
}

/**
 * Filter content based on a specified mode
 *
 * @param content The content to filter
 * @param filterMode The filter mode to apply (none, markdown, hidden_characters)
 * @param additionalAllowedTags Additional allowed HTML/XML tags for markdown mode
 * @returns Filtered content
 */
export function filterContent(
    content: string,
    filterMode?: ContentFilterMode,
    additionalAllowedTags: string[] = [],
): string {
    if (!content || filterMode === ContentFilterMode.None) {
        return content;
    }
    if (!filterMode) {
        filterMode = ContentFilterMode.Markdown;
    }
    switch (filterMode) {
        case ContentFilterMode.Markdown:
            return filterMarkdown(content, additionalAllowedTags);
        case ContentFilterMode.HiddenCharacters:
            return filterUnicode(content);
        default:
            throw new Error(`Unknown filter mode: ${filterMode}`);
    }
}

/**
 * Filter markdown by removing potentially malicious elements:
 * - Unicode characters that can hide directives or affect AI processing
 * - HTML comments outside code blocks
 * - Disallowed HTML tags and attributes (using DOMPurify)
 *
 * @param content The content to filter
 * @param additionalAllowedTags Additional allowed HTML/XML tags
 * @returns Filtered content
 */
export function filterMarkdown(content: string, additionalAllowedTags: string[] = []): string {
    if (!content) {
        return content;
    }
    const filteredContent = filterUnicode(content);
    // Split content by code blocks
    const parts = filteredContent.split(/(```.*?\n[\s\S\n]+?\n```)|(`[\s\S\n]+?`)/gm);
    let result = "";
    for (let i = 0; i < parts.length; i++) {
        const part = parts[i];
        if (!part) {
            continue;
        }
        if (part.startsWith("`") && part.endsWith("`")) {
            // This is a code block - keep it as is
            result += part;
        } else {
            result += filterHtml(part, additionalAllowedTags);
        }
    }

    return result;
}

/**
 * Filter disallowed HTML tags and attributes (using DOMPurify)
 *
 * @param content The HTML content to filter
 * @param additionalAllowedTags Additional allowed HTML/XML tags
 * @returns Filtered content
 */
export function filterHtml(content: string, additionalAllowedTags: string[] = []): string {
    const purify = DOMPurify(window);
    purify.addHook("uponSanitizeAttribute", uponSanitizeAttribute);
    purify.setConfig({
        ...basePurifyConfig,
        ALLOWED_TAGS: [...ALLOWED_TAGS, ...additionalAllowedTags],
    });
    return purify.sanitize(content);
}

/**
 * Filter invisible Unicode characters that directly affect AI processing
 *
 * @param text Text to process
 * @returns Text with invisible characters removed
 */
export function filterUnicode(text: string): string {
    // Normalize the string so that the unicode characters are decomposed
    let processed = text.normalize("NFD");
    let previous = "";
    // Remove invisible Unicode characters in the tag range (0xE0001, 0xE0020-0xE007F), BiDi range, and other concerning
    // hidden characters. Use a regex to remove them in a loop until the previous run and the current run match. One pass
    // does not always catch everything. You can use https://embracethered.com/blog/ascii-smuggler.html to check output for issues.
    do {
        previous = processed;
        processed = processed.replaceAll(
            /[\u{E0001}\u{E0020}-\u{E007F}\u{2066}-\u{2069}\u{202A}-\u{202E}\u{200B}\u{200C}\u{200E}\u{200F}\u{00AD}\u{FEFF}\u{180E}\u{2060}-\u{2064}]/gu,
            "",
        );
    } while (processed !== previous);
    // Now recompose the characters into the final output
    return processed.normalize("NFC");
}
