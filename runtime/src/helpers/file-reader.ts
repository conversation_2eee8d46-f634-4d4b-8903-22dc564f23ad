/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import * as fs from "fs";
import * as path from "path";

/**
 * Check if a path exists and if so what exists at that path.
 *
 * @param path The path to check
 *
 * @returns false if the path doesn't exist, "file" if it's a file, "directory" if it's a directory
 */
export async function pathExists(path: string): Promise<false | "file" | "directory"> {
    try {
        const stats = await fs.promises.stat(path);
        return stats.isDirectory() ? "directory" : "file";
    } catch {
        return false;
    }
}

/**
 * Reads content from a file if the input starts with '@', otherwise returns the input as-is.
 * @param input The input string that might start with '@' to indicate a file path
 * @returns The content of the file if input starts with '@', otherwise returns the input unchanged
 * @throws Error if the file doesn't exist or can't be read
 */
export function readFromFileIfNeeded(input: string | undefined): string | undefined {
    if (!input) return input;

    if (input.startsWith("@")) {
        const filePath = input.slice(1); // Remove the @ symbol
        try {
            // Resolve the path relative to current working directory
            const resolvedPath = path.resolve(process.cwd(), filePath);

            // Check if file exists
            if (!fs.existsSync(resolvedPath)) {
                throw new Error(`File not found: ${filePath}`);
            }

            // Read and return file contents
            return fs.readFileSync(resolvedPath, "utf-8").trim();
        } catch (error) {
            if (error instanceof Error) {
                throw new Error(`Failed to read file ${filePath}: ${error.message}`);
            }
            throw error;
        }
    }

    return input;
}

/**
 * Gets the file extension of the given path which is safe for telemetry.
 * - If the path is a directory, returns "directory".
 * - If the file extension is in the set of safe file extensions, returns the extension.
 * - If the file extension is not safe, returns "not-safe".
 * - In case of error (for example, the given path does not currently exist), returns "unknown".
 */
export function getSafeForTelemetryFileExtension(filePath: string): string | "directory" | "not-safe" | "unknown" {
    try {
        if (isDirectory(filePath) === true) {
            return "directory";
        }
        const extension = path.extname(filePath);
        return safeFileExtensions.has(extension) ? extension : "not-safe";
    } catch {
        return "unknown";
    }
}

export function isDirectory(path: string): boolean | "does-not-exist" | "unknown" {
    try {
        const stats = fs.statSync(path);
        return stats.isDirectory();
    } catch (error) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        if ((error as any).code === "ENOENT") {
            return "does-not-exist";
        }
    }
    return "unknown";
}

export const safeFileExtensions = new Set<string>([
    // Blank is of course safe.
    "",
    // Top 97% of text file extensions we've seen.
    ".ts",
    ".py",
    ".cs",
    ".tsx",
    ".md",
    ".js",
    ".json",
    ".go",
    ".php",
    ".yml",
    ".rb",
    ".html",
    ".java",
    ".dart",
    ".rs",
    ".swift",
    ".css",
    ".csproj",
    ".razor",
    ".kt",
    ".sh",
    ".vue",
    ".yaml",
    ".jsx",
    ".cpp",
    ".txt",
    ".gitignore",
    ".toml",
    ".sql",
    ".scss",
    ".ps1",
    ".cshtml",
    ".tf",
    ".xml",
    ".github",
    ".erb",
    ".svelte",
    ".h",
    ".c",
    ".example",
    ".mjs",
    ".astro",
    ".xaml",
    ".sln",
    ".env",
    ".bicep",
    ".gradle",
    ".ex",
    ".prisma",
    ".kts",
    ".scala",
    ".Tests",
    ".R",
    ".mod",
    ".props",
    ".ejs",
    ".j2",
    ".properties",
    ".lua",
    ".hpp",
    ".cls",
    ".vb",
    ".ipynb",
    ".mqh",
    ".exs",
    ".gs",
    ".ini",
    ".sol",
    ".pas",
    ".aspx",
    ".mdx",
    ".config",
    ".csv",
    ".m",
    // Common image extensions
    ".png",
    ".jpg",
    ".jpeg",
    ".gif",
    ".svg",
    ".ico",
    ".webp",
    ".bmp",
    ".tiff",
    // Common audio extensions
    ".mp3",
    ".wav",
    ".ogg",
    ".m4a",
    ".flac",
    // Common video extensions
    ".mp4",
    ".webm",
    ".avi",
    ".mov",
    ".mkv",
    // Common document extensions
    ".pdf",
    ".doc",
    ".docx",
    ".xls",
    ".xlsx",
    ".ppt",
    ".pptx",
    // Common archive extensions
    ".zip",
    ".rar",
    ".7z",
    ".tar",
    ".gz",
    // Common executable/binary extensions
    ".exe",
    ".dll",
    ".so",
    ".dylib",
    ".bin",
]);
