/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import * as fs from "fs";
import { existsSync } from "fs";
import { globSync } from "glob";
import * as path from "path";
import { filterMarkdown } from "./content-filter";

export type CustomInstructionsResult = {
    content: string;
    source: "copilot" | "claude" | "codex" | "gemini" | "vscode";
    sourcePath: string;
    additionalInstructions?: {
        content: string;
        source: string;
        sourcePath: string;
    };
};

export async function readCustomInstructions(repoPath: string): Promise<CustomInstructionsResult | undefined> {
    // Check for VS Code instructions folder first (additive to any primary instructions)
    const vscodeInstructions = await readVSCodeInstructions(repoPath);
    const additionalInstructions = vscodeInstructions
        ? {
              content: vscodeInstructions.content,
              source: "vscode",
              sourcePath: ".github/instructions",
          }
        : undefined;

    // First try to read Copilot instructions
    const copilotResult = hasCopilotInstructions(repoPath);
    if (copilotResult.exists) {
        const instructions = await fs.promises.readFile(copilotResult.path, "utf-8");
        return {
            content: filterMarkdown(instructions),
            source: "copilot",
            sourcePath: ".github/copilot-instructions.md",
            additionalInstructions: additionalInstructions,
        };
    }

    // Fallback to Claude instructions (preferred over Codex)
    const claudeResult = hasClaudeCustomInstructions(repoPath);
    if (claudeResult.exists) {
        const instructions = await fs.promises.readFile(claudeResult.path, "utf-8");
        return {
            content: filterMarkdown(instructions),
            source: "claude",
            sourcePath: "CLAUDE.md",
            additionalInstructions: additionalInstructions,
        };
    }

    // Fallback to Codex instructions
    const codexResult = hasCodexCustomInstructions(repoPath);
    if (codexResult.exists) {
        const instructions = await fs.promises.readFile(codexResult.path, "utf-8");
        return {
            content: filterMarkdown(instructions),
            source: "codex",
            sourcePath: "AGENTS.md",
            additionalInstructions: additionalInstructions,
        };
    }

    // Fallback to Gemini instructions
    const geminiResult = hasGeminiCustomInstructions(repoPath);
    if (geminiResult.exists) {
        const instructions = await fs.promises.readFile(geminiResult.path, "utf-8");
        return {
            content: filterMarkdown(instructions),
            source: "gemini",
            sourcePath: "GEMINI.md",
            additionalInstructions: additionalInstructions,
        };
    }

    // If only VS Code instructions exist
    if (additionalInstructions) {
        return vscodeInstructions;
    }

    // No custom instructions found
    return undefined;
}

/**
 * Checks if the cloned repository has an OpenAI Codex custom instructions
 * file (`AGENTS.md`).
 *
 * @param repoPath The path to the cloned repository
 * @returns Object with exists flag and path if found
 */
export function hasCodexCustomInstructions(
    repoPath: string,
): { exists: true; path: string } | { exists: false; path: undefined } {
    const instructionsFile = path.join(repoPath, "AGENTS.md");
    const exists = existsSync(instructionsFile);
    return exists ? { exists: true, path: instructionsFile } : { exists: false, path: undefined };
}

/**
 * Checks if the cloned repository has a Claude custom instructions
 * file (`CLAUDE.md`).
 *
 * @param repoPath The path to the cloned repository
 * @returns Object with exists flag and path if found
 */
export function hasClaudeCustomInstructions(
    repoPath: string,
): { exists: true; path: string } | { exists: false; path: undefined } {
    const instructionsFile = path.join(repoPath, "CLAUDE.md");
    const exists = existsSync(instructionsFile);
    return exists ? { exists: true, path: instructionsFile } : { exists: false, path: undefined };
}

/**
 * Checks if the cloned repository has a Gemini custom instructions
 * file (`GEMINI.md`).
 *
 * @param repoPath The path to the cloned repository
 * @returns Object with exists flag and path if found
 */
export function hasGeminiCustomInstructions(
    repoPath: string,
): { exists: true; path: string } | { exists: false; path: undefined } {
    const instructionsFile = path.join(repoPath, "GEMINI.md");
    const exists = existsSync(instructionsFile);
    return exists ? { exists: true, path: instructionsFile } : { exists: false, path: undefined };
}

/**
 * Checks if the cloned repository has a custom instructions (`.github/copilot-instructions.md`) file.
 *
 * @param repoPath The path to the cloned repository
 * @returns Object with exists flag and path if found
 */
export function hasCopilotInstructions(
    repoPath: string,
): { exists: true; path: string } | { exists: false; path: undefined } {
    const instructionsFile = path.join(repoPath, ".github", "copilot-instructions.md");
    const exists = existsSync(instructionsFile);
    return exists ? { exists: true, path: instructionsFile } : { exists: false, path: undefined };
}

/**
 * Checks if the cloned repository has a Copilot setup steps (`.github/workflows/copilot-setup-steps.yml`
 * or `.github/workflows/copilot-setup-steps.yaml`) file.
 *
 * @param repoPath The path to the cloned repository
 * @returns True if any of the files exist, false otherwise
 */
export function hasCopilotSetupSteps(repoPath: string): boolean {
    const setupStepsFiles = [
        path.join(repoPath, ".github", "workflows", "copilot-setup-steps.yml"),
        path.join(repoPath, ".github", "workflows", "copilot-setup-steps.yaml"),
    ];

    return setupStepsFiles.some((file) => existsSync(file));
}

/**
 * Checks if the cloned repository has at least one Visual Studio Code
 * instructions file (`.github/instructions/…/*.instructions.md`).
 *
 * @param repoPath The path to the cloned repository
 * @returns True if at least one instructions file exists, false otherwise
 */
export function hasAtLeastOneGitHubInstructionsMd(repoPath: string): boolean {
    const instructionsDir = path.join(repoPath, ".github", "instructions");

    if (!existsSync(instructionsDir)) {
        return false;
    }

    try {
        const pattern = path.join(instructionsDir, "**", "*.instructions.md");
        const matches = globSync(pattern);
        return matches.length > 0;
    } catch {
        // If there's an error reading the directory, assume no files exist
        return false;
    }
}

export function hasClaudeHooks(repoPath: string): boolean {
    const hasClaudeSettingsResults = hasClaudeSettings(repoPath);
    if (!hasClaudeSettingsResults.exists) {
        return false;
    }
    const claudeHookEvents = ["PreToolUse", "PostToolUse", "Notification", "Stop"];
    const settingsPath = hasClaudeSettingsResults.path;
    try {
        const settingsContent = fs.readFileSync(settingsPath, "utf-8");
        const settings = JSON.parse(settingsContent);
        if (!settings || !settings.hooks || typeof settings.hooks !== "object") {
            return false;
        }
        const hookKeys = Object.keys(settings.hooks);
        return claudeHookEvents.some(
            (key) => hookKeys.includes(key) && Array.isArray(settings.hooks[key]) && settings.hooks[key].length > 0,
        );
    } catch {
        // If there's an error reading or parsing the settings, assume no hooks exist
        return false;
    }
}

/**
 * Checks if the cloned repository has a Claude settings file (`.claude/settings.json`).
 *
 * @param repoPath The path to the cloned repository
 * @returns Object with exists flag and path if found
 */
export function hasClaudeSettings(
    repoPath: string,
): { exists: true; path: string } | { exists: false; path: undefined } {
    const settingsFile = path.join(repoPath, ".claude", "settings.json");
    const exists = existsSync(settingsFile);
    return exists ? { exists: true, path: settingsFile } : { exists: false, path: undefined };
}

export type VSCodeInstructionFile = {
    path: string;
    content: string;
    metadata?: {
        applyTo?: string;
        description?: string;
    };
};

/**
 * Reads and processes VS Code instructions from .github/instructions folder.
 * This follows VS Code's approach to automatic instructions handling.
 */
export async function readVSCodeInstructions(repoPath: string): Promise<CustomInstructionsResult | undefined> {
    // Use existing detection logic first
    if (!hasAtLeastOneGitHubInstructionsMd(repoPath)) {
        return undefined;
    }

    try {
        const instructionsDir = path.join(repoPath, ".github", "instructions");
        const pattern = path.join(instructionsDir, "**", "*.instructions.md");
        const matches = globSync(pattern);

        const instructionFiles: VSCodeInstructionFile[] = [];

        for (const filePath of matches) {
            const content = await fs.promises.readFile(filePath, "utf-8");
            const metadata = parseInstructionMetadata(content);
            const relativePath = path.relative(repoPath, filePath);

            instructionFiles.push({
                path: relativePath,
                content: filterMarkdown(content),
                metadata,
            });
        }

        // Build the instructions content with table
        const instructionsContent = buildInstructionsTable(instructionFiles);

        return {
            content: instructionsContent,
            source: "vscode",
            sourcePath: ".github/instructions",
        };
    } catch {
        // If there's an error reading the directory, return undefined
        return undefined;
    }
}

/**
 * Parses instruction file metadata from frontmatter.
 * Extracts applyTo from frontmatter and uses remaining content as description.
 */
function parseInstructionMetadata(content: string): {
    applyTo?: string;
    description?: string;
} {
    // Check for frontmatter
    const frontmatterMatch = content.match(/^---\s*\n([\s\S]*?)\n---\s*\n([\s\S]*)/);
    if (frontmatterMatch) {
        const frontmatter = frontmatterMatch[1];
        const remainingContent = frontmatterMatch[2].trim();

        const applyToMatch = frontmatter.match(/applyTo:\s*(.+)/);
        const applyTo = applyToMatch ? applyToMatch[1].trim().replace(/['"]/g, "") : undefined;

        return {
            applyTo,
            description: remainingContent,
        };
    }

    // No frontmatter - entire content is description
    return {
        description: content.trim(),
    };
}

/**
 * Builds instruction content with a table of available instruction files.
 * This follows VS Code's approach to embedding instruction tables.
 */
function buildInstructionsTable(instructionFiles: VSCodeInstructionFile[]): string {
    const tableEntries: string[] = [];
    const generalInstructions: string[] = [];

    for (const file of instructionFiles) {
        const { path: filePath, content, metadata } = file;
        const applyTo = metadata?.applyTo;
        const description = metadata?.description ?? "";

        if (applyTo && applyTo !== "**" && applyTo !== "**/*" && applyTo !== "*") {
            // File has specific patterns - add to table
            tableEntries.push(`| ${applyTo} | '${filePath}' | ${description} |`);
        } else {
            // General instructions or wildcard patterns - include content directly
            generalInstructions.push(content);
        }
    }

    let result = "";

    // Add general instructions first
    if (generalInstructions.length > 0) {
        result += generalInstructions.join("\n\n") + "\n\n";
    }

    // Add table of pattern-specific instructions
    if (tableEntries.length > 0) {
        result += [
            "Here is a list of instruction files that contain rules for modifying or creating new code.",
            "These files are important for ensuring that the code is modified or created correctly.",
            "Please make sure to follow the rules specified in these files when working with the codebase.",
            "If you have not already read the file, use the `view` command of the `str_replace_editor` tool to acquire it.",
            "Make sure to acquire the instructions before making any changes to the code.",
            "| Pattern | File Path | Description |",
            "| ------- | --------- | ----------- |",
            ...tableEntries,
        ].join("\n");
    }

    return result.trim();
}
