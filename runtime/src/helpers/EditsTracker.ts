/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { hashString } from "./crypto-helpers";
import { getSafeForTelemetryFileExtension } from "./file-reader";

export type TrackedEdit = {
    /**
     * A unique identifier for this edit among all other edits for the same path.
     *
     * If not provided, then defaults to the index of the edit for edits for that path.
     */
    id: string;
    /**
     * Some command that was used to edit or as part of the process
     * of editing a file.
     */
    command: string;
    result: "success" | "failure";
    /**
     * If preceding this edit there was a sequence of one or more failed edits, then
     * how long that sequence was. If the prior edit was a success, then this will be 0.
     */
    lenPriorFailureSequence: number;
};

export type PathEditsTracking = {
    edits: TrackedEdit[];
    numEditsByCommand: { [command: string]: number };
    numFailedEdits: number;
    numSuccessfulEdits: number;
    wasLastEditSuccessful: boolean;
    fileExtension: string | "not-safe" | "directory";
};

export class EditsTracker {
    private perPathEditsTracking: Map<string, PathEditsTracking>;

    constructor() {
        this.perPathEditsTracking = new Map<string, PathEditsTracking>();
    }

    public reset(): void {
        this.perPathEditsTracking.clear();
    }

    /**
     * Track an edit to a file or directory. If the tracking the edit is successful, it will return the
     * key used to track edits for that path. If not, returns `undefined`.
     */
    public trackEdit(
        path: string,
        command: string,
        result: "success" | "failure",
        editId?: string,
    ): string | undefined {
        const hashedPath = hashString(path);
        const fileExtension = getSafeForTelemetryFileExtension(path);
        if (!this.perPathEditsTracking.has(hashedPath)) {
            this.perPathEditsTracking.set(hashedPath, {
                edits: [],
                numEditsByCommand: {},
                numFailedEdits: 0,
                numSuccessfulEdits: 0,
                wasLastEditSuccessful: false,
                fileExtension: fileExtension,
            });
        }

        const tracking = this.perPathEditsTracking.get(hashedPath)!;
        // Update the file extension if it has changed. Most likely to happen if the first edit was a failure because
        // the path did not exist yet, and then the path was created, thus now allowing us to know the extension.
        tracking.fileExtension = fileExtension;

        const lastEdit = tracking.edits[tracking.edits.length - 1];
        const lenPriorFailureSequence =
            lastEdit && lastEdit.result === "failure" ? lastEdit.lenPriorFailureSequence + 1 : 0;
        tracking.edits.push({
            id: editId || tracking.edits.length.toString(),
            command: command,
            result: result,
            lenPriorFailureSequence: lenPriorFailureSequence,
        });

        if (tracking.numEditsByCommand[command]) {
            tracking.numEditsByCommand[command]++;
        } else {
            tracking.numEditsByCommand[command] = 1;
        }

        if (result === "success") {
            tracking.numSuccessfulEdits++;
            tracking.wasLastEditSuccessful = true;
        } else {
            tracking.numFailedEdits++;
            tracking.wasLastEditSuccessful = false;
        }

        return hashedPath;
    }

    public getTrackedEditsForPath(key: string): PathEditsTracking | undefined {
        const tracking = this.perPathEditsTracking.get(key);
        if (!tracking) {
            return undefined;
        }
        // Return a copy, not the original value objects & arrays
        return JSON.parse(JSON.stringify(tracking));
    }

    public getTrackedEdits(): { [hashedPath: string]: PathEditsTracking } {
        // Return a copy, not the original value objects & arrays
        return JSON.parse(JSON.stringify(Object.fromEntries(this.perPathEditsTracking.entries())));
    }

    /**
     * Returns a stringified JSON array of all tracked edits, in the shape:
     * (PathEditsTracking & { hashedPath: string; })[]
     */
    public getTrackedEditsJsonArrString(): string {
        return JSON.stringify(
            Array.from(this.perPathEditsTracking.entries()).map(([hashedPath, tracking]) => ({
                ...tracking,
                hashedPath: hashedPath,
            })),
        );
    }
}
