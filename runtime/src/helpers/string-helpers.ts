/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

// Convert a string or an object to a string
// @param value The value to convert
// @param spaces The number of spaces to use for indentation if the value is an object
// @returns The string representation of the value
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function stringOrStringify(value: any, spaces: string | number) {
    return typeof value === "string" ? value : JSON.stringify(value, null, spaces);
}

// Indent lines in a string by a specified number of spaces
// @param input The input string in which to indent lines
// @param spaces The number of spaces to use for indentation
// @returns The indented string
export function indentLines(input: string | undefined, spaces: number): string | undefined {
    const prefix = " ".repeat(spaces);
    return prefixLines(input, prefix);
}

// Prefix lines in a string by a specified string
// @param input The input string in which to prefix lines
// @param indent The string to use for indentation
// @returns The indented string
export function prefixLines(input: string | undefined, prefix: string): string | undefined {
    // Deal with the falsy input
    if (!input) {
        return input === "" ? prefix : undefined;
    }

    // Remember if the input starts or ends with an LF
    // And... remove it so we won't accidentally add the prefix at the end
    const tailHasLF = input.endsWith("\n");
    if (tailHasLF) {
        input = input.slice(0, -1);
    }

    // Split the input into lines and prefix each line
    const lines = input.split("\n");
    const prefixed = lines.map((line) => prefix + line);

    // Join the prefixed lines back together
    // And... add an LF at the end if it was there before
    let newVaue = prefixed.join("\n");
    if (tailHasLF) {
        newVaue += "\n";
    }

    return newVaue;
}

// Format a string for YAML output
// @param value The value to format
// @param indentSpaces The number of spaces to use for indentation
// @returns The formatted string
export function yamlFormatStringRhs(value: string | undefined, indentSpaces: number = 2): string {
    // If it's undefined, or empty, return it as 'null' or as is
    if (!value) {
        return value === undefined ? "null" : value;
    }

    // If it has no LFs, return it as is
    if (!value.includes("\n")) {
        return value;
    }

    // Otherwise we need to indent it
    return "|\n" + indentLines(value, indentSpaces)!;
}

export function surroundWithLFs(input: string | undefined): string | undefined {
    // Deal with the falsy input
    if (!input) {
        return input === "" ? "\n" : undefined;
    }

    // Remember if the input starts or ends with an LF
    const headHasLF = input.startsWith("\n");
    const tailHasLF = input.endsWith("\n");

    // Add LFs at the start and end if they are not there
    if (!headHasLF) {
        input = "\n" + input;
    }
    if (!tailHasLF) {
        input += "\n";
    }

    return input;
}

/**
 * Escapes special regex characters in a string.
 */
function escapeRegex(str: string): string {
    return str.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
}

/**
 * Replaces one occurrence of oldStr with newStr in text.
 * It first tries an exact match (using indexOf) and, if not found, attempts a fuzzy match.
 * Returns an object with the new text, a type ('exact' or 'fuzzy') or null on error,
 * and a message.
 */
export function findAndReplaceOne(
    text: string,
    oldStr: string,
    newStr: string,
): { text: string; type: "exact" | "fuzzy" | "multiple" | "none" } {
    // Try exact match first using indexOf.
    const firstExactIdx = text.indexOf(oldStr);
    if (firstExactIdx !== -1) {
        // Check for multiple exact occurrences.
        const secondExactIdx = text.indexOf(oldStr, firstExactIdx + oldStr.length);
        if (secondExactIdx !== -1) {
            return { text, type: "multiple" };
        }
        // Exactly one exact match found.
        const replaced = text.slice(0, firstExactIdx) + newStr + text.slice(firstExactIdx + oldStr.length);
        return { text: replaced, type: "exact" };
    }

    // No exact match, so try fuzzy match.
    // Normalize the search string: convert CRLF to LF
    let normalizedOld = oldStr.replace(/\r\n/g, "\n");

    // Trim the last LF if it exists
    // This is important for fuzzy matching, as we want to match the last line
    const hasTrailingLF = normalizedOld.endsWith("\n");
    if (hasTrailingLF) {
        normalizedOld = normalizedOld.slice(0, -1);
    }

    // Build a regex pattern where each line is matched exactly
    // but allows for trailing spaces/tabs and flexible newline formats.
    const lines = normalizedOld.split("\n");
    const pattern = lines
        .map((line, i) => {
            const escaped = escapeRegex(line);
            return i < lines.length - 1 || hasTrailingLF ? `${escaped}[ \\t]*\\r?\\n` : `${escaped}[ \\t]*`;
        })
        .join("");
    const regex = new RegExp(pattern, "g");

    const matches = Array.from(text.matchAll(regex));
    if (matches.length === 0) {
        return { text, type: "none" };
    }
    if (matches.length > 1) {
        return { text, type: "multiple" };
    }

    // Exactly one fuzzy match found.
    const match = matches[0];
    const startIdx = match.index || 0;
    const endIdx = startIdx + match[0].length;
    const replaced = text.slice(0, startIdx) + newStr + text.slice(endIdx);
    return { text: replaced, type: "fuzzy" };
}

export function surroundMultiLineWithLFs(input: string | undefined): string | undefined {
    // If it doesn't have LFs, just return it as-is
    const hasLFs = input && input.includes("\n");
    if (!hasLFs) {
        return input;
    }

    // Otherwise, surround it with LFs
    return surroundWithLFs(input);
}

const defaultBytesPrecision = 2;
const sizeUnits = ["B", "KiB", "MiB", "GiB", "TiB", "PiB", "EiB", "ZiB", "YiB"];

export function formatBytes(bytes: number, precision: number = defaultBytesPrecision): string {
    const divisor = 1024.0;

    let index = 0;
    while (bytes >= divisor && index < sizeUnits.length - 1) {
        bytes /= divisor;
        index++;
    }

    const precisionToUse = index === 0 ? 0 : Math.max(precision, 0);
    return `${bytes.toFixed(precisionToUse)} ${sizeUnits[index]}`;
}
