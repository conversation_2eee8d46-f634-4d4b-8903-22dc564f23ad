/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import * as os from "os";
import * as path from "path";
import { execSync } from "child_process";

/**
 * Converts a path to an absolute path, handling platform-specific cases
 * Works on Windows (WSL + Git Bash), Mac, and Linux
 *
 * @param inputPath The path to convert to absolute
 * @returns An absolute path
 */
export function getAbsolutePath(inputPath: string): string {
    // Early return if already absolute
    if (path.isAbsolute(inputPath)) {
        return inputPath;
    }

    // Expand ~ to home directory
    if (inputPath.startsWith("~")) {
        return path.normalize(inputPath.replace("~", os.homedir()));
    }

    // Handle . and ..
    return path.resolve(inputPath);
}

/**
 * Finds the root of a git repository using git rev-parse --show-toplevel
 * If not in a git repository, returns the start path with found: false.
 *
 * @param startPath The path to start searching from (defaults to current working directory)
 * @returns An object with the git root path and whether a git repo was found
 */
export function findGitRoot(startPath: string = process.cwd()): {
    gitRoot: string;
    found: boolean;
} {
    try {
        // Try using git rev-parse --show-toplevel first
        const gitRoot = execSync("git rev-parse --show-toplevel", {
            cwd: startPath,
            encoding: "utf8",
            timeout: 5000, // 5 second timeout to prevent hanging
            maxBuffer: 1024 * 1024, // 1MB buffer
        }).trim();

        if (gitRoot) {
            return { gitRoot, found: true };
        }
    } catch (_error) {
        // Git command failed, not in a git repository
    }

    // If no git repository is found, return the original start path
    return { gitRoot: startPath, found: false };
}
