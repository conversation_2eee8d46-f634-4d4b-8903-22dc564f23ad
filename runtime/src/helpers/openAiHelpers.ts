/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import Anthropic from "@anthropic-ai/sdk";
import type { ChatCompletion, ChatCompletionChunk } from "openai/resources/chat/completions";
import { CopilotChatCompletionMessage } from "../model/capi/types";

export function cleanUpMessage(message: CopilotChatCompletionMessage): CopilotChatCompletionMessage {
    // strip out <|im_sep|> and **header info** from reasoning summarizers if present.
    const formattedReasoningMessage = message.reasoning_text?.replace(/<\|im_sep\|>(\*\*.*?\*\*)?/g, "\n")?.trim();
    return {
        ...message,
        reasoning_text: formattedReasoningMessage,
    };
}

export function completionToChunk(completion: ChatCompletion): ChatCompletionChunk {
    return {
        ...completion,
        choices: completion.choices.map((c) => {
            return {
                ...c,
                message: undefined,
                delta: {
                    ...cleanUpMessage(c.message),
                    function_call: undefined,
                    tool_calls: c.message.tool_calls?.map((tc) => {
                        return {
                            ...tc,
                            index: 0,
                        };
                    }),
                },
            };
        }),
        object: "chat.completion.chunk",
    };
}

export function toolResultToChunkFromAnthropicMessage(
    sessionLog: string,
    message: Anthropic.Message,
): ChatCompletionChunk {
    return {
        id: message.id,
        created: Math.floor(Date.now() / 1000),
        model: message.model,
        choices: message.content
            .filter((c) => c.type === "tool_use")
            .map((c) => {
                const toolUse = c as Anthropic.ToolUseBlock;
                return {
                    index: 0,
                    finish_reason: "tool_calls",
                    delta: {
                        tool_calls: [
                            {
                                index: 0,
                                id: toolUse.id,
                                function: {
                                    name: toolUse.name,
                                    arguments: JSON.stringify(toolUse.input),
                                },
                            },
                        ],
                        content: sessionLog,
                    },
                };
            }),
        object: "chat.completion.chunk",
    };
}

/**
 * Creates a chat completion chunk with content being the session log representation of one of the original completion chunk's tool call's result.
 *
 * @param sessionLog - The session log content to be included in the chunk.
 * @param completionChunk - The original chat completion chunk from which to derive the chunk.
 * @param toolCallId - The tool call which `sessionLog` represents the result for.
 */
export function createChunkWithSessionLogToolResult(
    sessionLog: string,
    completionChunk: ChatCompletionChunk,
    toolCallId: string,
): ChatCompletionChunk {
    return {
        id: completionChunk.id,
        created: Math.floor(Date.now() / 1000),
        model: completionChunk.model,
        choices: completionChunk.choices
            .filter((c) => c.delta.tool_calls)
            .map((c) => {
                return {
                    index: c.index,
                    finish_reason: "tool_calls" as const,
                    delta: {
                        tool_calls: c.delta.tool_calls
                            // filter to only include the tool call with the specified ID
                            ?.filter((tc) => tc.id === toolCallId)
                            ?.map((tc) => {
                                return {
                                    ...tc,
                                    index: 0,
                                };
                            }),
                        content: sessionLog,
                    },
                };
            })
            // get rid of any choices which did not end up having tool calls
            .filter((c) => c.delta.tool_calls && c.delta.tool_calls.length > 0),
        object: "chat.completion.chunk",
    };
}

export function anthropicMessageToChunk(message: Anthropic.Message): ChatCompletionChunk {
    const choices = message.content
        .map((block, index) => {
            if (block.type === "text") {
                return {
                    delta: {
                        content: block.text,
                        role: message.role,
                    },
                    finish_reason: mapAnthropicFinishReasonToOpenAI(message.stop_reason),
                    index,
                    logprobs: null,
                };
            } else if (block.type === "tool_use") {
                return {
                    delta: {
                        tool_calls: [
                            {
                                index: 0,
                                id: block.id,
                                type: "function" as const,
                                function: {
                                    name: block.name,
                                    arguments: JSON.stringify(block.input),
                                },
                            },
                        ],
                        role: message.role,
                    },
                    finish_reason: mapAnthropicFinishReasonToOpenAI(message.stop_reason),
                    index,
                    logprobs: null,
                };
            } else if (block.type === "thinking") {
                return {
                    delta: {
                        content: block.thinking,
                        role: message.role,
                    },
                    finish_reason: mapAnthropicFinishReasonToOpenAI(message.stop_reason),
                    index,
                    logprobs: null,
                };
            } else if (block.type === "redacted_thinking") {
                return {
                    delta: {
                        content: block.data,
                        role: message.role,
                    },
                    finish_reason: mapAnthropicFinishReasonToOpenAI(message.stop_reason),
                    index,
                    logprobs: null,
                };
            }
            return null;
        })
        .filter((choice): choice is NonNullable<typeof choice> => choice !== null);

    return {
        id: message.id,
        model: message.model,
        object: "chat.completion.chunk",
        created: Math.floor(Date.now() / 1000),
        choices: choices,
    };
}

function mapAnthropicFinishReasonToOpenAI(
    stopReason: "end_turn" | "stop_sequence" | "tool_use" | "max_tokens" | null,
): "length" | "tool_calls" | "stop" | "content_filter" | "function_call" | null {
    switch (stopReason) {
        case "max_tokens":
            return "length";
        case "tool_use":
            return "tool_calls";
        case "end_turn":
        case "stop_sequence":
        case null:
        default:
            return "stop";
    }
}
