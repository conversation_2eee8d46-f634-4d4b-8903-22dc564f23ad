/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import Anthropic from "@anthropic-ai/sdk";

import { ContentBlock, TextBlock } from "@anthropic-ai/sdk/resources";
import { ChatCompletionMessage, ChatCompletionMessageParam, ChatCompletionToolChoiceOption } from "openai/resources";
import { RunnerLogger } from "../../runner";
import { RuntimeSettings } from "../../settings";
import { demandAnthropicApiKey } from "../../settings/demand";
import { createToolSet, Tool, Tool<PERSON>allback } from "../../tools";
import { CopilotChatCompletionMessage, CopilotChatCompletionMessageParam } from "../capi/types";
import type { Client, ClientOptions, GetCompletionWithToolsOptions } from "../client";
import type { Event } from "../event";

export type Section = { name: string; text: string };
export type CompletionSectionsResult = AsyncGenerator<Section>;

export class AIClient implements Client {
    private client?: Anthropic;
    public readonly model: string;

    constructor(
        private readonly settings: RuntimeSettings,
        private readonly logger: RunnerLogger,
        private readonly options?: ClientOptions,
    ) {
        this.model = options?.model || "claude-3-5-sonnet-20241022";
        logger.info(" ");
        logger.info(`Using model: ${this.model}`);
        if (options?.thinkingMode) {
            logger.info("Thinking mode enabled");
        }
    }

    public async *getCompletionWithTools(
        systemMessage: string,
        initialMessages: ChatCompletionMessageParam[],
        tools: Tool[],
        options?: GetCompletionWithToolsOptions,
    ): AsyncGenerator<Event> {
        if (!this.client) {
            this.client = await this.initializeClient();
        }
        const messages = initialMessages.map(toAnthropicMessageParam);

        const toolSet = createToolSet(tools);
        let iterations = 0;
        let lastThinkingBlock: Anthropic.ThinkingBlock | undefined;
        while (true) {
            // TODO[#134]: Improve truncation logic:
            // * Use token length instead of iterations
            // * Ask the LLM to do the summarization to make sure the most important learnings are retained
            if (iterations > 0 && iterations % 20 === 0 && messages.length > initialMessages.length + 10) {
                const bytesBefore = JSON.stringify(messages).length;
                messages.splice(initialMessages.length, 10);
                const bytesAfter = JSON.stringify(messages).length;
                this.logger.info(`truncated messages: ${bytesBefore} -> ${bytesAfter}`);
            }
            iterations++;

            const messageParams: Anthropic.MessageCreateParams = {
                max_tokens: 1024 * 8,
                model: this.model,
                // extended thinking is not compatible with temperature. Requires it to be 1
                // https://docs.anthropic.com/en/docs/build-with-claude/extended-thinking#important-considerations-when-using-extended-thinking
                temperature: this.options?.thinkingMode ? 1 : 0.1,
                system: [
                    {
                        type: "text",
                        text: systemMessage,
                        cache_control: { type: "ephemeral" },
                    },
                ],
                messages,
                // The `cache_control` flag is applied only to the last tool to ensure that its results are ephemeral.
                // This is a business rule that aligns with the expected behavior of the Anthropic API.
                tools: tools.map((tool, index) => ({
                    name: tool.name,
                    description: tool.description,
                    input_schema: tool.input_schema,
                    ...(index === tools.length - 1 ? { cache_control: { type: "ephemeral" } } : {}),
                })),
                thinking: this.options?.thinkingMode
                    ? {
                          type: "enabled",
                          budget_tokens: 1024 * 4,
                      }
                    : undefined,
                tool_choice: options?.toolChoice ? toAnthropicToolChoiceOption(options.toolChoice) : undefined,
            };
            let response;
            try {
                response = await this.client.messages.create(messageParams);
            } catch (error) {
                // Check if it's a rate limit error using Anthropic's error type
                if (error instanceof Anthropic.RateLimitError) {
                    this.logger.info("Rate limit reached, waiting 1 minute before retrying...");
                    // Wait for 1 minute
                    await new Promise((resolve) => setTimeout(resolve, 60 * 1000));
                    // Retry the request
                    response = await this.client.messages.create(messageParams);
                } else {
                    // print the last messages before error
                    this.logger.debug("Last messages before error: ");
                    this.logger.debug(JSON.stringify(messages, null, 2));

                    // Re-throw other errors
                    throw error;
                }
            }
            this.logger.debug(JSON.stringify(response, null, 2));

            const thinkingBlock = response.content.find(
                (block) => block.type === "thinking",
            ) as Anthropic.ThinkingBlock;

            // due to truncation, it's possible that the thinking block is lost as only the first turn in sonnet thinking has
            // thinking. This causes an error if it happens. We will track the last thinking block to every assistant message
            // that does not have it as recommended by the error
            // "a final `assistant` message must start with a thinking block (preceeding the lastmost set of `tool_use` and `tool_result` blocks). We recommend you include thinking blocks from previous turns. "
            if (thinkingBlock) {
                lastThinkingBlock = thinkingBlock;
            } else {
                if (lastThinkingBlock) {
                    response.content.unshift(lastThinkingBlock);
                }
            }

            yield {
                kind: "response",
                response: toChatCompletionMessage(response),
            };
            switch (response.stop_reason) {
                case "max_tokens":
                    this.logger.warning("Max tokens reached");
                    return;
                case "end_turn":
                    return;
                case "tool_use":
                    messages.push({
                        role: "assistant",
                        content: removeEmptyTextBlocks(response.content),
                    });
                    for (const message of toChatCompletionMessageParam(messages[messages.length - 1])) {
                        yield { kind: "message", message };
                    }
                    for (const message of response.content) {
                        switch (message.type) {
                            case "text":
                                break;
                            case "tool_use": {
                                const toolName = message.name;
                                const toolCallback = toolSet[toolName]?.callback as ToolCallback;
                                if (!toolCallback) {
                                    throw new Error("invalid tool: " + toolName);
                                }
                                try {
                                    const toolResult = await toolCallback(message.input);
                                    const output =
                                        typeof toolResult === "string" ? toolResult : toolResult.textResultForLlm;
                                    messages.push({
                                        role: "user",
                                        content: [
                                            {
                                                type: "tool_result",
                                                tool_use_id: message.id,
                                                content: output,
                                            },
                                        ],
                                    });
                                    for (const message of toChatCompletionMessageParam(messages[messages.length - 1])) {
                                        yield { kind: "message", message };
                                    }
                                } catch (err) {
                                    const result: Anthropic.MessageParam = {
                                        role: "user",
                                        content: [
                                            {
                                                type: "tool_result",
                                                tool_use_id: message.id,
                                                is_error: true,
                                                content: String(err),
                                            },
                                        ],
                                    };
                                    messages.push(result);
                                    for (const message of toChatCompletionMessageParam(result)) {
                                        yield { kind: "message", message };
                                    }
                                    this.logger.debug(JSON.stringify(result, null, 2));
                                }
                                break;
                            }
                        }
                    }
                    continue;
                default:
                    throw new Error("Unexpected stop reason: " + response.stop_reason);
            }
            return;
        }
    }

    private async initializeClient(): Promise<Anthropic> {
        const anthropicApiKey = demandAnthropicApiKey(this.settings);

        this.logger.debug(`Initializing Anthropic client...`);
        this.client = new Anthropic({
            apiKey: anthropicApiKey,
            maxRetries: 4,
        });
        return this.client;
    }
}

function toAnthropicMessageParam(
    chatCompletion: ChatCompletionMessageParam | ChatCompletionMessage,
): Anthropic.MessageParam {
    if ("tool_calls" in chatCompletion && chatCompletion.tool_calls) {
        return {
            role: "assistant",
            content: chatCompletion.tool_calls.map((c) => ({
                type: "tool_use",
                id: c.id,
                name: c.function.name,
                input: JSON.parse(c.function.arguments),
            })),
        };
    }

    switch (chatCompletion.role) {
        case "tool":
            return {
                role: "user",
                content: [
                    {
                        type: "tool_result",
                        tool_use_id: chatCompletion.tool_call_id,
                        content: chatCompletion.content,
                    },
                ],
            };
        default:
            return {
                role: chatCompletion.role === "assistant" ? "assistant" : "user",
                content:
                    typeof chatCompletion.content === "string"
                        ? chatCompletion.content
                        : (chatCompletion.content?.map((c) => {
                              return c.type === "text" ? c : { type: "text", text: "" }; // only text is supported for now
                          }) ?? []),
            };
    }
}

function toChatCompletionMessageParam(message: Anthropic.MessageParam): CopilotChatCompletionMessageParam[] {
    if (typeof message.content === "string") {
        return [{ role: message.role, content: message.content }];
    }

    // Extract thinking block and tool use block
    const thinkingBlock = message.content.find((block) => block.type === "thinking");

    if (message.content.find((c) => c.type === "tool_result")) {
        return [
            {
                role: "tool",
                content: message.content
                    .map((c) => {
                        const toolResult = c as Anthropic.ToolResultBlockParam;
                        return toolResult.content;
                    })
                    .join(" "),
                tool_call_id:
                    (message.content.find((c) => c.type === "tool_result") as Anthropic.ToolResultBlockParam)
                        .tool_use_id ?? "",
            },
        ];
    }

    if (message.content.find((c) => c.type === "tool_use")) {
        return [
            {
                role: "assistant",
                content: message.content
                    .filter((c) => "text" in c)
                    .map((c) => (c as Anthropic.TextBlock).text)
                    .join(" "),
                reasoning_text: thinkingBlock ? (thinkingBlock as Anthropic.ThinkingBlock).thinking : undefined,
                tool_calls: message.content
                    .filter((c) => c.type === "tool_use")
                    .map((c) => {
                        const toolUse = c as Anthropic.ToolUseBlock;
                        return {
                            id: toolUse.id,
                            type: "function",
                            function: {
                                name: toolUse.name,
                                arguments: JSON.stringify(toolUse.input),
                            },
                        };
                    }),
            },
        ];
    }

    return [
        {
            role: message.role,
            reasoning_text: thinkingBlock ? (thinkingBlock as Anthropic.ThinkingBlock).thinking : undefined,
            content: message.content.map((c) => ({
                type: "text",
                text: (c as Anthropic.TextBlock).text,
            })),
        },
    ];
}

function isTextBlock(content: ContentBlock): content is TextBlock {
    return "text" in content;
}

function toChatCompletionMessage(message: Anthropic.Message): CopilotChatCompletionMessage {
    // Extract thinking block
    const thinkingBlock = message.content.find((block) => block.type === "thinking");

    return {
        role: message.role,
        content: message.content
            .filter(isTextBlock)
            .map((textBlock) => textBlock.text)
            .join(""),
        reasoning_text: thinkingBlock ? (thinkingBlock as Anthropic.ThinkingBlock).thinking : undefined,
        refusal: null,
    };
}

function removeEmptyTextBlocks(content: ContentBlock[]): ContentBlock[] {
    return content.filter((block) => {
        if (block.type === "text") {
            return block.text.trim() !== "";
        }
        return true;
    });
}

function toAnthropicToolChoiceOption(toolChoice: ChatCompletionToolChoiceOption): Anthropic.ToolChoice {
    switch (toolChoice) {
        case "none":
            return { type: "none" };
        case "auto":
            return { type: "auto" };
        case "required":
            return { type: "any" };
        // TODO: add named tool choice support
        default:
            throw new Error(`Invalid tool choice: ${toolChoice}`);
    }
}
