/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import type {
    ChatCompletionMessageParam,
    ChatCompletionTool,
    ChatCompletionToolChoiceOption,
} from "openai/resources/chat/completions";
import { Tool } from "../tools";
import { Event } from "./event";

/**
 * Retry policies for the AI client.
 */
export type ClientRetryPolicy = {
    /**
     * The maximum number of retries for **any** type of retryable failure or error.
     */
    maxRetries?: number;

    /**
     * Specific error codes that should always be retried.
     * - If a `number`, that specific error code will be retried.
     * - If a `[number, number]`, all error codes in the range will be retried (inclusive).
     * - If a `[number, undefined]`, all error codes greater than or equal to the first number will be retried.
     * - To retry all error codes based on an upper bound, simply use `[0, number]`.
     *
     * Some error codes are retried by default even if not specified here, for example 429 (rate limit exceeded).
     */
    errorCodesToRetry?: (number | [number, number | undefined])[];

    /**
     * How to handle retries for rate limiting (429) errors. If a policy is not provided, a default
     * policy will be used.
     */
    rateLimitRetryPolicy?: {
        /**
         * The default wait time in between retries if the server does not
         * provide a `retry-after` header.
         */
        defaultRetryAfterSeconds?: number;
        /**
         * The initial extra wait time in between retries. The extra wait time will
         * be added to the `retry-after` header value (or {@link defaultRetryAfterSeconds} if
         * the header is not present). After each retry, the extra wait time will grow beyond
         * this value according to the {@link retryBackoffExtraGrowth} factor.
         */
        initialRetryBackoffExtraSeconds?: number;
        /**
         * The growth factor for the retry backoff extra time. E.g. 2x, 3x, etc.
         */
        retryBackoffExtraGrowth?: number;
        /**
         * The maximum wait time in between retries.
         */
        maxRetryAfterSeconds?: number;
    };
};

/**
 * The ideal set of options that a `{@link Client}` expose.
 */
export type ClientOptions = {
    /**
     * The model to use for LLM completions.
     */
    model?: string;

    /**
     * The proportion of the model's input/prompt token limit
     * that should be given to tools as their token budget.
     */
    toolTokenBudgetProportion?: number;

    retryPolicy?: ClientRetryPolicy;

    /**
     * Enable thinking mode for the model if available.
     */
    thinkingMode?: boolean;

    requestHeaders?: Record<string, string>;
};

export type DeepRequire<T> = {
    [K in keyof T]-?: Exclude<T[K], undefined> extends object ? DeepRequire<T[K]> : T[K];
};
export type ClientOptionsRequired = DeepRequire<ClientOptions>;

export type PreRequestContext = {
    turn: number;
    /**
     * The messages that will be sent to the model for the request.
     * If modifying them, do so in place.
     */
    messages: ChatCompletionMessageParam[];
    /**
     * The tool definitions that will be sent to the model for the request.
     * These should not be modified.
     */
    toolDefinitions: ChatCompletionTool[];
    /**
     * The name of the model that will be used for the request.
     */
    modelName: string;
    /**
     * The token limit that needs to be respected for the request.
     */
    tokenLimit: number;
};

export interface IPreRequestProcessor {
    /**
     * Called before a request is made to the model. Any {@link Event}s emitted
     * by this method will be re-emitted by the completion with tools call.
     */
    preRequest(context: PreRequestContext): AsyncGenerator<Event>;
}

// @todo add other interfaces for allowing processors to hook into the lifecycle of
// completion with tools calls at other stages. For example:
// - IPostRequestProcessor
// - IPostToolExecutionProcessor

export type GetCompletionWithToolsOptions = {
    /**
     * If `true`, then calls do `getCompletionWithTools` will check if the token counts of
     * the initial system messages, user messages, and tool definitions, exceed the limits
     * of the model. If they do, then the call will throw an error. If `false`, then the
     * call will not perform any checks.
     *
     * Defaults to `false`.
     */
    failIfInitialInputsTooLong?: boolean;

    toolChoice?: ChatCompletionToolChoiceOption;

    requestHeaders?: Record<string, string>;

    /**
     * If provided, this function will allow agents to add JIT instructions to the
     * end of the turns as a user message to steer the model in a specific direction mid-way
     * through the conversation.
     *
     * Instructions will be used after this function is called. If you only want an instruction to be used
     * once, then you should not return it from this function again.
     *
     * @todo: move to be a processor instead?
     */
    getJitInstructions?: () => string | undefined;

    /**
     * If this call is a continuation of a previous `getCompletionWithTools` call, this specifies what turn
     * that conversation was/is on. This is used to determine the initial turn count in this
     * call to `getCompletionWithTools`.
     */
    initialTurnCount?: number;

    /**
     * If `true`, then the client will execute tools in parallel if model supports `parallel_tool_calls`.
     * If `false`, then the client will execute tools sequentially even if the model returns multiple tool calls.
     * Defaults to false.
     */
    executeToolsInParallel?: boolean;

    /**
     * Processors provide a way to do work during different stages of the completion with tools
     * lifecycle. Processors will be called in the order they are provided.
     */
    processors?: {
        preRequest: IPreRequestProcessor[];
    };
};

export interface Client {
    readonly model: string;
    getCompletionWithTools(
        systemMessage: string,
        initialMessages: ChatCompletionMessageParam[],
        tools: Tool[],
        options?: GetCompletionWithToolsOptions,
    ): AsyncGenerator<Event>;
}
