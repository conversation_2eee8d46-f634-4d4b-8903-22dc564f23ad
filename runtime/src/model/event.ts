/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { OpenAI } from "openai/index.mjs";
import type {
    ChatCompletionAssistantMessageParam,
    ChatCompletionMessage,
    ChatCompletionMessageParam,
    ChatCompletionMessageToolCall,
    ChatCompletionToolMessageParam,
    ChatCompletionUserMessageParam,
} from "openai/resources/chat/completions";
import { ToolResultExpanded } from "../tools";
import { ImageProcessingMetrics } from "./capi/image-utils";
import { ReasoningMessageParam } from "./capi/types";

export type TurnEvent = {
    kind: "turn_started" | "turn_ended" | "turn_failed" | "turn_retry";
    model: string;
    modelInfo: object;
    turn: number;
    timestampMs: number;
    error?: string;
};

export type ModelCallSuccessEvent = {
    kind: "model_call_success";
    turn: number;
    modelCallDurationMs: number;
    modelCall: ModelCallParam;
    responseChunk: OpenAI.Chat.Completions.ChatCompletionChunk;
    responseUsage: OpenAI.ChatCompletion["usage"];
    /**
     * A string representation of the messages sent as input to the model call, if available.
     */
    requestMessages?: string;
};

export type ModelCallFailureEvent = {
    kind: "model_call_failure";
    turn: number;
    modelCallDurationMs: number;
    /**
     * The model call that failed, if available.
     */
    modelCall: ModelCallParam;
    /**
     * A string representation of the messages sent as input to the model call, if available.
     */
    requestMessages?: string;
};

export type ToolExecutionEvent = {
    kind: "tool_execution";
    turn: number;
    toolCallId: string;
    toolResult: ToolResultExpanded;
    durationMs: number;
};

/**
 * This event is temporary until we extract vision support from being internal to getCompletionWithTools.
 */
export type ImageProcessingEvent = {
    kind: "image_processing";
    turn: number;
    imageProcessingMetrics: ImageProcessingMetrics;
};
/**
 * This event is temporary until we extract vision support from being internal to getCompletionWithTools.
 */
export type ImageRemovalEvent = {
    kind: "images_removed";
    turn: number;
    largeImagesRemoved?: number;
    imagesRemoved: number;
};

export type TruncationEvent = {
    kind: "history_truncated";
    turn: number;
    truncateResult: {
        preTruncationTokensInMessages: number;
        preTruncationMessagesLength: number;
        postTruncationTokensInMessages: number;
        postTruncationMessagesLength: number;
        tokensRemovedDuringTruncation: number;
        messagesRemovedDuringTruncation: number;
    };
};

// #region MessageEvent

/**
 * An event that is emitted by the `Client` for each message it receives from the LLM.
 *
 * Currently does not include telemetry.
 */
export type AssistantMessageEvent = {
    kind: "message";
    modelCall?: ModelCallParam;
    message: ChatCompletionAssistantMessageParam & ReasoningMessageParam;
    turn?: number;
};

/**
 * An event that is emitted by the `Client` for each user message it adds to the middle of the conversation.
 */
export type UserMessageEvent = {
    kind: "message";
    modelCall?: ModelCallParam;
    message: ChatCompletionUserMessageParam;
    turn?: number;
    source?: "jit-instruction";
};

/**
 * An event that is emitted by the `Client` for each tool message it will send back to the LLM.
 */
export type ToolMessageEvent = {
    kind: "message";
    modelCall?: ModelCallParam;
    message: ChatCompletionToolMessageParam;
    turn?: number;
};

/**
 * All types of message events that can be emitted by the `Client`.
 */
export type MessageEvent =
    | {
          kind: "message";
          modelCall?: ModelCallParam;
          message: ChatCompletionMessageParam & ReasoningMessageParam;
          turn?: number;
      }
    | AssistantMessageEvent
    | UserMessageEvent
    | ToolMessageEvent;

// #endregion

// #region ResponseEvent

/**
 * An event that is emitted by the `Client` which contains the final response from the LLM.
 */
export type ResponseEvent = {
    kind: "response";
    modelCall?: ModelCallParam;
    response: ChatCompletionMessage;
    turn?: number;
};

// #endregion

export type SessionLogEvent = {
    kind: "session_log";
    sessionLog: OpenAI.Chat.Completions.ChatCompletionChunk;
    modelCallDurationMs?: number;
    requestMessages?: string;
};

/**
 * -----------------------------------------------------------------------
 * Events
 * -----------------------------------------------------------------------
 *
 * Event (Union Type)
 * ├── TurnStartedEvent
 * ├── TurnEndedEvent
 * ├── TurnFailedEvent
 * ├── TurnRetryEvent
 * ├── ModelCallSuccessEvent
 * ├── ModelCallFailureEvent
 * ├── ToolExecutionEvent
 * ├── ImageProcessingEvent
 * ├── ImageRemovalEvent
 * ├── TruncationEvent
 * ├── MessageEvent
 * │   ├── AssistantMessageEvent
 * │   ├── UserMessageEvent
 * │   └── ToolMessageEvent
 * ├── ResponseEvent
 * └── SessionLogEvent
 */

/**
 * All types of events that can be emitted by the `Client`.
 */

// Common property, to tie together events belonging to a model call. We do not enforce this to allow flexibility, but if possible we make it easier to join the events on kusto.
// SessionLogEvent and GetCompletionWithToolsTurnTelemetryEvent already have that information
export interface ModelCallParam {
    api_id?: string;
    model?: string;
    error?: string;
    status?: number;
    request_id?: string;
}

export type Event =
    | MessageEvent
    | ResponseEvent
    | ModelCallFailureEvent
    | TurnEvent
    | TruncationEvent
    | ImageProcessingEvent
    | ImageRemovalEvent
    | ModelCallSuccessEvent
    | ToolExecutionEvent
    | SessionLogEvent;

export function isAssistantMessageEvent(content: Event): content is AssistantMessageEvent {
    return content.kind === "message" && content.message.role === "assistant";
}

export function isUserMessageEvent(content: Event): content is UserMessageEvent {
    return content.kind === "message" && content.message.role === "user";
}

export function isAssistantMessageWithToolCalls(content: Event): content is AssistantMessageEvent & {
    message: { tool_calls: ChatCompletionMessageToolCall[] };
} {
    return content.kind === "message" && content.message.role === "assistant" && !!content.message.tool_calls;
}

export function isToolMessageEvent(content: Event): content is ToolMessageEvent {
    return content.kind === "message" && content.message.role === "tool";
}

export function isModelCallFailureEvent(content: Event): content is ModelCallFailureEvent {
    return content.kind === "model_call_failure";
}

export function isModelCallSuccessEvent(content: Event): content is ModelCallSuccessEvent {
    return content.kind === "model_call_success";
}

export function isToolExecutionEvent(content: Event): content is ToolExecutionEvent {
    return content.kind === "tool_execution";
}
