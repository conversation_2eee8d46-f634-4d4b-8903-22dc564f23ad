/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { imageSize } from "image-size";
import path from "path";
import { formatBytes } from "../../helpers/string-helpers";
import { RunnerLogger } from "../../runner";
import { ImageReference } from "./types";

export type ImageProcessingMetrics =
    | ({
          imagesExtractedCount: number;
          base64ImagesCount: number;
          imagesRemovedDueToSize: number;
          imagesRemovedDueToDimensions: number;
          imagesResolvedFromGitHubMCPCount: number;
          allImagesSendToLlm?: number;

          // There's also, per image in various categories:
          // base64ImageSize_${i}: number
          // extractedImageSize_${i}: number
          // imagesResolvedFromGitHubMCPSize_${i}: number
          // Not really sure how to type those beyond just `Record<string, number>`...
      } & Record<string, number>)
    | Record<string, never>;

// TODO: CAPI doesn't return dimension limits and we need to change that
// For now hardcoding limit of 2000px per dimension as Anthropic API doesn't support larger images
export const DIMENSION_LIMIT = 2000; // Maximum dimension size for images

export async function getAttachmentFromGuid(
    imageGuid: string,
    userToken: string,
    logger: RunnerLogger,
): Promise<{ size: number; buffer?: Buffer; mimeType?: string }> {
    // Downloads the image from the given URL and returns its size in bytes.
    const headers = {
        Accept: "application/vnd.github+json",
        "X-GitHub-Api-Version": "2022-11-28",
        Authorization: "Bearer " + userToken,
    };

    const downloadUrl = `https://api.github.com/internal/user-attachments/assets/${imageGuid}`;
    console.log(`Downloading image from ${downloadUrl}`);

    try {
        const downloadResponse = await fetch(downloadUrl, {
            method: "GET",
            headers: headers,
        });

        console.log(`Response status: ${downloadResponse.status} ${downloadResponse.statusText}`);

        if (!downloadResponse.ok) {
            console.log(
                `Failed to download image, status: ${downloadResponse.status}, statusText: ${downloadResponse.statusText}`,
            );

            // Try to read response body for error details
            try {
                const errorText = await downloadResponse.text();
                console.log(`Error response body: ${errorText}`);
            } catch (bodyError) {
                console.log(`Could not read error response body: ${bodyError}`);
            }

            return { size: 0 }; // Return 0 instead of throwing
        }
        const result = (await downloadResponse.json()) as { url: string };
        const assetURL = result.url;
        if (!assetURL) {
            console.error(`No asset URL found in response for image GUID: ${imageGuid}`);
            return { size: 0 };
        }
        // Download asset from the URL

        const response = await fetch(assetURL, { method: "GET" });
        if (!response.ok) {
            logger.error(`Failed to download image: ${response.status} ${response.statusText}`);
            return { size: 0 };
        }
        const arrayBuffer = await response.arrayBuffer();
        const size = arrayBuffer.byteLength;
        logger.info(`Downloaded content size: ${formatBytes(size)}`);

        return {
            size: size,
            buffer: Buffer.from(arrayBuffer),
            mimeType: response.headers.get("content-type") || undefined,
        };
    } catch (error) {
        logger.error("Network error downloading image");
        if (error instanceof Error) {
            logger.info(`Error message: ${error.message}`);
            logger.info(`Error stack: ${error.stack}`);
        }
        return { size: 0 }; // Return 0 instead of throwing
    }
}

export function matchAllImageReferences(
    message: string,
    githubServerUrl: string,
    includeBlobUrls: boolean,
    logger: RunnerLogger,
): ImageReference[] {
    const imgTagRegex = /<img\b[^>]*\bsrc="([^"]+)"[^>]*>/g;
    const imgMarkdownRegex = /!\[.*?\]\((.*?)\)/g;
    const imageReferences: ImageReference[] = [];

    imageReferences.push(...matchImageReferences(message, githubServerUrl, imgTagRegex, includeBlobUrls, logger));
    imageReferences.push(...matchImageReferences(message, githubServerUrl, imgMarkdownRegex, includeBlobUrls, logger));

    return imageReferences;
}

export function matchImageReferences(
    message: string,
    githubServerUrl: string,
    regex: RegExp,
    includeBlobUrls: boolean,
    logger: RunnerLogger,
): ImageReference[] {
    const matches = message.matchAll(regex);
    const imageReferences: ImageReference[] = [];
    const guidRegex = /\/([a-f0-9-]+)$/;

    for (const match of matches) {
        const inGHUserAttachment = isGitHubUserAttachment(match[1], githubServerUrl);
        const isGHLegacyAttachment = isGitHubLegacyAttachment(match[1], githubServerUrl, logger);
        if (inGHUserAttachment || isGHLegacyAttachment) {
            const guidMatch = match[1].match(guidRegex);
            const imageRef: ImageReference = {
                url: match[1],
                guid: "",
                kind: isGHLegacyAttachment ? "legacy_attachment" : "attachment",
            };
            if (guidMatch && guidMatch.length > 1) {
                imageRef.guid = guidMatch[1];
            }
            imageReferences.push(imageRef);
        } else if (includeBlobUrls) {
            const imageRef: ImageReference = {
                url: match[1],
                guid: "",
                kind: "committed_attachment",
            };
            imageReferences.push(imageRef);
        }
    }

    return imageReferences;
}

export function isImageInRepoPath(path: string): boolean {
    return !path.startsWith("http://") && !path.startsWith("https://") && isImageFilename(path);
}

export function isImageFilename(path: string): boolean {
    const imageExtRegex = /\.(png|jpe?g|gif|svg|webp|bmp|tiff?|ico|heic|avif)$/i;
    return imageExtRegex.test(path);
}

export function extractRepositoryInsights(
    message: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
): Record<string, any> | null {
    try {
        const newFormatRegex =
            /Here are relevant code snippets from the repository that might help with this task:\s*(\[[\s\S]*?\])(?=\n|$)/;
        const match = message.match(newFormatRegex);

        if (!match?.[1] || match[1].length <= 1) {
            return null;
        }

        // Parse the JSON string, handling both single objects and arrays
        const insightsString = match[1].trim();
        const insights = JSON.parse(insightsString);

        return insights;
    } catch {
        return null;
    }
}

export function convertPathToAbsolutePath(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    repoInsight: any,
    imagePath: string,
    githubServerUrl: string,
): string {
    if (!repoInsight?.url || !repoInsight?.ref || !repoInsight?.path) {
        throw new Error("Invalid repository insight: missing required fields");
    }

    if (!imagePath || imagePath.trim() === "") {
        throw new Error("Invalid image path: path cannot be empty");
    }

    // If correct, extract repository details
    const repoUserAndName = repoInsight["url"].split("/").slice(3, 5).join("/");
    const tree = repoInsight["ref"].split("/")[2];

    let resolvedImagePath: string;

    if (imagePath.startsWith("/")) {
        resolvedImagePath = imagePath.slice(1);
    } else {
        const repoInsightPath = repoInsight["path"];
        const currentFileDir = path.dirname(repoInsightPath);
        resolvedImagePath = path.resolve("/", currentFileDir, imagePath).slice(1);
    }

    // Construct URL
    const constructedUIURL = `${githubServerUrl}/${repoUserAndName}/blob/${tree}/${resolvedImagePath}`;

    return constructedUIURL;
}

export function isGitHubAttachment(url: string, githubServerUrl: string, logger: RunnerLogger): boolean {
    if (isGitHubUserAttachment(url, githubServerUrl)) {
        return true;
    }

    return isGitHubLegacyAttachment(url, githubServerUrl, logger);
}

export function isGitHubUserAttachment(url: string, githubServerUrl: string): boolean {
    if (!githubServerUrl) {
        return false;
    }

    if (url.startsWith(`${githubServerUrl}/user-attachments/assets`)) {
        return true;
    }

    return false;
}

export function isGitHubLegacyAttachment(url: string, githubServerUrl: string, logger: RunnerLogger): boolean {
    if (!githubServerUrl) {
        return false;
    }

    const legacyAssetRegex = new RegExp(`^${githubServerUrl}/[\\w.-]+/[\\w.-]+/assets/\\d+/[^.]+$`);
    if (legacyAssetRegex.test(url)) {
        logger.debug(`Legacy GitHub asset URL detected: ${url}`);
        return true;
    }

    return false;
}

export async function getImageDimensions(
    buffer: Buffer,
): Promise<{ width: number | undefined; height: number | undefined }> {
    // Load the file as a Buffer

    // image-size inspects the header and returns a Dimension object
    const { width, height } = imageSize(buffer);

    if (width === undefined || height === undefined) {
        throw new Error("Unable to determine image dimensions.");
    }

    return { width, height };
}

// Checks if the image exceeds the size and dimension limits
// Returns a tuple: [sizeExceedsLimit, dimensionsExceedLimit]
export async function imageExceedsLimits(maxSize: number, imageContent: Buffer): Promise<[boolean, boolean]> {
    const meta = await getImageDimensions(imageContent);
    const contentSize = imageContent.length;

    if (!meta.width || !meta.height) {
        return [contentSize > maxSize, false];
    }
    return [contentSize > maxSize, meta.width > DIMENSION_LIMIT || meta.height > DIMENSION_LIMIT];
}

export class VisionMetrics {
    public imagesRemovedDueToSize: number = 0;
    public imagesRemovedDueToDimensions: number = 0;
    public base64ImagesSize: number[] = [];
    public extractedImagesSize: number[] = [];
    public imagesResolvedFromGitHubMCPSize: number[] = [];
}

export function extractRepositoryImageUrls(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    messages: any[],
    maxImagesToResolve: number,
    githubServerUrl: string,
    logger: RunnerLogger,
): ImageReference[] {
    if (maxImagesToResolve <= 0) {
        return [];
    }

    const imageReferences: ImageReference[] = [];

    // Look through messages for repository insights
    for (const message of messages) {
        if (message.role !== "user" || imageReferences.length >= maxImagesToResolve) {
            continue;
        }

        const content = typeof message.content === "string" ? message.content : JSON.stringify(message.content);

        const repoInsights = extractRepositoryInsights(content);
        if (!repoInsights) {
            continue;
        }

        // Check each repository insight for image references
        for (const insight in repoInsights) {
            if (imageReferences.length >= maxImagesToResolve) {
                break;
            }

            const contents: string = repoInsights[insight]["contents"];
            const imageRefs = matchAllImageReferences(contents, githubServerUrl, true, logger);

            for (const imgRef of imageRefs) {
                if (imageReferences.length >= maxImagesToResolve) {
                    break;
                }

                const imagePath: string = imgRef.url;
                let imageUrl: string = "";
                let isBlobUrl = false;

                if (imgRef.kind === "attachment" || imgRef.kind === "legacy_attachment") {
                    imageReferences.push(imgRef);
                } else if (isImageInRepoPath(imagePath)) {
                    isBlobUrl = true;
                    imageUrl = convertPathToAbsolutePath(repoInsights[insight], imagePath, githubServerUrl);
                    imageReferences.push({
                        url: imageUrl,
                        guid: imgRef.guid,
                        kind: "committed_attachment",
                    });
                } else if (isImageFilename(imagePath) && imagePath.startsWith(githubServerUrl)) {
                    isBlobUrl = true;
                    imageReferences.push({
                        ...imgRef,
                        kind: "committed_attachment",
                    });
                }

                if (isBlobUrl) {
                    const imageRef = imageReferences[imageReferences.length - 1];
                    const blobUrlRegex = new RegExp(`^${githubServerUrl}/([^/]+)/([^/]+)/blob/([^/]+)/(.+)$`);
                    const blobMatch = imageRef.url.match(blobUrlRegex);

                    console.log(`Blob match for ${imageRef.url}:`, JSON.stringify(blobMatch));

                    if (blobMatch && blobMatch.length === 5) {
                        const [, owner, repo, branch, path] = blobMatch;
                        imageRef.owner = owner;
                        imageRef.repo = repo;
                        imageRef.branch = branch;
                        imageRef.path = path;
                        console.log(
                            `Repository metadata extracted: owner=${owner}, repo=${repo}, branch=${branch}, path=${path}`,
                        );
                    } else {
                        console.log(`Failed to extract repository metadata from URL: ${imageRef.url}`);
                    }
                }
            }
        }
    }

    return imageReferences;
}
