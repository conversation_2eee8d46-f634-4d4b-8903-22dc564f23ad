/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { ChatCompletionMessageParam, ChatCompletionTool } from "openai/resources";
import { IPreRequestProcessor, PreRequestContext } from "../client";
import { Event, TruncationEvent } from "../event";
import { countTokensInChatCompletionMessage, countTokensInString } from "../tokenCounting";

export class BasicTruncator implements IPreRequestProcessor {
    async *preRequest(context: PreRequestContext): AsyncGenerator<Event> {
        const result = await truncateContextWindow(
            context.messages,
            context.toolDefinitions,
            context.modelName,
            context.tokenLimit,
        );
        yield {
            kind: "history_truncated",
            turn: context.turn,
            truncateResult: result,
        } satisfies TruncationEvent;
    }
}

export type TruncateContextWindowResult = {
    preTruncationTokensInMessages: number;
    preTruncationMessagesLength: number;
    postTruncationTokensInMessages: number;
    postTruncationMessagesLength: number;
    tokensRemovedDuringTruncation: number;
    messagesRemovedDuringTruncation: number;
};

/**
 * We leave a buffer below the limit to account for needing tokens on the next turn.
 */
const limitBuffer = 0.95;

/**
 * Truncates the context window (messages + tool definitions) to a maximum number of tokens.
 *
 * - The {@param messages} array is modified in place.
 * - System messages are always preserved.
 * - User messages are always preserved.
 * - If an assistant message with tool calls is removed, the tool result messages will also be removed.
 * - It is assumed the oldest messages are at the start of the array.
 * - The {@param toolDefinitions} array is not modified.
 *
 * @returns the number of tokens removed.
 */
export function truncateContextWindow(
    messages: ChatCompletionMessageParam[],
    toolDefinitions: ChatCompletionTool[],
    modelName: string,
    modelTokenLimit: number,
): TruncateContextWindowResult {
    const originalMessagesLength = messages.length;
    if (originalMessagesLength === 0) {
        return {
            preTruncationTokensInMessages: 0,
            preTruncationMessagesLength: originalMessagesLength,
            postTruncationTokensInMessages: 0,
            postTruncationMessagesLength: originalMessagesLength,
            tokensRemovedDuringTruncation: 0,
            messagesRemovedDuringTruncation: 0,
        };
    }

    const messagesTokenCounts = messages.map((message) => countTokensInChatCompletionMessage(message, modelName));
    const toolDefinitionsTokenCount = countTokensInString(JSON.stringify(toolDefinitions), modelName);

    const modelTokenLimitAfterLimitBuffer = modelTokenLimit * limitBuffer;

    const startingTokenCount = messagesTokenCounts.reduce((a, b) => a + b, 0) + toolDefinitionsTokenCount;
    if (startingTokenCount <= modelTokenLimitAfterLimitBuffer) {
        return {
            preTruncationTokensInMessages: startingTokenCount,
            preTruncationMessagesLength: originalMessagesLength,
            postTruncationTokensInMessages: startingTokenCount,
            postTruncationMessagesLength: originalMessagesLength,
            tokensRemovedDuringTruncation: 0,
            messagesRemovedDuringTruncation: 0,
        };
    }

    const messagesIdxsToRemove = new Set<number>();
    let tokensToBeRemoved = 0;

    for (let i = 0; i < messages.length; i++) {
        const message = messages[i];
        if (message.role === "system") {
            // system messages are always preserved
            continue;
        }

        if (message.role === "tool") {
            // tool results are only removed when their corresponding tool call needs to be removed
            continue;
        }

        if (message.role === "user") {
            // user messages are always preserved
            continue;
        }

        // from here on out, assume the message is being removed!
        messagesIdxsToRemove.add(i);
        tokensToBeRemoved += messagesTokenCounts[i];

        if (message.role === "assistant") {
            const toolCalls = message.tool_calls;
            if (toolCalls && toolCalls.length > 0) {
                // if there were tool calls, we'll need to remove the corresponding tool results
                const toolCallsToBeRemoved = new Set<string>(toolCalls.map((tc) => tc.id));

                // remove all tool results that correspond to tool calls that are being removed
                for (let j = i + 1; j < messages.length; j++) {
                    const maybeToolResult = messages[j];
                    if (maybeToolResult.role === "tool" && toolCallsToBeRemoved.has(maybeToolResult.tool_call_id)) {
                        messagesIdxsToRemove.add(j);
                        tokensToBeRemoved += messagesTokenCounts[j];
                        toolCallsToBeRemoved.delete(maybeToolResult.tool_call_id);

                        if (toolCallsToBeRemoved.size === 0) {
                            // once all tool results have been removed, we can stop looking
                            break;
                        }
                    }
                }
            }
        }

        if (startingTokenCount - tokensToBeRemoved < modelTokenLimitAfterLimitBuffer) {
            // we're under the limit, so we don't need to remove any more messages
            break;
        }
    }

    // remove all messages that need to be removed
    // iterate backwards so we don't mess up the indexes of the messages that still need to be removed
    for (let i = messages.length - 1; i >= 0; i--) {
        if (messagesIdxsToRemove.has(i)) {
            messages.splice(i, 1);
        }
    }

    return {
        preTruncationTokensInMessages: startingTokenCount,
        preTruncationMessagesLength: originalMessagesLength,
        postTruncationTokensInMessages: startingTokenCount - tokensToBeRemoved,
        postTruncationMessagesLength: messages.length,
        tokensRemovedDuringTruncation: tokensToBeRemoved,
        messagesRemovedDuringTruncation: originalMessagesLength - messages.length,
    };
}
