/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import OpenAI, { APIError } from "openai";
import type { FinalRequestOptions } from "openai/core";
import { ChatCompletionCreateParamsNonStreaming } from "openai/resources";
import { RunnerLogger } from "../../runner";
import { HMACEncoder } from "./hmac-encoder";
import { ClientExtensionMethods, Model } from "./types";
// GitHub API version header as per Copilot API documentation
const GITHUB_API_VERSION_HEADER = "X-GitHub-Api-Version";
const GITHUB_API_VERSION_VALUE = "2025-05-01";

export class CopilotOpenAIClient extends OpenAI implements ClientExtensionMethods {
    static baseHeaders = {
        "Content-Type": "application/json",
        Accept: "application/json",
        "X-Interaction-Type": "conversation-agent",
        "Openai-Intent": "conversation-agent",
        "X-Initiator": "user",
        [GITHUB_API_VERSION_HEADER]: GITHUB_API_VERSION_VALUE,
    };

    private hmacKey?: string;

    private constructor(
        baseURL: string,
        private headers: Record<string, string>,
        private readonly logger: RunnerLogger,
        hmacKey?: string,
    ) {
        super({ baseURL, apiKey: "" });
        this.hmacKey = hmacKey;
    }

    public static createWithOAuthToken(
        logger: RunnerLogger,
        capiUrl: string,
        integrationId: string,
        token: string,
        interactionId: string,
        requestHeaders?: Record<string, string>,
    ): CopilotOpenAIClient {
        const headers: Record<string, string> = {
            ...this.baseHeaders,
            "Copilot-Integration-Id": integrationId,
            "X-Interaction-Id": interactionId,
            Authorization: `Bearer ${token}`,
            ...(requestHeaders ?? {}),
        };
        return new CopilotOpenAIClient(capiUrl, headers, logger);
    }

    public static createWithHmac(
        logger: RunnerLogger,
        capiUrl: string,
        integrationId: string,
        hmacKey: string,
        interactionId: string,
        userId?: string,
        requestHeaders?: Record<string, string>,
    ): CopilotOpenAIClient {
        const headers: Record<string, string> = {
            ...this.baseHeaders,
            "Copilot-Integration-Id": integrationId,
            "X-Interaction-Id": interactionId,
            Authorization: "",
            ...(requestHeaders ?? {}),
        };

        if (userId) {
            headers["X-GitHub-User"] = userId;
        }

        return new CopilotOpenAIClient(capiUrl, headers, logger, hmacKey);
    }

    protected async prepareOptions(options: FinalRequestOptions): Promise<void> {
        if (this.hmacKey) {
            this.headers["Request-HMAC"] = HMACEncoder.encode(this.hmacKey);
        }

        options.headers = {
            ...this.headers,
            ...options.headers, // order matters, options will contain any additional headers needed for this particular request
        };

        await super.prepareOptions(options); // Ensure base class logic is preserved
    }

    public setInitiatorHeader(initiator: string): void {
        this.headers["X-Initiator"] = initiator;
    }

    public async listModels(): Promise<Model[]> {
        const requestOptions: FinalRequestOptions = { method: "get", path: "" };
        await this.prepareOptions(requestOptions);

        const fetchHeaders: Record<string, string> = {};
        if (requestOptions.headers) {
            for (const [key, value] of Object.entries(requestOptions.headers)) {
                if (typeof value === "string") {
                    fetchHeaders[key] = value;
                }
            }
        }

        const url = `${this.baseURL}/models`;
        const response = await fetch(url, {
            method: requestOptions.method,
            headers: fetchHeaders,
        });
        if (!response.ok) {
            this.logger.error(`Failed to list models: ${response.statusText}`);
            return [];
        }
        const parsedResponse = (await response.json()) as { data: Model[] };
        this.logger.debug(`Successfully listed ${parsedResponse.data.length} models`);
        return parsedResponse.data;
    }

    protected async prepareRequest(
        request: RequestInit,
        { url, options }: { url: string; options: FinalRequestOptions },
    ): Promise<void> {
        // If we have images in our messages, we need to set the
        // Copilot-Vision-Request header.
        try {
            const body = options.body as ChatCompletionCreateParamsNonStreaming;
            const hasImages = body.messages.some((m) =>
                Array.isArray(m.content) ? m.content.some((c) => c.type === "image_url") : false,
            );
            if (hasImages) {
                request.headers = {
                    ...options.headers,
                    "Copilot-Vision-Request": "true",
                };
            }
        } catch {
            this.logger.error("Error while preparing request headers for Copilot-Vision-Request");
        }

        // Ensure base class logic is preserved
        await super.prepareRequest(request, { url, options });
    }
}

export class CAPIError extends APIError {
    private constructor(apiError: APIError) {
        super(apiError.status, apiError.error, apiError.message, apiError.headers);
        this.name = "CAPIError";
        this.ghRequestId = apiError.headers?.["x-github-request-id"] || this.request_id;
    }

    public readonly ghRequestId: string | null | undefined;

    public static fromAPIError(error: APIError): CAPIError | undefined {
        if (!error || !(error instanceof APIError)) {
            return undefined;
        }
        return new CAPIError(error);
    }
}
