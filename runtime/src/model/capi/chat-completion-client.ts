/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import type { APIError, OpenAI } from "openai";
import type {
    Chat<PERSON>ompletion,
    ChatCompletionContentPart,
    ChatCompletionContentPartImage,
    ChatCompletionCreateParamsNonStreaming,
    ChatCompletionMessageParam,
    ChatCompletionMessageToolCall,
    ChatCompletionToolMessageParam,
    ChatCompletionUserMessageParam,
} from "openai/resources/chat/completions";
import { includeScreenshotPrompt } from "../../agents/prompts";
import { SweAgentKind } from "../../agents/sweagent";
import { messageContainsScreenshot } from "../../agents/util";
import { deepJsonParse } from "../../helpers/json-helpers";
import { cleanUpMessage, completionToChunk } from "../../helpers/openAiHelpers";
import { formatBytes } from "../../helpers/string-helpers";
import { LogLevel, RunnerLogger } from "../../runner";
import { RuntimeSettings } from "../../settings";
import { demandCopilotToken } from "../../settings/demand";
import { isFeatureFlagEnabled } from "../../settings/types";
import {
    BinaryResult,
    buildToolCallbackOptions,
    createToolSet,
    Tool,
    ToolCallback,
    ToolCallbackOptions,
    ToolResultExpanded,
} from "../../tools";
import type { Client, ClientOptions, ClientOptionsRequired, GetCompletionWithToolsOptions } from "../client";
import { SweAgentCapiDefault } from "../defaults";
import type {
    AssistantMessageEvent,
    Event,
    ImageProcessingEvent,
    ImageRemovalEvent,
    MessageEvent,
    ModelCallParam,
    ModelCallSuccessEvent,
    SessionLogEvent,
    ToolExecutionEvent,
    ToolMessageEvent,
    TurnEvent,
    UserMessageEvent,
} from "../event";
import { countTokensInChatCompletionMessages, countTokensInToolDefinitions } from "../tokenCounting";
import { CAPIError } from "./copilot-client";
import {
    extractRepositoryImageUrls,
    getAttachmentFromGuid,
    imageExceedsLimits,
    ImageProcessingMetrics,
    matchAllImageReferences,
    VisionMetrics,
} from "./image-utils";
import { executeInParallelAndYieldAsCompleted } from "./promise-helpers";
import {
    CacheControlCheckpoint,
    ClientExtensionMethods,
    CopilotChatCompletionMessage,
    CopilotChatCompletionMessageParam,
    CopilotChatCompletionTool,
    ImageReference,
    Model,
    OpenAIClientProvider,
} from "./types";

const copilot_cache_control: CacheControlCheckpoint = {
    type: "ephemeral",
};

class ImageSizeError extends Error {
    constructor(message: string) {
        super(message);
        this.name = "ImageSizeError";

        // This is necessary in TypeScript to maintain proper prototype chain
        Object.setPrototypeOf(this, ImageSizeError.prototype);
    }
}

type CopilotToolCallResult = {
    durationMs: number;
    /**
     * @todo: Most of the below properties are already contained
     * inside of this property, therefore we can get rid of them.
     */
    originalToolResult: ToolResultExpanded;
    toolCallId: string;
    resultForLlm: string;
    binaryResultForLlm?: BinaryResult[];
    function?: string;
};

export class ChatCompletionClient implements Client {
    protected readonly clientOptions: ClientOptionsRequired;

    private readonly clientPromise: Promise<OpenAI & ClientExtensionMethods>;
    private readonly modelPromise: Promise<Model>;

    private agent: SweAgentKind = "sweagent-capi";

    public get model(): string {
        return this.clientOptions.model;
    }

    constructor(
        provider: OpenAIClientProvider,
        protected readonly settings: RuntimeSettings,
        protected readonly logger: RunnerLogger,
        clientOptions?: ClientOptions,
    ) {
        this.clientOptions = ChatCompletionClient.initDefaultOptions(clientOptions);

        logger.info(" ");
        logger.debug(`Using model: ${this.model}`);
        this.clientPromise = provider.createClient(settings, logger, clientOptions);
        this.modelPromise = this.clientPromise.then(async () => {
            const model = await this.getModel();
            logger.debug(`Got model info: ${JSON.stringify(model, null, 2)}`);
            return model;
        });
    }

    private static initDefaultOptions(options?: ClientOptions): ClientOptionsRequired {
        return {
            model: options?.model || SweAgentCapiDefault.model,
            toolTokenBudgetProportion: options?.toolTokenBudgetProportion ?? 0.25,
            retryPolicy: {
                maxRetries: options?.retryPolicy?.maxRetries ?? 5,
                errorCodesToRetry: options?.retryPolicy?.errorCodesToRetry ?? [],
                rateLimitRetryPolicy: {
                    defaultRetryAfterSeconds: options?.retryPolicy?.rateLimitRetryPolicy?.defaultRetryAfterSeconds ?? 5,
                    initialRetryBackoffExtraSeconds:
                        options?.retryPolicy?.rateLimitRetryPolicy?.initialRetryBackoffExtraSeconds ?? 1,
                    retryBackoffExtraGrowth: options?.retryPolicy?.rateLimitRetryPolicy?.retryBackoffExtraGrowth ?? 2,
                    maxRetryAfterSeconds: options?.retryPolicy?.rateLimitRetryPolicy?.maxRetryAfterSeconds ?? 180,
                },
            },
            thinkingMode: options?.thinkingMode ?? false,
            requestHeaders: options?.requestHeaders ?? {},
        };
    }

    protected getCompletionOptions(
        _tools?: Tool[],
        options?: GetCompletionWithToolsOptions,
    ): Omit<ChatCompletionCreateParamsNonStreaming, "model" | "messages"> {
        // Type hack to pass non-OpenAI option to copilot-api. Sorry.
        const snippyOff = {
            snippy: { enabled: false }, // hardcoded to disable snippy blocking in favour of annotations
        } as Omit<ChatCompletionCreateParamsNonStreaming, "model" | "messages">;

        return {
            temperature: 0.0,
            top_p: 0.95,
            frequency_penalty: 0,
            presence_penalty: 0,
            tool_choice: options?.toolChoice,
            ...snippyOff,
        };
    }

    /**
     * @todo: make use of instance members instead of taking in so many parameters
     */
    protected makeRequest(
        client: OpenAI & ClientExtensionMethods,
        model: string,
        requestMessages: ChatCompletionMessageParam[],
        requestOptions: Omit<ChatCompletionCreateParamsNonStreaming, "model" | "messages">,
        toolDefinitions: CopilotChatCompletionTool[],
        maxRetries: number,
        options?: GetCompletionWithToolsOptions,
    ): Promise<{
        data: OpenAI.Chat.Completions.ChatCompletion;
        response: Response;
        request_id: string | null | undefined;
    }> {
        return client.chat.completions
            .create(
                {
                    model: model,
                    messages: [...requestMessages],
                    ...requestOptions,
                    tools: toolDefinitions,
                },
                {
                    // enable retries for failures like rate limiting
                    maxRetries: maxRetries,
                    headers: options?.requestHeaders,
                },
            )
            .withResponse();
    }

    public async *getCompletionWithTools(
        systemMessage: string,
        initialMessages: ChatCompletionMessageParam[],
        tools: Tool[],
        options?: GetCompletionWithToolsOptions,
    ): AsyncGenerator<Event> {
        const model = this.model;
        const client = await this.clientPromise;
        const modelInfo = await this.modelPromise;

        const toolSet = createToolSet(tools);

        const messages: CopilotChatCompletionMessageParam[] = [
            { role: "system", content: systemMessage, copilot_cache_control },
            ...initialMessages,
        ];

        const toolDefinitions = tools.map(
            (t, index) =>
                ({
                    type: "function",
                    function: {
                        name: t.name,
                        description: t.description,
                        parameters: t.input_schema,
                    },
                    copilot_cache_control: index === tools.length - 1 ? copilot_cache_control : undefined,
                }) satisfies CopilotChatCompletionTool,
        );

        this.logger.startGroup(`Completion request configuration: `, LogLevel.Debug);
        this.logger.debug("Client options: ");
        this.logger.debug(JSON.stringify(this.clientOptions, null, 2));
        this.logger.debug("Request options: ");
        this.logger.debug(JSON.stringify(options ?? {}, null, 2));
        this.logger.debug("Tools: ");
        this.logger.debug(JSON.stringify(toolDefinitions, null, 2));
        this.logger.endGroup(LogLevel.Debug);

        const requestOptions = this.getCompletionOptions(tools, options);

        // Incremented before iterating the while loop
        let turnCount = options?.initialTurnCount !== undefined ? options.initialTurnCount + 1 : 0;

        let responseEmitted = false;
        let userHalted = false;

        const processedImages: Set<string> = new Set();
        while (!responseEmitted && !userHalted) {
            const turnStartMs: number = Date.now();
            yield {
                kind: "turn_started",
                model: model,
                modelInfo: modelInfo,
                turn: turnCount,
                timestampMs: turnStartMs,
            } satisfies TurnEvent;

            let startModelCallMs: number = 0;
            let modelCallDurationMs: number = 0;

            let retry = 0;
            // Retry on code 400 Bad Request because some models may return this code for transient issues.
            const retryableStatusCodes = [429, 503, 500, 400];
            const maxRetries = this.clientOptions.retryPolicy.maxRetries;

            let retryAfterBackoffExtra =
                this.clientOptions.retryPolicy.rateLimitRetryPolicy.initialRetryBackoffExtraSeconds;
            let totalRetryWaitTime = 0;
            const maxRetryAfterSeconds = this.clientOptions.retryPolicy.rateLimitRetryPolicy.maxRetryAfterSeconds;

            let modelTokenLimit =
                modelInfo.capabilities.limits.max_prompt_tokens ||
                modelInfo.capabilities.limits.max_context_window_tokens;
            let tokenLimitRetryBuffer = 1;
            const maxTokenLimitRetryBuffer = 0.5;
            const tokenLimit = () => modelTokenLimit * tokenLimitRetryBuffer;

            let response: ChatCompletion | undefined;
            let lastAPIError: APIError | undefined;

            try {
                if (turnCount === 0) {
                    const messagesTokenCount = countTokensInChatCompletionMessages(messages, model);
                    const toolDefinitionsTokenCount = countTokensInToolDefinitions(toolDefinitions, model);
                    const initialInputsTokenCount = messagesTokenCount + toolDefinitionsTokenCount;
                    if (initialInputsTokenCount > modelTokenLimit) {
                        if (options?.failIfInitialInputsTooLong) {
                            throw new Error(
                                `Initial inputs exceed the model's token limit of ${modelTokenLimit} tokens.`,
                            );
                        } else {
                            this.logger.warning(
                                `Initial inputs exceed the model's token limit of ${modelTokenLimit} tokens. The model may not be able to process the inputs correctly.`,
                            );
                        }
                    }
                }

                let requestMessages: ChatCompletionMessageParam[] = [];
                let modelCall: ModelCallParam | undefined;
                do {
                    if (retry > 0) {
                        yield {
                            kind: "turn_retry",
                            model: model,
                            modelInfo: modelInfo,
                            turn: turnCount,
                            timestampMs: Date.now(),
                        } satisfies TurnEvent;
                    }
                    try {
                        // Only worry about JIT instructions if we are not retrying.
                        if (retry == 0) {
                            // Add JIT instructions to the end of messages to steer the model. Note this will always take the highest priority for the model.
                            const jitInstructions = options?.getJitInstructions?.();
                            if (jitInstructions) {
                                this.logger.debug(`Adding JIT instructions to the history: ${jitInstructions}`);
                                const jitInstructionsMessage: CopilotChatCompletionMessageParam = {
                                    role: "user",
                                    content: jitInstructions,
                                };
                                messages.push(jitInstructionsMessage);

                                yield {
                                    kind: "message",
                                    message: jitInstructionsMessage,
                                    turn: turnCount,
                                    source: "jit-instruction",
                                } satisfies UserMessageEvent;
                            }
                        }

                        const [optionalImageMessages, visionTelemetryMetrics] = await this.imageMessagesIfAllowed(
                            messages,
                            processedImages,
                            toolSet,
                        );
                        if (optionalImageMessages.length > 0) {
                            this.logger.debug(`Using ${optionalImageMessages.length} image messages`);
                        }
                        messages.push(...optionalImageMessages);
                        for (const imageMessage of optionalImageMessages) {
                            if (messageContainsScreenshot(imageMessage)) {
                                yield {
                                    kind: "message",
                                    message: imageMessage,
                                    turn: turnCount,
                                } satisfies MessageEvent;
                            }
                        }

                        const messageIsImageMessage = (m: ChatCompletionMessageParam): boolean =>
                            m.role === "user" &&
                            typeof m.content !== "string" &&
                            m.content.some((c) => c.type === "image_url");
                        visionTelemetryMetrics[`allImagesSendToLlm`] = messages.filter(messageIsImageMessage).length;

                        yield {
                            kind: "image_processing",
                            turn: turnCount,
                            imageProcessingMetrics: visionTelemetryMetrics,
                        } satisfies ImageProcessingEvent;

                        for (const processor of options?.processors?.preRequest || []) {
                            const processorStream = processor.preRequest({
                                turn: turnCount,
                                messages: messages,
                                toolDefinitions: toolDefinitions,
                                modelName: model,
                                tokenLimit: tokenLimit(),
                            });
                            for await (const event of processorStream) {
                                yield event;
                            }
                        }

                        this.logger.startGroup("Sending request to the AI model", LogLevel.Debug);

                        requestMessages = messages.map((message, index) => {
                            return index === messages.length - 1 ? { ...message, copilot_cache_control } : message;
                        });
                        startModelCallMs = Date.now();
                        const res = await this.makeRequest(
                            client,
                            model,
                            requestMessages,
                            requestOptions,
                            toolDefinitions,
                            maxRetries,
                            options,
                        );
                        modelCallDurationMs = Date.now() - startModelCallMs;
                        response = res.data;
                        modelCall = {
                            model: response.model,
                            api_id: response.id,
                            request_id: res.response.headers.get("x-github-request-id") || undefined,
                        };

                        yield {
                            kind: "model_call_success",
                            turn: turnCount,
                            modelCallDurationMs: modelCallDurationMs,
                            modelCall: modelCall,
                            responseChunk: completionToChunk(response),
                            responseUsage: response.usage,
                            requestMessages: JSON.stringify(requestMessages),
                        } satisfies ModelCallSuccessEvent;
                        yield {
                            kind: "session_log",
                            sessionLog: completionToChunk(response),
                            modelCallDurationMs: modelCallDurationMs,
                            requestMessages: JSON.stringify(requestMessages),
                        } satisfies SessionLogEvent;

                        this.logger.debug(`response (Request-ID ${res.request_id}):`);
                        this.logger.debug("data:");
                        this.logger.debug(JSON.stringify(res.data, null, 2));
                    } catch (error) {
                        modelCallDurationMs = Date.now() - startModelCallMs;

                        const apiError = CAPIError.fromAPIError(error as APIError);
                        const innerApiError = apiError?.error as
                            | {
                                  message?: string;
                                  [key: string]: unknown;
                              }
                            | undefined;

                        if (apiError) {
                            yield {
                                kind: "model_call_failure",
                                turn: turnCount,
                                modelCallDurationMs: modelCallDurationMs,
                                requestMessages: JSON.stringify(requestMessages),
                                modelCall: {
                                    model: model,
                                    status: apiError.status,
                                    error: JSON.stringify(apiError?.error),
                                    api_id: apiError.request_id || undefined,
                                    request_id: apiError.headers?.["x-github-request-id"] || "",
                                },
                            };

                            lastAPIError = apiError;

                            const userFacingRequestId =
                                apiError.ghRequestId ||
                                apiError.headers?.["x-github-request-id"] ||
                                apiError.request_id;
                            this.logger.error(`error (Request-ID ${userFacingRequestId})`);

                            this.logger.error(JSON.stringify(apiError, null, 2));

                            this.logger.debug(`Failed to get response from the AI model: ${apiError}`);
                            if (innerApiError) {
                                this.logger.debug(`Inner error: ${JSON.stringify(innerApiError, null, 2)}`);
                            }

                            const statusCode = apiError.status;

                            const retryDueToErrorCodeRetryPolicy = !statusCode
                                ? false
                                : this.clientOptions.retryPolicy.errorCodesToRetry.some((errorCode) => {
                                      if (Array.isArray(errorCode)) {
                                          return (
                                              statusCode >= errorCode[0] &&
                                              (errorCode[1] === undefined || statusCode <= errorCode[1])
                                          );
                                      } else {
                                          return statusCode === errorCode;
                                      }
                                  });

                            let retryAfter: number | undefined;
                            const tokenLimitMatch =
                                typeof innerApiError?.message === "string"
                                    ? innerApiError.message.match(
                                          /prompt token count of (\d+) exceeds the limit of (\d+)/,
                                      )
                                    : undefined;
                            if (apiError.status === 402) {
                                // Reset X-Initiator header to "agent" and retry immediately for 402 Payment Required
                                client.setInitiatorHeader("agent");
                                retryAfter = 1;
                            } else if (apiError.status === 400 && tokenLimitMatch) {
                                retryAfter = 1;
                                const tokenLimitFromError = parseInt(tokenLimitMatch[2], 10);
                                if (tokenLimitFromError < modelTokenLimit) {
                                    // If the error indicates a lower limit for the model than we were told, switch to it and reset the buffer
                                    modelTokenLimit = tokenLimitFromError;
                                    tokenLimitRetryBuffer = 1;
                                } else {
                                    // Given an equal or larger limit, increase the buffer since we failed to stay under the limit
                                    // Do not switch to a larger limit, we will not exceed what `listModels` returns
                                    tokenLimitRetryBuffer = Math.max(
                                        tokenLimitRetryBuffer - maxTokenLimitRetryBuffer / maxRetries,
                                        maxTokenLimitRetryBuffer,
                                    );
                                }
                                this.logger.debug(
                                    `Adjusted model token limit to ${modelTokenLimit}, token limit retry buffer to ${tokenLimitRetryBuffer}`,
                                );
                            } else if (apiError.status === 413) {
                                // Error 413 indicates that the request is too large - only known cause of this is that an attached image is too large
                                // For today, we'll handle this by removing all of the images from the request
                                // TODO: Improve handling of large image attachments (https://github.com/github/sweagentd/issues/2407)
                                this.logger.debug(`Request is too large, trying to remove images from the request`);
                                const messageIsImageMessage = (m: ChatCompletionMessageParam): boolean =>
                                    m.role === "user" &&
                                    typeof m.content !== "string" &&
                                    m.content.some((c) => c.type === "image_url");
                                let largeImagesRemoved = 0;
                                for (let i = messages.length - 1; i >= 0; i--) {
                                    if (messageIsImageMessage(messages[i])) {
                                        messages.splice(i, 1);
                                        largeImagesRemoved++;
                                        yield {
                                            kind: "images_removed",
                                            turn: turnCount,
                                            imagesRemoved: 1,
                                            largeImagesRemoved: 1,
                                        } satisfies ImageRemovalEvent;
                                    }
                                }
                                if (largeImagesRemoved > 0) {
                                    // User facing log message
                                    this.logger.info(
                                        `Images have been removed from the request due to size constraints. Please resize images to be smaller than ${formatBytes(modelInfo.capabilities.limits.vision?.max_prompt_image_size || 0)}.`,
                                    );
                                    this.logger.debug(`Removed ${largeImagesRemoved} image messages from the request`);
                                    retryAfter = 1;
                                } else {
                                    this.logger.debug(
                                        `No image messages found in the request - unsure why the request is too large - will fail`,
                                    );
                                }
                            } else if (
                                (typeof apiError.status === "number" &&
                                    retryableStatusCodes.includes(apiError.status)) ||
                                retryDueToErrorCodeRetryPolicy
                            ) {
                                retryAfter =
                                    parseInt(
                                        apiError.headers?.["retry-after"] ||
                                            `${this.clientOptions.retryPolicy.rateLimitRetryPolicy.defaultRetryAfterSeconds}`,
                                        10,
                                    ) + retryAfterBackoffExtra;
                                retryAfterBackoffExtra *=
                                    this.clientOptions.retryPolicy.rateLimitRetryPolicy.retryBackoffExtraGrowth;

                                // If we have any messages with images, remove them in attempt to avoid workflow failure:
                                // we cannot know for sure that error is caused by vision but it's an assumption we can make
                                if (retry == maxRetries - 1) {
                                    const messageIsImageMessage = (m: ChatCompletionMessageParam): boolean =>
                                        m.role === "user" &&
                                        typeof m.content !== "string" &&
                                        m.content.some((c) => c.type === "image_url");
                                    for (let i = messages.length - 1; i >= 0; i--) {
                                        if (messageIsImageMessage(messages[i])) {
                                            this.logger.debug(`Removing image message at index ${i} to avoid failure`);
                                            messages.splice(i, 1);

                                            yield {
                                                kind: "images_removed",
                                                turn: turnCount,
                                                imagesRemoved: 1,
                                            } satisfies ImageRemovalEvent;
                                        }
                                    }
                                }
                            }

                            if (retryAfter !== undefined && retryAfter <= maxRetryAfterSeconds) {
                                const jitterMultiplier = 0.8 + Math.random() * 0.4; // ±20% jitter
                                const retryDelayWithJitter = retryAfter * jitterMultiplier;
                                this.logger.debug(`Retrying after ${retryDelayWithJitter} seconds...`);
                                await new Promise((resolve) => setTimeout(resolve, retryDelayWithJitter * 1000));
                                totalRetryWaitTime += retryDelayWithJitter;
                                this.logger.debug(`Retrying after ${retryAfter} seconds... Will try again now!`);

                                // If we have a valid retry after, we'll retry, assuming there's still retries left.
                                // If there's no retries left, we'll throw a generic error with this error as its "cause".
                                continue;
                            } else {
                                // If we don't have a valid retry after, we'll ultimately re-throw the error
                                if (!retryAfter) {
                                    this.logger.error(`Retry after is not set. Giving up.`);
                                } else {
                                    this.logger.error(`Retry after ${retryAfter} seconds is too long. Giving up.`);
                                }
                            }
                        }
                        // the rest we should rethrow
                        const errorToThrow = apiError || error;

                        if (processedImages.size > 0 && typeof errorToThrow === "object" && errorToThrow !== null) {
                            // eslint-disable-next-line @typescript-eslint/no-explicit-any
                            (errorToThrow as any).isVisionFlow = true;
                        }
                        this.logger.error(`error`);
                        this.logger.error(JSON.stringify(errorToThrow, null, 2));

                        throw errorToThrow;
                    } finally {
                        this.logger.endGroup(LogLevel.Debug);
                    }
                } while (!response && retry++ < maxRetries);

                if (!response) {
                    // Log some recent messages to help with debugging
                    this.logger.debug(`Recent messages prior to failure:`);
                    for (let i = Math.max(0, messages.length - 6); i < messages.length; i++) {
                        // CAPI error messages often reference messages at a specific index, so we print the index along with the message
                        this.logger.debug(`${i}: ${JSON.stringify(messages[i], null, 2)}`);
                    }

                    // Include the structured error if that's the cause of failures. Structured error help surface details to users via agent sessions
                    // e.g. 429 errors being retried but eventually failed
                    const finalError = new Error(
                        `Failed to get response from the AI model; retried ${retry - 1} times (total retry wait time: ${totalRetryWaitTime} seconds)`,
                        { cause: lastAPIError },
                    );

                    // Check if this is a rate limit exceeded error and mark it to skip Sentry reporting
                    if (this.isRateLimitExceededError(finalError)) {
                        this.logger.info(
                            `Rate limit exceeded after ${retry - 1} retries, will fail job but skip Sentry reporting`,
                        );

                        // Mark the error to skip Sentry reporting
                        (finalError as Error & { skipReport?: boolean }).skipReport = true;
                    }

                    // Always throw the final error to fail the job
                    throw finalError;
                }

                if (this.isToolCallResponse(response)) {
                    for (const message of response.choices.map((c) => c.message as CopilotChatCompletionMessage)) {
                        // The LLM may sometimes request tools that do not exist or provided arguments
                        // which are not valid JSON. If we leave this data as is, then CAPI will
                        // reject our response back to the LLM even though it was CAPI's LLM who requested
                        // the invalid tools in the first place! 😤
                        ensureToolCallsHaveValidNames(message.tool_calls || []);

                        messages.push(message);

                        yield {
                            kind: "message",
                            modelCall: modelCall,
                            message: cleanUpMessage(message),
                            turn: turnCount,
                        } satisfies AssistantMessageEvent;
                    }

                    const toolTokenLimit = tokenLimit() * this.clientOptions.toolTokenBudgetProportion;
                    const toolCallbackOptions = buildToolCallbackOptions(model, toolTokenLimit, this);

                    // If any of the tool calls have invalid JSON arguments, this will handle them gracefully.
                    const toolResults = this.callTools(
                        response,
                        toolSet,
                        toolCallbackOptions,
                        options?.executeToolsInParallel,
                    );

                    for await (const toolResult of toolResults) {
                        if (toolResult.originalToolResult.resultType === "rejected") {
                            userHalted = true;
                        }

                        let chatCompletionMessage: ChatCompletionUserMessageParam | ChatCompletionToolMessageParam;

                        if (modelInfo.capabilities.supports.vision && toolResult.binaryResultForLlm) {
                            // If the tool result contains binary results, we will create a user message with the image URLs
                            chatCompletionMessage = {
                                role: "tool",
                                tool_call_id: toolResult.toolCallId,
                                content: toolResult.resultForLlm,
                            };

                            messages.push(chatCompletionMessage);
                            yield {
                                kind: "message",
                                modelCall: modelCall,
                                message: chatCompletionMessage,
                                turn: turnCount,
                            } satisfies ToolMessageEvent;
                            yield {
                                kind: "tool_execution",
                                turn: turnCount,
                                toolCallId: toolResult.toolCallId,
                                toolResult: toolResult.originalToolResult,
                                durationMs: toolResult.durationMs,
                            } satisfies ToolExecutionEvent;

                            for (const binaryResult of toolResult.binaryResultForLlm) {
                                if (binaryResult.type === "image") {
                                    let imageText = `Here is an image:`;
                                    if (toolResult.function?.toLowerCase().includes("screenshot")) {
                                        imageText = "Here is a screenshot:";
                                    }
                                    chatCompletionMessage = {
                                        role: "user",
                                        content: [
                                            {
                                                type: "text",
                                                text: imageText,
                                            }, // Optional text description
                                            {
                                                type: "image_url",
                                                image_url: {
                                                    url: `data:${binaryResult.mimeType};base64,${binaryResult.data}`,
                                                },
                                            },
                                        ],
                                    };
                                    messages.push(chatCompletionMessage);
                                }
                            }
                        } else {
                            chatCompletionMessage = {
                                role: "tool",
                                tool_call_id: toolResult.toolCallId,
                                content: toolResult.resultForLlm,
                            };
                            messages.push(chatCompletionMessage);
                            yield {
                                kind: "message",
                                modelCall: {
                                    model: response.model,
                                    api_id: response.id,
                                },
                                message: chatCompletionMessage,
                                turn: turnCount,
                            } satisfies ToolMessageEvent;
                            yield {
                                kind: "tool_execution",
                                turn: turnCount,
                                toolCallId: toolResult.toolCallId,
                                toolResult: toolResult.originalToolResult,
                                durationMs: toolResult.durationMs,
                            } satisfies ToolExecutionEvent;
                        }
                    }
                } else {
                    for (const message of response.choices.map((c) => c.message as CopilotChatCompletionMessage)) {
                        messages.push(message);
                        yield {
                            kind: "response",
                            modelCall: {
                                model: response.model,
                                api_id: response.id,
                            },
                            response: cleanUpMessage(message),
                            turn: turnCount,
                        };
                        responseEmitted = true;
                    }
                }
            } catch (error) {
                let errorString: string;
                if (error instanceof Error && error.cause && error.cause instanceof CAPIError) {
                    errorString = `${error} (Cause: ${error.cause})`;
                } else {
                    errorString = `${error}`;
                }
                yield {
                    kind: "turn_failed",
                    model: model,
                    modelInfo: modelInfo,
                    turn: turnCount,
                    error: errorString,
                    timestampMs: Date.now(),
                } satisfies TurnEvent;
                throw error;
            } finally {
                if (isFeatureFlagEnabled(this.settings, "copilot_swe_agent_initiator_agent")) {
                    // An LLM request succeeded - from now on, we always want to use the "agent" initiator header, which
                    // will only charge the user for the first request, and not for subsequent requests.
                    client.setInitiatorHeader("agent");
                }

                // @todo delete turn telemetry things
                const turnFinishTime = Date.now();
                yield {
                    kind: "turn_ended",
                    model: model,
                    modelInfo: modelInfo,
                    turn: turnCount,
                    timestampMs: turnFinishTime,
                } satisfies TurnEvent;
                turnCount++;
            }
        }
    }

    private async *callTools(
        response: ChatCompletion,
        toolSet: Record<string, Tool>,
        standardToolCallbackOptions: ToolCallbackOptions,
        runToolsInParallel = false,
    ): AsyncGenerator<CopilotToolCallResult> {
        const choices = response.choices.filter((c) => c.message.tool_calls !== undefined);
        if (choices.length === 0) {
            throw new Error("Tool call response is missing");
        }

        const toolCalls = choices.flatMap((c) => c.message.tool_calls || []);
        if (!toolCalls || toolCalls.length === 0) {
            throw new Error("Tool call is missing");
        }

        this.logger.debug(`Tool calls count: ${toolCalls.length}`);
        if (runToolsInParallel) {
            this.logger.debug(`Running tool calls in parallel`);
            // Parallelize tool calls for efficiency
            yield* executeInParallelAndYieldAsCompleted(
                toolCalls.map((toolCall) => () => this.callTool(toolCall, toolSet, standardToolCallbackOptions)),
            );
        } else {
            this.logger.debug(`Running tool calls sequentially`);
            // Run tool calls sequentially
            for (const toolCall of toolCalls) {
                const toolResult = await this.callTool(toolCall, toolSet, standardToolCallbackOptions);
                yield toolResult;
            }
        }
    }

    private async callTool(
        toolCall: ChatCompletionMessageToolCall,
        toolSet: Record<string, Tool>,
        standardToolCallbackOptions: ToolCallbackOptions,
    ): Promise<CopilotToolCallResult> {
        let durationMs = 0;
        const toolName = toolCall?.function.name;
        if (!toolName) {
            throw new Error("Tool name is missing");
        }

        const argsStr = toolCall?.function.arguments;
        if (!argsStr) {
            throw new Error("Tool call arguments are missing");
        }

        const toolCallId = toolCall?.id;
        if (!toolCallId) {
            throw new Error("Tool call id is missing");
        }

        let toolResultExpanded: ToolResultExpanded;
        const tool: Tool | undefined = toolSet[toolName];
        try {
            const funcArgs = deepJsonParse(argsStr);

            if (!tool || !tool.callback) {
                toolResultExpanded = {
                    textResultForLlm: `Tool '${toolName}' does not exist. Available tools that can be called are ${Object.keys(toolSet).join(", ")}.`,
                    resultType: "failure",
                    error: `Tool '${toolName}' does not exist.`,
                    sessionLog: `<error>Tool '${toolName}' does not exist.</error>`,
                    toolTelemetry: {},
                };
            } else {
                const fn = tool.callback as ToolCallback;
                const start = Date.now();
                const tooSpecificToolCallbackOptions: ToolCallbackOptions = {
                    ...standardToolCallbackOptions,
                    toolCallId: toolCallId,
                    toolOptions: this.settings.service?.tools?.[toolName],
                };
                const toolResult = await fn({ ...funcArgs }, tooSpecificToolCallbackOptions);
                durationMs = Date.now() - start;
                toolResultExpanded =
                    typeof toolResult === "string"
                        ? {
                              textResultForLlm: toolResult,
                              resultType: "success",
                              toolTelemetry: {},
                          }
                        : toolResult;
            }
        } catch (error) {
            if (error instanceof SyntaxError) {
                // Gracefully handle JSON parse issues by asking the LLM to retry with valid JSON
                toolResultExpanded = {
                    textResultForLlm: `The arguments for the tool call '${toolName}' were not valid JSON. The arguments have been cleared. Arguments: ${argsStr}. JSON parse error: ${error.message}`,
                    resultType: "failure",
                    error: `${error.message}`,
                    sessionLog: `<error>Failed to execute \`${toolName}\` tool with arguments: ${argsStr} due to syntax error: ${error.message}</error>`,
                    toolTelemetry: {
                        properties: {
                            wasSyntaxError: "true",
                        },
                    },
                };

                // Clear out the arguments from the tool call to avoid further issues when calling back to the LLM
                toolCall.function.arguments = "{}";
            } else {
                toolResultExpanded = {
                    textResultForLlm: `Failed to execute \`${toolName}\` tool with arguments: ${argsStr} due to error: ${error}`,
                    resultType: "failure",
                    error: `${error}`,
                    sessionLog: `<error>Failed to execute \`${toolName}\` tool with arguments: ${argsStr}</error>`,
                    toolTelemetry: {},
                };
            }
        }

        return {
            originalToolResult: toolResultExpanded,
            toolCallId: toolCallId,
            function: toolName,
            resultForLlm: toolResultExpanded.textResultForLlm,
            binaryResultForLlm: toolResultExpanded.binaryResultForLlm,
            durationMs: durationMs,
        };
    }

    private isToolCallResponse(response: ChatCompletion): boolean {
        return response.choices.find((c) => c.message.tool_calls !== undefined) !== undefined;
    }

    private isRateLimitExceededError(error: Error | unknown): boolean {
        if (error instanceof Error) {
            // Check the main error message
            if (
                error.message?.includes("exceeded maximum number of retries") &&
                error.message?.includes("rate-limited requests")
            ) {
                return true;
            }

            // Check if it's the "Failed to get response from the AI model" error with rate limit cause
            if (error.message?.includes("Failed to get response from the AI model")) {
                // Check if the cause is a CAPIError with rate limiting status codes
                if (error.cause instanceof CAPIError) {
                    const statusCode = error.cause.status;
                    return statusCode === 429; // Too Many Requests
                }
            }
        }
        return false;
    }

    private async getModel(): Promise<Model> {
        const model = this.clientOptions.model;
        const defaultResult: Model = {
            id: model,
            name: model,
            capabilities: {
                supports: {
                    vision: false,
                },
                limits: {
                    max_prompt_tokens: 90_000,
                    max_context_window_tokens: 128_000,
                    vision: {
                        max_prompt_image_size: 3_145_728,
                        max_prompt_images: 1,
                        supported_media_types: ["image/jpeg", "image/png", "image/webp"],
                    },
                },
            },
        };

        const client = await this.clientPromise;
        const models = await client.listModels();
        const matchingModel = models.filter((m) => m.id === model).at(0);

        return matchingModel || defaultResult;
    }

    private async resolveRepositoryImages(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        messages: any[],
        maxImagesToResolve: number,
        githubServerUrl: string,
        logger: RunnerLogger,
        maxImageSize: number,
        processedImageUrls: Set<string>,
        visionMetrics: VisionMetrics,
        toolSet: Record<string, Tool>,
    ): Promise<string[]> {
        if (maxImagesToResolve <= 0) {
            return [];
        }

        // Use the function from image-utils for basic image URL resolution
        const imageReferences = extractRepositoryImageUrls(messages, maxImagesToResolve, githubServerUrl, logger);
        this.logger.debug(`Found ${imageReferences.length} repository image URLs to process`);

        // Process the image references to check sizes and get valid URLs
        const processedRefs = await this.processImageReferences(
            imageReferences,
            maxImageSize,
            processedImageUrls,
            visionMetrics,
            toolSet,
        );

        this.logger.debug(`Processed ${processedRefs.addedImages.length} repository image URLs`);

        return processedRefs.addedImages;
    }

    private async imageMessagesIfAllowed(
        messages: ChatCompletionMessageParam[],
        processedImageUrls: Set<string>,
        toolSet: Record<string, Tool>,
    ): Promise<[ChatCompletionMessageParam[], ImageProcessingMetrics]> {
        const capiModel = await this.modelPromise;
        const newImagesMessages: ChatCompletionMessageParam[] = [];
        const githubServerUrl = this.settings.github?.serverUrl || "";

        if (!capiModel.capabilities.supports.vision || !this.settings.featureFlags?.["copilot_swe_agent_vision"]) {
            return [newImagesMessages, {}];
        }

        // If there are <img> tags in the messages, embed as many as we are allowed
        const numImagesAllowed = capiModel.capabilities.limits.vision?.max_prompt_images ?? 1;
        const maxImageSize = capiModel.capabilities.limits.vision?.max_prompt_image_size ?? 3_145_728;
        let numImages = 0;
        const imgUrls: string[] = [];
        const visionMetrics: VisionMetrics = new VisionMetrics();
        const imageTextMap: Map<string, string> = new Map();

        // Find all available img tags
        for (const message of messages) {
            if (message.role == "user") {
                if (typeof message.content === "string") {
                    const imgRefs = matchAllImageReferences(
                        message.content,
                        githubServerUrl,
                        false,
                        this.logger,
                    ).filter((imgRef) => !processedImageUrls.has(imgRef.url));
                    const processedRefs = await this.processImageReferences(
                        imgRefs,
                        maxImageSize,
                        processedImageUrls,
                        visionMetrics,
                        toolSet,
                    );

                    imgUrls.push(...processedRefs.addedImages);
                } else {
                    const contents: ChatCompletionContentPart[] = [];

                    for (const [idx, content] of message.content.entries()) {
                        if (content.type === "text") {
                            const imageReferences = matchAllImageReferences(
                                content.text,
                                githubServerUrl,
                                false,
                                this.logger,
                            ).filter((imgRef) => !processedImageUrls.has(imgRef.url));
                            const processedRefs = await this.processImageReferences(
                                imageReferences,
                                maxImageSize,
                                processedImageUrls,
                                visionMetrics,
                                toolSet,
                            );

                            imgUrls.push(...processedRefs.addedImages);
                        } else if (content.type === "image_url") {
                            if (content.image_url.url.startsWith("data:")) {
                                const [uploadResult, _] = await this.extractAndUploadImageData(
                                    content,
                                    maxImageSize,
                                    visionMetrics,
                                );
                                if (uploadResult === "") {
                                    this.logger.debug(`Skipping image upload for empty image data`);
                                    continue;
                                }
                                imgUrls.push(uploadResult);

                                // Check if the image is a screenshot (we can check this by looking at the image descrition text)
                                if (idx >= 1 && contents.length > 0) {
                                    const textMsg = contents.at(-1);
                                    if (
                                        textMsg &&
                                        textMsg.type === "text" &&
                                        textMsg.text &&
                                        textMsg.text.includes("screenshot")
                                    ) {
                                        // Put custom image prompt into map, it will be used below when building final image message
                                        imageTextMap.set(uploadResult, includeScreenshotPrompt(uploadResult));
                                    }
                                }
                                // we're removing text item from contents because we will add it along with image_url item below
                                contents.pop();
                                continue;
                            }
                            numImages++;
                        }
                        contents.push(content);
                    }
                    message.content = contents;
                }
            }
        }
        this.logger.debug(`Found ${imgUrls.length} image urls in the messages`);
        this.logger.debug(`Allowed number of images: ${numImagesAllowed}`);
        // For each image we found, if we have room, add it to the newImagesMessages array

        while (numImages < numImagesAllowed && imgUrls.length > 0) {
            const imgUrl = imgUrls.shift();
            if (!imgUrl) {
                this.logger.debug(`Skipping empty image url`);
                continue;
            }

            if (processedImageUrls.has(imgUrl)) {
                this.logger.debug(`Skipping already processed image url: ${imgUrl}`);
                continue;
            }

            processedImageUrls.add(imgUrl);

            let imageText = `Here is the image url: ${imgUrl}`;

            if (imageTextMap.has(imgUrl)) {
                imageText = imageTextMap.get(imgUrl) || imageText;
            }

            this.logger.debug(`Adding image url: ${imgUrl}`);
            newImagesMessages.push({
                role: "user",
                content: [
                    { type: "text", text: imageText },
                    { type: "image_url", image_url: { url: imgUrl } },
                ],
            });
            numImages++;
        }

        if (this.settings.featureFlags?.["copilot_swe_agent_resolve_repo_images"]) {
            // If we still have room, resolve the repository images

            this.logger.debug(`Resolving repository images...`);

            if (numImages < numImagesAllowed) {
                const repoImgUrls = await this.resolveRepositoryImages(
                    messages,
                    numImagesAllowed - numImages,
                    this.settings.github?.serverUrl || "",
                    this.logger,
                    maxImageSize,
                    processedImageUrls,
                    visionMetrics,
                    toolSet,
                );

                this.logger.debug(`Resolved ${repoImgUrls.length} repository image URLs`);

                for (const imgUrl of repoImgUrls) {
                    if (numImages >= numImagesAllowed) {
                        break;
                    }

                    if (processedImageUrls.has(imgUrl)) {
                        this.logger.debug(`Skipping already processed repository image url: ${imgUrl}`);
                        continue;
                    }

                    newImagesMessages.push({
                        role: "user",
                        content: [
                            { type: "text", text: `Here is the image url:` },
                            { type: "image_url", image_url: { url: imgUrl } },
                        ],
                    });
                    processedImageUrls.add(imgUrl);
                    numImages++;
                }
            }
        }

        const imageMetrics: ImageProcessingMetrics = {
            imagesExtractedCount: visionMetrics.extractedImagesSize.length,
            base64ImagesCount: visionMetrics.base64ImagesSize.length,
            imagesRemovedDueToSize: visionMetrics.imagesRemovedDueToSize,
            imagesRemovedDueToDimensions: visionMetrics.imagesRemovedDueToDimensions,
            imagesResolvedFromGitHubMCPCount: visionMetrics.imagesResolvedFromGitHubMCPSize.length,
        };
        for (let i = 0; i < visionMetrics.base64ImagesSize.length; i++) {
            // We need to make the telemetry key unique because they're mapped to map type in sweagendt backend
            imageMetrics[`base64ImageSize_${i}`] = visionMetrics.base64ImagesSize[i];
        }
        for (let i = 0; i < visionMetrics.extractedImagesSize.length; i++) {
            imageMetrics[`extractedImageSize_${i}`] = visionMetrics.extractedImagesSize[i];
        }
        for (let i = 0; i < visionMetrics.extractedImagesSize.length; i++) {
            imageMetrics[`imagesResolvedFromGitHubMCPSize_${i}`] = visionMetrics.imagesResolvedFromGitHubMCPSize[i];
        }
        return [newImagesMessages, imageMetrics];
    }

    // Uploads the base64 image data to GitHub Copilot attachments and returns the URL of the uploaded image.
    // If the image size exceeds the maximum allowed size it won't attempt to upload it and will return an empty string.
    private async extractAndUploadImageData(
        content: ChatCompletionContentPartImage,
        maxImageSize: number,
        visionMetrics: VisionMetrics,
    ): Promise<[string, Error?]> {
        const userToken = demandCopilotToken(this.settings);
        if (!userToken) {
            this.logger.debug(`No user token available, skipping image upload`);
            return [""];
        }

        this.logger.debug(`Found base64 image data, uploading...`);
        // Parsing data:image/png;base64,abc123
        // Match image_url using regex to extract base64 data and content type
        const matches = content.image_url.url.match(/^data:(.*?);base64,(.*)$/);
        if (!matches || matches.length < 3) {
            this.logger.debug(`Invalid base64 image data format, skipping`);
            return [""];
        }

        const contentType = matches[1]; // Content type part
        if (contentType !== "image/png" && contentType !== "image/jpeg") {
            this.logger.debug(
                `Unsupported content type: ${contentType}, only image / png and image / jpeg are supported`,
            );
            return [""];
        }
        const base64ImageDataPart = matches[2]; // Base64 data part
        if (base64ImageDataPart.length === 0) {
            this.logger.debug(`Empty base64 image data, skipping`);
            return [""];
        }
        const imageBuffer = Buffer.from(base64ImageDataPart, "base64");
        const contentSize = imageBuffer.length;
        visionMetrics.base64ImagesSize.push(contentSize);
        const exceedsLimits = await imageExceedsLimits(maxImageSize, imageBuffer);
        if (exceedsLimits[0]) {
            visionMetrics.imagesRemovedDueToSize++;
            this.logger.debug(
                `Image size ${formatBytes(contentSize)} is larger than the maximum size of ${formatBytes(maxImageSize)}, skipping`,
            );
            return [
                "",
                new ImageSizeError(`Image size ${contentSize} exceeds maximum size of ${formatBytes(maxImageSize)}`),
            ];
        }
        if (exceedsLimits[1]) {
            visionMetrics.imagesRemovedDueToDimensions++;
            this.logger.debug(`Image dimensions exceed the maximum limit, skipping`);
            return ["", new ImageSizeError(`Image dimensions exceed the maximum limit`)];
        }
        try {
            const uploadedUrl = await this.uploadBase64ImageData(base64ImageDataPart, contentType, userToken);
            if (!uploadedUrl) {
                this.logger.debug(`Failed to upload image, skipping`);
                return [""];
            }

            this.logger.debug(`Uploaded image url: ${uploadedUrl}`);
            content.image_url.url = uploadedUrl;
            return [uploadedUrl];
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            this.logger.debug(`Error during image upload: ${errorMessage}`);
            return [""];
        }
    }

    private async uploadBase64ImageData(
        imageData: string,
        contentType: string,
        userToken: string,
    ): Promise<string | null> {
        try {
            // Upload to GitHub Copilot attachments
            const uploadUrl = `https://uploads.github.com/user-attachments/assets`;
            const fileExtension = contentType.split("/").at(1);
            const fileName = `image-${Date.now()}.${fileExtension}`;

            if (!imageData || imageData.trim() === "") {
                this.logger.error("Empty image data provided for upload");
                return null;
            }
            const imageBinaryData = Buffer.from(imageData, "base64");

            if (imageBinaryData.length === 0) {
                this.logger.error("Converted image data has zero length");
                return null;
            }

            const repoId = this.settings.github?.repo?.id;

            if (!repoId) {
                this.logger.error("GitHub repository ID is not configured, cannot upload image");
                return null;
            }

            const url = `${uploadUrl}?name=${encodeURIComponent(fileName)}&content_type=${encodeURIComponent(contentType)}&repository_id=${repoId}`;
            this.logger.debug(
                `Uploading image data (${formatBytes(imageBinaryData.length)}) to ${uploadUrl} with name ${encodeURIComponent(fileName)} and content type ${encodeURIComponent(contentType)}`,
            );
            const headers = {
                "Content-Type": "application/octet-stream",
                Accept: "application/vnd.github+json",
                "X-GitHub-Api-Version": "2022-11-28",
                Authorization: "Bearer " + userToken,
            };

            const uploadResponse = await fetch(url, {
                method: "POST",
                headers: headers,
                body: imageBinaryData,
            }).catch((error) => {
                if (error instanceof Error) {
                    this.logger.error(`Failed to upload image: ${error.message}`);
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    if ((error as any).code === "ECONNREFUSED") {
                        this.logger.error("Connection refused. The server might be down or blocked.");
                        // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    } else if ((error as any).code === "ETIMEDOUT") {
                        this.logger.error("Connection timed out. Check network conditions or server load.");
                    } else if (
                        error.message.includes("request entity too large") ||
                        error.message.includes("payload too large")
                    ) {
                        this.logger.error("Request payload too large. Try reducing the image size.");
                    }
                } else if (typeof error === "object" && error !== null && "code" in error && "message" in error) {
                    this.logger.error(
                        // eslint-disable-next-line @typescript-eslint/no-explicit-any
                        `Failed to upload image: ${(error as any).message}`,
                    );
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    if ((error as any).code === "ECONNREFUSED") {
                        this.logger.error("Connection refused. The server might be down or blocked.");
                        // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    } else if ((error as any).code === "ETIMEDOUT") {
                        this.logger.error("Connection timed out. Check network conditions or server load.");
                    } else if (
                        // eslint-disable-next-line @typescript-eslint/no-explicit-any
                        (error as any).message.includes("request entity too large") ||
                        // eslint-disable-next-line @typescript-eslint/no-explicit-any
                        (error as any).message.includes("payload too large")
                    ) {
                        this.logger.error("Request payload too large. Try reducing the image size.");
                    }
                } else {
                    this.logger.error(`Failed to upload image: ${String(error)}`);
                }

                this.logger.debug(
                    `Complete error details: ${JSON.stringify(
                        {
                            // eslint-disable-next-line @typescript-eslint/no-explicit-any
                            code: (error as any)?.code,
                            // eslint-disable-next-line @typescript-eslint/no-explicit-any
                            message: (error as any)?.message,
                            // eslint-disable-next-line @typescript-eslint/no-explicit-any
                            stack: (error as any)?.stack,
                            // eslint-disable-next-line @typescript-eslint/no-explicit-any
                            name: (error as any)?.name,
                            // eslint-disable-next-line @typescript-eslint/no-explicit-any
                            cause: (error as any)?.cause,
                        },
                        null,
                        2,
                    )}`,
                );
                throw error;
            });

            if (!uploadResponse.ok) {
                const errorText = await uploadResponse.text().catch(() => "Unable to read error response");
                this.logger.error(
                    `Failed to upload image: ${uploadResponse.status} ${uploadResponse.statusText} - ${errorText}`,
                );
                return null;
            }

            const uploadResult = (await uploadResponse.json()) as {
                url: string;
            };
            this.logger.debug(`Upload result: ${JSON.stringify(uploadResult, null, 2)}`);

            // Return the URL from the upload response
            return uploadResult.url || null;
        } catch (error) {
            this.logger.error(`Error handling image upload: ${error instanceof Error ? error.message : String(error)}`);
            if (error instanceof Error && error.stack) {
                this.logger.debug(`Stack trace: ${error.stack}`);
            }
            return null;
        }
    }

    private async getRepositoryImageViaTools(
        imgRef: ImageReference,
        toolSet: Record<string, Tool>,
    ): Promise<{ buffer: Buffer; mimeType: string } | null> {
        this.logger.debug(
            `Attempting to retrieve repository image: ${imgRef.owner}/${imgRef.repo}/${imgRef.path}@${imgRef.branch}`,
        );
        this.logger.debug(`Available tools: ${Object.keys(toolSet).join(", ")}`);
        this.logger.debug(`Looking for GitHub MCP tool: 'github-mcp-server-get_file_contents'`);

        const githubTool = toolSet["github-mcp-server-get_file_contents"];
        if (githubTool) {
            this.logger.debug(`GitHub MCP tool found, calling with image reference: ${JSON.stringify(imgRef)}`);

            try {
                const result = await githubTool.callback(
                    {
                        owner: imgRef.owner,
                        repo: imgRef.repo,
                        path: imgRef.path,
                        branch: imgRef.branch,
                    },
                    buildToolCallbackOptions(this.model, 10000, this),
                );
                this.logger.debug(`GitHub tool call completed. Result type: ${typeof result}`);
                this.logger.debug(
                    `GitHub tool call result structure: ${typeof result === "object" && result !== null ? Object.keys(result).join(", ") : typeof result}`,
                );

                if (
                    typeof result === "object" &&
                    result !== null &&
                    "binaryResultForLlm" in result &&
                    Array.isArray(result.binaryResultForLlm)
                ) {
                    this.logger.debug(
                        `Tool result has binaryResultForLlm array with ${result.binaryResultForLlm.length} items`,
                    );
                    try {
                        for (const binaryResult of result.binaryResultForLlm) {
                            if (binaryResult.data && binaryResult.mimeType) {
                                const buffer = Buffer.from(binaryResult.data, "base64");
                                const mimeType = binaryResult.mimeType;
                                return { buffer, mimeType };
                            }
                        }
                    } catch (parseError) {
                        this.logger.error(`Failed to parse GitHub tool response: ${parseError}`);
                        this.logger.debug(`binaryResultForLlm length: ${result.binaryResultForLlm?.length || 0}`);
                    }
                } else if (typeof result === "string") {
                    this.logger.debug(`GitHub tool returned string result: ${result}`);
                } else {
                    this.logger.debug(
                        `GitHub tool call failed or returned unexpected format. Result structure: ${typeof result === "object" && result !== null ? Object.keys(result).join(", ") : typeof result}`,
                    );
                }
            } catch (error) {
                this.logger.error(`Error calling GitHub MCP tool: ${error}`);
            }
        } else {
            this.logger.debug(`GitHub MCP tool 'github-mcp-server-get_file_contents' not found in toolSet`);
            this.logger.debug(`Available tool names: ${JSON.stringify(Object.keys(toolSet))}`);
        }

        this.logger.debug(`Failed to retrieve repository image, returning null`);
        return null;
    }

    private async getImgRefsSmallerThanMaxSize(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        attachmentPromises: any,
        visionMetrics: VisionMetrics,
        maxImageSize: number,
        processedImageUrls: Set<string>,
    ): Promise<string[]> {
        const imgRefsSmallerThanMaxSize: string[] = [];
        const attachments = await Promise.all(attachmentPromises);
        for (const { imgRef, attachment } of attachments) {
            this.logger.debug(
                `Processing attachment for ${imgRef.url}, has buffer: ${!!attachment.buffer}, buffer size: ${attachment.buffer?.length || 0}`,
            );

            if (attachment.buffer) {
                const exceedsLimits = await imageExceedsLimits(maxImageSize, attachment.buffer);
                visionMetrics.extractedImagesSize.push(attachment.buffer.length);

                this.logger.debug(
                    `Image size check for ${imgRef.url}: size=${attachment.buffer.length}, maxSize=${maxImageSize}, exceedsSize=${exceedsLimits[0]}, exceedsDimensions=${exceedsLimits[1]}`,
                );

                // Vision telemetry
                if (exceedsLimits[0]) {
                    visionMetrics.imagesRemovedDueToSize++;
                    this.logger.debug(
                        `Image ${imgRef.url} removed due to size (${attachment.buffer.length} > ${maxImageSize})`,
                    );
                } else if (exceedsLimits[1]) {
                    visionMetrics.imagesRemovedDueToDimensions++;
                    this.logger.debug(`Image ${imgRef.url} removed due to dimensions`);
                }
                if (exceedsLimits[0] || exceedsLimits[1]) {
                    processedImageUrls.add(imgRef.url);
                    // User facing log message
                    this.logger.info(
                        `Image has been removed from the request due to size constraints. Please resize images to be smaller than ${formatBytes(maxImageSize)}.`,
                    );
                    continue;
                }
                imgRefsSmallerThanMaxSize.push(imgRef.url);
                this.logger.debug(`Image ${imgRef.url} passed size checks and added to results`);
            } else {
                processedImageUrls.add(imgRef.url);
                this.logger.debug(`Skipping image ${imgRef.url} - could not fetch image data`);
            }
        }

        return imgRefsSmallerThanMaxSize;
    }

    private async processImageReferences(
        imgRefs: ImageReference[],
        maxImageSize: number,
        processedImageUrls: Set<string>,
        visionMetrics: VisionMetrics,
        toolSet: Record<string, Tool>,
    ): Promise<{ addedImages: string[] }> {
        const githubServerUrl = this.settings.github?.serverUrl || "";

        this.logger.debug(`Processing ${imgRefs.length} image references`);
        this.logger.debug(`GitHub server URL: ${githubServerUrl}`);
        this.logger.debug(`Image references: ${imgRefs.map((ref) => ref.url).join(", ")}`);

        const attachmentPromises = imgRefs
            .filter((imgRef) => !processedImageUrls.has(imgRef.url))
            .map(async (imgRef) => {
                this.logger.debug(`Processing image reference: ${JSON.stringify(imgRef, null, 2)}`);

                if (imgRef.kind === "committed_attachment") {
                    this.logger.debug(
                        `Detected repository image - Owner: ${imgRef.owner}, Repo: ${imgRef.repo}, Branch: ${imgRef.branch}, Path: ${imgRef.path}`,
                    );

                    const result = await this.getRepositoryImageViaTools(imgRef, toolSet);

                    if (result?.buffer) {
                        this.logger.debug(
                            `Successfully fetched repository image buffer, size: ${result.buffer.length} bytes`,
                        );
                        visionMetrics.imagesResolvedFromGitHubMCPSize.push(result.buffer.length);
                    } else {
                        this.logger.debug(`Failed to fetch repository image buffer`);
                    }

                    return {
                        imgRef,
                        attachment: {
                            size: result?.buffer?.length || 0,
                            mimeType: result?.mimeType || "",
                            buffer: result?.buffer || null,
                        },
                    };
                } else {
                    this.logger.debug(`Non-repository image, using getAttachmentFromGuid for: ${imgRef.url}`);
                    return {
                        imgRef,
                        attachment: await getAttachmentFromGuid(
                            imgRef.guid,
                            demandCopilotToken(this.settings),
                            this.logger,
                        ),
                    };
                }
            });

        const imgRefsSmallerThanMaxSize = await this.getImgRefsSmallerThanMaxSize(
            attachmentPromises,
            visionMetrics,
            maxImageSize,
            processedImageUrls,
        );

        this.logger.debug(
            `Processed image references, found ${imgRefsSmallerThanMaxSize.length} attachments smaller than max size of ${formatBytes(maxImageSize)}`,
        );

        return { addedImages: imgRefsSmallerThanMaxSize };
    }
}

function ensureToolCallsHaveValidNames(toolCalls: ChatCompletionMessageToolCall[]): void {
    for (const toolCall of toolCalls) {
        // Replace all invalid characters with valid ones
        toolCall.function.name = toolCall.function.name.replace(/[^a-zA-Z0-9_-]+/g, "_");
    }
}
