/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

/**
 * Executes an array of task factories in parallel and yields their results as they complete.
 *
 * This function starts all tasks immediately and yields results in the order they finish,
 * not in the order they were provided. This is useful for processing multiple async operations
 * concurrently while handling results as soon as they become available.
 *
 * @example
 * ```typescript
 * const tasks = [
 *   () => fetch('/api/data1').then(r => r.json()),
 *   () => fetch('/api/data2').then(r => r.json()),
 *   () => fetch('/api/data3').then(r => r.json())
 * ];
 *
 * for await (const result of executeInParallelAndYieldAsCompleted(tasks)) {
 *   console.log('Received:', result);
 * }
 * ```
 */
export async function* executeInParallelAndYieldAsCompleted<T>(taskFactories: (() => Promise<T>)[]): AsyncGenerator<T> {
    if (taskFactories.length === 0) {
        return;
    }

    // Start all tasks and keep track of them
    const promises = taskFactories.map((factory, index) => factory().then((result) => ({ result, index })));

    const completed = new Set<number>();

    // Yield results as they complete
    while (completed.size < promises.length) {
        const pendingPromises = promises.filter((_, index) => !completed.has(index));

        if (pendingPromises.length === 0) break;

        const { result, index } = await Promise.race(pendingPromises);
        completed.add(index);
        yield result;
    }
}

const defaultTimedOutValue = { timedOut: true } as const;

export async function executeWithTimeout<PromiseT>(
    promise: Promise<PromiseT>,
    timeoutMs: number,
): Promise<PromiseT | { timedOut: true }>;
export async function executeWithTimeout<PromiseT, TimedOutT>(
    promise: Promise<PromiseT>,
    timeoutMs: number,
    timedOutValue: TimedOutT,
    onTimeout?: "resolve",
): Promise<PromiseT | TimedOutT>;
export async function executeWithTimeout<PromiseT, TimedOutT>(
    promise: Promise<PromiseT>,
    timeoutMs: number,
    timedOutValue: TimedOutT,
    onTimeout?: "reject",
): Promise<PromiseT>;
export async function executeWithTimeout<PromiseT, TimedOutT>(
    promise: Promise<PromiseT>,
    timeoutMs: number,
    timedOutValue?: TimedOutT,
    onTimeout: "resolve" | "reject" = "resolve",
): Promise<PromiseT | TimedOutT | { timedOut: true }> {
    // Ensure timeout is non-negative
    timeoutMs = Math.max(timeoutMs, 0);

    const actualTimedOutValue = timedOutValue ?? defaultTimedOutValue;

    let timeoutId: NodeJS.Timeout | undefined;

    const timeoutPromise = new Promise<TimedOutT | { timedOut: true }>((resolve, reject) => {
        timeoutId = setTimeout(
            () => (onTimeout === "resolve" ? resolve(actualTimedOutValue) : reject(actualTimedOutValue)),
            timeoutMs,
        );
    });

    try {
        const result = await Promise.race([promise, timeoutPromise]);
        if (timeoutId) {
            clearTimeout(timeoutId);
        }
        return result;
    } catch (error) {
        if (timeoutId) {
            clearTimeout(timeoutId);
        }
        throw error;
    }
}
