/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import OpenAI from "openai";
import type {
    ChatCompletionCreateParamsNonStreaming,
    ChatCompletionMessageParam,
} from "openai/resources/chat/completions";
import { RunnerLogger } from "../../runner";
import { RuntimeSettings } from "../../settings";
import type { ClientOptions, GetCompletionWithToolsOptions } from "../client";
import { ChatCompletionClient } from "./chat-completion-client";
import { ClientExtensionMethods, CopilotChatCompletionTool, OpenAIClientProvider } from "./types";

export class PlaybackChatCompletionClient extends ChatCompletionClient {
    private _initialChatCompletionResponses: OpenAI.Chat.Completions.ChatCompletion[];

    constructor(
        initialChatCompletionResponses: OpenAI.Chat.Completions.ChatCompletion[],
        provider: OpenAIClientProvider,
        settings: RuntimeSettings,
        logger: RunnerLogger,
        clientOptions?: ClientOptions,
    ) {
        super(provider, settings, logger, clientOptions);
        this._initialChatCompletionResponses = initialChatCompletionResponses;
    }

    protected override async makeRequest(
        client: OpenAI & ClientExtensionMethods,
        model: string,
        requestMessages: ChatCompletionMessageParam[],
        requestOptions: Omit<ChatCompletionCreateParamsNonStreaming, "model" | "messages">,
        toolDefinitions: CopilotChatCompletionTool[],
        maxRetries: number,
        options?: GetCompletionWithToolsOptions,
    ): Promise<{
        data: OpenAI.Chat.Completions.ChatCompletion;
        response: Response;
        request_id: string | null | undefined;
    }> {
        if (this._initialChatCompletionResponses.length > 0) {
            const response = this._initialChatCompletionResponses.shift();
            if (response) {
                return {
                    data: response,
                    response: new Response(),
                    request_id: undefined,
                };
            }
        }
        return await super.makeRequest(
            client,
            model,
            requestMessages,
            requestOptions,
            toolDefinitions,
            maxRetries,
            options,
        );
    }
}
