/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { createHmac } from "crypto";

// Define a constant for the Unix epoch time in UTC
const UnixEpochUtc = new Date(Date.UTC(1970, 0, 1, 0, 0, 0));

export class HMACEncoder {
    // Method to encode a key using HMACSHA256
    public static encode(key: string): string {
        const bytes = HMACEncoder.hmacGetBytes(key);
        const secondsSinceUnixEpoch = Math.floor((Date.now() - UnixEpochUtc.getTime()) / 1000);
        const timestampChars = secondsSinceUnixEpoch.toString();
        const timestampBytes = Buffer.from(timestampChars, "ascii");

        const hmacAlgorithm = createHmac("sha256", bytes);
        const hmacBytes = hmacAlgorithm.update(timestampBytes).digest();
        const hmacString = hmacBytes.toString("hex").toUpperCase();

        return `${timestampChars}.${hmacString}`;
    }

    // Helper method to convert a string key to bytes
    private static hmacGetBytes(hmacKey: string): Buffer {
        return Buffer.from(hmacKey, "ascii");
    }
}
