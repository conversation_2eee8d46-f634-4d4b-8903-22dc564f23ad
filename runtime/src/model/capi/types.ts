/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import OpenAI from "openai";
import { ChatCompletionMessage, ChatCompletionMessageParam, ChatCompletionTool } from "openai/resources";
import { RunnerLogger } from "../../runner";
import { RuntimeSettings } from "../../settings";
import { ClientOptions } from "../client";

// Anthropic Claude models support prompt caching at the client system message, longer message turns and tool definitions.
// To cache, the cache_control is added to the last message that is cached to cache the window above it.
// See https://docs.anthropic.com/en/docs/build-with-claude/prompt-caching#prompt-caching-examples

// enabling prompt cache control checkpoints for Claude models in Github Copilot
// as per https://github.com/github/copilot/blob/5cd59ec8979fc5f3ef30b9c0f8e03535218d52c9/docs/adrs/0050-prompt-caching-for-copilot-api.md?plain=1#L91C1-L104C91
export type CacheControlCheckpoint = {
    type: "ephemeral";
};

// Reasoning fields added to support oswe model at
// as per https://github.com/github/copilot-api/blob/6def446440d45ebca2c29970c373e93a0ed8b3ea/docs/api/schema.md
export type ReasoningMessageParam = {
    /**
     * An ID or encrypted value that allows the model to restore
     */
    reasoning_opaque?: string;
    /**
     * Human-readable text describing the model's thinking process.
     */
    reasoning_text?: string;
};

// some tools may not have an id, but we need to support the type
export type StoreItem = {
    id?: string;
};

// support on the tool definition schema and the message schema
// as per https://github.com/github/copilot-api/blob/9b826198bf07a766bb59554ecd74b0b21df3b42a/docs/api/schema.md#post__chat_completions
export type CopilotChatCompletionTool = ChatCompletionTool & {
    copilot_cache_control?: CacheControlCheckpoint;
};

// Reasoning fields added to support oswe model at
// as per https://github.com/github/copilot-api/blob/6def446440d45ebca2c29970c373e93a0ed8b3ea/docs/api/schema.md
export type CopilotChatCompletionMessageParam = ChatCompletionMessageParam &
    ReasoningMessageParam &
    StoreItem & {
        copilot_cache_control?: CacheControlCheckpoint;
    };

export type CopilotChatCompletionMessage = ChatCompletionMessage & ReasoningMessageParam;

// AI Client types

export type Model = {
    id: string;
    name: string;
    capabilities: {
        supports: {
            vision: boolean;
        };
        limits: {
            max_prompt_tokens?: number;
            max_context_window_tokens: number;
            vision?: {
                supported_media_types: string[];
                max_prompt_images: number;
                max_prompt_image_size: number;
            };
        };
    };
};

export type ImageReference = {
    url: string;
    guid: string;
    kind: "attachment" | "legacy_attachment" | "committed_attachment";
    owner?: string;
    repo?: string;
    branch?: string;
    path?: string;
};

export interface ClientExtensionMethods {
    setInitiatorHeader(initiator: string): void;
    listModels(): Promise<Model[]>;
}

export interface OpenAIClientProvider {
    createClient(
        settings: RuntimeSettings,
        logger: RunnerLogger,
        options?: ClientOptions,
    ): Promise<OpenAI & ClientExtensionMethods>;
}
