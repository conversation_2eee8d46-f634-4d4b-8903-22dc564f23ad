/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import OpenAI from "openai";
import { createCapiSecretProvider } from "../../helpers/secretProvider";
import { LogLevel, RunnerLogger } from "../../runner";
import { RuntimeSettings } from "../../settings";
import { demandCopilotIntegrationId, demandCopilotUrl, demandInstanceId } from "../../settings/demand";
import { ClientOptions } from "../client";
import { ChatCompletionClient } from "./chat-completion-client";
import { CopilotOpenAIClient } from "./copilot-client";
import { PlaybackChatCompletionClient } from "./playback-chat-completion-client";
import { ClientExtensionMethods, OpenAIClientProvider } from "./types";

export class AIClient extends ChatCompletionClient {
    constructor(settings: RuntimeSettings, logger: RunnerLogger, options?: ClientOptions) {
        const provider = new CopilotOpenAIClientProvider();
        super(provider, settings, logger, options);
    }
}

export class PlaybackAIClient extends PlaybackChatCompletionClient {
    constructor(
        initialChatCompletionResponses: OpenAI.Chat.Completions.ChatCompletion[],
        settings: RuntimeSettings,
        logger: RunnerLogger,
        options?: ClientOptions,
    ) {
        const provider = new CopilotOpenAIClientProvider();
        super(initialChatCompletionResponses, provider, settings, logger, options);
    }
}

export class CopilotOpenAIClientProvider implements OpenAIClientProvider {
    public async createClient(
        settings: RuntimeSettings,
        logger: RunnerLogger,
        clientOptions?: ClientOptions,
    ): Promise<OpenAI & ClientExtensionMethods> {
        // get the token or hmac key from settings or secret provider

        logger.startGroup("configured settings:", LogLevel.Debug);
        logger.debug(JSON.stringify(settings, null, 2));
        logger.endGroup(LogLevel.Debug);

        let copilotToken = settings.api?.copilot?.token;
        let hmacKey = settings.api?.copilot?.hmacKey;

        let hasOneOfKeyOrToken = !!(hmacKey || copilotToken);
        if (!hasOneOfKeyOrToken) {
            logger.debug("No Copilot HMAC key or GitHub OAuth token provided, trying secret provider");
            const secretProvider = createCapiSecretProvider(settings, logger);

            // it's possible to have authorization for only one of these, so handle both in
            // separate try/catch blocks
            try {
                hmacKey = await secretProvider.getSecret("capi-hmac-key");
            } catch (e) {
                logger.debug(`Failed to get Copilot HMAC key from secret provider:\n${e}`);
            }

            try {
                copilotToken = await secretProvider.getSecret("capi-token");
            } catch (e) {
                logger.debug(`Failed to get Copilot GitHub OAuth token from secret provider:\n${e}`);
            }

            hasOneOfKeyOrToken = !!(hmacKey || copilotToken);
        }

        let client: CopilotOpenAIClient | undefined = undefined;

        // get the copilot url and integration id
        const capiUrl = demandCopilotUrl(settings);
        const integrationId = demandCopilotIntegrationId(settings);
        const interactionId = settings.api?.copilot?.sessionId ?? demandInstanceId(settings);

        logger.debug(`Using Copilot API at ${capiUrl} with integration ID ${integrationId}`);

        if (copilotToken) {
            // connect with user-to-server authentication if available first
            logger.debug(`Using GitHub OAuth token for Copilot API`);
            client = CopilotOpenAIClient.createWithOAuthToken(
                logger,
                capiUrl,
                integrationId,
                copilotToken!,
                interactionId,
                clientOptions?.requestHeaders,
            );
        }
        if (hmacKey) {
            // connect to shared key if no user-to-server authentication is available
            logger.debug(`Using Copilot HMAC key for Copilot API`);
            const userId = process.env["GITHUB_USER_ID"];
            if (userId) {
                logger.debug(`Using user ID ${userId} for Copilot HMAC key`);
            } else {
                logger.debug(`No user ID provided for Copilot HMAC key`);
            }
            client = CopilotOpenAIClient.createWithHmac(
                logger,
                capiUrl,
                integrationId,
                hmacKey!,
                interactionId,
                userId,
                clientOptions?.requestHeaders,
            );
        }

        if (client === undefined) {
            throw new Error("No GitHub OAuth token or Copilot HMAC key provided");
        }

        return client;
    }
}
