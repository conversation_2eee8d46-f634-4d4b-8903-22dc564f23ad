/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import { randomUUID } from "crypto";
import { APIError } from "openai/error";
import { ChatCompletionChunk } from "openai/resources/chat/completions";
import { JobError } from "../../errors";
import { ghFetch } from "../../helpers/gh-fetch-helpers";
import { SecretFilter } from "../../helpers/SecretFilter";
import { createCapiSecretProvider } from "../../helpers/secretProvider";
import { RunnerLogger } from "../../runner";
import { GitError, GitErrorType } from "../../runner/git";
import { RuntimeSettings } from "../../settings";
import { demandCopilotIntegrationId, demandCopilotSessionId, demandCopilotUrl } from "../../settings/demand";

export enum SessionState {
    Completed = "completed",
    Failed = "failed",
}

export class sessionErrorMessages {
    static readonly TryAgainInstructions =
        "To retry, leave a comment on this pull request asking Copilot to try again.";
    static readonly InternalError = "Copilot has encountered an internal error.";
    static readonly RateLimitExceededBase =
        "Sorry, you've hit a rate limit that restricts the number of Copilot model requests you can make within a specific time period.";
    static readonly UpstreamFailure =
        "Copilot encountered an unexpected capacity issue with its upstream model provider.";
    static readonly RAIError =
        "Copilot encountered content that it is not allowed to process or return. If you think this is incorrect, please try again. Otherwise, adjust your content and retry.";
    static readonly ProblemPersists = "If the problem persists, please contact GitHub Support.";
    static readonly ProblemPersistsWithRequestId =
        "If the problem persists, please contact GitHub Support, including the request ID";
}

const unknownGitErrorMessage = "Copilot encountered an unknown Git error.";
const gitMergeErrorMessage =
    "Changes were pushed to Copilot's branch while it was working, and Copilot was unable to merge its changes with the contents of the remote branch. See logs for details.";
const gitErrorMessages = new Map<GitErrorType, string>([
    [GitErrorType.PullFirst, gitMergeErrorMessage],
    [GitErrorType.FetchFirst, gitMergeErrorMessage],
    [GitErrorType.RebaseFailed, gitMergeErrorMessage],
    [
        GitErrorType.LFSError,
        "Copilot encountered an error with a large file when trying to push changes. See logs for details. If your repository uses Git Large File Storage (LFS), you can configure the Copilot setup steps to enable LFS access - for details, see https://gh.io/copilot-coding-agent-lfs.",
    ],
    [
        GitErrorType.HookError,
        "Copilot encountered an error when trying to run a configured Git hook. See logs for details.",
    ],
    [
        GitErrorType.RuleError,
        "Copilot encountered a repository rule or branch protection rule violation when trying to push changes. See logs for details.",
    ],
    [
        GitErrorType.DisconnectError,
        "Copilot encountered an unexpected disconnect from Git while trying to push or pull changes.",
    ],
    [
        GitErrorType.AccessDenied,
        "Copilot encountered an access denied error from Git while trying to push or pull changes.",
    ],
    [GitErrorType.Unknown, unknownGitErrorMessage],
]);

// Returns an error object that can be presented to the user. We lean towards not returning an
// error if there isn't something useful or clear to say, instead leaning on a default fallback
// error message implemented in the frontend.
export function getSessionError(error: Error): { message: string; code: string } | null {
    const errorBase = getSessionErrorBase(error);

    if (errorBase) {
        return {
            message: `${errorBase.message} ${sessionErrorMessages.TryAgainInstructions}`,
            code: errorBase.code,
        };
    } else {
        return null;
    }
}

function getSessionErrorBase(error: Error): { message: string; code: string } | null {
    let code: JobError = new JobError("runtime", "unknown");
    let message: string | null = null;
    let requestId: string | null = null;

    if (error instanceof GitError) {
        code = new JobError("git", error.errorType);
        message = gitErrorMessages.get(error.errorType) ?? unknownGitErrorMessage;
    } else {
        let apiError: APIError | undefined;
        while (error) {
            if (error instanceof APIError) {
                apiError = error;
                break;
            }
            if ("cause" in error && error.cause instanceof Error) {
                error = error.cause;
            } else {
                break;
            }
        }

        // Over time, add more specific error messages here based on the error type
        if (apiError) {
            code = new JobError("capi", apiError.status?.toString() || "unknown");
            // Rate limiting by CAPI
            if (apiError.status === 429) {
                message = generateRateLimitExceededErrorMessage(apiError);
            }
            // Upstream failure - 503 unavailable: the request was rate limited by the upstream provider
            if (apiError.status === 503) {
                message = sessionErrorMessages.UpstreamFailure;
            }
            // RAI error - 422 Unprocessable Entity
            if (apiError.status === 422) {
                message = sessionErrorMessages.RAIError;
            }

            // If available, use the GitHub request ID rather than the X-Request-ID
            requestId = apiError.headers?.["x-github-request-id"] || apiError.request_id || null;
        }
    }

    // Add in support for passing the request ID through errors
    // TODO: Add requestId to thrown errors wherever possible
    if (hasRequestId(error)) {
        requestId = error.requestId;
    }

    // Always append request ID if available
    if (requestId) {
        message = message || sessionErrorMessages.InternalError;
        return {
            message: `${message} ${sessionErrorMessages.ProblemPersistsWithRequestId} \`${requestId}\`.`,
            code: code.toString(),
        };
    } else if (message) {
        return {
            message: `${message} ${sessionErrorMessages.ProblemPersists}`,
            code: code.toString(),
        };
    }

    return null;
}

/**
 * hasRequestId is a type guard that ensures an error has a string property named requestId.
 *
 * @param error The error to test.
 * @returns true if the error has a string requestId property, false otherwise.
 */
function hasRequestId(error: Error): error is Error & { requestId: string } {
    return "requestId" in error && typeof error.requestId === "string";
}

function generateRateLimitExceededErrorMessage(apiError: APIError): string {
    const retryAfterHeaderValue = apiError.headers?.["retry-after"];
    const retryAfter = retryAfterHeaderValue ? Number(retryAfterHeaderValue) : NaN;

    if (!isNaN(retryAfter)) {
        const retryAfterAsString = presentRetryAfterSecondsAsString(retryAfter);
        return sessionErrorMessages.RateLimitExceededBase + ` Please try again in ${retryAfterAsString}.`;
    } else {
        return sessionErrorMessages.RateLimitExceededBase;
    }
}

function presentRetryAfterSecondsAsString(retryAfter: number): string {
    if (retryAfter <= 60) {
        return "1 minute";
    } else if (retryAfter < 3600) {
        return `${Math.ceil(retryAfter / 60)} minutes`;
    } else {
        return `${Math.floor(retryAfter / 3600) + 1} hours`;
    }
}

export interface CopilotSessionsClient {
    sessionId(): string;
    error(error: Error): Promise<void>;
    log(logs: ChatCompletionChunk): Promise<void>;
    logNonCompletionContent(content: string): Promise<void>;
}

async function getUserToken(settings: RuntimeSettings, logger: RunnerLogger): Promise<string | undefined> {
    let token = settings.api?.copilot?.token;
    if (!token) {
        logger.debug("No GitHub OAuth token found, trying secret provider");
        const secretProvider = createCapiSecretProvider(settings, logger);
        token = await secretProvider.getSecret("capi-token");
    }

    return token;
}

export async function createCopilotSessionsClient(
    settings: RuntimeSettings,
    logger: RunnerLogger,
): Promise<CopilotSessionsClient> {
    const token = await getUserToken(settings, logger);
    if (!settings.api?.copilot?.useSessions || !token) {
        return new NoopCopilotSessionsClient();
    }

    const capiUrl = demandCopilotUrl(settings);
    const integrationId = demandCopilotIntegrationId(settings);
    const sessionId = demandCopilotSessionId(settings);

    return new CapiCopilotSessionsClient(token, logger, integrationId, capiUrl, sessionId);
}

export class CapiCopilotSessionsClient implements CopilotSessionsClient {
    private headers: Record<string, string>;
    private capiUrl: string;
    private logger: RunnerLogger;
    private sessionID: string;
    private disableSessionLogging: boolean;
    private hasLoggedSessionFull: boolean;
    private secretFilter: SecretFilter = SecretFilter.getInstance();

    constructor(token: string, logger: RunnerLogger, integrationId: string, capiUrl: string, sessionId: string) {
        this.capiUrl = capiUrl;
        this.logger = logger;

        this.headers = {
            "Copilot-Integration-Id": integrationId,
            Authorization: `Bearer ${token}`,
        };

        this.sessionID = sessionId;

        this.disableSessionLogging = false;
        this.hasLoggedSessionFull = false;
    }

    public async error(error: Error): Promise<void> {
        if (this.disableSessionLogging) {
            return;
        }

        const sessionError = getSessionError(error);

        if (!sessionError) {
            this.logger.debug("No error message to report, skipping session error update");
            return;
        }

        const response = await ghFetch(
            this.capiUrl + "/agents/sessions/" + this.sessionID.toString(),
            {
                method: "PUT",
                headers: {
                    ...this.headers,
                    "Content-Type": "application/json",
                    Accept: "application/json",
                },
                body: JSON.stringify({
                    error: {
                        message: sessionError.message,
                        code: sessionError.code,
                    },
                }),
            },
            this.logger,
            "sessions update",
        );
        if (!response.ok) {
            // Check hasLoggedSessionFull again, so instead of calling logSessionFull, we throw an error
            // back to the caller (which in this case should be logSessionFull).
            if (response.status === 413 && !this.hasLoggedSessionFull) {
                await this.logSessionFull();
                return;
            } else {
                const requestId = response.headers.get("x-github-request-id") || "unknown";
                throw new Error(`Error completing session (Request ID: ${requestId}): ${response.status}`);
            }
        }
        return;
    }

    public sessionId(): string {
        return this.sessionID;
    }

    public async log(logs: ChatCompletionChunk) {
        if (this.disableSessionLogging) {
            return;
        }

        const redactedLogs = structuredClone(logs);

        // Redact any secrets from the logs
        for (const choice of redactedLogs.choices) {
            // Redact from the delta content
            if (choice.delta && choice.delta.content) {
                choice.delta.content = this.redactLogString(choice.delta.content);
            }
            // Redact from any tool calls' arguments
            if (choice.delta.tool_calls) {
                for (const toolCall of choice.delta.tool_calls) {
                    if (toolCall.function && toolCall.function.arguments) {
                        toolCall.function.arguments = this.redactToolCallArguments(toolCall.function.arguments);
                    }
                }
            }
        }

        const response = await ghFetch(
            this.capiUrl + "/agents/sessions/" + this.sessionID.toString() + "/logs",
            {
                method: "PUT",
                headers: this.headers,
                body: "data: " + JSON.stringify(redactedLogs) + "\n\n",
            },
            this.logger,
            "sessions log",
        );

        if (!response.ok) {
            // Check hasLoggedSessionFull again, so instead of calling logSessionFull, we throw an error
            // back to the caller (which in this case should be logSessionFull).
            if (response.status === 413 && !this.hasLoggedSessionFull) {
                await this.logSessionFull();
                return;
            } else {
                const requestId = response.headers.get("x-github-request-id") || "unknown";
                throw new Error(`Error adding session logs (Request ID ${requestId} failed): ${response.status}`);
            }
        }
    }

    private redactLogString(body: string): string {
        return this.secretFilter.filterSecrets(body);
    }

    private redactToolCallArguments(argumentsString: string): string {
        return this.secretFilter.filterSecretsFromJsonString(argumentsString);
    }

    public async logNonCompletionContent(content: string) {
        if (this.disableSessionLogging) {
            return;
        }

        // Currently, the only way to get a heading or any content formatting is to mock a tool call
        // into the session logs API. So, we'll add a "commands" tool call as a temporary workaround
        // until we have a better way to represent non-completion content.
        const toolCalls = [
            {
                function: {
                    name: "command",
                    arguments: "{}",
                },
                index: 0,
            },
        ];
        return await this.log({
            id: randomUUID(),
            choices: [
                {
                    delta: {
                        content: content,
                        role: "assistant",
                        tool_calls: toolCalls,
                    },
                    finish_reason: null,
                    index: 0,
                },
            ],
            created: Date.now(),
            model: "",
            object: "chat.completion.chunk",
        } satisfies ChatCompletionChunk);
    }

    private async logSessionFull(): Promise<void> {
        if (!this.hasLoggedSessionFull) {
            try {
                this.logger.debug("Reporting session log full.");
                this.hasLoggedSessionFull = true;
                await this.logNonCompletionContent(
                    `<error>Session logs full. Logging will continue in the GitHub Actions job logs. To see them, click the "..." menu and then "View verbose logs".</error>`,
                );
            } catch (err) {
                this.logger.error(`Failed to log session full message: ${err}`);
            }
        }
        this.disableSessionLogging = true;
    }
}

export class NoopCopilotSessionsClient implements CopilotSessionsClient {
    constructor() {}

    public sessionId(): string {
        return "";
    }

    public async error(_error: Error): Promise<void> {
        return;
    }

    public async log(_logs: ChatCompletionChunk): Promise<void> {
        return;
    }

    public async logNonCompletionContent(_content: string): Promise<void> {
        return;
    }
}
