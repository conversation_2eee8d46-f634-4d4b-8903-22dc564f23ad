/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { DefaultAzureCredential, getBearerTokenProvider } from "@azure/identity";
import OpenAI, { AzureClientOptions, AzureOpenAI } from "openai";
import { createAzureSecretProvider } from "../../helpers/secretProvider";
import { Log<PERSON><PERSON><PERSON>, RunnerLogger } from "../../runner";
import { RuntimeSettings } from "../../settings";
import { demandAzureOpenAiUrl } from "../../settings/demand";
import { ClientExtensionMethods, Model, OpenAIClientProvider } from "../capi/types";
import { ClientOptions } from "../client";

export class AzureOpenAIClient extends AzureOpenAI implements ClientExtensionMethods {
    public constructor(options: AzureClientOptions) {
        super(options);
    }

    public setInitiatorHeader(_initiator: string): void {
        // no-op for Azure OpenAI, as it does not support the initiator header
    }

    public listModels(): Promise<Model[]> {
        return Promise.resolve([]);
    }
}

export class AzureOpenAIClientProvider implements OpenAIClientProvider {
    public async createClient(
        settings: RuntimeSettings,
        logger: RunnerLogger,
        clientOptions?: ClientOptions,
    ): Promise<OpenAI & ClientExtensionMethods> {
        logger.startGroup("configured settings:", LogLevel.Debug);
        logger.debug(JSON.stringify(settings, null, 2));
        logger.endGroup(LogLevel.Debug);

        let azureOpenAIKey = settings.api?.openai?.apiKey;
        const azureKeyVaultUri = settings.api?.openai?.azureKeyVaultUri;
        const azureSecretName = settings.api?.openai?.azureSecretName;
        if (!azureOpenAIKey && azureKeyVaultUri) {
            logger.debug("No API_KEY provided, trying secret provider");
            const secretProvider = createAzureSecretProvider(azureKeyVaultUri, logger);

            // it's possible to have authorization for only one of these, so handle both in
            // separate try/catch blocks
            try {
                azureOpenAIKey = await secretProvider.getSecret(azureSecretName || "azure-openai-api-key");
            } catch (e) {
                logger.debug(`Failed to get API Key from secret provider:\n${e}`);
            }
        }

        const azureOpenAIUrl = demandAzureOpenAiUrl(settings);
        logger.debug(`Using Azure Open API at ${azureOpenAIUrl}`);

        // use the azure openai URL for the deployment
        const deploymentUrl = `${new URL(azureOpenAIUrl).origin}/openai`;

        let options: AzureClientOptions = {
            baseURL: deploymentUrl,
            apiVersion: settings.api?.openai?.azure?.apiVersion || "2024-10-21",
            deployment: clientOptions?.model,
            defaultHeaders: clientOptions?.requestHeaders,
        };

        if (!azureOpenAIKey) {
            // try connecting with Azure Managed Identity
            const credential = new DefaultAzureCredential();
            const scope = "https://cognitiveservices.azure.com/.default";
            const azureADTokenProvider = getBearerTokenProvider(credential, scope);

            options = {
                ...options,
                azureADTokenProvider,
            };

            logger.debug("Using Azure Managed Identity");
        } else {
            // use the API key
            options = {
                ...options,
                apiKey: azureOpenAIKey,
            };
            logger.debug("Using Azure OpenAI API Key");
        }

        return new AzureOpenAIClient(options);
    }
}
