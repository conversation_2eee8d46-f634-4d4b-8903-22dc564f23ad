/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import OpenAI, { ClientOptions as OpenAIClientOptions } from "openai";
import { LogLevel, RunnerLogger } from "../../runner";
import { RuntimeSettings } from "../../settings";
import { demandOpenAIApiKey } from "../../settings/demand";
import { ClientExtensionMethods, Model, OpenAIClientProvider } from "../capi/types";
import { ClientOptions } from "../client";

export class OpenAIClient extends OpenAI implements ClientExtensionMethods {
    public constructor(options: OpenAIClientOptions) {
        super(options);
    }

    public setInitiatorHeader(_initiator: string): void {
        // no-op for OpenAI, as it does not support the initiator header
    }

    public listModels(): Promise<Model[]> {
        return Promise.resolve([]);
    }
}

export class OpenAIKeyBasedAIClientProvider implements OpenAIClientProvider {
    public async createClient(
        settings: RuntimeSettings,
        logger: RunnerLogger,
        clientOptions?: ClientOptions,
    ): Promise<OpenAI & ClientExtensionMethods> {
        logger.startGroup("configured settings:", LogLevel.Debug);
        logger.debug(JSON.stringify(settings, null, 2));
        logger.endGroup(LogLevel.Debug);

        const apiKey = demandOpenAIApiKey(settings);
        const baseURL = settings.api?.openai?.baseUrl;
        const defaultHeaders = clientOptions?.requestHeaders;

        return new OpenAIClient({ baseURL, apiKey, defaultHeaders });
    }
}
