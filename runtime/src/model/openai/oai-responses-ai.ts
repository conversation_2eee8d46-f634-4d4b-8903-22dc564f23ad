/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { RunnerLogger } from "../../runner";
import { RuntimeSettings } from "../../settings";
import { OpenAIClientProvider } from "../capi/types";
import type { ClientOptions } from "../client";
import { ResponsesClient } from "./responses-ai";

export class AIClient extends ResponsesClient {
    constructor(
        provider: OpenAIClientProvider,
        settings: RuntimeSettings,
        logger: RunnerLogger,
        options?: ClientOptions,
    ) {
        super(provider, settings, logger, options);
    }
}
