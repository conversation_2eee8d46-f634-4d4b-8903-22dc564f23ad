/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import {
    ChatCompletionCreateParamsNonStreaming,
    ChatCompletionMessage,
    ChatCompletionMessageParam,
} from "openai/resources";
import { cleanUpMessage } from "../../helpers/openAiHelpers";
import { RunnerLogger } from "../../runner";
import { RuntimeSettings } from "../../settings";
import { Tool } from "../../tools";
import { Chat<PERSON>ompletionClient } from "../capi/chat-completion-client";
import { CopilotChatCompletionMessage, OpenAIClientProvider } from "../capi/types";
import type { ClientOptions, GetCompletionWithToolsOptions } from "../client";
import type { Event } from "../event";

// Azure openai has different property names for reasoning parameters that are mapped to CAPI parameters in GitHub Copilot API.
type AzureOpenAIReasoningParams = {
    cot_id?: string;
    cot_summary?: string;
};

type AzureOpenAIChatCompletionMessage = ChatCompletionMessage & AzureOpenAIReasoningParams;

export class AIClient extends ChatCompletionClient {
    constructor(
        provider: OpenAIClientProvider,
        settings: RuntimeSettings,
        logger: RunnerLogger,
        options?: ClientOptions,
    ) {
        super(provider, settings, logger, options);
    }

    protected override getCompletionOptions(
        tools?: Tool[],
        options?: GetCompletionWithToolsOptions,
    ): Omit<ChatCompletionCreateParamsNonStreaming, "model" | "messages"> {
        return {
            ...(this.model.startsWith("gpt-4")
                ? {
                      temperature: 1,
                    //   top_p: 0.95,
                    //   frequency_penalty: 0,
                    //   presence_penalty: 0,
                    //   parallel_tool_calls: tools ? false : undefined,
                  }
                : {
                    temperature: 1
                }),
            tool_choice: options?.toolChoice,
        };
    }

    public async *getCompletionWithTools(
        systemMessage: string,
        initialMessages: ChatCompletionMessageParam[],
        tools: Tool[],
        options?: GetCompletionWithToolsOptions,
    ): AsyncGenerator<Event> {
        const events = await super.getCompletionWithTools(systemMessage, initialMessages, tools, options);
        for await (const completionEvent of events) {
            if (completionEvent.kind === "message") {
                yield {
                    ...completionEvent,
                    message: cleanUpMessage({
                        ...completionEvent.message,
                        reasoning_text: (completionEvent.message as AzureOpenAIChatCompletionMessage)?.cot_summary,
                    } as CopilotChatCompletionMessage),
                };
            } else {
                yield completionEvent;
            }
        }
    }
}
