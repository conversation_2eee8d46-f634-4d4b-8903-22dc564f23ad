/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import OpenAI, { APIError } from "openai";
import { ChatCompletionMessageParam, Responses } from "openai/resources";
import { deepJsonParse } from "../../helpers/json-helpers";
import { LogLevel, RunnerLogger } from "../../runner";
import { RuntimeSettings } from "../../settings";
import { createToolSet, Tool, Too<PERSON><PERSON><PERSON><PERSON>, Tool<PERSON><PERSON>backOptions, ToolResultExpanded } from "../../tools";
import { ClientExtensionMethods, CopilotChatCompletionMessageParam, OpenAIClientProvider } from "../capi/types";
import type { Client, ClientOptions, ClientOptionsRequired, GetCompletionWithToolsOptions } from "../client";
import { Event } from "../event";

export class ResponsesClient implements Client {
    protected readonly clientOptions: ClientOptionsRequired;
    private clientPromise: Promise<OpenAI & ClientExtensionMethods>;
    constructor(
        provider: OpenAIClientProvider,
        protected readonly settings: Partial<RuntimeSettings>,
        protected readonly logger: RunnerLogger,
        clientOptions?: ClientOptions,
    ) {
        this.clientOptions = ResponsesClient.initDefaultOptions(clientOptions);

        this.logger.info(`Using responses with model: ${this.model}`);

        this.clientPromise = provider.createClient(settings, logger, this.clientOptions);
    }

    public get model(): string {
        return this.clientOptions.model;
    }

    private static initDefaultOptions(options?: ClientOptions): ClientOptionsRequired {
        return {
            model: options?.model || "o4-mini",
            toolTokenBudgetProportion: options?.toolTokenBudgetProportion ?? 0.25,
            retryPolicy: {
                maxRetries: options?.retryPolicy?.maxRetries ?? 5,
                errorCodesToRetry: options?.retryPolicy?.errorCodesToRetry ?? [],
                rateLimitRetryPolicy: {
                    defaultRetryAfterSeconds: options?.retryPolicy?.rateLimitRetryPolicy?.defaultRetryAfterSeconds ?? 5,
                    initialRetryBackoffExtraSeconds:
                        options?.retryPolicy?.rateLimitRetryPolicy?.initialRetryBackoffExtraSeconds ?? 1,
                    retryBackoffExtraGrowth: options?.retryPolicy?.rateLimitRetryPolicy?.retryBackoffExtraGrowth ?? 2,
                    maxRetryAfterSeconds: options?.retryPolicy?.rateLimitRetryPolicy?.maxRetryAfterSeconds ?? 60,
                },
            },
            thinkingMode: options?.thinkingMode ?? true,
            requestHeaders: options?.requestHeaders ?? {},
        };
    }

    public async *getCompletionWithTools(
        systemMessage: string,
        initialMessages: ChatCompletionMessageParam[],
        tools: Tool[],
        _options?: GetCompletionWithToolsOptions,
    ): AsyncGenerator<Event> {
        const client = await this.clientPromise;
        const toolSet = createToolSet(tools);

        const toolDefinitions = tools.map(
            (tool) =>
                ({
                    name: tool.name,
                    description: tool.description,
                    parameters: tool.input_schema,
                    strict: false,
                    type: "function",
                }) satisfies Responses.FunctionTool,
        );

        let retry = 0;
        const maxRetries = this.clientOptions.retryPolicy.maxRetries;

        let retryAfterBackoffExtra =
            this.clientOptions.retryPolicy.rateLimitRetryPolicy.initialRetryBackoffExtraSeconds;
        let totalRetryWaitTime = 0;
        const maxRetryAfterSeconds = this.clientOptions.retryPolicy.rateLimitRetryPolicy.maxRetryAfterSeconds;

        let input: Responses.ResponseInput = initialMessages.flatMap(toResponseInputItem);
        let response: Responses.Response | undefined;
        let finalResponseEmitted = false;
        while (!finalResponseEmitted) {
            let lastAPIError: APIError | undefined;

            do {
                try {
                    const res = await client.responses
                        .create(
                            {
                                model: this.model,
                                previous_response_id: response?.id,
                                instructions: systemMessage,
                                input,
                                parallel_tool_calls: toolDefinitions.length > 0 ? true : undefined,
                                tools: toolDefinitions,
                                reasoning: {
                                    // required for getting summary text - by default it's not enabled.
                                    summary: "auto",
                                    effort: "medium",
                                },
                            },
                            { maxRetries },
                        )
                        .withResponse();

                    response = res.data;

                    this.logger.debug(`response (Request-ID ${res.request_id}):`);
                    this.logger.debug("data:");
                    this.logger.debug(JSON.stringify(res.data, null, 2));
                } catch (error) {
                    const apiError = error as APIError;
                    const innerApiError = apiError.error as
                        | {
                              message?: string;
                              [key: string]: unknown;
                          }
                        | undefined;

                    if (apiError) {
                        lastAPIError = apiError;
                        this.logger.error(`error (Request-ID ${apiError.request_id})`);
                        this.logger.error(JSON.stringify(apiError, null, 2));

                        this.logger.debug(`Failed to get response from the AI model: ${apiError}`);
                        if (innerApiError) {
                            this.logger.debug(`Inner error: ${JSON.stringify(innerApiError, null, 2)}`);
                        }

                        const statusCode = apiError.status;

                        const retryDueToErrorCodeRetryPolicy = !statusCode
                            ? false
                            : this.clientOptions.retryPolicy.errorCodesToRetry.some((errorCode) => {
                                  if (Array.isArray(errorCode)) {
                                      return (
                                          statusCode >= errorCode[0] &&
                                          (errorCode[1] === undefined || statusCode <= errorCode[1])
                                      );
                                  } else {
                                      return statusCode === errorCode;
                                  }
                              });

                        let retryAfter: number | undefined;
                        if (apiError.status === 429 || retryDueToErrorCodeRetryPolicy) {
                            retryAfter =
                                parseInt(
                                    apiError.headers?.["retry-after"] ||
                                        `${this.clientOptions.retryPolicy.rateLimitRetryPolicy.defaultRetryAfterSeconds}`,
                                    10,
                                ) + retryAfterBackoffExtra;
                            retryAfterBackoffExtra *=
                                this.clientOptions.retryPolicy.rateLimitRetryPolicy.retryBackoffExtraGrowth;
                        }

                        if (retryAfter !== undefined && retryAfter <= maxRetryAfterSeconds) {
                            this.logger.debug(`Retrying after ${retryAfter} seconds...`);
                            await new Promise((resolve) => setTimeout(resolve, retryAfter * 1000));
                            totalRetryWaitTime += retryAfter;
                            this.logger.debug(`Retrying after ${retryAfter} seconds... Will try again now!`);
                            continue;
                        } else {
                            if (!retryAfter) {
                                this.logger.error(`Retry after is not set. Giving up.`);
                            } else {
                                this.logger.error(`Retry after ${retryAfter} seconds is too long. Giving up.`);
                            }
                        }
                    }
                    // the rest we should rethrow like 400 etc.

                    this.logger.error(`error`);
                    this.logger.error(JSON.stringify(error, null, 2));

                    throw error;
                } finally {
                    this.logger.endGroup(LogLevel.Debug);
                }
            } while (!response && retry++ < maxRetries);

            if (!response) {
                // Log some recent messages to help with debugging
                this.logger.debug(`Recent messages prior to failure:`);
                for (let i = Math.max(0, input.length - 6); i < input.length; i++) {
                    // CAPI error messages often reference messages at a specific index, so we print the index along with the message
                    this.logger.debug(`${i}: ${JSON.stringify(input[i], null, 2)}`);
                }

                // Include the structured error if that's the cause of failures. Structured error help surface details to users via agent sessions
                // e.g. 429 errors being retried but eventually failed
                throw new Error(
                    `Failed to get response from the AI model; retried ${retry - 1} times (total retry wait time: ${totalRetryWaitTime} seconds)`,
                    { cause: lastAPIError },
                );
            }

            if (this.requiresToolOutput(response)) {
                for (const item of response.output) {
                    yield {
                        kind: "message",
                        message: toChatCompletionMessageParam(item),
                    };
                }

                const toolResults = await this.runTool(response, toolSet);
                input = toolResults.map(
                    (r) =>
                        ({
                            type: "function_call_output",
                            call_id: r.toolCallId,
                            output: r.textResultForLlm,
                        }) as Responses.ResponseFunctionToolCallOutputItem,
                );

                for (const item of input) {
                    yield {
                        kind: "message",
                        message: toChatCompletionMessageParam(item),
                    };
                }
            } else {
                for (const item of response.output) {
                    yield {
                        kind: "message",
                        message: toChatCompletionMessageParam(item),
                    };
                }

                yield {
                    kind: "response",
                    response: {
                        content: response.output_text,
                        refusal: null,
                        role: "assistant",
                    },
                };

                finalResponseEmitted = true;
            }
        }
    }

    private async runTool(
        response: Responses.Response,
        toolSet: Record<string, Tool>,
        standardToolCallbackOptions?: ToolCallbackOptions,
    ): Promise<
        Array<{
            toolCallId: string;
            textResultForLlm: string;
            sessionLog?: string;
        }>
    > {
        const responseItems = response.output.filter((o) => o.type === "function_call");
        if (!responseItems || responseItems.length === 0) {
            throw new Error("Tool calls in response are missing");
        }

        const results: Array<{
            toolCallId: string;
            textResultForLlm: string;
            sessionLog?: string;
        }> = [];

        for (const toolCall of responseItems) {
            const toolName = toolCall.name;
            if (!toolName) {
                throw new Error("Tool name is missing");
            }

            const argsStr = toolCall.arguments;
            if (!argsStr) {
                throw new Error("Tool call arguments are missing");
            }

            const toolCallId = toolCall.call_id;
            if (!toolCallId) {
                throw new Error("Tool call id is missing");
            }

            let toolResultExpanded: ToolResultExpanded;
            const tool = toolSet[toolName];
            try {
                const funcArgs = deepJsonParse(argsStr);
                if (!tool || !tool.callback) {
                    toolResultExpanded = {
                        textResultForLlm: `Tool '${toolName}' does not exist. Available tools that can be called are ${Object.keys(toolSet).join(", ")}.`,
                        resultType: "failure",
                        error: `Tool '${toolName}' does not exist.`,
                        sessionLog: `<error>Tool '${toolName}' does not exist.</error>`,
                        toolTelemetry: {},
                    };
                } else {
                    const fn = tool.callback as ToolCallback;
                    const tooSpecificToolCallbackOptions: ToolCallbackOptions = {
                        ...standardToolCallbackOptions,
                        toolOptions: this.settings.service?.tools?.[toolName],
                    };
                    const toolResult = await fn({ ...funcArgs }, tooSpecificToolCallbackOptions);
                    toolResultExpanded =
                        typeof toolResult === "string"
                            ? {
                                  textResultForLlm: toolResult,
                                  resultType: "success",
                                  toolTelemetry: {},
                              }
                            : toolResult;
                }
            } catch (error) {
                toolResultExpanded = {
                    textResultForLlm: `Failed to execute \`${toolName}\` tool with arguments: ${argsStr} due to error: ${error}`,
                    resultType: "failure",
                    error: `${error}`,
                    sessionLog: `<error>Failed to execute \`${toolName}\` tool with arguments: ${argsStr}</error>`,
                    toolTelemetry: {
                        properties: {
                            wasSyntaxError: error instanceof SyntaxError ? "true" : "false",
                        },
                    },
                };
            }
            results.push({
                toolCallId: toolCallId,
                textResultForLlm: toolResultExpanded.textResultForLlm,
                sessionLog:
                    toolResultExpanded.resultType == "success"
                        ? toolResultExpanded.sessionLog
                        : toolResultExpanded.error
                          ? `<error>${toolResultExpanded.error}</error>`
                          : undefined,
            });
        }
        return results;
    }

    private requiresToolOutput(response: Responses.Response): boolean {
        return response.output.filter((o) => o.type === "function_call").length > 0;
    }
}

function toResponseInputItem(message: CopilotChatCompletionMessageParam): Responses.ResponseInputItem[] {
    if (message.role === "function") {
        throw new Error("Function role is not supported");
    }

    const reasoning: Responses.ResponseReasoningItem[] = [];
    if (message.reasoning_opaque) {
        reasoning.push({
            id: message.reasoning_opaque,
            type: "reasoning",
            summary: message.reasoning_text ? [{ text: message.reasoning_text, type: "summary_text" }] : [],
        } satisfies Responses.ResponseReasoningItem);
    }

    if (message.role === "assistant" && message.tool_calls && message.tool_calls.length > 0) {
        // there should only be one tool call
        const toolCall = message.tool_calls[0];
        return [
            ...reasoning,
            {
                type: "function_call",
                id: message.id,
                name: toolCall.function.name,
                arguments: toolCall.function.arguments,
                call_id: toolCall.id,
            } satisfies Responses.ResponseFunctionToolCall,
        ];
    }

    if (message.role === "tool") {
        return [
            ...reasoning,
            {
                type: "function_call_output",
                call_id: message.tool_call_id,
                output:
                    typeof message.content === "string"
                        ? message.content
                        : (message.content
                              ?.map((c) => {
                                  return c.type === "text" ? c : { type: "text", text: "" }; // only text is supported for now
                              })
                              .join(" ") ?? ""),
            } as Responses.ResponseFunctionToolCallOutputItem,
        ];
    }

    if (!message.content) {
        return reasoning;
    }

    if (message.role === "assistant") {
        return [
            ...reasoning,
            {
                type: "message",
                id: message.id,
                role: message.role,
                content: [
                    {
                        annotations: [],
                        type: "output_text",
                        text:
                            typeof message.content === "string"
                                ? message.content
                                : (message.content?.map((c) => (c.type === "text" ? c.text : "")).join(" ") ?? ""),
                    } as Responses.ResponseOutputText,
                ],
            } as Responses.ResponseOutputMessage,
        ];
    }

    return [
        ...reasoning,
        {
            role: message.role,
            content:
                typeof message.content === "string"
                    ? message.content
                    : (message.content?.map((c) => (c.type === "text" ? c.text : "")).join(" ") ?? ""),
            type: "message",
        } satisfies Responses.EasyInputMessage,
    ];
}

function toChatCompletionMessageParam(
    item: Responses.ResponseOutputItem | Responses.ResponseInputItem,
): CopilotChatCompletionMessageParam {
    if (item.type === "reasoning") {
        return {
            role: "assistant",
            reasoning_opaque: item.id,
            reasoning_text: item.summary ? item.summary.map((s) => s.text).join(" ") : undefined,
            content: "",
        };
    }

    if (item.type === "function_call") {
        return {
            role: "assistant",
            id: item.id,
            tool_calls: [
                {
                    id: item.call_id,
                    type: "function",
                    function: {
                        name: item.name,
                        arguments: item.arguments,
                    },
                },
            ],
            content: "",
        };
    }

    if (item.type === "function_call_output") {
        return {
            role: "tool",
            content: item.output,
            tool_call_id: item.call_id,
        };
    }

    if (item.type === "message") {
        return {
            id: "id" in item ? item.id : undefined,
            role: item.role,
            content:
                typeof item.content === "string"
                    ? item.content
                    : (item.content
                          ?.map((c) => (c.type === "input_text" || c.type === "output_text" ? c.text : ""))
                          .join(" ") ?? ""),
        };
    }

    throw new Error(`Unknown item type: ${item.type}`);
}
