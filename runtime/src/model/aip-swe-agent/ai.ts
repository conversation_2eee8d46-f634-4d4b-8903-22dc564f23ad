/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { Chat<PERSON>ompletionMessageParam, ChatCompletionTool } from "openai/resources";
import { v4 as uuidv4 } from "uuid";
import { RunnerLogger } from "../../runner";
import { RunnerExec } from "../../runner/exec";
import { DefaultExec } from "../../runner/exec/default";
import { RuntimeSettings } from "../../settings";
import {
    alwaysProceedToolConfirmation<PERSON><PERSON>back,
    createTool<PERSON>et,
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>back,
    ToolConfig,
    ToolResultExpanded,
} from "../../tools";
import { applyPatch, strLenTruncate } from "../../tools/util";
import type { Client, ClientOptions } from "../client";
import type { Event } from "../event";
import { OAIMessage, ReverseProxyClient } from "./reverseproxy";
import { getToolDescription } from "./util";

// This is a simple workaround to not having tool call support in the model.
// This will echo the information back over the stream that we can use for local processing for the tool only
// but we cannot relay the information back to the model. Only to be used for informational relay.
const remoteToolMessage = (tools: Tool[], location: string): string => `
Run "echo" command for the following <functions> if a **function_name** is called using 'container.exec' tool with input
{"cmd":["echo", "<functions><function_name>JSON object STRICTLY following Input Schema of the function_name</function_name></functions>", "workdir": "${location}"]}

<functions>
${tools
    .filter((t) => !!t.input_schema)
    .map((t) => getToolDescription(t, true))
    .join("\n ---- \n")}
</functions>

For example: For **add** with Input Schema { "a": { "type": "number" }, "b": { "type": "number" } }
Run echo: {"cmd":["echo", "<functions><add>{"a": 1, "b": 2}</add></functions>", "workdir": "${location}"]}
`;

const localToolMessage = (tools: Tool[]): string => {
    // provide the tool descriptions for the system prompt - this makes the
    // function calls more explicit to the LLM and hence more reliable
    const toolDescriptions = tools.map((tool) => {
        return `**${tool.name}**: ${tool.description}`;
    });

    const toolDescriptionsString =
        tools.length > 0 ? `You can use below tools:\n- ${toolDescriptions.join("\n- ")}` : "";

    return `Do not use oai commands because I don't have them installed.\n${toolDescriptionsString}`;
};

export class AIClient implements Client {
    private proxy: ReverseProxyClient;
    public initialized: boolean;
    public sessionId?: string;
    public location?: string;
    private readonly logger: RunnerLogger;
    private additionalTools: Tool[] = [];

    constructor(settings: RuntimeSettings, logger: RunnerLogger, options?: ClientOptions) {
        const { model } = options ?? {};

        // split model:<execMode> from model name
        const [modelName] = (model ?? "").split(":");

        // default to always use runLocal for local mode
        this.proxy = new ReverseProxyClient(settings, logger, modelName, true, options?.retryPolicy);
        this.initialized = false;
        this.logger = logger;

        logger.info(" ");
        logger.debug(`Using model: ${this.model} with exec mode: ${this.runLocal ? "local" : "remote"}`);
    }

    public get model(): string {
        return this.proxy.model;
    }

    public get runLocal(): boolean {
        return this.proxy.runLocal ?? true;
    }

    public async initialize(location: string): Promise<{ sessionId: string }> {
        const session = await this.proxy.newSession();
        this.sessionId = session.sessionId;

        const projectDir = await this.proxy.initializeProject(location);
        this.initialized = true;

        this.location = projectDir;
        if (!this.runLocal) {
            this.additionalTools = [
                git_apply_patch(
                    {
                        location,
                        toolConfirmationCallback: alwaysProceedToolConfirmationCallback,
                    },
                    new DefaultExec(this.logger),
                    this.logger,
                ),
            ];
        }

        return session;
    }

    // Note that this "model" doesn't support any custom tools, it has it's own baked in tools that it will use.  This model kind should be used with care, as many usage patterns
    // will not be supported.
    public async *getCompletionWithTools(
        systemMessage: string,
        initialMessages: ChatCompletionMessageParam[],
        tools: Tool[],
    ): AsyncGenerator<Event> {
        if (!this.runLocal && !this.initialized) throw new Error("Must call initialize() before use.");

        systemMessage = this.runLocal
            ? `${systemMessage}${localToolMessage(tools)}`
            : `${systemMessage}${remoteToolMessage(tools, this.location ?? "")}`;

        const toolSet = createToolSet([...tools, ...this.additionalTools]);
        const toolDefinitions = tools.map(
            (t) =>
                ({
                    type: "function",
                    function: {
                        name: t.name,
                        description: t.description,
                        parameters: t.input_schema,
                    },
                }) satisfies ChatCompletionTool,
        );

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const history: any = [
            ...initialMessages
                .map((m) => ({
                    role: m.role === "tool" ? "user" : m.role,
                    content:
                        typeof m.content === "string"
                            ? m.content
                            : m.content
                                  // eslint-disable-next-line @typescript-eslint/no-explicit-any
                                  ?.map((c) => (c as any).text ?? "")
                                  .join("\n-----\n"),
                }))
                .filter((m) => m.content && m.content.trim().length > 0),
        ];

        let lastMessage: OAIMessage | undefined;
        do {
            const stream = this.proxy.run(history, systemMessage, toolDefinitions);
            for await (const message of stream) {
                lastMessage = message;
                if (lastMessage) {
                    history.push(lastMessage);
                }

                this.logger.debug(JSON.stringify(message, null, 2));
                yield {
                    kind: "message",
                    message: this.convertOAItoRunMessage(message),
                };

                // replay supported tools back locally
                const toolCalls = this.getToolCalls(message);
                for (const toolCall of toolCalls) {
                    const toolCallback = toolSet[toolCall.toolName]?.callback as ToolCallback;
                    let toolOutput = "";

                    if (!toolCallback && this.runLocal) {
                        toolOutput = `Tool ${toolCall.toolName} not found`;
                    }

                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    let input: any;
                    if (!toolOutput) {
                        try {
                            input = JSON.parse(toolCall.input);
                        } catch {
                            toolOutput = `Tool ${toolCall.toolName} input is not valid JSON: ${toolCall.input}`;
                        }
                    }

                    if (!toolOutput && toolCallback) {
                        try {
                            const toolResult = await toolCallback(input);
                            toolOutput = typeof toolResult === "string" ? toolResult : toolResult.textResultForLlm;
                        } catch (err) {
                            toolOutput = `Tool ${toolCall.toolName} failed: ${err}`;
                        }
                    }

                    // for local, we always send the tool output back to the model
                    // for remote, we only relay any output to the logs that we executed and returned, since
                    // the remote exec output is already being relayed above.
                    if (this.runLocal || toolOutput) {
                        lastMessage = {
                            role: "tool",
                            author_name: message.audience,
                            content: toolOutput ?? "",
                            audience: "all",
                            end_turn: false,
                            id: uuidv4(),
                            channel: message.channel,
                        } as OAIMessage;

                        history.push(lastMessage);
                        yield {
                            kind: "message",
                            message: this.convertOAItoRunMessage(lastMessage, message.id),
                        };
                        this.logger.debug(JSON.stringify(lastMessage, null, 2));
                    }
                }
            }
        } while (this.runLocal && lastMessage?.end_turn !== true);

        yield {
            kind: "response",
            response: {
                role: "assistant",
                content: lastMessage?.content ?? "",
                refusal: null,
            },
        };
    }

    private getToolCalls(message: OAIMessage): { toolName: string; input: string; id: string }[] {
        if (this.isToolCall(message.audience)) {
            const toolName = message.audience.replace("functions.", "");
            return [{ toolName, input: message.content, id: message.id }];
        }

        if (message.channel === "git_apply_patch") {
            return [
                {
                    toolName: "git_apply_patch",
                    input: JSON.stringify({ diff: message.content }),
                    id: message.id,
                },
            ];
        }

        if (message.role === "tool" && message.content && typeof message.content === "string") {
            // parse out the function_name and the JSON object if it exists:
            // <functions><function_name>{{JSON object STRICTLY following Input Schema of the function_name}}</functions>
            const match = /^<functions><([^>]+)>([^>]+)</.exec(message.content);

            if (match) {
                return [{ toolName: match[1], input: match[2], id: "" }];
            }
        }

        return [];
    }

    private isToolCall(audience: string): boolean {
        return audience === "container.exec" || audience?.startsWith("functions.");
    }

    /**
     * Converts an OAIMessage into a ChatCompletionMessageParam.
     *
     * @param message - The OAIMessage to convert.
     * @param tool_call_id - Optional. If provided, this ID will override the default message ID
     *                       in cases where the role is 'tool'. This is useful when a specific
     *                       tool call ID needs to be associated with the message for tracking
     *                       or processing purposes.
     * @returns A ChatCompletionMessageParam object representing the converted message.
     */
    private convertOAItoRunMessage(message: OAIMessage, tool_call_id?: string): ChatCompletionMessageParam {
        if (this.isToolCall(message.audience)) {
            return {
                role: "assistant",
                content: "",
                tool_calls: [
                    {
                        id: message.id,
                        type: "function",
                        function: {
                            name: message.audience.replace("functions.", ""),
                            arguments: message.content,
                        },
                    },
                ],
            };
        }

        if (message.channel === "git_apply_patch") {
            return {
                role: "assistant",
                content: message.content,
                tool_calls: [
                    {
                        id: message.id,
                        type: "function",
                        function: {
                            name: "git_apply_patch",
                            arguments: JSON.stringify({
                                diff: message.content,
                            }),
                        },
                    },
                ],
            };
        }

        if (message.channel === "system_error") {
            throw new Error(message.content, { cause: "system_error" }); // the model stops the conversation on system errors
        }

        switch (message.role) {
            case "tool":
                return {
                    role: "tool",
                    content: message.content,
                    tool_call_id: tool_call_id ?? message.id,
                };
            default:
                return { role: "assistant", content: message.content }; // default to assistant to not stop the conversation
        }
    }
}

// this is only needed for remote tool calls. Since we are not using the remote execution, this code path is not used.
// we are leaving it here for future reference.
const git_apply_patch = (config: ToolConfig, exec: RunnerExec, logger: RunnerLogger): Tool => ({
    name: "git_apply_patch",
    description: "Apply a patch to the current repository",
    input_schema: null, // No input schema - we are not going to use it as a function call
    callback: async (input: { diff: string }): Promise<ToolResultExpanded> => {
        logger.info(input.diff);
        try {
            const output = await applyPatch(exec, input.diff, config.location);
            const trueOutput = `Applied patch:\n${input.diff}\nOutput:\n${output}\n`;
            return {
                textResultForLlm: trueOutput,
                resultType: "success",
                sessionLog: strLenTruncate(trueOutput, "output"),
                toolTelemetry: {},
            };
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } catch (error: any) {
            const err = `Error applying patch: ${error.message}`;
            return {
                textResultForLlm: err,
                resultType: "failure",
                sessionLog: err,
                toolTelemetry: {},
            };
        }
    },
});
