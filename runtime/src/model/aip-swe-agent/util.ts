/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import Anthropic from "@anthropic-ai/sdk";
import * as fs from "fs";
import * as path from "path";
import * as tar from "tar";

export async function tarAndBase64EncodeDirectory(directoryPath: string): Promise<string> {
    if (!fs.existsSync(directoryPath)) {
        throw new Error(`Directory ${directoryPath} does not exist.`);
    }

    return new Promise((resolve, reject) => {
        const buffers: Buffer[] = [];

        const tarStream = tar.c(
            {
                gzip: true,
                cwd: directoryPath,
                filter: (filePath) => {
                    // --exclude-vcs
                    const baseName = path.basename(filePath);
                    return ![".git", ".svn", ".hg", ".bzr"].includes(baseName);
                },
            },
            ["."],
        );

        tarStream.on("error", (err) => reject(err));

        tarStream.on("data", (data) => buffers.push(data));
        tarStream.on("end", () => {
            const buffer = Buffer.concat(buffers);
            const base64 = buffer.toString("base64");
            resolve(base64);
        });

        tarStream.end();
    });
}

export function getToolDescription(tool: Anthropic.Tool, includeSchema: boolean = false): string {
    function stringifyToolParameters(tool: Anthropic.Tool): string {
        const params = tool.input_schema;
        if (!params?.properties) {
            return "No Input Schema available. Use your own judgement to provide the correct input";
        }

        return Object.entries(params.properties)
            .map(([name, prop]) => {
                const isRequired = Array.isArray(params.required) && params.required.includes(name);
                const required = isRequired ? "(required)" : "(optional)";
                const type = prop.type;
                const desc = prop.description ?? "";
                const enumVals = prop.enum ? `[${prop.enum.join("|")}]` : "";
                return `\n  - ${name} ${required}: ${type} ${enumVals}\n    ${desc}`;
            })
            .join("");
    }

    return (
        `**${tool.name}**: ${tool.description}` +
        (includeSchema ? `\nInput Schema: ${stringifyToolParameters(tool)}` : "")
    );
}
