/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import path from "path";

import { DefaultAzureCredential } from "@azure/identity";

import { ChatCompletionTool } from "openai/resources";
import { v4 as uuid4 } from "uuid";
import { RunnerLogger } from "../../runner";
import { RuntimeSettings } from "../../settings";
import { demandInstanceId } from "../../settings/demand";
import { ClientRetryPolicy } from "../client";
import { tarAndBase64EncodeDirectory } from "./util";

export const DEFAULT_MODEL = "swe-mini2-genai-v2";

const BASE_URL = "";
const SESSION_TTL_IN_SECONDS = 600; // 10 minutes
const MAX_STEPS = 1000;
const MODELS = ["swe-mini2-genai-v2"];
const RETRY_DELAY_SECONDS = 5;

export type OAIMessage = {
    id: string;
    role: "tool" | "assistant" | "system" | "user";
    content: string;
    audience: string;
    channel: "git_apply_patch" | "system_error";
    end_turn: boolean;
    author_name?: string;
};

export class ReverseProxyClient {
    private _sessionId: string;
    private _projectLocation?: string;
    public readonly model: string;

    public constructor(
        private readonly settings: RuntimeSettings,
        private readonly logger: RunnerLogger,
        model?: string,
        public readonly runLocal?: boolean,
        private readonly retryPolicy?: ClientRetryPolicy,
    ) {
        if (model?.trim() && !MODELS.includes(model)) {
            throw new Error(`Model '${model}' is not supported`);
        }

        this.model = model?.trim() || DEFAULT_MODEL;
        this._sessionId = demandInstanceId(this.settings);
    }

    public async *run(
        messages: Required<Pick<OAIMessage, "role" | "content">>[],
        guidelines?: string,
        tools: ChatCompletionTool[] = [],
        signal?: AbortSignal,
    ): AsyncGenerator<OAIMessage> {
        const data = {
            session_id: this._sessionId,
            model: this.model,
            messages,
            preset_instruction: "no_question",
            additional_instruction: guidelines,
            cot_juice: 512, // default is 128. Increase to think more.
            search_folder_path: this._projectLocation,
            tools,
            tool_execution_mode: this.runLocal ? "local" : undefined,
            max_episode_steps: MAX_STEPS,
        };

        const response = await this.stream("/roll", "POST", data, signal);
        const streamedMessages: Set<string> = new Set(); // to avoid duplicate messages

        for await (const message of response) {
            const snapshotMessage = message as OAIMessage;
            if (streamedMessages.has(snapshotMessage.id)) {
                // we have already seen this message and processed it
                continue;
            }

            streamedMessages.add(snapshotMessage.id);
            yield snapshotMessage;
        }
    }

    public async newSession(signal?: AbortSignal): Promise<{ sessionId: string }> {
        if (!this.runLocal) {
            const response = await this.request(
                "/new_session",
                "POST",
                {
                    idle_ttl: SESSION_TTL_IN_SECONDS,
                },
                signal,
            );

            const session = response as { session_id: string };
            if (!session.session_id) {
                throw new Error("Failed to create a new session");
            }

            this._sessionId = session.session_id;
            this.logger.info(`Initialized session with ID: ${this._sessionId}`);
        }

        return { sessionId: this._sessionId };
    }

    public async initializeProject(projectDir: string, signal?: AbortSignal): Promise<string> {
        this._projectLocation = projectDir;
        if (!this.runLocal) {
            const gzip_b64encoded = await tarAndBase64EncodeDirectory(projectDir);

            const data = {
                session_id: this._sessionId,
                zip_base64: gzip_b64encoded,
                folder_name: path.basename(projectDir),
            };

            const response = await this.request("/upload", "POST", data, signal);

            const uploadedPath = (response as { unzipped_folder_path: string }).unzipped_folder_path;
            this._projectLocation = uploadedPath || `/root/${path.basename(projectDir)}`;
        }

        return this._projectLocation;
    }

    private async request(url: string, method: string = "GET", body?: unknown, signal?: AbortSignal): Promise<unknown> {
        const response = await this.fetch(url, method, body, signal);
        const json = await response.json();
        return json;
    }

    private async *stream(
        url: string,
        method: string = "GET",
        body?: unknown,
        signal?: AbortSignal,
    ): AsyncIterable<unknown> {
        const response = await this.fetch(url, method, body, signal);
        const reader = response.body?.getReader() as ReadableStreamDefaultReader<Uint8Array>;
        if (!reader) {
            throw new Error("No reader available");
        }

        for await (const line of this.readLines(reader, signal)) {
            if (line.trim()) {
                try {
                    yield JSON.parse(line);
                } catch (error) {
                    this.logger.warning(`Failed to parse JSON: '${line}': ${error}`);
                }
            }
        }
    }

    private async *readLines(
        reader: ReadableStreamDefaultReader<Uint8Array>,
        signal?: AbortSignal,
    ): AsyncIterable<string> {
        const decoder = new TextDecoder();
        let buffer = "";

        while (true) {
            const { value, done } = await reader.read();
            if (done) break;
            if (signal?.aborted) break;

            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split("\n");
            buffer = lines.pop() || ""; // Keep the last partial line in the buffer

            for (const line of lines) {
                yield line;
            }
        }

        if (buffer) {
            yield buffer;
        }
    }

    private async fetch(url: string, method: string = "GET", body?: unknown, signal?: AbortSignal): Promise<Response> {
        if (BASE_URL === "") {
            throw new Error("BASE_URL is not set. Please configure the reverse proxy URL.");
        }

        const token = await this.getSessionAccessToken();
        if (!token) {
            throw new Error("No access token available");
        }

        let retryCount = 0;
        const maxRetries = this.retryPolicy?.maxRetries || 3;
        const defaultRetryAfterSeconds =
            this.retryPolicy?.rateLimitRetryPolicy?.defaultRetryAfterSeconds || RETRY_DELAY_SECONDS;

        do {
            const requestId = uuid4();
            this.logger.debug(`${method} ${BASE_URL}${url}\nx-ms-client-request-id: ${requestId}`);

            let response: Response | Error;
            try {
                response = await fetch(`${BASE_URL}${url}`, {
                    method,
                    headers: {
                        Authorization: `Bearer ${token}`,
                        "Content-Type": "application/json",
                        "x-ms-client-request-id": requestId,
                        "User-Agent": "padawan",
                    },
                    body: body ? JSON.stringify(body) : undefined,
                    signal,
                });
            } catch (error) {
                response = error as Error;
            }

            // return if successful
            if (response instanceof Response && response.ok) {
                return response;
            }

            let errorMessage = "";
            let details = "";
            if (response instanceof Response) {
                errorMessage = `HTTP status: ${response.status}`;
                details = `Headers:\n`;
                response.headers.forEach((value, name) => {
                    details += `${name}: ${value}\n`;
                });
            } else {
                errorMessage = `Error: ${response.message}\n`;
            }

            this.logger.error(`Response failed. ${errorMessage}\n${details}`);
            if (this.shouldRetry(response, retryCount)) {
                retryCount++;
                this.logger.warning(`Retrying request for ${errorMessage} ... (${retryCount}/${maxRetries})`);
                await new Promise((resolve) => setTimeout(resolve, defaultRetryAfterSeconds * 1000)); // Wait before retrying
                continue;
            }

            // handle all other errors
            throw new Error(`Request failed: ${errorMessage}`);
        } while (retryCount <= maxRetries);

        throw new Error(`Failed to fetch after ${maxRetries} retries`);
    }

    private shouldRetry(response: Response | Error, retryCount: number): boolean {
        const maxRetries = this.retryPolicy?.maxRetries || 3;
        const errorCodesToRetry = [424, 429, ...(this.retryPolicy?.errorCodesToRetry || [])];

        if (retryCount >= maxRetries) {
            return false;
        }

        if (response instanceof Response && errorCodesToRetry.includes(response.status)) {
            return true;
        }

        if (response instanceof TypeError) {
            return true;
        }

        return false;
    }

    private async getSessionAccessToken() {
        // For local scenarios
        const token = this.settings.api?.aipSweAgent?.token;
        if (token) {
            return token;
        }

        // For deployed scenarios
        const credential = new DefaultAzureCredential();
        const tokenResponse = await credential.getToken("https://ml.azure.com/.default");
        return tokenResponse.token;
    }
}
