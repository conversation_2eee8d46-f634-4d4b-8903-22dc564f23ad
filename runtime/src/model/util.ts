/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { SweAgentKind } from "../agents/sweagent";
import { DefaultAgentModel } from "./defaults";

export function splitAgentModelSetting(
    agentModelSetting: string | undefined,
): Partial<{ agent: SweAgentKind; model: string }> {
    // if no model provided, use our default
    if (!agentModelSetting) {
        return DefaultAgentModel;
    }

    // split agent and model if provided
    const [agent, ...modelParts] = agentModelSetting?.split(":") ?? [agentModelSetting, undefined];
    const model = modelParts.join(":") || undefined;

    // return consistent values for agent and model if empty
    return {
        agent: (agent as SweAgentKind) || undefined,
        model: model || undefined,
    };
}
