/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { encodingForModel, Tiktoken, TiktokenModel } from "js-tiktoken";
import { ChatCompletionContentPartImage, ChatCompletionMessageParam, ChatCompletionTool } from "openai/resources";

const tokenizerCache = new Map<string, Tiktoken>();

/**
 * If our token counting package doesn't support counting tokens for a model, we'll
 * default to using the token counting method for this model.
 *
 * This should be a good-enough approximation for most models, and only a problem if
 * we're pushing right up against context window limits.
 *
 * The current default of "gpt-4o" ultimately resolves to a o200k_base tokenizer.
 */
const defaultModelForTokenCounting = "gpt-4o";

function initDefaultTokenizer(): Tiktoken {
    if (!tokenizerCache.has(defaultModelForTokenCounting)) {
        tokenizerCache.set(
            defaultModelForTokenCounting,
            encodingForModel(defaultModelForTokenCounting as TiktokenModel),
        );
    }

    const deafultTokenizer = tokenizerCache.get(defaultModelForTokenCounting);
    if (!deafultTokenizer) {
        throw new Error("Failed to initialize default tokenizer");
    }
    return deafultTokenizer;
}

function getTokenizer(modelName: string): Tiktoken {
    const defaultTokenizer = initDefaultTokenizer();

    let tokenizer: Tiktoken | undefined;
    if (tokenizerCache.has(modelName)) {
        tokenizer = tokenizerCache.get(modelName);
    } else {
        try {
            tokenizer = encodingForModel(modelName as TiktokenModel);
        } catch {
            // noop
        }
    }

    const resultingTokenizer = tokenizer ?? defaultTokenizer;
    tokenizerCache.set(modelName, resultingTokenizer);
    return resultingTokenizer;
}

export function countTokensInString(text: string, modelName: string): number {
    const tokenizer = getTokenizer(modelName);
    const tokens = tokenizer.encode(text);
    return tokens.length;
}

export function countTokensInChatCompletionMessages(messages: ChatCompletionMessageParam[], modelName: string): number {
    // A list of chat completion messages always consumes at least 3 tokens
    const baseNumTokens = 3;

    return (
        baseNumTokens +
        messages.reduce(
            (totalTokens, message) => countTokensInChatCompletionMessage(message, modelName) + totalTokens,
            0,
        )
    );
}

/**
 * Try to match logic in at least:
 * 1. https://github.com/github/copilot-api/blob/main/pkg/token/anthropic.go (we mainly use Anthropic models)
 * 2. https://github.com/github/copilot-api/blob/main/pkg/tokenizer/tokenizer.go
 *
 * Add logic/refactor/etc. for other models as needed.
 */
export function countTokensInChatCompletionMessage(message: ChatCompletionMessageParam, modelName: string): number {
    let strTokens = 0;
    // Every message incurrs at least 3 tokens
    let nonStrTokens = 3;

    if (message.role) {
        // Roles add a token
        nonStrTokens++;
    }

    if (message.role !== "function" && message.role !== "tool") {
        if (message.name) {
            // Names have an extra token onto of their string token count
            strTokens += countTokensInString(message.name, modelName) + 1;
        }
    }

    if (message.content) {
        if (typeof message.content === "string") {
            strTokens += countTokensInString(message.content, modelName);
        } else {
            for (const part of message.content) {
                let strContentToCount = "";
                if (part.type === "text") {
                    strContentToCount = part.text;
                } else if (part.type === "refusal") {
                    strContentToCount = part.refusal;
                } else if (part.type === "image_url") {
                    nonStrTokens += countImagePartTokens(part, modelName);
                } else {
                    // The above should be all of the types we care about, but
                    // just in case somebody forgets to update this code, we'll
                    // just stringify any other types of parts and count
                    // all of the tokens in that string.
                    strContentToCount = JSON.stringify(part);
                }
                strTokens += countTokensInString(strContentToCount, modelName);
            }
        }
    }
    return strTokens + nonStrTokens;
}

export function countTokensInToolDefinitions(toolDefinitions: ChatCompletionTool[], modelName: string): number {
    const toolDefinitionsString = JSON.stringify(toolDefinitions);
    return countTokensInString(toolDefinitionsString, modelName);
}

export function countImagePartTokens(_part: ChatCompletionContentPartImage, modelName: string): number {
    if (modelName.startsWith("claude")) {
        // Images are counted as 750 tokens for Claude models
        return 750;
    }
    return 0;
}
