/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { ChatCompletionChunk } from "openai/resources.mjs";
import { createChunkWithSessionLogToolResult } from "../helpers/openAiHelpers";
import { CopilotSessionsClient } from "../model/capi/sessions-client";
import { isModelCallSuccessEvent, isToolExecutionEvent } from "../model/event";
import { Runner<PERSON>ogger } from "../runner";
import { isTelemetryEvent } from "../telemetry";
import type {
    AgentCallbackCommentReplyEvent,
    AgentCallbackErrorEvent,
    AgentCallbackPartialResultEvent,
    AgentCallbackProgressEvent,
    AgentCallbackResultEvent,
} from "../types";
import { IAgentCallback } from "./callback";

export class SessionsCallback implements IAgentCallback {
    private sessionClient: CopilotSessionsClient;
    private logger: RunnerLogger;
    private toolCallIdToCompletion: Map<string, ChatCompletionChunk>;

    constructor(sessionsClient: CopilotSessionsClient, logger: RunnerLogger) {
        this.sessionClient = sessionsClient;
        this.logger = logger;
        this.toolCallIdToCompletion = new Map<string, ChatCompletionChunk>();
    }

    public async progress(content: AgentCallbackProgressEvent) {
        // @todo Improve type guards so we can simply do `isSessionLog(content)`
        const isLog = content.kind === "log";
        const isUserMessage = content.kind === "message" && content.message.role === "user";
        const isResponse = content.kind === "response";

        const skip = isLog || isUserMessage || isResponse || isTelemetryEvent(content);
        if (skip) return;
        if (isModelCallSuccessEvent(content)) {
            const responseChunk = content.responseChunk;
            await this.sessionClient.log(responseChunk);
            for (const choice of responseChunk.choices) {
                for (const toolCall of choice.delta.tool_calls || []) {
                    if (toolCall.id) {
                        this.toolCallIdToCompletion.set(toolCall.id, responseChunk);
                    }
                }
            }
        } else if (isToolExecutionEvent(content)) {
            const completionChunk = this.toolCallIdToCompletion.get(content.toolCallId);
            if (completionChunk) {
                const toolResult = content.toolResult;

                // Fallback to using the LLM text (required) if the session log is not provided.
                const toolResultSessionLog = toolResult.sessionLog ?? toolResult.textResultForLlm;

                // We must always provide a session log otherwise the UI will show a loading animation forever.
                // - If successful:
                //   - If there is a session log, use it.
                //   - If there is no session log, use the text result for LLM. This is often the same as the session log.
                // - If failure:
                //   - If there is an error, show it.
                //   - If there is no error, use the text result for LLM. In some tools, like Playwright MCP, this may contain an error.
                const sessionLogToUse =
                    toolResult.resultType == "success"
                        ? toolResultSessionLog
                        : toolResult.error
                          ? `<error>${toolResult.error}</error>`
                          : toolResultSessionLog;
                await this.sessionClient.log(
                    createChunkWithSessionLogToolResult(sessionLogToUse, completionChunk, content.toolCallId),
                );

                // Remove the tool call from the map as it has been processed.
                this.toolCallIdToCompletion.delete(content.toolCallId);
            } else {
                this.logger.warning(`No completion found for tool call ID: ${content.toolCallId}`);
            }
        }
    }

    public partialResult(_result: AgentCallbackPartialResultEvent) {
        // noop
        return Promise.resolve();
    }

    public commentReply(_reply: AgentCallbackCommentReplyEvent) {
        // noop
        return Promise.resolve();
    }

    public result(_result: AgentCallbackResultEvent) {
        // noop
        return Promise.resolve();
    }

    public error(_error: AgentCallbackErrorEvent) {
        // noop
        return Promise.resolve();
    }
}
