/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { Chalk } from "chalk";
import * as fs from "fs";
import {
    ChatCompletionContentPartRefusal,
    ChatCompletionContentPartText,
    ChatCompletionMessageToolCall,
} from "openai/resources/chat/completions";
import * as path from "path";
import { BlockedRequest, getBlockedRequests } from "../firewall";
import { isAssistantMessageEvent, isAssistantMessageWithToolCalls, isToolMessageEvent } from "../model/event";
import { isTelemetryEvent } from "../telemetry";
import { TrajectoryFormatter } from "../trajectory/TrajectoryFormatter";
import type {
    AgentCallbackCommentReplyEvent,
    AgentCallbackErrorEvent,
    AgentCallbackPartialResultEvent,
    AgentCallbackProgressEvent,
    AgentCallbackResultEvent,
} from "../types";
import { IAgentCallback } from "./callback";

const chalk =
    process.env.FORCE_COLOR === "true" || process.env.FORCE_COLOR === "1" ? new Chalk({ level: 3 }) : new Chalk();

const chalkTheError = (x: string) => {
    return chalk.red(x);
};

export class TrajectoryFileCallback implements IAgentCallback {
    private toolCalls: { [key: string]: ChatCompletionMessageToolCall } = {};
    private trajectoryOutputFile: string;
    private trajectoryStream?: fs.WriteStream;
    private trajectoryFormatter: TrajectoryFormatter = new TrajectoryFormatter();

    constructor(trajectoryOutputFilePath: string) {
        this.trajectoryOutputFile = trajectoryOutputFilePath;
        this.initTrajectoryOutputFileIfNeeded();
    }

    public async progress(content: AgentCallbackProgressEvent) {
        // @todo Improve type guards so we can go straight into the `if(this.trajectoryStream)` block
        const isLog = content.kind === "log";
        const isUserMessage = content.kind === "message" && content.message.role === "user";
        const isResponse = content.kind === "response";

        const skip = isLog || isUserMessage || isResponse || isTelemetryEvent(content);
        if (skip) return;

        if (this.trajectoryStream) {
            if (isAssistantMessageEvent(content)) {
                if (content.message.content) {
                    this.handleAssistantMessageWithContent(content.message.content);
                }

                if (content.message.reasoning_text) {
                    this.handleAssistantMessageWithContent(content.message.reasoning_text);
                }

                if (isAssistantMessageWithToolCalls(content)) {
                    this.handleAssistantToolCalls(content.message.tool_calls);
                }
                return;
            } else if (isToolMessageEvent(content)) {
                this.handleToolCallId(content.message.tool_call_id, content.message.content);
                return;
            }
        }
    }

    public partialResult(_result: AgentCallbackPartialResultEvent): Promise<void> {
        // noop
        return Promise.resolve();
    }

    public commentReply(_reply: AgentCallbackCommentReplyEvent): Promise<void> {
        // noop
        return Promise.resolve();
    }

    public async result(result: AgentCallbackResultEvent) {
        if (typeof result === "object") {
            result.blockedRequests = result.blockedRequests ?? (await this.getBlockedRequests());
        }

        // Close trajectory file stream if open
        if (this.trajectoryStream) {
            return await new Promise<void>((resolve) => {
                this.trajectoryStream?.end(() => {
                    this.trajectoryStream = undefined;
                    resolve();
                });
            });
        }
    }

    public async error(_error: AgentCallbackErrorEvent) {
        // Close trajectory file stream on error if open
        if (this.trajectoryStream) {
            return await new Promise<void>((resolve) => {
                this.trajectoryStream?.end(() => {
                    this.trajectoryStream = undefined;
                    resolve();
                });
            });
        }
    }

    private handleAssistantMessageWithContent(
        messageContent: string | (ChatCompletionContentPartText | ChatCompletionContentPartRefusal)[],
    ) {
        if (this.trajectoryStream) {
            this.writeTrajectoryAssistantMessage(messageContent);
        }
    }

    private handleAssistantToolCalls(tool_calls: ChatCompletionMessageToolCall[]) {
        for (const toolCall of tool_calls) {
            const id = toolCall.id;
            this.toolCalls[id] = toolCall;
            if (this.trajectoryStream) {
                this.writeTrajectoryToolCall(toolCall);
            }
        }
    }

    private handleToolCallId(id: string, messageContent: string | ChatCompletionContentPartText[]) {
        const toolCall = this.toolCalls[id];
        if (toolCall) {
            delete this.toolCalls[id];
            const isFunctionCall = toolCall.type === "function";
            if (isFunctionCall) {
                const functionName = toolCall.function.name;
                if (functionName) {
                    if (this.trajectoryStream) {
                        this.writeTrajectoryFunctionResult(messageContent);
                    }
                }
            }
        }
    }

    private initTrajectoryOutputFileIfNeeded() {
        if (this.trajectoryOutputFile) {
            try {
                const dir = path.dirname(this.trajectoryOutputFile!);
                if (!fs.existsSync(dir)) {
                    fs.mkdirSync(dir, { recursive: true });
                }
                this.trajectoryStream = fs.createWriteStream(this.trajectoryOutputFile!, { flags: "w" });
            } catch (e) {
                const errorEvent: AgentCallbackErrorEvent = {
                    text: e instanceof Error ? e.message : String(e),
                    name: e instanceof Error ? e.name : "Error",
                    stack: e instanceof Error ? e.stack : undefined,
                };
                console.error(chalkTheError(`ERROR: ${JSON.stringify(errorEvent, null, 2)}`));
                this.trajectoryStream = undefined;
            }
        }
    }

    private writeTrajectoryAssistantMessage(
        content: string | (ChatCompletionContentPartText | ChatCompletionContentPartRefusal)[],
    ) {
        const contentStr = typeof content === "string" ? content : JSON.stringify(content);
        const xml = this.trajectoryFormatter.formatMessage("assistant", contentStr);
        if (xml) this.writeToTrajectoryFile(xml);
    }

    private writeTrajectoryToolCall(toolCall: ChatCompletionMessageToolCall) {
        const xml = this.trajectoryFormatter.formatToolCall(toolCall);
        this.writeToTrajectoryFile(xml);
    }

    private writeTrajectoryFunctionResult(content: string | ChatCompletionContentPartText[]) {
        const contentStr = typeof content === "string" ? content : JSON.stringify(content);
        const xml = this.trajectoryFormatter.formatToolResult(contentStr);
        this.writeToTrajectoryFile(xml);
    }

    private writeToTrajectoryFile(content: string): void {
        if (this.trajectoryStream) {
            this.trajectoryStream.write(content + "\n");
        }
    }

    private async getBlockedRequests(): Promise<BlockedRequest[]> {
        try {
            return await getBlockedRequests();
        } catch (err) {
            console.error(`Error parsing firewall logs: ${err}`);
            return [];
        }
    }
}
