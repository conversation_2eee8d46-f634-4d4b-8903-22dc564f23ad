/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { Chalk } from "chalk";
import {
    ChatCompletionContentPartRefusal,
    ChatCompletionContentPartText,
    ChatCompletionMessageParam,
    ChatCompletionMessageToolCall,
} from "openai/resources/chat/completions";
import { BlockedRequest, getBlockedRequests } from "../firewall";
import { deepJsonParse } from "../helpers/json-helpers";
import { indentLines, stringOrStringify, yamlFormatStringRhs } from "../helpers/string-helpers";
import type { ToolMessageEvent } from "../model/event";
import {
    isAssistantMessageEvent,
    isAssistantMessageWithToolCalls,
    isModelCallFailureEvent,
    isToolMessageEvent,
    isUserMessageEvent,
} from "../model/event";
import { isTelemetryEvent } from "../telemetry";
import type {
    AgentCallbackCommentReplyEvent,
    AgentCallbackErrorEvent,
    AgentCallbackPartialResultEvent,
    AgentCallbackProgressEvent,
    AgentCallbackResultEvent,
} from "../types";
import { IAgentCallback } from "./callback";

const assistantLabel = "copilot";
const chalk =
    process.env.FORCE_COLOR === "true" || process.env.FORCE_COLOR === "1" ? new Chalk({ level: 3 }) : new Chalk();
const chalkTheAssistantLabel = (x: string) => {
    return chalk.cyan(x);
};
const chalkTheFunction = (x: string) => {
    return chalk.gray(x);
};
const chalkTheContent = (x: string) => {
    return chalk.whiteBright(x);
};
const chalkTheEvent = (x: string) => {
    return chalk.gray(x);
};
const chalkTheResult = (x: string) => {
    return chalk.green(x);
};
const chalkTheError = (x: string) => {
    return chalk.red(x);
};

export class LoggerCallback implements IAgentCallback {
    private toolCalls: { [key: string]: ChatCompletionMessageToolCall } = {};
    private log: (msg: string) => void;

    constructor(log: (msg: string) => void) {
        this.log = log;
    }

    public async progress(content: AgentCallbackProgressEvent) {
        // @todo Improve type guards so we can go straight into the `if(isAssistantMessageEvent)` block
        const isLog = content.kind === "log";
        const isUserMessage = content.kind === "message" && content.message.role === "user";
        const isResponse = content.kind === "response";

        const skip = isLog || isUserMessage || isResponse || isTelemetryEvent(content);
        if (skip) return;

        if (isAssistantMessageEvent(content)) {
            if (content.message.content) {
                this.handleAssistantMessageWithContent(content.message.content);
            }

            if (content.message.reasoning_text) {
                this.handleAssistantMessageWithContent(content.message.reasoning_text);
            }

            if (isAssistantMessageWithToolCalls(content)) {
                this.handleAssistantToolCalls(content.message.tool_calls);
            }
            return;
        } else if (isToolMessageEvent(content)) {
            this.handleToolCallId(content.message.tool_call_id, content.message.content);
            return;
        } else if (isModelCallFailureEvent(content)) {
            return;
        } else if (isUserMessageEvent(content)) {
            return;
        } else if (content.kind === "message" && "message" in content) {
            this.displayEvent(content);
        }
    }

    public partialResult(result: AgentCallbackPartialResultEvent) {
        this.displayPartialResult(result);
        return Promise.resolve();
    }

    public commentReply(reply: AgentCallbackCommentReplyEvent) {
        this.displayCommentReply(reply);
        return Promise.resolve();
    }

    public async result(result: AgentCallbackResultEvent) {
        if (typeof result === "object") {
            result.blockedRequests = result.blockedRequests ?? (await this.getBlockedRequests());
        }

        this.displayResult(result);
    }

    public async error(error: AgentCallbackErrorEvent) {
        error.blockedRequests = error.blockedRequests ?? (await this.getBlockedRequests());

        this.displayError(error);
    }

    private handleAssistantMessageWithContent(
        messageContent: string | (ChatCompletionContentPartText | ChatCompletionContentPartRefusal)[],
    ) {
        this.displayAssistantMessage(messageContent);
    }

    private handleAssistantToolCalls(tool_calls: ChatCompletionMessageToolCall[]) {
        for (const toolCall of tool_calls) {
            const id = toolCall.id;
            this.toolCalls[id] = toolCall;
        }
    }

    private handleToolCallId(id: string, messageContent: string | ChatCompletionContentPartText[]) {
        const toolCall = this.toolCalls[id];
        if (toolCall) {
            delete this.toolCalls[id];
            const isFunctionCall = toolCall.type === "function";
            if (isFunctionCall) {
                const functionName = toolCall.function.name;
                if (functionName) {
                    this.displayFunctionCall(functionName, toolCall.function.arguments, messageContent);
                }
            }
        }
    }

    private displayAssistantMessage(
        messageContent: string | (ChatCompletionContentPartText | ChatCompletionContentPartRefusal)[],
    ) {
        const displayContent = typeof messageContent === "string" ? messageContent : JSON.stringify(messageContent);
        this.log(chalkTheAssistantLabel(`\n${assistantLabel}: `) + chalkTheContent(displayContent.trim()));
    }

    private displayFunctionCall(
        functionName: string,
        functionArguments: string,
        functionResult: string | ChatCompletionContentPartText[],
    ) {
        let safeForDisplayFunctionArguments: { [key: string]: unknown };
        try {
            safeForDisplayFunctionArguments = deepJsonParse(functionArguments) ?? {};
        } catch {
            safeForDisplayFunctionArguments = {
                functionArguments: functionArguments,
            };
        }
        this.displayGenericFunctionCall(functionName, safeForDisplayFunctionArguments, functionResult);
    }

    private displayGenericFunctionCall(
        functionName: string,
        functionArguments: { [key: string]: unknown },
        result: string | ChatCompletionContentPartText[],
    ) {
        const indentBySpaces = 2;
        this.log(chalkTheFunction(`\nfunction:`));
        this.log(chalkTheFunction(indentLines(`name: ${functionName}`, indentBySpaces)!));

        const hasArgs = functionArguments && Object.keys(functionArguments).length > 0;
        if (hasArgs) {
            const argsLabel = indentLines("args:", indentBySpaces)!;
            this.log(chalkTheFunction(argsLabel));

            const sortedKeys = Object.keys(functionArguments).sort();
            for (const key of sortedKeys) {
                const value = stringOrStringify(functionArguments[key], indentBySpaces);
                const formatted = `${key}: ${yamlFormatStringRhs(value, indentBySpaces)}`;
                const indented = indentLines(formatted, indentBySpaces * 2)!;
                this.log(chalkTheFunction(indented));
            }
        }

        const resultStr = typeof result === "string" ? result : JSON.stringify(result);
        const trimmedResult = resultStr?.trim() || "";

        const formatted = `result: ${yamlFormatStringRhs(trimmedResult, indentBySpaces)}`;
        this.log(chalkTheFunction(indentLines(formatted, indentBySpaces)!));
    }

    private displayEvent(content: { kind: "message"; message: ChatCompletionMessageParam } | ToolMessageEvent) {
        this.log(
            chalkTheAssistantLabel(`\n${assistantLabel}-event: `) + chalkTheEvent(JSON.stringify(content, null, 2)),
        );
    }

    private displayPartialResult(result: { branchName: string; message: string }) {
        this.log(
            chalkTheAssistantLabel(`\n${assistantLabel}-update: `) +
                chalkTheResult(`${result.branchName}: ${result.message.trim()}`),
        );
    }

    private displayCommentReply(reply: { comment_id: number; message: string }) {
        this.log(
            chalkTheAssistantLabel(`\n${assistantLabel}-reply: `) +
                chalkTheResult(`${reply.comment_id}: ${reply.message.trim()}`),
        );
    }

    private displayResult(result: AgentCallbackResultEvent) {
        // Don't show blocked requests - can be very verbose
        if (typeof result === "object" && Object.prototype.hasOwnProperty.call(result, "blockedRequests")) {
            result = { ...result };
            delete result.blockedRequests;
        }
        const asStr = typeof result === "string" ? result : JSON.stringify(result, null, 2);
        this.log(chalkTheAssistantLabel(`\n${assistantLabel}-result: `) + chalkTheResult(asStr));
    }

    private displayError(error: AgentCallbackErrorEvent) {
        // Don't show blocked requests - can be very verbose
        if (typeof error === "object" && Object.prototype.hasOwnProperty.call(error, "blockedRequests")) {
            error = { ...error };
            delete error.blockedRequests;
        }
        this.log(chalkTheError(`ERROR: ${JSON.stringify(error, null, 2)}`));
    }

    private async getBlockedRequests(): Promise<BlockedRequest[]> {
        try {
            return await getBlockedRequests();
        } catch (err) {
            this.log(`Error parsing firewall logs: ${err}`);
            return [];
        }
    }
}
