/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { AgentAction, AgentUpdateKind } from "../agent";
import { BlockedRequest, getBlockedRequests } from "../firewall";
import { newRecordCallback } from "../record-callback";
import { Runner } from "../runner";
import { RuntimeSettings } from "../settings";
import type {
    AgentCallbackCommentReplyEvent,
    AgentCallbackError<PERSON><PERSON>,
    AgentCallbackPartialResultEvent,
    AgentCallbackProgressE<PERSON>,
    AgentCallbackResultEvent,
} from "../types";
import { AgentUpdateContentMap, IAgentCallback } from "./callback";

export type SweagentdCallbackInfo = {
    callbackUrl: string;
    runner: Runner;
    repo: string;
    repo_id: number;
    repo_name: string;
    repo_owner: string;
    repo_owner_id: number;
    job_id: string;
    action: AgentAction;
    settings: RuntimeSettings;
};

export class SweagentdCallback implements IAgentCallback {
    private recordCallback: <K extends AgentUpdateKind>(kind: K, content: AgentUpdateContentMap[K]) => Promise<void>;

    constructor(callbackInfo: SweagentdCallbackInfo) {
        // @todo Move everything in `newRecordCallback` to simply be in this class
        this.recordCallback = newRecordCallback(
            callbackInfo.callbackUrl,
            callbackInfo.runner,
            callbackInfo.repo,
            callbackInfo.repo_id,
            callbackInfo.repo_name,
            callbackInfo.repo_owner,
            callbackInfo.repo_owner_id,
            callbackInfo.job_id,
            callbackInfo.action,
            callbackInfo.settings,
        );
    }

    public async progress(content: AgentCallbackProgressEvent) {
        await this.recordCallback("progress", content);
    }

    public async partialResult(result: AgentCallbackPartialResultEvent) {
        await this.recordCallback("partialResult", result);
    }

    public async commentReply(reply: AgentCallbackCommentReplyEvent) {
        await this.recordCallback("commentReply", reply);
    }

    public async result(result: AgentCallbackResultEvent) {
        if (typeof result === "object") {
            result.blockedRequests = result.blockedRequests ?? (await this.getBlockedRequests());
        }
        await this.recordCallback("result", result);
    }

    public async error(error: AgentCallbackErrorEvent) {
        error.blockedRequests = error.blockedRequests ?? (await this.getBlockedRequests());
        await this.recordCallback("error", error);
    }

    private async getBlockedRequests(): Promise<BlockedRequest[]> {
        try {
            return await getBlockedRequests();
        } catch (err) {
            console.error(`Error parsing firewall logs: ${err}`);
            return [];
        }
    }
}
