/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import type {
    Agent<PERSON><PERSON>backCommentReplyEvent,
    AgentCallbackErrorEvent,
    AgentCallbackPartialResultEvent,
    AgentCallbackProgressEvent,
    AgentCallbackResultEvent,
} from "../types";

/**
 * A way for the runtime agent to callback to something with progress, results, errors, etc.
 */
export interface IAgentCallback {
    progress(content: AgentCallbackProgressEvent): Promise<void>;
    partialResult(result: AgentCallbackPartialResultEvent): Promise<void>;
    commentReply(reply: AgentCallbackCommentReplyEvent): Promise<void>;
    result(result: AgentCallbackResultEvent): Promise<void>;
    error(error: AgentCallbackErrorEvent): Promise<void>;
}

/**
 * Maps each AgentUpdateKind to its corresponding content type
 */
export type AgentUpdateContentMap = {
    progress: AgentCallbackProgressEvent;
    partialResult: AgentCallbackPartialResultEvent;
    commentReply: AgentCallbackCommentReplyEvent;
    result: AgentCallbackResultEvent;
    error: AgentCallbackErrorEvent;
};

export class CompoundCallback implements IAgentCallback {
    private callbacks: IAgentCallback[];

    constructor() {
        this.callbacks = [];
    }

    public addCallback(callback: IAgentCallback | undefined) {
        if (callback) {
            this.callbacks.push(callback);
        }
        return this;
    }

    public async progress(content: AgentCallbackProgressEvent) {
        await Promise.all(this.callbacks.map((cb) => cb.progress(content)));
    }

    public async partialResult(result: AgentCallbackPartialResultEvent) {
        await Promise.all(this.callbacks.map((cb) => cb.partialResult(result)));
    }

    public async commentReply(reply: AgentCallbackCommentReplyEvent) {
        await Promise.all(this.callbacks.map((cb) => cb.commentReply(reply)));
    }

    public async result(result: AgentCallbackResultEvent) {
        await Promise.all(this.callbacks.map((cb) => cb.result(result)));
    }

    public async error(error: AgentCallbackErrorEvent) {
        await Promise.all(this.callbacks.map((cb) => cb.error(error)));
    }
}
