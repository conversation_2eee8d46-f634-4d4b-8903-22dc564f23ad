/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { exec, ExecException, execFile, ExecFileException, ExecOptions } from "child_process";
import shellEscape from "shell-escape";
import { RunnerExec, RunnerExecOptions, RunnerExecOutput } from ".";
import { LogLevel, RunnerLogger } from "../logger";

// formerly knows as safeExec
export class DefaultExec implements RunnerExec {
    private logger: RunnerLogger;

    constructor(logger: <PERSON>Logger) {
        this.logger = logger;
    }

    async exec(
        commandLine: string,
        args?: string[],
        options?: RunnerExecOptions,
        filteredCmdArgs: string[] = [],
    ): Promise<number> {
        const silent = options?.silent ?? false;
        const silentDebugLogging = options?.silentDebugLogging ?? true;
        try {
            const commandString =
                (options?.isDirectAgentCommand ? "Copilot: " : "") +
                (args ? `${commandLine} ${args.join(" ")}` : commandLine);
            this.logger.startGroup(commandString, silent ? LogLevel.Debug : undefined);
            const result = await this._exec(commandLine, args, options, filteredCmdArgs);
            if (result.error) {
                throw result.error;
            }
            if (result.stdout) {
                if (silent) {
                    if (silentDebugLogging) {
                        this.logger.debug(result.stdout);
                    }
                } else {
                    this.logger.info(result.stdout);
                }
            }
            if (result.stderr) {
                this.logger.error(result.stderr);
            }
            return 0;
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } catch (error: any) {
            this.logger.error(error);
            throw error;
        } finally {
            this.logger.endGroup(silent ? LogLevel.Debug : undefined);
        }
    }

    async execReturn(
        commandLine: string,
        args?: string[],
        options?: RunnerExecOptions,
        filteredCmdArgs: string[] = [],
    ): Promise<RunnerExecOutput> {
        const silent = options?.silent ?? false;
        const silentDebugLogging = options?.silentDebugLogging ?? true;
        try {
            const commandString =
                (options?.isDirectAgentCommand ? "Copilot: " : "") +
                (args ? `${commandLine} ${args.join(" ")}` : commandLine);
            this.logger.startGroup(commandString, silent ? LogLevel.Debug : undefined);
            const result = await this._exec(commandLine, args, options, filteredCmdArgs);
            if (result.error) {
                throw result.error;
            }
            if (result.stdout) {
                if (silent) {
                    if (silentDebugLogging) {
                        this.logger.debug(result.stdout);
                    }
                } else {
                    this.logger.info(result.stdout);
                }
            }
            if (result.stderr) {
                this.logger.error(result.stderr);
            }
            return {
                exitCode: result.exitCode,
                stdout: result.stdout,
                stderr: result.stderr,
            };
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } catch (error: any) {
            this.logger.error(error);
            throw error;
        } finally {
            this.logger.endGroup(silent ? LogLevel.Debug : undefined);
        }
    }

    _exec(
        commandLine: string,
        args?: string[],
        options?: RunnerExecOptions,
        filteredCmdArgs: string[] = [],
    ): Promise<{
        error: ExecException | RunnerExecOutput | null;
        stdout: string;
        stderr: string;
        exitCode: number;
    }> {
        if (!options) {
            options = {};
        }

        if (!options.timeout) {
            // Default to 15 mins for timeout so we don't hang forever
            options.timeout = 900000;
        }

        if (!args) {
            args = [];
        }

        const opts: ExecOptions = {
            cwd: options.cwd,
            env: options.env,
            shell: options.shell,
            timeout: options.timeout,
        };

        return new Promise((resolve, reject) => {
            const process =
                options.shell && args?.length === 0
                    ? exec(commandLine, opts)
                    : options.shell
                      ? exec(`${commandLine} ${shellEscape(args)}`, opts)
                      : execFile(commandLine, args, opts);

            let stdout = "";
            let stderr = "";

            process.stdout?.on("data", (data: string) => {
                stdout += data;
            });

            process.stderr?.on("data", (data: string) => {
                stderr += data;
            });

            process.on("close", (code: number, signal: string) => {
                if (code !== 0 && !options?.ignoreReturnCode) {
                    const filteredCmd = this.filterCommand(commandLine, args, filteredCmdArgs);
                    const error: ExecException = new Error(`Command failed with exit code ${code}: ${filteredCmd}`);
                    error.cmd = filteredCmd;
                    error.code = code;
                    error.stdout = stdout;
                    error.stderr = stderr;
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    (error as any).signal = signal;
                    reject(error);
                } else {
                    resolve({ error: null, stdout, stderr, exitCode: code });
                }
            });

            process.on("error", (error: ExecException | ExecFileException) => {
                error.stdout = stdout;
                error.stderr = stderr;
                error.cmd = this.filterCommand(commandLine, args, filteredCmdArgs);
                reject(error);
            });

            if (options.input) {
                process.stdin?.write(options.input);
                process.stdin?.end();
            }
        });
    }

    private filterCommand(commandLine: string, args: string[] = [], filteredCmdArgs: string[]): string {
        const filteredArgs = args.reduce((acc, arg) => {
            if (!filteredCmdArgs.includes(arg)) {
                acc.push(arg);
            } else {
                acc.push("REDACTED"); // Mask filtered arguments
            }
            return acc;
        }, [] as string[]);
        return `${commandLine} ${filteredArgs.join(" ")}`;
    }
}
