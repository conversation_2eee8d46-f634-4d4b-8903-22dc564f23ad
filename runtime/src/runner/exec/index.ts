/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import * as stream from "stream";

export interface RunnerExecOptions {
    /** optional working directory.  defaults to current */
    cwd?: string;
    /** optional envvar dictionary.  defaults to current process's env */
    env?: {
        [key: string]: string;
    };
    /** optional.  defaults to false */
    silent?: boolean;
    /** optional out stream to use. Defaults to process.stdout */
    outStream?: stream.Writable;
    /** optional err stream to use. Defaults to process.stderr */
    errStream?: stream.Writable;
    /** optional. whether to skip quoting/escaping arguments if needed.  defaults to false. */
    windowsVerbatimArguments?: boolean;
    /** optional.  whether to fail if output to stderr.  defaults to false */
    failOnStdErr?: boolean;
    /** optional.  defaults to failing on non zero.  ignore will not fail leaving it up to the caller */
    ignoreReturnCode?: boolean;
    /** optional.  defaults to true.  If true, debug logging is enabled in silent mode */
    silentDebugLogging?: boolean;
    /** optional. How long in ms to wait for STDIO streams to close after the exit event of the process before terminating. defaults to 10000 */
    delay?: number;
    /** optional. input to write to the process on STDIN. */
    input?: Buffer;
    /** optional. Listeners for output. Callback functions that will be called on these events */
    listeners?: RunnerExecListeners;
    /** optional. In milliseconds the maximum amount of time the process is allowed to run. */
    timeout?: number | undefined;
    shell?: string | undefined;
    /** optional. Affects how the command is logged.  defaults to false.  if true, be prefixed by "Copilot:"" in Actions logs */
    isDirectAgentCommand?: boolean;
}

/**
 * Interface for the output of execReturn()
 */
export interface RunnerExecOutput {
    /**The exit code of the process */
    exitCode: number;
    /**The entire stdout of the process as a string */
    stdout: string;
    /**The entire stderr of the process as a string */
    stderr: string;
}

/**
 * The user defined listeners for an exec call
 */
export interface RunnerExecListeners {
    /** A call back for each buffer of stdout */
    stdout?: (data: Buffer) => void;
    /** A call back for each buffer of stderr */
    stderr?: (data: Buffer) => void;
    /** A call back for each line of stdout */
    stdline?: (data: string) => void;
    /** A call back for each line of stderr */
    errline?: (data: string) => void;
    /** A call back for each debug log */
    debug?: (data: string) => void;
}

export interface RunnerExec {
    /**
     * Exec a command.
     * Output will be streamed to the live console.
     * Returns promise with return code
     *
     * @param     commandLine        command to execute (can include additional args). Must be correctly escaped.
     * @param     args               optional arguments for tool. Escaping is handled by the lib.
     * @param     options            optional exec options.  See RunnerExecOptions
     * @returns   Promise<number>    exit code
     */
    exec(
        commandLine: string,
        args?: string[],
        options?: RunnerExecOptions,
        filteredCmdArgs?: string[],
    ): Promise<number>;

    /**
     * Exec a command and get the output.
     * Output will be streamed to the live console.
     * Returns promise with the exit code and collected stdout and stderr
     *
     * @param     commandLine           command to execute (can include additional args). Must be correctly escaped.
     * @param     args                  optional arguments for tool. Escaping is handled by the lib.
     * @param     options               optional exec options.  See RunnerExecOptions
     * @returns   Promise<RunnerExecOutput>   exit code, stdout, and stderr
     */
    execReturn(
        commandLine: string,
        args?: string[],
        options?: RunnerExecOptions,
        filteredCmdArgs?: string[],
    ): Promise<RunnerExecOutput>;
}
