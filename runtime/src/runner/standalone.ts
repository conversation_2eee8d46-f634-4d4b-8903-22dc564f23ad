/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { Runner, RunnerOptions, RunnerType } from ".";
import { DefaultProblemStatementDecoder, ProblemStatementDecoder } from "./decoder";
import { RunnerExec } from "./exec";
import { DefaultExec } from "./exec/default";
import { GitHandler } from "./git";
import { DefaultGitHandler } from "./git/default";
import { RunnerLogger } from "./logger";
import { ConsoleLogger } from "./logger/console";

/**
 * <PERSON>aloneRun<PERSON> is a runner that runs as a normal process (either directly on the host when running
 * `cpd` or in a container when launched via `sweagentd` in Docker during development).
 */
export class StandaloneRunner implements Runner {
    readonly type: RunnerType = "STANDALONE";

    readonly logger: RunnerLogger;
    readonly exec: RunnerExec;
    readonly git: GitHandler;
    readonly decoder: ProblemStatementDecoder;

    inputs = [];

    get sensitiveKeys(): string[] {
        return [];
    }

    constructor(options?: RunnerOptions) {
        this.logger = options?.logger || new ConsoleLogger();
        this.exec = options?.exec || new DefaultExec(this.logger);
        this.git = options?.gitHandler || new DefaultGitHandler(this.logger, this.exec);
        this.decoder = options?.decoder || new DefaultProblemStatementDecoder();
    }

    async prepareEnvironment() {
        // In standalone mode, all configuration is done via environment variables before the process is started, there's
        // nothing else extra to do.
    }
}
