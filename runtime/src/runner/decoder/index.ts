/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { unzipSync } from "zlib";

export interface ProblemStatementDecoder {
    decode(input: string): string;
}

export class DefaultProblemStatementDecoder implements ProblemStatementDecoder {
    decode(ps: string): string {
        if (ps.startsWith("gz:")) {
            const decoded = Buffer.from(ps.slice(3), "base64");
            const unzipped = unzipSync(decoded);
            return unzipped.toString("utf8");
        } else {
            // If the input is not gzipped, return it as is
            return Buffer.from(ps, "base64").toString("utf-8");
        }
    }
}
