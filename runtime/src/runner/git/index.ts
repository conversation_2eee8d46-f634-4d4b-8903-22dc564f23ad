/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { ExecException } from "child_process";
import { RuntimeSettings } from "../../settings";

export enum GitErrorType {
    HookError = "hook",
    PullFirst = "pull first",
    FetchFirst = "fetch first",
    LFSError = "LFS",
    RuleError = "protection rule",
    RebaseFailed = "rebase",
    DisconnectError = "disconnect",
    AccessDenied = "access denied",
    Unknown = "unknown",
}

/*
 * Custom error class for Git-related errors.
 * This class extends the built-in Error class and implements ExecException.
 * It provides additional properties to capture the type of Git error and whether it should be reported to Sentry.
 * It also includes logic to determine the type of Git error based on the error message.
 */
export class GitError extends Error implements ExecException {
    // Properties from ExecException
    cmd: string | undefined;
    killed: boolean | undefined;
    code: number | undefined;
    signal: NodeJS.Signals | undefined;
    stdout: string = "";
    stderr: string = "";

    // Specific type of Git error
    errorType: GitErrorType;

    // Indicates whether the error should be reported to Sentry in addition to being logged
    skipReport: boolean;

    constructor(err: ExecException | Error, errorType?: GitErrorType, skipReport = false) {
        // Determine the inner error type based on the error message - Git unfortunately does not return error codes we can use
        if (!errorType) {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            const exitCode = (err as any).code;
            let contentToParse = err.message;
            if ("stdout" in err && "stderr" in err) {
                contentToParse += "\n" + err.stderr?.trim() + "\n" + err.stdout?.trim();
            }
            if (
                contentToParse.includes("GH013: Repository rule violations found") ||
                contentToParse.includes("unknown revision or path not in the working tree")
            ) {
                errorType = GitErrorType.RuleError;
                skipReport = true;
            } else if (
                contentToParse.includes("You may want to try Git Large File Storage") ||
                contentToParse.includes("GH001: Large files detected")
            ) {
                errorType = GitErrorType.LFSError;
                skipReport = true;
            } else if (
                exitCode === 1 &&
                (contentToParse.includes("hook failed") ||
                    contentToParse.includes("husky -") ||
                    contentToParse.includes("hook declined") ||
                    contentToParse.includes("hook exited") ||
                    contentToParse.includes("vite ") ||
                    contentToParse.includes("eslint "))
            ) {
                errorType = GitErrorType.HookError;
                skipReport = true;
            } else if (contentToParse.includes("use 'git pull' before pushing again")) {
                errorType = GitErrorType.PullFirst;
                skipReport = true;
            } else if (contentToParse.includes("fetch first")) {
                errorType = GitErrorType.FetchFirst;
                skipReport = true;
            } else if (contentToParse.includes("The requested URL returned error: 403")) {
                errorType = GitErrorType.AccessDenied;
                skipReport = false;
            } else if (contentToParse.includes("unexpected disconnect")) {
                errorType = GitErrorType.DisconnectError;
                skipReport = false;
            } else {
                errorType = GitErrorType.Unknown;
                // Only report fatal errors or if git cannot be executed (and error code 127 is command not found for bash, 128 is a fatal git error)
                skipReport = !(
                    exitCode === "ENOENT" ||
                    exitCode === 127 ||
                    exitCode === 128 ||
                    contentToParse.includes("fatal: ")
                );
            }
        }
        super(`${errorType} git error: ${err.message}`);
        // Set other properties like message, stderr, stdout as needed
        for (const key of Object.keys(err)) {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            (this as any)[key] = (err as any)[key];
        }
        this.errorType = errorType ?? GitErrorType.Unknown;
        this.skipReport = skipReport;
        this.cause = err;
        // Set the prototype explicitly -  https://stackoverflow.com/questions/31626231/custom-error-class-in-typescript
        Object.setPrototypeOf(this, GitError.prototype);
        this.name = "GitError";
    }
}

export interface GitHandler {
    /**
     * Clone a repository.
     * @param settings The runtime settings.
     * @param repo The repository to clone.
     * @param repoLocation The location to clone the repository to.
     * @param branchName The name of the branch to clone.
     * @param commitCount The number of commits to fetch during cloning. Used for PR comment fix scenarios only. Defaults to 2.
     * @param baseCommit The base commit to use for the clone. Used for PR comment fix scenarios only.
     */
    cloneRepo: (
        settings: RuntimeSettings,
        repo: string,
        repoLocation: string,
        branchName: string,
        commitCount?: number,
        baseCommit?: string,
    ) => Promise<void>;

    /**
     * Commit changes to the repository without pushing.
     * @param settings The runtime settings.
     * @param branchName The name of the branch to commit to.
     * @param repoLocation The location of the repository.
     * @param commitMessage The commit message.
     * @param allowEmptyCommit Whether to allow empty commits.
     * @returns A promise indicating the success of the operation.
     */
    commitChanges: (
        settings: RuntimeSettings,
        branchName: string,
        repoLocation: string,
        commitMessage: string,
        allowEmptyCommit?: boolean,
    ) => Promise<string>;

    /**
     * Commit and push changes to the repository.
     * @param settings The runtime settings.
     * @param branchName The name of the branch to commit to.
     * @param repoLocation The location of the repository.
     * @param commitMessage The commit message.
     * @param allowEmptyCommit Whether to allow empty commits.
     * @param noVerify Whether to skip pre-commit/push hooks.
     * @returns A promise indicating the success of the operation.
     */
    commitAndPushChanges: (
        settings: RuntimeSettings,
        branchName: string,
        repoLocation: string,
        commitMessage: string,
        allowEmptyCommit?: boolean,
        noVerify?: boolean,
    ) => Promise<string>;

    /**
     * Get the diff of a branch.
     * @param repoLocation The location of the repository.
     * @param prettyPrint Whether to pretty print the diff.
     * @returns A promise containing the diff.
     */
    diff: (repoLocation: string, prettyPrint?: boolean) => Promise<string>;

    /**
     * Get the most recent commits in a repository.
     * @param repoLocation The location of the repository.
     * @param count The number of commits to retrieve.
     * @returns A promise containing the list of commit hashes.
     */
    getMostRecentCommits: (repoLocation: string, count: number) => Promise<string[]>;

    /**
     * Get the paths of files that have been changed between two commits.
     * @param repoLocation The location of the repository.
     * @param moreRecentCommit The more recent commit hash.
     * @param baseCommit The base commit hash to compare against.
     * @returns A promise containing the list of changed file paths.
     */
    getChangedPaths: (repoLocation: string, moreRecentCommit: string, baseCommit: string) => Promise<string[]>;
}
