/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { ExecException } from "child_process";
import { randomUUID } from "crypto";
import { cpSync, existsSync, mkdtempSync, rmSync } from "fs";
import { homedir, tmpdir } from "os";
import { join as pathJoin } from "path";
import { GitError, GitErrorType, GitHandler } from ".";
import { RuntimeSettings } from "../../settings";
import {
    demandGitHubHost,
    demandGitHubHostProtocol,
    demandGitHubServerUrl,
    demandGitHubToken,
    demandGitHubUserEmail,
    demandGitHubUserName,
    getGithubActorInfo,
} from "../../settings/demand";
import { RunnerExec, RunnerExecOptions, RunnerExecOutput } from "../exec";
import { RunnerLogger } from "../logger";

// Note: GITHUB_TOKEN here is intentionally pulled from the local env for security reasons. Do not update the line below to use process.env.
export const bashCredHelper = '!f() { test "$1" = get && echo "password=$GITHUB_TOKEN"; }; f';

export class DefaultGitHandler implements GitHandler {
    constructor(
        private logger: RunnerLogger,
        private exec: RunnerExec,
    ) {}

    private resolveGitUrl(settings: RuntimeSettings, repo?: string): URL {
        let url: URL | null = null;
        let urlString = "";
        try {
            urlString = demandGitHubServerUrl(settings);
            url = new URL(urlString);
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } catch (e: any) {
            // At this point, these fallback paths should not happen. However, we've seen certain Actions hosts
            // have either an unparseable server URL or an empty one. This adds more visibility to when this happens
            // and provides a final fallback to github.com, since this is the only scenario we've seen it.

            // TODO: We should remove this eventually and only use GITHUB_SERVER_URL.
            this.logger.warning(
                `Failed to parse GITHUB_SERVER_URL "${urlString}". Falling back to GITHUB_HOST and GITHUB_GIT_HOST_PROTOCOL. Error: ${e.message ?? e}`,
            );
            const githubHostProtocol = demandGitHubHostProtocol(settings);
            const githubHost = demandGitHubHost(settings);
            try {
                url = URL.parse(`${githubHostProtocol}://${githubHost}`);
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
            } catch (e: any) {
                this.logger.warning(
                    `Unable to determine GITHUB_SERVER_URL. Falling back to "https://github.com". Error: ${e.message ?? e}`,
                );
                url = URL.parse("https://github.com");
            }
        }

        // Should never happen but better to check than use url!
        if (!url) throw new Error("Could not determine the Git URL");

        if (repo) url.pathname = repo;

        this.logger.info(`Using Git URL: ${url.href}`);

        return url;
    }

    async cloneRepo(
        settings: RuntimeSettings,
        repo: string,
        repoLocation: string,
        branchName: string,
        commitCount?: number,
        baseCommit?: string,
    ) {
        // Ensure GITHUB_TOKEN is set for the process (and git subprocesses) is set so git credential helper can use it
        process.env.GITHUB_TOKEN = process.env.GITHUB_TOKEN ?? demandGitHubToken(settings);

        const remoteUrl = this.resolveGitUrl(settings, repo);
        const uriString = remoteUrl.href;

        // We want to increase the commit count by 1 to get the commit right before the PR, so the LLM can completely restart if prompted.
        // Use a minimum of depth 2 rather than 1 since we expect push. See  https://stackoverflow.com/questions/66431436/pushing-to-github-after-a-shallow-clone-is-horribly-slow
        commitCount = commitCount ?? 1;
        commitCount += 1;
        if (commitCount < 2) {
            commitCount = 2;
        }

        // If repository location already exists and has a .git folder, we're in a test scenario and the repo is
        // already set up. However, we still need to make sure the specified branch exists and is up to date.
        const alreadyCloned = existsSync(pathJoin(repoLocation, ".git"));
        if (alreadyCloned) {
            const execOptions: RunnerExecOptions = {
                cwd: repoLocation,
                silent: true,
            };
            this.logger.debug(`Repo ${repo} already cloned to ${repoLocation}`);

            // Since Actions setup steps can result in a cloned repo, we need to be sure to set
            // the credential helper in this situation. This is CRITICAL to make sure happens.
            this.logger.debug(`Configuring git credential helper for ${repo}`);
            await this.setGitConfig(settings, execOptions, false);
            // Reset remote origin to be certain does not have creds in it and is HTTP
            await this.execGit(["remote", "set-url", "origin", uriString], execOptions, [uriString]);

            // Check if we're already on the right branch just return if so
            const currentBranch = (await this.execGit(["rev-parse", "--abbrev-ref", "HEAD"], execOptions)).stdout
                .toString()
                .trim();
            if (currentBranch === branchName) {
                this.logger.debug(`Already on branch ${branchName} in repo ${repo}`);
                return;
            }

            // Check if branch is local and check out if so
            const localBranchExists =
                (await this.execGit(["branch", "--list", branchName], execOptions, [branchName])).stdout
                    .toString()
                    .trim().length > 0;
            if (localBranchExists) {
                this.logger.debug(`Local branch ${branchName} already exists in repo ${repo}`);
                this.logger.debug(`Checking out branch ${branchName}`);
                await this.execGit(["checkout", branchName], execOptions, [branchName]);
                return;
            }

            if (await this.hasRemoteBranch(branchName, repoLocation)) {
                this.logger.debug(`Remote branch ${branchName} exists in repo ${repo}`);
                // Undo a shallow clone if needed
                const { stdout } = await this.execGit(["rev-parse", "--is-shallow-repository"], execOptions);
                if (stdout.trim() === "true") {
                    await this.execGit(
                        ["fetch", "--unshallow", "--update-head-ok", "origin", "+refs/heads/*:refs/heads/*"],
                        execOptions,
                    );
                } else {
                    await this.execGit(["fetch", "origin"], execOptions);
                }
                this.logger.debug(`Pulling branch ${branchName} with depth ${commitCount}`);
                await this.execGit(["pull", "--depth", commitCount.toString(), "origin", branchName], execOptions, [
                    commitCount.toString(),
                    branchName,
                ]);
                this.logger.debug(`Checking out branch ${branchName}`);
                await this.execGit(["checkout", branchName], execOptions, [branchName]);
                return;
            }

            if (baseCommit) {
                this.logger.debug(`Base commit ${baseCommit} provided for branch ${branchName} in repo ${repo}`);
                this.logger.debug(`Checking out branch ${branchName}`);
                await this.execGit(["checkout", "-b", branchName, baseCommit], execOptions, [branchName, baseCommit]);
                return;
            }

            throw new Error(`Branch ${branchName} does not exist in repo ${repo} and no base commit was provided.`);
        }

        // Otherwise, try to clone the repo for using an existing remote branch if it exists, otherwise create one. This second
        // scenario is only for testing. This approach avoids having the main branch pulled down at all by default.
        this.logger.debug(`Repo ${repo} not cloned to ${repoLocation} yet`);

        // Git requires $HOME to be set, so handle scenarios where it is missing like if the Actions runner was started by init.d or systemd
        // However, in our case, we actually don't want global config sticking around at all so we'll set up a temp home for the global config
        const realHome = process.env.HOME || homedir();
        const tempHome = mkdtempSync(pathJoin(tmpdir(), `cpd-${randomUUID()}`));
        if (existsSync(pathJoin(realHome, ".gitconfig"))) {
            cpSync(pathJoin(realHome, ".gitconfig"), pathJoin(tempHome, ".gitconfig"));
        }
        const gitEnv = { ...process.env, HOME: tempHome };
        const tmpGlobalExecOptions: RunnerExecOptions = {
            silent: true,
            env: gitEnv,
        };
        const execOptions: RunnerExecOptions = {
            cwd: repoLocation,
            silent: true,
            env: gitEnv,
        };

        await this.setGitConfig(settings, tmpGlobalExecOptions, true);

        try {
            this.logger.debug(
                `Cloning repo ${repo} to ${repoLocation} with branch ${branchName} and depth ${commitCount}`,
            );
            await this.execGit(
                [
                    "clone",
                    "-b",
                    branchName,
                    "--single-branch",
                    "--depth",
                    commitCount.toString(),
                    uriString,
                    repoLocation,
                ],
                tmpGlobalExecOptions,
                [uriString, repoLocation, branchName, commitCount.toString()],
            );
        } catch (error) {
            this.logger.debug(`Error cloning repo ${repo} to ${repoLocation}: ${error}`);
            if (baseCommit) {
                this.logger.debug(`Base commit ${baseCommit} provided. Checking out branch ${branchName}`);
                // Have to do a full clone in this scenario. Not as efficient, but only for testing.
                await this.execGit(["clone", uriString, repoLocation], tmpGlobalExecOptions, [uriString, repoLocation]);
                await this.execGit(["checkout", "-b", branchName, baseCommit], execOptions, [branchName, baseCommit]);
            } else {
                throw new Error(`Branch ${branchName} does not exist in repo ${repo} and no base commit was provided.`);
            }
        } finally {
            await this.cleanGitConfig(tmpGlobalExecOptions, true, true);
        }

        // Set local config for the repo
        await this.setGitConfig(settings, execOptions, false);
    }

    async commitChanges(
        settings: RuntimeSettings,
        branchName: string,
        repoLocation: string,
        commitMessage: string,
        allowEmptyCommit: boolean = false,
        noVerify: boolean = false,
    ): Promise<string> {
        // Git requires $HOME to be set, so handle scenarios where it is missing like if the Actions runner was started by init.d or systemd
        if (!process.env.HOME) {
            process.env.HOME = homedir();
        }

        const execOptions: RunnerExecOptions = {
            cwd: repoLocation,
            silent: true,
        };

        // Get actor info to include as co-author
        const actorInfo = getGithubActorInfo(settings);
        if (actorInfo) {
            // Format co-author line
            // GitHub format: Co-authored-by: username <<EMAIL>>
            const coAuthorLine = `Co-authored-by: ${actorInfo.login} <${actorInfo.id}+${actorInfo.login}@users.noreply.github.com>`;

            // Ensure commit message ends with newline, add blank line and co-author
            commitMessage = commitMessage.trim() + "\n\n" + coAuthorLine;
        }

        // Make sure we are in the right branch and the agent didn't change it for security reasons
        await this.execGit(["checkout", branchName], execOptions, [branchName]);
        const needsCommit = await this.shouldCommit(allowEmptyCommit, repoLocation);
        // Commit changes to the repo only if there are actual changes
        let outputLog = "";
        if (needsCommit) {
            this.logger.debug(`Committing to branch ${branchName}`);
            const addResult = await this.execGit(["add", ".", "-v"], execOptions);
            outputLog += `$ git add . -v\n${addResult.stdout}${addResult.stderr}\n`;
            const commitArgs = allowEmptyCommit
                ? ["commit", "--allow-empty", "-m", commitMessage]
                : ["commit", "-m", commitMessage];
            if (noVerify) {
                commitArgs.push("--no-verify");
            }
            const commitResult = await this.execGit(commitArgs, execOptions, [commitMessage]);
            outputLog += `$ git ${commitArgs.join(" ")}\n${commitResult.stdout}${commitResult.stderr}\n`;
        }

        // Executed to get log output for debugging
        if (this.logger.isDebug()) {
            await this.execGit(["log", "-n", "3"], execOptions);
            await this.execGit(["status"], execOptions);
        }

        return outputLog;
    }

    async commitAndPushChanges(
        settings: RuntimeSettings,
        branchName: string,
        repoLocation: string,
        commitMessage: string,
        allowEmptyCommit: boolean = false,
        noVerify: boolean = false,
    ): Promise<string> {
        // Ensure GITHUB_TOKEN is set for the process (and git subprocesses) is set so git credential helper can use it
        process.env.GITHUB_TOKEN = process.env.GITHUB_TOKEN ?? demandGitHubToken(settings);
        const execOptions: RunnerExecOptions = {
            cwd: repoLocation,
            silent: true,
        };

        let outputLog = await this.commitChanges(
            settings,
            branchName,
            repoLocation,
            commitMessage,
            allowEmptyCommit,
            noVerify,
        );

        const pushCommand = ["push", "-v", "origin", branchName];
        if (noVerify) {
            pushCommand.push("--no-verify");
        }

        // Push no matter what just in case the agent actually committed itself
        try {
            this.logger.debug(`Pushing to origin branch ${branchName}`);
            const pushResult = await this.execGit(pushCommand, execOptions, [branchName]);
            outputLog += `$ git ${pushCommand.join(" ")}\n${pushResult.stdout}${pushResult.stderr}\n`;
        } catch (error) {
            if (
                error instanceof GitError &&
                (error.errorType === GitErrorType.FetchFirst || error.errorType === GitErrorType.PullFirst)
            ) {
                this.logger.debug("Push failed due to remote changes. Fetching and rebasing...");
                outputLog += `Push failed due to remote changes. Fetching and rebasing...\n`;
                const fetchCommand = ["fetch", "origin", branchName];
                const fetchResult = await this.execGit(fetchCommand, execOptions, [branchName]);
                outputLog += `$ git ${fetchCommand.join(" ")}\n${fetchResult.stdout}${fetchResult.stderr}\n`;

                try {
                    this.logger.debug(`Rebasing local changes on top of remote changes`);
                    // Try to rebase our changes on top of the remote changes
                    const rebaseCommand = ["rebase", `origin/${branchName}`];
                    if (noVerify) {
                        rebaseCommand.push("--no-verify");
                    }
                    const rebaseResult = await this.execGit(rebaseCommand, execOptions, [branchName]);
                    outputLog += `$ git ${rebaseCommand.join(" ")}\n${rebaseResult.stdout}${rebaseResult.stderr}\n`;

                    // Try pushing again after rebase
                    const pushResult = await this.execGit(pushCommand, execOptions, [branchName]);
                    outputLog += `$ git ${pushCommand.join(" ")}\n${pushResult.stdout}${pushResult.stderr}\n`;
                } catch (rebaseError) {
                    this.logger.info(`Rebase failed with error: ${rebaseError}`);
                    // If rebase fails, abort it
                    await this.execGit(["rebase", "--abort"], execOptions).catch(() => {});
                    const rebaseFailedError = new GitError(
                        rebaseError as ExecException,
                        GitErrorType.RebaseFailed,
                        true,
                    );
                    rebaseFailedError.cause = error;
                    throw rebaseFailedError;
                }
            } else {
                throw error;
            }
        }

        // Executed to get log output for debugging
        if (this.logger.isDebug()) {
            await this.execGit(["log", "-n", "3"], execOptions);
            await this.execGit(["status"], execOptions);
        }

        return outputLog;
    }

    async stageChanges(repoLocation: string): Promise<string> {
        return (await this.execGit(["add", "."], { cwd: repoLocation })).stdout.toString();
    }

    async diff(repoLocation: string, stagedChanges?: boolean): Promise<string> {
        const args = stagedChanges ? ["diff", "--cached"] : ["diff"];
        return (await this.execGit(args, { cwd: repoLocation })).stdout.toString();
    }

    async getCurrentCommitHash(repoLocation: string): Promise<string> {
        return (await this.execGit(["rev-parse", "HEAD"], { cwd: repoLocation })).stdout.toString();
    }

    async diffCommits(repoLocation: string, moreRecentCommit: string, baseCommit: string): Promise<string> {
        return (
            await this.execGit(["diff", moreRecentCommit.trim(), baseCommit.trim()], { cwd: repoLocation }, [
                moreRecentCommit.trim(),
                baseCommit.trim(),
            ])
        ).stdout.toString();
    }

    async getChangedPaths(repoLocation: string, moreRecentCommit: string, baseCommit: string): Promise<string[]> {
        const result = await this.execGit(
            ["diff", "--name-only", moreRecentCommit.trim(), baseCommit.trim()],
            { cwd: repoLocation },
            [moreRecentCommit.trim(), baseCommit.trim()],
        );
        return result.stdout
            .toString()
            .split("\n")
            .filter((path) => path.trim().length > 0);
    }

    async getMostRecentCommits(repoLocation: string, count: number): Promise<string[]> {
        return (
            await this.execGit(["log", '--pretty=format:"%s"', `-n`, `${count}`], { cwd: repoLocation }, [
                count.toString(),
            ])
        ).stdout
            .toString()
            .split("\n");
    }

    async setGitConfig(settings: RuntimeSettings, execOptions: RunnerExecOptions, global: boolean = false) {
        const commitLogin = demandGitHubUserName(settings);
        const commitEmail = demandGitHubUserEmail(settings);
        await this.cleanGitConfig(execOptions, global);
        this.logger.debug(`Setting ${global ? "global" : "local"} git config credential.username to ${commitLogin}`);
        await this.execGit(
            ["config", global ? "--global" : "--local", `credential.username`, commitLogin],
            execOptions,
            [commitLogin],
        );
        this.logger.debug(`Setting ${global ? "global" : "local"} git config credential.helper to ${bashCredHelper}`);
        await this.execGit(
            ["config", global ? "--global" : "--local", `credential.helper`, bashCredHelper],
            execOptions,
            [bashCredHelper],
        );
        this.logger.debug(`Setting ${global ? "global" : "local"} git config user.email to ${commitEmail}`);
        await this.execGit(["config", global ? "--global" : "--local", "user.email", commitEmail], execOptions, [
            commitEmail,
        ]);
        this.logger.debug(`Setting ${global ? "global" : "local"} git config user.name to ${commitLogin}`);
        await this.execGit(["config", global ? "--global" : "--local", "user.name", commitLogin], execOptions, [
            commitLogin,
        ]);
        this.logger.debug(`Setting ${global ? "global" : "local"} git config pull.rebase to false`);
        await this.execGit(["config", global ? "--global" : "--local", "pull.rebase", "false"], execOptions);
    }

    async cleanGitConfig(execOptions: RunnerExecOptions, global: boolean = false, rmHome: boolean = false) {
        this.logger.debug(`Cleaning ${global ? "global" : "local"} git config`);
        await this.clearGitConfigVal(execOptions, "credential.helper", global);
        await this.clearGitConfigVal(execOptions, "credential.username", global);
        await this.clearGitConfigVal(execOptions, "user.name", global);
        await this.clearGitConfigVal(execOptions, "user.email", global);
        await this.clearGitConfigVal(execOptions, "pull.rebase", global);
        if (rmHome && execOptions?.env?.HOME) {
            this.logger.debug(`Cleaning temporary home directory: ${execOptions.env.HOME}`);
            rmSync(execOptions.env.HOME, { recursive: true });
        }
    }

    async clearGitConfigVal(execOptions: RunnerExecOptions, name: string, global: boolean = false) {
        // In this case, we just need a 0 (found) or 1 (not found) exit code, so don't throw an exception or debug log
        const opts: RunnerExecOptions = {
            ...execOptions,
            ignoreReturnCode: true,
            failOnStdErr: false,
            silentDebugLogging: false,
        };
        const { exitCode } = await this.execGit(
            ["config", "get", global ? "--global" : "--local", "--all", name],
            opts,
        );
        if (exitCode === 0) {
            await this.execGit(["config", "unset", global ? "--global" : "--local", "--all", name], execOptions);
        }
    }

    async execGit(
        gitArgs: string[],
        execOptions: RunnerExecOptions,
        filteredCmdArgs: string[] = [],
    ): Promise<RunnerExecOutput> {
        try {
            return await this.exec.execReturn("git", gitArgs, execOptions, filteredCmdArgs);
        } catch (error) {
            throw new GitError(error as ExecException);
        }
    }

    async shouldCommit(allowEmptyCommit: boolean, repoLocation: string): Promise<boolean> {
        try {
            return (
                allowEmptyCommit ||
                (
                    await this.exec.execReturn("git status --porcelain | head -n 10", [], {
                        cwd: repoLocation,
                        shell: "sh",
                        silent: true,
                    })
                ).stdout
                    .toString()
                    .trim().length > 0
            );
        } catch (error) {
            throw new GitError(error as ExecException);
        }
    }
    async hasRemoteBranch(branchName: string, repoLocation: string): Promise<boolean> {
        try {
            const result = await this.execGit(
                ["ls-remote", "--heads", "origin", branchName],
                { cwd: repoLocation, silent: true },
                [branchName],
            );
            return result.stdout.toString().trim().length > 0;
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } catch (err: any) {
            // Git will return error code 128 if the branch does not exist, and generally if the command fails we should exit
            if (err.code ?? 0 != 0) {
                return false;
            }
            throw err;
        }
    }
}
