/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { Git<PERSON><PERSON><PERSON> } from ".";
import { RuntimeSettings } from "../../settings";

type GitHandlerCall =
    | [
          "cloneRepo",
          {
              settings: RuntimeSettings;
              repo: string;
              repoLocation: string;
              branchName: string;
              commitCount?: number;
              baseCommit?: string;
          },
      ]
    | [
          "commitChanges",
          {
              settings: RuntimeSettings;
              branchName: string;
              repoLocation: string;
              commitMessage: string;
              allowEmptyCommit?: boolean;
          },
      ]
    | [
          "commitAndPushChanges",
          {
              settings: RuntimeSettings;
              branchName: string;
              repoLocation: string;
              commitMessage: string;
              allowEmptyCommit?: boolean;
              noVerify?: boolean;
          },
      ]
    | [
          "diff",
          {
              repoLocation: string;
              prettyPrint?: boolean;
          },
      ]
    | [
          "getMostRecentCommits",
          {
              repoLocation: string;
              count: number;
          },
      ]
    | [
          "getChangedPaths",
          {
              repoLocation: string;
              moreRecentCommit: string;
              baseCommit: string;
          },
      ];

export type { GitHandlerCall };

export class NoopGitHandler implements GitHandler {
    calls: GitHandlerCall[] = [];

    cloneRepo = async (
        settings: RuntimeSettings,
        repo: string,
        repoLocation: string,
        branchName: string,
        commitCount?: number,
        baseCommit?: string,
    ): Promise<void> => {
        this.calls.push([
            "cloneRepo",
            {
                settings,
                repo,
                repoLocation,
                branchName,
                commitCount,
                baseCommit,
            },
        ]);
        return;
    };

    commitChanges = async (
        settings: RuntimeSettings,
        branchName: string,
        repoLocation: string,
        commitMessage: string,
        allowEmptyCommit?: boolean,
    ): Promise<string> => {
        this.calls.push([
            "commitChanges",
            {
                settings,
                branchName,
                repoLocation,
                commitMessage,
                allowEmptyCommit,
            },
        ]);
        return "output log goes here";
    };

    commitAndPushChanges = async (
        settings: RuntimeSettings,
        branchName: string,
        repoLocation: string,
        commitMessage: string,
        allowEmptyCommit?: boolean,
        noVerify?: boolean,
    ): Promise<string> => {
        this.calls.push([
            "commitAndPushChanges",
            {
                settings,
                branchName,
                repoLocation,
                commitMessage,
                allowEmptyCommit,
                noVerify,
            },
        ]);
        return "output log goes here";
    };

    diff = async (repoLocation: string, prettyPrint?: boolean): Promise<string> => {
        this.calls.push(["diff", { repoLocation, prettyPrint }]);
        return "diff-placeholder"; // Placeholder for diff output
    };

    getMostRecentCommits = async (repoLocation: string, count: number): Promise<string[]> => {
        this.calls.push(["getMostRecentCommits", { repoLocation, count }]);
        return ["commit-hash-placeholder"]; // Placeholder for commit hashes
    };

    getChangedPaths = async (repoLocation: string, moreRecentCommit: string, baseCommit: string): Promise<string[]> => {
        this.calls.push(["getChangedPaths", { repoLocation, moreRecentCommit, baseCommit }]);
        return ["path/to/changed-file.txt"]; // Placeholder for changed file paths
    };
}
