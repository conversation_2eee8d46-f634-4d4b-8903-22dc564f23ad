/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { ActionsRunner } from "./actions";
import { ProblemStatementDecoder } from "./decoder";
import { RunnerExec } from "./exec";
import { GitHandler } from "./git";
import { RunnerLogger } from "./logger";
import { StandaloneRunner } from "./standalone";
export { LogLevel, RunnerLogger } from "./logger";

export type RunnerType = "GITHUB_ACTIONS" | "STANDALONE";

export type Inputs = Array<{
    name: string;
    required: boolean;
    secret?: boolean;
}>;

/**
 * The runner is the environment in which the agent is running. Some logic may be
 * specific to the runner environment, so we expose this as a global variable.
 */
export interface Runner {
    /**
     * The runner environment in which the agent is running.
     */
    type: RunnerType;

    /**
     * Allows a runner to inject any environment variables it needs to run before we start processing work. This can be used to
     * set up process.env with configuration data in cases where this data is passed out of band (e.g. in GitHub Actions we pass a map of
     * in a special way when launching a dynamic job instead of setting them in the environment on the job when we launch it)
     */
    prepareEnvironment(): Promise<void>;

    exec: RunnerExec;

    git: GitHandler;

    logger: RunnerLogger;

    decoder: ProblemStatementDecoder;

    get sensitiveKeys(): string[];
}

export interface RunnerOptions {
    exec?: RunnerExec;
    logger?: RunnerLogger;
    gitHandler?: GitHandler;
    decoder?: ProblemStatementDecoder;
}

/**
 * Get the runner environment in which the agent is running.
 */
export function getRunnerType(): RunnerType {
    // Needed for CI testing to override the runner type
    if (process.env.COPILOT_AGENT_RUNNER_TYPE) return process.env.COPILOT_AGENT_RUNNER_TYPE as RunnerType;

    if (process.env.GITHUB_ACTIONS) return "GITHUB_ACTIONS";

    return "STANDALONE";
}

/**
 * Get the runner environment in which the agent is running.
 */
export function getRunner(options?: RunnerOptions): Runner {
    const runnerType = getRunnerType();
    switch (runnerType) {
        case "GITHUB_ACTIONS":
            return new ActionsRunner(options);
        case "STANDALONE":
            return new StandaloneRunner(options);
        default:
            throw new Error(`Unknown runner type: ${runnerType}`);
    }
}
