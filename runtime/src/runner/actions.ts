/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import * as actions from "@actions/core";
import { existsSync } from "fs";
import { readFile } from "fs/promises";
import { EOL } from "os";
import { secretEnvVarNames } from "../settings/environment-settings";
import { Inputs, Runner, RunnerOptions, RunnerType } from "./";
import { DefaultProblemStatementDecoder, ProblemStatementDecoder } from "./decoder";
import { RunnerExec } from "./exec";
import { DefaultExec } from "./exec/default";
import { GitHandler } from "./git";
import { DefaultGitHandler } from "./git/default";
import { RunnerLogger } from "./logger";
import { ConsoleLogger } from "./logger/console";

/**
 * GitHub Actions runner.
 */
export class ActionsRunner implements Runner {
    readonly type: RunnerType = "GITHUB_ACTIONS";

    readonly logger: RunnerLogger;
    readonly exec: RunnerExec;
    readonly git: GitHandler;
    readonly decoder: ProblemStatementDecoder;

    // A list of secrets that are set in the environment and should be masked
    // in the output.
    secrets = ["GITHUB_COPILOT_API_TOKEN", "GITHUB_COPILOT_INTEGRATION_ID"];

    inputs: Inputs = [
        { name: "COPILOT_API_URL", required: true },
        { name: "COPILOT_AGENT_ACTION", required: true },
        { name: "COPILOT_AGENT_ACTOR", required: false },
        { name: "COPILOT_AGENT_ACTOR_ID", required: false },
        { name: "COPILOT_AGENT_BASE_COMMIT", required: true },
        { name: "COPILOT_AGENT_BRANCH_NAME", required: true },
        { name: "COPILOT_AGENT_CALLBACK_URL", required: true },
        { name: "COPILOT_AGENT_COMMIT_LOGIN", required: true },
        { name: "COPILOT_AGENT_COMMIT_EMAIL", required: true },
        { name: "COPILOT_AGENT_ISSUE_NUMBER", required: true },
        { name: "COPILOT_AGENT_JOB_ID", required: true },
        { name: "COPILOT_AGENT_MODEL", required: false },
        { name: "COPILOT_AGENT_PR_NUMBER", required: false },
        { name: "COPILOT_AGENT_PROMPT", required: true },
        { name: "COPILOT_AGENT_PUSH", required: false },
        { name: "COPILOT_AGENT_PR_COMMIT_COUNT", required: false },
        { name: "COPILOT_AGENT_CONTENT_FILTER_MODE", required: false },
        { name: "COPILOT_AGENT_SESSION_ID", required: false },
        { name: "COPILOT_USE_SESSIONS", required: false },
        { name: "COPILOT_JOB_NONCE", required: true, secret: true },
    ];

    get sensitiveKeys(): string[] {
        const injectedSecretNames = (process.env.COPILOT_AGENT_INJECTED_SECRET_NAMES ?? "")
            .split(",")
            .map((name) => name.trim())
            .filter((name) => name.length > 0)
            .filter((name) => name in process.env);

        const sensitiveKeys = [
            // Any known secrets from the runner
            ...this.secrets,
            // Any inputs that are marked as secret
            ...this.inputs.filter((input) => input.secret).map((input) => input.name),
            // Any known secrets that are in the environment
            ...secretEnvVarNames,
            // Any user-provided secrets that are set in the copilot environment
            ...injectedSecretNames,
        ];

        // Remove duplicates
        return [...new Set(sensitiveKeys)];
    }

    constructor(options?: RunnerOptions) {
        this.logger = options?.logger || new ConsoleLogger();
        this.exec = options?.exec || new DefaultExec(this.logger);
        this.git = options?.gitHandler || new DefaultGitHandler(this.logger, this.exec);
        this.decoder = options?.decoder || new DefaultProblemStatementDecoder();
    }

    async prepareEnvironment(): Promise<void> {
        const eventName = process.env.GITHUB_EVENT_NAME;
        if (!eventName) throw new Error("GITHUB_EVENT_NAME not found in environment");

        // We only support the 'dynamic' event
        if (eventName !== "dynamic") throw new Error(`Unsupported event: ${eventName}`);
        // TODO: We should support other events (only) for testing

        // Allow developers to set these values as copilot environment secrets to override the main values
        if (process.env["COPILOT_INTEGRATION_ID_OVERRIDE"]) {
            process.env["GITHUB_COPILOT_INTEGRATION_ID"] = process.env["COPILOT_INTEGRATION_ID_OVERRIDE"];
            delete process.env.COPILOT_INTEGRATION_ID_OVERRIDE;
        }
        if (process.env["CAPI_HMAC_KEY_OVERRIDE"]) {
            process.env["CAPI_HMAC_KEY"] = process.env["CAPI_HMAC_KEY_OVERRIDE"];
            delete process.env.CAPI_HMAC_KEY_OVERRIDE;
        }

        // Get secrets from the environment and tell the runner to mask their values in output
        for (const secret of this.secrets) {
            const val = process.env[secret];
            if (!val) throw new Error(`Secret ${secret} not found in environment`);

            // Skip the secret if it's the integration id because it causes actions to replace all
            // instances of 'copilot-developer' with '***'
            if (secret !== "GITHUB_COPILOT_INTEGRATION_ID") {
                actions.setSecret(val);
            }
        }

        // Get the event json and extract the inputs
        let event: {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            [key: string]: any;
            inputs: { [key: string]: string };
        } = { inputs: {} };

        if (process.env.GITHUB_EVENT_PATH) {
            if (existsSync(process.env.GITHUB_EVENT_PATH)) {
                event = JSON.parse(
                    await readFile(process.env.GITHUB_EVENT_PATH, {
                        encoding: "utf8",
                    }),
                );
            } else {
                const path = process.env.GITHUB_EVENT_PATH;
                this.logger.warning(`GITHUB_EVENT_PATH ${path} does not exist${EOL}`);
            }
        }

        if (!event.inputs) throw new Error("Inputs not found in event");
        const inputJson = event.inputs["COPILOT_AGENT_INPUTS"];
        const agentInputs = JSON.parse(inputJson);

        // Set the inputs as environment variables
        for (const input of this.inputs) {
            const val = agentInputs[input.name] || "";
            if (input.required && !val) throw new Error(`Input ${input.name} not found in inputs`);
            if (input.secret) actions.setSecret(val);
            actions.exportVariable(input.name, val);
        }
    }
}
