/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { BaseLogger, LogLevel, RunnerLogger } from ".";

export class NoopLogger extends BaseLogger implements RunnerLogger {
    constructor() {
        super();
    }

    debug(_message: string): void {
        // no-op
    }

    log(_message: string): void {
        // no-op
    }

    info(_message: string): void {
        // no-op
    }

    notice(_message: string | Error): void {
        // no-op
    }

    warning(_message: string | Error): void {
        // no-op
    }

    error(_message: string | Error): void {
        // no-op
    }

    startGroup(_name: string, _level?: LogLevel): void {
        // no-op
    }

    endGroup(_level?: LogLevel): void {
        // no-op
    }
}
