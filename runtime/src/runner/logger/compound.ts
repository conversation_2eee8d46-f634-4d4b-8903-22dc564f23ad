/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { LogLevel, RunnerLogger } from ".";

export class CompoundLogger implements RunnerLogger {
    public readonly loggers: RunnerLogger[];

    constructor(loggers: RunnerLogger[]) {
        this.loggers = loggers;
    }

    isDebug(): boolean {
        return this.loggers.some((l) => l.isDebug());
    }

    debug(message: string): void {
        this.loggers.forEach((l) => l.debug(message));
    }

    log(message: string): void {
        this.loggers.forEach((l) => l.log(message));
    }

    info(message: string): void {
        this.loggers.forEach((l) => l.info(message));
    }

    notice(message: string | Error): void {
        this.loggers.forEach((l) => l.notice(message));
    }

    warning(message: string | Error): void {
        this.loggers.forEach((l) => l.warning(message));
    }

    error(message: string | Error): void {
        this.loggers.forEach((l) => l.error(message));
    }

    startGroup(name: string, level?: LogLevel): void {
        this.loggers.forEach((l) => l.startGroup(name, level));
    }

    endGroup(level?: LogLevel): void {
        this.loggers.forEach((l) => l.endGroup(level));
    }
}
