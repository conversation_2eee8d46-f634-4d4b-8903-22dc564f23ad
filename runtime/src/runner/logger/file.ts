/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { appendFileSync } from "fs";
import { BaseLogger, LogLevel, RunnerLogger } from ".";

export class FileLogger extends BaseLogger implements RunnerLogger {
    private filePath: string;

    constructor(path: string, logLevel?: LogLevel, debugEnvironmentVariables?: string[]) {
        super(logLevel, debugEnvironmentVariables);
        this.filePath = path;
    }

    log(message: string): void {
        this.write("LOG", message);
    }

    debug(message: string): void {
        if (super.shouldLog(LogLevel.Debug)) {
            this.write("DEBUG", this.filterSecrets(message).toString());
        }
    }

    info(message: string): void {
        if (super.shouldLog(LogLevel.Info)) {
            this.write("INFO", this.filterSecrets(message).toString());
        }
    }

    notice(message: string | Error): void {
        if (super.shouldLog(LogLevel.Warning)) {
            this.write("NOTICE", this.filterSecrets(message).toString());
        }
    }

    warning(message: string | Error): void {
        if (super.shouldLog(LogLevel.Warning)) {
            this.write("WARNING", this.filterSecrets(message).toString());
        }
    }

    error(message: string | Error): void {
        if (super.shouldLog(LogLevel.Error)) {
            this.write("ERROR", this.filterSecrets(message).toString());
        }
    }

    startGroup(name: string, level?: LogLevel): void {
        if (super.shouldLog(level || LogLevel.Info)) {
            this.write("START-GROUP", name);
        }
    }

    endGroup(level?: LogLevel): void {
        if (super.shouldLog(level || LogLevel.Info)) {
            this.write("END-GROUP", "");
        }
    }

    write(category: string, message: string): void {
        const timestamp = new Date().toISOString();
        appendFileSync(this.filePath, `${timestamp} [${category}] ${message}\n`);
    }
}
