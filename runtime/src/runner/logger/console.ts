/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { BaseLogger, LogLevel, RunnerLogger } from ".";

export class ConsoleLogger extends BaseLogger implements RunnerLogger {
    constructor(logLevel?: LogLevel, debugEnvironmentVariables?: string[]) {
        super(logLevel, debugEnvironmentVariables);
    }

    log(message: string): void {
        console.log(this.filterSecrets(message));
    }

    debug(message: string): void {
        if (super.shouldLog(LogLevel.Debug)) {
            console.debug(this.filterSecrets(message));
        }
    }

    info(message: string): void {
        if (super.shouldLog(LogLevel.Info)) {
            console.info(this.filterSecrets(message));
        }
    }

    notice(message: string | Error): void {
        if (super.shouldLog(LogLevel.Warning)) {
            console.info(this.filterSecrets(message));
        }
    }

    warning(message: string | Error): void {
        if (super.shouldLog(LogLevel.Warning)) {
            console.warn(this.filterSecrets(message));
        }
    }

    error(message: string | Error): void {
        if (super.shouldLog(LogLevel.Error)) {
            console.error(this.filterSecrets(message));
        }
    }

    startGroup(name: string, level?: LogLevel): void {
        if (super.shouldLog(level || LogLevel.Info)) {
            console.group(name);
        }
    }

    endGroup(level?: LogLevel): void {
        if (super.shouldLog(level || LogLevel.Info)) {
            console.groupEnd();
        }
    }
}
