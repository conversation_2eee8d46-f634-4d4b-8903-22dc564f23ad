/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { SecretFilter } from "../../helpers/SecretFilter";

export enum LogLevel {
    None = 0,
    Error = 1 << 0, // 1
    Warning = 1 << 1, // 2
    Info = 1 << 2, // 4
    Debug = 1 << 3, // 8
    All = Error | Warning | Info | Debug,
    Default = Error | Warning | Info,
}

export interface RunnerLogger {
    /**
     * Log a message ignoring the configured log level.
     * This is useful for logging messages that should always be logged, regardless of the log level.
     * @param message The message to log.
     */
    log(message: string): void;

    /**
     * Returns true if the environment is set to debug.
     * Note: This is not the same as the log level being set to debug.
     */
    isDebug(): boolean;

    /**
     * Log a debug message. This is only logged if the log level is set to debug.
     * @param message The message to log.
     */
    debug(message: string): void;

    /**
     * Log an info message. This is only logged if the log level is set to info or debug.
     * @param message The message to log.
     */
    info(message: string): void;

    /**
     * Log a notice message. This is only logged if the log level is set to warning, info, or debug,
     * but logs using the logger's info method.
     * This is useful for logging messages that are not errors, but are important enough to log on
     * less verbose log levels.
     * @param message The message to log.
     */
    notice(message: string | Error): void;

    /**
     * Log a warning message. This is only logged if the log level is set to warning, info, or debug
     * @param message The message to log.
     */
    warning(message: string | Error): void;

    /**
     * Log an error message. This is only logged if the log level is set to error, warning, info, or debug
     * @param message The message to log.
     */
    error(message: string | Error): void;

    /**
     * Log a message that starts a new group.
     * @param name The name of the group.
     * @param level The log level of the group. Defaults to info.
     */
    startGroup(name: string, level?: LogLevel): void;

    /**
     * Log a message that ends the current group.
     * @param level The log level of the group. Defaults to info.
     */
    endGroup(level?: LogLevel): void;
}

/**
 * Returns true if the DEBUG or COPILOT_AGENT_DEBUG environment variable is set to 1 or true (case-insensitive).
 * If additionalVariables are provided, they are also checked.
 * @param additionalVariables Additional environment variables to check for debug logging.
 */
export function isDebugEnvironment(...additionalVariables: string[]): boolean {
    const vars = new Set(["DEBUG", "COPILOT_AGENT_DEBUG", ...additionalVariables]);
    for (const key of vars) {
        if (process.env[key] === "1" || process.env[key]?.toLocaleLowerCase() === "true") {
            return true;
        }
    }
    return false;
}

export abstract class BaseLogger implements RunnerLogger {
    protected logLevel?: LogLevel;
    protected debugEnvironmentVariables?: string[];

    private secretFilter: SecretFilter = SecretFilter.getInstance();

    constructor(logLevel?: LogLevel, debugEnvironmentVariables?: string[]) {
        // If log level is not explicitly set, check if the debug environment variables are set.
        // If they are set, then set the log level to all, otherwise default it to info.
        if (logLevel === undefined) {
            const checkDebug = isDebugEnvironment(...(debugEnvironmentVariables ?? []));
            if (checkDebug) {
                this.logLevel = LogLevel.All;
            } else {
                this.logLevel = LogLevel.Default;
            }
        } else {
            this.logLevel = logLevel;
        }
        this.debugEnvironmentVariables = debugEnvironmentVariables;
    }

    filterSecrets(messageOrError: string | Error): string | Error {
        if (typeof messageOrError === "string") {
            return this.secretFilter.filterSecrets(messageOrError);
        } else {
            return this.secretFilter.filterSecrets(messageOrError);
        }
    }

    /**
     * Returns true if the log level is not set, or the log level is set and the level is enabled.
     */
    shouldLog(level: LogLevel): boolean {
        return this.logLevel === undefined || (this.logLevel & level) === level;
    }

    isDebug(): boolean {
        return isDebugEnvironment(...(this.debugEnvironmentVariables ?? []));
    }

    abstract log(message: string): void;
    abstract info(message: string): void;
    abstract debug(message: string): void;
    abstract notice(message: string | Error): void;
    abstract warning(message: string | Error): void;
    abstract error(message: string | Error): void;
    abstract startGroup(name: string, level?: LogLevel): void;
    abstract endGroup(level?: LogLevel): void;
}
