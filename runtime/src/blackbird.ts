/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { ContentFilterMode } from "./helpers/content-filter";
import { RunnerLogger } from "./runner";
import { MCPTransport } from "./tools/mcp-transport";

export default async function search(
    task: string,
    owner: string,
    repo: string,
    transport: MCPTransport,
    logger: RunnerLogger,
): Promise<string> {
    try {
        // TODO: Blackbird search will be in the github MCP server
        // This is a temporary implementation to use the blackbird MCP server until we talk with the MCP teams
        return (
            await transport.invokeTool(
                "blackbird-mcp-server/search_repository_with_agent",
                { task, depth: 2, owner, repo, numSnippets: 10 },
                ContentFilterMode.HiddenCharacters,
            )
        ).textResultForLlm;
    } catch (error) {
        logger.error(`Error during Blackbird search: ${error}`);
        return "";
    }
}
