/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { SecretFilter } from "./helpers/SecretFilter";
import { RuntimeSettings } from "./settings";

/**
 * Telemetry emitted by the runtime contains properties and metrics. These are non-sensitive pieces
 * of information. There are also restricted properties that must be used to store sensitive information.
 */
export type Telemetry = {
    /**
     * Telemetry properties can be used to store string props.
     * WARNING: Do not put sensitive data here. Use restrictedProperties for that.
     */
    properties: Record<string, string | undefined>;
    /**
     * Restricted telemetry properties must be used to store sensitive string props. These props will only be available on the restricted kusto topics.
     * Nonnullable so it is harder to overlook.
     */
    restrictedProperties: Record<string, string | undefined>;
    /**
     * The name of the telemetry event associated with the emitted runtime event.
     */
    metrics: Record<string, number | undefined>;
};

/**
 * Telemetry can be emitted by the runtime via progress events. Telemetry is attached to progress events
 * on a `telemetry` property whose type is this.
 */
export type EventTelemetry<EventT = string, TelemetryT extends Telemetry = Telemetry> = {
    /**
     * The name of the telemetry event associated with the emitted runtime progress event.
     */
    event: EventT;
    /**
     * String-esque properties that are associated with the telemetry event.
     * WARNING: Do not put sensitive data here. Use restrictedProperties for that.
     */
    properties: TelemetryT["properties"];
    /**
     * String-esque properties that are associated with the telemetry event. These are only available on the restricted topics
     */
    restrictedProperties: TelemetryT["restrictedProperties"];
    /**
     * Number-esque metrics that are associated with the telemetry event. Both integer and floating point values are possible.
     */
    metrics: TelemetryT["metrics"];
};

/**
 * Alternatively telemetry can be emitted by an event which just contains telemetry. This is that type.
 *
 * You can use this type with our without generics. The generics help you to enforce what properties/metrics are on your event
 * more precisely and safely.
 */
export type TelemetryEvent<EventT = string, TelemetryT extends Telemetry = Telemetry> = {
    kind: "telemetry";
    telemetry: EventTelemetry<EventT, TelemetryT>;
};

export function isTelemetryEvent(event: unknown | TelemetryEvent): event is TelemetryEvent {
    return typeof event === "object" && event !== null && "kind" in event && event.kind === "telemetry";
}

/**
 * If placing features flags in telemetry, use this name.
 */
export const FEATURE_FLAGS_TELEMETRY_PROPERTY_NAME = "featureFlags";

/**
 * Helper for adding an unknown typed error in a catch block to something with telemetry.
 */
export function addError(event: { telemetry: Telemetry }, error: unknown, errorPropertyName: string = "error"): void {
    if (error instanceof Error && error.cause) {
        event.telemetry.restrictedProperties[errorPropertyName] = `${error} (Cause: ${error.cause})`;
    } else {
        event.telemetry.restrictedProperties[errorPropertyName] = `${error}`;
    }
}

/**
 * Adds feature flags to the telemetry event's properties.
 */
export function addFeatureFlags(event: { telemetry: Telemetry }, settings: RuntimeSettings): void {
    const featureFlags = settings.featureFlags || {};
    event.telemetry.properties[FEATURE_FLAGS_TELEMETRY_PROPERTY_NAME] = JSON.stringify(featureFlags);
}

/**
 * Redacts sensitive information from telemetry events properties and restricted properties.
 */
export function redactTelemetry(event: { telemetry: Telemetry }): void {
    redactProperties(event.telemetry.properties);
    redactProperties(event.telemetry.restrictedProperties);
}

function redactProperties(properties: Record<string, string | undefined>): void {
    for (const key of Object.keys(properties)) {
        if (properties[key]) {
            properties[key] =
                // Since properties are sometimes stringified JSON, we use
                // filterSecretsFromJsonString. For properties which aren't
                // the function will fallback to plain-string redaction.
                SecretFilter.getInstance().filterSecretsFromJsonString(properties[key]);
        }
    }
}
