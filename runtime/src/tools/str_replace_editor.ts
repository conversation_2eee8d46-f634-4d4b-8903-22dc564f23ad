/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { diff } from "fast-myers-diff";
import { existsSync } from "fs";
import { readdir, readFile, stat, writeFile } from "fs/promises";
import { dirname, isAbsolute, join } from "path";
import { EditsTracker } from "../helpers/EditsTracker";
import { getSafeForTelemetryFileExtension, isDirectory, pathExists } from "../helpers/file-reader";
import { findAndReplaceOne } from "../helpers/string-helpers";
import { getDiagnosticsEditFeedbackText } from "../lsp/diagnostics";
import { LSPClient, LSPClientFactory, WaitedForDiagnostics } from "../lsp/LSPClient";
import { RunnerLogger } from "../runner";
import { DeepPartial, isFeatureFlagEnabled, RuntimeSettings } from "../settings/types";
import { TelemetryEvent } from "../telemetry";
import {
    llmMessageFromToolHalt,
    shouldHalt,
    Tool,
    ToolCallbackOptions,
    ToolConfig,
    ToolConfirmationCallback,
    ToolResultExpanded,
} from "./index";
import { maybeResolvePathAgainstDir, strLenTruncate, tokenCountTruncate } from "./util";

export type StrReplaceEditorOptions = {
    /**
     * What to base the truncation of the output of the tool on. Defaults to `"strLen"`.
     */
    truncateBasedOn?: "strLen" | "tokenCount";

    /**
     * How to style the truncation of the output of the tool. Defaults to `"middle"`.
     */
    truncateStyle?: "end" | "middle";
};

export type StrReplaceEditorTelemetry = {
    properties: {
        command: "invalid" | StrReplaceEditorArgs["command"];
        resolvedPathAgainstCwd: string;
        /**
         * Stringified copy of {@link StrReplaceEditorOptions}.
         */
        options: string;
        /**
         * Stringified list of the input names passed to the tool.
         */
        inputs: string;
        /**
         * The extension of the file being edited (if a directory, then "directory").
         */
        fileExtension?: string;
    };
    restrictedProperties: {
        initFeedbackError?: string;
        getEditFeedbackError?: string;
        editFeedback?: string;
    };
    metrics: {
        responseTokenLimit: number | undefined;
        resultLength: number;
        resultForLlmLength: number;
    } & Partial<InitEditFeedbackMetrics & GetEditFeedbackMetrics>;
};

export type StrReplaceEditorResult = Omit<ToolResultExpanded, "toolTelemetry"> & {
    toolTelemetry: StrReplaceEditorTelemetry;
};

export type StrReplaceEditorArgs =
    | {
          command: "view";
          path: string;
          view_range: [number, number] | undefined;
      }
    | { command: "create"; path: string; file_text: string }
    | {
          command: "str_replace";
          path: string;
          new_str?: string;
          old_str: string;
      }
    | { command: "insert"; path: string; insert_line: number; new_str: string };

export type StrReplaceEditorShutdownTelemetry = TelemetryEvent<
    "str_replace_editor_shutdown",
    {
        properties: { trackedEdits: string };
        metrics: Record<string, never>;
        restrictedProperties: Record<string, never>;
    }
>;

const allCommands = new Set<StrReplaceEditorArgs["command"]>(["create", "view", "str_replace", "insert"]);

/**
 * @param resultType The type of the expanded tool result
 * @param baseToolTelemetry The base telemetry for the tool
 * @param commandTelemetry The command specific telemetry
 * @param result The result of calling the tool, prior to any truncation or other modifications.
 * @param sessionLog The session log for the tool.
 * @param resultForLlm A modified version of the result that is suitable for the LLM. If not provided, then `result` will be used (we'll assume there was no truncation or other modifications).
 * @param error An error message describing a failure of the tool. If not provided, and the `resultType` is "failure", then `result` will be used (we'll assume that `result` describes the error).
 */
function getExpandedToolResult<CommandTelemetry extends ToolResultExpanded["toolTelemetry"]>(
    resultType: ToolResultExpanded["resultType"],
    baseToolTelemetry: StrReplaceEditorTelemetry,
    commandTelemetry: CommandTelemetry,
    result: string,
    sessionLog: string,
    resultForLlm?: string,
    error?: string,
): StrReplaceEditorResult {
    resultForLlm = resultForLlm || result;
    error = error || (resultType === "failure" ? result : undefined);

    return {
        resultType: resultType,
        textResultForLlm: resultForLlm,
        error: error,
        toolTelemetry: {
            properties: {
                ...baseToolTelemetry.properties,
                ...commandTelemetry.properties,
            },
            metrics: {
                ...baseToolTelemetry.metrics,
                resultLength: result.length,
                resultForLlmLength: resultForLlm.length,
                ...commandTelemetry.metrics,
            },
            restrictedProperties: {
                ...baseToolTelemetry.restrictedProperties,
                ...commandTelemetry.restrictedProperties,
            },
        },
        sessionLog: sessionLog,
    };
}

export const str_replace_editor = (config: ToolConfig, logger: RunnerLogger, settings?: RuntimeSettings): Tool => {
    const editsTracker = new EditsTracker();

    const defaultOptions: Required<StrReplaceEditorOptions> = {
        truncateBasedOn: "tokenCount",
        truncateStyle: "middle",
    };
    logger.debug(`str_replace_editor: default options: ${JSON.stringify(defaultOptions, null, 2)}`);

    return {
        name: "str_replace_editor",
        description: `Editing tool for viewing, creating and editing files
     * State is persistent across command calls and discussions with the user
     * If \`path\` is a file, \`view\` displays the result of applying \`cat -n\`. If \`path\` is a directory, \`view\` lists non-hidden files and directories up to 2 levels deep
     * The \`create\` command cannot be used if the specified \`path\` already exists, or if parent directories don't exist
     * If a \`command\` generates a long output, output will be truncated and marked with \`<file too long...\`

     Notes for using the \`str_replace\` command:
     * The \`old_str\` parameter should match EXACTLY one or more consecutive lines from the original file
     * If the \`old_str\` parameter is not unique in the file, replacement will not be performed. Make sure to include enough context in \`old_str\` to make it unique
     * The \`new_str\` parameter should contain the edited lines that should replace the \`old_str\``,
        input_schema: {
            type: "object",
            properties: {
                command: {
                    type: "string",
                    enum: ["view", "create", "str_replace", "insert"] satisfies StrReplaceEditorArgs["command"][],
                    description: `The commands to run. Allowed options are: ${Array.from(allCommands)
                        .map((cmd) => `\`${cmd}\``)
                        .join(", ")}.`,
                },
                file_text: {
                    description: "Required parameter of `create` command; the content of the file to be created.",
                    type: "string",
                },
                insert_line: {
                    description:
                        "Required parameter of `insert` command. The `new_str` will be inserted AFTER the line `insert_line` of `path`.",
                    type: "integer",
                },
                new_str: {
                    description:
                        "Required parameter of `str_replace` command containing the new string. Required parameter of `insert` command containing the string to insert.",
                    type: "string",
                },
                old_str: {
                    description:
                        "Required parameter of `str_replace` command containing the string in `path` to replace. Leading and ending whitespaces from file content should be preserved!",
                    type: "string",
                },
                path: {
                    description: "Absolute path to file or directory.",
                    type: "string",
                },
                view_range: {
                    description:
                        "Optional parameter of `view` command when `path` points to a file. If none is given, the full file is shown. If provided, the file will be shown in the indicated line number range, e.g. [11, 12] will show lines 11 and 12. Indexing at 1 to start. Setting `[start_line, -1]` shows all lines from `start_line` to the end of the file.",
                    items: {
                        type: "integer",
                    },
                    type: "array",
                },
            },
            required: ["command", "path"],
        },
        shutdown: async (): Promise<StrReplaceEditorShutdownTelemetry> => {
            const trackedEditsJsonArrString = editsTracker.getTrackedEditsJsonArrString();
            editsTracker.reset();
            return {
                kind: "telemetry",
                telemetry: {
                    event: "str_replace_editor_shutdown",
                    properties: {
                        trackedEdits: trackedEditsJsonArrString,
                    },
                    metrics: {},
                    restrictedProperties: {},
                },
            };
        },
        summariseIntention: (input: StrReplaceEditorArgs): string => {
            switch (input.command) {
                case "create":
                    return `create a new file at ${input.path}.`;
                case "view":
                    return `view the file at ${input.path}.`;
                case "str_replace":
                case "insert":
                    return `edit the file at ${input.path}.`;
                default: {
                    const _exhaustive: never = input;
                    return `unknown command: ${JSON.stringify(input)}`;
                }
            }
        },
        callback: async (
            input: StrReplaceEditorArgs,
            options?: ToolCallbackOptions<StrReplaceEditorOptions>,
        ): Promise<StrReplaceEditorResult> => {
            const toolOptionsWithDefaults: Required<StrReplaceEditorOptions> = {
                ...defaultOptions,
                ...(options?.toolOptions || {}),
            };

            logger.debug(input.command + ": " + input.path);

            const baseToolTelemetry: StrReplaceEditorTelemetry = {
                properties: {
                    command: returnCommandIfValid(input.command),
                    options: JSON.stringify(toolOptionsWithDefaults),
                    inputs: JSON.stringify(Object.keys(input)),
                    resolvedPathAgainstCwd: "false",
                    fileExtension: input.path ? getSafeForTelemetryFileExtension(input.path) : undefined,
                },
                metrics: {
                    resultLength: 0,
                    resultForLlmLength: 0,
                    responseTokenLimit: options?.truncationOptions?.tokenLimit,
                },
                restrictedProperties: {},
            };

            let result: StrReplaceEditorResult;
            try {
                if (typeof input.path !== "string" || !input.path) {
                    return getExpandedToolResult(
                        "failure",
                        baseToolTelemetry,
                        {},
                        `A path parameter is required, and must be a non-empty string.`,
                        "",
                        undefined,
                        `Path not provided`,
                    );
                }

                if (config.cwd) {
                    const cwd = await config.cwd();
                    if (cwd) {
                        const triedToVerifyPath = await maybeResolvePathAgainstDir(input.path, cwd);
                        if (!isAbsolute(triedToVerifyPath)) {
                            logger.debug(
                                `Could not resolve path "${input.path}" against agent cwd "${cwd}". Need to use absolute path.`,
                            );

                            // If we were unable to resolve to an absolute path, return error to avoid
                            // confusion for now. We will need to evaluate if we should use the bash cwd path instead.
                            return getExpandedToolResult(
                                "failure",
                                baseToolTelemetry,
                                {},
                                `Path "${input.path}" is not absolute. Please provide an absolute path.`,
                                "",
                                undefined,
                                `Path not absolute`,
                            );
                        }
                        if (triedToVerifyPath !== input.path) {
                            logger.debug(`using resolved path ${triedToVerifyPath} based on agent cwd ${cwd}`);
                            input.path = triedToVerifyPath;
                            baseToolTelemetry.properties.resolvedPathAgainstCwd = "true";
                        }
                    }
                }

                const {
                    lspClient,
                    metrics: initEditsFeedbackMetrics,
                    error: initEditsFeedbackError,
                } = await initEditFeedback(settings, input, logger, config);
                Object.assign(baseToolTelemetry.metrics, initEditsFeedbackMetrics);
                baseToolTelemetry.restrictedProperties.initFeedbackError = initEditsFeedbackError;

                switch (input.command) {
                    case "view":
                        result = await handleViewCommand(input, baseToolTelemetry, logger, options);
                        break;
                    case "str_replace":
                        result = await handleStrReplaceCommand(
                            input,
                            baseToolTelemetry,
                            config.toolConfirmationCallback,
                            logger,
                            options,
                        );
                        break;
                    case "create":
                        result = await handleCreateCommand(
                            input,
                            baseToolTelemetry,
                            config.toolConfirmationCallback,
                            logger,
                            options,
                        );
                        break;
                    case "insert":
                        result = await handleInsertCommand(
                            input,
                            baseToolTelemetry,
                            config.toolConfirmationCallback,
                            logger,
                            options,
                        );
                        break;
                    default: {
                        const failSafeInput = input as { command: string };
                        const commandError = failSafeInput.command
                            ? `Unknown command: ${failSafeInput.command}`
                            : "No command provided";
                        return Promise.reject(
                            new Error(`${commandError}. Allowed commands are: ${Array.from(allCommands).join(", ")}`),
                        );
                    }
                }

                const {
                    feedback: editFeedback,
                    metrics: editFeedbackMetrics,
                    error: getEditFeedbackError,
                } = await getEditFeedback(lspClient, input, logger);
                Object.assign(result.toolTelemetry.metrics, editFeedbackMetrics);
                result.toolTelemetry.restrictedProperties.getEditFeedbackError = getEditFeedbackError;
                result.toolTelemetry.restrictedProperties.editFeedback = JSON.stringify(editFeedback, undefined, 2);

                const doUseEditFeedback = isFeatureFlagEnabled(
                    settings,
                    "copilot_swe_agent_str_replace_editor_use_lsp_diagnostics",
                );
                if (editFeedback.length > 0 && doUseEditFeedback) {
                    const feedbackText = getDiagnosticsEditFeedbackText(input.path, editFeedback);
                    logger.debug(`Appending feedback text to result: ${feedbackText}`);
                    result.textResultForLlm += feedbackText;
                }
            } catch (e) {
                result = getExpandedToolResult(
                    "failure",
                    baseToolTelemetry,
                    {},
                    `Failed to execute \`str_replace_editor\` tool with arguments: ${JSON.stringify(input)} due to error: ${e}`,
                    "",
                    undefined,
                    `Unhandled error: ${e}`,
                );
            }

            if (
                result.resultType !== "rejected" &&
                input.path &&
                input.command &&
                typeof input.path === "string" &&
                typeof input.command === "string"
            ) {
                editsTracker.trackEdit(
                    input.path,
                    returnCommandIfValid(input.command),
                    result.resultType,
                    options?.toolCallId,
                );
            }

            return result;
        },
        safeForTelemetry: true,
    };
};

type ViewTelemetry = {
    properties: { viewType: "file" | "directory" | "unknown" };
};

/**
 * Handler for the "view" command
 */
async function handleViewCommand(
    input: StrReplaceEditorArgs & { command: "view" },
    baseToolTelemetry: StrReplaceEditorTelemetry,
    logger: RunnerLogger,
    options?: ToolCallbackOptions<StrReplaceEditorOptions>,
): Promise<StrReplaceEditorResult> {
    const toolOptionsWithDefaults: Required<StrReplaceEditorOptions> = {
        truncateBasedOn: "tokenCount",
        truncateStyle: "middle",
        ...(options?.toolOptions || {}),
    };

    let viewType: "file" | "directory";
    let viewResult: string;
    let gitDiff = "";

    if (!existsSync(input.path)) {
        return getExpandedToolResult<ViewTelemetry>(
            "failure",
            baseToolTelemetry,
            { properties: { viewType: "unknown" } },
            `Path ${input.path} does not exist. Please provide a valid path.`,
            "",
            undefined,
            `Path does not exist`,
        );
    }

    const stats = await stat(input.path);
    if (stats.isDirectory()) {
        viewType = "directory";
        const lines = await readdir(input.path);
        viewResult = lines.join("\n");
        const range = `@@ -1,0 +1,${lines.length} @@`;
        // with directory, return session log in git-diff patch format as if output of readdir is content of a file
        gitDiff = createGitDiff(undefined, input.path, `${range}\n${lines.map((line) => ` ${line}`).join("\n")}`);
    } else {
        viewType = "file";
        const [start, end] = input.view_range || [1, undefined];
        const lines = (await readFile(input.path, "utf-8")).split("\n");
        const viewedLines = lines.slice(start < 1 ? 0 : start - 1, !end || end < start ? undefined : end);

        const length = (end ?? lines.length) - (start - 1);
        const range = `@@ -${start},${length} +${start},${length} @@`;
        gitDiff = createGitDiff(
            input.path,
            input.path,
            `${range}\n${viewedLines.map((line) => ` ${line}`).join("\n")}`,
        );
        viewResult = viewedLines.map((line, index) => `${index + start}.${line}`).join("\n");
    }

    const maybeTruncatedViewResult = truncateResponse(
        viewResult,
        viewType,
        options?.truncationOptions,
        toolOptionsWithDefaults,
        logger,
    );
    return getExpandedToolResult<ViewTelemetry>(
        "success",
        baseToolTelemetry,
        { properties: { viewType: viewType } },
        viewResult,
        gitDiff,
        maybeTruncatedViewResult,
    );
}

/**
 * Handler for the "str_replace" command
 */
async function handleStrReplaceCommand(
    input: StrReplaceEditorArgs & { command: "str_replace" },
    baseToolTelemetry: StrReplaceEditorTelemetry,
    toolConfirmationCallback: ToolConfirmationCallback,
    _logger: RunnerLogger,
    _options?: ToolCallbackOptions<StrReplaceEditorOptions>,
): Promise<StrReplaceEditorResult> {
    let strReplaceResult:
        | { type: "success"; resultForLlm: string; newText: string }
        | { type: "failure"; resultForLlm: string; error?: string }
        | undefined;
    const originalText = await readFile(input.path, "utf-8");

    if (!input.old_str) {
        strReplaceResult = {
            type: "failure",
            resultForLlm: `Invalid inputs: old_str is required for str_replace`,
        };
    } else if (input.old_str === input.new_str) {
        strReplaceResult = {
            type: "success",
            resultForLlm: `No changes made: old_str and new_str are the same`,
            newText: originalText,
        };
    } else if (input.new_str === undefined || input.new_str === null) {
        strReplaceResult = {
            type: "failure",
            resultForLlm: `Invalid inputs: new_str is required for str_replace`,
        };
    } else {
        const findAndReplaceOneResult = findAndReplaceOne(originalText, input.old_str, input.new_str || "");
        const type = findAndReplaceOneResult.type;
        if (type == "none") {
            strReplaceResult = {
                type: "failure",
                resultForLlm: `No match found: old_str not found in ${input.path}; no changes made; you might want to try again with correct old_str`,
                error: `No match found`,
            };
        } else if (type == "multiple") {
            strReplaceResult = {
                type: "failure",
                resultForLlm: `Multiple matches found for old_str in ${input.path}; no changes made; you might want to try again and be more specific with old_str`,
                error: "Multiple matches found",
            };
        } else if (type === "exact" || type === "fuzzy") {
            strReplaceResult = {
                type: "success",
                resultForLlm: `File ${input.path} updated with changes.`,
                newText: findAndReplaceOneResult.text,
            };
        }
    }

    // All attempts at replace failed or no attempts made, return an error.
    if (!strReplaceResult) {
        strReplaceResult = {
            type: "failure",
            resultForLlm: `Failed to replace ${input.old_str} with ${input.new_str || ""} in ${input.path}.`,
            error: "Failed to replace",
        };
    }

    let gitDiff = "";
    if (strReplaceResult.type === "success") {
        const newText = strReplaceResult.newText;

        const diffBlock = generateDiffFromStrings(originalText, newText);
        gitDiff = createGitDiff(input.path, input.path, diffBlock);

        const confirmationResult = await toolConfirmationCallback({
            type: "edit",
            title: "Edit file",
            fileName: input.path,
            diff: gitDiff,
        });

        if (shouldHalt(confirmationResult)) {
            const message = llmMessageFromToolHalt(confirmationResult);
            return getExpandedToolResult("rejected", baseToolTelemetry, {}, message, "", undefined, message);
        }

        await writeFile(input.path, newText);
    }

    return getExpandedToolResult(
        strReplaceResult.type,
        baseToolTelemetry,
        {},
        strReplaceResult.resultForLlm,
        gitDiff,
        undefined,
        strReplaceResult.type === "success" ? undefined : strReplaceResult.error,
    );
}

/**
 * Handler for the "create" command
 */
async function handleCreateCommand(
    input: StrReplaceEditorArgs & { command: "create" },
    baseToolTelemetry: StrReplaceEditorTelemetry,
    toolConfirmationCallback: ToolConfirmationCallback,
    _logger: RunnerLogger,
    _options?: ToolCallbackOptions<StrReplaceEditorOptions>,
): Promise<StrReplaceEditorResult> {
    const content = input.file_text || "";

    const pathDirname = dirname(input.path);
    const doesDirnameExist = await pathExists(pathDirname);
    if (!doesDirnameExist) {
        return getExpandedToolResult(
            "failure",
            baseToolTelemetry,
            {},
            `Parent directory ${pathDirname} does not exist. You need to create it before creating the file.`,
            "",
            undefined,
            `Parent directory does not exist`,
        );
    }

    const doesPathExist = await pathExists(input.path);
    if (doesPathExist) {
        return getExpandedToolResult(
            "failure",
            baseToolTelemetry,
            {},
            `Path ${input.path} already exists as a ${doesPathExist}. Use the \`view\` command to check contents and \`str_replace\` command to edit it.`,
            "",
            undefined,
            `Path already exists`,
        );
    } else {
        const lines = content.split("\n");
        const range = `@@ -1,0 +1,${lines.length} @@`;
        const gitDiff = createGitDiff(undefined, input.path, `${range}\n${lines.map((line) => `+${line}`).join("\n")}`);

        const confirmationResult = await toolConfirmationCallback({
            type: "edit",
            title: "Create file",
            fileName: input.path,
            diff: gitDiff,
        });

        if (shouldHalt(confirmationResult)) {
            const message = llmMessageFromToolHalt(confirmationResult);
            return getExpandedToolResult("rejected", baseToolTelemetry, {}, message, "", undefined, message);
        }

        await writeFile(input.path, content);

        return getExpandedToolResult(
            "success",
            baseToolTelemetry,
            {},
            `Created file ${input.path} with ${content.length} characters`,
            gitDiff,
        );
    }
}

type InsertTelemetry = { metrics: { emptyLinesAdded: number } };

/**
 * Handler for the "insert" command
 */
async function handleInsertCommand(
    input: StrReplaceEditorArgs & { command: "insert" },
    baseToolTelemetry: StrReplaceEditorTelemetry,
    toolConfirmationCallback: ToolConfirmationCallback,
    _logger: RunnerLogger,
    _options?: ToolCallbackOptions<StrReplaceEditorOptions>,
): Promise<StrReplaceEditorResult> {
    if (input.new_str === undefined || input.new_str === null || input.insert_line === undefined) {
        return getExpandedToolResult<InsertTelemetry>(
            "failure",
            baseToolTelemetry,
            { metrics: { emptyLinesAdded: 0 } },
            `Missing required arguments for command insert: insert_line and new_str`,
            "",
        );
    }

    const fileText = await readFile(input.path, "utf-8");
    const lines = fileText.split("\n");

    // Only validate against negative line numbers
    if (input.insert_line < 0) {
        return getExpandedToolResult<InsertTelemetry>(
            "failure",
            baseToolTelemetry,
            { metrics: { emptyLinesAdded: 0 } },
            `Invalid line number: ${input.insert_line}; must be non-negative`,
            "",
        );
    }

    let emptyLinesToAdd = 0;
    // If insert_line is beyond the end of the file, add empty lines
    if (input.insert_line > lines.length) {
        emptyLinesToAdd = input.insert_line - lines.length;
        for (let i = 0; i < emptyLinesToAdd; i++) {
            lines.push("");
        }
    }

    lines.splice(input.insert_line, 0, input.new_str);
    const newFileText = lines.join("\n");
    const diffBlock = generateDiffFromStrings(fileText, newFileText);
    const gitDiff = createGitDiff(input.path, input.path, diffBlock);

    const confirmationResult = await toolConfirmationCallback({
        type: "edit",
        title: "Edit file",
        fileName: input.path,
        diff: gitDiff,
    });

    if (shouldHalt(confirmationResult)) {
        const message = llmMessageFromToolHalt(confirmationResult);
        return getExpandedToolResult("rejected", baseToolTelemetry, {}, message, "", undefined, message);
    }

    await writeFile(input.path, newFileText);

    return getExpandedToolResult<InsertTelemetry>(
        "success",
        baseToolTelemetry,
        { metrics: { emptyLinesAdded: emptyLinesToAdd } },
        `Inserted ${input.new_str.length} characters at line ${input.insert_line} in ${input.path}`,
        gitDiff,
    );
}

function truncateResponse(
    fullResponse: string,
    kind: "file" | "directory" | "output",
    truncationOptions: ToolCallbackOptions["truncationOptions"],
    strReplaceEditorOptions: Required<StrReplaceEditorOptions>,
    logger: RunnerLogger,
): string {
    if (strReplaceEditorOptions.truncateBasedOn === "tokenCount" && truncationOptions) {
        try {
            return tokenCountTruncate(fullResponse, kind, truncationOptions, strReplaceEditorOptions.truncateStyle);
        } catch (e) {
            // fallback to string length truncation
            logger.debug(`Error truncating: ${e}\n. Switching to string length truncation.`);
        }
    }

    return strLenTruncate(fullResponse, kind, undefined, strReplaceEditorOptions.truncateStyle);
}

const MAX_CONTEXT_LINES = 3;

function generateDiffFromStrings(original: string, changed: string): string {
    const originalLines = original.split("\n");
    const changedLines = changed.split("\n");

    const diffs = [];
    for (const quad of diff(originalLines, changedLines)) {
        const [sx, ex, sy, ey] = quad;
        const removedLines = originalLines.slice(sx, ex).map((line) => `-${line}`);
        const addedLines = changedLines.slice(sy, ey).map((line) => `+${line}`);
        diffs.push({
            quad: quad,
            diff: removedLines.concat(addedLines),
        });
    }

    diffs.sort((l, r) => l.quad[0] - r.quad[0]);

    // Combine diffs that are roughly adjacent
    const combinedDiffs = [];
    for (let i = 0; i < diffs.length; i++) {
        if (i == diffs.length - 1) {
            combinedDiffs.push(diffs[i]);
            continue;
        }

        const thisDiff = diffs[i];
        const nextDiff = diffs[i + 1];
        const [sx, ex, sy, _ey] = thisDiff.quad;
        const [nsx, nex, _nsy, ney] = nextDiff.quad;

        if (ex + 2 * MAX_CONTEXT_LINES <= nsx) {
            combinedDiffs.push(thisDiff);
            continue;
        }

        // There are 6 or fewer lines inbetween the diffs, so add context lines and combine them
        const diff = thisDiff.diff.concat(originalLines.slice(ex, nsx).map((line) => ` ${line}`)).concat(nextDiff.diff);
        combinedDiffs.push({
            quad: [sx, nex, sy, ney],
            diff: diff,
        });
        i++;
    }

    const diffStrings = combinedDiffs.map((d) => {
        const [sx, ex, sy, ey] = d.quad;
        const newSx = Math.max(sx - MAX_CONTEXT_LINES, 0);
        const newEx = Math.min(ex + MAX_CONTEXT_LINES, originalLines.length);
        const newSy = Math.max(sy - MAX_CONTEXT_LINES, 0);
        const newEy = Math.min(ey + MAX_CONTEXT_LINES, changedLines.length);
        const numX = newEx - newSx;
        const numY = newEy - newSy;

        const preContext = originalLines.slice(newSx, sx).map((line) => ` ${line}`);
        const postContext = originalLines.slice(ex, newEx).map((line) => ` ${line}`);

        const headerSx = newSx + 1;
        const headerSy = newSy + 1;

        const diffContent = preContext.concat(d.diff).concat(postContext).join("\n");

        return `@@ -${headerSx},${numX} +${headerSy},${numY} @@\n${diffContent}`;
    });

    return diffStrings.join("\n");
}

// Creates a diff in the git diff format.  See https://git-scm.com/docs/git-diff-index for more details.
function createGitDiff(beforeFileName: string | undefined, afterFileName: string, diff: string): string {
    const a = join("a", beforeFileName ?? "/dev/null");
    const aDiff = join("a", afterFileName);
    const b = join("b", afterFileName);

    // TODO (houndie):  We punt on the index line with made-up information
    // because I don't think we need it for the frontend right now.  For
    // completeness, it would be good to calculate these filesums and check on
    // the file mode.
    const index = beforeFileName ? "index 0000000..0000000 100644" : "create file mode 100644\nindex 0000000..0000000";

    return `
diff --git ${aDiff} ${b}
${index}
--- ${a}
+++ ${b}
${diff}
`;
}

function returnCommandIfValid(command: string): StrReplaceEditorArgs["command"] | "invalid" {
    return Array.from(allCommands).includes(command as StrReplaceEditorArgs["command"])
        ? (command as StrReplaceEditorArgs["command"])
        : "invalid";
}

function isNotViewCommand(command: string): boolean {
    const validatedCommand = returnCommandIfValid(command);
    return validatedCommand !== "view" && validatedCommand !== "invalid";
}

type InitEditFeedbackMetrics = {
    initFeedbackDurationMs: number;
    didAttemptInitFeedback: number;
    initFeedbackSuccess: number;
};
async function initEditFeedback(
    settings: DeepPartial<RuntimeSettings> | undefined,
    input: StrReplaceEditorArgs,
    logger: RunnerLogger,
    config: ToolConfig,
): Promise<{
    lspClient?: LSPClient;
    metrics: InitEditFeedbackMetrics;
    error?: string;
}> {
    const metrics: InitEditFeedbackMetrics = {
        initFeedbackDurationMs: Date.now(),
        didAttemptInitFeedback: 0,
        initFeedbackSuccess: 0,
    };
    let errorStr: string | undefined;

    const getLspDiagnostics =
        settings && isFeatureFlagEnabled(settings, "copilot_swe_agent_str_replace_editor_get_lsp_diagnostics");
    let lspClient: LSPClient | undefined;
    if (
        getLspDiagnostics &&
        !isDirectory(input.path) &&
        isNotViewCommand(input.command) &&
        LSPClientFactory.getInstance(logger).canCreateForFile(input.path)
    ) {
        metrics.didAttemptInitFeedback = 1;
        try {
            lspClient = await LSPClientFactory.getInstance(logger).getOrCreateForFile(input.path, config.location);
            if (lspClient) {
                if (existsSync(input.path)) {
                    const fileContents = await readFile(input.path, "utf-8");
                    await lspClient.openOrUpdateDocument(`file://${input.path}`, fileContents);
                }
                metrics.initFeedbackSuccess = 1;
            }
        } catch (error) {
            errorStr = `${error}`;
        }
    }

    metrics.initFeedbackDurationMs = Date.now() - metrics.initFeedbackDurationMs;
    return { lspClient: lspClient, metrics: metrics, error: errorStr };
}

type GetEditFeedbackMetrics = {
    waitForFeedbackTimeoutMs: number;
    numFeedbacksReceived: number;
    firstFeedbackReceivedAfterMs: number;
    lastFeedbackReceivedAfterMs: number;
};
async function getEditFeedback(
    lspClient: LSPClient | undefined,
    input: StrReplaceEditorArgs,
    logger: RunnerLogger,
): Promise<{
    feedback: WaitedForDiagnostics;
    metrics: GetEditFeedbackMetrics;
    error?: string;
}> {
    const metrics: GetEditFeedbackMetrics = {
        waitForFeedbackTimeoutMs: 2000,
        numFeedbacksReceived: -1,
        firstFeedbackReceivedAfterMs: -1,
        lastFeedbackReceivedAfterMs: -1,
    };
    let errorStr: string | undefined;

    let lspDiagnostics: WaitedForDiagnostics = [];
    if (lspClient && !isDirectory(input.path) && isNotViewCommand(input.command) && existsSync(input.path)) {
        try {
            const diagnosticsTimeoutMs = metrics.waitForFeedbackTimeoutMs;
            const diagnosticsPromise = lspClient.waitForDiagnostics(diagnosticsTimeoutMs);

            const fileContents = await readFile(input.path, "utf-8");
            await lspClient.openOrUpdateDocument(`file://${input.path}`, fileContents);

            lspDiagnostics = (await diagnosticsPromise).filter((d) => d.diagnostics.length > 0);
            metrics.numFeedbacksReceived = lspDiagnostics.length;
            if (lspDiagnostics.length > 0) {
                metrics.firstFeedbackReceivedAfterMs = lspDiagnostics[0].receivedAfterMs;
                metrics.lastFeedbackReceivedAfterMs = lspDiagnostics[lspDiagnostics.length - 1].receivedAfterMs;
            }
            logger.debug(`LSP diagnostics for ${input.path}: ${JSON.stringify(lspDiagnostics, null, 2)}`);
        } catch (error) {
            errorStr = `${error}`;
        }
    }

    return { feedback: lspDiagnostics, metrics: metrics, error: errorStr };
}
