/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { existsSync } from "fs";
import fs from "fs/promises";
import path from "path";
import { Tool, ToolCallbackOptions, ToolResult } from ".";
import { SecretFilter } from "../helpers/SecretFilter";
import { Client } from "../model/client";
import { addError, TelemetryEvent } from "../telemetry";

export const STORE_MEMORY_TOOL_NAME = "store_memory";
export const READ_MEMORY_TOOL_NAME = "read_memory";

// Template variable usage is not properly detected by the linter. Adding _ to avoid false positive of unused variable
export const memoryConsolidationPrompt = (_itemsToConsolidate: string) => `
You are an expert in knowledge management.
Your task is to consolidate the following into a single collection of non-redundant, high quality facts that can be used to help with future coding tasks across the repository.
- Facts that are redundant or very similar should be combined and rephrased into a single coherent fact.
- Facts that are outdated should be removed.

Think through each decision silently; only output a valid JSON object for each fact, each on its own separate line, in the following format.
Do not include any other text in your response, no markdown, no newlines or unnecessary whitespace.

<example>
{"subject": "building and debugging", "fact": "You can use 'npm run dev' to start the development server.", "source": "package.json:52", "reason": "This information will help the agent to more quickly establish how to build and run the project.", "category": "bootstrap_and_build"}
</example>

<items_to_consolidate>
${_itemsToConsolidate}
</items_to_consolidate>
`;

// Template variable usage is not properly detected by the linter. Adding _ to avoid false positive of unused variable
export const memoryLookupPrompt = (_facts: string, _question: string) => `
You are an expert in knowledge management.
Your task is to consider the given facts and use them to answer the question.

- Think through the user's question:
  - What is the user asking?
  - Which facts are relevant to the question?
  - What is the user trying to achieve?
  - Are some of the given facts not directly relevant to the question, but can be given as additional context to help the user choose their next steps?

- Answer the question thoroughly, using the facts provided. It is ok if your answer is long or detailed because it will be used to make decisions about what to do next.
- If the question is not answerable based on the facts, respond with "I don't have enough information to answer that question."
- Expand your answer with additional, background, breadth, and depth from the given related facts, to help the user choose their next steps.
- Do not include any markdown formatting or XML tags in your response.

<example>
<facts>
{"subject":"database schema","fact":"Messages table supports both text and image messages with columns: content, image_url, message_type, using 'text' as default message type.","source":"server.cjs messages table schema","reason":"This documents the database schema for messages including support for both text and image message types, which is important for message-related feature development.","category":"file_specific"}
{"subject": "bootstrapping", "fact": "Before building, you will need to first run setup.sh script to authenticate.", "source": "README.md:12", "reason": "This information will help the agent to more quickly establish how to build and run the project.", "category": "bootstrap_and_build"}
{"subject": "building and debugging", "fact": "You can use 'npm run dev' to start the development server for the client.", "source": "package.json:52", "reason": "This information will help the agent to more quickly establish how to build and run the project.", "category": "bootstrap_and_build"}
{"subject": "building and debugging", "fact": "You can use 'npm run server' to start the development server for the server.", "source": "package.json:54", "reason": "This information will help the agent to more quickly establish how to build and run the project.", "category": "bootstrap_and_build"}
{"subject":"server architecture","fact":"Server code is organized into modular TypeScript files in server/src/ directory with files including auth.ts, contacts.ts, messages.ts, upload.ts, database.ts, and index.ts.","source":"server/src/ directory structure","reason":"This establishes the modular TypeScript architecture pattern for server-side development that should be maintained for consistency.","category":"general"}
{"subject":"front end architecture","fact":"Client code is organized into modular TypeScript files in client/src/ directory with files including App.tsx, index.tsx, and components/ directory.","source":"client/src/ directory structure","reason":"This establishes the modular TypeScript architecture pattern for client-side development that should be maintained for consistency.","category":"general"}
</facts>
<question>
What is the recommended way to do manual validation of the project?
</question>
<answer>
The recommended way to validate the project is to run the development server using 'npm run dev' command.

Additional information:
- Before building, you will need to first run setup.sh script to authenticate.
- This repo has two parts: a client, implemented in client/src and a server implemented in server/src. Both parts are written in TypeScript.
</answer>
</example>

<facts>
${_facts}
</facts>

Respond to the following question using the facts provided, in natural language.

<question>
${_question}
</question>
`;

export type Memory = {
    subject: string;
    fact: string;
    source: string;
    reason: string;
    category: string;
};

export function isMemory(obj: unknown): obj is Memory {
    return (
        typeof obj === "object" &&
        obj !== null &&
        "subject" in obj &&
        typeof (obj as Memory).subject === "string" &&
        "fact" in obj &&
        typeof (obj as Memory).fact === "string" &&
        "source" in obj &&
        typeof (obj as Memory).source === "string" &&
        "reason" in obj &&
        typeof (obj as Memory).reason === "string" &&
        "category" in obj &&
        typeof (obj as Memory).category === "string"
    );
}

type Question = {
    question: string;
};

export function isQuestion(obj: unknown): obj is Question {
    return typeof obj === "object" && obj !== null && typeof (obj as Question).question === "string";
}

/**
 * Common telemetry that should be emitted by all strategies on shutdown.
 */
export type MemoryToolTelemetry = TelemetryEvent<
    "memory_tool",
    {
        properties: Record<string, never>;
        metrics: {
            addedMemoriesCount: number;
            initialMemoriesCount: number;
            finalMemoriesBeforeOptimizationCount: number;
            finalMemoriesAfterOptimizationCount: number;
            retrievedMemoriesCount: number;
        };
        restrictedProperties: Record<string, never>;
    }
>;

/**
 * Defines a strategy for storing and reading memories.
 *
 * Strategies must support storing, retrieving, and consolidating memories. Consolidation typically happens
 * on shutdown or via some asynchronous background process.
 */
interface MemoryStrategy {
    storeMemory(memory: Memory, client: Client): Promise<ToolResult>;
    readMemory(question: Question, client: Client): Promise<ToolResult>;
    shutdown(): Promise<MemoryToolTelemetry>;
}

/**
 * Tools for reading and writing memories learned by the agent.
 *
 * @see MemoryStrategy is the underlying implementation used to store, retrieve, and consolidate memories.
 */
export class MemoryContext {
    constructor(private readonly memoryStrategy: MemoryStrategy) {}

    public getStoreMemory(): Tool {
        return {
            name: STORE_MEMORY_TOOL_NAME,
            description: `Store a fact about the codebase in memory, so that it can be used in future code generation or review tasks. The fact should be a clear and concise statement about the codebase conventions, structure, logic, or usage. It may be based on the code itself, or on information provided by the user.`,
            input_schema: {
                type: "object",
                properties: {
                    subject: {
                        type: "string",
                        description:
                            "The topic to which this memory relates. 1-2 words. Examples: 'naming conventions', 'testing practices', 'documentation', 'logging', 'authentication', 'sanitization', 'error handling'.",
                    },
                    fact: {
                        type: "string",
                        description:
                            "A clear and short description of a fact about the codebase or a convention used in the codebase. Must be less than 200 characters. Examples: 'Use JWT for authentication.', 'Follow PEP 257 docstring conventions.', 'Use single quotes for strings in Python.', 'Use Winston for logging.'",
                    },
                    source: {
                        type: "string",
                        description:
                            "The source of this fact, such as a file and line number in the codebase (e.g., 'path/file.go:123'). If the convention is not explicitly stated in the codebase, you can point at several examples that illustrate it, selecting the most diverse set of examples you can find (e.g. from multiple files or contexts). If the fact is based on user input, the source should be 'User input: ' followed by the contents of the input.",
                    },
                    reason: {
                        type: "string",
                        description:
                            "A clear and detailed explanation of the reason behind storing this fact. Must be at least 2-3 sentences long, and include which future tasks this fact will be useful for and why it is important to remember this fact.",
                    },
                    category: {
                        type: "string",
                        description:
                            "The type of memory being stored. This can be 'bootstrap_and_build' (info about how to bootstrap and build the project), 'user_preferences' (preferences of the user, such as coding style, tabs vs. spaces, favorite libraries, etc.), 'general' (file independent facts), or 'file_specific' (information about a specific file in the codebase).",
                    },
                },
                required: ["subject", "fact", "source", "reason", "category"],
            },
            callback: this.storeMemory.bind(this),
            shutdown: this.shutdown.bind(this),
            safeForTelemetry: true,
        };
    }

    public getReadMemory(): Tool {
        return {
            name: READ_MEMORY_TOOL_NAME,
            description: `Searches facts learned in previous sessions and stored with ${STORE_MEMORY_TOOL_NAME} for an answer to a question.
            * Facts include, but are not limited to, information about repo build and bootstrap steps, the codebase conventions, structure, logic, usage, as well as learned work-arounds and troubleshooting steps.
            * Use ${READ_MEMORY_TOOL_NAME} often as it is a faster, more efficient, and more effective way to retrieve information than searching the codebase. Use it instead of grep and other search tools, when possible.
            * Reference all relevant topic, specific file names, and concepts that you want to learn about in the question. For example, this question references 3 closely related topics: "What is the recommended way to bootstrap, build, and validate the project?".
            * The returned answer will be in natural language and may be derived from multiple facts.`,
            input_schema: {
                type: "object",
                properties: {
                    question: {
                        type: "string",
                        description:
                            "A question about the codebase. It should reference by name any topics, specific file names, and concepts that are relevant to the question.",
                    },
                },
                required: ["question"],
            },
            callback: this.readMemory.bind(this),
            shutdown: this.shutdown.bind(this),
            safeForTelemetry: true,
        };
    }

    private async storeMemory(inputs: unknown, options?: ToolCallbackOptions): Promise<ToolResult> {
        // Make sure the model isn't trying to store secrets before proceeding.
        inputs = SecretFilter.getInstance().filterSecretsFromObj(inputs);

        if (!isMemory(inputs)) {
            return {
                resultType: "failure",
                textResultForLlm: `Invalid inputs for ${STORE_MEMORY_TOOL_NAME} tool. Expected an object with properties: subject, fact, source, reason, category.`,
                toolTelemetry: {},
            };
        }

        if (!options?.client) {
            return {
                resultType: "failure",
                textResultForLlm: "Unable to store memory due to configuration error.",
                toolTelemetry: {},
            };
        }

        return this.memoryStrategy.storeMemory(inputs, options.client);
    }

    private async readMemory(inputs: unknown, options?: ToolCallbackOptions): Promise<ToolResult> {
        if (!isQuestion(inputs)) {
            return {
                resultType: "failure",
                textResultForLlm: `Invalid inputs for ${READ_MEMORY_TOOL_NAME} tool. Expected an object with a "question" property of type string.`,
                toolTelemetry: {},
            };
        }

        if (!options?.client) {
            return {
                resultType: "failure",
                textResultForLlm: "Unable to read memory due to configuration error.",
                toolTelemetry: {},
            };
        }

        return this.memoryStrategy.readMemory(inputs, options.client);
    }

    private shutdown(): Promise<TelemetryEvent> {
        return this.memoryStrategy.shutdown();
    }
}

/**
 * @see MemoryStrategy that writes memories to a JSON file in the `.github` directory of the repository
 * and consolidates and deduplicates them using LLM calls.
 *
 * This strategy will be used to get a baseline measurement of the impact of memory on the agent's performance.
 * It is expected to be flighted only for some internal users and used in benchmarks but likely will be replaced
 * by a cloud served version before being flighted to external users.
 */
export class JsonFileMemoryStrategy implements MemoryStrategy {
    private readonly memories: Memory[] = [];

    private client?: Client;

    private readonly telemetryEvent: MemoryToolTelemetry = {
        kind: "telemetry",
        telemetry: {
            event: "memory_tool",
            properties: {},
            restrictedProperties: {},
            metrics: {
                addedMemoriesCount: 0,
                initialMemoriesCount: 0,
                finalMemoriesBeforeOptimizationCount: 0,
                finalMemoriesAfterOptimizationCount: 0,
                retrievedMemoriesCount: 0,
            },
        },
    };

    private initializedSuccessfully?: boolean = undefined;

    constructor(private readonly repoRoot: string) {}

    public async storeMemory(memory: Memory, client: Client): Promise<ToolResult> {
        this.client ??= client;
        if (!(await this.tryEnsureInitialized())) {
            return {
                resultType: "failure",
                textResultForLlm: "Unable to store memory due to memory initialization error.",
                toolTelemetry: {},
            };
        }

        this.telemetryEvent.telemetry.metrics.addedMemoriesCount++;

        this.memories.push(memory);

        return {
            resultType: "success",
            textResultForLlm: `Memory stored successfully.`,
            toolTelemetry: {},
        };
    }

    public async readMemory(question: Question, client: Client): Promise<ToolResult> {
        this.client ??= client;

        if (!(await this.tryEnsureInitialized())) {
            return {
                resultType: "failure",
                textResultForLlm: "Unable to read memory due to memory initialization error.",
                toolTelemetry: {},
            };
        }

        if (this.memories.length === 0) {
            return {
                resultType: "failure",
                textResultForLlm: "No memories found.",
                toolTelemetry: {},
            };
        }

        // Prepare memories response.
        this.telemetryEvent.telemetry.metrics.retrievedMemoriesCount = this.memories.length;
        const memoryText = await this.memoryLookup(client, question.question);

        return {
            resultType: "success",
            textResultForLlm: memoryText,
            toolTelemetry: {
                metrics: {
                    retrievedMemoriesCount: this.telemetryEvent.telemetry.metrics.retrievedMemoriesCount,
                },
            },
        };
    }

    public async shutdown(): Promise<MemoryToolTelemetry> {
        try {
            // Ensure we have initialized the memories so we can send valid telemetry.
            // NOTE -- optimization won't work since we won't have a client available.
            await this.tryEnsureInitialized();

            this.telemetryEvent.telemetry.metrics.finalMemoriesBeforeOptimizationCount = this.memories.length;

            // Do an initial consolidation of the memory before writing it to disk.
            // If no client is available (e.g., storeMemory was never called), preserve existing memories
            const memories = this.client ? await this.optimizeMemories(this.client) : this.memories;
            this.telemetryEvent.telemetry.metrics.finalMemoriesAfterOptimizationCount = memories.length;

            const stringifiedMemories = memories.map((memory) => JSON.stringify(memory)).join("\n");

            const memoryFilePath = await this.getMemoryFilePath();
            await fs.writeFile(memoryFilePath, stringifiedMemories, {
                encoding: "utf8",
            });
        } catch (error: unknown) {
            addError(this.telemetryEvent, error, "writeMemoriesError");
        }

        return this.telemetryEvent;
    }

    private async tryEnsureInitialized(): Promise<boolean> {
        // Don't re-initialize if we've tried before.
        // - Undefined means we haven't tried to initialize yet.
        // - True means we successfully initialized.
        // - false means we failed to initialize.
        if (this.initializedSuccessfully !== undefined) {
            return this.initializedSuccessfully;
        }

        try {
            const memoryFilePath = await this.getMemoryFilePath();

            if (existsSync(memoryFilePath)) {
                const buffer = await fs.readFile(memoryFilePath, {
                    encoding: "utf8",
                });

                buffer
                    .split("\n")
                    .filter((line) => line.trim().length > 0)
                    .forEach((line) => {
                        this.memories.push(JSON.parse(line));
                    });
            }

            this.telemetryEvent.telemetry.metrics.initialMemoriesCount = this.memories.length;
            this.initializedSuccessfully = true;
        } catch (error: unknown) {
            this.initializedSuccessfully = false;
            addError(this.telemetryEvent, error, "readMemoriesError");
            this.telemetryEvent.telemetry.metrics.initialMemoriesCount = -1;
        }

        return this.initializedSuccessfully;
    }

    private async getMemoryFilePath(): Promise<string> {
        const githubDirPath = path.join(this.repoRoot, ".github");

        // Ensure the .github directory exists.
        await fs.mkdir(githubDirPath, { recursive: true });

        return path.join(githubDirPath, "copilot-memories.jsonl");
    }

    private async optimizeMemories(client: Client): Promise<Memory[]> {
        // Any errors in this function will be caught by the shutdown method and reported as telemetry.

        const itemsToConsolidate = this.memories.map((memory) => JSON.stringify(memory)).join("\n");

        const newMemories: Memory[] = [];

        const responses = client.getCompletionWithTools(
            memoryConsolidationPrompt(itemsToConsolidate),
            [
                {
                    role: "user",
                    content: "Please consolidate the memory items.",
                },
            ],
            [],
        );

        for await (const response of responses) {
            if (response.kind === "response" && response.response.content) {
                // If the response is malformed JSON, the error will bubble up to shutdown and get
                // logged. Let's see how this does in production and consider adding retry logic if needed.
                newMemories.push(
                    ...response.response.content
                        .split("\n")
                        .map((entry) => JSON.parse(entry))
                        .filter(isMemory),
                );
            }
        }

        return newMemories;
    }

    /**
     * Trivial 'memory lookup' procedure that just relies on the LLM having an exceptionally long context
     * window to do the actual sifting through and paring down of the memories to the response to send back
     * to the outer loop.
     *
     * Future considerations:
     * - This strategy -- we'll need to set a max number of memories to retain to avoid exceeding the context window.
     *
     * - Simpler strategy -- skip the LLM call and just return all the memories as a string.
     *
     * - Medium large context store -- could use multiple LLM calls to do a linear search through the memories
     *   to find the most relevant ones.
     *
     * - Very large context store -- could use a vector store to do a similarity search through the memories.
     */
    private async memoryLookup(client: Client, question: string): Promise<string> {
        const facts = this.memories.map((memory) => JSON.stringify(memory)).join("\n");

        const responses = client.getCompletionWithTools(
            memoryLookupPrompt(facts, question),
            [
                {
                    role: "user",
                    content: "Please answer the question using the facts provided.",
                },
            ],
            [],
        );

        const responseLines = [];

        for await (const response of responses) {
            if (response.kind === "response" && response.response.content) {
                responseLines.push(...response.response.content.split("\n"));
            }
        }

        return responseLines.join("\n");
    }
}
