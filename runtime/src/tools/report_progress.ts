/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { IAgentCallback } from "../callbacks/callback";
import { Log<PERSON><PERSON>l, RunnerLogger } from "../runner";
import { RunnerExec } from "../runner/exec";
import { GitHandler } from "../runner/git";
import { DefaultGitHandler } from "../runner/git/default";
import { RuntimeSettings } from "../settings";
import { Tool, ToolConfig, ToolResultExpanded } from "./index";
import { strLenTruncate } from "./util";

export type ReportProgressInput = {
    commitMessage: string;
    prDescription: string;
};

// Export to allow prompt testing
export const reportProgressToolDescription = (issueNumber: number) => {
    const includeFixNumberPrompt =
        issueNumber > 0
            ? `\n* Include "Fixes #${issueNumber}." as the last line of the body in the PR description.`
            : "";
    return `Report progress on the task. Call when you complete a meaningful unit of work. Commits and pushes changes that are pending in the repo, then updates the PR description.
* Use this tool at least once, and as early as possible once you've established a plan. Outline the complete plan as a checklist.
* Use only when you have meaningful progress to report (you need to update the plan in the checklist, you have code changes to commit, or you have completed a new item in the checklist)
* Use markdown checklists to show progress (- [x] for completed items, - [ ] for pending items).
* Keep the checklist structure as consistent as you can between updates, while still being accurate and useful.${includeFixNumberPrompt}
* Don't use headers in the PR description, just the checklist.
* If there are changes in the repo this tool will run \`git add .\`, \`git commit -m <msg>\`, and \`git push\`.`;
};
export const report_progress = (
    config: ToolConfig,
    issueNumber: number,
    settings: RuntimeSettings,
    logger: RunnerLogger,
    exec: RunnerExec,
): Tool => ({
    name: "report_progress",
    description: reportProgressToolDescription(issueNumber),
    input_schema: {
        type: "object",
        properties: {
            commitMessage: {
                type: "string",
                description: "A short single line of text to use as the commit message",
            },
            prDescription: {
                type: "string",
                description: "A description of work completed and remaining, using markdown checklists",
            },
        },
        required: ["commitMessage", "prDescription"],
    },
    callback: async (input: ReportProgressInput): Promise<ToolResultExpanded> => {
        const gitHandler = new DefaultGitHandler(logger, exec);

        logger.startGroup(input.commitMessage, LogLevel.Debug);
        logger.debug(input.prDescription);
        logger.endGroup(LogLevel.Debug);

        if (!config.branchName) {
            throw new Error("branchName is required for report_progress tool");
        }
        const progressOutput = await handleProgressReport(
            config.location,
            input.commitMessage,
            input.prDescription,
            config.branchName,
            config.push ?? false,
            settings,
            logger,
            gitHandler,
            config.callback,
        );
        const trueOutput = strLenTruncate(
            `${progressOutput ? `${progressOutput}\n\n` : "No changes to commit and push.\n"}Progress reported successfully.`,
            "output",
        );

        return {
            textResultForLlm: trueOutput,
            resultType: "success",
            sessionLog: trueOutput,
            toolTelemetry: {},
        };
    },
    safeForTelemetry: true,
});

async function handleProgressReport(
    location: string,
    commitMessage: string,
    prDescription: string,
    branchName: string,
    push: boolean,
    settings: RuntimeSettings,
    logger: RunnerLogger,
    gitHandler: GitHandler,
    callback?: IAgentCallback,
): Promise<string> {
    let output = "";
    try {
        const gitAction = push ? gitHandler.commitAndPushChanges : gitHandler.commitChanges;
        output += await gitAction.bind(gitHandler)(settings, branchName, location, commitMessage, false);
    } catch (error) {
        logger.error(`Error committing ${push ? "and pushing " : ""}changes: ${error}`);
        output += `Error committing ${push ? "and pushing " : ""} changes.`;
    }

    try {
        await callback?.partialResult({
            branchName,
            message: prDescription,
        });
    } catch (error) {
        logger.error(`Error reporting progress: ${error}`);
    }
    return output;
}
