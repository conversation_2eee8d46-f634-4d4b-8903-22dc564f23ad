/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { Client as McpClient } from "@modelcontextprotocol/sdk/client/index.js";
import axios, { AxiosInstance } from "axios";
import * as fs from "fs";
import { applyContentFilter, ContentFilterMode, filterAllJsonFields } from "../helpers/content-filter";
import { isKnownMCPTool } from "../mcp-client/known-mcp-servers";
import type { Tool as McpTool } from "../mcp-client/mcp-registry";
import { RunnerLogger } from "../runner";
import { RuntimeSettings } from "../settings";
import { isFeatureFlagEnabled } from "../settings/types";
import {
    alwaysProceedToolConfirmationCallback,
    Tool as RuntimeTool,
    shouldHalt,
    ToolConfirmationCallback,
    ToolResultExpanded,
    toolResultFromHalt,
} from "./index";
import { strLenTruncate } from "./util";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type InvokeToolResponseData = { isToolError: boolean; content: any[] };

type BasicToolConfig = {
    name: string;
    description: string;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    input_schema: any;
    readOnly?: boolean;
    safeForTelemetry?: RuntimeTool["safeForTelemetry"] & McpTool["safeForTelemetry"];
    filterMode?: ContentFilterMode;
};

export const CONTENT_TYPE_IMAGE = "image";
export const CONTENT_TYPE_TEXT = "text";
export const CONTENT_TYPE_RESOURCE = "resource";

// Double the default MCP timeout to reduce timeout errors (MCP error -32001).
// Telemetry indicates ~8% of Playwright tool calls timeout with default settings,
// primarily during navigate and browser installation.
const MCP_TOOL_CALL_TIMEOUT_MS = 60 * 1000 * 2;
export const DEFAULT_REQUEST_OPTIONS = {
    timeout: MCP_TOOL_CALL_TIMEOUT_MS,
};

export abstract class MCPTransport<ToolsProviderT = unknown> {
    constructor(
        protected readonly settings: RuntimeSettings,
        protected readonly logger: RunnerLogger,
        protected readonly toolConfirmationCallback: ToolConfirmationCallback,
    ) {
        // no-op
    }

    public async invokeTool(
        toolId: string,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        toolParams: any,
        filterMode: ContentFilterMode = ContentFilterMode.HiddenCharacters,
    ): Promise<ToolResultExpanded> {
        const responseData = await this.doInvokeTool(toolId, toolParams);
        return this.invokeToolResponseToToolResult(responseData, filterMode);
    }

    /**
     * Allows us to directly invoke a tool, which could be one that is suppressed from the tool list.
     * - Response can be either a text or a Base64 encoded image based on mcp-client/src/MCPServer.ts implementation of the invoke-tool endpoint.
     * - If `isToolError` is true, it means the tool call failed.
     */
    protected abstract doInvokeTool(
        toolId: string,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        toolParams: any,
    ): Promise<InvokeToolResponseData>;

    protected invokeToolResponseToToolResult(
        responseData: InvokeToolResponseData,
        filterMode: ContentFilterMode,
    ): ToolResultExpanded {
        // All text content is returned as a single string
        const content = responseData.content || [];

        let result = "";
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const binaryResults: any[] = [];

        for (const item of content) {
            if (item.type === "text") {
                result += item.text || "";
            } else if (item.type === "resource") {
                // Handle embedded resources
                const resource = item.resource;
                if (resource && resource.blob) {
                    binaryResults.push({
                        type: CONTENT_TYPE_RESOURCE,
                        data: resource.blob,
                        mimeType: resource.mimeType || "application/octet-stream",
                    });
                } else if (resource && resource.text) {
                    result += resource.text;
                }
            } else if (item.type === "image") {
                binaryResults.push({
                    type: CONTENT_TYPE_IMAGE,
                    data: item.data,
                    mimeType: item.mimeType,
                });
            }
        }

        let filteredResult: string = result;

        if (isFeatureFlagEnabled(this.settings, "copilot_swe_agent_mcp_filtering")) {
            if (result === null || result === undefined || result === "") {
                this.logger.debug(`Tool invocation result is empty or undefined, skipping content filtering.`);
                filteredResult = result;
            } else {
                try {
                    const parsedJson = JSON.parse(result);
                    const filteredJson = filterAllJsonFields(parsedJson, filterMode);
                    filteredResult = JSON.stringify(filteredJson);
                } catch (e) {
                    this.logger.debug(
                        `Unable to parse tool invocation as JSON. Treating it as a string for filtering: ${e}`,
                    );
                    filteredResult = applyContentFilter(result, filterMode);
                }
            }
        }
        this.logger.debug(`Tool invocation result: ${filteredResult}`);

        const toolTelemetry = {};
        const sessionLog = filteredResult ? strLenTruncate(filteredResult, "output") : "";

        if (responseData.isToolError) {
            return {
                textResultForLlm: filteredResult,
                resultType: "failure",
                error: filteredResult,
                sessionLog: sessionLog,
                toolTelemetry: toolTelemetry,
            };
        } else {
            return {
                textResultForLlm: filteredResult,
                binaryResultForLlm: binaryResults,
                resultType: "success",
                sessionLog: sessionLog,
                toolTelemetry: toolTelemetry,
            };
        }
    }

    async loadTools(provider: ToolsProviderT): Promise<RuntimeTool[]> {
        const toolsFromProvider = await this.loadToolsFromProvider(provider);

        const tools: Record<string, RuntimeTool> = {};
        for (const [toolId, toolConfig] of Object.entries(toolsFromProvider) as [string, McpTool][]) {
            const filterMode = toolConfig.filterMode || ContentFilterMode.HiddenCharacters;
            tools[toolId] = {
                name: toolConfig.name,
                description: toolConfig.description,
                input_schema: toolConfig.input_schema,
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                callback: async (input: any): Promise<ToolResultExpanded> => {
                    // Read-only tools are treated as safe, not requiring confirmation.
                    if (!toolConfig.readOnly) {
                        const confirmationResult = await this.toolConfirmationCallback({
                            type: "mcp",
                            toolName: toolConfig.name,
                            args: input,
                        });

                        if (shouldHalt(confirmationResult)) {
                            return toolResultFromHalt(confirmationResult);
                        }
                    }

                    return await this.invokeTool(toolId, input, filterMode);
                },
                safeForTelemetry: toolConfig.safeForTelemetry,
            };
        }
        return Object.values(tools);
    }

    protected abstract loadToolsFromProvider(provider: ToolsProviderT): Promise<Record<string, BasicToolConfig>>;
}

export class OutOfProcMCPTransport extends MCPTransport<string> {
    private readonly httpClient: AxiosInstance;

    constructor(
        settings: RuntimeSettings,
        logger: RunnerLogger,
        toolConfirmationCallback: ToolConfirmationCallback = alwaysProceedToolConfirmationCallback,
        port: number = 2301,
    ) {
        super(settings, logger, toolConfirmationCallback);
        this.httpClient = axios.create({
            baseURL: `http://localhost:${port}`,
        });
    }

    /**
     * Check if an MCP server with the given name has registered tools.
     * This reads the MCP config file and looks for tools with the specified server name prefix.
     * @param configPath Path to the MCP config JSON file
     * @param serverName Name of the MCP server to check for
     * @returns true if the server has registered tools, false otherwise
     */
    public hasRegisteredServer(configPath: string, serverName: string): boolean {
        try {
            if (!fs.existsSync(configPath)) {
                this.logger.debug(`Config file not found at path: ${configPath}`);
                return false;
            }

            const configContent = fs.readFileSync(configPath, "utf-8");
            const toolsConfig = JSON.parse(configContent);

            // Check if any tool has the server name prefix in its key
            // Tools are stored with keys like "serverName/toolName" according to the MCPRegistry
            const serverPrefix = `${serverName}/`;
            const hasServerTools = Object.keys(toolsConfig).some((toolKey) => toolKey.startsWith(serverPrefix));

            this.logger.debug(`Server '${serverName}' has registered tools: ${hasServerTools}`);
            return hasServerTools;
        } catch (error) {
            this.logger.debug(`Error checking for registered server '${serverName}': ${error}`);
            return false;
        }
    }

    protected async doInvokeTool(
        toolId: string,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        toolParams: any,
    ): Promise<InvokeToolResponseData> {
        try {
            this.logger.info(`Invoking tool: ${toolId} with params: ${JSON.stringify(toolParams)}`);
            const response = await this.httpClient.post("/invoke-tool", {
                toolId: toolId,
                params: toolParams,
            });
            return response.data as InvokeToolResponseData;
        } catch (e) {
            this.logger.error(`Error invoking tool ${toolId} with error ${e}`);
            if (axios.isAxiosError(e)) {
                this.logger.error(`Axios error when invoking ${toolId} with error  ${e.message}`);
            }
            throw e;
        }
    }

    protected async loadToolsFromProvider(configPath: string): Promise<Record<string, BasicToolConfig>> {
        this.logger.info(`Loading tools from config file: ${configPath}`);

        if (!fs.existsSync(configPath)) {
            this.logger.info(`Config file not found at path: ${configPath}`);
            return {};
        }
        const configContent = fs.readFileSync(configPath, "utf-8");
        const toolsConfig = JSON.parse(configContent);

        const mcpTools: Record<string, BasicToolConfig> = {};
        for (const [toolId, toolConfig] of Object.entries(toolsConfig) as [string, McpTool][]) {
            mcpTools[toolId] = {
                name: toolConfig.name,
                description: toolConfig.description,
                input_schema: toolConfig.input_schema,
                readOnly: toolConfig.readOnly,
                safeForTelemetry: toolConfig.safeForTelemetry,
                filterMode: toolConfig.filterMode || ContentFilterMode.HiddenCharacters,
            };
        }
        return mcpTools;
    }
}

export type ClientInfo = {
    clientName: string;
    mcpClient: McpClient;
    safeForTelemetry?: RuntimeTool["safeForTelemetry"];
};

export class InProcMCPTransport extends MCPTransport<ClientInfo> {
    private toolIdToClientInfo: Map<string, ClientInfo> = new Map();

    constructor(
        settings: RuntimeSettings,
        logger: RunnerLogger,
        toolConfirmationCallback: ToolConfirmationCallback = alwaysProceedToolConfirmationCallback,
    ) {
        super(settings, logger, toolConfirmationCallback);
    }

    protected async doInvokeTool(
        toolId: string,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        toolParams: any,
    ): Promise<InvokeToolResponseData> {
        const clientInfo = this.toolIdToClientInfo.get(toolId);
        if (!clientInfo) {
            throw new Error(`No MCP client found for tool ID: ${toolId}`);
        }
        const mcpClient = clientInfo.mcpClient;

        // Remove client name prefix
        const toolName = InProcMCPTransport.getToolNameFromIdAndClientName(toolId, clientInfo.clientName);

        const result: Awaited<ReturnType<typeof mcpClient.callTool>> = await mcpClient.callTool({
            name: toolName,
            arguments: toolParams,
            requestOptions: DEFAULT_REQUEST_OPTIONS,
        });

        if (!Array.isArray(result.content)) {
            throw new Error("Expected array of results");
        }

        return {
            content: result.content,
            isToolError: result.error !== undefined && result.error !== null,
        };
    }

    protected async loadToolsFromProvider(clientInfo: ClientInfo): Promise<Record<string, BasicToolConfig>> {
        const mcpTools: Record<string, BasicToolConfig> = {};
        this.logger.debug(`Loading tools for client: ${clientInfo.clientName}`);
        for (const mcpTool of (await clientInfo.mcpClient.listTools()).tools) {
            const toolId = InProcMCPTransport.getToolIdFromClientAndToolName(clientInfo.clientName, mcpTool.name);

            this.toolIdToClientInfo.set(toolId, clientInfo);

            let safeForTelemetry: {
                name: boolean;
                inputsNames: boolean;
            };

            if (clientInfo.safeForTelemetry === true) {
                safeForTelemetry = {
                    name: true,
                    inputsNames: true,
                };
            } else if (clientInfo.safeForTelemetry && typeof clientInfo.safeForTelemetry === "object") {
                safeForTelemetry = clientInfo.safeForTelemetry;
            } else {
                const toolIsKnown = isKnownMCPTool(clientInfo.clientName, mcpTool.name);

                // Since knowing if a tool is safe for telemetry only inspects the tool name, do not mark inputs names as safe for telemetry.
                safeForTelemetry = {
                    name: toolIsKnown,
                    inputsNames: false,
                };
            }

            this.logger.debug(`Adding tool: ${toolId}`);

            mcpTools[toolId] = {
                name: toolId,
                description: mcpTool.description || "",
                input_schema: mcpTool.inputSchema,
                readOnly: mcpTool.annotations?.readOnlyHint,
                safeForTelemetry: safeForTelemetry,
                filterMode: ContentFilterMode.HiddenCharacters,
            };
        }

        return mcpTools;
    }

    private static getToolIdFromClientAndToolName(clientName: string, toolName: string): string {
        return `${clientName}-${toolName}`;
    }

    private static getToolNameFromIdAndClientName(toolId: string, clientName: string): string {
        return toolId.substring(clientName.length + 1);
    }
}
