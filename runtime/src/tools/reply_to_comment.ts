/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { IAgentCallback } from "../callbacks/callback";
import { Log<PERSON>eve<PERSON>, RunnerLogger } from "../runner";
import { Tool, ToolConfig } from "./index";

export const REPLY_TO_COMMENT_TOOL_NAME = "reply_to_comment";

export const reply_to_comment = (config: ToolConfig, logger: RunnerLogger): Tool => ({
    name: REPLY_TO_COMMENT_TOOL_NAME,
    description: `Reply to new comments. Use when you've completed a meaningful unit of work that specifically addresses the request, question, or feedback in the comment.
    * Use this tool no more than once per <comment_id>, and only after you have completed meaningful work that specifically addresses the text in the comment.
    * Ignore comments with no comment_id. Do not make up comment_ids.
    * Keep your reply simple, focused, and concise. Don't use headers or tables in the reply.
    * Include the short hash of the commit that addresses the comment in the reply.
    * Do NOT reply to comments that are clearly directed at other users (e.g., "Hey @username, ..." or "@username can you ...").
    * Do NOT reply to comments that don't require a response or action from you.
    * Do NOT reply to comments that are just acknowledgments, thank you messages, or contain no actionable requests.
`,
    input_schema: {
        type: "object",
        properties: {
            reply: {
                type: "string",
                description: "A short reply to the comment that addresses the user's request, question, or feedback",
            },
            comment_id: {
                type: "number",
                description: "Id of the comment thread to reply to. Use the <comment_id> of the <comment_new>.",
            },
        },
        required: ["reply", "comment_id"],
    },
    callback: async (input: { reply: string; comment_id: number | string }): Promise<string> => {
        logger.startGroup(`Reply to comment ${input.comment_id}`, LogLevel.Debug);
        logger.debug(input.reply);
        logger.endGroup(LogLevel.Debug);

        // We've seen cases where the model passes a string instead of a number for comment_id (since we don't use
        // the Structured Outputs feature to ensure the input matches the schema), so in some cases we need to
        // parse the comment_id to a number.
        let commentId: number;
        try {
            commentId = typeof input.comment_id === "string" ? parseInt(input.comment_id, 10) : input.comment_id;
            if (isNaN(commentId)) {
                return `error: invalid comment_id provided. Expected a valid number but received: ${input.comment_id}`;
            }
        } catch (error) {
            return `error: failed to parse comment_id as a number: ${error}`;
        }

        await handleCommentReply(commentId, input.reply, logger, config.callback);
        return `Replied to comment_id ${input.comment_id}.`;
    },
    safeForTelemetry: true,
});

async function handleCommentReply(comment_id: number, reply: string, logger: RunnerLogger, callback?: IAgentCallback) {
    try {
        await callback?.commentReply({
            comment_id,
            message: reply,
        });
    } catch (error) {
        logger.error(`Error replying to comment: ${error}`);
    }
}
