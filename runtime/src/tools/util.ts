/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { isAbsolute, resolve } from "path";
import { ToolCallbackOptions } from ".";
import { pathExists } from "../helpers/file-reader";
import { RunnerExec } from "../runner/exec";
import { envVarNamesToFilterFromBash } from "../settings/environment-settings";

export const defaultStrLenTruncateLimit = 10 * 1024; // 10KB

/**
 * Truncates a given string based on its type (file, directory, or output) and a specified token limit.
 *
 * - For single-line strings, it removes characters from either the middle or the end, depending on the `truncateStyle`.
 * - For multi-line strings, it removes whole lines from either the middle or the end, depending on the `truncateStyle`.
 *
 * @param str - The string to be truncated.
 * @param kind - The type of content being truncated.
 * @param truncationOptions - The truncation options to use.
 * @param contentLen - The maximum length of the content to retain.
 * @param truncateStyle - The truncation style to use when the content is too long.
 * @returns - The truncated string with a message indicating the truncation.
 */
export function tokenCountTruncate(
    str: string,
    kind: "file" | "directory" | "output",
    truncationOptions: Required<ToolCallbackOptions>["truncationOptions"],
    truncateStyle: "middle" | "end" = "middle",
): string {
    const tokensInStr = truncationOptions.countTokens(str);
    const tokenLimit = truncationOptions.tokenLimit;
    if (tokensInStr > tokenLimit) {
        const keepRatio = tokenLimit / tokensInStr;
        const contentLen = Math.floor(str.length * keepRatio);
        return strLenTruncate(str, kind, contentLen, truncateStyle);
    } else {
        return str;
    }
}

/**
 * Truncates a given string based on its type (file, directory, or output) and a specified content length.
 *
 * - For single-line strings, it removes characters from either the middle or the end, depending on the `truncateStyle`.
 * - For multi-line strings, it removes whole lines from either the middle or the end, depending on the `truncateStyle`.
 *
 * @param str - The string to be truncated.
 * @param kind - The type of content being truncated.
 * @param contentLen - The maximum length of the content to retain.
 * @param truncateStyle - The truncation style to use when the content is too long.
 * @returns - The truncated string with a message indicating the truncation.
 */
export function strLenTruncate(
    str: string,
    kind: "file" | "directory" | "output",
    contentLen: number = defaultStrLenTruncateLimit,
    truncateStyle: "middle" | "end" = "middle",
): string {
    // If string is shorter than contentLen, return it as-is
    if (str.length <= contentLen) {
        return str;
    }

    // Detect if string has multiple lines by checking for new lines.
    // Carriage returns are not considered, as they are followed by new lines.
    const lines = str.split("\n");
    const isMultiLine = lines.length > 1;

    if (!isMultiLine) {
        if (truncateStyle === "middle") {
            return (
                str.slice(0, contentLen / 2) +
                `<${kind} too long - dropped ${str.length - contentLen} characters from the middle>` +
                str.slice(str.length - contentLen / 2)
            );
        } else {
            return (
                str.slice(0, contentLen) +
                `<${kind} too long - dropped ${str.length - contentLen} characters from the end>`
            );
        }
    } else {
        // Keep track of which lines to remove
        const removeLines = new Set<number>([]);
        const lengthOfRemovedLines = () =>
            Array.from(removeLines)
                // Add 1 to account for the newline character which will be added back later
                .reduce((acc, i) => acc + lines[i].length + 1, 0);

        if (truncateStyle === "middle") {
            // For multiline strings, start from the middle and work outwards
            const middleIndex = Math.floor(lines.length / 2);
            // Start with the middle line
            removeLines.add(middleIndex);

            let aboveMiddleLinesRemoved = 0;
            let belowMiddleLinesRemoved = 0;
            while (str.length - lengthOfRemovedLines() > contentLen) {
                let nextIdxToRemoveAdj;
                if (aboveMiddleLinesRemoved === belowMiddleLinesRemoved) {
                    aboveMiddleLinesRemoved++;
                    nextIdxToRemoveAdj = -aboveMiddleLinesRemoved;
                } else {
                    belowMiddleLinesRemoved++;
                    nextIdxToRemoveAdj = belowMiddleLinesRemoved;
                }

                const nextIdxToRemove = middleIndex + nextIdxToRemoveAdj;
                if (nextIdxToRemove <= 0 || nextIdxToRemove >= lines.length - 1) {
                    // Leave at least one line at the top & bottom
                    break;
                }
                removeLines.add(nextIdxToRemove);
            }
        } else {
            let nextLineFromEndToRemove = lines.length - 1;
            while (str.length - lengthOfRemovedLines() > contentLen) {
                removeLines.add(nextLineFromEndToRemove);
                nextLineFromEndToRemove--;

                if (nextLineFromEndToRemove <= 0) {
                    // Leave at least one line at the top
                    break;
                }
            }
        }

        let result = "";
        const numDroppedLines = removeLines.size;

        // Iterate through all lines to build the final truncated string
        let insertedTruncationMessage = false;
        for (let i = 0; i < lines.length; i++) {
            if (removeLines.has(i)) {
                if (!insertedTruncationMessage) {
                    insertedTruncationMessage = true;
                    result += `<${kind} too long - dropped ${numDroppedLines} lines from the ${truncateStyle}>\n`;
                }
                continue;
            }
            // Add the line and newline character (except for the last line)
            result += lines[i] + (i < lines.length - 1 ? "\n" : "");
        }

        return result;
    }
}

export async function applyPatch(exec: RunnerExec, patchString: string, repoPath: string): Promise<string> {
    try {
        const encoded = new TextEncoder().encode(patchString);
        const result = await exec.execReturn("git", ["apply", "--binary"], {
            cwd: repoPath,
            input: Buffer.from(encoded),
        });
        return result.stdout || result.stderr;
    } catch (error) {
        return `Error applying patch: ${(error as Error).message}`;
    }
}

export function getSafeEnvVarsForToolBashSession(): NodeJS.ProcessEnv {
    const bashEnv: { [key: string]: string } = {};
    for (const key in process.env) {
        if (!envVarNamesToFilterFromBash.includes(key) && process.env[key] !== undefined) {
            bashEnv[key] = process.env[key]!;
        }
    }
    return bashEnv;
}

export async function maybeResolvePathAgainstDir(path: string, dir: string): Promise<string> {
    if (isAbsolute(path)) {
        // Whatever the path is, if it is absolute, we cannot resolve it against `dir`
        return path;
    }

    const dirBasedPath = resolve(dir, path);
    const dirBasedPathExists = await pathExists(dirBasedPath);
    if (dirBasedPathExists) {
        return dirBasedPath;
    }

    return path;
}
