/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { type IPty, type spawn as spawnPty } from "node-pty";
import stripAnsi from "strip-ansi";

const COMMAND_OUTPUT_MARKER = "___BEGIN___COMMAND_OUTPUT_MARKER___";
const PRINTED_COMMAND_OUTPUT_MARKER = `\n${COMMAND_OUTPUT_MARKER}`;
const COMMAND_DONE_MARKER = "___BEGIN___COMMAND_DONE_MARKER___";
const COMMAND_DONE_MARKER_REGEX = new RegExp(`${COMMAND_DONE_MARKER}(\\d+)`);

const COMMAND_COMPLETION_POLL_INTERVAL_MS = 100;
const TRIVIAL_COMMAND_TIMEOUT_MS = 5_000;

export interface BashOutput {
    output: string;
    exitCode?: number;
}

export interface SessionStats {
    createdAtMs: number;
    lastUsedAtMs: number;
}

export async function canUseInteractiveBashSession(): Promise<boolean> {
    try {
        await import("node-pty");
        return true;
    } catch {
        return false;
    }
}

/**
 * Implements support for running bash commands synchronously with a timeout,
 * asynchronously with no timeout, or interactively by simulating a TTY.
 */
export class InteractiveBashSession {
    // The pseudo-terminal process. This class uses node-pty to simulate a terminal
    // instead of simply redirecting bash's stdin/stdout/stderr because it supports
    // additional valuable scenarios that are not possible with simple redirection
    // such as:
    //
    // - Sending keyboard input to running processes that require a TTY, like those
    //   built with node 'readline-sync' package.
    //
    // - Interacting with NCurses style applications, like the configuration tool
    //   that runs after installing 'tzdata' package with apt.
    //
    // - Directly using text editors like 'vim' or 'nano' in the terminal.
    //
    // For more information on TTYs vs. pipes see: https://stackoverflow.com/questions/26659595
    private process?: IPty;

    // Output buffer contains stdin, stdout, and stderr output from the bash process.
    private outputBuffer = "";

    // True if a command is in progress.
    private isCommandInProgress = false;

    // Session stats
    private readonly createdAtMs: number;
    private lastUsedAtMs: number;

    public static async create(startingCwd: string, env: NodeJS.ProcessEnv = {}): Promise<InteractiveBashSession> {
        // Lazy-load the node-pty module only when this function is called
        const nodePty = await import("node-pty");
        const session = new InteractiveBashSession(startingCwd, env, nodePty.spawn);
        await session.initializeShellEnvironment();
        return session;
    }

    private constructor(startingCwd: string, env: NodeJS.ProcessEnv = {}, spawn: typeof spawnPty) {
        // Initialize session stats
        this.createdAtMs = Date.now();
        this.lastUsedAtMs = Date.now();

        // Simulate a color terminal with a reasonable size and disable
        // bash profile and rc files to keep the environment clean.
        this.process = spawn("bash", ["--norc", "--noprofile"], {
            name: "xterm-color",
            cols: 120,
            rows: 80,
            cwd: startingCwd,
            env: env,
        });

        // Set up output collector.
        this.process.onData((data) => {
            // 'data' is a string containing stdin, stdout, and stderr output
            // as well as any ANSI escape codes for colors, formatting, and control.
            //
            // For this initial implementation, we will strip ANSI codes and
            // leave just the text. The model is usually clever enough to figure
            // out what control codes it needs to respond with from the text alone.
            //
            // In the future we could consider preserving the control codes
            // specifically for asynchronous commands that might require them
            // to understand subtleties of the output, like which NCurses button
            // is focused.
            //
            // This rudimentary TTY support also doesn't handle things like paging
            // or incremental updates to the buffer, so we might want to consider
            // using something like @xterm/headless in the future to turn the control
            // codes into a buffer closer to what a real terminal would do.
            const plainText = stripAnsi(data);

            // For some reason, node-pty sends \r\n instead of \n, even on Linux, so
            // normalize it to \n.
            this.outputBuffer += plainText.replaceAll("\r", "");
        });

        this.process.onExit((_) => this.shutdown());
    }

    /**
     * Synchronously executes a command in the bash process and waits for it to complete.
     * @param command The bash command to execute.
     * @param timeoutMs The amount of time in milliseconds to wait for the command to complete.
     * @returns The output of the command and its exit code or undefined if a command is already in progress.
     * @throws An error if the command times out or fails to execute in an unexpected way.
     */
    public async executeCommand(command: string, timeoutMs?: number): Promise<BashOutput | undefined> {
        this.updateLastUsedTime();

        if (!this.process) {
            throw new Error("Failed to start bash process");
        }

        // Try to start the command. This will fail if one is already in progress.
        if (!this.tryStartCommand(command)) {
            return undefined;
        }

        return await (timeoutMs ? this.waitForCommandCompletionOrTimeout(timeoutMs) : this.waitForCommandCompletion());
    }

    /**
     * Gets the current working directory of the bash session.
     *
     * @returns The current working directory as a `string`, or `undefined` if the
     * command fails.
     */
    public async cwd(): Promise<string | undefined> {
        try {
            // This will update last used time.
            const result = await this.executeCommand("pwd", TRIVIAL_COMMAND_TIMEOUT_MS);
            return result?.exitCode === 0 ? result.output : undefined;
        } catch {
            return undefined;
        }
    }

    /**
     * Starts a command and then returns immediately. The command will be executed in
     * the background with no timeout. Its output and/or exit code can be read
     * with 1 or more calls to @see readOutput.
     *
     * @param command The command to execute.
     * @returns True if the command was started successfully, false if a comand
     * is already running in this session.
     */
    public tryExecuteAsyncCommand(command: string): boolean {
        this.updateLastUsedTime();

        // Try to start the command. This will fail if one is already in progress.
        if (!this.tryStartCommand(command)) {
            return false;
        }

        // Start the completion waiter. This will update isCommandInProgress
        // when the command completes. We also need to swallow any errors
        // to avoid unhandled promise rejections.
        this.waitForCommandCompletion().catch((_) => {});

        return true;
    }

    /**
     * Sends input to the currently running command in the bash process.
     * @param input The input to send to the command, potentially including ANSI codes.
     * @returns True if the input was sent and false if no command is currently in progress.
     */
    public trySendInput(input: string): boolean {
        this.updateLastUsedTime();

        if (!this.process) {
            throw new Error("Failed to start bash process");
        }

        // If a command is already in progress, we should send input to the running process
        // instead of trying to start a new command.
        if (!this.isCommandInProgress) {
            return false;
        }

        this.process.write(input);
        return true;
    }

    /**
     * Reads the output of the last command queued for execution. Each subsequent call
     * will read just the new output since the last call.
     *
     * @returns The output of the command so far, and its exit code if it has completed.
     */
    public readOutput(): BashOutput | undefined {
        this.updateLastUsedTime();

        // If the command is done, we need to drop the marker. Otherwise return everything.
        const output = this.tryExtractOutputFromDoneMarker();
        if (output) {
            return output;
        }

        // Extract the output before marker. We need to start after the command text.
        //
        // Make sure we're matching the printed marker and not the echo command to avoid
        // a race condition when the timeout is short.
        const startMarkerIndex = this.outputBuffer.lastIndexOf(PRINTED_COMMAND_OUTPUT_MARKER);
        const outputString =
            startMarkerIndex > -1
                ? this.outputBuffer.substring(startMarkerIndex + PRINTED_COMMAND_OUTPUT_MARKER.length).trim()
                : "";

        return {
            output: outputString,
            exitCode: undefined,
        };
    }

    /**
     * Gets the session statistics.
     * @returns The session stats including creation time and last used time.
     */
    public getStats(): SessionStats {
        return {
            createdAtMs: this.createdAtMs,
            lastUsedAtMs: this.lastUsedAtMs,
        };
    }

    /**
     * Waits up to the specified timeout for the command to complete and then returns its output.
     *
     * @throws Throws a timeout error if the command does not complete within the specified timeout.
     *         Partial output can be read with @see readOutput.
     */
    public waitForCommandCompletionOrTimeout(timeoutMs: number): Promise<BashOutput> {
        const commandCompletionPromise = this.waitForCommandCompletion();
        const timeoutPromise = new Promise<BashOutput>((_, reject) => {
            setTimeout(() => {
                reject(new Error("Command timed out"));
            }, timeoutMs);
        });

        return Promise.race([commandCompletionPromise, timeoutPromise]);
    }

    public clearBuffer(): void {
        // Clear the command output buffer. We want to save this so that calls to readOutput()
        // return an empty string, but with the exit code of the last command.
        const endMarkerIndex = this.outputBuffer.lastIndexOf(COMMAND_DONE_MARKER);
        this.outputBuffer =
            endMarkerIndex > 0 ? `${COMMAND_OUTPUT_MARKER}\n${this.outputBuffer.substring(endMarkerIndex).trim()}` : "";
    }

    public shutdown(): void {
        if (this.process) {
            this.isCommandInProgress = false;
            try {
                this.process.kill();
            } catch {
                // Ignore kill errors
            }
            this.process = undefined;
            this.outputBuffer = "";
        }
    }

    private updateLastUsedTime(): void {
        this.lastUsedAtMs = Date.now();
    }

    private async initializeShellEnvironment(): Promise<void> {
        if (!this.process) {
            throw new Error("Failed to start bash process");
        }

        // Disable the bash prompt (PS1) and the '>' that gets inserted after a multiline command (PS2).
        await this.executeCommand('PS1="";PS2=""', TRIVIAL_COMMAND_TIMEOUT_MS);
    }

    /**
     * Starts a command. isCommandInProgress must be set to false after the command
     * completes or the session will be stuck in an unusable state.
     * @param command
     * @returns
     */
    private tryStartCommand(command: string): boolean {
        if (!this.process) {
            throw new Error("Failed to start bash process");
        }

        // Disallow starting a new command if one is already in progress.
        if (this.isCommandInProgress) {
            return false;
        }

        this.isCommandInProgress = true;

        // Run command in braces for grouping. We have a marker at the start
        // to indicate where the output begins and a marker at the end containing
        // our exit code.
        const commandText = `
            {
                echo ${COMMAND_OUTPUT_MARKER}
                ${command}
                EC=$?
                echo "${COMMAND_DONE_MARKER}$EC"
            }\n`;

        // Clear output buffers
        this.outputBuffer = "";

        this.process.write(commandText);

        return true;
    }

    private async waitForCommandCompletion(): Promise<BashOutput> {
        let output: BashOutput | undefined = undefined;

        // Wait loop until the command completes.
        for (
            output = this.tryExtractOutputFromDoneMarker();
            output === undefined && this.process;
            output = this.tryExtractOutputFromDoneMarker()
        ) {
            // Wait a bit before checking again and yield so other tasks can progress.
            await new Promise((resolve) => {
                setTimeout(resolve, COMMAND_COMPLETION_POLL_INTERVAL_MS);
            });
        }

        this.isCommandInProgress = false;

        if (output) {
            // Signal we're done and return the output.
            return output;
        } else {
            throw new Error("terminal unexpectedly exited before command completed");
        }
    }

    private tryExtractOutputFromDoneMarker(): BashOutput | undefined {
        // Check and see if the command is done and we can extract any remaining output
        // and the exit code.
        const startMarkerIndex = this.outputBuffer.lastIndexOf(COMMAND_OUTPUT_MARKER);
        const markerMatch = this.outputBuffer.match(COMMAND_DONE_MARKER_REGEX);
        if (startMarkerIndex < 0 || !markerMatch) {
            return undefined;
        }

        // Get index of marker.
        const endMarkerIndex = this.outputBuffer.lastIndexOf(COMMAND_DONE_MARKER);

        // Extract the output before marker. We need to start after the command text.
        const output = this.outputBuffer
            .substring(startMarkerIndex + COMMAND_OUTPUT_MARKER.length, endMarkerIndex)
            .trim();

        // Get exit code from captured group
        const exitCode = parseInt(markerMatch[1]);

        return {
            output: output,
            exitCode,
        };
    }
}
