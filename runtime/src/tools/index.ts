/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { IAgentCallback } from "../callbacks/callback";
import { Client } from "../model/client";
import { countTokensInString } from "../model/tokenCounting";
import { Telemetry, TelemetryEvent } from "../telemetry";

export * from "./interactiveBashTool";
export * from "./reply_to_comment";
export * from "./report_progress";
export * from "./str_replace_editor";
export * from "./think";

export type ToolResultExpanded<TelemetryT extends Telemetry = Telemetry> = {
    /**
     * The result to be given back to the LLM.
     *
     * If @see sessionLog is omitted, then this will be used as the session log.
     */
    textResultForLlm: string;

    /**
     * The result to be given back to the LLM. It can be either base64 encoded image or audio content.
     */
    binaryResultForLlm?: BinaryResult[];

    /**
     * Whether or not the result should be considered a success, failure, or previously interrupted.
     * - `success`: The tool executed successfully and produced a valid result.
     * - `failure`: The tool encountered an error or did not produce a valid result.
     * - `rejected`: The tool call was rejected either because the user didn't want this call, or a previous dependent one.
     */
    resultType: "success" | "failure" | "rejected";

    /**
     * If there was any sort of error that caused the tool to fail, then a string representation of the error. Typically
     * only set if {@link resultType} is `'failure'`.
     */
    error?: string;

    /**
     * Specific telemetry for the tool. Will be sent back to the server by the agent.
     */
    toolTelemetry: {
        properties?: TelemetryT["properties"];
        restrictedProperties?: TelemetryT["restrictedProperties"];
        metrics?: TelemetryT["metrics"];
    };

    /**
     * Well-formatted (typically Markdown) string that can be used to display the input/output of the tool invoked.
     *
     * (Optional) If omitted, the text result for the LLM will be used as the session log.
     */
    sessionLog?: string;
};

export type ToolResult = string | ToolResultExpanded;

export type BinaryResult = {
    data: string;
    mimeType: string;
    type: string;
};

export type ToolCallbackOptions<OptionsT = { [key: string]: unknown }> = {
    /**
     * If this tool call was initiated by an LLM, then the ID of that tool call.
     */
    toolCallId?: string;

    truncationOptions?: {
        /**
         * The number of tokens that the tool's response should ideally be limited to.
         */
        tokenLimit: number;
        /**
         * A function to count the number of tokens in a string.
         */
        countTokens: (input: string) => number;
    };
    /**
     * A client that the tool can use to make chat completion calls.
     */
    client?: Client;

    /**
     * Other options specific to the tool. Passed in from settings.
     */
    toolOptions?: OptionsT;
};

export function buildToolCallbackOptions(model: string, tokenLimit: number, client: Client): ToolCallbackOptions {
    return {
        truncationOptions: {
            tokenLimit: tokenLimit,
            countTokens: (str: string) => {
                return countTokensInString(str, model);
            },
        },
        client: client,
    };
}

/**
 * @param input The input to the tool
 * @param options Options for the tool, includes the standard `ToolCallbackOptions` as well as any additional options that were set for the tool in settings.
 */
export type ToolCallback = (
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    input: any,
    options?: ToolCallbackOptions,
) => Promise<ToolResult>;

/**
 * A callback to be called when the tool is shutting down. Gives the tool
 * a chance to clean things up, and return a telemetry event (if desired) which
 * will be emitted by the agent.
 */
export type ToolShutdown = () => Promise<TelemetryEvent | void>;

/**
 * What a tool intends to do if it is executed.
 */
export type ToolIntention =
    | { type: "exec"; title: string; command: string }
    | { type: "edit"; title: string; fileName: string; diff: string }
    | { type: "mcp"; toolName: string; args: unknown };

export function matchToolIntention<R>(
    intention: ToolIntention,
    fns: {
        onExec: (title: string, command: string) => R;
        onEdit: (title: string, fileName: string, diff: string) => R;
        onMcp: (toolName: string, args: unknown) => R;
    },
): R {
    switch (intention.type) {
        case "exec":
            return fns.onExec(intention.title, intention.command);
        case "edit":
            return fns.onEdit(intention.title, intention.fileName, intention.diff);
        case "mcp":
            return fns.onMcp(intention.toolName, intention.args);
        default: {
            const _: never = intention;
            throw new Error(`Unknown tool intention type: ${JSON.stringify(intention)}`);
        }
    }
}

/**
 * A callback that is called when a tool call is made, allowing for accepting or rejecting the tool call.
 *
 * @param intention The intention of the tool call, which can be interrogated to help choose whether to accept or reject.
 * @returns A promise that resolves to a ToolConfirmationResult indicating the outcome of the confirmation.
 */
export type ToolConfirmationCallback = (intention: ToolIntention) => Promise<ToolConfirmationResult>;

export const alwaysProceedToolConfirmationCallback: ToolConfirmationCallback = async () => {
    return "proceed-once";
};

export type HaltedToolConfirmationResult = "rejected" | "previously-interrupted";

export type ToolConfirmationResult = "proceed-once" | HaltedToolConfirmationResult;

export function shouldHalt(result: ToolConfirmationResult): result is HaltedToolConfirmationResult {
    return result === "rejected" || result === "previously-interrupted";
}

export function llmMessageFromToolHalt(result: HaltedToolConfirmationResult) {
    switch (result) {
        case "rejected":
            return "The tool call required confirmation from the user, but the user rejected it. Await further instructions.";
        case "previously-interrupted":
            return "The user rejected an earlier tool call and does not want to take this action right now. Await further instructions.";
        default: {
            const _: never = result;
            throw new Error("Unexpected HaltedToolConfirmationResult type: " + result);
        }
    }
}

export function toolResultFromHalt(
    result: HaltedToolConfirmationResult,
): ToolResultExpanded & { resultType: "rejected" } {
    const message = llmMessageFromToolHalt(result);
    return {
        textResultForLlm: message,
        resultType: "rejected",
        error: message,
        sessionLog: `<error>${message}</error>`,
        toolTelemetry: {},
    };
}

export type Tool = {
    name: string;
    description: string;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    input_schema: any;

    /**
     * A human readable string summary of what this command intends to do if executed.
     *
     * If not set, no summarised intention should be assumed by the caller.
     */
    summariseIntention?: (
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        input: any,
    ) => string;

    callback: ToolCallback;
    /**
     * A callback to get the current working directory of the tool.
     */
    cwd?: () => Promise<string>;
    shutdown?: ToolShutdown;
    /**
     * Whether or not information about this tool is safe to send to telemetry without obfuscation.
     * - If `true`/`false`, then it will be assumed that all such information is safe/unsafe.
     * - If an object, then safety is determined per property.
     */
    safeForTelemetry?:
        | {
              name: boolean;
              inputsNames: boolean;
          }
        | true;
};

export type ToolConfig = {
    /**
     * Where a tool should initially set its current working directory.
     */
    location: string;
    /**
     * A callback to retrieve the current working directory of the agent
     * which is using the tool.
     */
    cwd?: () => Promise<string>;
    /**
     * Timeout in milliseconds.
     */
    timeout?: number;
    /**
     * Optional callback for progress events.
     */
    callback?: IAgentCallback;
    /**
     * Whether to push changes to remote.
     */
    push?: boolean;
    /**
     * The branch name our work will be done within.
     */
    branchName?: string;
    /**
     * Require additional reasoning; usually because the model is not a reasoning model.
     * Defaults to 'false'. Used to determine if tools need to adjust to add reasoning to their output.
     */
    requireReasoning?: boolean;
    /**
     * A callback used to confirm whether a tool call should be executed.
     *
     * Autonomous usage can use `alwaysProceedToolConfirmationCallback` to always proceed with the tool call,
     * and over time this can be expanded to allow for more complex confirmation logic, such as configuration via
     * flags or user preferences.
     */
    toolConfirmationCallback: ToolConfirmationCallback;
};

export const createToolSet = (tools: Tool[]): Record<string, Tool> => {
    const toolSet: Record<string, Tool> = {};
    tools.forEach((tool) => {
        toolSet[tool.name] = tool;
    });
    return toolSet;
};
