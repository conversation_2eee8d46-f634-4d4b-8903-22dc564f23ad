/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { shouldHalt, Tool, ToolConfig, ToolResult, toolResultFromHalt } from ".";
import { TelemetryEvent } from "../telemetry";
import { InteractiveBashSession, SessionStats } from "./interactiveBash";
import { getSafeEnvVarsForToolBashSession, strLenTruncate } from "./util";

const DEFAULT_SESSION_ID = "default-bash-session";

export const BASH_TOOL_NAME = "bash";
export const WRITE_BASH_TOOL_NAME = "write_bash";
export const READ_BASH_TOOL_NAME = "read_bash";
export const STOP_BASH_TOOL_NAME = "stop_bash";

// The maximum value for the "timeout" parameter in the bash tool as passed by the agent.
// We want this to be sufficiently large that the model can run long-running commands without
// interruption but small enough that the model can't decide to just wait seemingly forever.
const MAX_COMMAND_TIMEOUT_SECONDS = 60 * 10;

// The minimum value for the "delay" parameter in the write_bash tool. We want this to be
// sufficiently large that the model sees any output that that results from the input.
const MIN_WRITE_TIMEOUT_SECONDS = 10;

type ShutdownSessionStats = SessionStats & {
    killedAtMs: number;
};

export type BashShutdownTelemetry = TelemetryEvent<
    "bash_shutdown",
    {
        properties: {
            /**
             * Stringified array of stats for all active ({@link SessionStats}) and shutdown ({@link ShutdownSessionStats}) sessions.
             */
            sessionStats: string;
        };
        metrics: Record<string, never>;
        restrictedProperties: Record<string, never>;
    }
>;

/**
 * Implements tools that expose interactive bash terminals to Copilot agent.
 * This tool supports multiple bash sessions, including long-running and interactive
 * (apps using NCurses or requiring a TTY) to work.
 */
export class InteractiveBashToolContext {
    private readonly sessions = new Map<string, InteractiveBashSession>();
    private readonly shutdownSessionsInfos: ShutdownSessionStats[] = [];

    constructor(
        private readonly config: ToolConfig,
        private readonly inTests: boolean = false,
    ) {}

    public getBashTool(): Tool {
        return {
            name: BASH_TOOL_NAME,

            description: `Runs a bash command in an interactive bash session.
                 * When invoking this tool, the contents of the "command" parameter does NOT need to be XML-escaped.
                 * You don't have access to the internet via this tool.
                 * You can run Python, Node.js and Go code with the \`python\`, \`node\` and \`go\` commands.
                 * You can install Linux, Python, JavaScript and Go packages with the \`apt\`, \`pip\`, \`npm\` and \`go\` commands.
                 * Each sessionId identifies a persistent bash session. State is saved across command calls and discussions with the user.
                 * \`timeout\` parameter must be greater than the default timeout of ${this.defaultTimeoutInMs() / 1_000} seconds and less than ${MAX_COMMAND_TIMEOUT_SECONDS} seconds}. Give long-running commands enough time to complete.
                 * If the command does not complete within "timeout" seconds, the tool will return a status indicating that it is still running asynchronously. You can then use \`${READ_BASH_TOOL_NAME}\` or \`${STOP_BASH_TOOL_NAME}\`.`,
            input_schema: {
                type: "object",
                properties: {
                    command: {
                        type: "string",
                        description: "The bash command and arguments to run.",
                    },
                    description: {
                        type: "string",
                        description:
                            'A short human-readable description of what the command does, limited to 100 characters, for example "List files in the current directory", "Install dependencies with npm" or "Run RSpec tests".',
                    },
                    timeout: {
                        type: "integer",
                        description: `(Optional) Maximum time in seconds to wait for the command to complete when "async" is false. Default is ${this.defaultTimeoutInMs() / 1_000} seconds if not provided.`,
                    },
                    sessionId: {
                        type: "string",
                        description: `Indicates which bash session to run the command in. Multiple sessions may be used to run different commands at the same time.`,
                    },
                    async: {
                        type: "boolean",
                        description: `If true, runs the command asynchronously. You can send input to the command using the \`${WRITE_BASH_TOOL_NAME}\` tool and read its output using the \`${READ_BASH_TOOL_NAME}\` tool.`,
                    },
                },
                required: ["command", "description", "sessionId", "async"],
            },
            shutdown: this.shutdown.bind(this),
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            summariseIntention: (input: any): string => input.description || input.command,
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            callback: async (input: any): Promise<ToolResult> => {
                const confirmationResult = await this.config.toolConfirmationCallback({
                    type: "exec",
                    title: "Bash",
                    command: input.command,
                });

                if (shouldHalt(confirmationResult)) {
                    return toolResultFromHalt(confirmationResult);
                }

                return this.bashTool(input);
            },
            cwd: async () => {
                return (
                    (await (await this.getOrCreateSession(this.config.location, DEFAULT_SESSION_ID))?.cwd()) ||
                    this.config.location
                );
            },
            safeForTelemetry: true,
        };
    }

    public getWriteBashTool(): Tool {
        return {
            name: WRITE_BASH_TOOL_NAME,

            description: `Sends input to the specified command or bash session.
                 * This tool can be used to send input to a running bash command or an interactive console app.
                 * Bash commands are run in an interactive bash session with a TTY device and bash command processor.
                 * sessionId (required) must match the sessionId used to invoke the async ${BASH_TOOL_NAME} command.
                 * You can send text, {up}, {down}, {left}, {right}, {enter}, and {backspace} as input.
                 * Some applications present a list of options to select from. The selection is often denoted using ❯, >, or different formatting.
                 * When presented with a list of items, you must make a selection by sending arrow keys like {up} or {down} to move the selection to the item that you want to select and then {enter} to select it.
                 * The response will contain any output read after "delay" seconds. Delay should be appropriate for the task and never less than 10 seconds.`,
            input_schema: {
                type: "object",
                properties: {
                    sessionId: {
                        type: "string",
                        description: `Indicates which bash session to run the command in. Multiple sessions may be used to run different commands at the same time.`,
                    },
                    input: {
                        type: "string",
                        description: `The input to send to the command or session.`,
                    },
                    delay: {
                        type: "integer",
                        description: `(Optional) The amount of time in seconds to wait before reading the output that resulted from the input.`,
                    },
                },
                required: ["sessionId", "input"],
            },
            shutdown: this.shutdown.bind(this),
            callback: this.writeBashTool.bind(this),
            cwd: async () => {
                return (
                    (await (await this.getOrCreateSession(this.config.location, DEFAULT_SESSION_ID))?.cwd()) ||
                    this.config.location
                );
            },
            safeForTelemetry: true,
        };
    }

    public getReadBashTool(): Tool {
        return {
            name: READ_BASH_TOOL_NAME,
            description: `Reads output from a bash command.
                 * Reads the output of a command running in an "async" bash session.
                 * The sessionId must be the same one used to invoke the ${BASH_TOOL_NAME} command.
                 * You can call this tool multiple times to read output produced since the last call.
                 * Each request has a cost, so provide a reasonable "delay" parameter value for the task, to minimize the need for repeated reads that return no output.
                 * If a read request generates no output, consider using exponential backoff in choosing the delay between reads of the same command.
                 * Though \`${WRITE_BASH_TOOL_NAME}\` accepts ANSI control codes, this tool does not include them in the output.`,
            input_schema: {
                type: "object",
                properties: {
                    sessionId: {
                        type: "string",
                        description: "The ID of the shell session used to invoke the bash command.",
                    },
                    delay: {
                        type: "integer",
                        description: `(Optional) The amount of time in seconds to wait before reading the output.`,
                    },
                },
                required: ["sessionId", "delay"],
            },
            callback: this.readBashTool.bind(this),
            safeForTelemetry: true,
        };
    }

    public getStopBashTool(): Tool {
        return {
            name: STOP_BASH_TOOL_NAME,
            description: `Stops a running bash command.
                 * Stops a running bash command by terminating the entire bash session and process.
                 * This tool can be used to stop commands that have not exited on their own.
                 * Any environment variables defined will have to be redefined after using this tool if the same session ID is used to run a new command.
                 * The sessionId must match the sessionId used to invoke the ${BASH_TOOL_NAME} command.`,
            input_schema: {
                type: "object",
                properties: {
                    sessionId: {
                        type: "string",
                        description: `The ID of the bash session used to invoke the ${BASH_TOOL_NAME} command.`,
                    },
                },
                required: ["sessionId"],
            },
            callback: this.stopBashTool.bind(this),
            safeForTelemetry: true,
        };
    }

    private defaultTimeoutInMs(): number {
        return this.config.timeout ?? 2 * 60 * 1000; // Default to 2 minutes
    }

    private async bashTool(input: {
        command: string;
        description: string;
        timeout?: number;
        sessionId?: string;
        async?: boolean;
    }): Promise<ToolResult> {
        if (!input.command) {
            const errorMessage = "No command provided. Please supply a valid command to execute.";

            return {
                textResultForLlm: errorMessage,
                resultType: "failure",
                error: errorMessage,
                sessionLog: errorMessage,
                toolTelemetry: {},
            };
        }

        if (input.timeout !== undefined) {
            if (typeof input.timeout !== "number" || input.timeout <= 0) {
                const errorMessage = `Invalid timeout value ${input.timeout}. Please supply a valid timeout value in seconds between 0 and ${MAX_COMMAND_TIMEOUT_SECONDS}.`;

                return {
                    textResultForLlm: errorMessage,
                    resultType: "failure",
                    error: errorMessage,
                    sessionLog: errorMessage,
                    toolTelemetry: {},
                };
            }
        }

        let timeout: number | undefined = undefined;

        // Only respect timeouts greater than the default timeout. Silently set the max
        // timeout to MAX_COMMAND_TIMEOUT_SECONDS to prevent the agent from getting stuck
        // waiting for a command that never completes.
        //
        // On timeout, it will have an opportunity to resume waiting or stop the command.
        if (input.timeout !== undefined && input.timeout > this.defaultTimeoutInMs() / 1000) {
            timeout = Math.min(input.timeout, MAX_COMMAND_TIMEOUT_SECONDS) * 1000;
        } else if (!input.async) {
            // Only synchronous commands may timeout.
            timeout = this.defaultTimeoutInMs();
        }

        const telemetry = {
            properties: {
                customTimeout: input.timeout !== undefined ? "true" : "false",
                isBackgroundSession: input.async ? "true" : "false",
            },
            metrics: {
                commandTimeout: timeout,
            },
        };

        const sessionId = input.sessionId ?? DEFAULT_SESSION_ID;
        let session: InteractiveBashSession | undefined;

        try {
            session = await this.getOrCreateSession(this.config.location, sessionId);

            // If a background session ID is provided, run the command in the background
            if (input.sessionId && input.async) {
                if (await session.tryExecuteAsyncCommand(input.command)) {
                    return {
                        textResultForLlm: `<command started in background with ID: ${sessionId}>`,
                        resultType: "success",
                        sessionLog: `<command started in background with ID: ${sessionId}>`,
                        toolTelemetry: telemetry,
                    };
                } else {
                    return {
                        textResultForLlm: `<command with id: ${sessionId} is already running, wait for output with read_bash, stop it with stop_bash tool, or use a different sessionId>`,
                        resultType: "failure",
                        sessionLog: `<command with id: ${sessionId} is already running, wait for output with read_bash, stop it with stop_bash tool, or use a different sessionId>`,
                        toolTelemetry: telemetry,
                    };
                }
            }

            // Otherwise, run the command synchronously.
            const output = await session.executeCommand(input.command, timeout);
            if (output === undefined) {
                return {
                    textResultForLlm: `<command with id: ${sessionId} is already running, wait for output with read_bash, stop it with stop_bash tool, or use a different sessionId>`,
                    resultType: "failure",
                    sessionLog: `<command with id: ${sessionId} is already running, wait for output with read_bash, stop it with stop_bash tool, or use a different sessionId>`,
                    toolTelemetry: telemetry,
                };
            }
            let result = formatOutput(output.output);
            if (output.exitCode !== undefined) {
                result += `\n<exited with exit code ${output.exitCode}>`;
            }

            const truncatedResult = result ? strLenTruncate(result, "output") : "";

            return {
                textResultForLlm: truncatedResult,
                resultType: "success",
                sessionLog: truncatedResult,
                toolTelemetry: telemetry,
            };
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } catch (error: any) {
            const errorOutput = formatErrorOutput(session, error, timeout ?? -1, sessionId, "exit");
            const errorMessage = strLenTruncate(errorOutput.output, "output");

            // If the error is a timeout, we don't want to shutdown the session
            // because we'll let the model decide whether to wait a bit longer
            // and read or stop the command.
            if (!errorOutput.isTimeout) {
                // Shutdown the session if it exists to force a reset of the environment.
                this.shutdownSession(sessionId);
            }

            return {
                textResultForLlm: errorMessage,
                resultType: errorOutput.resultType,
                error: errorMessage,
                sessionLog: errorMessage,
                toolTelemetry: telemetry,
            };
        }
    }

    private async writeBashTool(input: { sessionId?: string; input?: string; delay: number }): Promise<ToolResult> {
        if (!input.input) {
            const errorMessage = "No input provided. Please supply input to send to the running process.";

            return {
                textResultForLlm: errorMessage,
                resultType: "failure",
                error: errorMessage,
                sessionLog: errorMessage,
                toolTelemetry: {},
            };
        }

        // Ensure that the delay is at least 10 seconds. Claude Sonnet 4
        // often underestimates how long it takes for input to show up.
        input.delay = this.inTests || input.delay > MIN_WRITE_TIMEOUT_SECONDS ? input.delay : MIN_WRITE_TIMEOUT_SECONDS;

        const telemetry = {
            metrics: {
                commandTimeout: input.delay,
            },
        };

        const sessionId = input.sessionId ?? DEFAULT_SESSION_ID;
        let session: InteractiveBashSession | undefined;

        try {
            session = await this.getOrCreateSession(this.config.location, sessionId);

            // Send the input to the running command.
            if (!session.trySendInput(substituteAnsiCodes(input.input))) {
                return {
                    textResultForLlm: `<unable to send input. no command with id: ${sessionId} is currently running>`,
                    resultType: "failure",
                    sessionLog: `<unable to send input. no command with id: ${sessionId} is currently running>`,
                    toolTelemetry: telemetry,
                };
            }

            // Wait a few seconds for a response. By default it's 5 seconds, but the model can customize it.
            return await this.readBashTool({
                sessionId: sessionId,
                delay: input.delay ?? 5,
            });
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } catch (error: any) {
            const errorOutput = formatErrorOutput(session, error, input.delay, sessionId, "produce output");
            const errorMessage = strLenTruncate(errorOutput.output, "output");

            // If the error is a timeout, we don't want to shutdown the session
            // because we'll let the model decide whether to wait a bit longer
            // and read or stop the command.
            if (!errorOutput.isTimeout) {
                // Shutdown the session if it exists to force a reset of the environment.
                this.shutdownSession(input.sessionId ?? DEFAULT_SESSION_ID);
            }

            return {
                textResultForLlm: errorMessage,
                resultType: errorOutput.resultType,
                error: errorMessage,
                sessionLog: errorMessage,
                toolTelemetry: telemetry,
            };
        }
    }

    private async readBashTool(input: { sessionId: string; delay: number }): Promise<ToolResult> {
        // If no valid sessionId is provided, the call is invalid.
        const session = this.getSession(input.sessionId);
        if (!session) {
            const errorMessage = `Invalid session ID: ${input.sessionId}. Please supply a valid session ID to read output from.`;

            return {
                textResultForLlm: errorMessage,
                resultType: "failure",
                error: errorMessage,
                sessionLog: errorMessage,
                toolTelemetry: {},
            };
        }

        if (input.delay === undefined || input.delay < 0 || input.delay > MAX_COMMAND_TIMEOUT_SECONDS) {
            const errorMessage = `Invalid delay: ${input.delay}. Please supply a valid delay between 0 and ${MAX_COMMAND_TIMEOUT_SECONDS} seconds.`;

            return {
                textResultForLlm: errorMessage,
                resultType: "failure",
                error: errorMessage,
                sessionLog: errorMessage,
                toolTelemetry: {},
            };
        }

        try {
            // Once again, wait for the command to complete or timeout. If it times out, we return
            // what we have so far.
            const output = await session.waitForCommandCompletionOrTimeout(input.delay * 1000);

            // If the command exited, include the exit code.
            let result = formatOutput(output.output);
            if (output.exitCode !== undefined) {
                // Clear the buffer after the final read
                // when the command has finished executing. This behavior is intended to
                // emphasize the '<exited with exit code ...>' message to reduce the odds
                // of the model idly looping after the process has exited, as observed in
                // https://github.com/github/sweagentd/issues/2868
                session.clearBuffer();
                result += `\n<command with id: ${input.sessionId} exited with exit code ${output.exitCode}>`;
            }

            const truncatedResult = result ? strLenTruncate(result, "output") : "";

            return {
                textResultForLlm: truncatedResult,
                resultType: "success",
                sessionLog: truncatedResult,
                toolTelemetry: {},
            };
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } catch (error: any) {
            const errorOutput = formatErrorOutput(
                session,
                error,
                input.delay * 1000,
                input.sessionId,
                "produce output",
            );
            if (errorOutput.isTimeout) {
                const errorMessage = strLenTruncate(errorOutput.output, "output");
                return {
                    textResultForLlm: errorMessage,
                    resultType: errorOutput.resultType,
                    sessionLog: errorMessage,
                    toolTelemetry: {},
                };
            }

            return {
                textResultForLlm: "<internal error: " + (error as Error).message + ">",
                resultType: errorOutput.resultType,
                error: "<internal error: " + (error as Error).message + ">",
                sessionLog: "<internal error: " + (error as Error).message + ">",
                toolTelemetry: {},
            };
        }
    }

    private async stopBashTool(input: { sessionId: string }): Promise<ToolResult> {
        if (!input.sessionId) {
            const errorMessage =
                "No background session ID provided. Please supply a valid background session ID to stop the command.";

            return {
                textResultForLlm: errorMessage,
                resultType: "failure",
                error: errorMessage,
                sessionLog: errorMessage,
                toolTelemetry: {},
            };
        }

        this.shutdownSession(input.sessionId);

        return {
            textResultForLlm: `<command with id: ${input.sessionId} stopped>`,
            resultType: "success",
            sessionLog: `<command with id: ${input.sessionId} stopped>`,
            toolTelemetry: {},
        };
    }

    private async shutdown(): Promise<BashShutdownTelemetry> {
        for (const sessionId of this.sessions.keys()) {
            this.shutdownSession(sessionId);
        }

        const sessionInfos: (SessionStats | ShutdownSessionStats)[] = Array.from(this.sessions.values())
            .map((session) => session.getStats())
            .concat(this.shutdownSessionsInfos);

        return {
            kind: "telemetry",
            telemetry: {
                event: "bash_shutdown",
                properties: {
                    sessionStats: JSON.stringify(sessionInfos),
                },
                metrics: {},
                restrictedProperties: {},
            },
        };
    }

    private getSession(sessionId?: string): InteractiveBashSession | undefined {
        sessionId ??= DEFAULT_SESSION_ID;
        const session = this.sessions.get(sessionId);
        // The session itself will track the last used time when its methods are called
        return session;
    }

    private async getOrCreateSession(location: string, sessionId: string): Promise<InteractiveBashSession> {
        let session = this.sessions.get(sessionId);

        if (!session) {
            session = await InteractiveBashSession.create(location, getSafeEnvVarsForToolBashSession());
            this.sessions.set(sessionId, session);
        }

        return session;
    }

    private shutdownSession(sessionId: string): void {
        const session = this.sessions.get(sessionId);
        if (session) {
            const stats = session.getStats();
            session.shutdown();
            this.sessions.delete(sessionId);
            this.shutdownSessionsInfos.push({
                ...stats,
                killedAtMs: Date.now(),
            });
        }
    }
}

function formatOutput(stdout: string): string {
    let result = "";
    if (stdout?.trim()) {
        result += stdout.trim();
    }
    return result;
}

function formatErrorOutput(
    session: InteractiveBashSession | undefined,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    error: any,
    timeout: number,
    sessionId: string,
    action: string,
): { output: string; isTimeout: boolean; resultType: "success" | "failure" } {
    const output = session?.readOutput();
    if (output?.exitCode !== undefined) {
        // Clear the buffer after the final read
        // when the command has finished executing. This behavior is intended to
        // emphasize the '<exited with exit code ...>' message to reduce the odds
        // of the model idly looping after the process has exited, as observed in
        // https://github.com/github/sweagentd/issues/2868
        session?.clearBuffer();
    }
    const baseOutput = formatOutput(output?.output || "");

    // Add error status
    if (error.message?.includes("timed out")) {
        return {
            output: [
                baseOutput,
                `<waiting for command with sessionId: ${sessionId} to ${action} timed out after ${timeout / 1_000} seconds. try again with ${READ_BASH_TOOL_NAME} or abort it with ${STOP_BASH_TOOL_NAME} tool>`,
            ]
                .filter(Boolean)
                .join("\n"),
            isTimeout: true,
            resultType: "success",
        };
    }

    if (error.code !== undefined) {
        return {
            output: [baseOutput, `<exited with exit code ${error.code}>`].filter(Boolean).join("\n"),
            isTimeout: false,
            resultType: "failure",
        };
    }

    return {
        output: [baseOutput, `<exited with error: ${error.message}>`].filter(Boolean).join("\n"),
        isTimeout: false,
        resultType: "failure",
    };
}

function substituteAnsiCodes(input: string): string {
    // For some reason Claude Sonnet 4 at least isn't able to provide ANSI codes as tool call params.
    // They come through as empty strings, so we need to substitute them manually. This is a basic set
    // that we may want to expand in the future.
    return input
        .replaceAll("{up}", "\x1b[A")
        .replaceAll("{down}", "\x1b[B")
        .replaceAll("{left}", "\x1b[D")
        .replaceAll("{right}", "\x1b[C")
        .replaceAll("{enter}", "\x0d")
        .replaceAll("{backspace}", "\x08");
}
