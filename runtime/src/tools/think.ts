/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import { Tool, ToolConfig, ToolResultExpanded } from "./index";

export const think = (_config: ToolConfig): Tool | undefined => {
    return {
        name: "think",
        description: `Use the tool to think about something. 
It will not obtain new information or make any changes to the repository, but just log the thought. 
Use it when complex reasoning or brainstorming is needed. 
For example, if you explore the repo and discover the source of a bug, call this tool to brainstorm several unique ways of fixing the bug, and assess which change(s) are likely to be simplest and most effective. 
Alternatively, if you receive some test results, call this tool to brainstorm ways to fix the failing tests.`,
        input_schema: {
            type: "object",
            properties: {
                thought: {
                    type: "string",
                    description: "Your thoughts.",
                },
            },
            required: ["thought"],
        },
        callback: async (input: { thought: string }): Promise<ToolResultExpanded> => {
            // check for empty input
            if (!input || !input.thought || typeof input.thought !== "string" || input.thought.trim() === "") {
                return {
                    textResultForLlm: "No thought provided",
                    resultType: "success",
                    sessionLog: "No thought provided",
                    toolTelemetry: {},
                };
            }

            /** No-op - just sit and think */
            // TODO: Should this go to memory/file? Could be useful as context for resuming the conversation
            return {
                textResultForLlm: "Thought logged",
                resultType: "success",
                sessionLog: input.thought,
                toolTelemetry: {},
            };
        },
        safeForTelemetry: true,
    };
};
