/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import { ChatCompletionMessageParam } from "openai/resources";

import { ChatCompletionMessage } from "openai/resources";

import { fixFeedbackUserMessage, fixUserMessage, prDescriptionUserMessage, systemMessage } from "./prompts";
import type { PRDetails, SweAgent } from "./sweagent";

import { join as pathJoin } from "path";
import searchWithBlackbird from "../blackbird";
import { IAgent<PERSON>allback } from "../callbacks/callback";
import { filterContent, problemStatementXMLTags } from "../helpers/content-filter";
import { BasicTruncator } from "../model/capi/truncateContextWindow";
import type { Client, GetCompletionWithToolsOptions } from "../model/client";
import { RunnerLogger } from "../runner";
import { RunnerExec } from "../runner/exec";
import { DefaultGitHandler } from "../runner/git/default";
import { NoopLogger } from "../runner/logger/noop";
import { RuntimeSettings } from "../settings";
import { isFeatureFlagEnabled, isWithinPercentOfTimeout } from "../settings/types";
import * as tools from "../tools";
import { alwaysProceedToolConfirmationCallback } from "../tools";
import { ClientInfo, InProcMCPTransport, OutOfProcMCPTransport } from "../tools/mcp-transport";
import { fixTools, SweAgentActionConfig, SweBenchConfig, ToolInit } from "./config";
import { OnlineEvaluation } from "./onlineEvaluation";
import { AgentTelemetryEmitter } from "./telemetry";
import { createTagSchema, extractTopLevelXmlTags, preprocessAgentHistory } from "./util";

// Structured response types for LLM responses.
type PullRequestResponse = {
    pr_title: string;
    pr_description: string;
};

export class SweBenchAgent implements SweAgent {
    outOfProcMcpTransport?: OutOfProcMCPTransport;
    inProcMcpTransport: InProcMCPTransport;
    onlineEvaluation?: OnlineEvaluation;

    public constructor(
        private readonly client: Client,
        private readonly settings: RuntimeSettings,
        private readonly logger: RunnerLogger,
        private readonly exec: RunnerExec,
        private readonly sweConfig: SweBenchConfig = {},
        private readonly mcpClients?: ClientInfo[],
        private readonly callback?: IAgentCallback,
    ) {
        if (process.env.COPILOT_MCP_ENABLED == "true") {
            this.outOfProcMcpTransport = new OutOfProcMCPTransport(this.settings, this.logger);
        }
        this.inProcMcpTransport = new InProcMCPTransport(this.settings, this.logger);
        this.onlineEvaluation =
            this.settings.onlineEvaluation?.disableOnlineEvaluation !== true
                ? new OnlineEvaluation(this.settings, this.logger)
                : undefined;
    }

    private async agent(
        location: string,
        systemMessage: string,
        userMessage: string,
        issueNumber: number,
        branchName: string,
        jitInstructions?: SweAgentActionConfig["jitInstructions"],
        config?: Partial<tools.ToolConfig>,
        toolInit: ToolInit = fixTools,
    ): Promise<{
        finalTurnCount: number;
        messages: ChatCompletionMessageParam[];
        tools: tools.Tool[];
        gitCommitHashBeforeAgent: string;
    }> {
        const initialUserMessage: ChatCompletionMessageParam = {
            role: "user",
            content: userMessage,
        };
        const tools = await this.toolSet(
            {
                location,
                branchName,
                callback: this.callback,
                push: config?.push,
                requireReasoning: this.sweConfig?.supports?.reasoning !== true,
                toolConfirmationCallback: alwaysProceedToolConfirmationCallback,
            },
            issueNumber,
            toolInit,
        );

        await this.callback?.progress({
            kind: "message",
            message: initialUserMessage,
            turn: 0,
        });
        const completionWithToolsResult = await this.runCompletionWithTools(
            location,
            systemMessage,
            [initialUserMessage],
            tools,
            jitInstructions,
            {
                executeToolsInParallel: isFeatureFlagEnabled(
                    this.settings,
                    "copilot_swe_agent_parallel_tool_execution",
                ),
            },
        );

        // Cleanup tools and bash session
        for (const tool of tools) {
            if (tool.shutdown) {
                const maybeShutdownTelemetry = await tool.shutdown();
                if (maybeShutdownTelemetry) {
                    await this.callback?.progress(maybeShutdownTelemetry);
                }
            }
        }

        return {
            finalTurnCount: completionWithToolsResult.finalTurnCount,
            messages: completionWithToolsResult.messages,
            tools,
            gitCommitHashBeforeAgent: completionWithToolsResult.startingCommitHash,
        };
    }

    private async runCompletionWithTools(
        location: string,
        systemMessage: string,
        initialMessages: ChatCompletionMessageParam[],
        tools: tools.Tool[],
        jitInstructions?: SweAgentActionConfig["jitInstructions"],
        options?: GetCompletionWithToolsOptions,
    ): Promise<{
        finalTurnCount: number;
        messages: ChatCompletionMessageParam[];
        resultMessages: ChatCompletionMessage[];
        startingCommitHash: string;
    }> {
        // todo get agent name non-hardcoded
        const telemetryEmitter = this.callback
            ? new AgentTelemetryEmitter("sweagent-capi", this.callback, this.settings, tools)
            : undefined;
        const gitHandler = new DefaultGitHandler(this.logger, this.exec);
        const startingCommitHash = await gitHandler.getCurrentCommitHash(location);

        const returnedJitInstructions = new Set<string>();
        let nextJitInstructionToReturn: string | undefined = undefined;
        const getJitInstructions: GetCompletionWithToolsOptions["getJitInstructions"] = () => {
            if (nextJitInstructionToReturn) {
                if (returnedJitInstructions.has(nextJitInstructionToReturn)) {
                    return undefined;
                }
                returnedJitInstructions.add(nextJitInstructionToReturn);
            }
            return nextJitInstructionToReturn;
        };

        const stream = this.client.getCompletionWithTools(systemMessage, initialMessages, tools, {
            ...options,
            getJitInstructions: getJitInstructions,
            // @todo move to SweBenchConfig after doing some soul searching there
            processors: {
                preRequest: [new BasicTruncator()],
            },
        });

        let finalTurnCount = 0;
        const messages: ChatCompletionMessageParam[] = [...initialMessages];
        const resultMessages: ChatCompletionMessage[] = [];
        for await (const event of stream) {
            await this.callback?.progress(event);
            if (telemetryEmitter) {
                await telemetryEmitter.ingestEvent(event);
            }
            if (event.kind === "message" || event.kind === "response") {
                if (event.kind === "message") {
                    messages.push(event.message);
                }
                if (event.kind === "response") {
                    resultMessages.push(event.response);
                }
                finalTurnCount = Math.max(finalTurnCount, event.turn ?? 0);
            }

            // After each event from the agent, check if we need to change our JIT instruction
            const jitInstruction = await this.getCurrentlyApplicableJitInstruction(
                jitInstructions,
                location,
                startingCommitHash,
            );
            if (jitInstruction) {
                nextJitInstructionToReturn = jitInstruction;
            }
        }

        return {
            finalTurnCount,
            messages,
            resultMessages,
            startingCommitHash,
        };
    }

    private async generatePRDescription(
        issueNumber: number,
        location: string,
        agentSystemMessage: string,
        messages: ChatCompletionMessageParam[],
        tools: tools.Tool[],
        finalTurnCount: number,
        revision = false,
    ): Promise<PRDetails> {
        let history: ChatCompletionMessageParam[] = [...messages];
        let responseRetryCount = 0;
        const maxResponseRetries = 3;

        const [processedHistory, containsScreenshots] = preprocessAgentHistory(history);
        history = processedHistory;

        const responseStructure = createTagSchema<PullRequestResponse>({
            pr_title: "",
            pr_description: "",
        });
        history.push({
            role: "user",
            content: prDescriptionUserMessage(issueNumber, responseStructure, containsScreenshots, revision),
        });
        do {
            // Summarize the changes made by the agent for the PR description
            const completionWithToolsResult = await this.runCompletionWithTools(
                location,
                agentSystemMessage,
                history,
                tools,
                undefined,
                {
                    toolChoice: this.sweConfig?.supports?.tool_choice ? "none" : undefined,
                    requestHeaders: { "X-Initiator": "agent" },
                    initialTurnCount: finalTurnCount,
                },
            );

            // Handle results
            const resultMessages = completionWithToolsResult.resultMessages;
            if (resultMessages.length !== 1) {
                this.logger.warning(
                    `Unexpected replies from PR description request: ${JSON.stringify(resultMessages)}`,
                );
            }

            const content = this.getContent(resultMessages[resultMessages.length - 1]);
            const prResponse = extractTopLevelXmlTags<PullRequestResponse>(content);

            // If this a revision, then allow for empty title and description
            // otherwise, both fields must be present and non-empty
            if (revision || (prResponse && prResponse.pr_title?.trim() && prResponse.pr_description?.trim())) {
                return {
                    title: prResponse?.pr_title?.trim(),
                    description: prResponse?.pr_description?.trim(),
                };
            }

            this.logger.warning(
                `Invalid response from PR description request: ${JSON.stringify(prResponse)}. Retrying attempt ${responseRetryCount + 1} of ${maxResponseRetries}`,
            );
            history.push(resultMessages[resultMessages.length - 1]);
            history.push({
                role: "user",
                content: `The response is not structured correctly with all fields REQUIRED. Please try again. Structure MUST be:\n${responseStructure}`,
            });
        } while (responseRetryCount++ < maxResponseRetries);

        throw new Error(`Failed to get a valid PR summary after ${maxResponseRetries} attempts.`);
    }

    public async createPRAgent(
        location: string,
        problem_statement: string,
        repo: string,
        issueNumber: number,
        branchName: string,
        config?: Partial<tools.ToolConfig>,
    ): Promise<PRDetails> {
        problem_statement = filterContent(
            problem_statement,
            this.settings.problem?.contentFilterMode,
            problemStatementXMLTags,
        );
        const agentSystemMessage = await systemMessage(
            location,
            this.sweConfig.createPR?.systemMessage,
            this.sweConfig.supports,
        );

        const git = new DefaultGitHandler(this.logger, this.exec);
        const firstCommitHash: string = await git.getCurrentCommitHash(location);

        let repoInsights = undefined;
        if (this.outOfProcMcpTransport && this.settings.blackbird?.mode == "initial-search") {
            // For swebench, we must search using the base commit appended to the repo name, like this:
            // nwo = myorg/reponame_d16bfe
            const nwo = this.settings.github?.repo?.name ?? "";
            this.logger.info("Searching with NWO: " + nwo);
            let owner = "";
            let repo = "";
            const nwoParts = nwo.split("/");
            if (nwoParts.length !== 2) {
                this.logger.info("Unable to parse owner and repo from nwo: " + nwo);
                this.logger.info("Skipping repository insights collection");
            } else {
                [owner, repo] = nwoParts;
                this.logger.info("Searching for repository insights");
                try {
                    repoInsights = await searchWithBlackbird(
                        problem_statement,
                        owner,
                        repo,
                        this.outOfProcMcpTransport,
                        this.logger,
                    );
                    this.logger.info("Collected repository insights: " + JSON.stringify(repoInsights));
                } catch (error) {
                    this.logger.error("Error while searching for repository insights: " + error);
                    this.logger.info("Skipping repository insights collection");
                }
            }
        } else {
            this.logger.info("Blackbird is disabled: " + this.settings.blackbird?.mode);
            this.logger.info("MCP transport: " + (this.outOfProcMcpTransport ? "enabled" : "disabled"));
        }

        try {
            // Call the core agent to make the changes
            const { finalTurnCount, messages, tools } = await this.agent(
                location,
                agentSystemMessage,
                fixUserMessage(
                    location,
                    repo,
                    problem_statement,
                    repoInsights,
                    this.sweConfig.createPR?.userMessage,
                    this.sweConfig.supports,
                ),
                issueNumber,
                branchName,
                this.sweConfig.createPR?.jitInstructions,
                config,
                this.sweConfig?.createPR?.toolInit,
            );

            this.logger.debug("Available tools: " + JSON.stringify(tools));

            return await this.generatePRDescription(
                issueNumber,
                location,
                agentSystemMessage,
                messages,
                tools,
                finalTurnCount,
            );
        } finally {
            await this.onlineEvaluation?.evaluate(
                "createPRAgent",
                location,
                problem_statement,
                firstCommitHash,
                this.settings.trajectory?.outputFile ?? "",
                this.logger,
                this.exec,
                this.callback,
            );
        }
    }

    public async respondToPRCommentAgent(
        location: string,
        problem_statement: string,
        repo: string,
        issueNumber: number,
        branchName: string,
        config?: Partial<tools.ToolConfig>,
    ): Promise<PRDetails> {
        const git = new DefaultGitHandler(this.logger, this.exec);
        const firstCommitHash: string = await git.getCurrentCommitHash(location);

        problem_statement = filterContent(
            problem_statement,
            this.settings.problem?.contentFilterMode,
            problemStatementXMLTags,
        );

        const agentSystemMessage = await systemMessage(
            location,
            this.sweConfig.respondToPRComment?.systemMessage,
            this.sweConfig.supports,
        );

        try {
            // Call the core agent to make the changes
            const { finalTurnCount, messages, tools } = await this.agent(
                location,
                agentSystemMessage,
                fixFeedbackUserMessage(
                    location,
                    repo,
                    problem_statement,
                    this.sweConfig.respondToPRComment?.userMessage,
                    this.sweConfig?.supports,
                ),
                issueNumber,
                branchName,
                this.sweConfig.respondToPRComment?.jitInstructions,
                config,
                this.sweConfig?.respondToPRComment?.toolInit,
            );

            if (messages.length === 0) {
                this.logger.warning(`Unexpected no messages from PR comment job`);
            }

            // check if there needs to be a final commit message
            // stage any pending changes
            await git.stageChanges(location);
            const gitDiff = await git.diff(location, true);
            const prDetails = isFeatureFlagEnabled(this.settings, "copilot_swe_agent_sync_pr_title_description")
                ? await this.generatePRDescription(
                      issueNumber,
                      location,
                      agentSystemMessage,
                      messages,
                      tools,
                      finalTurnCount,
                      true,
                  )
                : undefined;
            return {
                ...(prDetails ?? { title: "", description: "" }),
                diff: gitDiff,
            };
        } finally {
            await this.onlineEvaluation?.evaluate(
                "respondToPRCommentAgent",
                location,
                problem_statement,
                firstCommitHash,
                this.settings.trajectory?.outputFile ?? "",
                this.logger,
                this.exec,
            );
        }
    }

    async toolSet(config: tools.ToolConfig, issueNumber: number, toolInit: ToolInit): Promise<tools.Tool[]> {
        this.logger.info(this.outOfProcMcpTransport ? "MCP transport enabled" : "MCP transport disabled");
        const outOfProcMcpTools: tools.Tool[] =
            (await this.outOfProcMcpTransport?.loadTools(
                pathJoin(process.env.COPILOT_AGENT_MCP_SERVER_TEMP ?? ".", "mcp-config.json"),
            )) ?? [];

        const inProcMcpTools: tools.Tool[] = [];
        for (const clientInfo of this.mcpClients ?? []) {
            const toolsFromProvider = await this.inProcMcpTransport.loadTools(clientInfo);
            inProcMcpTools.push(...toolsFromProvider);
        }

        return [
            ...(await toolInit(config, issueNumber, this.settings, this.logger, this.exec)),
            ...outOfProcMcpTools,
            ...inProcMcpTools,
        ];
    }

    private getContent(message: ChatCompletionMessageParam): string {
        const content = message.content;
        if (typeof content === "string") {
            return content;
        }
        if (!Array.isArray(content) || content.length === 0 || !("text" in content[0])) {
            throw new Error("Expected text content in response");
        }

        return content[0].text;
    }

    private async getCurrentlyApplicableJitInstruction(
        jitInstructions: SweAgentActionConfig["jitInstructions"],
        location: string,
        initialCommit: string,
    ): Promise<string | undefined> {
        if (!jitInstructions || Object.keys(jitInstructions).length === 0) {
            return undefined;
        }

        let numPathsChanged: number | undefined = undefined;
        const getNumPathsChanged = async (): Promise<number> => {
            if (numPathsChanged === undefined) {
                // we don't need the calls to this git handler to be logged
                const git = new DefaultGitHandler(new NoopLogger(), this.exec);
                try {
                    numPathsChanged = (await git.getChangedPaths(location, "HEAD", initialCommit)).length;
                } catch (err) {
                    this.logger.error(`Failed to get changed paths, err: ${err}`);
                    // if for some reason we can't get a diff, we'll just assume some paths have changed
                    numPathsChanged = 1;
                }
            }
            return numPathsChanged;
        };

        let bestNudge: [number, string | ((location: string) => string)] | undefined = undefined;
        for (const [_, config] of Object.entries(jitInstructions)) {
            const percentResult = isWithinPercentOfTimeout(this.settings, config.percentRemainingOfTimeout);
            if (percentResult.isWithin) {
                const noPathsChangedResult = config.whenNoPathsChanged ? (await getNumPathsChanged()) === 0 : true;
                if (noPathsChangedResult && (!bestNudge || percentResult.withinMs < bestNudge[0])) {
                    bestNudge = [percentResult.withinMs, config.instruction];
                }
            }
        }

        const instruction = bestNudge?.[1];
        if (typeof instruction === "function") {
            return instruction(location);
        } else if (typeof instruction === "string") {
            return instruction;
        } else {
            return undefined;
        }
    }
}
