/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { Client as McpClient } from "@modelcontextprotocol/sdk/client/index.js";
import { join as pathJoin } from "path";
import { ClientOptions, GetCompletionWithToolsOptions } from "../model/client";
import { RunnerLogger } from "../runner";
import { RunnerExec } from "../runner/exec";
import { RuntimeSettings } from "../settings";
import { isFeatureFlagEnabled } from "../settings/types";
import * as tools from "../tools";
import { InProcMCPTransport, OutOfProcMCPTransport } from "../tools/mcp-transport";
import { JsonFileMemoryStrategy, MemoryContext } from "../tools/memory";
import { fixTimeoutNudges, remindWhereToDoWorkNudge } from "./prompts";
import { SweAgentKind } from "./sweagent";

export type ToolInit = (
    config: tools.ToolConfig,
    issueNumber: number,
    settings: RuntimeSettings,
    logger: RunnerLogger,
    exec: RunnerExec,
) => Promise<tools.Tool[]>;

/**
 * Represents the capabilities that a SweBench a model can support to enable features in the agent.
 */
export type SweBenchAgentCapabilities = {
    /** Indicates if the agent supports tools for working with memory of prior sessions. */
    memory?: boolean;
    /**Indicates if the agent can choose which tool to use. */
    tool_choice?: boolean;
    /** Indicates if the supports reasoning capabilities and whether agent should enable features related to it. Default is true. Note: Think tool will only be added for non-reasoning model setting. */
    reasoning?: boolean;
    /** Indicates if the model should supports making parallel tool calls and whether agent should enable features related to it. Default is false. */
    parallel_tool_calls?: boolean;
    /** Indicates if the model should support vision capabilities and whether the agent should enable features related to it. Default is false. */
    vision?: boolean;
};

/**
 * Represents the system prompt parts that can be used to customize the agent's behavior for a model.
 */
export type SweBenchAgentSystemPromptParts = {
    /** expand scope of the changes to be made */
    rules?: string;

    /** Instructions for the agent on how to use tools specific to the model. This is added to the <tools> section of the system message. */
    toolInstructions?: string;

    /** Additional examples for the agent to follow. This will be added to the <examples> section of the system message. */
    examples?: string;
};

/**
 * Represents the user prompt parts that can be used to customize the agent's behavior for a model.
 */
export type SweBenchAgentUserPromptParts = {
    /** additional instructions to be added to the user message. This is added to the end of the user message. */
    additionalInstructions?: string;
};

/**
 * Represents the configuration for a SweBench agent for a specific model.
 * This configuration can be used to customize the agent's behavior based on the model's capabilities.
 */
export type SweBenchConfig = {
    supports?: SweBenchAgentCapabilities;
    createPR?: SweAgentActionConfig;
    prDetails?: SweAgentActionConfig;
    respondToPRComment?: SweAgentActionConfig;
};

/**
 * Represents the configuration for an agent's action, such as creating a PR or responding to a comment.
 */
export type SweAgentActionConfig = {
    systemMessage?: SweBenchAgentSystemPromptParts;
    userMessage?: SweBenchAgentUserPromptParts;
    toolInit?: ToolInit;
    options?: GetCompletionWithToolsOptions;
    /**
     * JIT instructions to use during the fix portion of the agent's action.
     * @todo somehow make these more flexible with their conditions/instruction callback?
     */
    jitInstructions?: {
        [jitInstructionId: string]: {
            /**
             * The actual instruction text to send to the model.
             */
            instruction: string | ((location: string) => string);

            /**
             * At what percentage (0-1) of total available execution time remaining should the JIT instruction be triggered.
             * - Instruction will only be triggered once.
             * - If multiple instructions are valid given the amount of time remaining, the one with the lowest percentage will be used.
             */
            percentRemainingOfTimeout: number;

            /**
             * If the JIT instruction should only be triggered there are no committed changes to paths.
             *
             * Defaults to false.
             */
            whenNoPathsChanged?: boolean;
        };
    };
};

export async function fixTools(
    config: tools.ToolConfig,
    issueNumber: number,
    settings: RuntimeSettings,
    logger: RunnerLogger,
    exec: RunnerExec,
): Promise<tools.Tool[]> {
    const bashTools = await getBashTools(config);

    // The bash tool's cwd callback will serve as the way other tools get the
    // current working directory of the agent which is using the tools.
    // Allow passed in config to override cwd if it wants.
    const configForOtherTools = { cwd: bashTools.bashTool.cwd, ...config };

    const memoryTools = await getMemoryTools(configForOtherTools, settings);

    return [
        bashTools.bashTool,
        ...(bashTools.otherTools ?? []),
        ...memoryTools,
        tools.str_replace_editor(configForOtherTools, logger, settings),
        tools.report_progress(configForOtherTools, issueNumber, settings, logger, exec),
        config?.requireReasoning ? tools.think(configForOtherTools) : undefined,
    ].filter((tool) => tool !== undefined); // Filter out undefined tools
}

export async function fixToolsForCLI(
    config: tools.ToolConfig,
    logger: RunnerLogger,
    settings?: RuntimeSettings,
    mcpClients?: Record<string, McpClient>,
): Promise<tools.Tool[]> {
    const bashTools = await getBashTools(config);
    const configForOtherTools = { cwd: bashTools.bashTool.cwd, ...config };

    // Load MCP tools if enabled
    const mcpTools: tools.Tool[] = [];
    if (settings && process.env.COPILOT_MCP_ENABLED === "true") {
        logger.info("MCP transport enabled for CLI");

        // OutOfProc MCP transport (from mcp-config.json)
        const outOfProcMcpTransport = new OutOfProcMCPTransport(settings, logger, config.toolConfirmationCallback);
        const outOfProcMcpTools: tools.Tool[] =
            (await outOfProcMcpTransport?.loadTools(
                pathJoin(process.env.COPILOT_AGENT_MCP_SERVER_TEMP ?? ".", "mcp-config.json"),
            )) ?? [];
        mcpTools.push(...outOfProcMcpTools);

        // InProc MCP transport (from MCP clients)
        const inProcMcpTransport = new InProcMCPTransport(settings, logger, config.toolConfirmationCallback);
        for (const [name, mcpClient] of Object.entries(mcpClients ?? {})) {
            const toolsFromProvider = await inProcMcpTransport.loadTools({
                clientName: name,
                mcpClient,
            });
            mcpTools.push(...toolsFromProvider);
        }
    } else {
        logger.info("MCP transport disabled for CLI");
    }

    return [
        bashTools.bashTool,
        ...(bashTools.otherTools ?? []),
        tools.str_replace_editor(configForOtherTools, logger),
        ...mcpTools,
        // Note: report_progress tool is excluded for CLI usage
        config?.requireReasoning ? tools.think(configForOtherTools) : undefined,
    ].filter((tool) => tool !== undefined); // Filter out undefined tools
}

export async function fixCommentTools(
    config: tools.ToolConfig,
    issueNumber: number,
    settings: RuntimeSettings,
    logger: RunnerLogger,
    exec: RunnerExec,
): Promise<tools.Tool[]> {
    return [...(await fixTools(config, issueNumber, settings, logger, exec)), tools.reply_to_comment(config, logger)];
}

export const defaultSweBenchConfig = (clientOptions?: ClientOptions): SweBenchConfig => ({
    ...DefaultSweBenchConfig,
    supports: {
        ...DefaultSweBenchConfig.supports,
        reasoning: clientOptions?.thinkingMode ? true : DefaultSweBenchConfig.supports?.reasoning,
    },
});

const defaultJitInstructions: SweAgentActionConfig["jitInstructions"] = {
    remindWhereToDoWork: {
        instruction: remindWhereToDoWorkNudge,
        // Percent is equivalent to 30 minutes remaining for a 60 minute timeout.
        percentRemainingOfTimeout: 1 / 2,
        whenNoPathsChanged: true,
    },
    completeAsSoonAsPossible: {
        instruction: fixTimeoutNudges.completeAsSoonAsPossible,
        // Percent is equivalent to 10 minutes remaining for a 60 minute timeout.
        percentRemainingOfTimeout: 1 / 6,
    },
    commitNow: {
        instruction: fixTimeoutNudges.commitNow,
        // Percent is equivalent to 8 minutes remaining for a 60 minute timeout.
        percentRemainingOfTimeout: 2 / 15,
    },
    finalAnswerNeeded: {
        instruction: fixTimeoutNudges.finalAnswerNeeded,
        // Percent is equivalent to 6 minutes remaining for a 60 minute timeout.
        percentRemainingOfTimeout: 1 / 10,
    },
};

const DefaultSweBenchConfig: SweBenchConfig = {
    supports: {
        tool_choice: false,
        // most of the models we will be working with will use reasoning by default
        reasoning: true,
        parallel_tool_calls: false, // Default to false, reasoning models like o-series do not support parallel tool calls.
    },
    createPR: {
        toolInit: fixTools,
        jitInstructions: defaultJitInstructions,
    },
    prDetails: {},
    respondToPRComment: {
        toolInit: fixCommentTools,
        jitInstructions: defaultJitInstructions,
    },
};

export type SweAgentModelConfig = Record<string, SweBenchConfig>;

export const SweAgentModelConfigMap: Partial<Record<SweAgentKind, SweAgentModelConfig>> = {
    "sweagent-capi": {
        "claude-3.5-sonnet": {
            ...DefaultSweBenchConfig,
            supports: {
                // Bedrock client removes cot summary for tool_choice: required and doesn't support tool_choice: none.
                tool_choice: false,
                reasoning: false,
            },
        },
        "claude-3.7-sonnet": {
            ...DefaultSweBenchConfig,
            supports: {
                // Bedrock client removes cot summary for tool_choice: required and doesn't support tool_choice: none.
                tool_choice: false,
                reasoning: false,
                vision: true,
            },
        },
        "claude-sonnet-4": {
            ...DefaultSweBenchConfig,
            supports: {
                // Bedrock client removes cot summary for tool_choice: required and doesn't support tool_choice: none.
                tool_choice: false,
                reasoning: false,
                parallel_tool_calls: true, // sonnet-4 has support for parallel tool calls.
                vision: true,
            },
        },
        oswe: {
            ...DefaultSweBenchConfig,
            supports: {
                // require tool_choice to enforce are more deterministic behavior for where tools are not to be used.
                tool_choice: true,
                reasoning: true,
            },
        },
    },
};

async function getBashTools(config: tools.ToolConfig): Promise<{ bashTool: tools.Tool; otherTools?: tools.Tool[] }> {
    const bashContext = new tools.InteractiveBashToolContext(config);

    return {
        bashTool: bashContext.getBashTool(),
        otherTools: [bashContext.getWriteBashTool(), bashContext.getReadBashTool(), bashContext.getStopBashTool()],
    };
}

async function getMemoryTools(config: tools.ToolConfig, settings: RuntimeSettings): Promise<tools.Tool[]> {
    if (isFeatureFlagEnabled(settings, "copilot_swe_agent_memory_in_repo_store")) {
        const memoryContext = new MemoryContext(new JsonFileMemoryStrategy(config.location));
        return [memoryContext.getStoreMemory(), memoryContext.getReadMemory()];
    }

    return [];
}
