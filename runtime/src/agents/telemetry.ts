/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { ChatCompletionMessageToolCall } from "openai/resources.mjs";
import { IAgentCallback } from "../callbacks/callback";
import { hashString } from "../helpers/crypto-helpers";
import { jsonStringifyHashed } from "../helpers/json-helpers";
import { ImageProcessingMetrics } from "../model/capi/image-utils";
import {
    Event,
    isAssistantMessageEvent,
    isToolMessageEvent,
    isUserMessageEvent,
    ToolExecutionEvent,
    TruncationEvent,
} from "../model/event";
import { DeepPartial, featureFlagsAsString, RuntimeSettings } from "../settings/types";
import { FEATURE_FLAGS_TELEMETRY_PROPERTY_NAME, TelemetryEvent } from "../telemetry";
import { Tool, ToolResultExpanded } from "../tools";

export type GetCompletionWithToolsTurnTelemetryEvent = TelemetryEvent<
    "get_completion_with_tools_turn",
    {
        properties: {
            /**
             * The name of the LLM used for the turn.
             */
            model: string;
            /**
             * The model information returned by the provider, will be a stringified JSON object.
             */
            modelInfo: string;
            /**
             * The ID of the LLM API call.
             *
             * - For calls to CAPI, this is likely to be the `azure_request_id` or some other internal request ID.
             */
            api_call_id: string | undefined;
            /**
             * The ID of the provider API call.
             *
             * - For calls to CAPI, this is a `gh.request_id`.
             */
            provider_call_id: string | undefined;
            /**
             * The name of the LLM used for the turn.
             */
            agent: string;
            /**
             * A stringified list of `{ toolCallId: string; toolName: string; }` for each tool call the LLM responded with.
             */
            toolCalls: string;
            /**
             * A stringified array of the tool call IDs for each tool calls which were executed.
             *
             * This should be the same length as the number of tool calls in the {@link toolCalls} property.
             */
            toolCallExecutions: string;
            /**
             * If the turn failed due to an error, then a string representing the error.
             */
            error: string | undefined;
            /**
             * A comma separated list of feature flags that were enabled for this run.
             */
            [FEATURE_FLAGS_TELEMETRY_PROPERTY_NAME]: string;
        };
        restrictedProperties: Record<string, never>;
        metrics: {
            /**
             * The current iteration number of the get_completion_with_tools loop.
             */
            turn: number;
            /**
             * How long it took to complete the turn.
             */
            turnDurationMs: number | undefined;
            /**
             * How long it took for the turn's LLM call to complete.
             */
            modelCallDurationMs: number | undefined;
            /**
             * How many times the turn's LLM call was retried.
             */
            retriesUsed: number | undefined;
            /**
             * How many calls to tools the LLM made during the turn.
             */
            numToolCalls: number | undefined;
            /**
             * How many tools were executed during the turn.
             *
             * If this number is less than {@link numTurnCalls}, then some of the tool calls failed/were not executed for some reason.
             */
            numToolExecutions: number | undefined;
            /**
             * How many large images had to be removed for the turn to be successful.
             */
            largeImagesRemoved: number | undefined;
            /**
             * How many images had to be removed for the turn to be successful.
             */
            imagesRemoved: number | undefined;
            /**
             * The number of prompt tokens the LLM response indicates we used.
             */
            responsePromptTokens: number | undefined;
            /**
             * The number of completion tokens the LLM response indicates we used.
             */
            responseCompletionTokens: number | undefined;
            /**
             * The number of total tokens the LLM response indicates we used.
             */
            responseTotalTokens: number | undefined;
            /**
             * The number of JIT instructions added during the turn.
             */
            jitInstructionsAdded: number | undefined;
        } &
            /**
             * Metrics for truncation will be based on the last truncation that was performed before sending the request to the model.
             */
            Partial<TruncationEvent["truncateResult"]> &
            Partial<ImageProcessingMetrics>;
    }
>;

export type ToolCallExecutedTelemetry = TelemetryEvent<
    "tool_call_executed",
    {
        properties: {
            resultType: ToolResultExpanded["resultType"];
            toolCallId: string;
            /**
             * For tools included with the runtime, the arguments provided to the tool call, but
             * with the values one-way obfuscated. For all other tools this will be `undefined`.
             */
            arguments: string;
            /**
             * The name of the tool that was called. Should be one-way obfuscated in some way
             * for any tool names potentially created by a third party.
             */
            toolName: string;
            /**
             * The ID of the LLM API call which contained the tool call that led to this tool execution.
             */
            api_call_id: string | undefined;
        } & ToolResultExpanded["toolTelemetry"]["properties"];
        restrictedProperties: {
            /**
             * The arguments provided to the tool call.
             */
            arguments: string;
            /**
             * The name of the tool that was called. Does not need to be obfuscated since
             * this is only available on the restricted topics.
             */
            toolName: string;
            /**
             * If the tool call failed due to error, then a string representation of the error.
             */
            error: string | undefined;
        } & ToolResultExpanded["toolTelemetry"]["restrictedProperties"];
        metrics: {
            durationMs: number;
        } & ToolResultExpanded["toolTelemetry"]["metrics"];
    }
>;

type TurnData = {
    // #region properties data
    model?: string;
    modelInfo?: string;
    api_call_id?: string;
    provider_call_id?: string;
    agent?: string;
    toolCalls: {
        toolCallId: string;
        toolName: string;
        arguments: string;
        safeForTelemetryToolName: string;
        safeForTelemetryArguments: string;
    }[];
    toolCallExecutions: string[];
    error?: string;
    featureFlagsAsString: string;
    // #endregion properties data

    // #region metrics data
    turn: number;
    startTimeMs?: number;
    endTimeMs?: number;
    modelCallDurationMs?: number;
    retriesUsed?: number;
    largeImagesRemoved?: number;
    imagesRemoved?: number;
    responsePromptTokens?: number;
    responseCompletionTokens?: number;
    responseTotalTokens?: number;
    jitInstructionsAdded?: number;
    truncateResult?: TruncationEvent["truncateResult"];
    imageProcessingMetrics?: ImageProcessingMetrics;
    // #endregion metrics data
};

export function getToolCallFromTurnData(turnData: TurnData, toolCallId: string): TurnData["toolCalls"][0] | undefined {
    return turnData.toolCalls.find((tc) => tc.toolCallId === toolCallId);
}

export function redactToolCallBasedOnToolTelemetryRules(
    toolsInUse: Tool[],
    toolCall: ChatCompletionMessageToolCall,
): TurnData["toolCalls"][0] {
    const toolName = toolCall.function.name;
    try {
        const parsedArguments = JSON.parse(toolCall.function.arguments || "{}");
        const tool = toolsInUse.find((t) => t.name === toolName);
        const safeForTelemetry = tool?.safeForTelemetry || {
            name: false,
            inputsNames: false,
        };
        return {
            toolCallId: toolCall.id,
            toolName: toolName,
            arguments: toolCall.function.arguments,
            safeForTelemetryToolName:
                safeForTelemetry === true || safeForTelemetry.name ? toolName : hashString(toolName),
            safeForTelemetryArguments:
                safeForTelemetry === true || safeForTelemetry.inputsNames ? jsonStringifyHashed(parsedArguments) : "",
        };
    } catch {
        return {
            toolCallId: toolCall.id,
            toolName: toolName,
            arguments: toolCall.function.arguments,
            safeForTelemetryToolName: hashString(toolName),
            safeForTelemetryArguments: "",
        };
    }
}

/**
 * Emits telemetry events on behalf of the runtime agent.
 */
export class AgentTelemetryEmitter {
    private forAgent: string;
    private turnDataCache: Map<number, TurnData>;
    private emittedTurns: Set<number> = new Set();
    private callback: IAgentCallback;
    private settings: DeepPartial<RuntimeSettings>;
    private toolsInUse: Tool[];

    constructor(
        forAgent: string,
        callback: IAgentCallback,
        settings: DeepPartial<RuntimeSettings>,
        toolsInUse: Tool[],
    ) {
        this.forAgent = forAgent;
        this.turnDataCache = new Map<number, TurnData>();
        this.emittedTurns = new Set<number>();
        this.callback = callback;
        this.settings = settings;
        this.toolsInUse = toolsInUse;
    }

    /**
     * For testing only.
     */
    getTurnData(): Map<number, TurnData> {
        return this.turnDataCache;
    }

    async ingestEvent(event: Event): Promise<void> {
        let turn: number;
        let turnData: TurnData;
        if ("turn" in event && typeof event.turn === "number") {
            // Turn telemetry is 1 indexed, maybe change after removing old turn telemetry code?
            turn = event.turn + 1;
            if (this.emittedTurns.has(turn)) {
                // Too late we already emitted telemetry for this turn
                return;
            }
            if (!this.turnDataCache.has(turn)) {
                this.turnDataCache.set(turn, {
                    featureFlagsAsString: featureFlagsAsString(this.settings),
                    toolCalls: [],
                    toolCallExecutions: [],
                    turn: turn,
                });
            }
            turnData = this.turnDataCache.get(turn)!;
        } else {
            // cannot do anything unless the event has a turn number
            return;
        }

        if (event.kind === "tool_execution") {
            await this.ingestToolExecutionEventAndEmitToolCallExecutedTelemetry(turnData, event as ToolExecutionEvent);
        } else if (
            event.kind === "turn_started" ||
            event.kind === "turn_failed" ||
            event.kind === "turn_retry" ||
            event.kind === "turn_ended"
        ) {
            turnData = {
                ...turnData,
                model: event.model,
                modelInfo: JSON.stringify(event.modelInfo),
            };
            if (event.kind === "turn_started") {
                turnData = { ...turnData, startTimeMs: event.timestampMs };
            } else if (event.kind === "turn_retry") {
                turnData = {
                    ...turnData,
                    endTimeMs: event.timestampMs,
                    retriesUsed: (turnData.retriesUsed ?? 0) + 1,
                };
            } else if (event.kind === "turn_failed") {
                turnData = {
                    ...turnData,
                    endTimeMs: event.timestampMs,
                    error: event.error,
                };
            } else if (event.kind === "turn_ended") {
                turnData = { ...turnData, endTimeMs: event.timestampMs };
            }
        } else if (event.kind === "history_truncated") {
            turnData = {
                ...turnData,
                truncateResult: event.truncateResult,
            };
        } else if (event.kind === "image_processing") {
            turnData = {
                ...turnData,
                imageProcessingMetrics: event.imageProcessingMetrics,
            };
        } else if (event.kind === "images_removed") {
            turnData = {
                ...turnData,
                largeImagesRemoved: (turnData.largeImagesRemoved ?? 0) + (event.largeImagesRemoved ? 1 : 0),
                imagesRemoved: (turnData.imagesRemoved ?? 0) + (event.imagesRemoved ? 1 : 0),
            };
        } else if (event.kind === "model_call_success" || event.kind === "model_call_failure") {
            turnData = {
                ...turnData,
                modelCallDurationMs: event.modelCallDurationMs,
                api_call_id: event.modelCall.api_id,
                provider_call_id: event.modelCall.request_id,
            };
            if (event.kind === "model_call_success") {
                turnData = {
                    ...turnData,
                    responsePromptTokens: event.responseUsage?.prompt_tokens,
                    responseCompletionTokens: event.responseUsage?.completion_tokens,
                    responseTotalTokens: event.responseUsage?.total_tokens,
                };
            }
        } else if (isUserMessageEvent(event) && event.source === "jit-instruction") {
            turnData = {
                ...turnData,
                jitInstructionsAdded: (turnData?.jitInstructionsAdded ?? 0) + 1,
            };
        } else if (isAssistantMessageEvent(event) && event.message.tool_calls) {
            turnData = {
                ...turnData,
                toolCalls: turnData.toolCalls.concat(
                    event.message.tool_calls.map((tc) => ({
                        ...redactToolCallBasedOnToolTelemetryRules(this.toolsInUse, tc),
                    })),
                ),
            };
        } else if (isToolMessageEvent(event)) {
            turnData = {
                ...turnData,
                toolCallExecutions: turnData.toolCallExecutions.concat([event.message.tool_call_id]),
            };
        }
        this.turnDataCache.set(turn, turnData);

        if (event.kind === "turn_ended") {
            await this.emitTurnTelemetry(turn);
            this.emittedTurns.add(turn);
            this.turnDataCache.delete(turn);
        }
    }

    private async emitTurnTelemetry(turn: number): Promise<void> {
        const turnData = this.turnDataCache.get(turn);
        if (turnData) {
            const telemetryEvent: GetCompletionWithToolsTurnTelemetryEvent = {
                kind: "telemetry",
                telemetry: {
                    event: "get_completion_with_tools_turn",
                    properties: {
                        model: turnData.model || "default",
                        modelInfo: turnData.modelInfo || "{}",
                        api_call_id: turnData.api_call_id,
                        provider_call_id: turnData.provider_call_id,
                        agent: this.forAgent,
                        // This field does differ from the original get_completion_with_tools_turn event!
                        toolCalls: JSON.stringify(
                            turnData.toolCalls.map((tc) => ({
                                toolCallId: tc.toolCallId,
                                toolName: tc.safeForTelemetryToolName,
                            })),
                        ),
                        // This field does differ from the original get_completion_with_tools_turn event!
                        toolCallExecutions: JSON.stringify(turnData.toolCallExecutions),
                        error: turnData.error,
                        featureFlags: featureFlagsAsString(this.settings),
                    },
                    metrics: {
                        turn: turn,
                        turnDurationMs:
                            turnData.endTimeMs !== undefined && turnData.startTimeMs !== undefined
                                ? turnData.endTimeMs - turnData.startTimeMs
                                : 0,
                        modelCallDurationMs: turnData.modelCallDurationMs,
                        retriesUsed: turnData.retriesUsed ?? 0,
                        numToolCalls: turnData.toolCalls.length,
                        numToolExecutions: turnData.toolCallExecutions.length,
                        largeImagesRemoved: turnData.largeImagesRemoved,
                        imagesRemoved: turnData.imagesRemoved,
                        responsePromptTokens: turnData.responsePromptTokens,
                        responseCompletionTokens: turnData.responseCompletionTokens,
                        responseTotalTokens: turnData.responseTotalTokens,
                        jitInstructionsAdded: turnData.jitInstructionsAdded,
                        ...turnData.truncateResult,
                        ...turnData.imageProcessingMetrics,
                    },
                    restrictedProperties: {},
                },
            };
            await this.callback.progress(telemetryEvent);
        }
    }

    private async ingestToolExecutionEventAndEmitToolCallExecutedTelemetry(
        turnData: TurnData,
        event: ToolExecutionEvent,
    ): Promise<void> {
        const toolCallId = event.toolCallId;
        const toolCallData = getToolCallFromTurnData(turnData, toolCallId);
        if (toolCallData) {
            const toolResult = event.toolResult;
            const telemetryEvent: ToolCallExecutedTelemetry = {
                kind: "telemetry",
                telemetry: {
                    event: "tool_call_executed",
                    properties: {
                        ...toolResult.toolTelemetry.properties,
                        resultType: toolResult.resultType,
                        toolCallId: toolCallId,
                        arguments: toolCallData.safeForTelemetryArguments,
                        toolName: toolCallData.safeForTelemetryToolName,
                        api_call_id: turnData.api_call_id,
                    },
                    restrictedProperties: {
                        ...toolResult.toolTelemetry.restrictedProperties,
                        arguments: toolCallData.arguments,
                        toolName: toolCallData.toolName,
                        error: toolResult.error,
                    },
                    metrics: {
                        ...toolResult.toolTelemetry.metrics,
                        durationMs: event.durationMs,
                    },
                },
            };
            await this.callback.progress(telemetryEvent);
        }
    }
}
