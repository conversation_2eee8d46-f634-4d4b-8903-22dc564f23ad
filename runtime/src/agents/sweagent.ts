/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import type { IAgentCallback } from "../callbacks/callback";
import { AIClient as AIPAIClient } from "../model/aip-swe-agent/ai";
import { AIClient as AnthropicAIClient } from "../model/anthropic/ai";
import { AIClient as CAPIAIClient } from "../model/capi/ai";
import { OpenAIClientProvider } from "../model/capi/types";
import type { Client, ClientOptions } from "../model/client";
import { DefaultAgentModel } from "../model/defaults";
import { AzureOpenAIClientProvider } from "../model/openai/aoai-client";
import { AIClient as OAIClient } from "../model/openai/oai-chat-completions-ai";
import { OpenAIKeyBasedAIClientProvider } from "../model/openai/oai-client";
import { AIClient as OAIResponseAIClient } from "../model/openai/oai-responses-ai";
import { RunnerLogger } from "../runner";
import { RunnerExec } from "../runner/exec";
import { RuntimeSettings } from "../settings";
import { isFeatureFlagEnabled } from "../settings/types";
import { ToolConfig } from "../tools";
import { ClientInfo } from "../tools/mcp-transport";
import { defaultSweBenchConfig, SweAgentModelConfigMap } from "./config";
import * as swebench from "./swebench";

export type SweAgentAction<T> = (
    location: string,
    problem_statement: string,
    repo: string,
    issueNumber: number,
    branchName: string,
    config?: Partial<ToolConfig>,
) => Promise<T>;
export type SweAgentKind = "sweagent-capi" | "sweagent-aip" | "sweagent-anthropic" | "sweagent-openai";

export type PRDetails = {
    title: string;
    description: string;
    diff?: string;
};

export interface SweAgent {
    createPRAgent: SweAgentAction<PRDetails>;
    respondToPRCommentAgent: SweAgentAction<PRDetails>;
}
export type EvaluationType = keyof SweAgent;

export function getAgent(
    settings: RuntimeSettings,
    logger: RunnerLogger,
    exec: RunnerExec,
    agent?: SweAgentKind,
    clientOptions?: ClientOptions,
    mcpClients?: ClientInfo[],
    callback?: IAgentCallback,
    client?: Client,
): SweAgent {
    client = client ?? getClient(settings, logger, agent, clientOptions);
    const config =
        SweAgentModelConfigMap[agent ?? DefaultAgentModel.agent]?.[client.model] ??
        defaultSweBenchConfig(clientOptions);

    // Enable memory prompt pieces if the feature flag is enabled.
    config.supports ??= {};
    config.supports.memory = isFeatureFlagEnabled(settings, "copilot_swe_agent_memory_in_repo_store");

    // Disable vision support for models where it's supported if the feature flag is not enabled.
    if (config.supports?.vision && !isFeatureFlagEnabled(settings, "copilot_swe_agent_vision")) {
        config.supports.vision &&= false;
    }

    return new swebench.SweBenchAgent(client, settings, logger, exec, config, mcpClients, callback);
}

export function getClient(
    settings: RuntimeSettings,
    logger: RunnerLogger,
    agent?: SweAgentKind,
    clientOptions?: ClientOptions,
): Client {
    // thinking mode is used for extended thinking or responses.
    // Only use the feature flag, if the clientOptions is not defined.
    if (
        clientOptions?.thinkingMode === undefined &&
        isFeatureFlagEnabled(settings, "copilot_swe_agent_thinking_mode")
    ) {
        clientOptions = {
            ...clientOptions,
            thinkingMode: true,
        };
    }

    switch (agent) {
        case "sweagent-aip":
            return new AIPAIClient(settings, logger, clientOptions);
        case "sweagent-anthropic":
            return new AnthropicAIClient(settings, logger, clientOptions);
        case "sweagent-capi":
            return new CAPIAIClient(settings, logger, clientOptions);
        case "sweagent-openai":
            return clientOptions?.thinkingMode
                ? new OAIResponseAIClient(getOpenAIClientProvider(settings), settings, logger, clientOptions)
                : new OAIClient(getOpenAIClientProvider(settings), settings, logger, clientOptions);
        default:
            // Default to CAPI with Sonnet
            return new CAPIAIClient(settings, logger, clientOptions);
    }
}

function getOpenAIClientProvider(settings: RuntimeSettings): OpenAIClientProvider {
    return settings.api?.openai?.azure?.url ? new AzureOpenAIClientProvider() : new OpenAIKeyBasedAIClientProvider();
}
