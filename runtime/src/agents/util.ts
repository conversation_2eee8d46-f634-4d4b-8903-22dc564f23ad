/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { ChatCompletionMessageParam } from "openai/resources";

/**
 * A constant representing an empty key.
 * Used to present when there should be no content.
 */
export const NO_CONTENT_TOKEN = "<empty>";

/**
 * Extracts XML-like tags from a string into a strongly-typed object.
 *
 * This function handles nested tags and only extracts top-level elements.
 * It uses a stack-based approach for robust parsing of XML-like structures
 * without requiring a full XML parser. Nested tags are always included as
 * part of the content string.
 *
 * If a tag is opened but not closed before the end of the string,
 * the function will still extract the content collected so far for that tag.
 *
 * @example
 * ```typescript
 * interface PullRequest {
 *   pr_title?: string;
 *   pr_description?: string;
 * }
 *
 * const text = `Some text
 * <pr_title>My PR Title</pr_title>
 * <pr_description>This is a description with <nested>tags</nested></pr_description>`;
 *
 * const result = extractTopLevelXmlTags<PullRequest>(text);
 * // result: {
 * //   pr_title: "My PR Title",
 * //   pr_description: "This is a description with <nested>tags</nested>"
 * // }
 * ```
 *
 * @param content The string containing XML-like tags
 * @returns An object with properties corresponding to the top-level tag names
 */
export function extractTopLevelXmlTags<T extends Record<string, string | undefined>>(content: string): T {
    const result = {} as T;

    // Stack-based approach to handle only top-level tags
    const stack: string[] = [];
    let currentTag = "";
    let currentContent = "";
    let inTag = false;

    // Process character by character
    for (let i = 0; i < content.length; i++) {
        const char = content[i];
        const nextChar = content[i + 1] || "";
        const isLastChar = i === content.length - 1;

        // Opening tag detection
        if (char === "<" && nextChar !== "/") {
            if (inTag && stack.length > 0) {
                // We're inside a tag and found another opening tag
                currentContent += char;
            } else {
                // Start of a new tag
                inTag = true;
                currentTag = "";
                i++; // Move past '<'

                // Extract tag name
                while (i < content.length && content[i] !== ">") {
                    currentTag += content[i];
                    i++;
                }

                stack.push(currentTag);
                currentContent = "";
            }
        }
        // Closing tag detection
        else if (char === "<" && nextChar === "/") {
            i += 2; // Move past '</'
            let closingTag = "";

            // Extract closing tag name
            while (i < content.length && content[i] !== ">") {
                closingTag += content[i];
                i++;
            }

            // Check if this is the closing tag for our current open tag
            if (stack.length > 0 && stack[stack.length - 1] === closingTag) {
                // We're closing the current tag
                if (stack.length === 1) {
                    // It's a top-level tag, save it
                    const trimmedContent = currentContent.trim();
                    (result as Record<string, string>)[closingTag] =
                        trimmedContent === NO_CONTENT_TOKEN ? "" : trimmedContent;
                } else {
                    // It's a nested tag, add the closing tag to parent content
                    currentContent = currentContent + `</${closingTag}>`;
                }

                stack.pop();

                // If we still have tags in the stack, we need to continue collecting content
                if (stack.length > 0) {
                    inTag = true;
                } else {
                    inTag = false;
                }
            } else if (inTag) {
                // Nested closing tag, include it in content
                currentContent += `</${closingTag}>`;
            }
        }
        // Content collection
        else if (inTag) {
            currentContent += char;
        }

        // Handle case where we reach the end of string with open tags
        if (isLastChar && stack.length > 0) {
            // Save content for the currently open tag(s)
            if (inTag) {
                const lastTag = stack[0]; // Get the first (top-level) tag
                const trimmedContent = currentContent.trim();
                (result as Record<string, string>)[lastTag] = trimmedContent === NO_CONTENT_TOKEN ? "" : trimmedContent;
            }
        }
    }

    return result;
}

/**
 * Creates an XML tag schema string from an object type's properties.
 *
 * This function uses a sample object to extract property names at runtime,
 * which allows it to work with TypeScript interfaces.
 *
 * @example
 * ```typescript
 * // Using with a type and sample object:
 * interface PullRequest {
 *   pr_title: string;
 *   pr_description: string;
 * }
 *
 * const schema = createTagSchema<PullRequest>({
 *   pr_title: '',
 *   pr_description: ''
 * });
 * // Output:
 * // <pr_title>
 * // </pr_title>
 * //
 * // <pr_description>
 * // </pr_description>
 *
 * // Alternative: using with specific property names:
 * const schema2 = createTagSchema(['author', 'message']);
 * ```
 *
 * @param input A sample object matching type T, or an array of property names
 * @param indentation Optional string for indentation between tags (default is newline)
 * @returns A string with the XML tag schema
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function createTagSchema<T extends Record<string, any>>(
    input: T | (keyof T)[] | string[],
    indentation: string = "\n",
): string {
    // Determine properties based on input type
    let properties: string[];

    if (Array.isArray(input)) {
        // Input is already an array of property names
        properties = input.map(String);
    } else if (typeof input === "object" && input !== null) {
        // Input is a sample object, extract its keys
        properties = Object.keys(input);
    } else {
        // Invalid input
        return "";
    }

    if (properties.length === 0) {
        return "";
    }

    // Generate tag schema
    return properties
        .map((prop) => {
            return `<${prop}>\n</${prop}>`;
        })
        .join(indentation);
}

export function preprocessAgentHistory(history: ChatCompletionMessageParam[]): [ChatCompletionMessageParam[], boolean] {
    const processedHistory: ChatCompletionMessageParam[] = [];
    let containsScreenshots = false;

    // removing image_url items to avoid sending vision request second time for the same image
    for (const msg of history) {
        const message = { ...msg };
        if (messageContainsScreenshot(msg)) {
            containsScreenshots = true;
            if (Array.isArray(msg.content) && msg.content.length > 0) {
                const textPart = msg.content.at(0);
                if (textPart && typeof textPart === "object" && "text" in textPart) {
                    message.content = textPart.text;
                }
            }
        }
        processedHistory.push(message);
    }

    return [processedHistory, containsScreenshots];
}

export function messageContainsScreenshot(msg: ChatCompletionMessageParam): boolean {
    if (msg.role !== "user" || !msg.content || !Array.isArray(msg.content) || msg.content.length === 0) {
        return false;
    }
    const contentPart = msg.content.at(0);
    if (!contentPart || typeof contentPart !== "object" || !("text" in contentPart)) {
        return false;
    }

    if (contentPart.text.startsWith("Here is the url of a screenshot:")) {
        return true;
    }

    return false;
}
