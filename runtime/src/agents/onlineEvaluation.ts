/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import * as fs from "fs";
import { ChatCompletionMessageParam } from "openai/resources";
import { IAgentCallback } from "../callbacks/callback";
import type { Client, GetCompletionWithToolsOptions } from "../model/client";
import { splitAgentModelSetting } from "../model/util";
import { RunnerLogger } from "../runner";
import { <PERSON>Exe<PERSON> } from "../runner/exec";
import { Defa<PERSON>G<PERSON><PERSON><PERSON><PERSON> } from "../runner/git/default";
import { RuntimeSettings } from "../settings";
import { demandInstanceId } from "../settings/demand";
import { TelemetryEvent } from "../telemetry";
import {
    baseTrajectoryMessagePrompt,
    fixCommentTrajectoryMessagePrompt,
    fixTrajectoryMessagePrompt,
    problemClassificationMessagePrompt,
    qualityMessagePrompt,
    taskHardnessMessagePrompt,
} from "./prompts";
import { EvaluationType, getClient } from "./sweagent";

/**
 * Telemetry for online evaluation
 */
type OnlineEvalHardnessTelemetry = TelemetryEvent<
    "online_eval_hardness",
    {
        properties: Record<string, never>;
        restrictedProperties: {
            justification: string;
        };
        metrics: {
            difficulty: number;
        };
    }
>;

type OnlineEvalProblemClassificationTelemetry = TelemetryEvent<
    "online_eval_problem_classification",
    {
        properties: {
            problemClassification: string;
        };
        restrictedProperties: Record<string, never>;
        metrics: Record<string, never>;
    }
>;

type OnlineEvaluationQualityTelemetry = TelemetryEvent<
    "online_eval_quality",
    {
        properties: {
            values: string;
        };
        restrictedProperties: Record<string, never>;
        metrics: Record<string, never>;
    }
>;

type OnlineEvaluationTrajectoryTelemetry = TelemetryEvent<
    "online_eval_trajectory",
    {
        properties: {
            values: string;
        };
        restrictedProperties: Record<string, never>;
        metrics: Record<string, never>;
    }
>;

export class OnlineEvaluation {
    private readonly client: Client;

    constructor(
        private readonly settings: RuntimeSettings,
        private readonly logger: RunnerLogger,
    ) {
        // Create a new client instance with gpt-4.1 model using the same configuration
        this.client = this.createGpt4Client(logger, settings);
    }

    private createGpt4Client(logger: RunnerLogger, settings: RuntimeSettings): Client {
        // Extract agent type from settings, falling back to parsing from the original client's model if needed
        const agentModelSetting = settings.service?.agent?.model;
        const { agent: agentType } = splitAgentModelSetting(agentModelSetting);

        // Get the original client options from settings, preserving all configuration except model
        const originalClientOptions = settings.service?.agent || {};
        const newClientOptions = {
            ...originalClientOptions,
            model: "gpt-4.1",
            requestHeaders: { "X-Initiator": "agent" },
        };

        // Create new client with the same agent type but gpt-4.1 model
        return getClient(settings, logger, agentType, newClientOptions);
    }

    public async evaluate(
        evaluationType: EvaluationType,
        repo_location: string,
        problem_statement: string,
        initialCommit: string,
        trajectoryOutputFile: string,
        logger: RunnerLogger,
        exec: RunnerExec,
        callback?: IAgentCallback,
    ): Promise<void> {
        logger.debug("Running online evaluation...");

        const results = [];
        results.push(await this.taskHardnessOnlineEvaluation(problem_statement, logger, callback));
        results.push(await this.classifyProblemOnlineEvaluation(problem_statement, logger, callback));
        results.push(
            await this.qualityMessageOnlineEvaluation(
                problem_statement,
                repo_location,
                initialCommit,
                logger,
                exec,
                callback,
            ),
        );
        results.push(await this.trajectoryOnlineEvaluation(evaluationType, trajectoryOutputFile, logger, callback));

        if (this.settings.onlineEvaluation?.enableOnlineEvaluationOutputFile) {
            const onlineEvaluationResults = {
                values: results,
            };

            const outputDir = `/tmp/online_evaluation_${demandInstanceId(this.settings)}`;
            fs.mkdirSync(outputDir, { recursive: true });
            fs.writeFileSync(outputDir + `/onlineEvaluationResults.json`, JSON.stringify(onlineEvaluationResults));
            logger.debug(`Online evaluation results saved to ${outputDir}/onlineEvaluationResults.json`);
        }

        logger.debug("Online evaluation complete.");
    }

    private async taskHardnessOnlineEvaluation(
        problem_statement: string,
        logger: RunnerLogger,
        callback?: IAgentCallback,
    ): Promise<OnlineEvalHardnessTelemetry | undefined> {
        try {
            const taskHardnessResult: string = await this.evaluateOnlineEvaluationPrompt(
                taskHardnessMessagePrompt(problem_statement),
            );

            const taskHardnessObject = JSON.parse(taskHardnessResult);

            const hardnessEvent: OnlineEvalHardnessTelemetry = {
                kind: "telemetry",
                telemetry: {
                    event: "online_eval_hardness",
                    properties: {},
                    restrictedProperties: {
                        justification: taskHardnessObject.justification,
                    },
                    metrics: {
                        difficulty: taskHardnessObject.difficulty,
                    },
                },
            };
            await callback?.progress(hardnessEvent);

            return hardnessEvent;
        } catch (error) {
            logger.debug("Online evaluation hardness error:" + error);
            return;
        }
    }

    private async classifyProblemOnlineEvaluation(
        problem_statement: string,
        logger: RunnerLogger,
        callback?: IAgentCallback,
    ): Promise<OnlineEvalProblemClassificationTelemetry | undefined> {
        try {
            const problemClassificationResult: string = await this.evaluateOnlineEvaluationPrompt(
                problemClassificationMessagePrompt(problem_statement),
            );

            const problemClassificationEvent: OnlineEvalProblemClassificationTelemetry = {
                kind: "telemetry",
                telemetry: {
                    event: "online_eval_problem_classification",
                    properties: {
                        problemClassification: problemClassificationResult,
                    },
                    restrictedProperties: {},
                    metrics: {},
                },
            };
            await callback?.progress(problemClassificationEvent);

            return problemClassificationEvent;
        } catch (error) {
            logger.debug("Online evaluation classify problem error:" + error);
            return;
        }
    }

    private async qualityMessageOnlineEvaluation(
        problem_statement: string,
        repo_location: string,
        initialCommit: string,
        logger: RunnerLogger,
        exec: RunnerExec,
        callback?: IAgentCallback,
    ): Promise<OnlineEvaluationQualityTelemetry | undefined> {
        try {
            // Git the diff of all commits between the initial commit and the current HEAD
            const git = new DefaultGitHandler(logger, exec);
            const diff = await git.diffCommits(repo_location, initialCommit, "HEAD");

            const qualityMessageResult: string = await this.evaluateOnlineEvaluationPrompt(
                qualityMessagePrompt(problem_statement, diff),
            );

            const qualityMessageEvent: OnlineEvaluationQualityTelemetry = {
                kind: "telemetry",
                telemetry: {
                    event: "online_eval_quality",
                    properties: {
                        values: qualityMessageResult,
                    },
                    metrics: {},
                    restrictedProperties: {},
                },
            };
            await callback?.progress(qualityMessageEvent);

            return qualityMessageEvent;
        } catch (error) {
            logger.debug("Online evaluation quality message error:" + error);
            return;
        }
    }

    private async trajectoryOnlineEvaluation(
        evaluationType: EvaluationType,
        trajectoryOutputFile: string,
        logger: RunnerLogger,
        callback?: IAgentCallback,
    ): Promise<OnlineEvaluationTrajectoryTelemetry | undefined> {
        try {
            const trajectory = fs.readFileSync(trajectoryOutputFile, "utf8");
            let extendedPromptMessage: string;
            switch (evaluationType) {
                case "createPRAgent":
                    extendedPromptMessage = fixTrajectoryMessagePrompt();
                    break;
                case "respondToPRCommentAgent":
                    extendedPromptMessage = fixCommentTrajectoryMessagePrompt();
                    break;
            }

            const trajectoryResult = await this.evaluateOnlineEvaluationPrompt(
                baseTrajectoryMessagePrompt(trajectory) + "\n" + extendedPromptMessage,
            );
            const trajectoryObjectEvent: OnlineEvaluationTrajectoryTelemetry = {
                kind: "telemetry",
                telemetry: {
                    event: "online_eval_trajectory",
                    properties: {
                        values: trajectoryResult,
                    },
                    restrictedProperties: {},
                    metrics: {},
                },
            };
            await callback?.progress(trajectoryObjectEvent);

            return trajectoryObjectEvent;
        } catch (error) {
            logger.debug("Online evaluation trajectory error:" + error);
            return;
        }
    }

    private async evaluateOnlineEvaluationPrompt(message: string): Promise<string> {
        const initialUserMessage: ChatCompletionMessageParam = {
            role: "user",
            content: "userMessage",
        };

        const options: GetCompletionWithToolsOptions = {
            failIfInitialInputsTooLong: true,
        };
        const stream = this.client.getCompletionWithTools(message, [initialUserMessage], [], options);

        const onlineEvaluationResultMessages: ChatCompletionMessageParam[] = [];
        for await (const message of stream) {
            if (message.kind === "response") {
                onlineEvaluationResultMessages.push(message.response);
            }
        }

        const lastMessage = onlineEvaluationResultMessages[onlineEvaluationResultMessages.length - 1]?.content;
        const taskHardnessResult: string = typeof lastMessage === "string" ? lastMessage : "";
        return taskHardnessResult;
    }
}
