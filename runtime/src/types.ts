/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { BlockedRequest } from "./firewall";
import type { Event } from "./model/event";
import { EventTelemetry, TelemetryEvent } from "./telemetry";

export type AgentCallbackProgressEvent =
    | (({ kind: "log"; message: string } | Event) & {
          /**
           * Progress events may have telemetry associated with them.
           */
          telemetry?: EventTelemetry;
      })
    | TelemetryEvent;
export type AgentCallbackResultEvent = {
    diff: string;
    branchName: string;
    prTitle: string;
    prDescription: string;
    blockedRequests?: BlockedRequest[];
};
// Note: This is a contract with sweagentd. See https://github.com/github/sweagentd/blob/463009a5df2a9297ef617b33504209001cbe42f9/internal/agentcallback/jobmanager.go#L753
export type AgentCallbackErrorEvent = {
    text: string;
    name?: string;
    message?: string;
    stack?: string;
    stdout?: string;
    stderr?: string;
    blockedRequests?: BlockedRequest[];
    request_id?: string;
    ghRequestId?: string;
    cmd?: string;
    code?: number | string;
    signal?: string;
    skipReport?: boolean;
    isVisionFlow?: boolean;
};
export type AgentCallbackPartialResultEvent = {
    branchName: string;
    message: string;
};
export type AgentCallbackCommentReplyEvent = {
    comment_id: number;
    message: string;
};
