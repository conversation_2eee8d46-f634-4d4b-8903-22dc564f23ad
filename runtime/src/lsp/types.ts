/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { ChildProcess } from "node:child_process";
import { LSPAny } from "vscode-languageserver-types";
import { RunnerLogger } from "../runner";

export type LSPServer = {
    /**
     * The process of the LSP server.
     */
    process: ChildProcess;
};

/**
 * Defines a configuration for a Language Server Protocol (LSP) server that can
 * be spawned by the runtime.
 */
export type LSPConfig = {
    /**
     * A unique identifier for the LSP server configuration.
     *
     * @example "typescript"
     */
    id: string;
    /**
     * What file extensions are associated with this LSP server. When a file of this type is encountered
     * the server should be spawned at the root of the project associated with the file.
     *
     * @example [".ts", ".js", ".jsx", ".tsx"]
     */
    fileExtensions: string[];
    /**
     * The names or a set of names of files one might find at the root of a project for this LSP server.
     * If any of these files or one of the complete sets is found in a directory, then
     * that directory will be considered the root of a project.
     *
     * @example ["package.json", "tsconfig.json"]
     */
    projectRootFiles: (string | string[])[];
    /**
     * Initialization options to be sent to the LSP server.
     */
    lspInitializationOptions?: LSPAny;
    /**
     * A callback to spawn an instance of the LSP server at the given project root.
     */
    spawnServer: (projectRoot: string, logger: RunnerLogger) => Promise<LSPServer>;
};
