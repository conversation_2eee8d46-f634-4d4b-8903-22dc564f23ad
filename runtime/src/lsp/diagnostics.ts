/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { Diagnostic, DiagnosticSeverity } from "vscode-languageserver-types";
import { editedFileDiagnosticsFeedbackPrompt, nonEditedFileDiagnosticsFeedbackPrompt } from "../agents/prompts";

const severityToString: Map<DiagnosticSeverity | undefined, string> = new Map([
    [DiagnosticSeverity.Error, "ERROR"],
    [DiagnosticSeverity.Warning, "WARN"],
    [DiagnosticSeverity.Information, "INFO"],
    [DiagnosticSeverity.Hint, "HINT"],
    [undefined, "DIAGNOSTIC"],
]);

export function prettyPrintDiagnostic(diagnostic: Diagnostic): string {
    const severity = severityToString.get(diagnostic.severity);
    const line = diagnostic.range.start.line + 1;
    const col = diagnostic.range.start.character + 1;
    return `${severity} [${line}:${col}] ${diagnostic.message}`;
}

export function organizeDiagnosticsByFile(
    diagnostics: { uri: string; diagnostics: Diagnostic[] }[],
): Map<string, Diagnostic[]> {
    const diagnosticsByFile = new Map<string, Diagnostic[]>();
    for (const diagnostic of diagnostics) {
        if (!diagnosticsByFile.has(diagnostic.uri)) {
            diagnosticsByFile.set(diagnostic.uri, []);
        }
        diagnosticsByFile.get(diagnostic.uri)?.push(...diagnostic.diagnostics);
    }
    return diagnosticsByFile;
}

const fileProtocol = "file://";
export function fileUriToPath(fileUri: string): string {
    if (fileUri.startsWith(fileProtocol)) {
        return fileUri.slice(fileProtocol.length);
    }
    return fileUri;
}

export function getDiagnosticsEditFeedbackText(
    editedFilePath: string,
    diagnostics: { uri: string; diagnostics: Diagnostic[] }[],
): string {
    const diagnosticsWithFilePaths = diagnostics.map((diagnostic) => ({
        uri: fileUriToPath(diagnostic.uri),
        diagnostics: diagnostic.diagnostics,
    }));

    const organizedDiagnostics = organizeDiagnosticsByFile(diagnosticsWithFilePaths);
    const feedbackForCurrentFile = organizedDiagnostics.get(editedFilePath) || [];
    const feedbackForOtherFiles = Array.from(organizedDiagnostics.entries())
        .filter(([uri]) => uri !== editedFilePath)
        .filter(([_, diagnostics]) => diagnostics.length > 0);

    const editedFileFeedbackText = editedFileDiagnosticsFeedbackPrompt(feedbackForCurrentFile);
    const otherFilesFeedbackText = nonEditedFileDiagnosticsFeedbackPrompt(feedbackForOtherFiles);

    return editedFileFeedbackText + otherFilesFeedbackText;
}
