/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { ChildProcess } from "node:child_process";
import {
    createMessageConnection,
    MessageConnection,
    StreamMessageReader,
    StreamMessageWriter,
} from "vscode-jsonrpc/node";
import {
    ClientCapabilities,
    Diagnostic,
    DidChangeTextDocumentParams,
    DidCloseTextDocumentParams,
    DidOpenTextDocumentParams,
    InitializeParams,
    PublishDiagnosticsParams,
    TextDocumentContent<PERSON><PERSON>eE<PERSON>,
    TextDocumentItem,
    VersionedTextDocumentIdentifier,
} from "vscode-languageserver-protocol";
import { executeWithTimeout } from "../model/capi/promise-helpers.js";
import { RunnerLogger } from "../runner/index.js";
import { findProjectRootForFile, getConfigForFile } from "./configs.js";
import { LSPConfig } from "./types";

const SERVER_SPAWN_TIMEOUT_MS = 30 * 1000;

export type DiagnosticsHandler = (uri: string, diagnostics: Diagnostic[]) => void;

export class LSPClientFactory {
    private static singleton: LSPClientFactory | undefined;
    public static getInstance(logger: RunnerLogger): LSPClientFactory {
        if (!LSPClientFactory.singleton) {
            LSPClientFactory.singleton = new LSPClientFactory(logger);
        }
        return LSPClientFactory.singleton;
    }

    /**
     * Map of all created LSP clients, keyed by `${LSPConfig.id}-${projectRoot}`.
     */
    clients: Map<string, LSPClient>;

    private logger: RunnerLogger;

    constructor(logger: RunnerLogger) {
        this.clients = new Map<string, LSPClient>();
        this.logger = logger;
    }

    get numCachedClients(): number {
        return this.clients.size;
    }

    /**
     * If the factory can attempt creating an LSP client for the specified file.
     */
    canCreateForFile(filePath: string): boolean {
        const config = getConfigForFile(filePath);
        return config !== undefined;
    }

    /**
     * Creates or retrieves an existing LSP client for the specified file's project. Will return after the
     * LSP server is started.
     */
    async getOrCreateForFile(filePath: string, highestPossibleProjectRoot: string): Promise<LSPClient | undefined> {
        const config = getConfigForFile(filePath);
        if (!config) {
            this.logger.debug(`No LSP config found for file: ${filePath}`);
            return undefined;
        }

        const projectRoot = findProjectRootForFile(filePath, highestPossibleProjectRoot);
        if (!projectRoot) {
            this.logger.debug(`No LSP project root found for file: ${filePath} under ${highestPossibleProjectRoot}`);
            return undefined;
        }

        const clientKey = `${config.id}-${projectRoot}`;
        const clientToUse =
            this.clients.get(clientKey) ||
            new LSPClient(projectRoot, config, this.logger, () => this.removeClient(clientToUse));
        this.clients.set(clientKey, clientToUse);

        try {
            await clientToUse.start();
            return clientToUse;
        } catch (error) {
            this.logger.error(`Failed to start ${config.id} LSP client for ${filePath} during get or create: ${error}`);

            // If the client fails to start, remove it from the cache so we can try again later for the same project.
            this.removeClient(clientToUse);

            throw error;
        }
    }

    private removeClient(client: LSPClient): void {
        this.clients.delete(client.key);
    }
}

export type WaitedForDiagnostics = {
    uri: string;
    diagnostics: Diagnostic[];
    receivedAfterMs: number;
}[];

/**
 * LSP Client for connecting to and communicating with Language Server Protocol servers.
 * Focuses specifically on diagnostics functionality.
 */
export class LSPClient {
    private projectRoot: string;
    private config: LSPConfig;
    private connection: MessageConnection | undefined;
    private startPromise: Promise<void> | undefined;
    private serverProcess: ChildProcess | undefined;
    private isInitialized: boolean;
    private documentVersion: Map<string, number>;
    private diagnosticsHandlers: Set<DiagnosticsHandler>;
    private logger: RunnerLogger;
    private onDispose?: () => void;

    constructor(projectRoot: string, config: LSPConfig, logger: RunnerLogger, onDispose?: () => void) {
        this.isInitialized = false;
        this.documentVersion = new Map<string, number>();
        this.diagnosticsHandlers = new Set<DiagnosticsHandler>();
        this.projectRoot = projectRoot;
        this.config = config;
        this.logger = logger;
        this.onDispose = onDispose;
    }

    start(): Promise<void> {
        if (!this.startPromise) {
            this.startPromise = this.doStart();
        }
        return this.startPromise;
    }

    private async doStart(): Promise<void> {
        if (this.connection || this.serverProcess) {
            throw new Error("LSP client is already started");
        }

        this.logger.debug(`Starting ${this.config.id} LSP client at project root: ${this.projectRoot}`);

        const serverPromise = this.config.spawnServer(this.projectRoot, this.logger);

        const server = await executeWithTimeout(
            serverPromise,
            SERVER_SPAWN_TIMEOUT_MS,
            new Error(`${this.logPrefix}: Server spawn timed out after ${SERVER_SPAWN_TIMEOUT_MS}ms`),
            "reject",
        );

        this.serverProcess = server.process;

        this.serverProcess.on("error", (error) => {
            this.logger.error(`${this.logPrefix} LSP server error: ${error.message}`);
        });

        this.serverProcess.on("exit", (code, signal) => {
            this.logger.debug(`${this.logPrefix} LSP server exited with code ${code} and signal ${signal}`);
            if (code !== 0 && code !== null) {
                this.logger.error(`${this.logPrefix} LSP server exited with code ${code}`);
            }
            this.dispose();
        });

        if (!this.serverProcess.stdin || !this.serverProcess.stdout) {
            throw new Error("Failed to create language server process with proper stdio");
        }

        const reader = new StreamMessageReader(this.serverProcess.stdout);
        const writer = new StreamMessageWriter(this.serverProcess.stdin);
        this.connection = createMessageConnection(reader, writer);

        this.connection.onNotification("textDocument/publishDiagnostics", (params: PublishDiagnosticsParams) => {
            this.handleDiagnostics(params);
        });

        this.connection.listen();

        await this.initialize();
    }

    private async initialize(): Promise<void> {
        if (!this.connection) {
            throw new Error("Connection not established");
        }

        const initParams: InitializeParams = {
            processId: process.pid,
            rootUri: this.projectRoot || null,
            workspaceFolders: [
                {
                    uri: `file://${this.projectRoot}`,
                    name: "workspace",
                },
            ],
            capabilities: this.getClientCapabilities(),
            initializationOptions: this.config.lspInitializationOptions,
        };

        await this.connection.sendRequest("initialize", initParams);

        await this.connection.sendNotification("initialized", {});

        this.isInitialized = true;

        this.logger.debug(`${this.logPrefix} Language server initialized successfully`);
    }

    private getClientCapabilities(): ClientCapabilities {
        return {
            workspace: {
                configuration: true,
            },
            textDocument: {
                synchronization: {
                    dynamicRegistration: false,
                    willSave: false,
                    willSaveWaitUntil: false,
                    didSave: false,
                },
                publishDiagnostics: {
                    relatedInformation: true,
                    versionSupport: false,
                    codeDescriptionSupport: true,
                    dataSupport: true,
                },
            },
        };
    }

    async openOrUpdateDocument(uri: string, content: string): Promise<"opened" | "updated"> {
        if (!this.connection || !this.isInitialized) {
            throw new Error("LSP client not initialized");
        }

        if (this.documentVersion.has(uri)) {
            await this.updateDocument(uri, content);
            return "updated";
        } else {
            await this.openDocument(uri, content);
            return "opened";
        }
    }

    private async openDocument(uri: string, content: string): Promise<void> {
        if (!this.connection || !this.isInitialized) {
            throw new Error("LSP client not initialized");
        }

        const version = 1;
        this.documentVersion.set(uri, version);

        const textDocument: TextDocumentItem = {
            uri,
            languageId: this.config.id,
            version,
            text: content,
        };

        const params: DidOpenTextDocumentParams = {
            textDocument,
        };

        await this.connection.sendNotification("textDocument/didOpen", params);
    }

    private async updateDocument(
        uri: string,
        content: string,
        range?: {
            start: { line: number; character: number };
            end: { line: number; character: number };
        },
    ): Promise<void> {
        if (!this.connection || !this.isInitialized) {
            throw new Error("LSP client not initialized");
        }

        const currentVersion = this.documentVersion.get(uri) || 0;
        const newVersion = currentVersion + 1;
        this.documentVersion.set(uri, newVersion);

        const versionedTextDocument: VersionedTextDocumentIdentifier = {
            uri,
            version: newVersion,
        };

        const contentChanges: TextDocumentContentChangeEvent[] = range
            ? [
                  {
                      range,
                      text: content,
                  },
              ]
            : [
                  {
                      text: content,
                  },
              ];

        const params: DidChangeTextDocumentParams = {
            textDocument: versionedTextDocument,
            contentChanges,
        };

        await this.connection.sendNotification("textDocument/didChange", params);
    }

    async closeDocument(uri: string): Promise<void> {
        if (!this.connection || !this.isInitialized) {
            throw new Error("LSP client not initialized");
        }

        const params: DidCloseTextDocumentParams = {
            textDocument: { uri },
        };

        await this.connection.sendNotification("textDocument/didClose", params);
        this.documentVersion.delete(uri);
    }

    waitForDiagnostics(timeoutMs: number): Promise<WaitedForDiagnostics> {
        const startTimeMs = Date.now();
        return new Promise((resolve) => {
            const allDiagnostics: WaitedForDiagnostics = [];
            const handler: DiagnosticsHandler = (uri: string, diagnostics: Diagnostic[]) => {
                allDiagnostics.push({
                    uri: uri,
                    diagnostics: diagnostics,
                    receivedAfterMs: Date.now() - startTimeMs,
                });
            };
            this.diagnosticsHandlers.add(handler);

            setTimeout(() => {
                this.diagnosticsHandlers.delete(handler);
                resolve(allDiagnostics);
            }, timeoutMs);
        });
    }

    private handleDiagnostics(params: PublishDiagnosticsParams): void {
        for (const handler of this.diagnosticsHandlers) {
            handler(params.uri, params.diagnostics);
        }
    }

    async shutdown(): Promise<number | null> {
        if (!this.connection || !this.isInitialized) {
            this.dispose();
            return null;
        }

        try {
            // Send shutdown request
            await this.connection.sendRequest("shutdown");

            // Send exit notification
            await this.connection.sendNotification("exit");
        } catch (error) {
            this.logger.error(`${this.logPrefix} Error during shutdown: ${error}`);
        }

        let exitCode: number | null = null;

        // Wait for the process to exit if it's still running
        if (this.serverProcess && !this.serverProcess.killed) {
            exitCode = await new Promise<number | null>((resolve) => {
                const process = this.serverProcess!;

                const onExit = (code: number | null) => {
                    process.off("exit", onExit);
                    resolve(code);
                };

                process.on("exit", onExit);

                // Give it a moment to exit naturally, then force kill
                setTimeout(() => {
                    if (!process.killed) {
                        process.kill("SIGKILL");
                        resolve(-1); // Return -1 for force-killed processes
                    }
                }, 1000);
            });
        }

        this.dispose();
        return exitCode;
    }

    private dispose(): void {
        this.onDispose?.();

        if (this.connection) {
            this.connection.dispose();
            this.connection = undefined;
        }

        if (this.serverProcess && !this.serverProcess.killed) {
            this.serverProcess.kill();
        }
        this.serverProcess = undefined;

        this.isInitialized = false;
        this.documentVersion.clear();
        this.diagnosticsHandlers.clear();
    }

    get initialized(): boolean {
        return this.isInitialized;
    }

    get running(): boolean {
        return this.serverProcess !== undefined && !this.serverProcess.killed;
    }

    get key(): string {
        return `${this.config.id}-${this.projectRoot}`;
    }

    private get logPrefix(): string {
        return `LSP ${this.config.id} server for ${this.projectRoot}:`;
    }
}
