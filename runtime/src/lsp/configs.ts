/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { spawn } from "node:child_process";
import { existsSync } from "node:fs";
import { dirname, extname, join } from "node:path";
import { fileURLToPath } from "node:url";
import { RunnerLogger } from "../runner/index.js";
import { LSPConfig, LSPServer } from "./types.js";

export const typescriptLSPConfig: LSPConfig = {
    id: "typescript",
    fileExtensions: [".ts", ".tsx", ".js", ".jsx", ".mjs", ".cjs", ".mts", ".cts"],
    projectRootFiles: ["package.json", "tsconfig.json"],
    spawnServer: (projectRoot: string, logger: RunnerLogger): Promise<LSPServer> => {
        return new Promise<LSPServer>((resolve, reject) => {
            logger.debug(`Starting TypeScript Language Server at project root: ${projectRoot}`);

            // Try to determine if we're in a bundled environment by looking for the bundled typescript-language-server
            let command: string;
            let args: string[];

            // Check if we're running from a bundled dist directory
            const currentDir = dirname(fileURLToPath(import.meta.url));
            const bundledServerPath = join(
                currentDir,
                ".",
                "node_modules",
                "typescript-language-server",
                "lib",
                "cli.mjs",
            );

            logger.debug(`From cwd of '${currentDir}', checking for bundled server at: ${bundledServerPath}`);

            if (existsSync(bundledServerPath)) {
                // Use the bundled typescript-language-server
                logger.debug(`Using bundled TypeScript Language Server at: ${bundledServerPath}`);
                command = "node";
                args = [bundledServerPath, "--stdio"];
            } else {
                // Development mode - use npx
                logger.debug("Using npx for TypeScript Language Server (development mode)");
                command = "npx";
                args = ["typescript-language-server", "--stdio"];
            }

            const serverProcess = spawn(command, args, {
                cwd: projectRoot,
                stdio: ["pipe", "pipe", "pipe"],
            });

            const errorListener = (error: unknown) => {
                reject(`LSP server error: ${error}`);
            };
            serverProcess.on("error", errorListener);

            const exitListener = (code: number | null, signal: NodeJS.Signals | null) => {
                logger.debug(`LSP server exited with code ${code} and signal ${signal}`);
                if (code !== 0 && code !== null) {
                    reject(`LSP server exited with code ${code}`);
                }
            };
            serverProcess.on("exit", exitListener);

            // The server is ready when it starts successfully
            // For LSP servers, they typically start immediately and communicate via stdio
            serverProcess.on("spawn", () => {
                // Clean up listeners used just during spawn
                serverProcess.off("error", errorListener);
                serverProcess.off("exit", exitListener);

                logger.debug(`TypeScript LSP server spawned successfully for project: ${projectRoot}`);
                resolve({
                    process: serverProcess,
                });
            });
        });
    },
};

const lspConfigs: LSPConfig[] = [typescriptLSPConfig];

export function getConfigForFile(filePath: string): LSPConfig | undefined {
    const fileExtension = extname(filePath);
    return lspConfigs.find((config) => config.fileExtensions.includes(fileExtension));
}

// Cache for project root lookups: file path -> project root
const projectRootCache = new Map<string, string | undefined>();
export function findProjectRootForFile(filePath: string, stopAtDirectory: string): string | undefined {
    if (!stopAtDirectory || stopAtDirectory.trim() === "") {
        return undefined;
    }

    // Create a cache key that includes both the file path and stop directory
    const cacheKey = `${filePath}:${stopAtDirectory}`;

    // Check cache first
    if (projectRootCache.has(cacheKey)) {
        return projectRootCache.get(cacheKey);
    }

    const config = getConfigForFile(filePath);
    if (!config) {
        // Cache the undefined result
        projectRootCache.set(cacheKey, undefined);
        return undefined;
    }

    if (!filePath.startsWith(stopAtDirectory)) {
        // Cache the undefined result
        projectRootCache.set(cacheKey, undefined);
        return undefined;
    }

    const result = findProjectRoot(filePath, config, stopAtDirectory);

    // Cache the result (even if undefined)
    projectRootCache.set(cacheKey, result);

    return result;
}

/**
 * Find the project root directory for a given file path based on the LSP config's project root files.
 * @param filePath The path to the file
 * @param config The LSP configuration to use
 * @param stopAtDirectory Directory to stop searching at
 * @returns The project root directory, or undefined if no project root is found
 */
function findProjectRoot(filePath: string, config: LSPConfig, stopAtDirectory: string): string | undefined {
    let currentDir = dirname(filePath);

    // Walk up the directory tree looking for project root files
    // Stop at least when we reach the filesystem root or the specified stop directory
    while (currentDir.startsWith(stopAtDirectory) && currentDir !== dirname(currentDir)) {
        // Check if any of the project root files exist in this directory
        const hasProjectRootFile = config.projectRootFiles.some((fileOrFiles) => {
            if (Array.isArray(fileOrFiles)) {
                // All files in the array must exist
                return fileOrFiles.every((file) => existsSync(join(currentDir, file)));
            } else {
                // Single file must exist
                return existsSync(join(currentDir, fileOrFiles));
            }
        });

        if (hasProjectRootFile) {
            return currentDir;
        }

        // Move up one directory level
        currentDir = dirname(currentDir);
    }

    return undefined;
}
