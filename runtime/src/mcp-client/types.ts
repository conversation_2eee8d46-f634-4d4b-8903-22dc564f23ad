/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { ContentFilterMode } from "../helpers/content-filter";

// Base interface with common properties
interface MCPServerConfigBase {
    /**
     * List of tools to include from this server. [] means none. "*" means all.
     */
    tools: string[];
    /**
     * Indicates "remote" or "local" server type.
     * If not specified, defaults to "local".
     */
    type?: string;
    /**
     * Optional. Denotes if this is a MCP server we have defined to be used when
     * the user has not provided their own MCP server config.
     *
     * Marked optional as configs coming from users will/should not have this set. Defaults to `false`.
     */
    isDefaultServer?: boolean;
    /**
     * Optional. Either a content filter mode for all tools from this server, or a map of tool name to content filter mode for the tool with that name.
     * If not specified, defaults to "hidden_characters"
     */
    filterMapping?: Record<string, ContentFilterMode> | ContentFilterMode;
}

export interface MCPLocalServerConfig extends MCPServerConfigBase {
    type?: "local";
    command: string;
    args: string[];
    /**
     * An object of the environment variables to pass to the server. Key is whats sent to the MCP Server. Value is whats read from the actions environment. Empty means no env vars passed.
     */
    env?: Record<string, string>;
}

export interface MCPRemoteServerConfig extends MCPServerConfigBase {
    type: "http" | "sse";
    /**
     * URL of the remote server
     * NOTE: this has to be converted to a URL object before giving to transport.
     * TransportFactory will handle this conversion.
     */
    url: string;
    /**
     * Optional. HTTP headers to include in requests to the remote server.
     * This can be used for authentication or other purposes.
     * For example, you might include an Authorization header.
     */
    headers?: Record<string, string>;
}

export type MCPServerConfig = MCPLocalServerConfig | MCPRemoteServerConfig;

// Keep the same structure for the overall config
export interface MCPServersConfig {
    mcpServers: Record<string, MCPServerConfig>;
}
