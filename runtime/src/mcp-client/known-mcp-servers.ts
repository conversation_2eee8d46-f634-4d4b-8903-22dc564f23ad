/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

export type KnownMCPServer = {
    tools: {
        toolName: string;
        /**
         * TODO: If we want to capture the names of inputs of user provided
         * MCP tools in unrestricted telemetry, we should also validate that the the names
         * of user provided MCP tools are known and thus safe for telemetry.
         */
        inputsNames?: never;
    }[];
};

/**
 * A map of server name to tools OR another name for the server (an alias).
 */
export const knownMCPServers: {
    [serverName: string]: KnownMCPServer | string;
} = {
    fetch: {
        tools: [{ toolName: "fetch" }],
    },
    time: {
        tools: [{ toolName: "get_current_time" }, { toolName: "convert_time" }],
    },
    sequentialthinking: {
        tools: [{ toolName: "sequentialthinking" }],
    },
    "github-mcp-server": {
        tools: [
            { toolName: "get_code_scanning_alert" },
            { toolName: "get_commit" },
            { toolName: "get_file_contents" },
            { toolName: "get_issue" },
            { toolName: "get_issue_comments" },
            { toolName: "get_me" },
            { toolName: "get_pull_request" },
            { toolName: "get_pull_request_comments" },
            { toolName: "get_pull_request_files" },
            { toolName: "get_pull_request_reviews" },
            { toolName: "get_pull_request_status" },
            { toolName: "get_secret_scanning_alert" },
            { toolName: "get_tag" },
            { toolName: "list_branches" },
            { toolName: "list_code_scanning_alerts" },
            { toolName: "list_commits" },
            { toolName: "list_issues" },
            { toolName: "list_pull_requests" },
            { toolName: "list_secret_scanning_alerts" },
            { toolName: "list_tags" },
            { toolName: "search_code" },
            { toolName: "search_issues" },
            { toolName: "search_repositories" },
            { toolName: "search_users" },
        ],
    },
    playwright: {
        tools: [
            { toolName: "browser_close" },
            { toolName: "browser_resize" },
            { toolName: "browser_console_messages" },
            { toolName: "browser_handle_dialog" },
            { toolName: "browser_file_upload" },
            { toolName: "browser_install" },
            { toolName: "browser_press_key" },
            { toolName: "browser_navigate" },
            { toolName: "browser_navigate_back" },
            { toolName: "browser_navigate_forward" },
            { toolName: "browser_network_requests" },
            { toolName: "browser_pdf_save" },
            { toolName: "browser_take_screenshot" },
            { toolName: "browser_snapshot" },
            { toolName: "browser_click" },
            { toolName: "browser_drag" },
            { toolName: "browser_hover" },
            { toolName: "browser_type" },
            { toolName: "browser_select_option" },
            { toolName: "browser_tab_list" },
            { toolName: "browser_tab_new" },
            { toolName: "browser_tab_select" },
            { toolName: "browser_tab_close" },
            { toolName: "browser_generate_playwright_test" },
            { toolName: "browser_wait_for" },
        ],
    },
    azure: "azure-mcp-server",
    "azure-azmcp": "azure-mcp-server",
    "azure-mcp-server-azmcp": "azure-mcp-server",
    "azure-mcp-server": {
        tools: [
            { toolName: "aks" },
            { toolName: "appconfig" },
            { toolName: "azureterraformbestpractices" },
            { toolName: "bestpractices" },
            { toolName: "bicepschema" },
            { toolName: "cosmos" },
            { toolName: "datadog" },
            { toolName: "documentation" },
            { toolName: "extension_az" },
            { toolName: "extension_azd" },
            { toolName: "extension_azqr" },
            { toolName: "foundry" },
            { toolName: "grafana" },
            { toolName: "group" },
            { toolName: "keyvault" },
            { toolName: "kusto" },
            { toolName: "loadtesting" },
            { toolName: "marketplace" },
            { toolName: "monitor" },
            { toolName: "postgres" },
            { toolName: "redis" },
            { toolName: "role" },
            { toolName: "search" },
            { toolName: "servicebus" },
            { toolName: "sql" },
            { toolName: "storage" },
            { toolName: "subscription" },
            { toolName: "workbooks" },
        ],
    },
};

/**
 * @param otherKnownMCPServers This param should only be used for testing.
 */
export function isKnownMCPTool(
    serverName: string,
    toolName: string,
    otherKnownMCPServers?: typeof knownMCPServers,
): boolean {
    let server = (otherKnownMCPServers || knownMCPServers)[serverName.toLowerCase()];
    const visitedAliases = new Set<string>();

    while (typeof server === "string") {
        if (visitedAliases.has(server)) {
            // Circular reference detected, break the loop
            return false;
        }
        visitedAliases.add(server);
        server = knownMCPServers[server];
    }

    if (!server) {
        return false;
    }
    const toolNameLower = toolName.toLowerCase();
    return server.tools.some((tool) => tool.toolName === toolNameLower);
}
