/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import { SSEClientTransport } from "@modelcontextprotocol/sdk/client/sse.js";
import { StdioClientTransport } from "@modelcontextprotocol/sdk/client/stdio.js";
import { StreamableHTTPClientTransport } from "@modelcontextprotocol/sdk/client/streamableHttp.js";
export interface StdioTransportConfig {
    type: "stdio";
    command: string;
    args: string[];
    env: Record<string, string>;
}

export interface HTTPTransportConfig {
    type: "http";
    url: string;
    headers?: Record<string, string>;
}

export interface SSETransportConfig {
    type: "sse";
    url: string;
    headers?: Record<string, string>;
}

export type TransportOptions = StdioTransportConfig | HTTPTransportConfig | SSETransportConfig;

export interface TransportFactory {
    createTransport(
        transportOptions: TransportOptions,
    ): StdioClientTransport | StreamableHTTPClientTransport | SSEClientTransport;
}

export class DefaultTransportFactory implements TransportFactory {
    createTransport(
        transportOptions: TransportOptions,
    ): StdioClientTransport | StreamableHTTPClientTransport | SSEClientTransport {
        switch (transportOptions.type) {
            case "stdio":
                return new StdioClientTransport({
                    command: transportOptions.command,
                    args: transportOptions.args,
                    env: transportOptions.env,
                });
            case "http":
                return new StreamableHTTPClientTransport(new URL(transportOptions.url), {
                    // TODO (belaltaher8): support more customization of RequestInit and OAuth (also for SSE)
                    requestInit: {
                        headers: transportOptions.headers || {},
                    },
                });
            case "sse":
                return new SSEClientTransport(new URL(transportOptions.url), {
                    requestInit: {
                        headers: transportOptions.headers || {},
                    },
                });
            default:
                // This should never happen if the type is correctly enforced
                // but we include it for completeness and type safety.
                // If this happens, it means the transportOptions were not correctly typed.
                throw new Error(
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    `Unsupported transport type: ${(transportOptions as any).type}`,
                );
        }
    }
}
