/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import { Client } from "@modelcontextprotocol/sdk/client/index.js";

export interface ClientFactory {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    createClient(options: any, capabilities: any): Client;
}

export class DefaultClientFactory implements ClientFactory {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    createClient(options: any, capabilities: any): Client {
        return new Client(options, capabilities);
    }
}
