/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { ConsoleLogger } from "../runner/logger/console";
import { getOrInitSettings } from "../settings/factory";
import { MCPRegistry } from "./mcp-registry";
import { MCPServer } from "./mcp-server";
import { ServerConfigProcessor } from "./server-config-processor";

import { ToolConfigWriter } from "./tool-config-writer";

const logger = new ConsoleLogger();

export async function main() {
    logger.log("Created MCP Registry instance");
    const registry = new MCPRegistry(logger);
    const serverConfigProcessor = new ServerConfigProcessor(logger, registry);
    const toolConfigWriter = new ToolConfigWriter(logger, registry);
    const server = new MCPServer(logger, registry);

    const runtimeSettings = await getOrInitSettings();
    const allServersConfig = serverConfigProcessor.ReadMcpConfigFromEnv(runtimeSettings);

    if (!allServersConfig) {
        throw new Error("Failed to read MCP configuration from environment variable.");
    }

    await serverConfigProcessor.processServers(allServersConfig);

    await toolConfigWriter.writeToolConfig(allServersConfig);

    try {
        await server.startServer();
    } catch (error) {
        logger.error(`Failed to start MCP server: ${error}`);
        process.exit(1);
    }
    logger.log("MCP Tool server started successfully");
}

main().catch(logger.error);
