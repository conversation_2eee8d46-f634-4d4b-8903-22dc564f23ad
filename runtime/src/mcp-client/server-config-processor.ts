/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { ContentFilterMode } from "../helpers/content-filter";
import { RunnerLogger } from "../runner";
import { RuntimeSettings } from "../settings/types";
import { MCPRegistry } from "./mcp-registry";
import { MCPLocalServerConfig, MCPRemoteServerConfig, MCPServerConfig, MCPServersConfig } from "./types";

const defaultMcpConfig: MCPServersConfig = { mcpServers: {} };

export class ServerConfigProcessor {
    private remoteEnabled: boolean;
    private mcp3pEnabled: boolean;
    private blackbirdMetisIndexEnabled: boolean;
    private mcpEnvConfig: string | undefined;

    constructor(
        private logger: RunnerLogger,
        private registry: MCPRegistry,
    ) {
        this.remoteEnabled = false;
        this.mcp3pEnabled = false;
        this.mcpEnvConfig = undefined;
        this.blackbirdMetisIndexEnabled = false;
    }

    public ReadMcpConfigFromEnv(runtimeSettings: RuntimeSettings): MCPServersConfig | undefined {
        this.mcpEnvConfig = process.env.GITHUB_COPILOT_MCP_JSON;
        this.mcp3pEnabled = process.env.GITHUB_COPILOT_3P_MCP_ENABLED === "true";
        this.remoteEnabled = process.env.GITHUB_COPILOT_REMOTE_MCP_ENABLED === "true";
        this.blackbirdMetisIndexEnabled = process.env.BLACKBIRD_METIS_INDEX_ENABLED === "true";
        const allServersConfig: MCPServersConfig = this.validateEnvConfig();

        // Skip adding default MCP servers in CLI mode
        const isCliMode = process.env.GITHUB_COPILOT_CLI_MODE === "true";
        if (!isCliMode) {
            this.logger.log("Adding default MCP servers to configuration");
            this.configureBlackbirdMcp(allServersConfig, runtimeSettings);
            this.configureGitHubMcp(allServersConfig);
            this.configurePlaywrightMcp(allServersConfig);
        } else {
            this.logger.log("CLI mode detected - skipping default MCP servers");
        }

        return allServersConfig;
    }

    private validateEnvConfig(): MCPServersConfig {
        if (!this.mcp3pEnabled) {
            // If 3P is not enabled, we use default configuration
            this.logger.log("User-provided MCPs are disabled");
            return defaultMcpConfig;
        }

        // If 3P is enabled, we try to read from the environment, and use default if we have issues
        this.logger.log("User-provided MCPs are enabled, checking for environment variable");
        if (!this.mcpEnvConfig) {
            this.logger.log("No user-provided MCP servers found");
            return defaultMcpConfig;
        }

        try {
            const mcpServersConfig: MCPServersConfig = JSON.parse(this.mcpEnvConfig);
            if (!mcpServersConfig.mcpServers) {
                throw new Error("User-provided config had incorrect format. Missing 'mcpServers' property.");
            }

            // Users can/should/do not need to set/define `isDefaultServer` property, we will set it to false for them
            for (const serverName in mcpServersConfig.mcpServers) {
                mcpServersConfig.mcpServers[serverName].isDefaultServer = false;
            }

            return mcpServersConfig;
        } catch (error) {
            this.logger.error(`Warning: User-provided MCP servers were defined but invalid: ${error}`);
            return defaultMcpConfig;
        }
    }

    private configureGitHubMcp(mcpServersConfig: MCPServersConfig) {
        if (this.remoteEnabled) {
            this.configureRemoteGitHubMcp(mcpServersConfig);
        } else {
            this.configureLocalGitHubMcp(mcpServersConfig);
        }
    }

    private configureRemoteGitHubMcp(mcpServersConfig: MCPServersConfig) {
        this.logger.log("Using default remote GitHub MCP server configuration");
        const headers: Record<string, string> = {
            Authorization: "Bearer " + process.env.GITHUB_PERSONAL_ACCESS_TOKEN,
            "X-MCP-Toolsets": "repos,issues,users,pull_requests,code_security,secret_protection,actions",
            "X-MCP-Host": "github-coding-agent",
        };
        const githubMcpServerConfig: MCPRemoteServerConfig = {
            type: "http",
            url: "https://api.githubcopilot.com/mcp/readonly",
            headers: headers,
            tools: ["*"],
            isDefaultServer: true,
            filterMapping: {
                // The default filter level is hidden_characters, but some tools we want markdown
                get_issue: ContentFilterMode.Markdown,
                get_issue_comments: ContentFilterMode.Markdown,
                get_pull_request: ContentFilterMode.Markdown,
                get_pull_request_comments: ContentFilterMode.Markdown,
                get_pull_request_reviews: ContentFilterMode.Markdown,
            },
        };
        mcpServersConfig.mcpServers["github-mcp-server"] = githubMcpServerConfig;
    }

    private configureLocalGitHubMcp(mcpServersConfig: MCPServersConfig) {
        // GitHub MCP server configuration - only add if not already provided by user
        if (!mcpServersConfig.mcpServers["github-mcp-server"]) {
            this.logger.log("Using default local GitHub MCP server configuration");
            const githubMcpServerConfig: MCPLocalServerConfig = {
                command: "./copilot-developer-action-main/github-mcp-server/github-mcp-server",
                args: ["stdio", "--read-only"],
                env: {
                    GITHUB_PERSONAL_ACCESS_TOKEN: "GITHUB_PERSONAL_ACCESS_TOKEN",
                },
                tools: ["*"],
                isDefaultServer: true,
            };
            mcpServersConfig.mcpServers["github-mcp-server"] = githubMcpServerConfig;
        } else {
            // Only override the command if the user didn't provide one
            this.logger.log("Using user-provided GitHub MCP server configuration");
            const userConfig = mcpServersConfig.mcpServers["github-mcp-server"] ?? ({} as MCPServerConfig);
            if (this.isLocalServerConfig(userConfig) && !userConfig.command) {
                mcpServersConfig.mcpServers["github-mcp-server"] = {
                    ...userConfig,
                    command: "./copilot-developer-action-main/github-mcp-server/github-mcp-server",
                    isDefaultServer: true,
                };
                this.logger.log("Added default command to user-provided GitHub MCP server configuration");
            }
        }
    }

    private configurePlaywrightMcp(mcpServersConfig: MCPServersConfig) {
        // Playwright MCP server configuration - only add if not already provided by user
        if (!mcpServersConfig.mcpServers["playwright"]) {
            // If the feature flag to use playwright behind the firewall is enabled, we don't configure the server here
            // instead it will be launched by the runtime, which is subject to the firewall.
            if (
                (process.env.COPILOT_FEATURE_FLAGS ?? "")
                    .split(",")
                    .map((flag) => flag.trim())
                    .includes("copilot_swe_agent_playwright_use_firewall")
            ) {
                this.logger.log("Playwright MCP server is configured to use the firewall, skipping launch.");
                return;
            }

            this.logger.log("Enabling Playwright MCP server");

            const playwrightMcpServerConfig: MCPLocalServerConfig = {
                command: "npx",

                // Only enable requesting localhost so that we can staff-ship.
                // We can remove this argument when Playwright is behind the firewall.
                args: [
                    "@playwright/mcp@latest",
                    "--allowed-origins",
                    "localhost;localhost:*;127.0.0.1;127.0.0.1:*",
                    "--viewport-size",
                    "1280, 720",
                ],
                tools: ["*"],
                isDefaultServer: true,
            };
            mcpServersConfig.mcpServers["playwright"] = playwrightMcpServerConfig;
        }
    }

    private configureBlackbirdMcp(mcpServersConfig: MCPServersConfig, runtimeSettings: RuntimeSettings) {
        if (this.remoteEnabled) {
            this.configureRemoteBlackbirdMcp(mcpServersConfig);
        } else if (this.blackbirdMetisIndexEnabled) {
            this.configureLocalMetisBlackbirdMCP(mcpServersConfig, runtimeSettings);
        } else {
            this.configureLocalBlackbirdMcp(mcpServersConfig);
        }
    }

    private configureLocalMetisBlackbirdMCP(mcpServersConfig: MCPServersConfig, runtimeSettings: RuntimeSettings) {
        this.logger.log("Using local Metis Blackbird MCP server configuration");
        this.logger.log(`Using blackbird mode: ${runtimeSettings.blackbird?.mode}`);
        const agentDir = process.env.AGENT_DIR || "/agent";
        this.logger.log(`Using agent directory: ${agentDir}`);
        let toolRestrictions: string[] = [];
        if (runtimeSettings.blackbird?.mode === "tool") {
            toolRestrictions = ["*"];
        }
        this.logger.log(`Using blackbird Metis server: ${runtimeSettings.blackbird?.mode}`);
        const blackbirdMcpServerConfig: MCPServerConfig = {
            command: `${agentDir}/blackbird/mcp`,
            args: ["serve", "--index", `${agentDir}/blackbird/metis`],
            env: {
                GITHUB_PERSONAL_ACCESS_TOKEN: "GITHUB_PERSONAL_ACCESS_TOKEN",
                COPILOT_API_HMAC_KEY: "CAPI_HMAC_KEY",
                MODEL_BASED_RETRIEVAL_TOKEN: "BLACKBIRD_AUTH_MODEL_BASED_RETRIEVAL_TOKEN",
                METIS_API_KEY: "BLACKBIRD_AUTH_METIS_API_KEY",
            },
            tools: toolRestrictions,
            isDefaultServer: true,
        };
        mcpServersConfig.mcpServers["blackbird-mcp-server"] = blackbirdMcpServerConfig;
    }

    private configureLocalBlackbirdMcp(mcpServersConfig: MCPServersConfig) {
        this.logger.log("Using local Blackbird MCP server configuration");
        this.logger.log(`Using blackbird mode: ${process.env.BLACKBIRD_MODE}`);
        let toolRestrictions: string[] = [];
        if (process.env.BLACKBIRD_MODE === "tool") {
            toolRestrictions = ["*"];
        }
        const blackbirdMcpServerConfig: MCPServerConfig = {
            command: "./copilot-developer-action-main/blackbird-mcp-server/blackbird-mcp-server",
            args: ["stdio"],
            env: {
                GITHUB_PERSONAL_ACCESS_TOKEN: "GITHUB_PERSONAL_ACCESS_TOKEN",
            },
            tools: toolRestrictions,
            isDefaultServer: true,
        };
        mcpServersConfig.mcpServers["blackbird-mcp-server"] = blackbirdMcpServerConfig;
    }

    private configureRemoteBlackbirdMcp(mcpServersConfig: MCPServersConfig) {
        this.logger.log("Using remote Blackbird MCP server configuration");
        this.logger.log(`Using blackbird mode: ${process.env.BLACKBIRD_MODE}`);
        let toolRestrictions: string[] = [];
        if (process.env.BLACKBIRD_MODE === "tool") {
            toolRestrictions = ["*"];
        }
        const headers: Record<string, string> = {
            Authorization: "Bearer " + process.env.GITHUB_PERSONAL_ACCESS_TOKEN,
            "X-MCP-Toolsets": "search_agent",
            "X-MCP-Host": "github-coding-agent",
        };
        if (process.env.GITHUB_COPILOT_INTERACTION_ID) {
            headers["X-Interaction-Id"] = process.env.GITHUB_COPILOT_INTERACTION_ID;
        }
        const blackbirdMcpServerConfig: MCPRemoteServerConfig = {
            type: "http",
            url: "https://api.githubcopilot.com/mcp/readonly",
            headers: headers,
            tools: toolRestrictions,
            isDefaultServer: true,
        };
        mcpServersConfig.mcpServers["blackbird-mcp-server"] = blackbirdMcpServerConfig;
    }

    private isLocalServerConfig(config: MCPServerConfig): config is MCPLocalServerConfig {
        return config.type === undefined || config.type.toLowerCase() === "local";
    }

    private isHttpServerConfig(config: MCPServerConfig): config is MCPRemoteServerConfig {
        return config.type?.toLowerCase() === "http";
    }

    private isSseServerConfig(config: MCPServerConfig): config is MCPRemoteServerConfig {
        return config.type?.toLowerCase() === "sse";
    }

    private isValidServerType(config: MCPServerConfig): boolean {
        return this.isLocalServerConfig(config) || this.isHttpServerConfig(config) || this.isSseServerConfig(config);
    }

    private isValidLocalServerConfig(config: MCPLocalServerConfig): boolean {
        return config.command !== undefined && config.command.trim() !== "" && Array.isArray(config.args);
    }

    private isValidRemoteServerConfig(config: MCPRemoteServerConfig): boolean {
        return config.url !== undefined && config.url.trim() !== "";
    }

    private validateServerConfig(serverName: string, serverConfig: MCPServerConfig): boolean {
        if (this.remoteEnabled && !this.isValidServerType(serverConfig)) {
            this.logger.error(
                `Unsupported server type "${serverConfig.type}" for server "${serverName}". Only "Local" or "Remote" are supported.`,
            );
            return false;
        }

        if (!this.remoteEnabled && !this.isLocalServerConfig(serverConfig)) {
            this.logger.error(
                `Unsupported server type "${serverConfig.type}" for server "${serverName}". Only "Local" is supported.`,
            );
            return false;
        }

        if (!serverConfig.tools) {
            this.logger.error(
                `No tools specified for server "${serverName}". Please provide a list of tools or "*" to include all tools.`,
            );
            return false;
        }

        if (this.isLocalServerConfig(serverConfig) && !this.isValidLocalServerConfig(serverConfig)) {
            this.logger.error(
                `Invalid local server configuration for "${serverName}". Please ensure 'command' and 'args' are provided.`,
            );
            return false;
        }

        if (
            (this.isHttpServerConfig(serverConfig) || this.isSseServerConfig(serverConfig)) &&
            !this.isValidRemoteServerConfig(serverConfig)
        ) {
            this.logger.error(
                `Invalid remote server configuration for "${serverName}". Please ensure 'url' is provided.`,
            );
            return false;
        }
        return true;
    }

    async processHttpServer(serverName: string, serverConfig: MCPRemoteServerConfig): Promise<void> {
        try {
            const resolvedHeaders = this.resolveHeaders(serverConfig.headers, process.env);

            const updatedConfig: MCPRemoteServerConfig = {
                ...serverConfig,
                headers: resolvedHeaders,
            };

            await this.registry.startHttpMcpClient(serverName, updatedConfig);
            this.logger.log(`Started MCP client for remote server ${serverName}`);
        } catch (error) {
            this.logger.error(`Failed to start MCP client for remote server ${serverName}: ${error}`);
        }
    }

    async processSseServer(serverName: string, serverConfig: MCPRemoteServerConfig): Promise<void> {
        try {
            const resolvedHeaders = this.resolveHeaders(serverConfig.headers, process.env);

            const updatedConfig: MCPRemoteServerConfig = {
                ...serverConfig,
                headers: resolvedHeaders,
            };

            await this.registry.startSseMcpClient(serverName, updatedConfig);
            this.logger.log(`Started MCP client for remote server ${serverName}`);
        } catch (error) {
            this.logger.error(`Failed to start MCP client for remote server ${serverName}: ${error}`);
        }
    }

    async processLocalServer(serverName: string, serverConfig: MCPLocalServerConfig): Promise<void> {
        let updatedConfig: MCPLocalServerConfig = { ...serverConfig };
        if (serverConfig.command === "python") {
            try {
                updatedConfig = await this.convertPythonToPipx(updatedConfig);
            } catch (error) {
                this.logger.error(`Failed to handle Python module for ${serverName}: ${error}`);
            }
        }
        const filteredEnv = this.buildEnvironment(updatedConfig);

        const resolvedArgs = this.resolveArray(updatedConfig.args, filteredEnv);

        this.logger.log(
            `Starting MCP client for ${serverName} with command: ${updatedConfig.command} and args: ${resolvedArgs}`,
        );
        try {
            await this.registry.startLocalMcpClient(serverName, filteredEnv, updatedConfig.command, resolvedArgs);
            this.logger.log(`Started MCP client for ${serverName}`);
        } catch (error) {
            this.logger.error(`Failed to start MCP client for ${serverName}: ${error}`);
        }
    }

    private convertPythonToPipx(serverConfig: MCPLocalServerConfig): MCPLocalServerConfig {
        if (!serverConfig || !serverConfig.args || serverConfig.args.length === 0) {
            return serverConfig;
        }

        const args = serverConfig.args;
        const mIndex = args.indexOf("-m");
        if (mIndex === -1 || mIndex === args.length - 1) {
            throw new Error("Python command with -m flag must specify a module");
        }

        const moduleName = args[mIndex + 1];
        this.logger.log(`Converting Python module: ${moduleName} to pipx command`);

        // Create new args array with format: ["run", "module-name", ...otherArgs]
        const newArgs = ["run", moduleName];

        // Add any args that were after the module name in the original command
        for (let i = mIndex + 2; i < args.length; i++) {
            newArgs.push(args[i]);
        }

        // Add any args that were before the -m flag in the original command
        // (excluding the -m flag itself and the module name)
        for (let i = 0; i < mIndex; i++) {
            newArgs.push(args[i]);
        }

        return {
            ...serverConfig,
            command: "pipx",
            args: newArgs,
        };
    }

    private buildEnvironment(serverConfig: MCPLocalServerConfig): Record<string, string> {
        const filteredEnv: Record<string, string> = {};

        if (serverConfig.env) {
            for (const [targetEnvName, sourceEnvName] of Object.entries(serverConfig.env)) {
                const normalizedEnvName = sourceEnvName.trim();
                if (process.env[normalizedEnvName] !== undefined) {
                    filteredEnv[targetEnvName] = process.env[normalizedEnvName]!;
                }
            }
        }

        return filteredEnv;
    }

    private resolveArray(vals: string[] | undefined, env: Record<string, string | undefined>): string[] {
        if (!vals) {
            return [];
        }
        if (!env) {
            return vals;
        }
        return vals.map((val) => val.replace(/\$([A-Z0-9_]+)/g, (match, varName) => env[varName] || match));
    }

    private resolveHeaders(
        headers: Record<string, string> | undefined,
        env: Record<string, string | undefined>,
    ): Record<string, string> {
        if (!headers) {
            return {};
        }
        if (!env) {
            return headers;
        }

        const resolvedHeaders: Record<string, string> = {};

        for (const [key, value] of Object.entries(headers)) {
            const resolvedWords = this.resolveArray([value], env);
            resolvedHeaders[key] = resolvedWords[0];
        }

        return resolvedHeaders;
    }

    async processServers(allServersConfig: MCPServersConfig | undefined): Promise<void> {
        if (!allServersConfig) {
            throw new Error("No servers to process");
        }
        for (const [serverName, serverConfig] of Object.entries(allServersConfig.mcpServers)) {
            if (!this.validateServerConfig(serverName, serverConfig)) {
                this.logger.error(`Skipping server "${serverName}" due to invalid configuration.`);
                continue;
            }
            if (this.isLocalServerConfig(serverConfig)) {
                await this.processLocalServer(serverName, serverConfig);
            } else if (this.isHttpServerConfig(serverConfig) && this.remoteEnabled) {
                await this.processHttpServer(serverName, serverConfig);
            } else if (this.isSseServerConfig(serverConfig) && this.remoteEnabled) {
                await this.processSseServer(serverName, serverConfig);
            }
        }
    }
}
