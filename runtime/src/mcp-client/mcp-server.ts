/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import express from "express";
import { DefaultExpressFactory, ExpressFactory } from "./factories/server-factory";
import { MCPRegistry } from "./mcp-registry";
import { DEFAULT_REQUEST_OPTIONS } from "../tools/mcp-transport";
import { RunnerLogger } from "../runner";

export class MCPServer {
    private server: express.Application | null = null;
    private port = 2301;

    constructor(
        private logger: RunnerLogger,
        private registry: MCPRegistry,
        private expressFactory: ExpressFactory = new DefaultExpressFactory(),
    ) {}

    async startServer(): Promise<void> {
        this.server = this.expressFactory.createExpressApp();
        this.server.use(express.json());

        this.server.get("/health", (_req, res) => {
            res.status(200).send("OK");
        });

        this.server.post("/invoke-tool", async (req, res) => {
            try {
                const { toolId, params } = req.body;
                this.logger.log(`Received tool invocation request: ${JSON.stringify(req.body)}`);
                this.logger.log(`Parsed toolId: ${toolId}`);
                this.logger.log(`Parsed params: ${JSON.stringify(params)}`);

                if (!toolId) {
                    res.status(400).json({
                        error: "Missing toolId in request",
                    });
                    return;
                }

                const [serverName, toolName] = toolId.split("/");
                this.logger.log(`Parsed serverName: ${serverName}`);
                this.logger.log(`Parsed toolName: ${toolName}`);

                if (!serverName || !toolName) {
                    res.status(400).json({
                        error: "Invalid toolId format: " + toolId,
                    });
                    return;
                }

                const client = this.registry.clients[serverName];
                if (!client) {
                    res.status(404).json({
                        error: "Client not found: " + serverName,
                    });
                    return;
                }

                this.logger.log(`Invoking tool ${toolName} with params: ${JSON.stringify(params)}`);

                let result: Awaited<ReturnType<typeof client.callTool>>;

                // If the MCP tool fails and returns an error, the `await` to callTool will throw. We want
                // to catch that error and return it as a "success" response to the client, but with a message
                // indicating that the tool call failed, so that the LLM can see the error and respond accordingly.
                try {
                    result = await client.callTool({
                        name: toolName,
                        arguments: params,
                        requestOptions: DEFAULT_REQUEST_OPTIONS,
                    });
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                } catch (error: any) {
                    this.logger.error(`MCP tool invocation returned an error: ${error}`);
                    const errorMessage = error.message || error.toString();

                    res.json({
                        isToolError: true,
                        content: [
                            {
                                type: "text",
                                text: `Tool call failed: ${errorMessage}`,
                            },
                        ],
                    });
                    return;
                }

                this.logger.log(`Tool invocation result: ${JSON.stringify(result)}`);

                if (!Array.isArray(result.content)) {
                    throw new Error("Expected an array of content");
                }

                const content = result.content.filter((c) => ["text", "image", "resource"].includes(c.type));

                res.json({
                    isToolError: false,
                    content: content,
                });
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
            } catch (error: any) {
                this.logger.error(`Error handling tool invocation: ${error}`);
                res.status(500).json({
                    error: `Tool execution failed: ${error.message}`,
                });
            }
        });

        return new Promise<void>((resolve) => {
            this.server!.listen(this.port, () => {
                this.logger.log(`MCP Tool server listening on http://localhost:${this.port}`);
                resolve();
            });
        });
    }
}
