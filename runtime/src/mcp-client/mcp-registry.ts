/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { SSEClientTransport } from "@modelcontextprotocol/sdk/client/sse.js";
import { StdioClientTransport } from "@modelcontextprotocol/sdk/client/stdio.js";
import { StreamableHTTPClientTransport } from "@modelcontextprotocol/sdk/client/streamableHttp.js";
import { ContentFilterMode, getContentFilterMode } from "../helpers/content-filter";
import { ClientFactory, DefaultClientFactory } from "./factories/client-factory";
import { DefaultTransportFactory, TransportFactory, TransportOptions } from "./factories/transport-factory";
import { isKnownMCPTool } from "./known-mcp-servers";
import { MCPRemoteServerConfig, MCPServersConfig } from "./types";
import { RunnerLogger } from "../runner";

export type Tool = {
    name: string;
    description: string;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    input_schema: any;
    readOnly?: boolean;
    safeForTelemetry: {
        name: boolean;
        inputsNames: boolean;
    };
    filterMode?: ContentFilterMode;
};

export class MCPRegistry {
    clients: Record<string, Client> = {};

    constructor(
        private logger: RunnerLogger,
        private clientFactory: ClientFactory = new DefaultClientFactory(),
        private transportFactory: TransportFactory = new DefaultTransportFactory(),
    ) {}

    async startLocalMcpClient(serverName: string, env: Record<string, string>, command: string, args: string[]) {
        this.logger.log(`Starting MCP client for ${serverName} with command: ${command} and args: ${args}`);
        const transportOptions: TransportOptions = {
            type: "stdio",
            command: command,
            args: args,
            env: {
                ...env,
                PATH: process.env.PATH!,
            },
        };
        const transport = this.transportFactory.createTransport(transportOptions);
        await this.setupAndConnectClient(serverName, transport);
    }

    async startHttpMcpClient(serverName: string, serverConfig: MCPRemoteServerConfig) {
        this.logger.log(`Starting remote MCP client for ${serverName} with url: ${serverConfig.url}`);
        const transportOptions: TransportOptions = {
            type: "http",
            url: serverConfig.url,
            headers: serverConfig.headers,
        };
        const transport = this.transportFactory.createTransport(transportOptions);
        await this.setupAndConnectClient(serverName, transport);
    }

    async startSseMcpClient(serverName: string, serverConfig: MCPRemoteServerConfig) {
        this.logger.log(`Starting remote MCP client for ${serverName} with url: ${serverConfig.url}`);
        const transportOptions: TransportOptions = {
            type: "sse",
            url: serverConfig.url,
            headers: serverConfig.headers,
        };
        const transport = this.transportFactory.createTransport(transportOptions);
        await this.setupAndConnectClient(serverName, transport);
    }

    private async setupAndConnectClient(
        serverName: string,
        transport: StdioClientTransport | StreamableHTTPClientTransport | SSEClientTransport,
    ) {
        this.logger.log(`Creating MCP client for ${serverName}...`);
        const client = this.clientFactory.createClient(
            {
                name: "github-copilot-developer",
                version: "1.0.0",
            },
            {
                capabilities: {
                    experimental: undefined,
                    roots: undefined,
                    sampling: undefined,
                },
            },
        );
        const startTime = Date.now();
        this.logger.log(`Connecting MCP client for ${serverName}...`);
        await client.connect(transport);
        client.onclose = () => {
            this.logger.log(`MCP client for ${serverName} closed`);
            delete this.clients[serverName];
        };
        client.onerror = (error) => {
            this.logger.error(`MCP client for ${serverName} errored ${error}`);
        };

        this.logger.log(`MCP client for ${serverName} connected, took ${Date.now() - startTime}ms`);
        this.clients[serverName] = client;
    }

    async getTools(mcpServersConfig: MCPServersConfig | undefined): Promise<Record<string, Tool>> {
        const tools: Record<string, Tool> = {};
        for (const [clientName, client] of Object.entries(this.clients)) {
            try {
                this.logger.log(`Fetching tools from client: ${clientName}`);
                const resp = await client.listTools();
                const toolsArray = mcpServersConfig?.mcpServers[clientName].tools;
                const filterMapping = mcpServersConfig?.mcpServers[clientName].filterMapping;

                for (const tool of resp.tools) {
                    const isToolAllowed = toolsArray?.includes("*") || toolsArray?.includes(tool.name);
                    if (!isToolAllowed) {
                        this.logger.log(`Tool ${tool.name} is not in the allowed list for client: ${clientName}`);
                        continue;
                    }
                    const toolIsKnown = isKnownMCPTool(clientName, tool.name);
                    // Prefix tool name with client name to avoid conflicts if two MCP servers have a tool with the same name.
                    // We don't use / as the separator because it is not allowed in tool names by some models.
                    const toolNameWithClientPrefix = `${clientName}-${tool.name}`;

                    let toolFilterMode: ContentFilterMode;
                    if (typeof filterMapping === "string") {
                        // If filterMapping is a string, use it directly
                        toolFilterMode = getContentFilterMode(filterMapping);
                    } else {
                        // If it's an object or undefined, use the object lookup with fallback
                        toolFilterMode = getContentFilterMode(filterMapping?.[tool.name] ?? "hidden_characters");
                    }

                    tools[`${clientName}/${tool.name}`] = {
                        name: toolNameWithClientPrefix,
                        description: tool.description ?? toolNameWithClientPrefix,
                        input_schema: tool.inputSchema,
                        readOnly: tool.annotations?.readOnlyHint,
                        safeForTelemetry: {
                            name:
                                mcpServersConfig?.mcpServers[clientName].isDefaultServer || toolIsKnown ? true : false,
                            // Since knowing if a tool is safe for telemetry only inspects the tool name, do not mark inputs names as safe for telemetry.
                            inputsNames: mcpServersConfig?.mcpServers[clientName].isDefaultServer ? true : false,
                        },
                        filterMode: toolFilterMode,
                    };
                    this.logger.log(`Tool ${tool.name} added to tools list for client: ${clientName}`);
                }
                this.logger.log(`Successfully retrieved ${resp.tools.length} tools from client: ${clientName}`);
            } catch (error) {
                this.logger.error(`Failed to get tools from client: ${clientName} ${error}`);
            }
        }
        this.logger.log(`All tools retrieved: ${JSON.stringify(tools, null, 2)}`);

        return tools;
    }
}
