# MCP client

This directory houses the MCP related code for Padawan

## Dev Setup

In order to set this up in dev, follow these steps:

1. Follow the [service-dev-with-github.md](./../docs/service-dev-setup-with-github.md)
2. After you have a functioning environment enable the following FFs
    - `./bin/toggle-feature-flag enable actions_launch_configuration_variables` (this is shipped in production but not dev)
    - `./bin/toggle-feature-flag enable sweagentd_mcp_3p_enabled`
    - `./bin/toggle-feature-flag enable copilot_swe_agent_repo_settings`
3. Add the following line to your `sweagent/.env` file: `COPILOT_MCP_ENABLED="true"`
4. On your dev instance's UI, go to the repo settings for wherever you plan to use Padawan.
5. Under your repository's settings go to `Copilot > Coding Agent`
6. If you enabled `sweagentd_mcp_3p_enabled`, you can use a JSON here to configure 3P servers or overwrite the built in GitHub one!

## Dogfooding in production

In order to dogfood this in production, follow these steps:

1. Make sure you are flagged into all required Padawan dogfood FFs!
2. Make sure Padawan is installed on your repo.
3. In your test repo, go to your repository settings. Go to `Copilot > Coding Agent`
4. You can use a JSON to configure 3P servers! An example config is provided below:
   **Note: you still need to enable the 3P FF to reconfigure the GitHub MCP since that's done through `COPILOT_MCP_JSON`**

```
{
    "mcpServers":{
        "playwright":{
            "command":"npx", // We support `npx` & `docker` currently
            "args":["-y","@executeautomation/playwright-mcp-server"],
            "type": "local", // Optional. We only support "local"
            "tools":["playwright_navigate"] // Optional. We allow enabling a subset of tools. Excluding this argument provides all tools. [] means none.
        },
        "figma":{
            "command":"npx",
            "args":["-y","figma-developer-mcp","--figma-api-key=$FIGMA_KEY","--stdio"], // $FIGMA_KEY will be read in from `env`
            "env":{
                "FIGMA_KEY": "COPILOT_MCP_FIGMA_KEY" // Optional. Recall that the `COPILOT_MCP` prefix is required to get this in the correct actions step's environment
            }
        },
        "time":{
            "command": "docker",
            "args": ["run", "-i", "--rm", "mcp/time"]
        }
    }
}
```

The config format is provided below:

```
MCPServersConfig {
    mcpServers: Record<string, MCPServerConfig>; // Hash of all the MCP server names to their configs
}
```

```
MCPServerConfig {
    command: string;
    args: string[];
    tools: string[]; // List of tools to include from this server. ["*"] means all. [] means none.
    type?: string; // Optional. We only support "Local"
    env?: Record<string, string>; // An object of the environment variables to pass to the server. Key is whats sent to the MCP Server. Value is whats read from the actions environment. Empty means no env vars passed.
}
```
