/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/
import * as fs from "fs";
import { join as pathJoin } from "path";
import { MCPRegistry } from "./mcp-registry";
import { MCPServersConfig } from "./types";
import { RunnerLogger } from "../runner";

export class ToolConfigWriter {
    constructor(
        private logger: RunnerLogger,
        private registry: MCPRegistry,
    ) {}

    async writeToolConfig(mcpServersConfig: MCPServersConfig | undefined): Promise<void> {
        const toolsConfig = await this.registry.getTools(mcpServersConfig);
        const configFilePath = pathJoin(process.env.COPILOT_AGENT_MCP_SERVER_TEMP ?? ".", "mcp-config.json");
        try {
            await fs.promises.writeFile(configFilePath, JSON.stringify(toolsConfig, null, 2));
            this.logger.log(`Tool configuration written to ${configFilePath}`);
        } catch (error) {
            this.logger.error(`Failed to write tool configuration: ${error}`);
        }
    }
}
