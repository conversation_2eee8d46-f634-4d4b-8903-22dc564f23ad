/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { ChatCompletionMessageToolCall } from "openai/resources";
import { SecretFilter } from "../helpers/SecretFilter";
import { surroundMultiLineWithLFs } from "../helpers/string-helpers";

export class TrajectoryFormatter {
    private secretFilter: SecretFilter = SecretFilter.getInstance();

    /**
     * Format a tool call and its parameters into XML format
     */
    formatToolCall(toolCall: ChatCompletionMessageToolCall): string {
        let xml = "<function_calls>\n";
        xml += '<invoke name="' + this.escapeXml(toolCall.function.name) + '">\n';

        let args: { [key: string]: unknown };
        try {
            // Parse arguments as JSON to handle both string and object args
            args = JSON.parse(toolCall.function.arguments);
        } catch {
            args = { functionArguments: toolCall.function.arguments };
        }

        // Add each parameter
        for (const [key, value] of Object.entries(args)) {
            const formatted = surroundMultiLineWithLFs(
                this.escapeXml(SecretFilter.getInstance().filterSecrets(String(value))),
            );
            xml += `<parameter name="${this.escapeXml(key)}">${formatted}</parameter>\n`;
        }

        xml += "</invoke>\n";
        xml += "</function_calls>";
        return xml;
    }

    /**
     * Format a tool result into XML format
     */
    formatToolResult(result: string): string {
        let xml = "<function_results>\n";
        if (result && result.trim()) {
            xml += this.escapeXml(this.secretFilter.filterSecrets(result.trim())) + "\n";
        } else {
            xml += "<system>Tool ran without output or errors</system>\n";
        }
        xml += "</function_results>";
        return xml;
    }

    /**
     * Format an AI message into XML format
     */
    formatMessage(_role: string, content: string): string {
        if (!content || !content.trim()) {
            return "";
        }
        return `\n${this.escapeXml(this.secretFilter.filterSecrets(content.trim()))}\n`;
    }

    /**
     * Escapes special characters for XML
     */
    private escapeXml(unsafe: string): string {
        return unsafe;
        // return unsafe
        //   .replace(/&/g, '&amp;')
        //   .replace(/</g, '&lt;')
        //   .replace(/>/g, '&gt;')
        //   .replace(/"/g, '&quot;')
        //   .replace(/'/g, '&apos;');
    }
}
