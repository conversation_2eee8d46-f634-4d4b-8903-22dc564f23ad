/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

// This file is the entrypoint for the runtime when launched by the `sweagentd` service.

import { Client as McpClient } from "@modelcontextprotocol/sdk/client/index.js";
import { getDefaultEnvironment, StdioClientTransport } from "@modelcontextprotocol/sdk/client/stdio.js";
import { ExecException } from "child_process";
import { randomBytes } from "crypto";
import { tmpdir } from "os";
import { join as pathJoin } from "path";
import { getAgent, SweAgentKind } from "./agents/sweagent";
import { CompoundCallback, IAgentCallback } from "./callbacks/callback";
import { LoggerCallback } from "./callbacks/LoggerCallback";
import { SessionsCallback } from "./callbacks/SessionsCallback";
import { SweagentdCallback } from "./callbacks/SweagentdCallback";
import { TrajectoryFileCallback } from "./callbacks/TrajectoryFileCallback";
import { ErrorHandler } from "./errors";
import { forceExit } from "./helpers/force-exit";
import { SecretFilter } from "./helpers/SecretFilter";
import { createCopilotSessionsClient } from "./model/capi/sessions-client";
import { splitAgentModelSetting } from "./model/util";
import { emitPostCloneTelemetry } from "./postCloneTelemetry";
import { getRunner, RunnerLogger } from "./runner";
import { RunnerExec } from "./runner/exec";
import { GitError } from "./runner/git";
import { DefaultGitHandler } from "./runner/git/default";
import { RuntimeSettings } from "./settings";
import {
    demandAction,
    demandCallbackUrl,
    demandGitHubPRNumber,
    demandGitHubRepoCommit,
    demandGitHubRepoId,
    demandGitHubRepoName,
    demandGitHubRepoOwnerId,
    demandInstanceId,
    demandProblemStatement,
} from "./settings/demand";
import { getOrInitSettings } from "./settings/factory";
import { isFeatureFlagEnabled } from "./settings/types";
import { ClientInfo, OutOfProcMCPTransport } from "./tools/mcp-transport";

export interface SWEBenchVerifiedProblem {
    repo: string;
    base_commit: string;
    issue_number: number;
    problem_statement: string;
    job_id: string;
    branchName: string;
    repoLocation: string; // location where the repo either exists or should be cloned
    callback: IAgentCallback;
    push?: boolean;
    agent?: SweAgentKind;
    model?: string;
    prNumber?: number; // only for fix when PR already exists
    commitCount?: number; // only for fix when PR already exists
}

async function solveProblem(
    settings: RuntimeSettings,
    logger: RunnerLogger,
    exec: RunnerExec,
    problem: SWEBenchVerifiedProblem,
): Promise<void> {
    const git = new DefaultGitHandler(logger, exec);
    logger.info(`Solving problem: ${problem.job_id} from ${problem.repo}@${problem.base_commit}`);
    logger.info(`Problem statement:\n`);
    logger.info(problem.problem_statement);
    logger.info("\n");

    // Get branch name and expected location to clone or generate new ones
    const branchName = problem.branchName ?? `${problem.job_id}-${randomBytes(16).toString("hex")}`;

    // TODO: Remove this once we are confident about error handling in prod
    if (settings.featureFlags?.["copilot_swe_agent_simulate_runtime_git_clone_error"]) {
        const simulatedError = new Error(
            "Command failed with exit code 128: git clone <value> <value>",
        ) as ExecException;
        simulatedError.cmd = "git clone <value> <value>";
        simulatedError.stdout = "";
        simulatedError.stderr =
            "Cloning into '/some/dir'...\nremote: Not Found\nfatal: repository 'https://github.com/repo-that-should-be-filtered/' not found";
        simulatedError.code = 128;
        throw new GitError(simulatedError);
    }
    // Clone the Git repo to a temp folder
    await git.cloneRepo(
        settings,
        problem.repo,
        problem.repoLocation,
        branchName,
        problem.commitCount,
        problem.base_commit,
    );
    await emitPostCloneTelemetry(problem, settings);

    const mcpTransport =
        process.env.COPILOT_MCP_ENABLED === "true" ? new OutOfProcMCPTransport(settings, logger) : undefined;
    const { transports, clients } = await startMcpServers(settings, logger, mcpTransport);

    try {
        // Allow configuring the agent to use for producing the PR via the agent parameter
        const agent = getAgent(
            settings,
            logger,
            exec,
            problem.agent,
            { model: problem.model },
            clients,
            problem.callback,
        );

        // The createPRAgent is expected to do two things:
        // 1. Make local edits to the project folder that are expected to be ready to push
        // 2. Produce a PR title and description
        const prDetails = await agent.createPRAgent(
            problem.repoLocation,
            problem.problem_statement,
            problem.repo,
            problem.issue_number,
            branchName,
            { push: problem.push },
        );
        const prTitle =
            prDetails.title ||
            (problem.issue_number > 0 ? `Resolving issue #${problem.issue_number}` : "Solving problem");

        if (problem.push) {
            // TODO: Remove this once we are confident about error handling in prod
            if (settings.featureFlags?.["copilot_swe_agent_simulate_runtime_git_push_error"]) {
                const simulatedError = new Error(
                    "Command failed with exit code 1: git push <value> <value>",
                ) as ExecException;
                simulatedError.cmd = "git push <value> <value>";
                simulatedError.stdout = "";
                simulatedError.stderr =
                    "Git hook failed with exit code 1\nThis is where you would see details about the hook failure\nmore words go here";
                simulatedError.code = 1;
                throw new GitError(simulatedError);
            }
            await git.commitAndPushChanges(settings, branchName, problem.repoLocation, prTitle, false);
        }

        const diff = await git.diff(problem.repoLocation);

        logger.debug("Final git diff:");
        await problem.callback.result({
            diff: diff,
            branchName,
            prTitle: prDetails.title,
            prDescription: prDetails.description,
        });
    } finally {
        for (const [name, transport] of Object.entries(transports)) {
            try {
                await transport.close();
            } catch (err) {
                logger.warning(`Failed to close MCP transport ${name}: ${err}`);
            }
        }
    }
}

async function respondToComment(
    settings: RuntimeSettings,
    logger: RunnerLogger,
    exec: RunnerExec,
    problem: SWEBenchVerifiedProblem,
): Promise<void> {
    const git = new DefaultGitHandler(logger, exec);

    // General strategy is:
    // * Checkout the branch for the PR
    // * Run the agent to generate a fix
    // * Push the changes to the branch
    // * Create a reply to the PR comment
    // Note that it is possible that the agent decides it can reply to the comment without pushing any changes.  That is okay.
    logger.info(`Responding to feedback: ${problem.job_id} from ${problem.repo}@${problem.base_commit}`);
    logger.info(`Problem statement:\n${problem.problem_statement}`);

    // Clone the Git repo into a specified location or a temp folder
    await git.cloneRepo(
        settings,
        problem.repo,
        problem.repoLocation,
        problem.branchName ?? problem.base_commit,
        problem.commitCount,
    );
    await emitPostCloneTelemetry(problem, settings);

    const mcpTransport =
        process.env.COPILOT_MCP_ENABLED === "true" ? new OutOfProcMCPTransport(settings, logger) : undefined;
    const { transports, clients } = await startMcpServers(settings, logger, mcpTransport);

    try {
        const agent = getAgent(
            settings,
            logger,
            exec,
            problem.agent,
            { model: problem.model },
            clients,
            problem.callback,
        );
        const commentResponse = await agent.respondToPRCommentAgent(
            problem.repoLocation,
            problem.problem_statement,
            problem.repo,
            problem.issue_number,
            problem.base_commit,
            { push: problem.push },
        );

        if (problem.push && commentResponse.diff) {
            // If the agent has made changes and push these with the PR title or a default title.
            // generally we should not need to get into this scenario as the changes should be pushed by the report_progress tool, but this is
            // in case any changes are missed after the last report_progress call.
            // TODO: Do we have telemetry for this?
            await git.commitAndPushChanges(
                settings,
                problem.branchName ?? problem.base_commit,
                problem.repoLocation,
                commentResponse.title ?? "Addressing PR comments",
                false,
            );
        }

        logger.debug("Final git diff:");
        const diff = await git.diff(problem.repoLocation);

        await problem.callback.result({
            branchName: problem.branchName ?? problem.base_commit,
            prTitle: commentResponse.title,
            prDescription: commentResponse.description,
            diff,
        });
    } finally {
        for (const [name, transport] of Object.entries(transports)) {
            try {
                await transport.close();
            } catch (err) {
                logger.warning(`Failed to close MCP transport ${name}: ${err}`);
            }
        }
    }
}

export async function main() {
    // Init runner and logger
    const runner = getRunner();

    const secretFilter = SecretFilter.getInstance();
    secretFilter.setRunner(runner);

    await runner.prepareEnvironment();

    const settings = await getOrInitSettings();

    const logger = runner.logger;
    const exec = runner.exec;
    logger.debug(`Runner: ${runner.type}`);

    const repo = demandGitHubRepoName(settings);
    const repo_id = demandGitHubRepoId(settings);
    const repo_owner_id = demandGitHubRepoOwnerId(settings);
    const base_commit = demandGitHubRepoCommit(settings);
    const issue_number = settings?.github?.issue?.number ?? 0; // optional
    const problem_statement = demandProblemStatement(settings);
    const job_id = demandInstanceId(settings);
    const action = demandAction(settings);
    const push = settings?.github?.repo?.readWrite ?? false; // optional
    const agentModel = settings?.service?.agent?.model; // optional
    const prNumber = demandGitHubPRNumber(settings);
    const callbackUrl = demandCallbackUrl(settings);
    const trajectoryOutputFile = settings?.trajectory?.outputFile;
    const commitCount = settings?.github?.pr?.commitCount; // optional

    // Get branch name from settings if it exists , otherwise generate new one
    const branchName = settings?.github?.repo?.branch ?? `${job_id}-${randomBytes(16).toString("hex")}`;
    // Repo location either comes from Actions itself or we'll generate a new one in a temp dir
    const repoLocation = process.env.GITHUB_WORKSPACE ?? pathJoin(tmpdir(), `repo-${job_id}`);

    const parts = repo.split("/");
    if (parts.length !== 2) {
        throw new Error(`Invalid repo: ${repo}`);
    }
    const [repo_owner, repo_name] = parts;

    // split agent and model if provided
    const { agent, model } = splitAgentModelSetting(agentModel);

    const sessionsClient = await createCopilotSessionsClient(settings, logger);
    const sessionID = sessionsClient.sessionId();

    const callback = new CompoundCallback()
        .addCallback(
            new SweagentdCallback({
                callbackUrl,
                runner,
                repo,
                repo_id,
                repo_name,
                repo_owner,
                repo_owner_id,
                job_id,
                action,
                settings,
            }),
        )
        .addCallback(new LoggerCallback((s: string) => runner.logger.info(s)))
        .addCallback(trajectoryOutputFile ? new TrajectoryFileCallback(trajectoryOutputFile) : undefined)
        .addCallback(new SessionsCallback(sessionsClient, runner.logger));

    // Create a problem object that contains all the necessary information for the agent to solve the problem
    const problem = {
        repo,
        base_commit,
        issue_number,
        problem_statement: runner.decoder.decode(problem_statement),
        job_id,
        push,
        agent: agent as SweAgentKind,
        model,
        callback,
        prNumber,
        branchName,
        repoLocation,
        commitCount,
    };

    // Initialize the error handler to catch unhandled exceptions and rejections while solving the problem
    const errorHandler = new ErrorHandler(settings, logger, problem, runner.git, sessionsClient);
    process.on(
        "uncaughtException",
        // eslint-disable-next-line @typescript-eslint/no-misused-promises
        errorHandler.handleError.bind(errorHandler),
    );
    process.on(
        "unhandledRejection",
        // eslint-disable-next-line @typescript-eslint/no-misused-promises
        errorHandler.handleError.bind(errorHandler),
    );

    try {
        await callback?.progress({
            kind: "log",
            message: `Agent job started: ${job_id}. Agent requested: ${agent}. Session: ${sessionID}`,
        });

        switch (action) {
            case "fix": {
                await solveProblem(settings, logger, exec, problem);
                break;
            }
            case "fix-pr-comment": {
                await respondToComment(settings, logger, exec, problem);
                break;
            }
            default:
                logger.warning(`Unknown action: ${action}`);
        }
    } catch (err) {
        await errorHandler.handleError(err);
    }
}

async function startMcpServers(
    settings: RuntimeSettings,
    logger: RunnerLogger,
    outOfProcTransport?: OutOfProcMCPTransport,
): Promise<{
    transports: Record<string, StdioClientTransport>;
    clients: ClientInfo[];
}> {
    const transports: Record<string, StdioClientTransport> = {};
    const clients: ClientInfo[] = [];

    if (
        process.env.COPILOT_MCP_ENABLED === "true" &&
        isFeatureFlagEnabled(settings, "copilot_swe_agent_playwright_use_firewall")
    ) {
        // Check if playwright is already provided via the MCP transport before adding it here
        const mcpConfigPath = pathJoin(process.env.COPILOT_AGENT_MCP_SERVER_TEMP ?? ".", "mcp-config.json");
        const playwrightAlreadyRegistered =
            outOfProcTransport?.hasRegisteredServer(mcpConfigPath, "playwright") ?? false;

        if (playwrightAlreadyRegistered) {
            logger.debug(
                "Playwright is already provided via MCP transport, skipping standalone Playwright MCP server setup",
            );
        } else {
            // When launching the MCP server, the StdioClientTransport does not flow all the environment variables from the current process to the MCP
            // server. We need to augment the default environment with a few additional variables which are set by the `launch.sh` script that configure
            // the use of the self-signed certificate generated by the firewall process.
            const mcpEnv: Record<string, string> = getDefaultEnvironment();
            if (process.env.NODE_EXTRA_CA_CERTS) {
                mcpEnv["NODE_EXTRA_CA_CERTS"] = process.env.NODE_EXTRA_CA_CERTS;
            }
            if (process.env.SSL_CERT_DIR) {
                mcpEnv["SSL_CERT_DIR"] = process.env.SSL_CERT_DIR;
            }
            if (process.env.SSL_CERT_FILE) {
                mcpEnv["SSL_CERT_FILE"] = process.env.SSL_CERT_FILE;
            }

            logger.debug(`Starting Playwright MCP server with environment: ${JSON.stringify(mcpEnv, null, 2)}`);

            const transport = new StdioClientTransport({
                command: "npx",
                args: ["--yes", "@playwright/mcp@latest", "--viewport-size", "1280, 720"],
                env: mcpEnv,
            });
            const client = new McpClient(
                {
                    name: "github-copilot-developer",
                    version: "1.0.0",
                },
                {
                    capabilities: {
                        experimental: undefined,
                        roots: undefined,
                        sampling: undefined,
                    },
                },
            );

            try {
                const startTime = Date.now();
                logger.debug("Connecting to Playwright MCP server...");
                await client.connect(transport, {
                    timeout: 5 * 60 * 1000, // 5 minutes timeout
                });
                logger.debug(`Connected to Playwright MCP server. Took ${Date.now() - startTime}ms`);
                transports["playwright"] = transport;
                clients.push({
                    clientName: "playwright",
                    mcpClient: client,
                    safeForTelemetry: true,
                });
            } catch (err) {
                logger.warning(
                    `Failed to connect to Playwright MCP server: ${err}. Playwright tools will not be available.`,
                );
            }
        }
    }

    return { transports, clients };
}

// eslint-disable-next-line @typescript-eslint/no-floating-promises
main()
    .catch((err) => {
        console.error(`Service error: ${err}`);
        console.error(`Stack trace: ${err.stack}`);
        process.exit(1);
    })
    .then(forceExit);
