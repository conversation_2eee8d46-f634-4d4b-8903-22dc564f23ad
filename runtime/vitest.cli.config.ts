/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { loadEnv } from "vite";
import { defineConfig } from "vitest/config";

export default defineConfig(({ mode }) => {
    return {
        test: {
            env: loadEnv(mode, process.cwd(), ""),
            globals: true,
            environment: "node",
            coverage: {
                provider: "v8",
                reporter: ["text", "json", "html"],
                reportOnFailure: true,
            },
            // CLI tests only - run sequentially due to shared state
            include: ["test/cli/**/*.test.ts", "test/cli/**/*.test.tsx"],
            pool: "forks",
            poolOptions: {
                forks: {
                    singleFork: true,
                },
            },
            maxConcurrency: 1,
            testTimeout: 60000,
        },
    };
});
