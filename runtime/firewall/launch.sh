#!/bin/bash
set -e

launcher_dir="$(realpath "$(dirname "${BASH_SOURCE[0]}")")"
launcher_version="development"
if [ -e "${launcher_dir}/version" ]; then
  launcher_version="$(cat "${launcher_dir}/version")"
fi
echo "Launcher version: ${launcher_version}"

if [ -z "$1" ]; then
  echo "No command to run. Exiting."
  exit 1
fi

command_to_execute="$1"
ebpf_temp="${2:-"${TMPDIR:-/tmp}/ebpf-$(date +%Y%m%d-%H%M%S)"}"
log_to_stdout="${3:-true}"
log_to_stdout_device="/dev/stdout"
if [ "${log_to_stdout}" != "true" ]; then
  log_to_stdout_device="/dev/null"
fi

fw_enabled="${COPILOT_AGENT_FIREWALL_ENABLED:-false}"
unset COPILOT_AGENT_FIREWALL_ENABLED

domains="${COPILOT_AGENT_FIREWALL_ALLOW_LIST:-"https://github.com,https://api.githubcopilot.com"}"
# Trim trailing commas from the domains list before we append `hostname` so we don't get `bing.com,,hostname`
domains="${domains%,}"
domains="${domains},$(hostname)"
# Remove all whitespace from domains
domains="$(echo "${domains}" | tr -d '[:space:]')"
unset COPILOT_AGENT_FIREWALL_ALLOW_LIST
unset COPILOT_AGENT_FIREWALL_ALLOW_LIST_ADDITIONS

if [ "${fw_enabled}" != "true" ]; then
    echo "Firewall disabled."
    eval exec $command_to_execute
    exit $?
else
  echo -e "==============================\nAllow list\n----\n${domains}\n----" | tr ',' '\n'
  # Print the domains allow list
  echo -e "==============================\nAllow list\n----\n${domains}\n----" | tr ',' '\n'

  # Print the ruleset allow list if it exists and decode it
  if [ "${COPILOT_AGENT_FIREWALL_ENABLE_RULESET_ALLOW_LIST}" = "true" ]; then
    echo -e "==============================\nUsing Firewall Recommended Rules\n----"
    echo "${COPILOT_AGENT_FIREWALL_RULESET_ALLOW_LIST}" | base64 -d | gunzip
    echo -e "\n----"
  else
    echo "Firewall Recommended Rules are not enabled."
  fi
fi

if [ "$(uname -s)" != "Linux" ] || [ "$(uname -m)" != "x86_64" ]; then
  echo "This script is only supported on x86_64 Linux. Exiting."
  exit 1
fi

# Firewall binaries may be under dist or in dist/in-path during testing
firewall_bin_dir="${launcher_dir}/in-path"
if [ ! -e "${firewall_bin_dir}/padawan-fw" ]; then
  firewall_bin_dir="${firewall_bin_dir}/dist/in-path"
  if [ ! -e "${firewall_bin_dir}/padawan-fw" ]; then
    echo "Firewall binary not found. Exiting."
    exit 1
  fi
fi

# Set up directories
fw_log="${ebpf_temp}/fw.jsonl"
blocked_log="${ebpf_temp}/blocked.jsonl"
blocked_md="${ebpf_temp}/blocked.md"
mkcert_log="${ebpf_temp}/mkcert.log"
output_log="${ebpf_temp}/output.log"
params_log="${ebpf_temp}/params.log"
ca_root="${ebpf_temp}/mkcert"
mkdir -p "${ebpf_temp}" "${ca_root}"
current_user="$(id -un)"
current_group="$(id -gn)"

# Log parameters
cat << EOF > "${params_log}"
Launcher version: ${launcher_version}
Firewall allow list: ${domains}
Command: ${command_to_execute}
----------------------------
Bin path: ${firewall_bin_dir}
Log path: ${ebpf_temp}
CA root: ${ca_root}
Current user: ${current_user}
Current group: ${current_group}
Current process ID: $$
Current cgroup: $(cat /proc/self/cgroup)
----------------------------
EOF

# Capture current variables that should be restored, but omit anything cgroup related
declare -p | grep -E '(^declare -x | ^declare -.x).+' > "${ebpf_temp}/user.env"
# Full command with user exports - use EOF to expand variables
cat << EOF1 > "${ebpf_temp}/command.sh"
#!/bin/bash
set -e
. "${ebpf_temp}/user.env"
rm -f "${ebpf_temp}/user.env"

declare -xr COPILOT_AGENT_FIREWALL_LOG_FILE="${fw_log}"

# Explicitly declare common variables used by languages and tools to determine the CA root location
# Locations from https://go.dev/src/crypto/x509/root_linux.go
if [ -e "/etc/ssl/certs/ca-certificates.crt" ]; then
  # Ubuntu/Debian
  declare -x SSL_CERT_DIR="/etc/ssl/certs"
  declare -x SSL_CERT_FILE="\${SSL_CERT_DIR}/ca-certificates.crt"
elif [ -e "/etc/pki/tls/certs/ca-bundle.crt" ]; then
  # Fedora/RHEL 6
  declare -x SSL_CERT_DIR="etc/pki/tls/certs"
  declare -x SSL_CERT_FILE="\${SSL_CERT_DIR}/ca-bundle.crt"
elif [ -e "/etc/pki/ca-trust/extracted/pem/tls-ca-bundle.pem" ]; then
  # Fedora/RHEL 7
  declare -x SSL_CERT_DIR="/etc/pki/ca-trust"
  declare -x SSL_CERT_FILE="\${SSL_CERT_DIR}/extracted/pem/tls-ca-bundle.pem"
elif [ -e "/etc/ssl/ca-bundle.pem" ]; then
  # OpenSuSE
  declare -x SSL_CERT_DIR="/etc/ssl"
  declare -x SSL_CERT_FILE="\${SSL_CERT_DIR}/ca-bundle.pem"
elif [ -e "/etc/ssl/cert.pem" ]; then
  # OpenSuSE
  declare -x SSL_CERT_DIR="/etc/ssl"
  declare -x SSL_CERT_FILE="\${SSL_CERT_DIR}/cert.pem"
else
  echo "No cert file found. Exiting."
  exit 1
fi
declare -x CURL_CA_BUNDLE="\${SSL_CERT_FILE}"

# Explicitly declare CA bundle for Python's requests library
declare -x REQUESTS_CA_BUNDLE="\${SSL_CERT_FILE}"

# Add the firewall cert to NODE_EXTRA_CA_CERTS
declare -x NODE_EXTRA_CA_CERTS="${ca_root}/rootCA.pem"

# Disable IPv6 for .NET since its not supported by the firewall
declare -x DOTNET_SYSTEM_NET_DISABLEIPV6=1
if [ "${COPILOT_AGENT_DEBUG}" = "true" ]; then
  echo "=============================="
  echo "Command process ID: \$\$"
  echo "Command cgroup: \$(cat /proc/self/cgroup)"
  echo ""
  echo "Restored environment: \$(env)"
  echo "=============================="
fi
${command_to_execute}
EOF1
chmod +x "${ebpf_temp}/command.sh"

# Command wrapped for calling it via padawan-firewall - don't expand PATH variable
debug_arg=""
if [ "${COPILOT_AGENT_DEBUG}" = "true" ]; then
  debug_arg="--debug"
  echo "Debugging is enabled."
  if [ "${log_to_stdout}" = "true" ]; then
    cat "${params_log}"
  fi
fi
cat << EOF2 > "${ebpf_temp}/wrapper.sh"
#!/bin/bash
# This script is generated by the firewall launch script.
if [ "\$(id -u)" -ne 0 ]; then
  echo "Not running as root. Exiting."
  exit 1
fi
export CAROOT="${ca_root}"
export PATH="${firewall_bin_dir}:\${PATH}"
# Important: Do not bomb if exit code is non-zero, so we ensure we output the blocked.jsonl file

# This will only be used if it has values, they'll merged with the allow list passed on the command line
if [ "${COPILOT_AGENT_FIREWALL_ENABLE_RULESET_ALLOW_LIST}" = "true" ]; then
  export FIREWALL_RULESET_CONTENT=${COPILOT_AGENT_FIREWALL_RULESET_ALLOW_LIST}
fi

set +e
set -o pipefail
if [ "${current_user}" == "root" ]; then
  padawan-fw run ${ebpf_temp// /\\ }/command.sh ${debug_arg} --allow-list="${domains}" --log-file="${fw_log}" 2>&1 | tee "${output_log}"
else
  padawan-fw run su\ ${current_user}\ -p\ -c\ ${ebpf_temp// /\\ }/command.sh ${debug_arg} --allow-list="${domains}" --log-file="${fw_log}" 2>&1 | tee "${output_log}"
fi
exit_code="\$?"

# Check for exit code 200 and display error about unsupported OS versions if found
if [ "\${exit_code}" -eq 200 ]; then
  echo "Firewall not supported on current OS"
  cat "${fw_log}"
fi

# Create a log of blocked requests for end user and UX reference
touch "${blocked_log}"
if [ -e "${fw_log}" ]; then
  cat "${fw_log}" | grep -E '"blocked":\s*true,' | grep -v 'IPv6AlwaysBlocked'  > "${blocked_log}" 2>&1 || true
fi
# Get a unique list of domains out of the blocked.jsonl file
domain_list="\$(cat "${blocked_log}" | sed 's/.*"domains":\s*"\([^"]*\)".*/\1/' | grep -vE '"domains":\s*"None"' | sort -u  || echo '')"

if [ -n "\${domain_list}" ]; then
  echo -e '\n⚠️ Warning: I tried to connect to the following addresses, but was blocked by firewall rules:\n' | tee "${blocked_md}"

  # Produce a deduplicated list of blocked domains and list the first three
  # - example.com.
  #   - Triggering Command: command 1 (dns block)
  #   - Triggering Command: command 2 (dns block)
  # - google.com.
  #   - Triggering Command: command 1 (dns block)
  #   - Triggering Command: command 2 (dns block)
  #   - Triggering Command: command 3 (dns block)
  cat ${fw_log} \
    | jq --slurp -c '.[] | select(.blocked == true and .domains != "None") | {domain: .domains, cmd: .cmd, blockedAt: .blockedAt}' \
    | jq -s 'group_by(.domain) | map({domain: .[0].domain, entries: (map({cmd: .cmd, blockedAt: .blockedAt}) | unique | .[0:3])})' \
    | jq -r '.[] | "- \`\(.domain)\`\n" + (.entries | map("  - Triggering Command: \`\(.cmd)\` (\(.blockedAt) block)") | join("\n"))' 2>&1 \
    | tee -a "${blocked_md}" || echo "Error fetching blocked domains" | tee -a "${blocked_md}"

  echo -e "\nIf you need me to access, download, or install something from one of these locations, you can either:\n\n  - Configure Actions setup steps (https://gh.io/copilot/actions-setup-steps) to set up my env before the firewall is enabled.\n  - Add the appropriate URLs or hosts to my firewall allow list (https://gh.io/copilot/firewall-config)\n" | tee -a "${blocked_md}"
else
  echo "I was able to access everything I needed!" > "${blocked_md}"
fi
exit "\${exit_code}"
EOF2
chmod +x "${ebpf_temp}/wrapper.sh"

set +e
# Set up cert for temp use
echo "CA root: ${ca_root}" 2>&1 | tee "${mkcert_log}" > ${log_to_stdout_device}
CAROOT="${ca_root}" "${firewall_bin_dir}/mkcert" -install 2>&1 | tee -a "${mkcert_log}" > ${log_to_stdout_device}
# Exit on failure, output error message
if [ $? -ne 0 ]; then
  echo "mkcert install failed."
  exit $?
fi
ls -l "${ca_root}" 2>&1 | tee -a "${mkcert_log}" > ${log_to_stdout_device}
echo "" | tee -a "${mkcert_log}" > ${log_to_stdout_device}

# Start up the firewall and execute the command
if [ "$(id -u)" -eq 0 ]; then
  "${ebpf_temp}"/wrapper.sh
else
  # Switch to root user and execute the command - su is needed in this case since we will be messing with cgroups
  if ! type sudo > /dev/null 2>&1; then
    echo "Running as non-root user and sudo not found . Add sudo configured for"
    echo "password-less execution into the environment and try again. Exiting."
    exit 1
  fi
  sudo su -c "${ebpf_temp}"/wrapper.sh
fi
exit_code=$?

# Clean up
CAROOT="${ca_root}" "${firewall_bin_dir}/mkcert" -uninstall 2>&1 | tee -a "${mkcert_log}" > ${log_to_stdout_device}
rm -rf "${ca_root}" 2>&1 | tee -a "${mkcert_log}" > ${log_to_stdout_device}

if [ "${COPILOT_AGENT_DEBUG}" = "true" ] && [ "${log_to_stdout}" = "true" ]; then
  if [ -e "${fw_log}" ]; then
    echo "=============================="
    echo "Firewall log:"
    cat "${fw_log}"
  fi
    echo "=============================="
    echo "Exit code: ${exit_code}"
fi

exit ${exit_code}
