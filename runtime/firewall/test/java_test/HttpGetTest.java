package java_test;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;

public class HttpGetTest {

    public static void main(String[] args) {
        if (args.length < 1) {
            System.err.println("Usage: java HttpGetTest <url>");
            System.exit(1);
        }

        String url = args[0];
        System.out.println("Testing connection to: " + url);

        try {
            int statusCode = performHttpGet(url);
            System.out.println("Connection successful! Status code: " + statusCode);
            
            // Exit with 0 if status code indicates success (2xx)
            System.exit(statusCode >= 200 && statusCode < 300 ? 0 : statusCode);
            
        } catch (IOException e) {
            System.err.println("Connection failed: " + e.getMessage());
            System.exit(2); // Non-zero exit code for connection errors
        }
    }

    private static int performHttpGet(String urlString) throws IOException {
        URL url = new URL(urlString);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.setConnectTimeout(5000);
        connection.setReadTimeout(5000);
        connection.connect();
        
        try {
            return connection.getResponseCode();
        } finally {
            connection.disconnect();
        }
    }
}