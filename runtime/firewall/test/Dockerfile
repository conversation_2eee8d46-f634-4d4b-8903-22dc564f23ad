FROM mcr.microsoft.com/devcontainers/python:3.12-bookworm

# Install deps
RUN apt-get update \
    && apt-get install -y curl ca-certificates jq dnsutils iputils-ping iproute2 ncat \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Node
RUN . ${NVM_DIR}/nvm.sh && nvm install 20

# Install Python requests
RUN pip3 install requests

# Install .NET
RUN . /etc/os-release \
    && wget https://packages.microsoft.com/config/${ID}/${VERSION_ID}/packages-microsoft-prod.deb -O packages-microsoft-prod.deb \
    && dpkg -i packages-microsoft-prod.deb \
    && rm packages-microsoft-prod.deb \
    && apt-get update \
    && apt-get install -y dotnet-sdk-9.0 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Java 17
RUN . /etc/os-release \
    && wget -qO - https://packages.adoptium.net/artifactory/api/gpg/key/public | gpg --dearmor | tee /etc/apt/trusted.gpg.d/adoptium.gpg > /dev/null \
    && echo "deb https://packages.adoptium.net/artifactory/deb ${VERSION_CODENAME} /etc/os-release) main" | tee /etc/apt/sources.list.d/adoptium.list \
    && apt-get update \
    && apt-get install -y temurin-17-jdk

# Download firewall
RUN --mount=type=bind,source=../../,target=/runtime,readonly \
    --mount=type=secret,id=EBPF_DOWNLOAD_GITHUB_TOKEN \
    export EBPF_DOWNLOAD_GITHUB_TOKEN=$(cat /run/secrets/EBPF_DOWNLOAD_GITHUB_TOKEN) \
    && /bin/bash /runtime/script/firewall-build /opt/ebpf false \ 
    && rm -rf /var/lib/apt/lists/*

# Define the entrypoint
ENTRYPOINT [ "/bin/bash", "/opt/ebpf/launch.sh" ]
CMD [ "curl -sSLf 'https://github.com'" ]


