import { spawn } from "child_process";
import { fileURLToPath } from "url";
import { dirname } from "path";
import { tmpdir } from "os";
import { join as pathJoin } from "path";
import { randomUUID } from "crypto";
import { cpSync, mkdtempSync } from "fs";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const workingDir = mkdtempSync(pathJoin(tmpdir(), `firewall-test-${randomUUID()}`));
// Copy test files to unique working directory that is writable
cpSync(pathJoin(__dirname, "dotnet"), pathJoin(workingDir, "dotnet"), {
    recursive: true,
});
cpSync(pathJoin(__dirname, "java_test"), pathJoin(workingDir, "java_test"), {
    recursive: true,
});
cpSync(pathJoin(__dirname, "python"), pathJoin(workingDir, "python"), {
    recursive: true,
});

const fetchUrl = process.env.TEST_FETCH_URL ?? "https://github.com";
const successStatus = process.env.TEST_SUCCESS_STATUS ?? "fulfilled"; // fulfilled or rejected

function spawnCommand(command, suffix = "") {
    return new Promise((resolve, reject) => {
        console.log(`  - 🏃 Test: ${command} ${suffix}...`);
        let output = "";
        const commandProcess = spawn("bash", ["--norc", "--noprofile", "-c", command], { cwd: workingDir });
        commandProcess.stderr.on("data", (data) => {
            output += data.toString();
        });
        commandProcess.stdout.on("data", (data) => {
            output += data.toString();
        });
        commandProcess.on("error", (error) => {
            reject({ code: 1, output: `${output}\n${error.message ?? error}` });
        });
        commandProcess.on("close", (code) => {
            if (code !== 0) {
                reject({ code, output });
                return;
            }
            resolve({ code, output });
        });
    });
}

function commandPromiseWithRetry(command, retries = 3) {
    return new Promise((resolve, reject) => {
        const attempt = (n) => {
            spawnCommand(command, `(try ${retries + 1 - n} of ${retries})`)
                .then(({ code, output }) => {
                    if (successStatus === "fulfilled") {
                        console.log(`  - ✅ Test: ${command}`);
                        resolve({ code, output });
                    } else if (n > 1) {
                        setTimeout(() => {
                            attempt(n - 1);
                        }, 500);
                    } else {
                        console.log(`  - ❌ Test: ${command}`);
                        reject({
                            code,
                            output: `Command promise was not in expected status "${successStatus}" after ${retries} attempts. Exit code: ${code}\n${output}`,
                        });
                    }
                })
                .catch(({ code, output }) => {
                    if (successStatus !== "fulfilled") {
                        console.log(`  - ✅ Test: ${command}`);
                        resolve({ code, output });
                    } else if (n > 1) {
                        setTimeout(() => {
                            attempt(n - 1);
                        }, 500);
                    } else {
                        console.log(`  - ❌ Test: ${command}`);
                        reject({
                            code,
                            output: `Command promise was not in expected status "${successStatus}" after ${retries} attempts. Exit code: ${code}\n${output}`,
                        });
                    }
                });
        };
        attempt(retries);
    });
}

function fetchWithRetry(fetchUrl, retries = 3) {
    return new Promise((resolve, reject) => {
        const attempt = (n) => {
            console.log(`  - 🏃 Test: Node.js (try ${retries + 1 - n} of ${retries})...`);
            fetch(fetchUrl)
                .then((res) => {
                    if (successStatus === "fulfilled") {
                        console.log("  - ✅ Test: Node.js");
                        resolve({ code: 0, output: res.body });
                    } else if (n > 1) {
                        setTimeout(() => {
                            attempt(n - 1);
                        }, 500);
                    } else {
                        console.log(`  - ❌ Test: Node.js`);
                        reject({
                            code: 1,
                            output: `Fetch promise was not in expected status "${successStatus}" after ${retries} attempts. Status code: ${res.status}\n${res.body}`,
                        });
                    }
                })
                .catch((error) => {
                    if (successStatus !== "fulfilled") {
                        console.log("  - ✅ Test: Node.js");
                        resolve({ code: 1, output: error.message ?? error });
                    } else if (n > 1) {
                        setTimeout(() => {
                            attempt(n - 1);
                        }, 500);
                    } else {
                        console.log(`  - ❌ Test: Node.js`);
                        reject({
                            code: 0,
                            output: `Fetch promise was not in expected status "${successStatus}" after ${retries} attempts. Error: ${error.message}`,
                        });
                    }
                });
        };
        attempt(retries);
    });
}

console.log(`- 🔬 Starting tests for "${fetchUrl}"...`);
const promises = [
    fetchWithRetry(fetchUrl),
    commandPromiseWithRetry(`curl ${fetchUrl}`),
    commandPromiseWithRetry(`wget -qO- ${fetchUrl} &> /dev/null`),
    commandPromiseWithRetry(`python3 python/get_test.py "${fetchUrl}"`),
    commandPromiseWithRetry(`dotnet run --project ./dotnet -- "${fetchUrl}"`),
    commandPromiseWithRetry(`javac java_test/HttpGetTest.java && java java_test.HttpGetTest "${fetchUrl}"`),
];

Promise.all(promises)
    .then(() => {
        console.log("- 🎉 All tests completed successfully");
        process.exit(0);
    })
    .catch((error) => {
        console.error("- 😭 One or more tests failed:", error.output ?? error.message ?? error);
        process.exit(1);
    });
