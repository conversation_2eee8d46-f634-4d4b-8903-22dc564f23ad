﻿// Simplest possible test to download a url from the internet
using System;
using System.Net.Http;
using System.Threading.Tasks;

if (args.Length < 1)
{
    Console.WriteLine("Usage: SimpleDownloader <url>");
    return;
}
var url = args[0];
try
{
    using var client = new HttpClient();
    string content = await client.GetStringAsync(url);
    var response = await client.GetAsync(url);
    if (!response.IsSuccessStatusCode)
    {
        Console.Error.WriteLine($"Error: Received HTTP {(int)response.StatusCode} - {response.ReasonPhrase}");
        Environment.Exit(1);
    }
    Console.WriteLine(content);
}
catch (Exception ex)
{
    Console.Error.WriteLine($"Error downloading url: {ex.Message}");
    Environment.Exit(1);
}
