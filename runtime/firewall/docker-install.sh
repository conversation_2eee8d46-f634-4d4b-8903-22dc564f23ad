#!/bin/bash
set -e

# Whether or not to return an exit code of 0 if the installation cannot happen.
allow_skip_install="${1:-true}"

script_dir="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
runtime_dir="${script_dir}/.."

# Set placeholder launch script in target location before running checks
mkdir -p /opt/ebpf
cat << 'EOF' > /opt/ebpf/launch.sh
#!/bin/bash
exec $1
EOF
chmod +x /opt/ebpf/launch.sh


if [ "$(uname -s)" != "Linux" ] || [ "$(uname -m)" != "x86_64" ]; then
  echo "This script is only supported on x86_64 Linux."
  if [ "${allow_skip_install}" = "true" ]; then
    echo "Skipping installation."
    exit 0
  fi
  echo "Installation failed."
  exit 1
fi

if [ -z "${EBPF_DOWNLOAD_GITHUB_TOKEN}" ]; then
  echo "EBPF_DOWNLOAD_GITHUB_TOKEN environment variable is not set. Skipping installation."
  if [ "${allow_skip_install}" = "true" ]; then
    echo "Skipping installation."
    exit 0
  fi
  echo "Installation failed."
  exit 1
fi

# Install firewall binaries (assuming EBPF_GITHUB_TOKEN is set)
if [ "$(id -u)" -ne 0 ]; then
    echo -e 'Script must be run as root. Use sudo, su, or add "USER root" to your Dockerfile before running this script.'
    exit 1
fi

cd "$(dirname "$0")"

# Install deps
apt-get update
apt-get install -y curl ca-certificates jq dnsutils iputils-ping iproute2 ncat

bash "${runtime_dir}/script/firewall-build" /opt/ebpf

# Clean up
apt-get -y clean
