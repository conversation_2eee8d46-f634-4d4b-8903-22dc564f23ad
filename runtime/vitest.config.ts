/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { loadEnv } from "vite";
import { defineConfig } from "vitest/config";

export default defineConfig(({ mode }) => {
    return {
        test: {
            env: loadEnv(mode, process.cwd(), ""),
            globals: true,
            environment: "node",
            coverage: {
                provider: "v8",
                reporter: ["text", "json", "html"],
                reportOnFailure: true,
            },
            // Run all tests in parallel except CLI tests
            include: ["test/**/*.test.ts"],
            exclude: ["test/cli/**/*.test.ts"],
            pool: "forks",
            testTimeout: 30000,
        },
    };
});
