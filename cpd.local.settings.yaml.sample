github:
  host:
  token:
  user:
    name:
    email:
  repo:
    name:
    branch:
    commit:
    readWrite:
  issue:
    number:
  pr:
    number:

problem:
  statement: 
  action:

service:
  instance:
    id:
  agent:
    name:
    model:
  callback:
    url:

api:
  aipSweAgent:
    token:
  anthropic:
    key:
  copilot:
    url: https://api.githubcopilot.com
    integrationId: copilot-developer-dev
    azureKeyVaultUri:
    hmacKey: # Use COPILOT_HMAC_KEY environment variable

# Optional settings for generating trajectories
trajectory:
  # filepath to save the AI trajectory log in XML format
  outputFile: