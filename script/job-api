#!/bin/bash
#
# SWE Agent Job Management Script
#
# This script provides a unified interface for managing SWE Agent jobs and memories.
# It supports local development, staging, and production environments.
# For production and staging, it can automatically use GitHub CLI authentication.
#
# Usage:
#   script/job-api [--production|--staging] <command> <repo_owner>/<repo_name> [args...]
#
# Commands:
#   new <repo> <json_payload>           - Create a new job
#   get <repo> <job_id>                 - Get job details
#   get <repo> session <session_id>     - Get session details
#   cancel <repo> <job_id>              - Cancel a job
#   cancel <repo> session <session_id>  - Cancel a session
#   memory create <repo> <json_payload> - Create a new memory
#   memory search <repo> <json_payload> - Search memories
#
# Options:
#   --production                        - Use production environment
#   --staging                           - Use staging environment
#   --local                             - Use local development environment (default)
#
# Environment Variables:
#   USER_TOKEN                          - Token for production/staging API (auto-detected from gh CLI if not set)
#   MONALISA_PAT                        - Token for local development (Codespace)
#   JOB_API_BASE_URL                    - Override API base URL
#
# Examples:
#   script/job-api new github/private-server '{"problem_statement":"create a nice readme"}'
#   script/job-api get github/private-server 12345
#   script/job-api get github/private-server session abc123
#   script/job-api --staging cancel github/private-server 12345
#   script/job-api --production cancel github/private-server 12345
#   script/job-api memory create github/private-server '{"subject":"user preferences","fact":"user prefers TypeScript","category":"preference"}'
#   script/job-api memory search github/private-server '{"question":"what does the user prefer?"}'

set -e

# Function to show help
show_help() {
    cat << 'EOF'
SWE Agent Job Management Script

Usage:
  script/job-api [--production|--staging] <command> <repo_owner>/<repo_name> [args...]

Commands:
  new <repo> <json_payload>           - Create a new job
  get <repo> <job_id>                 - Get job details
  get <repo> session <session_id>     - Get session details
  cancel <repo> <job_id>              - Cancel a job
  cancel <repo> session <session_id>  - Cancel a session
  memory create <repo> <json_payload> - Create a new memory
  memory search <repo> <json_payload> - Search memories

Options:
  --production                        - Use production environment
  --staging                           - Use staging environment
  --local                             - Use local development environment (default)
  --help, -h                          - Show this help message

Environment Variables:
  USER_TOKEN                          - Token for production/staging API (auto-detected from gh CLI if not set)
  MONALISA_PAT                        - Token for local development (Codespace, default)
  JOB_API_BASE_URL                    - Override API base URL

Examples:
  script/job-api new github/private-server '{"problem_statement":"create a nice readme"}'
  script/job-api get github/private-server 12345
  script/job-api get github/private-server session abc123
  script/job-api --staging cancel github/private-server 12345
  script/job-api --production cancel github/private-server 12345
  script/job-api memory create github/private-server '{"subject":"user preferences","fact":"user prefers TypeScript","category":"preference"}'
  script/job-api memory search github/private-server '{"question":"what does the user prefer?"}'
EOF
}

# Configuration
SCRIPT_DIR="$(dirname "${BASH_SOURCE[0]}")"
cd "$SCRIPT_DIR"

# Default API base URLs
PRODUCTION_API_BASE_URL="https://api.githubcopilot.com/agents/swe"
STAGING_API_BASE_URL="https://api.githubcopilot.com/agents/swe-staging"
LOCAL_API_BASE_URL="http://localhost:2208"

# Parse command line options
LOCAL_MODE=true
STAGING_MODE=false
while [[ $# -gt 0 ]]; do
    case $1 in
        --production)
            LOCAL_MODE=false
            STAGING_MODE=false
            shift
            ;;
        --staging)
            LOCAL_MODE=false
            STAGING_MODE=true
            shift
            ;;
        --local)
            LOCAL_MODE=true
            STAGING_MODE=false
            shift
            ;;
        --help|-h)
            show_help
            exit 0
            ;;
        -*)
            echo "Unknown option: $1" >&2
            show_help
            exit 1
            ;;
        *)
            break
            ;;
    esac
done

# Function to set up environment based on mode
setup_environment() {
    if [ "$LOCAL_MODE" = true ]; then
        # Local development mode
        export USER_TOKEN="${MONALISA_PAT}"
        export JOB_API_BASE_URL="${JOB_API_BASE_URL:-$LOCAL_API_BASE_URL}"
        
        if [ -z "${MONALISA_PAT}" ]; then
            echo "Error: MONALISA_PAT environment variable is required for local mode." >&2
            echo "If you are not in a Codespace, please set this variable before running the script." >&2
            exit 1
        fi
    elif [ "$STAGING_MODE" = true ]; then
        # Staging mode
        export JOB_API_BASE_URL="${JOB_API_BASE_URL:-$STAGING_API_BASE_URL}"
        
        if [ -z "${USER_TOKEN}" ]; then
            # Try to get token from GitHub CLI
            if command -v gh >/dev/null 2>&1; then
                echo "USER_TOKEN not found, attempting to get token from GitHub CLI..." >&2
                export USER_TOKEN=$(gh auth token 2>/dev/null)
                if [ -z "${USER_TOKEN}" ]; then
                    echo "Error: Could not get token from GitHub CLI. Please run 'gh auth login' or set USER_TOKEN environment variable." >&2
                    exit 1
                fi
            else
                echo "Error: USER_TOKEN environment variable is required for staging mode." >&2
                echo "Please set this variable with an appropriately scoped token or install GitHub CLI." >&2
                exit 1
            fi
        fi
    else
        # Production mode
        export JOB_API_BASE_URL="${JOB_API_BASE_URL:-$PRODUCTION_API_BASE_URL}"
        
        if [ -z "${USER_TOKEN}" ]; then
            # Try to get token from GitHub CLI
            if command -v gh >/dev/null 2>&1; then
                echo "USER_TOKEN not found, attempting to get token from GitHub CLI..." >&2
                export USER_TOKEN=$(gh auth token 2>/dev/null)
                if [ -z "${USER_TOKEN}" ]; then
                    echo "Error: Could not get token from GitHub CLI. Please run 'gh auth login' or set USER_TOKEN environment variable." >&2
                    exit 1
                fi
            else
                echo "Error: USER_TOKEN environment variable is required for production mode." >&2
                echo "Please set this variable with an appropriately scoped token or install GitHub CLI." >&2
                exit 1
            fi
        fi
    fi
}

# Function to create a new job
cmd_new() {
    if [ "$#" -ne 2 ]; then
        echo "Usage: script/job-api [--production|--staging] new <repo_owner>/<repo_name> <json_payload>" >&2
        exit 1
    fi
    
    local repo="$1"
    local json_payload="$2"
    
    if [ "$LOCAL_MODE" = true ]; then
        curl -i --fail -L \
            -H "Authorization: Bearer ${USER_TOKEN}" \
            -H "Content-Type: application/json" \
            -H "Accept: application/json" \
            --data "$json_payload" \
            "$JOB_API_BASE_URL/jobs/$repo"
    else
        curl -i --fail -L \
            -H "Copilot-Integration-Id: copilot-developer-dev" \
            -H "Authorization: Bearer ${USER_TOKEN}" \
            -H "Content-Type: application/json" \
            -H "Accept: application/json" \
            --data "$json_payload" \
            "$JOB_API_BASE_URL/jobs/$repo"
    fi
}

# Function to get job or session details
cmd_get() {
    if [ "$#" -lt 2 ]; then
        echo "Usage: script/job-api [--production|--staging] get <repo_owner>/<repo_name> <job_id>" >&2
        echo "       script/job-api [--production|--staging] get <repo_owner>/<repo_name> session <session_id>" >&2
        exit 1
    fi
    
    local repo="$1"
    shift
    
    local session_url_part=""
    if [ "$1" == "session" ]; then
        session_url_part="/session"
        shift
    fi
    
    if [ "$#" -ne 1 ]; then
        echo "Usage: script/job-api [--production|--staging] get <repo_owner>/<repo_name> <job_id>" >&2
        echo "       script/job-api [--production|--staging] get <repo_owner>/<repo_name> session <session_id>" >&2
        exit 1
    fi
    
    local id="$1"
    
    if [ "$LOCAL_MODE" = true ]; then
        curl -i --fail -L \
            -H "Authorization: Bearer ${USER_TOKEN}" \
            --request "GET" \
            "$JOB_API_BASE_URL/jobs/${repo}${session_url_part}/$id"
    else
        curl -i --fail -L \
            -H "Copilot-Integration-Id: copilot-developer-dev" \
            -H "Authorization: Bearer ${USER_TOKEN}" \
            --request "GET" \
            "$JOB_API_BASE_URL/jobs/${repo}${session_url_part}/$id"
    fi
}

# Function to cancel a job or session
cmd_cancel() {
    if [ "$#" -lt 2 ]; then
        echo "Usage: script/job-api [--production|--staging] cancel <repo_owner>/<repo_name> <job_id>" >&2
        echo "       script/job-api [--production|--staging] cancel <repo_owner>/<repo_name> session <session_id>" >&2
        exit 1
    fi
    
    local repo="$1"
    shift
    
    local url_path=""
    if [ "$1" == "session" ]; then
        if [ "$#" -ne 2 ]; then
            echo "Usage: script/job-api [--production|--staging] cancel <repo_owner>/<repo_name> session <session_id>" >&2
            exit 1
        fi
        url_path="/session/$2"
    else
        if [ "$#" -ne 1 ]; then
            echo "Usage: script/job-api [--production|--staging] cancel <repo_owner>/<repo_name> <job_id>" >&2
            exit 1
        fi
        url_path="/$1"
    fi
    
    if [ "$LOCAL_MODE" = true ]; then
        curl -i --fail -L \
            -H "Authorization: Bearer ${USER_TOKEN}" \
            --request "POST" \
            "$JOB_API_BASE_URL/jobs/${repo}${url_path}/cancel"
    else
        curl -i --fail -L \
            -H "Copilot-Integration-Id: copilot-developer-dev" \
            -H "Authorization: Bearer ${USER_TOKEN}" \
            --request "POST" \
            "$JOB_API_BASE_URL/jobs/${repo}${url_path}/cancel"
    fi
}

# Function to create a new memory
cmd_memory_create() {
    if [ "$#" -ne 2 ]; then
        echo "Usage: script/job-api [--production|--staging] memory create <repo_owner>/<repo_name> <json_payload>" >&2
        exit 1
    fi
    
    local repo="$1"
    local json_payload="$2"
    
    if [ "$LOCAL_MODE" = true ]; then
        curl -i --fail -L \
            -H "Authorization: Bearer ${USER_TOKEN}" \
            -H "Content-Type: application/json" \
            -H "Accept: application/json" \
            --request "PUT" \
            --data "$json_payload" \
            "$JOB_API_BASE_URL/internal/memory/v0/$repo"
    else
        curl -i --fail -L \
            -H "Copilot-Integration-Id: copilot-developer-dev" \
            -H "Authorization: Bearer ${USER_TOKEN}" \
            -H "Content-Type: application/json" \
            -H "Accept: application/json" \
            --request "PUT" \
            --data "$json_payload" \
            "$JOB_API_BASE_URL/internal/memory/v0/$repo"
    fi
}

# Function to search memories
cmd_memory_search() {
    if [ "$#" -ne 2 ]; then
        echo "Usage: script/job-api [--production|--staging] memory search <repo_owner>/<repo_name> <json_payload>" >&2
        exit 1
    fi
    
    local repo="$1"
    local json_payload="$2"
    
    if [ "$LOCAL_MODE" = true ]; then
        curl -i --fail -L \
            -H "Authorization: Bearer ${USER_TOKEN}" \
            -H "Content-Type: application/json" \
            -H "Accept: application/json" \
            --request "POST" \
            --data "$json_payload" \
            "$JOB_API_BASE_URL/internal/memory/v0/$repo/search"
    else
        curl -i --fail -L \
            -H "Copilot-Integration-Id: copilot-developer-dev" \
            -H "Authorization: Bearer ${USER_TOKEN}" \
            -H "Content-Type: application/json" \
            -H "Accept: application/json" \
            --request "POST" \
            --data "$json_payload" \
            "$JOB_API_BASE_URL/internal/memory/v0/$repo/search"
    fi
}

# Function to handle memory subcommands
cmd_memory() {
    if [ "$#" -lt 1 ]; then
        echo "Usage: script/job-api [--production|--staging] memory <subcommand> <repo_owner>/<repo_name> [args...]" >&2
        echo "Subcommands:" >&2
        echo "  create <repo> <json_payload>  - Create a new memory" >&2
        echo "  search <repo> <json_payload>  - Search memories" >&2
        exit 1
    fi
    
    local subcommand="$1"
    shift
    
    case "$subcommand" in
        create)
            cmd_memory_create "$@"
            ;;
        search)
            cmd_memory_search "$@"
            ;;
        *)
            echo "Error: Unknown memory subcommand '$subcommand'" >&2
            echo "Available subcommands: create, search" >&2
            exit 1
            ;;
    esac
}

# Main command dispatch
if [ "$#" -lt 2 ]; then
    echo "Error: Insufficient arguments" >&2
    show_help
    exit 1
fi

setup_environment

command="$1"
shift

case "$command" in
    new)
        cmd_new "$@"
        ;;
    get)
        cmd_get "$@"
        ;;
    cancel)
        cmd_cancel "$@"
        ;;
    memory)
        cmd_memory "$@"
        ;;
    *)
        echo "Error: Unknown command '$command'" >&2
        show_help
        exit 1
        ;;
esac
