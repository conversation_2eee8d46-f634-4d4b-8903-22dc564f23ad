#!/bin/bash
set -e
cd "$(dirname "$0")"

mkdir -p actions-runner

# Defaults
image_name="mcr.microsoft.com/devcontainers/typescript-node:20-bookworm"
runner_user="node"

# Handle "set" command to change the image or runner user
if [ "$1" = "set" ]; then
    if [ ! -z "$2" ]; then
        image_name="$2"
    fi
    if [ ! -z "$3" ]; then
        runner_user="$3"
    fi
cat <<EOF > ./actions-runner/.docker-self-hosted-runner
image_name=$image_name
runner_user=$runner_user
EOF
    echo "Setup updated."
    exit 0
fi

# Load tool settings
if [ -e "actions-runner/.docker-self-hosted-runner" ]; then
    . ./actions-runner/.docker-self-hosted-runner
fi

# Map "config" to "./config.sh" and "run" to "./run.sh", default to run
command="$1"
if [ -z "$2" ]; then
    command_args=""
else
    shift
    command_args="$@"
fi
if [ "$command" = "" ]; then
    command="./run.sh"
elif [ "$command" = "run" ]; then
    command="./run.sh"
elif [ "$command" = "config" ]; then
    command="./config.sh"
fi
full_command="${command} ${command_args}"

cat <<EOF > ./actions-runner/docker-self-hosted-runner-bootstrap.sh
#!/bin/bash
set -e

# Install git if needed
if ! type -P git > /dev/null 2>&1; then
    sudo apt-get update
    sudo apt-get install -y git
fi

# Install git-lfs if needed
if ! type -P git-lfs > /dev/null 2>&1; then
    sudo apt-get update
    sudo apt-get install -y git-lfs
fi

cd "\$(dirname "\$0")"
cd /actions-runner
if [ ! -e "config.sh" ] && [ ! -e "run.sh" ]; then
    curl -o actions-runner-linux-x64-2.315.0.tar.gz -L https://github.com/actions/runner/releases/download/v2.315.0/actions-runner-linux-x64-2.315.0.tar.gz
    tar xzf ./actions-runner-linux-x64-2.315.0.tar.gz
    rm -rf ./actions-runner-linux-x64-2.315.0.tar.gz
    chown -R $runner_user:$runner_user .
fi
sudo su $runner_user -c '$full_command'
EOF
chmod +x ./actions-runner/docker-self-hosted-runner-bootstrap.sh

docker_command="$(cat <<EOF
  docker run \
    --userns=host \
    --cgroupns=host \
    --privileged \
    --pid=host \
    --network=host \
    --mount=type=bind,source=$(pwd)/actions-runner,target=/actions-runner \
    --mount=type=bind,source=/sys/fs/cgroup,target=/sys/fs/cgroup \
    -it \
    --rm
EOF
)"

${docker_command} ${image_name} /actions-runner/docker-self-hosted-runner-bootstrap.sh

