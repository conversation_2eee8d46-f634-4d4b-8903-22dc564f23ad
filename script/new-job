#!/bin/bash
#
# Example usage:
#
# Set an appropriately scoped in the env var USER_TOKEN and then run:
#
# script/new-job github/private-server '{"problem_statement":"create a nice readme"}'
# script/new-job github/private-server '{"problem_statement":"create a nice readme", "run_name": "Creating a nice README", "pull_request": {"title": "Create a nice README"}}'

set -e 
cd "$(dirname "${BASH_SOURCE[0]}")"

if [ "$#" -ne 2 ]; then
    echo "Usage: $0 <repo_owner>/<repo_name> <json_payload>"
    exit 1
fi

./job-api new "$@"

echo -e "\n\n**NOTE**: This script is deprecated. Please use 'script/job-api' instead. See 'script/job-api --help' for info."
