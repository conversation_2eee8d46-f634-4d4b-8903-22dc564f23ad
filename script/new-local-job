#!/bin/bash
#
# Example usage:
#
#
# From our development Codespace, run the script for an owner/repo and a JSON payload:
#
# script/new-local-job github/private-server '{"problem_statement":"create a nice readme"}'
# script/new-local-job github/private-server '{"problem_statement":"create a nice readme", "run_name": "Creating a nice README", "pull_request": {"title": "Create a nice README"}}'
#
# If you are not in a Codespace, be sure to set the MONALISA_PAT environment variable before running the script.

set -e 
cd "$(dirname "${BASH_SOURCE[0]}")"

if [ "$#" -ne 2 ]; then
    echo "Usage: $0 <repo_owner>/<repo_name> <json_payload>"
    exit 1
fi

./job-api --local new "$@"

echo -e "\n\n**NOTE**: This script is deprecated. Please use 'script/job-api' instead. See 'script/job-api --help' for info."
