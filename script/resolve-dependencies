#!/usr/bin/env bash
#
# This script is run automatically in Janky CI builds.
# It vendors the dependencies from our internal GOPROXY to be used in later stages.

set -euo pipefail

echo "Downloading dependencies." >&2
echo "machine goproxy.githubapp.com login nobody password $BP_GITHUB_TOKEN" > "$HOME/.netrc"

export GOPROXY=https://goproxy.githubapp.com/mod,https://proxy.golang.org,direct
export GOPRIVATE=
export GONOPROXY=
export GONOSUMDB=github.com/github/*

docker run -e "GOPROXY=$GOPROXY" -e"GONOSUMDB=$GONOSUMDB" -v "$HOME/.netrc:/root/.netrc" -v "$PWD:/app" -w /app ghcr.io/github/gh-base-image/go-builder-focal:latest go mod vendor
