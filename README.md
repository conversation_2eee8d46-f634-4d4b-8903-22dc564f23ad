# GitHub Copilot Coding Agent (sweagentd)

> **Note:** You might see this project referred to by the name `sweagentd`, "GitHub Copilot Developer (CPD)", "Project Padawan", or "Copilot dev mode"

GitHub Copilot coding agent can be assigned issues, produce PRs, respond to feedback or build failures, and collaborate with engineering teams on software engineering projects.

Read the original whitepaper: [Autonomous Software Engineering AI Agents.docx](https://microsoft-my.sharepoint.com/:w:/p/lukehoban/EXa5UXaWosBMsrmK97fK4vMBVr7qroJ_jtU7Iej8f1cIRg?e=jno0dM)

## Want to try it out?

The agent is now in Public Preview! Check out our [docs](https://docs.github.com/en/copilot/using-github-copilot/coding-agent/about-assigning-tasks-to-copilot) to get started.

Head to `#coding-agent-dogfood` in Slack, and share your feedback! ❤️👀

## Staging environment

### Deployment

At this moment in time, we operate out of the [#copilot-extensibility-ops](https://github-grid.enterprise.slack.com/archives/C06C2V2EE5T) channel for running these commands. See [how to deploy an app](https://thehub.github.com/epd/engineering/devops/deployment/how-to-deploy-an-app/#deploy-the-app) for more info about how GitHub does deployments.

1. Get a PR approval (do not merge, only approval is needed)
1. `.wcid sweagentd` (optional) you can check status of environments by running
1. `.deploy <PR url> to staging` (you may see a locked env here, that's ok and you can ping whoever has it locked to ask for availability info)
1. `.unlock sweagentd in staging` when you're finished. Important to release it back to others.
1. `.deploy sweagentd to staging` (optional) restores staging back to latest main. This happens automatically every time we deploy main, too.

### Opting-in

To route requests to staging, you need to opt in with a feature flag.

If you _don't_ want to opt in, just use [padawan-testing-staging](https://github.com/github/padawan-testing-staging)!

In order to get specific Copilot coding agent jobs to route to staging, you opt in your user to the:

[sweagentd_route_user_or_repo_to_staging feature flag](https://devportal.githubapp.com/feature-flags/sweagentd_route_user_or_repo_to_staging/targeting-rules/actors-view?stamp=dotcom)

Hydro events that trigger the runtime arrive with your user as the actor, and that means that event will be fully processed in the staging environment. **However**, certain events (like ComputeUsage) **do not contain your user as the actor** , meaning that the feature flag enablement can be skipped for those specific events. If you want staging to process these events, you should enable the feature flag on a repository that can safely be opted into staging routing. **Do not** enable this feature flag on a repository that might see non-development traffic, since you will impact all repository users if you do!

**Make sure to remove your user from the feature flag when you are done**, or you may find that you are confused about why you are seeing strange behavior as others may be testing changes in staging.

One easy trick could be to just consistently use a single test repository that is permanently opted into the feature flag, which will guarantee that all events are processed in the staging environment, and you won't need to keep track of toggling your user.

### What if the environment is in use?

Just DM the person who has it locked. If there isn't an explicit lock reason and the person is not responding, there's a good chance that they forgot to unlock the deployment. If you DM them and they're not available and it's been an hour or two since they deployed to staging, you should be OK to manually unlock and use the environment.

## Testing the Job API

The `sweagentd` service provides a [Job API](./docs/adr/0001-create-job-api.md) that allows you to kick off, get status on, and cancel coding agent jobs. Since this can have unique challenges, be sure you run a test of the API before deploying your changes.

To make this easy, the `script/job-api` script is simple wrapper around the needed curl calls. You can use it against a `--local` environment for testing in a codespace, the `--staging` environment, or even triage problems in `--production`.

For example, this will create a simple new job locally in a codespace:

```bash
script/job-api --local new github/private-server '{"problem_statement":"Improve the README."}'
```

More examples and usage can be found by running:

```bash
script/job-api --help
```

You can copy this script locally for use as well. See `--help` for details on expected environment variables.

## Want to start contributing to this project?

Jump to one of these docs:

- **sweagentd**: [docs/service-dev-setup-with-github.md](docs/service-dev-setup-with-github.md)
- **Runtime**: [docs/runtime-dev-setup.md](docs/runtime-dev-setup.md)
- **CLI**: [docs/cli-dev-setup.md](docs/cli-dev-setup.md)

## Architecture

You can read more about the architecture of Project Padawan in [docs/architecture.md](docs/architecture.md).

## Where can you reach us? :copilot:
- Team channel: `#coding-agent-team`
- General channel: `#coding-agent`
- Dogfood channel: `#coding-agent-dogfood`

## What are we up to? 
Check out our weekly team demos [here](https://www.loom.com/spaces/Copilot-Coding-Agent-38857939/folders/Coding-Agent-team-demos-f9f61f478d1a4403b1880d7ec00fe3ed), or see the pinned issue. 
