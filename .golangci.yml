run:
  # Include test files or not.
  tests: true

linters:
  # Disable all rules, then manually enable
  default: "none"
  # https://golangci-lint.run/usage/linters/
  enable:
    # rules enabled by default
    - errcheck
    - gosimple
    - govet
    - ineffassign
    - staticcheck
    - typecheck
    - unused
    - goconst
    - gocritic
    - gocyclo
    - gofmt
    - goimports
    - revive
    - unconvert
