#!/bin/bash

# Check if the runID is provided as an argument
if [ -z "$1" ]; then
    echo "Usage: $0 <runID> [--lock]"
    exit 1
fi

# Get the runID and construct the report.json path
RUN_ID="$1"
REPORT_PATH="data-$RUN_ID/report.json"
OUTPUT_DIR="data-$RUN_ID-error"

# Check for --lock option
USE_LOCK=""
if [ "$2" = "--lock" ]; then
    USE_LOCK="--lock --backend ces-ame"
fi

# Check if the report.json file exists
if [ ! -f "$REPORT_PATH" ]; then
    echo "Error: report.json not found at $REPORT_PATH"
    exit 1
fi

# Create output directory if it doesn't exist
[ ! -d "${OUTPUT_DIR}" ] && mkdir -p "${OUTPUT_DIR}"

# Check and update the runGroup in the run_agent_config.yaml file
CONFIG_FILE="run_agent_config.yaml"

if [ -f "$CONFIG_FILE" ]; then
    echo "Updating runGroup in $CONFIG_FILE to match runId: $RUN_ID"
    
    # Check if runGroup exists in the file
    if grep -q "runGroup:" "$CONFIG_FILE"; then
        # Update existing runGroup
        sed -i "s/runGroup:.*$/runGroup: \"$RUN_ID\"/" "$CONFIG_FILE"
    else
        # Add runGroup at the same level as agent-model
        sed -i "/agent-model:/i\\  runGroup: \"$RUN_ID\"" "$CONFIG_FILE"
    fi
else
    echo "Warning: $CONFIG_FILE not found, cannot update runGroup"
fi

echo "Re-running Errored instances"
msbench-cli run --config run_agent_config.yaml --lock --backend ces-ame --benchmark "$REPORT_PATH:error,missing" --output "${OUTPUT_DIR}/report.json"  --workers 20 $USE_LOCK

# After the run completes, extract the run_id from the generated report.json
if [ -f "${OUTPUT_DIR}/report.json" ]; then
    # Extract run_id using grep and cut (works on most systems without jq dependency)
    NEW_RUN_ID=$(grep -o '"run_id":[[:space:]]*"[^"]*"' "${OUTPUT_DIR}/report.json" | cut -d'"' -f4)
    
    if [ -n "$NEW_RUN_ID" ]; then
        echo "Extracted new run_id: $NEW_RUN_ID"
        echo "Running extract-and-report script with the new run_id..."
        cd "${OUTPUT_DIR}" && ../3-extract-and-report.sh "$NEW_RUN_ID"
    else
        echo "Error: Could not extract run_id from ${OUTPUT_DIR}/report.json"
        exit 1
    fi
else
    echo "Error: ${OUTPUT_DIR}/report.json was not generated"
    exit 1
fi
