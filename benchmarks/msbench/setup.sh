#!/bin/bash

# Check if msbench-cli is installed, if not install it and requirements
if ! pip3 show msbench-cli > /dev/null 2>&1; then
    echo "Upgrading pip..."
    pip3 install --upgrade pip
    pip3 install artifacts-keyring

    echo "Installing msbench-cli requirements..."
    pip3 install -r requirements.txt
    echo
fi
echo "msbench-cli version:"
msbench-cli version
echo

# Check Azure login and refresh token if needed
if ! az account get-access-token > /dev/null 2>&1; then
    echo "Logging to Azure to connect to CES (Code Execution Service)..."
    az login
    echo
fi
echo