/**
 * Collect Corruption Statistics
 * 
 * This script analyzes git diff statistics to identify potentially corrupted files
 * in code changes across multiple repositories.
 * 
 * Usage:
 *   node collect_corruption_stats.js <runID>
 * 
 * Where:
 *   - runID: The identifier for the run data folder (data-<runID>)
 * 
 * Output:
 *   - corruption_stats.csv: CSV file with corruption statistics for each instance
 *   - overall_corruption_stats.txt: Summary of corruption statistics across all instances
 * 
 * CSV Output Format:
 *   The CSV file contains the following columns:
 *   - instance_corrupted: Repository/PR identifier
 *   - branch_commit: The commit hash of the branch
 *   - changes_stats: Overall git diff stats summary
 *   - files_changed: Number of files modified
 *   - inserted_lines: Total lines inserted
 *   - deleted_lines: Total lines deleted
 *   - binary_files: Number of binary files
 *   - files_too_many_deletes: Number of files with >500 deletions
 *   - files_corrupted: Number of files considered corrupted
 *   - corruption_index: Ratio of corrupted files to total files changed
 * 
 * How to read the CSV:
 *   1. Import the CSV into a spreadsheet application or use a CSV parser
 *   2. Look for instances with high corruption_index values (closer to 1.0)
 *   3. Pay attention to instances with high files_corrupted counts and files_too_many_deletes
 */

const fs = require('fs');
const path = require('path');

// Check if the runID is provided as an argument
if (process.argv.length < 3) {
    console.error('Usage: node collect_corruption_stats.js <runID>');
    process.exit(1);
}

// Get the runID and construct the base path
const runID = process.argv[2];
const basePath = path.resolve(`data-${runID}`);

// Output CSV file path
const outputPath = path.join(basePath, 'corruption_stats.csv');

// Function to read file content
const readFileContent = (filePath) => {
    try {
        const content =  fs.readFileSync(filePath, 'utf8').trim();
        return content.replaceAll(',', ';');
    } catch (err) {
        return '';
    }
};

function countFileDiff(diffStr) {
    let fileNum = 0,
        insertNum = 0,
        deleteNum = 0,
        corruptNum = 0, 
        filesWithTooManyDeletes = 0,
        binaryFiles = 0;

    const lines = diffStr.split('\n');
    for (const line of lines) {
        if (line.length === 0) continue;
        try {
            const parts = line.split(/\s+/);
            
            // Check if this is a binary file (represented by "- -" in git numstat)
            if (parts[0] === '-' && parts[1] === '-') {
                binaryFiles += 1;
                continue; // Skip binary files in corruption metrics
            }
            
            const insertLines = parseInt(parts[0]);
            const deleteLines = parseInt(parts[1]);
            insertNum += insertLines;
            deleteNum += deleteLines;
            fileNum += 1;
            const meetsThreshold = deleteLines >= 10;
            filesWithTooManyDeletes += deleteLines > 500 ? 1 : 0; // count files with more than 500 deletions

            // if the file has more than 10 lines of deletion and the ratio of insertions to deletions is greater than 4, mark it as corrupt
            // this is a heuristic to identify files that are likely to be incorrectly updated
            if (meetsThreshold && (insertLines === 0 || deleteLines / insertLines > 4)) {
                corruptNum += 1;
            }
        } catch (error) {
            continue;
        }
    }

    // if number files changed 0 and number of corrupt files > 0, set corruption index to -1, since metric is not valid
    // else set it to the ratio of corrupt files to number of files changed
    const corruptionIndex = fileNum == 0 ? corruptNum > 0 ? -1 : 0 : (corruptNum / fileNum);

    return { fileNum, insertNum, deleteNum, corruptNum, corruptionIndex, filesWithTooManyDeletes, binaryFiles };
}

// Collect stats from each subdirectory
const collectStats = () => {
    const csvLines = ['instance_corrupted,branch_commit,changes_stats,files_changed,inserted_lines,deleted_lines,binary_files,files_too_many_deletes,files_corrupted,corruption_index'];

    // Find all subdirectories containing the required files without using glob
    const findDirectories = (dir) => {
        let results = [];
        const list = fs.readdirSync(dir);
        list.forEach((file) => {
            const filePath = path.join(dir, file);
            const stat = fs.statSync(filePath);
            if (stat && stat.isDirectory()) {
                if (fs.existsSync(path.join(filePath, 'output'))) {
                    results.push(path.join(filePath, 'output'));
                }
                results = results.concat(findDirectories(filePath));
            }
        });
        return results;
    };

    const directories = findDirectories(basePath);

    let all_files_changed = 0;
    let all_files_corrupted = 0;
    let all_files_too_many_deletes = 0;
    let repos_corrupted = 0;

    directories.forEach((dir) => {
        let folderName = path.relative(basePath, path.dirname(dir));
        // Trim the specific prefix "sweb.eval.x86_64." and suffix "-output" or "\output" or "/output" from folder names
        folderName = folderName.replace(/^.*\.eval\.x86_64\./, '').replace(/-output.*$/, '');
        const changesStats = readFileContent(path.join(dir, 'changes.stats'));
        const branchCommit = readFileContent(path.join(dir, 'changes.branch'));
        const changesNumstat = readFileContent(path.join(dir, 'changes.numstat'));

        // corruption metrics
        const changesStatsCorruptionStats = countFileDiff(changesNumstat);

        const inserted_lines = changesStatsCorruptionStats.insertNum;
        const deleted_lines = changesStatsCorruptionStats.deleteNum;
        const files_too_many_deletes = changesStatsCorruptionStats.filesWithTooManyDeletes;
        const files_changed = changesStatsCorruptionStats.fileNum; 
        const files_corrupted = changesStatsCorruptionStats.corruptNum;
        const binary_files = changesStatsCorruptionStats.binaryFiles;
        
        csvLines.push(`${folderName},${branchCommit},${changesStats},${files_changed},${inserted_lines},${deleted_lines},${binary_files},${files_too_many_deletes},${files_corrupted},${changesStatsCorruptionStats.corruptionIndex}`);

        // update overall stats
        all_files_changed += files_changed;
        all_files_corrupted += files_corrupted;
        all_files_too_many_deletes += files_too_many_deletes;
        repos_corrupted += files_corrupted > 0 ? 1 : 0;
    });

    // Write the CSV data to the output file
    fs.writeFileSync(outputPath, csvLines.join('\n'), 'utf8');

    // write to a separate file the overall stats
    const overallStatsPath = path.join(basePath, 'overall_corruption_stats.txt');
    const runCorruptionIndex = all_files_changed == 0 ? 0 : (all_files_corrupted / all_files_changed);
    
    const overallStats = `
Total files changed: ${all_files_changed}
Total files corrupted: ${all_files_corrupted}
Total files with too many deletes: ${all_files_too_many_deletes}
Number of repos with corrupted files: ${repos_corrupted}

Run corruption index: ${runCorruptionIndex}
`;
    fs.writeFileSync(overallStatsPath, overallStats.trim(), 'utf8');
    
    console.log('');
    console.group('Corruption Statistics Summary');
    console.table({
        'Total files changed': all_files_changed,
        'Total files corrupted': all_files_corrupted,
        'Total files with too many deletes': all_files_too_many_deletes,
        'Number of repos with corrupted files': repos_corrupted,
        'Run corruption index': runCorruptionIndex.toFixed(4)
    });

    console.groupEnd();

    console.log('');
    console.log(`CSV file has been written to ${outputPath}\n`);
    console.log(`Overall stats have been written to ${overallStatsPath}`);
};

// Run the collectStats function
collectStats();