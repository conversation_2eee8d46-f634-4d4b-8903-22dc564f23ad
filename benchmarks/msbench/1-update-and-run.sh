#!/bin/bash
set -e

if [ -z "$2" ]; then
  echo "Usage: $0 <benchmark> [<integration_id>] [--copilot-key-vault VAULT_URI] [--kv-secret-name SECRET_NAME] [--copilot-hmac-key KEY] [--agent-model sweagent-capi:MODEL_NAME] [--include-mcps] [--url AZURE_OPENAI_ENDPOINT_URL] [--tags key1=value1,key2=value2] [--workers NUM] [--no-wait]"
  echo "       [--blackbird-storage-account ACCOUNT, default padawanblackbird] [--blackbird-container CONTAINER, default blackbird] [--blackbird-mcp-blob BLOB_PATH, default blackbird-mcp] [--blackbird-local-blob BLOB_PATH default blackbird-local] [--blackbird-index-blob BLOB_PATH] [--disable-blackbird] [--blackbird-mode initial-search|tool (default initial-search)]"
  exit 1
fi

# get the benchmark name
benchmark=$1
shift

# check for --no-wait and --workers flags and extract them
no_wait_flag=""
workers=20
lock_flag=""
remaining_args=()
while [[ $# -gt 0 ]]; do
  case $1 in
    --no-wait)
      no_wait_flag="--no_wait -y"
      shift
      ;;
    --workers)
      workers="$2"
      shift 2
      ;;
    --lock)
      lock_flag="--lock --backend ces-ame"
      shift
      ;;
    *)
      remaining_args+=("$1")
      shift
      ;;
  esac
done

# pass remaining params to update_cpd.sh
./update_cpd.sh "${remaining_args[@]}"

# Run the benchmark
echo "Running benchmark... ($benchmark)"
msbench-cli run --config run_agent_config.yaml --lock --backend ces-ame --benchmark $benchmark --workers $workers $no_wait_flag $lock_flag
