#!/bin/bash
set -e

# Check if we can get a token for the required scope for the AME backend
if ! az account get-access-token --scope api://1b702e92-5faa-4853-b2be-cd5e5d0c21ff/.default >/dev/null 2>&1; then
    echo "Not logged in or token expired. Logging in..."
    az login --scope api://1b702e92-5faa-4853-b2be-cd5e5d0c21ff/.default
fi

# Pass all arguments to 1-update-and-run.sh with the --lock option added
./1-update-and-run.sh "$@" --lock
