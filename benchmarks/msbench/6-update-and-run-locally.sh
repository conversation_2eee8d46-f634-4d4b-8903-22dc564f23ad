#!/bin/bash
#------------------------------------------------------------------------------
# Update and run benchmarks locally with batching support
#
# This script:
# 1. Updates the CPD (Code Problem Description) with a specified integration ID
# 2. Runs benchmark instances in batches to avoid memory/resource issues
# 3. Extracts and consolidates results into a single report
# 4. Organizes all outputs in a timestamped directory
#
# PREREQUISITES:
# - This script only works when use_cpd_image is set to false in runner.sh
# - Requires msbench-cli to be installed and configured
# - Requires Azure CLI with access to the Container Registry
# - Requires Docker to be installed and running
#
# Resume running:
# "$(paste -sd, data-local-20250509-232821/pending_instances.csv)" for benchmark 
# USAGE:
#   ./6-update-and-run-locally.sh <benchmark> <integration_id> [options]
#------------------------------------------------------------------------------
set -e

# Function to retry a command with a delay
retry_command() {
    local command="$1"
    local max_attempts=3
    local delay=30
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        echo "Attempt $attempt of $max_attempts: $command"
        if eval "$command"; then
            return 0
        else
            echo "Command failed, retrying in $delay seconds..."
            sleep $delay
            attempt=$((attempt + 1))
        fi
    done
    
    echo "All $max_attempts attempts failed for: $command"
    return 1
}

# Check if a Docker image exists locally
image_exists() {
    local image="$1"
    if docker image inspect "$image" &>/dev/null; then
        return 0  # Image exists
    else
        return 1  # Image does not exist
    fi
}

# ensure params are passed in
if [ -z "$3" ]; then
  echo "Usage: $0 <benchmark> <integration_id> [--copilot-key-vault VAULT_URI] [--copilot-hmac-key KEY] [--agent-model sweagent-capi:MODEL_NAME] [--tags key1=value1,key2=value2]"
  exit 1
fi

# get the benchmark name
benchmark=$1
shift

# pass all other params to update_cpd.sh
./update_cpd.sh $@

# Login to Azure Container Registry
echo "Logging in to Azure Container Registry..."
retry_command "az acr login --name codeexecservice"

# Get list of all instances in CSV format
echo "Listing all instances for benchmark: $benchmark..."
instances=$(msbench-cli list_instances --benchmark $benchmark --format csv)

# Fix: Store instances in a temporary file to avoid subshell issues
temp_file=$(mktemp)
# Only extract non-empty rows that don't contain "Benchmark,Instance,Image" (header rows)
# and filter out log messages that start with date/time in brackets
echo "$instances" | grep -v "^$" | grep -v "Benchmark,Instance,Image" | grep -v "^\[[0-9]" > "$temp_file"

# Initialize arrays
instance_names=()
image_names=()

# Process each line of the CSV - only data rows remain in the temp file
while IFS=, read -r bench instance image; do
    instance_names+=("$bench.$instance")
    image_names+=("codeexecservice.azurecr.io/$image")
done < "$temp_file"

rm "$temp_file"  # Clean up temp file

# Generate timestamp in format YYYYMMDD-HHMMSS
timestamp=$(date '+%Y%m%d-%H%M%S')
OUTPUT_DIR="data-local-$timestamp"

# Create output directory if it doesn't exist
[ ! -d "${OUTPUT_DIR}" ] && mkdir -p "${OUTPUT_DIR}"

# Copy the run configuration to the output directory
echo "Copying run configuration to output directory..."
cp run_agent_config.yaml "${OUTPUT_DIR}/"

# Create a summary file with information about the benchmark
summary_file="${OUTPUT_DIR}/run_summary.txt"
echo "Benchmark Summary" > "$summary_file"
echo "----------------" >> "$summary_file"
echo "Benchmark: $benchmark" >> "$summary_file"
echo "Run Date: $(date '+%Y-%m-%d %H:%M:%S')" >> "$summary_file"
echo "----------------" >> "$summary_file"
echo "" >> "$summary_file"

# Calculate total number of instances and batches
total_instances=${#instance_names[@]}
batch_size=10
# Fix: Ensure at least 1 batch when there are instances
if [ $total_instances -gt 0 ]; then
    total_batches=$(( (total_instances + batch_size - 1) / batch_size ))
else
    total_batches=0
fi

echo "Processing $total_instances instances in $total_batches batches of $batch_size"

# Process instances in batches
for (( batch=0; batch<total_batches; batch++ ))
do 
    start_idx=$((batch * batch_size))
    end_idx=$(( (batch + 1) * batch_size - 1 ))
    if [ $end_idx -ge $total_instances ]; then
        end_idx=$((total_instances - 1))
    fi
    
    echo "Processing batch $((batch + 1))/$total_batches (instances $start_idx-$end_idx)"
    
    # Prepare comma-separated list of instances for this batch
    batch_instances=""
    batch_images=()
    
    for (( i=start_idx; i<=end_idx; i++ ))
    do
        if [ -n "$batch_instances" ]; then
            batch_instances+=","
        fi
        # Format as benchmark.instance
        batch_instances+="${instance_names[$i]}"
        batch_images+=("${image_names[$i]}")
    done
    
    # Output the instances for this batch to a file for processing again in case of failure
    batch_file="${OUTPUT_DIR}/batch.${batch}.txt"
    echo "$batch_instances" >> "$batch_file"
    
    # Update the pending_instances.csv file with all instances in remaining batches
    pending_file="${OUTPUT_DIR}/pending_instances.csv"
    > "$pending_file" # Clear the file first
    
    # Calculate the starting index for the remaining instances
    # Fix: Use end_idx + 1 to avoid overlapping instances between batches
    remaining_start=$((end_idx + 1))
    if [ $remaining_start -lt $total_instances ]; then
        # Write all remaining instances to the pending file
        for (( i=remaining_start; i<total_instances; i++ )); do
            echo "${instance_names[$i]}" >> "$pending_file"
        done
        echo "Updated pending_instances.csv with $(( total_instances - remaining_start )) remaining instances"
    else
        echo "No pending instances remaining"
        echo "" > "$pending_file" # Empty file but still create it
    fi
    
    # Pull Docker images for this batch
    echo "Pulling Docker images for batch $((batch + 1))..."
    for image in "${batch_images[@]}"
    do
        if image_exists "$image"; then
            echo "Image already exists locally: $image (skipping pull)"
        else
            echo "Pulling image: $image"
            retry_command "docker pull \"$image\""
        fi
    done
    
    # Run the benchmark for this batch
    echo "Running instances: $batch_instances"
    report_file="${OUTPUT_DIR}/report.${batch}.csv"
    msbench-cli run --config run_agent_config.yaml --benchmark $batch_instances --backend local --output "$report_file"
    
    # Extract run_id from the report file and run extract command
    if [ -f "$report_file" ]; then
        # Get the run_id from the CSV file - it's in the second column of the first data row
        run_id=$(tail -n +2 "$report_file" | head -n 1 | awk -F ',' '{print $2}')
        
        if [ -n "$run_id" ]; then
            # Create batch directory
            batch_dir="${OUTPUT_DIR}/batch_${batch}"
            
            # Run extract command
            echo "Extracting data for run_id: $run_id"
            msbench-cli extract --run_id "$run_id" --output "$batch_dir"
        else
            echo "Warning: Could not extract run_id from $report_file"
        fi
    else
        echo "Warning: Report file $report_file not found"
    fi
    
    # Delete Docker images to free up space
    echo "Cleaning up Docker images for batch $((batch + 1))..."
    for image in "${batch_images[@]}"
    do
        echo "Removing image: $image"
        docker rmi "$image" || true
    done

    # Consolidate reports - keep headers only from the first batch
    if [ -f "$report_file" ]; then
        if [ "$batch" -eq 0 ]; then
            # For first batch, copy the entire file including headers
            cp "$report_file" "${OUTPUT_DIR}/report.csv"
            echo "Created consolidated report file: ${OUTPUT_DIR}/report.csv"
        else
            # For subsequent batches, append data without headers
            tail -n +2 "$report_file" >> "${OUTPUT_DIR}/report.csv"
            echo "Appended batch $((batch + 1)) results to consolidated report"
        fi
    fi
    
    echo "Completed batch $((batch + 1))/$total_batches"
    echo "-----------------------------------"
done

echo "All batches completed successfully."
echo "Consolidated report available at: ${OUTPUT_DIR}/report.csv"
