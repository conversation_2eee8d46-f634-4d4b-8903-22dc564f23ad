const fs = require('fs');
const path = require('path');

// Get runID from command line arguments
const runID = process.argv[2];

if (!runID) {
  console.error('Error: Please provide a runID as an argument');
  console.error('Usage: node collect_online_eval_metrics.js <runID>');
  process.exit(1);
}

const dataFolderName = `data-${runID}`;
const currentDir = path.dirname(__filename);
const dataFolderPath = path.join(currentDir, dataFolderName);

// Function to find all onlineEvaluationResults.json files
function findJsonFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      findJsonFiles(filePath, fileList);
    } else if (file === 'onlineEvaluationResults.json') {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// Function to extract data from JSON file
function extractData(filePath, basePath) {
  try {
    const data = fs.readFileSync(filePath, 'utf8');
    const json = JSON.parse(data);
    let relativePath = path.relative(basePath, path.dirname(filePath));
    
    // Clean up the instance path by removing prefixes and suffixes
    relativePath = relativePath.replace(/^.*\.eval\.x86_64\./, '').replace(/-output.*$/, '');
    
    // Group data by event type
    const eventData = {};
    
    // Process each value entry in the JSON
    if (json.values && Array.isArray(json.values)) {
      json.values.forEach(value => {
        if (value.kind === 'telemetry' && value.telemetry) {
          const telemetry = value.telemetry;
          const event = telemetry.event;
          
          // Initialize event data if it doesn't exist
          if (!eventData[event]) {
            eventData[event] = {
              [`instance_${event}`]: relativePath
            };
          }
          
          // Special handling for trajectory event
          if (event === 'online_eval_trajectory') {
            if (telemetry.properties && telemetry.properties.values) {
              try {
                const trajectoryData = JSON.parse(telemetry.properties.values);
                if (trajectoryData.values && Array.isArray(trajectoryData.values)) {
                  trajectoryData.values.forEach(item => {
                    if (item.message && item.answer) {
                      // Create a clean column name from the message
                      const columnName = `trajectory_${item.message
                        .replace(/[^a-zA-Z0-9]/g, '_')
                        .replace(/_+/g, '_')
                        .toLowerCase()
                        .substring(0, 50)}`; // Limit length for readability
                      
                      eventData[event][columnName] = item.answer;
                    }
                  });
                }
              } catch (e) {
                console.error(`Error parsing trajectory data in ${filePath}:`, e);
              }
            }
          }
          
          // Extract metrics
          if (telemetry.metrics) {
            Object.entries(telemetry.metrics).forEach(([key, val]) => {
              eventData[event][key] = val;
            });
          }
          
          // Extract and parse properties
          if (telemetry.properties) {
            Object.entries(telemetry.properties).forEach(([key, val]) => {
              try {
                // Try to parse as JSON if it's a string
                if (typeof val === 'string' && (val.startsWith('{') || val.startsWith('['))) {
                  const parsedValue = JSON.parse(val);
                  
                  // If it's an object with "values" array, flatten each value
                  if (parsedValue && parsedValue.values && Array.isArray(parsedValue.values)) {
                    parsedValue.values.forEach(item => {
                      if (item.description && (item.score !== undefined || item.answer !== undefined)) {
                        const columnName = item.description.replace(/[^a-zA-Z0-9_]/g, '_');
                        eventData[event][columnName] = item.score !== undefined ? item.score : item.answer;
                        
                        // Add justification if it exists
                        if (item.justification) {
                          eventData[event][`${columnName}_justification`] = item.justification;
                        }
                      }
                    });
                  } else {
                    // For other JSON objects, flatten their properties
                    flattenObject(parsedValue, `${key}_`, eventData[event]);
                  }
                } else {
                  eventData[event][key] = val;
                }
              } catch (e) {
                // If parsing fails, use the original value
                eventData[event][key] = val;
              }
            });
          }
          
          // Extract and parse restricted properties
          if (telemetry.restrictedProperties) {
            Object.entries(telemetry.restrictedProperties).forEach(([key, val]) => {
              try {
                // Try to parse as JSON if it's a string
                if (typeof val === 'string' && (val.startsWith('{') || val.startsWith('['))) {
                  const parsedValue = JSON.parse(val);
                  
                  // If it's an object with "values" array, flatten each value
                  if (parsedValue && parsedValue.values && Array.isArray(parsedValue.values)) {
                    parsedValue.values.forEach(item => {
                      if (item.description && (item.score !== undefined || item.answer !== undefined)) {
                        const columnName = `${item.description.replace(/[^a-zA-Z0-9_]/g, '_')}_restricted`;
                        eventData[event][columnName] = item.score !== undefined ? item.score : item.answer;
                        
                        // Add justification if it exists
                        if (item.justification) {
                          eventData[event][`${columnName}_justification`] = item.justification;
                        }
                      }
                    });
                  } else {
                    // For other JSON objects, flatten their properties
                    flattenObject(parsedValue, `${key}_restricted_`, eventData[event]);
                  }
                } else {
                  eventData[event][`${key}_restricted`] = val;
                }
              } catch (e) {
                // If parsing fails, use the original value
                eventData[event][`${key}_restricted`] = val;
              }
            });
          }
        }
      });
    }
    
    return eventData;
  } catch (err) {
    console.error(`Error processing file ${filePath}:`, err);
    return { error: { instance_eval: path.relative(basePath, path.dirname(filePath)), message: err.message } };
  }
}

// Helper function to flatten nested objects
function flattenObject(obj, prefix = '', result = {}) {
  for (const key in obj) {
    const value = obj[key];
    const newKey = prefix + key;
    
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      flattenObject(value, newKey + '_', result);
    } else {
      result[newKey] = value;
    }
  }
  return result;
}

// Main function
function main() {
  try {
    if (!fs.existsSync(dataFolderPath)) {
      console.error(`Directory not found: ${dataFolderPath}`);
      process.exit(1);
    }
    
    console.log(`Searching for onlineEvaluationResults.json files in ${dataFolderPath}...`);
    const jsonFiles = findJsonFiles(dataFolderPath);
    console.log(`Found ${jsonFiles.length} files`);
    
    if (jsonFiles.length === 0) {
      console.log('No files found. Exiting.');
      process.exit(0);
    }
    
    // Extract data from each file and group by event type
    const eventTypes = new Set();
    const eventData = {};
    
    jsonFiles.forEach(file => {
      const fileData = extractData(file, dataFolderPath);
      
      // Process each event type from this file
      for (const eventType in fileData) {
        eventTypes.add(eventType);
        
        if (!eventData[eventType]) {
          eventData[eventType] = [];
        }
        
        eventData[eventType].push(fileData[eventType]);
      }
    });
    
    console.log(`Found ${eventTypes.size} event types: ${Array.from(eventTypes).join(', ')}`);
    
    // Create a CSV file for each event type
    eventTypes.forEach(eventType => {
      const rows = eventData[eventType];
      
      if (!rows || rows.length === 0) {
        console.log(`No data for event type: ${eventType}`);
        return;
      }
      
      // Get all unique column names for this event type
      const columns = new Set();
      rows.forEach(row => {
        Object.keys(row).forEach(key => columns.add(key));
      });
      
      // Create CSV content
      const columnArray = Array.from(columns);
      let csvContent = columnArray.join(',') + '\n';
      
      rows.forEach(row => {
        const csvRow = columnArray.map(col => {
          const value = row[col] === undefined ? '' : row[col];
          // Handle CSV special characters by wrapping in quotes and escaping quotes
          return typeof value === 'string' 
            ? `"${value.replace(/"/g, '""')}"` 
            : value;
        }).join(',');
        csvContent += csvRow + '\n';
      });
      
      // Write to event-specific file
      const eventFileName = path.join(dataFolderPath, `${eventType}_metrics.csv`);
      fs.writeFileSync(eventFileName, csvContent);
      console.log(`CSV data for event ${eventType} written to ${eventFileName}`);
    });
    
  } catch (err) {
    console.error('Error:', err);
    process.exit(1);
  }
}

main();
