#!/bin/bash

# Function to recursively unzip files
recursive_unzip() {
    local dir="$1"
    
    # Keep track if we found any zip files
    local found=0
    
    # Find all zip files in the current directory and subdirectories
    while IFS= read -r -d '' zip_file; do
        found=1
        # Get the directory containing the zip file
        local dirname=$(dirname "$zip_file")
        # Create a directory name from the zip file name
        local basename=$(basename "$zip_file" .zip)
        local extract_dir="$dirname/$basename"
        
        echo "Extracting: $zip_file"
        # Create directory and unzip
        mkdir -p "$extract_dir"
        unzip -q "$zip_file" -d "$extract_dir"
        # Remove the zip file after extraction
        rm "$zip_file"
    done < <(find "$dir" -type f -name "*.zip" -print0)
    
    # If we found and extracted any zip files, run again to check for nested zips
    if [ $found -eq 1 ]; then
        # Sleep briefly to ensure file system is updated
        sleep 1
        recursive_unzip "$dir"
    fi
}

# Check if zip file argument is provided
if [ $# -ne 1 ]; then
    echo "Usage: $0 <zipfile>"
    exit 1
fi

# Check if file exists and is a zip file
if [ ! -f "$1" ] || [[ "$1" != *.zip ]]; then
    echo "Error: '$1' is not a valid zip file"
    exit 1
fi

# Get parent directory and basename of zip file
PARENT_DIR=$(dirname "$1")
BASE_NAME=$(basename "$1" .zip)
EXTRACT_DIR="$PARENT_DIR/$BASE_NAME"

# Create extraction directory
mkdir -p "$EXTRACT_DIR"

# Extract initial zip file
echo "Extracting initial zip file: $1"
unzip -q "$1" -d "$EXTRACT_DIR"

# Start recursive unzipping on the extracted contents
recursive_unzip "$EXTRACT_DIR"
echo "All zip files have been extracted in: $EXTRACT_DIR"
