# CPD Agent for msbench

This folder contains the msbench runner for CPD. It ties together a pre-built CPD runtime (copied from the sweagentd repository) with a TypeScript-based runner that executes the agent. The following documentation describes how to set up and run the agent and how to integrate it with the msbench-cli command-line tool.

## Quickstart

-   Msbench instructions are available at `aka.ms/msbench/docs`. 

### Using WSL 
-   Clone and build sweagentd in WSL. sweagentd has a dependency on a `node-pty` that requires the same build env as the runtime env. If using WSL, use the ubuntu file system instead of the windows `/mnt` as that is known to be very slow with file read and write. 
    - NOTE: You can target your windows git credential manager like below for WSL or use SSH setup if you are setting up first time.
    ```
    git config --global credential.helper wincred
    git config --global credential.helper "/mnt/c/Program\ Files/Git/mingw64/bin/git-credential-manager.exe"
    ```

### Installing msbench 
You need dotnet runtime, az cli available to run. Codespaces is already configured to use these. 

NOTE: `./setup.sh` is also run as part of the `update_cpd.sh` script so you can skip the install and go directly to run if you want and it will start with install for you.

-   Run `./setup.sh` in the `benchmark/msbench` folder. 
-   If msbench-cli is not already installed, this will install artifact-keyring and install latest msbench-cli. You may need to device login.
-   It will az login. You may need to device login to communicate to CES (Code execution service) that runs the benchmarks.

### Running benchmarks
-   If you make changes in between runs, make sure to run `npm run install` and `npm run build` on the `/runtime` folder of sweagentd.
-   Do `npm install` and `npm run build` in this repo under `cpd/agent_pkg/cpd-msbench-runner` to ensure up-to-date scripts.
-   `VAULT_URI` below needs to have permissions for access via CES via SP. See `aka.ms/msbench/docs` for instructions.

1. **Update the Agent Package**

    `./update_cpd.sh <integration_id> [--copilot-key-vault VAULT_URI] [--copilot-hmac-key KEY] [--agent-model AGENT:MODEL] [--blackbird-mcp-url URL] [--blackbird-index-url URL]`

2. **Update the Agent Package and Run benchmark**

Run with CAPI (default production)
`./1-update-and-run.sh <benchmark> <integration_id> [--copilot-key-vault VAULT_URI] [--copilot-hmac-key KEY] --agent-model sweagent-capi:<modelName> [--tags key1=value1,key2=value2]`

Run with OpenAI 
`./1-update-and-run.sh <benchmark> --url https://<resourceName>.openai.azure.com --copilot-key-vault <keyvault-withapikey> --kv-secret-name <api-key-secret-name> --agent-model sweagent-openai:<modelName> [--tags key1=value1,key2=value2]`

Run with AIP (dogfooding)
`./1-update-and-run.sh <benchmark> <integration_id> [--copilot-key-vault VAULT_URI] [--copilot-hmac-key KEY] [--agent-model AGENT:MODEL] [--tags key1=value1,key2=value2] [--blackbird-mcp-url URL] [--blackbird-index-url URL]`

(Note: if not running, and just updating the package):
 `./update_cpd.sh <integration_id> [--copilot-key-vault VAULT_URI] [--copilot-hmac-key KEY] [--agent-model AGENT:MODEL]`

2. **Resume run and extract results**

These are extracted to `data-<run_id>/**` folder with a `report.json`
`./2-resume-extract-and-report.sh <run_id>`

3. **Extract results**

These are extracted to `data-<run_id>/**` folder with a `report.json`
`./3-extract-and-report.sh <run_id>`

4. **Re-run errored instances**

This will use the `report.json` and start a run using the existing `agent_pkg/cpd` and `cpd.local.settings.yaml` as the previous run and just the errored instances.

`./4-rerun-errored-instances.sh <run_id>`

NOTE: if you don't have the same configuration, you can call below again with same configuration before calling re-run script.

 `./update_cpd.sh <integration_id> [--copilot-key-vault VAULT_URI] [--copilot-hmac-key KEY] [--agent-model AGENT:MODEL]`

By default, the logs will automatically be downloaded. But if you need to disconnect and resume and extract, you can use `2-result-extract-and-report.sh` or `3-extact-and-report.sh` script with this `<run-id>` the same way.

5. **Running secure runs** 

For secure/locked runs, e.g. for running evaluations on tented endpoints or models, MSbench supports a `--lock` capability and running in the secure AME tenant. To use this run with `.\7-secure-update-and-run.sh` script with same parameters and it will lock and run in AME environment. Locking a run will ensure that only the person who started the run can access the run logs and it will not be available for download or access or viewing in the UX to anyone else. If you need to share the logs, the run owner is responsible for sharing and security. 

NOTE: when using secure runs, you cannot use Keyvault support. Pass the HMAC instead directly. MSbench will discard the agent package after the run completes in it's cleanup step in AME. 

### Benchmarks examples to use

`swebench` - 500 instances of swebench.verified
`swebench.sample` - 10 instances from swebench.verified. 1 from each repo in the benchmark.
`swebench.matplotlib__matplotlib-13989` - 1 test instance for sanity testing. Usually always resolves.
`msbench.sample` - 200 instances sampled as easy/medium/hard from across all msbench benchmarks.

Other benchmarks could be found on `aka.ms/msbench/docs` under Benchmark Index.

### Using Blackbird Local Index

By default, the agent leverages a local blackbird index file via a MCP server. You can alter the blackbird behaviour with the following flags

- `--blackbird-storage-account ACCOUNT`: Azure Storage account name where Blackbird files are stored
- `--blackbird-container CONTAINER`: Container name in Azure Storage
- `--blackbird-mcp-blob BLOB_PATH`: Path to the MCP server binary blob within the container
- `--blackbird-index-blob BLOB_PATH`: Path to the index data file blob within the container
- `--blackbird-local-blob BLOB_PATH`: Path to the local binary for Blackbird
- `--disable-blackbird`: Disable all blackbird behaviour

When blackbird is enabled the runner will, 
1. Connect to Azure Storage using DefaultAzureCredential
2. Download the MCP server binary, the local Blackbird Binary and index file (if provied/present)
    - If the index file is not present, one will be generated automatically
3. Make the MCP server binary executable
5. Pass this environment variable to the CPD process

Example:
```bash
./update_cpd.sh <integration_id> --copilot-key-vault VAULT_URI \
  --blackbird-storage-account myblackbirdstorage \
  --blackbird-container files \
  --blackbird-mcp-blob binaries/mcp \
  --blackbird-index-blob data/index.dat
```

Note: The Azure Storage authentication uses DefaultAzureCredential, which supports multiple authentication methods including environment variables, managed identities, and Azure CLI credentials. Make sure you have appropriate credentials configured in your environment.

## TL;DR

### Overview

**Files/folders**

```
cpd/
├── run_agent_config.yaml
├── runner.sh
├── update_cpd.sh (and other scripts)
└── agent_pkg/
     ├── cpd/ (empty)
     └── cpd-msbench-runner/
          ├── package.json
          ├── tsconfig.json
          ├── dist/ (empty)
          └── node_modules/ (empty)
```

| What               | File                  | Description                                                 | Where        |
| ------------------ | --------------------- | ----------------------------------------------------------- | ------------ |
| Configuration      | `run_agent_config.yaml`   | Specifies where the agent package + runner are.             | `.`          |
| Update script      | `update_cpd.sh`       | Prepares the CPD agent package folder for use by the unner. | `.`          |
| Runner script      | `runner.sh`           | Called by `msbench-cli` to invoke the CPD msbench runner.   | `.`          |
| CPD msbench runner | `cpd-msbench-runner/` | Called by `runner.sh` to invoke the CPD runtime.            | `agent_pkg/` |
| CPD runtime        | `cpd/`                | Called by `cpd-msbench-runner/` to invoke the CPD runtime.  | `agent_pkg/` |

## Running `./update_cpd.sh`

The `update_cpd.sh` script is responsible for the following:

1. CPD runtime is built and copied from the sweagentd runtime folder
2. CPD msbench runner is built in-place
3. `cpd.local.settings.yaml` is created in `agent_pkg/cpd`.

After running `./update_cpd.sh`, the folder structure looks like this:

```
cpd/
├── agent_config.yaml
├── runner.sh
├── update_cpd.sh
└── agent_pkg/
     ├── cpd.local.settings.env     (created via `./update_cpd.sh`)
     ├── cpd/                       (created via `./update_cpd.sh`)
     │    ├── package.json          (created via `./update_cpd.sh`)
     │    └── dist/                 (created via `./update_cpd.sh`)
     │         └── ...              (created via `./update_cpd.sh`)
     └── cpd-msbench-runner/
          ├── package.json
          ├── tsconfig.json
          ├── dist/                 (created via `./update_cpd.sh`)
          │    └── ...              (created via `./update_cpd.sh`)
          └── node_modules/         (created via `./update_cpd.sh`)
               └── ...              (created via `./update_cpd.sh`)
```

**New files/folders**
| What | File | Description | Where |
|---|---|---|---|
| CPD local settings | `cpd.local.settings.yaml` | Configuration file for the CPD agent. | `agent_pkg/cpd/` |
| Bundled CPD runtime | `cpd/dist/` | Pure JavaScript | `agent_pkg/` |
| Bundled CPD msbench runner | `cpd-msbench-runner/dist`<br/>`cpd-msbench-runner/node_modules` | Pure JavaScript<br/>NPM dependencies | `agent_pkg/` |

---

### Running `cpd` msbench runner (using `msbench-cli` - see above for In-Built scripts)

Using msbench-cli with the CPD Agent

Once the agent folder is set up and the update script has been executed, you can use msbench-cli as follows:

-   `msbench-cli run --config agent_config.yaml --benchmark <benchmark>`
-   `msbench-cli run --config agent_config.yaml --benchmark swebench.sample`
-   `msbench-cli run --config agent_config.yaml --benchmark swebench`

### Testing the `cpd` msbench runner

To test the `cpd` msbench runner outside of `msbench-cli`, you can run the `runner.sh` script directly.

1.  **Set the Required Environment Variables**

    Make sure that you set the following environment variables before invoking the runner:

    -   `METADATA_PATH`: Path to your metadata file (JSON or YAML) that contains at least the `problem_statement`.
    -   `AGENT_DIR`: Path to the directory where the agent package resides.
    -   `OUTPUT_DIR`: Directory where execution logs and outputs will be stored.

    For example, in a Bash shell you might do:

        export METADATA_PATH=/path/to/metadata.yaml
        export AGENT_DIR=/path/to/this/agent_pkg
        export OUTPUT_DIR=/path/to/output

2.  **Execute the Runner**

    Run the `runner.sh` script to start the process:

        ./runner.sh

    The runner will:

    -   Validate that all required environment variables are set.
    -   Read and parse the metadata file to extract the problem statement.
    -   Call the CPD runner (via Node.js) in the `cpd-msbench-runner/dist` folder.
    -   Pass the problem statement and configuration file (`cpd.local.settings.yaml`) as arguments to the CPD agent.
    -   Write logs to the specified output directory (for example, in `cpd.log` and `cpd-runtime-log.log`).
