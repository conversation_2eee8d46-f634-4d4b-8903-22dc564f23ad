/**
 * Edit Pass@1 Analysis Tool for `aider-code` benchmark.
 * 
 * This script analyzes CPD trajectory files to measure the effectiveness of different
 * editing commands (str_replace, create, insert, undo_edit) by tracking:
 * 
 * Assumptions:
 * - The script assumes that LLM is trying to edit the same content until it succeeds for <command+path> pairs.
 * - This works single file `aider-code` benchmark, but may not be applicable to other benchmarks.
 * 
 * Metrics:
 * - pass1: Count of unique <command+path> changes that succeeded on their first attempt
 * - total: Count of all unique <command+path> changes, regardless of attempts needed
 * - diff: Count of unique <command+path> changes that required more than one attempt
 * 
 * The CSV output contains the following columns for each command type:
 * - <command>_pass1: Count of command+path pairs that succeeded on first attempt
 * - <command>_total: Count of all unique command+path pairs attempted
 * - <command>_diff: Count of command+path pairs that needed multiple attempts
 * - <command>_turnNsum: Sum of edit turn counts for command+path pairs with multiple attempts
 * - <command>_maxturn: Maximum edit turn count for command+path pairs
 * 
 * Additionally, the following sum columns are included:
 * - sum_pass1: Total unique command+path pairs that succeeded on first attempt
 * - sum_total: Total unique command+path pairs attempted across all commands
 * - sum_diff: Total unique command+path pairs that needed multiple attempts
 * - sum_turnNsum: Total sum of edit turn counts across all commands with multiple attempts
 * - sum_maxturn: Maximum edit turn count across all commands
 * 
 * Example CSV row:
 * output/myproject,4,7,3,10,5,2,2,0,4,2,0,1,1,1,3,2,7,13,6,20,7
 * 
 * This means:
 * - str_replace: 4 pairs succeeded first try, 7 total unique pairs, 3 needed retries, 10 total turns for passN cases, 5 max turns
 * - create: 2 pairs succeeded first try, 2 total unique pairs, 0 needed retries, 4 total turns for passN cases, 2 max turns
 * - undo_edit: 0 pairs succeeded first try, 1 total unique pair, 1 needed retries, 2 total turns for passN cases, 2 max turns
 * - insert: 1 pair succeeded first try, 3 total unique pairs, 2 needed retries, 3 total turns for passN cases, 2 max turns
 * - sums: 7 first-try successes, 13 total unique pairs, 6 needed retries, 20 total turns for passN cases, 7 max turns
 * 
 * A higher Pass@1 rate (where diff is low) indicates more efficient editing with
 * fewer retries needed per unique command+path pair.
 * 
 * Usage: 
 *   node edit_pass_at_1.js <run_id>                     # Process a specific run directory
 *   node edit_pass_at_1.js --debug <path>               # Process with verbose output for a single instance
 *   node edit_pass_at_1.js <run_id> --language <lang>   # Process only instances containing the language value
 *   node edit_pass_at_1.js --debug <path> --language <lang>  # Debug with language filter
 */

const fs = require('fs');
const path = require('path');

/**
 * Extracts str_replace_editor function calls and their results from a trajectory file
 * @param {string} filePath - Path to the trajectory markdown file
 */
function extractStrReplaceEditorTurns(filePath) {
  try {
    // Read the file
    const content = fs.readFileSync(filePath, 'utf8');

    // Split by function calls
    const functionCallBlocks = content.split('<function_calls>');

    // Store the turns and track edit counts
    const turns = [];
    let turn_counter = 0;
    const editTurnCounts = new Map();

    for (let i = 1; i < functionCallBlocks.length; i++) {
      const block = functionCallBlocks[i];

      // Check if this is a str_replace_editor call
      if (block.includes('<invoke name="str_replace_editor">')) {
        // Extract the command parameter
        const commandMatch = block.match(/<parameter name="command">([^<]+)<\/parameter>/);
        const command = commandMatch ? commandMatch[1] : 'unknown';

        // Skip "view" commands
        if (command === "view") {
          continue;
        }

        // Extract the path parameter if it exists
        const pathMatch = block.match(/<parameter name="path">([^<]+)<\/parameter>/);
        const pathValue = pathMatch ? pathMatch[1] : 'N/A';

        // Find the corresponding function results
        const resultMatch = block.match(/<\/function_calls>\s*<function_results>([\s\S]*?)<\/function_results>/);
        const result = resultMatch ? resultMatch[1].trim() : 'No result found';

        // Determine success based on command and result
        let isSuccess = determineSuccessStatus(command, result, pathValue);

        // Update edit turn count - start at 1 for new attempts
        const key = `${command}:${pathValue}`;
        editTurnCounts.set(key, (editTurnCounts.get(key) || 0) + 1);

        // Add to turns
        turn_counter++;
        turns.push({
          turn: turn_counter,
          command,
          path: pathValue,
          result,
          success: isSuccess,
          edit_turn_count: editTurnCounts.get(key)
        });

        if (isSuccess) {
          editTurnCounts.delete(key); // Remove from editTurnCounts if successful
        }
      }
    }

    return turns;
  } catch (error) {
    console.error('Error reading or parsing file:', error.message);
    return [];
  }
}

/**
 * Determines if the result indicates success based on command type
 * @param {string} command - The command that was executed
 * @param {string} result - The result string from the function
 * @param {string} path - The path parameter value
 * @returns {boolean} - true for success, false for failure
 */
function determineSuccessStatus(command, result, path) {
  if (!result) return false;

  switch (command) {
    case "str_replace":
      // Check for successful str_replace messages
      if (result.includes(`File ${path} updated with changes`) ||
        result.includes("updated with changes")) {
        return true;
      }
      return false;

    case "create":
      // Check for successful create message
      if (result.includes(`Created file ${path}`) ||
        result.includes("Created file") && result.includes("characters")) {
        return true;
      }
      return false;

    case "insert":
      // Check for successful insert message
      if (result.includes(`Inserted`) &&
        result.includes(`characters at line`) &&
        result.includes(path)) {
        return true;
      }
      return false;

    case "undo_edit":
      // Check for successful undo message
      if (result.includes(`Reverted last edit made to ${path}`) ||
        result.includes("Reverted last edit made to")) {
        return true;
      }
      return false;

    default:
      return false;
  }
}

/**
 * Recursively finds all cpd-trajectory.md files under the given directory
 * @param {string} dir - Directory to search
 * @returns {string[]} - Array of file paths
 */
function findTrajectoryFiles(dir) {
  let results = [];
  const files = fs.readdirSync(dir);

  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      results = results.concat(findTrajectoryFiles(filePath));
    } else if (file === 'cpd-trajectory.md') {
      results.push(filePath);
    }
  }

  return results;
}

/**
 * Main function to run the script
 */
function main() {
  // Parse arguments
  const args = process.argv.slice(2);
  let folderPath;
  let verbose = false;
  let language = null;

  // Check for language parameter
  const langIndex = args.indexOf('--language');
  if (langIndex !== -1 && langIndex + 1 < args.length) {
    language = args[langIndex + 1];
    // Remove the language args
    args.splice(langIndex, 2);
  }

  if (args[0] === '--debug') {
    if (!args[1]) {
      console.error('Please provide a folder path with --debug');
      process.exit(1);
    }
    folderPath = args[1];
    verbose = true;
  } else {
    // Assume run_id
    const run_id = args[0];
    if (!run_id) {
      console.error('Please provide either a run_id or use --debug <folder_path>');
      process.exit(1);
    }
    folderPath = path.join(process.cwd(), `data-${run_id}`);
  }

  const baseDir = path.resolve(folderPath);
  if (!fs.existsSync(baseDir)) {
    console.error(`Directory ${baseDir} not found`);
    process.exit(1);
  }

  // Find all trajectory files
  let trajectoryFiles = findTrajectoryFiles(baseDir);

  // Filter by language if specified
  if (language) {
    trajectoryFiles = trajectoryFiles.filter(filePath => {
      return filePath.toLowerCase().includes(language.toLowerCase());
    });

    if (verbose) {
      console.log(`Filtering for language: ${language}`);
    }
  }

  if (verbose) {
    console.log(`Found ${trajectoryFiles.length} trajectory files${language ? ` containing "${language}"` : ''}`);
  }

  // Process each file and collect results
  const results = [];

  for (const filePath of trajectoryFiles) {
    console.log(`Processing file: ${filePath}`);
    const turns = extractStrReplaceEditorTurns(filePath);
    const folderPath = path.relative(baseDir, path.dirname(filePath));

    if (verbose) {
      console.log(`\nTurns for ${folderPath}:`);
      turns.forEach(turn => {
        console.log(`Turn ${turn.turn}: ${turn.command} ${turn.path}`);
        console.log(`  Success: ${turn.success}, Attempt: ${turn.edit_turn_count}`);
        console.log(`  Result: ${turn.result}\n`);
      });
    }

    const commandStats = {
      str_replace: { pass1: 0, total: 0, passNTurnSum: 0, maxTurnCount: 0 },
      create: { pass1: 0, total: 0, passNTurnSum: 0, maxTurnCount: 0 },
      undo_edit: { pass1: 0, total: 0, passNTurnSum: 0, maxTurnCount: 0 },
      insert: { pass1: 0, total: 0, passNTurnSum: 0, maxTurnCount: 0 }
    };

    const processedPaths = new Map();

    turns.forEach(turn => {
      const key = `${turn.command}:${turn.path}`;

      // Track total successes (unique command+path pairs that ever succeeded)
      if (!processedPaths.has(key)) {
        processedPaths.set(key);
      }

      processedPaths.set(key, turn);

      if (turn.success) {
        if (commandStats.hasOwnProperty(turn.command)) {
          commandStats[turn.command].total++;
          processedPaths.delete(key); // Remove from processedPaths if successful

          if (turn.edit_turn_count > 1) {
            commandStats[turn.command].passNTurnSum += turn.edit_turn_count;
            commandStats[turn.command].maxTurnCount = Math.max(commandStats[turn.command].maxTurnCount, turn.edit_turn_count);
          } else if (turn.edit_turn_count === 1) {
            // Track pass@1 (first attempt successes)
            commandStats[turn.command].pass1++;
          }
        }
      }
    });

    // add any remaining processed paths to total counts
    processedPaths.forEach((_, key) => {
      const [command, path] = key.split(':');
      if (commandStats.hasOwnProperty(command)) {
        commandStats[command].total++;
        commandStats[command].passNTurnSum += processedPaths.get(key).edit_turn_count;
        commandStats[command].maxTurnCount = Math.max(commandStats[command].maxTurnCount, processedPaths.get(key).edit_turn_count);
      }
    });

    results.push({
      folderPath,
      ...commandStats
    });
  }

  if (verbose) {
    console.log('\nFinal results:');
    console.log(results);
  } else {
    // Write results to CSV only in non-debug mode
    const csvPath = path.join(baseDir, language ? `edit_pass_at_1_${language}.csv` : 'edit_pass_at_1.csv');
    const header = 'instance_run,' +
      'str_replace_pass1,str_replace_total,str_replace_diff,str_replace_turnNsum,str_replace_maxturn,' +
      'create_pass1,create_total,create_diff,create_turnNsum,create_maxturn,' +
      'undo_edit_pass1,undo_edit_total,undo_edit_diff,undo_edit_turnNsum,undo_edit_maxturn,' +
      'insert_pass1,insert_total,insert_diff,insert_turnNsum,insert_maxturn,' +
      'sum_pass1,sum_total,sum_diff,sum_turnNsum,sum_maxturn\n';

    let total_pass1 = 0;
    let total_pairs = 0;
    let total_diff = 0;
    let total_turnNsum = 0;
    let overall_max_turn = 0;

    const csvContent = results.map(r => {
      const sum_pass1 = r.str_replace.pass1 + r.create.pass1 + r.undo_edit.pass1 + r.insert.pass1;
      const sum_total = r.str_replace.total + r.create.total + r.undo_edit.total + r.insert.total;
      const sum_diff = sum_total - sum_pass1;
      const sum_turnNsum = r.str_replace.passNTurnSum + r.create.passNTurnSum + r.undo_edit.passNTurnSum + r.insert.passNTurnSum;
      const sum_maxturn = Math.max(r.str_replace.maxTurnCount, r.create.maxTurnCount, r.undo_edit.maxTurnCount, r.insert.maxTurnCount);

      total_pass1 += sum_pass1;
      total_pairs += sum_total;
      total_diff += sum_diff;
      total_turnNsum += sum_turnNsum;
      overall_max_turn = Math.max(overall_max_turn, sum_maxturn);

      // Trim the folder name
      const trimmedFolder = r.folderPath.replace(/^.*\.eval\.x86_64\./, '').replace(/-output.*$/, '');

      return `${trimmedFolder},` +
        `${r.str_replace.pass1},${r.str_replace.total},${r.str_replace.total - r.str_replace.pass1},${r.str_replace.passNTurnSum},${r.str_replace.maxTurnCount},` +
        `${r.create.pass1},${r.create.total},${r.create.total - r.create.pass1},${r.create.passNTurnSum},${r.create.maxTurnCount},` +
        `${r.undo_edit.pass1},${r.undo_edit.total},${r.undo_edit.total - r.undo_edit.pass1},${r.undo_edit.passNTurnSum},${r.undo_edit.maxTurnCount},` +
        `${r.insert.pass1},${r.insert.total},${r.insert.total - r.insert.pass1},${r.insert.passNTurnSum},${r.insert.maxTurnCount},` +
        `${sum_pass1},${sum_total},${sum_diff},${sum_turnNsum},${sum_maxturn}`;
    }).join('\n');

    fs.writeFileSync(csvPath, header + csvContent);

    // Create summary table
    const summaryTable = [{
      "Metric": "Files processed",
      "Value": trajectoryFiles.length
    }, {
      "Metric": "Total command+path",
      "Value": total_pairs
    }, {
      "Metric": "Total pass@1",
      "Value": total_pass1
    }, {
      "Metric": "Pass@1 metric",
      "Value": (total_pass1 / total_pairs).toFixed(5)
    }, {
      "Metric": "Average retry attempts",
      "Value": (total_turnNsum / total_diff).toFixed(4)
    }, {
      "Metric": "Max turns",
      "Value": overall_max_turn
    }, {
      "Metric": "Language filter",
      "Value": language || "none"
    }];

    // Output table to console
    console.group('\nSummary Statistics:');
    console.table(summaryTable);
    console.groupEnd();

    console.log('');
    // Write text version to file
    const summaryText = summaryTable
      .map(row => `${row.Metric}: ${row.Value}`)
      .join('\n');

    const summaryPath = path.join(baseDir, language ? `summary_edit_pass_at_1_${language}.txt` : 'summary_edit_pass_at_1.txt');
    fs.writeFileSync(summaryPath, summaryText);

    console.log(`Summary written to ${summaryPath}\n`);
    console.log(`CSV file written to ${csvPath}`);
  }
}

// Run the script
main();