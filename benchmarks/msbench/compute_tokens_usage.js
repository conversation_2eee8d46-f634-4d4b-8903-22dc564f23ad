/**
 * Token Usage Analysis Script
 * 
 * This script analyzes "cpd-runtime-log.log" files to extract token usage information
 * and outputs the results to a CSV file.
 * 
 * Usage:
 *   node compute_tokens_usage.js <run_id>
 * 
 * Output:
 *   The script produces a CSV file named "token_usage.csv" in the data-<run_id> folder 
 *   with the following columns:
 * 
 *   - instance_token: The benchmark instance name (e.g. "astropy__astropy-12907")
 *   - requests_count: Number of requests made to the AI model
 *   - total_tokens: Total number of tokens used in all requests
 *   - prompt_tokens: Total number of tokens used in prompts
 *   - completion_tokens: Total number of tokens in completions
 *   - cached_tokens: Total number of tokens that were cached during processing
 *   - reasoning_tokens: Total number of tokens used for reasoning
 *   - truncation_count: Number of times truncation occurred
 *   - tokens_removed: Total number of tokens removed during truncations
 *   - messages_removed: Total number of messages removed during truncations
 *   - agent_execution_time_seconds: Total execution time in seconds
 * 
 * Example output:
 *   instance_token,requests_count,total_tokens,prompt_tokens,completion_tokens,cached_tokens,reasoning_tokens,truncation_count,tokens_removed,messages_removed,agent_execution_time_seconds
 *   accumulate,7,14620,11229,1005,9984,448,0,0,0,30.412
 *   astropy__astropy-12907,12,25460,19874,2630,15872,1024,2,4096,3,145.782
 *   django__django-14671,15,32784,25688,3521,20480,2048,4,8192,6,278.541
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Parse command line arguments
const args = process.argv.slice(2);
if (args.length < 1) {
  console.error('Usage: node compute_tokens_usage.js <run_id>');
  process.exit(1);
}
const runId = args[0];

// Define the directory to search
const searchDir = `./data-${runId}`;

// Check if directory exists
if (!fs.existsSync(searchDir)) {
  console.error(`Directory ${searchDir} not found`);
  process.exit(1);
}

// Function to recursively find log files
function findLogFiles(dir) {
  const results = [];
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const fullPath = path.join(dir, file);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      results.push(...findLogFiles(fullPath));
    } else if (file === 'cpd-runtime-log.log') {
      results.push(fullPath);
    }
  }
  
  return results;
}

// Function to extract usage information from a log file
async function extractUsageInfo(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Initialize counters for token types
    const usageInfo = {
      requests_count: 0,
      total_tokens: 0,
      reasoning_tokens: 0,
      completion_tokens: 0,
      prompt_tokens: 0,
      cached_tokens: 0,
      truncation_count: 0,
      tokens_removed: 0,
      messages_removed: 0,
      agent_execution_time: 0 // New field for execution time
    };
    
    // Extract timestamps to calculate execution time
    const timestampPattern = /(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z)/g;
    const timestamps = content.match(timestampPattern);
    
    if (timestamps && timestamps.length >= 2) {
      // Get first and last timestamp
      const startTime = new Date(timestamps[0]);
      const endTime = new Date(timestamps[timestamps.length - 1]);
      
      // Calculate the difference in seconds
      usageInfo.agent_execution_time = (endTime - startTime) / 1000;
    }
    
    // Use regex to find all usage objects in the log file
    const usagePattern = /"usage": \{[\s\S]*?(?:total_tokens|input_tokens)": (\d+)[\s\S]*?\}/g;
    const matches = content.matchAll(usagePattern);
    
    for (const match of matches) {
      // Count each usage object as a request
      usageInfo.requests_count++;
      
      const usageBlock = match[0];
      
      try {
        // Extract just the usage object and parse it
        const jsonStartIndex = usageBlock.indexOf('"usage":');
        const jsonString = '{' + usageBlock.substring(jsonStartIndex) + '}';
        
        // Clean up potential trailing commas or other JSON issues
        const cleanedJson = jsonString
          .replace(/,\s*}/g, '}')
          .replace(/,\s*\n\s*}/g, '}');
        
        const usageObj = JSON.parse(cleanedJson);
        
        // Add to our running totals
        const completionTokens = (usageObj.usage.completion_tokens ?? usageObj.usage.output_tokens) || 0;
        usageInfo.completion_tokens += completionTokens;
        const promptTokens = (usageObj.usage.prompt_tokens ?? usageObj.usage.input_tokens) || 0;
        usageInfo.prompt_tokens += promptTokens;
        usageInfo.total_tokens += (usageObj.usage.total_tokens ?? completionTokens + promptTokens) || 0;
        
        // Handle detailed token information if available
        if (usageObj.usage.completion_tokens_details) {
          usageInfo.reasoning_tokens += usageObj.usage.completion_tokens_details.reasoning_tokens || 0;
        }
        
        if (usageObj.usage.prompt_tokens_details) {
          usageInfo.cached_tokens += usageObj.usage.prompt_tokens_details.cached_tokens || 0;
        } else if (usageObj.usage.cache_read_input_tokens) {
          usageInfo.cached_tokens += usageObj.usage.cache_read_input_tokens || 0;
        }

      } catch (e) {
        console.error(`Error parsing usage JSON block in ${filePath}: ${e.message}`);
      }
    }
    
    // Count truncation instances and sum tokens/messages removed
    const truncationPattern = /\[DEBUG\] Removed (\d+) tokens and (\d+) messages during truncation/g;
    const truncationMatches = content.matchAll(truncationPattern);
    
    for (const truncMatch of truncationMatches) {
      const tokensRemoved = parseInt(truncMatch[1], 10);
      const messagesRemoved = parseInt(truncMatch[2], 10);
      
      // Only count instances where tokens or messages were actually removed
      if (tokensRemoved > 0 || messagesRemoved > 0) {
        usageInfo.truncation_count++;
        usageInfo.tokens_removed += tokensRemoved;
        usageInfo.messages_removed += messagesRemoved;
      }
    }
    
    return usageInfo;
  } catch (err) {
    console.error(`Error reading file ${filePath}: ${err.message}`);
    return {
      requests_count: 0,
      total_tokens: 0,
      reasoning_tokens: 0,
      completion_tokens: 0,
      prompt_tokens: 0,
      cached_tokens: 0,
      truncation_count: 0,
      tokens_removed: 0,
      messages_removed: 0,
      agent_execution_time: 0 // Include default value for new field
    };
  }
}

// Main function
async function main() {
  const logFiles = findLogFiles(searchDir);
  console.log(`Found ${logFiles.length} log files to process`);
  
  const results = [];
  
  for (const file of logFiles) {
    try {
      // Get relative path from search directory
      let relativePath = path.dirname(path.relative(searchDir, file));
      
      // Trim the leading "sweb.eval.x86_64." and trailing "-output/output" from folder names
      relativePath = relativePath.replace(/^.*\.eval\.x86_64\./, '').replace(/-output.*$/, '');
      const usageInfo = await extractUsageInfo(file);
      
      results.push({
        path: relativePath,
        ...usageInfo
      });
    } catch (err) {
      console.error(`Error processing ${file}: ${err.message}`);
    }
  }
  
  // Generate CSV with reordered columns and updated column names (with time unit)
  const csvHeader = 'instance_token,requests_count,total_tokens,prompt_tokens,completion_tokens,cached_tokens,reasoning_tokens,truncation_count,tokens_removed,messages_removed,agent_execution_time_seconds';
  const csvRows = results.map(r => 
    `${r.path},${r.requests_count},${r.total_tokens},${r.prompt_tokens},${r.completion_tokens},${r.cached_tokens},${r.reasoning_tokens},${r.truncation_count},${r.tokens_removed},${r.messages_removed},${r.agent_execution_time}`
  );
  
  const csvContent = [csvHeader, ...csvRows].join('\n');
  
  // Write to file in the data-{runId} folder 
  const outputPath = path.join(searchDir, `token_usage.csv`);
  fs.writeFileSync(outputPath, csvContent);
  
  console.log(`Results written to ${outputPath}`);
}

main().catch(err => {
  console.error('An error occurred:', err);
  process.exit(1);
});
