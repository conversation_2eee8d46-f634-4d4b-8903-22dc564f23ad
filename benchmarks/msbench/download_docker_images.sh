#!/bin/bash
#------------------------------------------------------------------------------
# Download Docker images for benchmark instances
#
# This script:
# 1. Lists all instances for a specified benchmark
# 2. Pulls the corresponding Docker images from the container registry
#
# PREREQUISITES:
# - Requires msbench-cli to be installed and configured
# - Requires Azure CLI with access to the Container Registry
# - Requires Docker to be installed and running
#
# USAGE:
#   ./download_docker_images.sh <benchmark> [start_index]
#------------------------------------------------------------------------------
set -e

# Check if benchmark parameter is provided
if [ -z "$1" ]; then
  echo "Usage: $0 <benchmark> [start_index]"
  exit 1
fi

# Get the benchmark name
benchmark=$1

# Get the starting index if provided, default to 0
start_index=0
if [ -n "$2" ]; then
  start_index=$2-1
fi

# Login to Azure Container Registry
echo "Logging in to Azure Container Registry..."
az acr login --name codeexecservice

# Get list of all instances in CSV format
echo "Listing all instances for benchmark: $benchmark..."
instances=$(msbench-cli list_instances --benchmark $benchmark --format csv)

# Fix: Store instances in a temporary file to avoid subshell issues
temp_file=$(mktemp)
# Only extract non-empty rows that don't contain "Benchmark,Instance,Image" (header rows)
# and filter out log messages that start with date/time in brackets
echo "$instances" | grep -v "^$" | grep -v "Benchmark,Instance,Image" | grep -v "^\[[0-9]" > "$temp_file"

# Initialize array
image_names=()

# Process each line of the CSV - only data rows remain in the temp file
echo "Extracting image names..."
while IFS=, read -r bench instance image; do
    image_names+=("codeexecservice.azurecr.io/$image")
done < "$temp_file"

rm "$temp_file"  # Clean up temp file

# Calculate total number of images
total_images=${#image_names[@]}
echo "Found $total_images images to download"

# Check if start_index is valid
if [ $start_index -ge $total_images ]; then
  echo "Error: Starting index ($start_index) is out of range. Total images: $total_images"
  exit 1
fi

echo "Starting download from index $start_index (image $((start_index+1)) of $total_images)"

# Pull Docker images
echo "Pulling Docker images..."

for ((i=start_index; i<total_images; i++))
do
    image="${image_names[$i]}"
    echo "[$((i+1))/$total_images] Processing image: $image"
    
    # Check if image already exists locally
    if docker image inspect "$image" &>/dev/null; then
        echo "  Image already exists locally, skipping download"
    else
        echo "  Pulling image: $image"
        docker pull "$image"
    fi
done

echo "All images processed successfully."
