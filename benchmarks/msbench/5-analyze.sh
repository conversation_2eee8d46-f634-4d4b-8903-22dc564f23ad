#!/bin/bash

# Tool Usage Analysis Script for CPD Trajectory Files
#
# This script serves as a wrapper to call four specialized Node.js analysis scripts:
# - collect_corruption_stats.js: Analyzes file corruption statistics
# - compute_tokens_usage.js: Analyzes token usage across runs
# - get_tool_stats.js: Collects tool usage statistics 
# - collect_online_eval_metrics.js: Collects online evaluation metrics
#
# Usage: ./5-analyze.sh <run_id>
#   where <run_id> is the numeric identifier of the run (e.g., 14525455148)

if [ $# -ne 1 ]; then
    echo "Usage: $0 <run_id>"
    exit 1
fi

RUN_ID=$1
echo "Starting analysis for run ID: ${RUN_ID}"

# Run the Node.js scripts with the provided run ID
echo "Running collect_corruption_stats.js..."
node collect_corruption_stats.js "${RUN_ID}" || { echo "Error running collect_corruption_stats.js"; exit 1; }

echo "Running compute_tokens_usage.js..."
node compute_tokens_usage.js "${RUN_ID}" || { echo "Error running compute_tokens_usage.js"; exit 1; }

echo "Running get_tool_stats.js..."
node get_tool_stats.js "${RUN_ID}" || { echo "Error running get_tool_stats.js"; exit 1; }

# echo "Running collect_online_eval_metrics.js..."
# node collect_online_eval_metrics.js "${RUN_ID}" || { echo "Error running collect_online_eval_metrics.js"; exit 1; }

echo "Analysis complete. All scripts executed successfully."
