/**
 * msbench-trajectory-converter.js
 * 
 * Converts Padawan markdown trajectory files to MSBench Trajectories JSON format
 * 
 * Usage: node msbench-trajectory-converter.js [PATH_TO_PADAWAN_TRAJECTORY_FILE].md $OUTPUT_DIR/trajectories/trajectory.json
 * This will output a JSON file with the proper trajectory schema at $OUTPUT_DIR/trajectories/trajectory.json.
 */

const fs = require('fs');
const path = require('path');

/**
 * Converts a markdown trajectory file to JSON format
 * @param {string} mdFilePath - Path to the markdown file
 * @param {string} jsonFilePath - Path to output the JSON file
 * @param {Object} options - Additional options for conversion
 */
function convertMdToJson(mdFilePath, jsonFilePath, options = {}) {
  const { 
    defaultInputTokens = 0, 
    defaultOutputTokens = 0 
  } = options;
  
  // Read the markdown file
  const mdContent = fs.readFileSync(mdFilePath, 'utf8');
  
  // Split the content by function calls blocks to identify steps
  const steps = [];
  const regex = /<function_calls>([\s\S]*?)<\/function_calls>[\s\S]*?<function_results>([\s\S]*?)<\/function_results>/g;
  
  // Track thoughts that appear before function calls
  let currentThought = null;
  let remainingContent = mdContent;
  
  // Match function calls and their results
  let match;
  let stepCount = 0;
  while ((match = regex.exec(mdContent)) !== null) {
    const fullMatch = match[0];
    const functionCallsBlock = match[1].trim();
    const functionResultsBlock = match[2].trim();
    
    // Extract thought that might appear before this function call
    if (remainingContent.indexOf(fullMatch) > 0) {
      const precedingContent = remainingContent.substring(0, remainingContent.indexOf(fullMatch)).trim();
      
      if (precedingContent && !precedingContent.endsWith('</function_results>')) {
        currentThought = precedingContent;
      }
    }
    remainingContent = remainingContent.substring(remainingContent.indexOf(fullMatch) + fullMatch.length);
    
    // Parse the function call to extract tool name and parameters
    const toolRegex = /<invoke name="([^"]+)">/i;
    const toolMatch = toolRegex.exec(functionCallsBlock);
    
    if (toolMatch) {
      const toolName = toolMatch[1];
      
      // Parse parameters
      const paramRegex = /<parameter name="([^"]+)">([\s\S]*?)<\/parameter>/g;
      const params = {};
      let paramMatch;
      
      while ((paramMatch = paramRegex.exec(functionCallsBlock)) !== null) {
        params[paramMatch[1]] = paramMatch[2].trim();
      }
      
      // Extract command for specific tools like str_replace_editor
      let toolNameWithCommand = toolName;
      if (toolName === 'str_replace_editor' && params['command']) {
        toolNameWithCommand = `${toolName}.${params['command']}`;
      }
      
      // Build the action string
      let actionString = "";
      actionString = `${toolName}(${JSON.stringify(params)})`;
    
      // Create the step object
      const step = {
        tool: toolNameWithCommand,
        input_tokens: defaultInputTokens,
        output_tokens: defaultOutputTokens,
        action: actionString,
        observation: functionResultsBlock,
        response: "", // Keep empty if not providing any additional information
        state: "{}", // State of the tool (if the tool is stateless, then {})
        thought: currentThought || null // Use the thought that preceded this function call or null
      };
      
      steps.push(step);
      stepCount++;
      
      // Reset thought after adding to a step
      currentThought = null;
    }
  }
  
  // Clean up state field to ensure it's valid JSON
  steps.forEach(step => {
    // Try to parse the state as JSON if it's not already an object
    if (typeof step.state === 'string' && step.state !== '{}') {
      try {
        const parsedState = JSON.parse(step.state);
        step.state = JSON.stringify(parsedState);
      } catch (e) {
        // If parsing fails, keeping the original string
        console.log(`Failed to parse state for step. Keeping original string.`);
      }
    }
    
    // Make sure thought is null if empty
    if (!step.thought) {
      step.thought = null;
    }
  });
  
  // Write the JSON file 
  const jsonContent = JSON.stringify(steps, null, 2);
  fs.writeFileSync(jsonFilePath, jsonContent);
  
  console.log(`Converted ${mdFilePath} to ${jsonFilePath}`);
  console.log(`Total steps: ${stepCount}`);
  return steps;
}

/**
 * Main function to parse command line arguments and run the converter
 */
function main() {
  if (process.argv.length < 4) {
    console.log('Usage: node msbench-trajectory-converter.js <input-md-file> <output-json-file>');
    process.exit(1);
  }
  
  const mdFilePath = process.argv[2];
  const jsonFilePath = process.argv[3];
  
  try {
    convertMdToJson(mdFilePath, jsonFilePath);
  } catch (error) {
    console.error('Error converting markdown to JSON:', error);
    process.exit(1);
  }
}

// Run the script if executed directly
if (require.main === module) {
  main();
}

module.exports = { convertMdToJson };