import { ChildProcessWithoutNullStreams, spawn } from 'child_process';
import * as fs from 'fs';
import * as yaml from 'js-yaml';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { BlackbirdDownloader, BlackbirdDownloadResult, extractBlackbirdConfig, hasValidBlackbirdConfig } from './blackbirdDownloader';
import { fixTemplate } from './templates';

const exclude_file_regexes = [
  /\.git/,
  /node_modules/,
  /dist/,
  /build/,
  /\.gz$/,
  /\.zip$/,
  /\.tar$/,
  /\.tar\.gz$/,
  /\.tgz$/,
  /\.rar$/,
  /\.7z$/,
  /\.tar\.xz$/,
  /\.tar\.bz2$/,
  /\.tar\.lzma$/,
  /\.tar\.lzo$/,
  /\.tar\.zst$/,
  /\.tar\.sz$/,
  /\.fits$/
];

interface Metadata {
  problem_statement: string;
  instance_id: string;
  [key: string]: any;
}

async function setupBlackbirdFiles(agentDir: string, repoDir: string, instanceId: string): Promise<string | null> {
  if (!hasValidBlackbirdConfig()) {
    console.log('No valid Blackbird configuration found, skipping file downloads.');
    return null;
  }

  console.log('Blackbird configuration found, downloading required files from Azure Storage...');

  const storageConfig = extractBlackbirdConfig();
  if (!storageConfig) {
    console.error('Failed to extract Blackbird configuration.');
    return null;
  }

  try {
    const blackbirdDir = path.join(agentDir, 'blackbird');
    const downloader = new BlackbirdDownloader(storageConfig, blackbirdDir, repoDir, instanceId);

    let result: BlackbirdDownloadResult | undefined;
    for (const attempt of [1, 2, 3]) {
      result = await downloader.downloadFiles();
      if (result.success) {
        break;
      }
      console.error(`[attempt ${attempt}]: Failed to download Blackbird files: ${result.error}`);
      await new Promise((resolve) => setTimeout(resolve, 5000));
    }

    if (!result) {
      console.error('Failed to download Blackbird files after multiple attempts.');
      return null;
    }

    console.log('Blackbird files downloaded and configured successfully.');
    return result.indexPath || null;
  } catch (error: any) {
    console.error(`Error setting up Blackbird files: ${error.message}`);
    return null;
  }
}

function parseMetadataFile(filePath: string): Metadata {
  try {
    const fileContents = fs.readFileSync(filePath, 'utf8');
    const extension = path.extname(filePath).toLowerCase();

    let data: Metadata;
    if (extension === '.json') {
      data = JSON.parse(fileContents);
    } else if (extension === '.yaml' || extension === '.yml') {
      data = yaml.load(fileContents) as Metadata;
    } else {
      throw new Error(`Unsupported file type: ${extension}`);
    }

    if (!data.problem_statement) {
      throw new Error('problem_statement not found in metadata file');
    }

    return data;
  } catch (error: any) {
    throw new Error(`Error parsing metadata file: ${error.message}`);
  }
}

async function runMCP(agentDir: string): Promise<ChildProcessWithoutNullStreams> {
  console.log('Running MCP Server...');
  const args = [
    path.join(agentDir, 'mcp-client/dist-mcp-client/index.js')
  ];
  console.log(`Running command: node ${args.join(' ')}`);
  const mcpProcess = spawn('node', args, {
    env: {
      'GITHUB_COPILOT_MCP_JSON': '{"mcpServers": {}}',
      'PATH': process.env.PATH,
      ...process.env, // Pass through existing environment variables
    },
    detached: true,
  });
  mcpProcess.on('close', (code) => {
    if (code === 0) {
      console.log('MCP Server started successfully');
    } else {
      console.error(`MCP Server exited with code ${code}`);
    }
  });

  let mcpServerReady = false;

  mcpProcess.on('error', (err) => {
    console.error(`Failed to start MCP process: ${err.message}`);
  });
  mcpProcess.stdout.on('data', (data) => {
    const output = data.toString();
    if (output.includes('All tools retrieved')) {
      mcpServerReady = true;
      console.log('MCP Server is ready');
    }
    console.log(`MCP Server: ${data}`);
  });
  mcpProcess.stderr.on('data', (data) => {
    console.error(`MCP Server Error: ${data}`);
  });

  const maxRetries = 30;
  let retries = 0;
  while (!mcpServerReady && retries < maxRetries) {
    console.log(`Waiting for MCP Server to be ready...${retries + 1}/${maxRetries} attempts`);
    // Wait for a short period before checking again
    await new Promise(resolve => setTimeout(resolve, 1000));

  }
  return mcpProcess;
}

function runCPD(problemStatement: string, repoDir: string, outputDir: string, agentDir: string, branchName: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const logStream = fs.createWriteStream(`${outputDir}/cpd.log`);
    const instanceId = uuidv4();
    console.log('Running cpd with the problem statement...');

    const args = [
      '--enable-source-maps',
      `--env-file=${path.join(agentDir, 'cpd.local.settings.env')}`,
      path.join(agentDir, 'cpd/dist/index.js'),
      'fix',
      '--log',
      path.join(outputDir, 'cpd-runtime-log.log'),
      '--github-repo-directory',
      repoDir
    ];
    console.log(`Running command: node ${args.join(' ')}`);

    let timeoutMs = 55 * 60 * 1000; // Default 55 minutes
    if (process.env.RUNNER_NO_TIMEOUT === 'true') {
      timeoutMs = 0;
    }

    const extraEnvVars: NodeJS.ProcessEnv = {
      [`COPILOT_AGENT_JOB_ID`]: instanceId,
      [`COPILOT_AGENT_BRANCH_NAME`]: branchName,
      [`COPILOT_AGENT_PROMPT`]: problemStatement,
      [`CPD_SAVE_TRAJECTORY_OUTPUT`]: path.join(outputDir, 'cpd-trajectory.md'),
      [`COPILOT_AGENT_TIMEOUT_MIN`]: timeoutMs > 0 ? Math.floor(timeoutMs / 60000).toString() : undefined,
      [`COPILOT_AGENT_START_TIME_SEC`]: Math.floor(Date.now() / 1000).toString(),
    };
    console.log(`Extra environment variables: ${JSON.stringify(extraEnvVars)}`);

    const cpdProcess = spawn('node', args, {
      env: {
        ...process.env,
        ...extraEnvVars,
      }
    });

    // Set timeout if timeoutMs > 0
    console.log(`CPD timeout is set to ${timeoutMs > 0 ? `${timeoutMs / 60000} minutes` : 'disabled'}`);
    const timeout = timeoutMs > 0 ? setTimeout(() => {
      console.log(`CPD process timed out after ${timeoutMs / 60000} minutes. Terminating...`);
      logStream.write(`\nProcess timed out after ${timeoutMs / 60000} minutes and was terminated.\n`);
      cpdProcess.kill('SIGKILL');
      reject(new Error('CPD process timed out after 55 minutes'));
    }, timeoutMs) : null;

    cpdProcess.stdout.pipe(logStream);
    cpdProcess.stderr.pipe(logStream);

    cpdProcess.on('close', (code) => {
      // Clear the timeout if it exists
      if (timeout) clearTimeout(timeout);

      if (code === 0) {
        console.log('CPD execution completed successfully');

        // if the /tmp/<instanceId>/onlineEvaluationResults.json exists, copy to outputDir
        const onlineEvalFile = path.join(`/tmp/online_evaluation_${instanceId}`, 'onlineEvaluationResults.json');
        if (fs.existsSync(onlineEvalFile)) {
          const destFile = path.join(outputDir, 'onlineEvaluationResults.json');
          fs.copyFileSync(onlineEvalFile, destFile);
          console.log(`Online evaluation results copied to ${destFile}`);
        } else {
          console.log(`No online evaluation results found at ${onlineEvalFile}`);
        }

        resolve();
      } else {
        reject(new Error(`CPD process exited with code ${code}`));
      }
    });

    cpdProcess.on('error', (err) => {
      // Clear the timeout as the process has errored
      if (timeout) clearTimeout(timeout);
      reject(new Error(`Failed to start CPD process: ${err.message}`));
    });
  });
}

function file_is_text_file(filePath: string): boolean {
  try {
    const data = fs.readFileSync(filePath);
    for (let i = 0; i < data.length && i < 1024; i++) {
      if (data[i] === 0) {
        return false;
      }
    }
    return true;
  } catch (err: any) {
    console.error(`Error reading file: ${err.message}`);
    return false;
  }
}

function create_deep_backup(sourceDir: string, destDir: string): void {
  const files = fs.readdirSync(sourceDir, { withFileTypes: true });
  for (const file of files) {
    const filePath = path.join(sourceDir, file.name);
    const destPath = path.join(destDir, file.name);

    if (exclude_file_regexes.some((regex) => regex.test(filePath))) {
      continue;
    }

    if (file.isDirectory()) {
      if (!fs.existsSync(destPath)) {
        fs.mkdirSync(destPath, { recursive: true });
      }
      create_deep_backup(filePath, destPath);
    } else if (file_is_text_file(filePath)) {
      try {
        fs.mkdirSync(destDir, { recursive: true });
        fs.copyFileSync(filePath, destPath);
      } catch (err: any) {
        console.error(`Error copying file: ${err.message}`);
      }
    }
  }
}

function create_deep_snapshot(sourceDir: string, outputDir: string, suffix: string): void {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const destDir = path.join(outputDir, `${timestamp}-${suffix}`);
  create_deep_backup(sourceDir, destDir);
  console.log(`Created snapshot: ${destDir}`);
}

function create_deep_snapshot_no_throw(sourceDir: string, outputDir: string, suffix: string): void {
  try {
    create_deep_snapshot(sourceDir, outputDir, suffix);
  } catch (error: any) {
    console.error(`Error creating snapshot: ${error.message}`);
  }
}

async function main() {
  const repoDir = process.env.TESTBED_DIR;
  const metadataPath = process.env.METADATA_PATH;
  const outputDir = process.env.OUTPUT_DIR;
  const agentDir = process.env.AGENT_DIR;
  const branchName = process.env.BRANCH_NAME;
  const deepCopy = process.argv.includes('--deep-copy');
  const runnerSettingsPath = path.join(agentDir!, 'runner.settings.env');
  require('dotenv').config({ path: runnerSettingsPath });

  try {
    // Check if required environment variables are set;
    if (!repoDir || !metadataPath || !outputDir || !agentDir || !branchName) {
      throw new Error('TESTBED_DIR, METADATA_PATH, OUTPUT_DIR, AGENT_DIR and BRANCH_NAME environment variables must be set');
    }

    // Make a copy of the metadata file
    fs.copyFileSync(metadataPath!, path.join(outputDir!, path.basename(metadataPath!)));

    // Create a snapshot before running CPD, if --deep-copy is specified
    if (deepCopy) {
      create_deep_snapshot_no_throw(repoDir, outputDir!, 'before-cpd');
    }
    // Parse the metadata file, and run CPD
    const metadata = parseMetadataFile(metadataPath!);

    // Parse the CPD config file; setup Blackbird files if configured
    if (process.env.BLACKBIRD_ENABLED === 'true' && hasValidBlackbirdConfig()) {
      await setupBlackbirdFiles(agentDir!, repoDir, metadata.instance_id);
      await runMCP(agentDir!);
    }

    // Set the SWEBENCH_BASE_COMMIT environment variable
    console.log("loading base commit from metadata: ", metadata);
    process.env.SWEBENCH_BASE_COMMIT = metadata.base_commit;
    process.env.SWEBENCH_REPO = metadata.repo;

    await runCPD(fixTemplate(metadata.problem_statement), repoDir, outputDir!, agentDir!, branchName!);

    // Create a snapshot after running CPD, if --deep-copy is specified
    if (deepCopy) {
      create_deep_snapshot_no_throw(repoDir, outputDir!, 'after-cpd-success');
    }

    // We're done!
    console.log('CPD succeeded!');
    process.exit(0);
  } catch (error: any) {
    // Create a snapshot after running CPD
    if (deepCopy) {
      create_deep_snapshot(repoDir!, outputDir!, 'after-cpd-error');
    }
    console.error(`CPD error: ${error.message}`);
    process.exit(1);
  }
}

main();