import { DefaultAzureCredential } from '@azure/identity';
import { BlobServiceClient, ContainerClient } from '@azure/storage-blob';
import * as fs from 'fs';
import { chmod, chown } from 'fs/promises';
import { exec, ExecOptions } from 'node:child_process';
import * as path from 'path';

/**
 * Configuration for Azure Storage access
 */
export interface BlackbirdStorageConfig {
  accountName: string;
  containerName: string;
  mcpBlobPath: string;
  localBlobPath: string;
  indexBlobPath: string | null;
}

export interface BlackbirdAuthConfig {
  modelBasedRetrievalToken: string;
  metisApiKey: string;
}

export interface BlackbirdConfig {
  mode: 'tool' | 'initial-search';
  storage: BlackbirdStorageConfig;
  auth: BlackbirdAuthConfig;
}

/**
 * Response from downloading Blackbird files
 */
export interface BlackbirdDownloadResult {
  success: boolean;
  mcpPath?: string;
  indexPath?: string;
  error?: string;
}

/**
 * Class responsible for downloading Blackbird files from Azure Storage
 */
export class BlackbirdDownloader {
  private config: BlackbirdConfig;
  private outputDir: string;
  private repoDir: string;
  private instanceId: string;

  /**
   * Creates a new BlackbirdDownloader
   * 
   * @param config Configuration for Blackbird including storage and authentication details
   * @param outputDir Directory where files should be downloaded
   */
  constructor(config: BlackbirdConfig, outputDir: string, repoDir: string, instanceId: string) {
    this.config = config;
    this.outputDir = outputDir;
    this.repoDir = repoDir;
    this.instanceId = instanceId;
  }

  /**
   * Downloads the MCP binary and index files from Azure Storage
   */
  public async downloadFiles(): Promise<BlackbirdDownloadResult> {
    try {
      // Create output directory if it doesn't exist
      fs.mkdirSync(this.outputDir, { recursive: true });

      // Set up paths
      const mcpFilePath = path.join(this.outputDir, 'mcp');
      const localBlackbirdPath = path.join(this.outputDir, 'blackbird-local');

      // Set up Azure Storage client with DefaultAzureCredential
      const credential = new DefaultAzureCredential();
      const blobServiceClient = new BlobServiceClient(
        `https://${this.config.storage.accountName}.blob.core.windows.net`,
        credential
      );

      const containerClient = blobServiceClient.getContainerClient(this.config.storage.containerName);

      // Download MCP binary
      console.log(`Downloading MCP binary from ${this.config.storage.mcpBlobPath} to ${mcpFilePath}...`);
      const mcpBlobClient = containerClient.getBlobClient(this.config.storage.mcpBlobPath);
      await this.downloadToFile(mcpBlobClient, mcpFilePath);

      console.log(`Downloading local Blackbird binary ${this.config.storage.localBlobPath} to ${localBlackbirdPath}...`);
      const localBlackbirdBlobClient = containerClient.getBlobClient(this.config.storage.localBlobPath);
      const localBlackbirdEtag = await localBlackbirdBlobClient.getProperties().then((props) => props.etag) || 'unknown';
      await this.downloadToFile(localBlackbirdBlobClient, localBlackbirdPath);

      console.log('Making local Blackbird files executable...');
      await chmod(localBlackbirdPath, 0o755);
      if (process.getuid && process.getgid) {
        const uid = process.getuid();
        const gid = process.getgid();
        console.log(`Setting ownership of local Blackbird files to UID: ${uid}, GID: ${gid}`);
        await chown(this.repoDir, uid, gid);
      }

      // wait for the file system to catch up
      await new Promise((resolve) => setTimeout(resolve, 1000));

      await this.fetchOrCreateLocalIndex(localBlackbirdPath, localBlackbirdEtag, containerClient);
      // Make MCP binary executable
      console.log('Making MCP binary executable...');
      await chmod(mcpFilePath, 0o755);


      // For debugging, print the contents of "outputDir"
      console.log('Contents of outputDir:', fs.readdirSync(this.outputDir));

      // For debugging, print the absolute path to `this.outputDir`
      console.log('Absolute path to outputDir:', path.resolve(this.outputDir));

      return {
        success: true,
        mcpPath: mcpFilePath,
        indexPath: `${this.outputDir}/metis`
      };
    } catch (error) {
      console.error('Error downloading Blackbird files:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private async fetchOrCreateLocalIndex(localBlackbirdPath: string, localBlackbirdEtag: string, containerClient: ContainerClient): Promise<void> {
    const indexFilePath = path.join(this.outputDir, 'index.tar.gz');
    console.log(`BLACKBIRD_ETAG: ${localBlackbirdEtag}`);
    const indexBlobPath = this.config.storage.indexBlobPath || `msbench-data/local-blackbird-index/${this.instanceId}/index-${localBlackbirdEtag}.tar.gz`;
    const indexBlobClient = containerClient.getBlobClient(indexBlobPath);
    if (await indexBlobClient.exists()) {
      // Download index file
      console.log(`Downloading index file from ${this.config.storage.indexBlobPath} to ${indexFilePath}...`);
      await this.downloadToFile(indexBlobClient, indexFilePath);

      // Untar the index.tar.gz file
      console.log('Extracting index file...');
      await this.executeCommand(`tar -xzf ${indexFilePath} -C ${this.outputDir}`);

    } else {
      console.log(`index file ${indexBlobPath} Does not exist, create local index manually.`);
      await this.executeCommand(`${localBlackbirdPath} ingest repo --repo ${this.repoDir} --nwo msbench/${this.instanceId}  --index ${this.outputDir}/metis`, { env: { 'METIS_API_KEY': this.config.auth.metisApiKey } });
      console.log(`compress local index to ${this.outputDir}/local_index.tar.gz`);
      await this.executeCommand(`tar -czvf local_index.tar.gz metis`, { cwd: this.outputDir });
      const localIndexArchive = path.join(this.outputDir, 'local_index.tar.gz');


      console.log(`Uploading local index archive ${localIndexArchive} to Azure Blob Storage at ${indexBlobPath}...`);
      await containerClient.uploadBlockBlob(indexBlobPath, fs.createReadStream(localIndexArchive), fs.statSync(localIndexArchive).size);
    }
  }

  private async executeCommand(command: string, options: ExecOptions | undefined = undefined): Promise<void> {
    return new Promise((resolve, reject) => {

      exec(command, options, (error, stdout, stderr) => {
        if (error) {
          console.error(`Error executing command "${command}":`, stderr);
          reject(error);
        } else {
          console.log(`Command "${command}" executed successfully:`, stdout);
          resolve();
        }
      });
    });
  }

  /**
   * Downloads a blob to a local file
   * 
   * @param blobClient Blob client for the blob to download
   * @param filePath Local file path where the blob should be saved
   */
  private async downloadToFile(blobClient: any, filePath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const downloadResponse = blobClient.download();

      // Create a write stream to write data incrementally
      const fileStream = fs.createWriteStream(filePath);

      downloadResponse
        .then((response: any) => {
          const readableStream = response.readableStreamBody;

          if (!readableStream) {
            reject(new Error('Unable to retrieve readable stream from blob.'));
            return;
          }

          // Pipe the readable stream directly to the file stream
          readableStream.pipe(fileStream);

          // Handle stream events
          fileStream.on('finish', () => resolve());
          fileStream.on('error', (err) => reject(err));
          readableStream.on('error', (err: any) => reject(err));
        })
        .catch((err: any) => reject(err));
    });
  }
}

/**
 * Helper function to check if Blackbird configuration exists and is valid
 * 
 * @param config Generic configuration object
 * @returns True if valid Blackbird configuration exists, false otherwise
 */
export function hasValidBlackbirdConfig(): boolean {
  return !!(
    process.env.BLACKBIRD_STORAGE_ACCOUNT_NAME &&
    process.env.BLACKBIRD_STORAGE_CONTAINER_NAME &&
    process.env.BLACKBIRD_STORAGE_MCP_BLOB_PATH &&
    process.env.BLACKBIRD_STORAGE_LOCAL_BLOB_PATH &&
    process.env.BLACKBIRD_AUTH_MODEL_BASED_RETRIEVAL_TOKEN &&
    process.env.BLACKBIRD_AUTH_METIS_API_KEY
  );
}
/**
 * Extract Blackbird storage configuration from generic config object
 * 
 * @param config Generic configuration object
 * @returns BlackbirdStorageConfig object or null if config is invalid
 */
export function extractBlackbirdConfig(): BlackbirdConfig | null {

  let mode = process.env.BLACKBIRD_MODE || 'initial-search';
  if (mode !== 'tool' && mode !== 'initial-search') {
    console.warn(`Invalid BLACKBIRD_MODE: ${mode}. Defaulting to 'initial-search'.`);
    mode = 'initial-search';
  }

  return {
    mode: mode as 'initial-search' | 'tool',
    storage: {
      accountName: process.env.BLACKBIRD_STORAGE_ACCOUNT_NAME!!,
      containerName: process.env.BLACKBIRD_STORAGE_CONTAINER_NAME!!,
      mcpBlobPath: process.env.BLACKBIRD_STORAGE_MCP_BLOB_PATH!!,
      indexBlobPath: process.env.BLACKBIRD_STORAGE_INDEX_BLOB_PATH || null,
      localBlobPath: process.env.BLACKBIRD_STORAGE_LOCAL_BLOB_PATH!!
    },
    auth: {
      modelBasedRetrievalToken: process.env.BLACKBIRD_AUTH_MODEL_BASED_RETRIEVAL_TOKEN!!,
      metisApiKey: process.env.BLACKBIRD_AUTH_METIS_API_KEY!!
    }
  };
}