/**
 * SWE Agent Benchmark Tool Stats Analysis Script
 * 
 * This script processes trajectory and log files from benchmark runs to extract
 * metrics about tool usage, error counts, and other statistics, then outputs them
 * to a CSV file for further analysis.
 * 
 * Usage:
 *   node get_tool_stats.js <RUN_ID>
 * 
 * Parameters:
 *   RUN_ID - The identifier of the benchmark run (used to locate the data-<RUN_ID> directory)
 * 
 * Output:
 *   Creates a file data-<RUN_ID>/tool_counts.csv with the following metrics:
 *   - instance_run: The benchmark instance name
 *   - total_invoke_counts: Total number of tool invocations
 *   - known_tool_total: Total of recognized tool invocations
 *   - think_counts: Number of "think" tool invocations
 *   - report_progress_counts: Number of "report_progress" tool invocations
 *   - bash_counts: Number of "bash" tool invocations 
 *   - str_replace_editor_counts: Number of "str_replace_editor" tool invocations
 *   - str_replace_editor_*: Counts of various editor command types
 *   - agent_error_counts: Number of agent errors
 *   - model_error_count: Number of model errors
 *   - model_exits: Number of model exits
 *   - status_*: Counts of various HTTP error status codes
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);
const readdirAsync = promisify(fs.readdir);

// Recursive function to find files with a specific name
async function findFiles(dir, filename) {
    let results = [];
    const entries = await readdirAsync(dir, { withFileTypes: true });
    
    for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        
        if (entry.isDirectory()) {
            const subResults = await findFiles(fullPath, filename);
            results = results.concat(subResults);
        } else if (entry.name === filename) {
            results.push(fullPath);
        }
    }
    
    return results;
}

// Count occurrences of a pattern in a file
function countOccurrences(content, pattern) {
    const regex = new RegExp(pattern, 'g');
    const matches = content.match(regex);
    return matches ? matches.length : 0;
}

// Get count of a specific command in str_replace_editor invocations
function countEditorCommand(content, command) {
    const regex = new RegExp(`<invoke name="str_replace_editor">\\s*<parameter name="command">${command}</parameter>`, 'g');
    const matches = content.match(regex);
    return matches ? matches.length : 0;
}

// Convert absolute path to relative path and clean it
function getRelativePath(filePath, baseDir) {
    let relativePath = path.dirname(filePath).replace(baseDir, '');
    if (relativePath.startsWith(path.sep)) {
        relativePath = relativePath.substring(1);
    }
    relativePath = relativePath.replace(/^.*\.eval\.x86_64\./, '').replace(/-output.*$/, '');
    return relativePath;
}

// Check if a file exists
function fileExists(filePath) {
    try {
        fs.accessSync(filePath, fs.constants.F_OK);
        return true;
    } catch (err) {
        return false;
    }
}

// Count error statuses from log file
function countErrorStatus(content, statusCode) {
    const regex = new RegExp(`"status": ${statusCode}`, 'g');
    const matches = content.match(regex);
    return matches ? matches.length : 0;
}

// Count model errors and exits from log file
function countModelErrors(content) {
    const errorRegex = new RegExp('Failed to get response from the AI model:', 'g');
    const timeoutRegex = new RegExp('seconds is too long. Giving up.', 'g');
    const retryRegex = new RegExp('Failed to get response from the AI model; retried', 'g');
    
    const errors = content.match(errorRegex);
    const timeouts = content.match(timeoutRegex);
    const retries = content.match(retryRegex);
    
    return {
        errorCount: errors ? errors.length : 0,
        exitCount: (timeouts ? timeouts.length : 0) + (retries ? retries.length : 0)
    };
}

// Main function that processes the files
async function processFiles(runId) {
    const baseDir = `data-${runId}`;
    const outputFile = path.join(baseDir, 'tool_counts.csv');
    
    // Create CSV header - ensure every column has a matching calculation below
    const header = "instance_run,total_invoke_counts,known_tool_total,think_counts,report_progress_counts,bash_counts,str_replace_editor_counts,str_replace_editor_known_subcmd_total,str_replace_editor_view_counts,str_replace_editor_replace_counts,str_replace_editor_create_counts,str_replace_editor_insert_counts,str_replace_editor_undo_edit_counts,agent_error_counts,model_error_count,model_exits,status_429,status_400,status_422,status_500,status_502";
    
    // Write header to file
    await writeFileAsync(outputFile, header + '\n');
    
    // Find all cpd-trajectory.md files
    const trajectoryFiles = await findFiles(baseDir, 'cpd-trajectory.md');
    
    for (const file of trajectoryFiles) {
        // Get relative path from base directory
        const relPath = getRelativePath(file, baseDir);
        
        // Read file content
        const content = await readFileAsync(file, 'utf8');
        
        // Get the log file path in the same directory
        const logFile = path.join(path.dirname(file), 'cpd-runtime-log.log');
        let logContent = '';
        if (fileExists(logFile)) {
            logContent = await readFileAsync(logFile, 'utf8');
        }
        
        // Initialize counts
        const reportProgressCounts = countOccurrences(content, '<invoke name="report_progress">');
        const totalInvokeCounts = countOccurrences(content, '<invoke name=');
        const bashCounts = countOccurrences(content, '<invoke name="bash">');
        const strReplaceEditorCounts = countOccurrences(content, '<invoke name="str_replace_editor">');
        
        // Count editor commands
        const strReplaceEditorViewCounts = countEditorCommand(content, 'view');
        const strReplaceEditorReplaceCounts = countEditorCommand(content, 'str_replace');
        const strReplaceEditorCreateCounts = countEditorCommand(content, 'create');
        const strReplaceEditorInsertCounts = countEditorCommand(content, 'insert');
        const strReplaceEditorUndoEditCounts = countEditorCommand(content, 'undo_edit');
        
        const strReplaceEditorKnownSubcmdTotal = 
            strReplaceEditorViewCounts +
            strReplaceEditorReplaceCounts +
            strReplaceEditorCreateCounts +
            strReplaceEditorInsertCounts +
            strReplaceEditorUndoEditCounts;
        
        const thinkCounts = countOccurrences(content, '<invoke name="think">');
        const knownToolTotal = thinkCounts + reportProgressCounts + bashCounts + strReplaceEditorCounts;
        
        // Check for agent error
        const agentErrorFile = path.join(path.dirname(file), 'agent.error');
        const agentErrorCounts = fileExists(agentErrorFile) ? 1 : 0;
        
        // Count model errors from log file
        let modelErrorCount = 0;
        let modelExits = 0;
        
        if (logContent) {
            const modelErrors = countModelErrors(logContent);
            modelErrorCount = modelErrors.errorCount;
            modelExits = modelErrors.exitCount;
        }
        
        // Count HTTP status errors - these match columns in the CSV header
        const status429 = countErrorStatus(logContent, 429);
        const status400 = countErrorStatus(logContent, 400);
        const status422 = countErrorStatus(logContent, 422);
        const status500 = countErrorStatus(logContent, 500);
        const status502 = countErrorStatus(logContent, 502);
        
        // Create the CSV line
        // Ensure each value corresponds to a column in the header
        const csvLine = [
            relPath,                          // instance_run
            totalInvokeCounts,                // total_invoke_counts
            knownToolTotal,                   // known_tool_total
            thinkCounts,                      // think_counts
            reportProgressCounts,             // report_progress_counts
            bashCounts,                       // bash_counts
            strReplaceEditorCounts,           // str_replace_editor_counts
            strReplaceEditorKnownSubcmdTotal, // str_replace_editor_known_subcmd_total
            strReplaceEditorViewCounts,       // str_replace_editor_view_counts
            strReplaceEditorReplaceCounts,    // str_replace_editor_replace_counts
            strReplaceEditorCreateCounts,     // str_replace_editor_create_counts
            strReplaceEditorInsertCounts,     // str_replace_editor_insert_counts
            strReplaceEditorUndoEditCounts,   // str_replace_editor_undo_edit_counts
            agentErrorCounts,                 // agent_error_counts
            modelErrorCount,                  // model_error_count
            modelExits,                       // model_exits
            status429,                        // status_429
            status400,                        // status_400
            status422,                        // status_422
            status500,                        // status_500
            status502                         // status_502
        ].join(',');
        
        // Append to the output file
        await fs.promises.appendFile(outputFile, csvLine + '\n');
    }
    
    console.log(`Analysis complete. Results written to ${outputFile}`);
}

// Main execution
(async function main() {
    try {
        // Get the RUN_ID from command line arguments
        const runId = process.argv[2];
        
        if (!runId) {
            console.error('Please provide a RUN_ID as an argument');
            process.exit(1);
        }
        
        await processFiles(runId);
    } catch (error) {
        console.error('Error:', error);
        process.exit(1);
    }
})();
