#!/bin/bash
set +e

SCRIPT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
TEST_DIR=$SCRIPT_DIR/test-the-runner

# ensure params are passed in
if [ -z "$1" ]; then
  echo "Usage: $0 <integration_id> [--copilot-key-vault VAULT_URI] [--copilot-hmac-key KEY]"
  echo "       [--blackbird-storage-account ACCOUNT, default padawanblackbird] [--blackbird-container CONTAINER, default blackbird] [--blackbird-mcp-blob BLOB_PATH, default blackbird-mcp] [--blackbird-local-blob BLOB_PATH default blackbird-local] [--blackbird-index-blob BLOB_PATH] [--disable-blackbird]"
  exit 1
fi

# clean up the test dir if it exists
if [ -d $TEST_DIR ]; then
  rm -rf $TEST_DIR
fi
mkdir -p $TEST_DIR
mkdir -p $TEST_DIR/output

# pass all params to update_cpd.sh
./update_cpd.sh $@

# create a sample metadata file
echo "Creating sample metadata file..."
cat <<EOT > $TEST_DIR/metadata.json
{
  "problem_statement": "Add a file called 'animals.txt' that has a list of animals in it. One animal per line. The list should be sorted alphabetically."
}
EOT
echo "Sample metadata file created."

# create a sample "repo" as the starting point
TEST_REPO_DIR=$TEST_DIR/repo
echo "Creating sample repo ($TEST_REPO_DIR)..."
mkdir -p $TEST_REPO_DIR
cd $TEST_REPO_DIR
git init
cat <<EOT > $TEST_REPO_DIR/README.md
# Sample repo
This is a sample repo for testing the CPD agent.
EOT
git add .
git commit -m "Initial commit"
echo "Creating sample repo ($TEST_REPO_DIR)... Done!"
echo

# Run the benchmark
echo "Running benchmark... (cpd)"
export AGENT_DIR=$SCRIPT_DIR/agent_pkg
export METADATA_PATH=$TEST_DIR/metadata.json
export OUTPUT_DIR=$TEST_DIR/output
export TESTBED_DIR=$TEST_REPO_DIR
$SCRIPT_DIR/runner.sh

# show the output directory (recursively, only showing the relative path)
echo
find $TEST_DIR/output -type f -print |grep -v ".git" |grep -v "node_modules" |grep -v "dist"
