{"cells": [{"cell_type": "markdown", "id": "45c69927", "metadata": {}, "source": ["#### Load and merge run data\n", "Load both the run data and the error data set. \n", "Note: before running this, run ./5-analyze <run-Id> scripts for the main and error runs to merge with. This will generate all the necessary files."]}, {"cell_type": "markdown", "id": "a51ad751", "metadata": {}, "source": ["Tools for use:\n", "- Ana<PERSON><PERSON> with 3.12+ env\n", "- DataWrangler extension\n", "- GitHub Copilot "]}, {"cell_type": "markdown", "id": "553862ca", "metadata": {}, "source": ["%pip install pandas matplotlib seaborn"]}, {"cell_type": "code", "execution_count": null, "id": "f41f80ae", "metadata": {}, "outputs": [], "source": ["import subprocess\n", "\n", "def run_analyze_script(path):\n", "    # Extract the run ID from the path\n", "    run_id = path\n", "    \n", "    # Remove \"data-\" prefix if it exists\n", "    if run_id.startswith(\"data-\"):\n", "        run_id = run_id.replace(\"data-\", \"\", 1)\n", "    \n", "    # Run the analysis script\n", "    print(f\"Running analysis for: {run_id}\")\n", "    try:\n", "        result = subprocess.run([\"./5-analyze.sh\", run_id], \n", "                               capture_output=True, \n", "                               text=True, \n", "                               check=True)\n", "        print(f\"Success! Output: {result.stdout}\")\n", "        return True\n", "    except subprocess.CalledProcessError as e:\n", "        print(f\"Error running analysis: {e}\")\n", "        print(f\"Error output: {e.stderr}\")\n", "        return False"]}, {"cell_type": "code", "execution_count": null, "id": "b321f096", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "def create_merged_dataframe(base_path, analyze=False):\n", "    if analyze:\n", "        run_analyze_script(base_path)\n", "\n", "    # Construct file paths based on the provided runId\n", "    report_path = f'{base_path}/report.csv'\n", "    tool_counts_path = f'{base_path}/tool_counts.csv'\n", "    token_usage_path = f'{base_path}/token_usage.csv'\n", "    corruption_stats_path = f'{base_path}/corruption_stats.csv'\n", "    \n", "    # New eval metric paths\n", "    # hardness_path = f'{base_path}/online_eval_hardness_metrics.csv'\n", "    # problem_class_path = f'{base_path}/online_eval_problem_classification_metrics.csv'\n", "    # quality_path = f'{base_path}/online_eval_quality_metrics.csv'\n", "    # trajectory_path = f'{base_path}/online_eval_trajectory_metrics.csv'\n", "\n", "    # Load the data from the CSV files\n", "    results = pd.read_csv(report_path)\n", "    tool_counts = pd.read_csv(tool_counts_path)\n", "    token_usage = pd.read_csv(token_usage_path)\n", "    corruption_stats = pd.read_csv(corruption_stats_path)\n", "    \n", "    # Load evaluation metrics\n", "    # hardness_metrics = pd.read_csv(hardness_path)\n", "    # problem_class_metrics = pd.read_csv(problem_class_path)\n", "    # quality_metrics = pd.read_csv(quality_path)\n", "    # trajectory_metrics = pd.read_csv(trajectory_path)\n", "\n", "    # Perform the joins to create the merged DataFrame\n", "    merged_data = pd.merge(results, tool_counts, left_on='instance', right_on='instance_run', how='inner')\n", "    merged_dataframe = pd.merge(merged_data, token_usage, left_on='instance', right_on='instance_token', how='inner')\n", "    merged_dataframe = pd.merge(merged_dataframe, corruption_stats, left_on='instance', right_on='instance_corrupted', how='inner')\n", "    \n", "    # Merge evaluation metrics using instance_eval as key\n", "    # merged_dataframe = pd.merge(merged_dataframe, hardness_metrics, left_on='instance', right_on='instance_online_eval_hardness', how='left')\n", "    # merged_dataframe = pd.merge(merged_dataframe, problem_class_metrics, left_on='instance', right_on='instance_online_eval_problem_classification', how='left')\n", "    # merged_dataframe = pd.merge(merged_dataframe, quality_metrics, left_on='instance', right_on='instance_online_eval_quality', how='left')\n", "    # merged_dataframe = pd.merge(merged_dataframe, trajectory_metrics, left_on='instance', right_on='instance_online_eval_trajectory', how='left')\n", "\n", "    # Drop rows where 'resolved' is 'Error'\n", "    merged_dataframe = merged_dataframe[merged_dataframe['resolved'] != 'error']\n", "    \n", "    # Calculate the unknown tool count as total_invoke_counts - known_tool_total\n", "    merged_dataframe['unknown_tool_count'] = merged_dataframe['total_invoke_counts'] - merged_dataframe['known_tool_total']\n", "    \n", "     # normalize the 'resolved' column to boolean\n", "    merged_dataframe['resolved'] = merged_dataframe['resolved'].map({'True': True, 'False': False, True: True, False: False})\n", "\n", "    # Convert 'resolved' column to boolean\n", "    return merged_dataframe\n", "\n", "\n", "def create_full_dataset(main_run_path, error_run_paths, analyze=False):\n", "    \n", "    # Create the main dataset\n", "    main_dataset = create_merged_dataframe(main_run_path, analyze)\n", "\n", "    # Iterate over each error run path and create datasets\n", "    for error_run_path in error_run_paths:\n", "        error_dataset = create_merged_dataframe(error_run_path, analyze)\n", "\n", "        # drop the rows in main dataset that match the error dataset and append the error dataset to the main dataset\n", "        main_dataset = main_dataset[~main_dataset['instance'].isin(error_dataset['instance'])]\n", "        main_dataset = pd.concat([main_dataset, error_dataset], ignore_index=True)\n", "\n", "        # de-duplicate rows taking the first occurrence\n", "        main_dataset = main_dataset.drop_duplicates(subset=['instance'], keep='first')\n", "\n", "        # sort the dataset by 'instance'\n", "        main_dataset = main_dataset.sort_values(by='instance')\n", "\n", "        # Reset the index of the final dataset\n", "        main_dataset.reset_index(drop=True, inplace=True)\n", "                \n", "    return main_dataset"]}, {"cell_type": "code", "execution_count": null, "id": "b8e47af0", "metadata": {}, "outputs": [], "source": ["def analyze_resolution_status(df):\n", "    \"\"\"\n", "    Analyze and display the resolution status distribution of a dataframe.\n", "    \n", "    Parameters:\n", "    df (pandas.DataFrame): The dataframe containing a 'resolved' column\n", "    \n", "    Returns:\n", "    tuple: (resolution_counts, resolution_percentages)\n", "    \"\"\"\n", "    # Get both counts and percentages\n", "    resolution_counts = df['resolved'].value_counts()\n", "    resolution_percentages = (df['resolved'].value_counts() / len(df) * 100).round(1)\n", "\n", "    print(f\"Total instances: {len(df)}\\n\")\n", "    print(\"Resolution Status Distribution:\")\n", "    print(\"\\nCounts:\")\n", "    print(resolution_counts)\n", "    print(\"\\nPercentages (%):\")\n", "    print(resolution_percentages)"]}, {"cell_type": "code", "execution_count": null, "id": "954b4306", "metadata": {}, "outputs": [], "source": ["def calculate_percentile_metrics(df, percentile=0.9, dataset_name=None):\n", "    \"\"\"\n", "    Calculate key percentile metrics for a dataframe and return them as a DataFrame.\n", "    Also includes total counts/sums for relevant metrics.\n", "    \n", "    Parameters:\n", "    df (pandas.DataFrame): The dataframe containing run metrics\n", "    percentile (float): The percentile to calculate (default: 0.9)\n", "    dataset_name (str): Name of the dataset for identification (default: None)\n", "    \n", "    Returns:\n", "    pandas.DataFrame: DataFrame of percentile metrics and totals\n", "    \"\"\"\n", "\n", "    # Calculate percentile values\n", "    percentile_metrics = {\n", "        # Token metrics\n", "        f'{int(percentile*100)}th_total_tokens': df['total_tokens_y'].quantile(percentile),\n", "        f'{int(percentile*100)}th_prompt_tokens': df['prompt_tokens'].quantile(percentile),\n", "        f'{int(percentile*100)}th_completion_tokens': df['completion_tokens'].quantile(percentile),\n", "        f'{int(percentile*100)}th_cached_tokens': df['cached_tokens'].quantile(percentile),\n", "        \n", "        # Request metrics\n", "        f'{int(percentile*100)}th_requests_count': df['requests_count'].quantile(percentile),\n", "        \n", "        # Tool usage metrics\n", "        f'{int(percentile*100)}th_total_invoke_counts': df['total_invoke_counts'].quantile(percentile),\n", "        f'{int(percentile*100)}th_unknown_tool_counts': df['unknown_tool_count'].quantile(percentile),\n", "        f'{int(percentile*100)}th_bash_counts': df['bash_counts'].quantile(percentile), \n", "        f'{int(percentile*100)}th_str_replace_editor_counts': df['str_replace_editor_counts'].quantile(percentile),\n", "        f'{int(percentile*100)}th_report_progress_counts': df['report_progress_counts'].quantile(percentile),\n", "        f'{int(percentile*100)}th_str_replace_editor_view_counts': df['str_replace_editor_view_counts'].quantile(percentile),\n", "        f'{int(percentile*100)}th_str_replace_editor_replace_counts': df['str_replace_editor_replace_counts'].quantile(percentile),\n", "        f'{int(percentile*100)}th_str_replace_editor_create_counts': df['str_replace_editor_create_counts'].quantile(percentile),\n", "        \n", "        # Other metrics\n", "        f'{int(percentile*100)}th_inserted_lines': df['inserted_lines'].quantile(percentile),\n", "        f'{int(percentile*100)}th_deleted_lines': df['deleted_lines'].quantile(percentile),\n", "        f'{int(percentile*100)}th_files_corrupted': df['files_corrupted'].quantile(percentile),\n", "        f'{int(percentile*100)}th_files_changed': df['files_changed'].quantile(percentile),\n", "        \n", "        # Time metrics\n", "        f'{int(percentile*100)}th_execution_time': df['agent_execution_time_seconds'].quantile(percentile)\n", "    }\n", "    \n", "    # Calculate totals/sums\n", "    total_metrics = {\n", "        # Token totals\n", "        'total_tokens_sum': df['total_tokens_y'].sum(),\n", "        'prompt_tokens_sum': df['prompt_tokens'].sum(),\n", "        'completion_tokens_sum': df['completion_tokens'].sum(),\n", "        'cached_tokens_sum': df['cached_tokens'].sum(),\n", "        \n", "        # Request totals\n", "        'requests_count_sum': df['requests_count'].sum(),\n", "        'requests_count_mean': df['requests_count'].mean(),\n", "        \n", "        # Tool usage totals\n", "        'total_invoke_counts_sum': df['total_invoke_counts'].sum(),\n", "        'unknown_tool_counts_sum': df['unknown_tool_count'].sum(),\n", "        'bash_counts_sum': df['bash_counts'].sum(),\n", "        'str_replace_editor_counts_sum': df['str_replace_editor_counts'].sum(),\n", "        'report_progress_counts_sum': df['report_progress_counts'].sum(),\n", "        'str_replace_editor_view_counts_sum': df['str_replace_editor_view_counts'].sum(),\n", "        'str_replace_editor_replace_counts_sum': df['str_replace_editor_replace_counts'].sum(),\n", "        'str_replace_editor_create_counts_sum': df['str_replace_editor_create_counts'].sum(),\n", "        \n", "        # Other totals\n", "        'inserted_lines_sum': df['inserted_lines'].sum(),\n", "        'deleted_lines_sum': df['deleted_lines'].sum(),\n", "        'files_corrupted_sum': df['files_corrupted'].sum(),\n", "        'files_changed_sum': df['files_changed'].sum(),\n", "        \n", "        # Time metrics - mean makes more sense than sum\n", "        'execution_time_mean': df['agent_execution_time_seconds'].mean(),\n", "        \n", "        # Resolution stats\n", "        'resolved_count': df['resolved'].sum(),\n", "        'unresolved_count': (~df['resolved']).sum(),\n", "        'success_rate': df['resolved'].mean() * 100,\n", "        'total_instances': len(df)\n", "    }\n", "    \n", "    # Combine both metrics dictionaries\n", "    all_metrics = {**percentile_metrics, **total_metrics}\n", "    \n", "    # Convert to DataFrame for better display\n", "    metrics_df = pd.DataFrame(all_metrics, index=[dataset_name if dataset_name else \"Metrics\"])\n", "    \n", "    return metrics_df"]}, {"cell_type": "code", "execution_count": null, "id": "bdebcfc9", "metadata": {}, "outputs": [], "source": ["main_run = 'data-15429586146-oswe-swe'\n", "# by order of execution\n", "error_runs = ['data-15429586146-oswe-swe-error/data-15449035346', 'data-15429586146-oswe-swe-error/data-15455736216'] \n", "runData = create_full_dataset(main_run, error_runs, False)\n", "analyze_resolution_status(runData)"]}, {"cell_type": "code", "execution_count": null, "id": "50c44ef4", "metadata": {}, "outputs": [], "source": ["sonnet3_7Run = create_full_dataset('data-15178171174-3.7-swe', [], False)\n", "analyze_resolution_status(sonnet3_7Run)"]}, {"cell_type": "code", "execution_count": null, "id": "6722c57e", "metadata": {}, "outputs": [], "source": ["sonnet4_0Run = create_full_dataset('data-15221737683-4.0-swe', ['data-15221737683-4.0-swe-error/data-15229230193'], False)\n", "analyze_resolution_status(sonnet4_0Run)"]}, {"cell_type": "code", "execution_count": null, "id": "e3d6b5e5", "metadata": {}, "outputs": [], "source": ["# Find common instances among all three datasets\n", "common_instances = list(set(runData['instance']) & set(sonnet3_7Run['instance']) & set(sonnet4_0Run['instance']))\n", "print(f\"Found {len(common_instances)} common instances between OSWE, Sonnet 3.7, and Sonnet 4.0\")\n", "\n", "# Filter each dataframe to include only common instances\n", "runData = runData[runData['instance'].isin(common_instances)].copy()\n", "sonnet3_7Run = sonnet3_7Run[sonnet3_7Run['instance'].isin(common_instances)].copy()\n", "sonnet4_0Run = sonnet4_0Run[sonnet4_0Run['instance'].isin(common_instances)].copy()"]}, {"cell_type": "code", "execution_count": null, "id": "3e0f96da", "metadata": {}, "outputs": [], "source": ["analyze_resolution_status(runData)\n", "analyze_resolution_status(sonnet3_7Run)\n", "analyze_resolution_status(sonnet4_0Run)"]}, {"cell_type": "code", "execution_count": null, "id": "7a245ab6", "metadata": {}, "outputs": [], "source": ["percentile_90_runData = calculate_percentile_metrics(runData, dataset_name=\"OSWE\")\n", "percentile_90_sonnet3_7 = calculate_percentile_metrics(sonnet3_7Run, dataset_name=\"Sonnet 3.7\")\n", "percentile_90_sonnet4_0 = calculate_percentile_metrics(sonnet4_0Run, dataset_name=\"Sonnet 4.0\")\n", "\n", "# Compare all three datasets\n", "comparison_df = pd.concat([\n", "    percentile_90_runData, \n", "    percentile_90_sonnet3_7, \n", "    percentile_90_sonnet4_0\n", "])\n", "\n", "# Transpose the dataframe for better comparison\n", "comparison_transposed = comparison_df.transpose()\n", "\n", "# Display the transposed comparison dataframe\n", "print(\"Comparison of OSWE vs Sonnet 3.7 vs Sonnet 4.0:\")\n", "comparison_transposed"]}, {"cell_type": "code", "execution_count": null, "id": "a4a97513", "metadata": {}, "outputs": [], "source": ["# Filter each dataset to keep only resolved instances\n", "runData_resolved = runData[runData['resolved'] == True]\n", "sonnet37_resolved = sonnet3_7Run[sonnet3_7Run['resolved'] == True]\n", "sonnet40_resolved = sonnet4_0Run[sonnet4_0Run['resolved'] == True]\n", "\n", "# Calculate percentile metrics for resolved instances only\n", "percentile_90_runData_resolved = calculate_percentile_metrics(runData_resolved, dataset_name=\"OSWE (Resolved)\")\n", "percentile_90_sonnet37_resolved = calculate_percentile_metrics(sonnet37_resolved, dataset_name=\"Sonnet 3.7 (Resolved)\")\n", "percentile_90_sonnet40_resolved = calculate_percentile_metrics(sonnet40_resolved, dataset_name=\"Sonnet 4.0 (Resolved)\")\n", "\n", "# Filter each dataset to get the unresolved instances\n", "runData_unresolved = runData[runData['resolved'] == False]\n", "sonnet37_unresolved = sonnet3_7Run[sonnet3_7Run['resolved'] == False]\n", "sonnet40_unresolved = sonnet4_0Run[sonnet4_0Run['resolved'] == False]\n", "\n", "# Calculate percentile metrics for unresolved instances\n", "percentile_90_runData_unresolved = calculate_percentile_metrics(runData_unresolved, dataset_name=\"OSWE (Unresolved)\")\n", "percentile_90_sonnet37_unresolved = calculate_percentile_metrics(sonnet37_unresolved, dataset_name=\"Sonnet 3.7 (Unresolved)\")\n", "percentile_90_sonnet40_unresolved = calculate_percentile_metrics(sonnet40_unresolved, dataset_name=\"Sonnet 4.0 (Unresolved)\")\n", "\n", "# Combine the dataframes for comparison - both resolved and unresolved\n", "comparison_all_df = pd.concat([\n", "    percentile_90_runData_resolved, \n", "    percentile_90_sonnet37_resolved,\n", "    percentile_90_sonnet40_resolved,\n", "    percentile_90_runData_unresolved,\n", "    percentile_90_sonnet37_unresolved,\n", "    percentile_90_sonnet40_unresolved\n", "])\n", "\n", "# Transpose for better comparison\n", "comparison_all_transposed = comparison_all_df.transpose()\n", "\n", "# Display the comparison table\n", "print(\"Comparison of Resolved vs Unresolved across all models:\")\n", "comparison_all_transposed"]}], "metadata": {"kernelspec": {"display_name": "msbench", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}