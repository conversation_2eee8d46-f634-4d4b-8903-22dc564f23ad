#!/bin/bash

# Fail on any error
set -e

# Run setup script to ensure everything is properly configured
./setup.sh

# Initialize variables
SWEAGENTD_ROOT=$(cd "$(dirname "${BASH_SOURCE[0]}")/../../" && pwd)
INTEGRATION_ID=""
AZURE_KEY_VAULT_URI="https://kv-cpd-ci.vault.azure.net"
AZURE_KEY_VAULT_SECRET=""
HMAC_KEY=""
AGENT_MODEL=""
ADDITIONAL_TAGS=""
BLACKBIRD_STORAGE_ACCOUNT="padawanblackbird"
BLACKBIRD_CONTAINER_NAME="blackbird"
BLACKBIRD_MCP_BLOB_PATH="blackbird-mcp"
BLACKBIRD_INDEX_BLOB_PATH=""
BLACKBIRD_LOCAL_BLOB_PATH="blackbird-mcp"
INCLUDE_MCPS="true"
AZURE_URL=""
RUNNER_NO_TIMEOUT="false"
RUNNER_NO_CUSTOM_IMAGE="false"
BLACKBIRD_ENABLED="true"
BLACKBIRD_MODE='initial-search'
BASE_URL=""
HEADERS=""

usage() {
    echo "Usage: $0 [<integration_id> default copilot-developer-dev] [--copilot-key-vault VAULT_URI, default https://kv-cpd-ci.vault.azure.net] [--kv-secret-name SECRET_NAME] [--copilot-hmac-key KEY] [--agent-model sweagent-capi:MODEL_NAME] [--disable-mcps] [--disable-blackbird] [--url AZURE_OPENAI_ENDPOINT_URL] [--baseURL BASE_URL] [--api-key KEY] [--headers HEADERS] [--tags key1=value1,key2=value2] [--runner-no-timeout] [--runner-no-custom-image]"
    echo "       [--blackbird-storage-account ACCOUNT] [--blackbird-container CONTAINER] [--blackbird-mcp-blob BLOB_PATH] [--blackbird-index-blob BLOB_PATH] [--blackbird-mode initial-search|tool (default initial-search)] [--runner-no-custom-image]  [--runner-no-timeout]"
    echo "Note: Either --copilot-key-vault or --copilot-hmac-key must be provided if integration_id is given"
    echo "Note: For Azure openAI, provide a --url to point to the endpoint."
    echo "Note: If using Azure URL with a key vault, --kv-secret-name must be provided"
    echo "Note: If --disable-mcps is provided, MCPs will be disabled, otherwise a secret named 'dev-CloudColonelGHPAT' must exist in the keyvault with a GitHub PAT"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --agent-model)
            AGENT_MODEL="$2"
            shift 2
            ;;
        --copilot-key-vault)
            AZURE_KEY_VAULT_URI="$2"
            shift 2
            ;;
        --kv-secret-name)
            AZURE_KEY_VAULT_SECRET="$2"
            shift 2
            ;;
        --copilot-hmac-key)
            HMAC_KEY="$2"
            shift 2
            ;;
        --disable-mcps)
            INCLUDE_MCPS="false"
            shift
            ;;
        --url)
            AZURE_URL="$2"
            shift 2
            ;;
        --baseURL)
            BASE_URL="$2"
            shift 2
            ;;
        --headers)
            HEADERS="$2"
            shift 2
            ;;
        --tags)
            ADDITIONAL_TAGS="$2"
            shift 2
            ;;
        --blackbird-mode)
            BLACKBIRD_MODE="$2"
            if [[ "$BLACKBIRD_MODE" != "initial-search" && "$BLACKBIRD_MODE" != "tool" ]]; then
                echo "Error: --blackbird-mode must be either 'initial-search' or 'tool'"
                usage
                exit 1
            fi
            shift 2
            ;;
        --disable-blackbird)
            BLACKBIRD_ENABLED="false"
            shift
            ;;
        --blackbird-storage-account)
            BLACKBIRD_STORAGE_ACCOUNT="$2"
            shift 2
            ;;
        --blackbird-container)
            BLACKBIRD_CONTAINER_NAME="$2"
            shift 2
            ;;
        --blackbird-mcp-blob)
            BLACKBIRD_MCP_BLOB_PATH="$2"
            shift 2
            ;;
        --blackbird-local-blob)
            BLACKBIRD_LOCAL_BLOB_PATH="$2"
            shift 2
            ;;
        --blackbird-index-blob)
            BLACKBIRD_INDEX_BLOB_PATH="$2"
            shift 2
            ;;
        --runner-no-timeout)
            RUNNER_NO_TIMEOUT="true"
            shift
            ;;
        --runner-no-custom-image)
            RUNNER_NO_CUSTOM_IMAGE="true"
            shift
            ;;
        --api-key)
            API_KEY="$2"
            shift 2
            ;;
        *)
            if [ -z "$INTEGRATION_ID" ]; then
                INTEGRATION_ID="$1"
            else
                echo "Error: Unknown argument '$1'"
                usage
                exit 1
            fi
            shift
            ;;
    esac
done

# Check required arguments
if [ -n "$INTEGRATION_ID" ]; then
    # If integration ID is given, require at least one of key vault or HMAC key
    if [ -z "$AZURE_KEY_VAULT_URI" ] && [ -z "$HMAC_KEY" ]; then
        echo "Error: Either --copilot-key-vault or --copilot-hmac-key must be provided if integration_id is given"
        usage
        exit 1
    fi
fi

# Check if Azure URL and key vault are provided, then secret name is required
if [ -n "$AZURE_URL" ] && [ -n "$AZURE_KEY_VAULT_URI" ] && [ -z "$AZURE_KEY_VAULT_SECRET" ]; then
    echo "Error: When using Azure URL with a key vault, --kv-secret-name must be provided"
    usage
    exit 1
fi

if [ "$INCLUDE_MCPS" == "true" ]; then
    # Allow the user to provide a PAT for the GitHub MCP server, but if they don't, fetch the "dev-CloudColonelGHPAT" secret from the key vault
    if [ -z "$GITHUB_MCP_SERVER_TOKEN" ]; then
        if [ -z "$AZURE_KEY_VAULT_URI" ]; then
            echo "Error: --copilot-key-vault must be provided if MCPs are enabled and a GH PAToken is not provided via GITHUB_MCP_SERVER_TOKEN"
            exit 1
        else
            GITHUB_MCP_SERVER_TOKEN=$(az keyvault secret show --id "$AZURE_KEY_VAULT_URI/secrets/dev-CloudColonelGHPAT" --query value -o tsv)
            if [ -z "$GITHUB_MCP_SERVER_TOKEN" ]; then
                echo "Error: failed to retrieve GitHub PAT from key vault"
                exit 1
            fi
        fi
    fi
fi

# Check that sweagentd repo exists and has required structure
if [ ! -d "$SWEAGENTD_ROOT" ]; then
    echo "Error: '$SWEAGENTD_ROOT' directory does not exist"
    exit 1
else
    # Get the commit hash of the cpd runtime
    COMMIT_HASH=$(cd "$SWEAGENTD_ROOT" && git rev-parse --short HEAD)
    echo "cpd commit hash: $COMMIT_HASH"
    export COMMIT_HASH
    
    # Get the runtime commit hash
    RUNTIME_COMMIT=$(cd "$SWEAGENTD_ROOT" && git log -1 --format="%h" -- "runtime")
    echo "runtime commit hash: $RUNTIME_COMMIT"
    export COMMIT_HASH RUNTIME_COMMIT
fi

# Determine where agent package directory is/should be
SCRIPT_DIR="$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
AGENT_PKG_DIR="$SCRIPT_DIR/agent_pkg"
mkdir -p "$AGENT_PKG_DIR"

# Determine where cpd runtime is/should be
RUNTIME_DIR="$SWEAGENTD_ROOT/runtime"
RUNTIME_DIST_DIR="$RUNTIME_DIR/dist-msbench"

# Determine where cpd-msbench-runner is/should be
RUNNER_DIR="$AGENT_PKG_DIR/cpd-msbench-runner"
RUNNER_DIST_DIR="$RUNNER_DIR/dist"

# Check that runtime directory exists
if [ ! -d "$RUNTIME_DIR" ]; then
    echo "Error: 'runtime' directory not found in $SWEAGENTD_ROOT"
    exit 1
fi

# Build cpd runtime if needed
echo "Checking for cpd runtime build..."
if [ ! -d "$RUNTIME_DIST_DIR" ]; then
    echo
    echo "Building cpd runtime..."
    cd "$RUNTIME_DIR"
    npm install
    npm run build:msbench
    echo "Building cpd runtime... Done!"

else
    echo "Checking for cpd runtime build... cpd runtime is already built."
fi
echo

if [ "$INCLUDE_MCPS" == "true" ]; then
    echo "Copying github-mcp-server into agent_pkg..."
    "$RUNTIME_DIR/script/github-mcp-pull" "$AGENT_PKG_DIR/cpd/dist"
fi

# Copy the cpd runtime dist folder into agent_pkg
echo "Copying cpd runtime dist folder to agent_pkg..."
mkdir -p "$AGENT_PKG_DIR/cpd/dist"
cp -r "$RUNTIME_DIST_DIR/." "$AGENT_PKG_DIR/cpd/dist"
echo "Copying cpd runtime dist folder to agent_pkg... Done!"
echo


# Build cpd-msbench-runner if needed
echo "Checking for cpd-msbench-runner build..."
if [ ! -d "$RUNNER_DIST_DIR" ]; then
    echo
    echo "Building cpd-msbench-runner..."
    cd "$RUNNER_DIR"
    npm install
    npm run build
    echo "Building cpd-msbench-runner... Done!"
else
    echo "Building cpd-msbench-runner... cpd-msbench-runner is already built."
fi
echo

# Build the mcp-client

MCP_CLIENT_DIR="$SWEAGENTD_ROOT/runtime"
MCP_CLIENT_DIST_DIR="$MCP_CLIENT_DIR/dist-mcp-client/"


if [ ! -d "$MCP_CLIENT_DIR" ]; then
    echo "Error: 'mcp-client' directory not found in $SWEAGENTD_ROOT"
    exit 1
fi


echo "Checking for mcp-client build..."
if [ ! -d "$MCP_CLIENT_DIST_DIR" ]; then
    echo
    echo "Building mcp-client..."
    cd "$MCP_CLIENT_DIR"
    npm install
    npm run build:mcp-client
    echo "Building mcp-client... Done!"
else
    echo "Checking for mcp-client build... mcp-client is already built."
fi

# Copy the dist file into the $AGENT_PKG_DIR

echo "Copying mcp-client dist folder to agent_pkg..."
mkdir -p "$AGENT_PKG_DIR/mcp-client"
cp -r "$MCP_CLIENT_DIST_DIR" "$AGENT_PKG_DIR/mcp-client/"
echo "Copying mcp-client dist folder to agent_pkg... Done!"
echo

# Create the cpd.local.settings.env file
cat > "$AGENT_PKG_DIR/cpd.local.settings.env" << EOL
COPILOT_AGENT_ONLINE_EVALUATION_DISABLED=true
COPILOT_AGENT_ONLINE_EVALUATION_OUTPUT_FILE=true
COPILOT_FEATURE_FLAGS=copilot_swe_agent_parallel_tool_execution
EOL

# Add the agent model if provided
if [ -n "$AGENT_MODEL" ]; then
    echo "COPILOT_AGENT_MODEL=${AGENT_MODEL}" >> "$AGENT_PKG_DIR/cpd.local.settings.env"
fi

# split AGENT_MODEL into AGENT and MODEL by first ":"
if [[ "$AGENT_MODEL" == *":"* ]]; then
    AGENT="${AGENT_MODEL%%:*}"
    MODEL="${AGENT_MODEL##*:}"
else
    AGENT="$AGENT_MODEL"
    MODEL=""
fi

# Add Azure OpenAI configuration if AGENT is sweagent-openai
if [ "$AGENT" = "sweagent-openai" ]; then

    [ -n "$AZURE_KEY_VAULT_URI" ] && echo "AZURE_OPENAI_KEY_VAULT_URI=${AZURE_KEY_VAULT_URI}" >> "$AGENT_PKG_DIR/cpd.local.settings.env"
    [ -n "$AZURE_KEY_VAULT_SECRET" ] && echo "AZURE_OPENAI_KEY_VAULT_SECRET_NAME=${AZURE_KEY_VAULT_SECRET}" >> "$AGENT_PKG_DIR/cpd.local.settings.env"
    [ -n "$API_KEY" ] && echo "AZURE_OPENAI_API_KEY=${API_KEY}" >> "$AGENT_PKG_DIR/cpd.local.settings.env"
    [ -n "$BASE_URL" ] && echo "OPENAI_BASE_URL=${BASE_URL}" >> "$AGENT_PKG_DIR/cpd.local.settings.env"
    
    if [ -n "$AZURE_URL" ]; then
        echo "AZURE_OPENAI_API_VERSION=2025-04-01-preview" >> "$AGENT_PKG_DIR/cpd.local.settings.env"
        echo "AZURE_OPENAI_API_ENDPOINT=${AZURE_URL}" >> "$AGENT_PKG_DIR/cpd.local.settings.env"
    fi
fi

# Add Anthropic configuration if AGENT is sweagent-anthropic
if [ "$AGENT" = "sweagent-anthropic" ]; then
    [ -n "$API_KEY" ] && echo "ANTHROPIC_API_KEY=${API_KEY}" >> "$AGENT_PKG_DIR/cpd.local.settings.env"
fi

# Add headers if provided
if [ -n "$HEADERS" ]; then
    echo "COPILOT_AGENT_REQUEST_HEADERS=${HEADERS}" >> "$AGENT_PKG_DIR/cpd.local.settings.env"
fi

# Add copilot configuration if integration ID is provided
if [ -n "$INTEGRATION_ID" ]; then
    echo "GITHUB_COPILOT_INTEGRATION_ID=${INTEGRATION_ID}" >> "$AGENT_PKG_DIR/cpd.local.settings.env"
    
    # If the hmac key is empty, fetch it from the key vault
    if [ -z "$HMAC_KEY" ]; then
        if [ -z "$AZURE_KEY_VAULT_URI" ]; then
            echo "Error: --copilot-key-vault must be provided if --copilot-hmac-key is not provided"
            exit 1
        else
            HMAC_KEY=$(az keyvault secret show --id "$AZURE_KEY_VAULT_URI/secrets/capi-hmac-key" --query value -o tsv)
            if [ -z "$HMAC_KEY" ]; then
                echo "Error: failed to retrieve HMAC key from key vault"
                exit 1
            fi
        fi
    fi

    # Add the azureKeyVaultUri and hmacKey if provided
    [ -n "$HMAC_KEY" ] && echo "CAPI_HMAC_KEY=${HMAC_KEY}" >> "$AGENT_PKG_DIR/cpd.local.settings.env"
fi

cd "$SCRIPT_DIR"

# Copy the agent_config.yaml file to run_agent_config.yaml
cp "agent_config.yaml" "run_agent_config.yaml"

# Add model to run_agent_config.yaml if available
if [ -n "$MODEL" ]; then
    echo "model: ${MODEL}" >> "run_agent_config.yaml"
fi

# If MCPs are enabled, add the token we launch the GitHub MCP with to the cpd.local.settings.env file.
if [ "$INCLUDE_MCPS" == "true" ]; then
    echo "GITHUB_MCP_SERVER_TOKEN=${GITHUB_MCP_SERVER_TOKEN}" >> "$AGENT_PKG_DIR/cpd.local.settings.env"
fi

echo "COPILOT_AGENT_ERROR_CODES_TO_RETRY=400,422,429,500,502,503,504" >> "$AGENT_PKG_DIR/cpd.local.settings.env"

# add tags to run_agent_config.yaml
echo "tags:" >> "run_agent_config.yaml"
# if custom setup is to be used, set the runAgentOnVM to true
if [ "$RUNNER_NO_CUSTOM_IMAGE" == "false" ]; then
    echo "  runAgentOnVM: \"true\"" >> "run_agent_config.yaml"
    echo "  image: github-actions-vm" >> "run_agent_config.yaml"
else 
    echo "  image: msbench" >> "run_agent_config.yaml"
fi

if [ "$RUNNER_NO_TIMEOUT" == "true" ]; then
    echo "  timeout: disabled" >> "run_agent_config.yaml"
fi

if [ -n "$AGENT_MODEL" ]; then
    echo "  agent-model: ${AGENT_MODEL}" >> "run_agent_config.yaml"
else
    echo "  agent-model: default" >> "run_agent_config.yaml"
fi

# add the commit hash to run_agent_config.yaml
if [ -n "$COMMIT_HASH" ]; then
    echo "  commit-hash: \"${COMMIT_HASH}\"" >> "run_agent_config.yaml"
fi

# add the runtime commit hash to run_agent_config.yaml
if [ -n "$RUNTIME_COMMIT" ]; then
    echo "  runtime-commit: \"${RUNTIME_COMMIT}\"" >> "run_agent_config.yaml"
fi

# Add additional tags if provided
if [ -n "$ADDITIONAL_TAGS" ]; then
    IFS=',' read -ra TAG_PAIRS <<< "$ADDITIONAL_TAGS"
    for pair in "${TAG_PAIRS[@]}"; do
        IFS='=' read -r key value <<< "$pair"
        if [ -n "$value" ]; then
            echo "  $key: $value" >> "run_agent_config.yaml"
        else
            echo "  $key: none" >> "run_agent_config.yaml"
        fi
    done
fi
# Create runner.settings.env file
cat > "$AGENT_PKG_DIR/runner.settings.env" << EOL
RUNNER_NO_TIMEOUT=${RUNNER_NO_TIMEOUT}
RUNNER_NO_CUSTOM_IMAGE=${RUNNER_NO_CUSTOM_IMAGE}
EOL

# If blackbird is enabled
if [ "$BLACKBIRD_ENABLED" == "true" ]; then
    # Add Blackbird configuration if all required parameters are provided
    if [ -n "$BLACKBIRD_STORAGE_ACCOUNT" ] && [ -n "$BLACKBIRD_CONTAINER_NAME" ] && [ -n "$BLACKBIRD_MCP_BLOB_PATH" ] && [ -n "$BLACKBIRD_LOCAL_BLOB_PATH" ]; then
        if [ -z "$AZURE_KEY_VAULT_URI" ]; then
            echo "Error: --copilot-key-vault must be provided if --blackbird-storage-account is used"
            exit 1
        else
            MODEL_BASED_RETRIEVAL_TOKEN=$(az keyvault secret show --id "$AZURE_KEY_VAULT_URI/secrets/blackbird-model-based-retrieval-token" --query value -o tsv)
            if [ -z "$MODEL_BASED_RETRIEVAL_TOKEN" ]; then
                echo "Error: failed to retrieve Model Based Retrieval token from key vault"
                exit 1
            fi

            METIS_API_KEY=$(az keyvault secret show --id "$AZURE_KEY_VAULT_URI/secrets/blackbird-metis-api-key" --query value -o tsv)
            if [ -z "$METIS_API_KEY" ]; then
                echo "Error: failed to retrieve Metis API key from key vault"
                exit 1
            fi
        fi
        echo "BLACKBIRD_ENABLED=true" >> "$AGENT_PKG_DIR/runner.settings.env"
        echo "BLACKBIRD_MODE=${BLACKBIRD_MODE}" >> "$AGENT_PKG_DIR/runner.settings.env"
        echo "BLACKBIRD_AUTH_MODEL_BASED_RETRIEVAL_TOKEN=${MODEL_BASED_RETRIEVAL_TOKEN}" >> "$AGENT_PKG_DIR/runner.settings.env"
        echo "BLACKBIRD_AUTH_METIS_API_KEY=${METIS_API_KEY}" >> "$AGENT_PKG_DIR/runner.settings.env"
        echo "BLACKBIRD_STORAGE_ACCOUNT_NAME=${BLACKBIRD_STORAGE_ACCOUNT}" >> "$AGENT_PKG_DIR/runner.settings.env"
        echo "BLACKBIRD_STORAGE_CONTAINER_NAME=${BLACKBIRD_CONTAINER_NAME}" >> "$AGENT_PKG_DIR/runner.settings.env"
        echo "BLACKBIRD_STORAGE_MCP_BLOB_PATH=${BLACKBIRD_MCP_BLOB_PATH}" >> "$AGENT_PKG_DIR/runner.settings.env"
        echo "BLACKBIRD_STORAGE_LOCAL_BLOB_PATH=${BLACKBIRD_LOCAL_BLOB_PATH}" >> "$AGENT_PKG_DIR/runner.settings.env"
        echo "BLACKBIRD_METIS_INDEX_ENABLED=true" >> "$AGENT_PKG_DIR/runner.settings.env"
        echo "COPILOT_MCP_ENABLED=true" >> "$AGENT_PKG_DIR/runner.settings.env"

        # we also need to provide the CAPI HMAC key
        [ -n "$HMAC_KEY" ] && echo "CAPI_HMAC_KEY=${HMAC_KEY}" >> "$AGENT_PKG_DIR/runner.settings.env"
    fi
fi


# Function to redact sensitive environment variables
redact_and_echo() {
    local file="$1"
    awk '
    /^(GITHUB_TOKEN|GITHUB_COPILOT_API_TOKEN|CAPI_HMAC_KEY|CAPI_HMAC_KEY_OVERRIDE|ANTHROPIC_API_KEY|AIP_SWE_AGENT_TOKEN|CAPI_AZURE_KEY_VAULT_URI|COPILOT_JOB_NONCE|GITHUB_MCP_SERVER_TOKEN|OPENAI_BASE_URL|OPENAI_API_KEY|COPILOT_AGENT_REQUEST_HEADERS|AZURE_OPENAI_API_KEY|AZURE_OPENAI_API_ENDPOINT|AZURE_OPENAI_KEY_VAULT_URI|AZURE_OPENAI_KEY_VAULT_SECRET_NAME|BLACKBIRD_AUTH_METIS_API_KEY|BLACKBIRD_AUTH_MODEL_BASED_RETRIEVAL_TOKEN)=/ {
        gsub(/=.*/, "=[REDACTED]")
    }
    { print }
    ' "$file"
}

echo "runner.settings.env file created."
redact_and_echo "$AGENT_PKG_DIR/runner.settings.env"
echo

echo "cpd.local.settings.env file created."
redact_and_echo "$AGENT_PKG_DIR/cpd.local.settings.env"
echo

echo "run_agent_config.yaml used"
cat "run_agent_config.yaml"
echo

echo "Update complete! Agent package is ready!"
echo
