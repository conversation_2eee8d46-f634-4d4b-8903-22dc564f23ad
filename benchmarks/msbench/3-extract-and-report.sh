#!/bin/bash
set -e

# ensure param 1 is run_id
if [ -z "$1" ]; then
  echo "Usage: $0 <run_id> [--lock]"
  exit 1
fi

run_id=$1

# Check for --lock option
BACKEND_FLAG=""
if [ "$2" = "--lock" ]; then
    BACKEND_FLAG="--backend ces-ame"
fi

echo "Extracting benchmark data..."
if [ -d data-$run_id ]; then
  rm -rf data-$run_id
fi
msbench-cli extract --run_id $run_id --output data-$run_id/ $BACKEND_FLAG

echo "Generating report..."
if [ -f data-$run_id/report.json ]; then
  rm -f data-$run_id/report.json
fi
if [ -f data-$run_id/report.csv ]; then
  rm -f data-$run_id/report.csv
fi
msbench-cli report --run_id $run_id --output data-$run_id/report.json $BACKEND_FLAG
msbench-cli report --run_id $run_id --output data-$run_id/report.csv $BACKEND_FLAG

echo "Done..."
ls -l data-$run_id/
ls -l data-$run_id/report.json
cat data-$run_id/report.json
