#!/bin/bash

# Don't fail on error
set +e

# Check that required env vars are set
if [ -z "$TESTBED_DIR" ]; then
    echo "Error: TESTBED_DIR environment variable is not set"
    exit 1
fi
if [ -z "$METADATA_PATH" ]; then
    echo "Error: METADATA_PATH environment variable is not set"
    exit 1
fi
if [ -z "$AGENT_DIR" ]; then
    echo "Error: AGENT_DIR environment variable is not set"
    exit 1
fi
if [ -z "$OUTPUT_DIR" ]; then
    echo "Error: OUTPUT_DIR environment variable is not set"
    exit 1
fi

# Activate the environment
if [ -f "$HOME/.bashrc" ]; then
    source $HOME/.bashrc
else
    echo "Error: $HOME/.bashrc not found"
    ls -a $HOME/.bashrc
    exit 1
fi

# Default to using CPD image. This will be overridden by settings file if it exists
use_cpd_image=true

# Read runner settings if they exist
if [ -f "$AGENT_DIR/runner.settings.env" ]; then
    source "$AGENT_DIR/runner.settings.env"
    # Set use_cpd_image based on RUNNER_NO_CUSTOM_IMAGE
    if [ "$RUNNER_NO_CUSTOM_IMAGE" = "true" ]; then
        use_cpd_image=false
    fi
fi

# Activate node js version only if not using CPD image
if [ "$use_cpd_image" = false ]; then
    . /opt/activate_node.sh
fi

# Check for and delete agent.zip if it exists
if [ -f "$TESTBED_DIR/agent.zip" ]; then
    echo "Found agent.zip in testbed directory. Deleting it..."
    rm "$TESTBED_DIR/agent.zip"
    echo "agent.zip deleted."
fi

# Set Git configuration for this repository
git config --global user.name "CPD MSBench Runner"
git config --global user.email "<EMAIL>"    

# Change to the testbed directry

cd "$TESTBED_DIR"

# Check if git is initialized and initialize if needed
if [ ! -d ".git" ]; then
    echo "No git; Initializing git repository..."
    git init
    git config --global init.defaultBranch main
    git add --all
    git commit --allow-empty -m "Initial commit for benchmark run"
fi

# Store the initial branch name and commit hash
INITIAL_COMMIT=$(git show -s --format=%H HEAD)
INITIAL_BRANCH=$(git branch --show-current)

# If the initial branch is empty, create and checkout a new branch called "copilot-fix"
if [ -z "$INITIAL_BRANCH" ]; then
    echo "Current branch name is empty - likely because it's detached. Checking out new branch 'copilot-fix'..."
    git checkout -b copilot-fix
    INITIAL_BRANCH="copilot-fix"
    echo "Now on branch: $INITIAL_BRANCH"
fi

# Create and export BRANCH_NAME environment variable
export BRANCH_NAME=$INITIAL_BRANCH

# log the base branch and commit before the runner starts
echo "Branch: $INITIAL_BRANCH, Commit: $INITIAL_COMMIT" >> "$OUTPUT_DIR/changes.branch"

COMMIT=$(cat $METADATA_PATH | jq -r '.base_commit')
export GITHUB_REPOSITORY=msbench/$(cat $METADATA_PATH | jq -r '.instance_id')

echo "Using GITHUB_REPOSITORY: $MSBENCH_REPO_NWO"

# Run the CPD runner
echo "Running CPD runner ..."

# Log some packages - Useful for debugging to ensure the environment is correct.
echo "Logging installed packages (Host)..."
{
    echo "Machine: $(hostname)"
    echo "Date: $(date)"
    echo "--- Installed packages (Host) ---"
    pip list
} > "$OUTPUT_DIR/pip-list.log" 2>&1 || echo "Warning: Failed to generate pip list"

node "$AGENT_DIR/cpd-msbench-runner/dist/index.js" "$@"

errorlevel=$?
echo "Running CPD runner ... Done!"
echo

# Make sure you are in the testbed directory
cd "$TESTBED_DIR"

# Commit any pending changes
echo "Committing any pending changes..."
git add -A
git commit -m "Final changes made by CPD MSBench Runner" || echo "No changes to commit"
echo "Commit done!"
echo

# Run stats for analysis
echo "Collecting stats..."

# High level stats for post-run analysis
# Generate diff against the initial commit
echo "Generating diff against initial commit ($INITIAL_COMMIT)..."
git diff --shortstat $INITIAL_COMMIT > "$OUTPUT_DIR/changes.stats"

# used to see and apply changes locally
git diff $INITIAL_COMMIT > "$OUTPUT_DIR/changes.patch"

# Used for post-run delete metrics
git diff --numstat $INITIAL_COMMIT > "$OUTPUT_DIR/changes.numstat"

# Convert the agent trajectory to msbench trajectory format if cpd-trajectory.md exists
if [ -f "$OUTPUT_DIR/cpd-trajectory.md" ]; then
    echo "Converting CPD trajectory to MSBench format..."
    mkdir -p "$OUTPUT_DIR/trajectories"
    # Don't fail the script if the conversion fails
    node "$AGENT_DIR/cpd-msbench-runner/scripts/msbench-trajectory-converter.js" "$OUTPUT_DIR/cpd-trajectory.md" "$OUTPUT_DIR/trajectories/trajectory.json" || \
    echo "Warning: Trajectory conversion failed but continuing execution."
else
    echo "cpd-trajectory.md not found. Skipping trajectory conversion."
fi

# If the CPD runner failed, exit with the error code
if [ $errorlevel -ne 0 ]; then
    error_msg="Error: CPD runner failed with error code $errorlevel"
    echo "$error_msg"
    echo "$error_msg" >> "$OUTPUT_DIR/agent.error"
    exit $errorlevel
fi
